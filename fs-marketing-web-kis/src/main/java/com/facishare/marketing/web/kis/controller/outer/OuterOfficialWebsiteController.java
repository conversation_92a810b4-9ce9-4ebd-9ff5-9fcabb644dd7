package com.facishare.marketing.web.kis.controller.outer;

import com.facishare.marketing.api.arg.cta.CreateCtaQywxQrCodeArg;
import com.facishare.marketing.api.arg.cta.CreateCtaWxQrCodeArg;
import com.facishare.marketing.api.arg.cta.PollingQrCodeStatusArg;
import com.facishare.marketing.api.arg.cta.QueryCtaSimpleDetailArg;
import com.facishare.marketing.api.result.cta.CreateCtaWxQrCodeResult;
import com.facishare.marketing.api.result.cta.QueryCtaSimpleDetailResult;
import com.facishare.marketing.api.result.officialWebsite.GetWebsiteByIdResult;
import com.facishare.marketing.api.result.qr.CreateQRCodeResult;
import com.facishare.marketing.api.service.kis.CtaService;
import com.facishare.marketing.api.service.qr.QRCodeService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.EaArg;
import com.facishare.marketing.api.arg.MemberEnrollArg;
import com.facishare.marketing.api.arg.RecordUtmParamArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.officialWebsite.QueryWebsiteTrackDataArg;
import com.facishare.marketing.api.arg.qywx.customizeFormData.GetAreaDataArg;
import com.facishare.marketing.api.arg.wx.CreateOfficialWebsiteWxQrCodeArg;
import com.facishare.marketing.api.arg.wx.QueryOfficialWebsiteWxQrCodeArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.member.MemberEnrollResult;
import com.facishare.marketing.api.result.officialWebsite.QueryPageDataResult;
import com.facishare.marketing.api.result.officialWebsite.QueryWebsiteTrackDataResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryAddfanQrCodeResult;
import com.facishare.marketing.api.result.wxcoupon.CouponPlanInfoResult;
import com.facishare.marketing.api.service.*;
import com.facishare.marketing.api.service.kis.KisActionService;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.service.wxcoupon.WxCouponPayService;
import com.facishare.marketing.api.vo.MemberAccessibleWebsiteVo;
import com.facishare.marketing.api.vo.MemberConfigVO;
import com.facishare.marketing.api.vo.qywx.CreateOrUpdateWebSiteFanQrCodeVO;
import com.facishare.marketing.api.vo.wxcoupon.QueryCouponPlanVO;
import com.facishare.marketing.common.annoation.Authentication;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.typehandlers.value.FieldInfo;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.*;
import com.facishare.marketing.web.kis.arg.officialWebsite.CreateOrUpdateWebSiteFanQrCodeArg;
import com.facishare.marketing.web.kis.arg.officialWebsite.GetCustomizeFormDataByIdArg;
import com.facishare.marketing.web.kis.arg.qr.CreateQRCodeArg;
import com.facishare.marketing.web.kis.interceptor.*;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.jedis.support.MergeJedisCmd;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.params.SetParams;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Created  By zhoux 2019/12/03
 **/
@RestController
@RequestMapping("/web/officialWebsite")
@CrossOrigin
@Slf4j
public class OuterOfficialWebsiteController {

    @Autowired
    private OfficialWebsiteService officialWebsiteService;

    @Autowired
    private KisActionService kisActionService;
    
    @Autowired
    private MemberService memberService;

    @Autowired
    private WxOfficialAccountsService wxOfficialAccountsService;

    @Autowired
    private QYWXContactService qywxContactService;

    @Autowired
    private BrowserUserService browserUserService;

    @Autowired
    private CustomizeFormDataService marketingCustomizeFormDataService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private MergeJedisCmd jedisCmd;

    @Autowired
    private WxCouponPayService wxCouponPayService;

    @Autowired
    private CtaService ctaService;

    @Autowired
    private QRCodeService qrCodeService;

    @ReloadableProperty("access_blacklist")
    private String accessBlacklist;

    @ApiOperation(value = "查询追踪页面列表")
    @RequestMapping(value = "/queryWebsiteTrackData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<PageResult<QueryWebsiteTrackDataResult>> queryWebsiteTrackData(@RequestBody com.facishare.marketing.web.kis.arg.officialWebsite.QueryWebsiteTrackDataArg arg) {
        if (arg.isWrongParam()) {
            log.warn("OfficialWebsiteController.queryWebsiteTrackData param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        QueryWebsiteTrackDataArg queryWebsiteTrackDataArg = BeanUtil.copy(arg, QueryWebsiteTrackDataArg.class);
        return officialWebsiteService.queryWebsiteTrackData(queryWebsiteTrackDataArg);
    }

    @ApiOperation(value = "查询兼容旧官网页面ID")
    @RequestMapping(value = "/queryWebsiteId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<GetWebsiteByIdResult> queryWebsiteId(@RequestBody com.facishare.marketing.web.kis.arg.officialWebsite.QueryWebsiteTrackDataArg arg) {
        if (arg.isWrongParam()) {
            log.warn("OfficialWebsiteController.queryWebsiteTrackData param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        QueryWebsiteTrackDataArg queryWebsiteTrackDataArg = BeanUtil.copy(arg, QueryWebsiteTrackDataArg.class);
        return officialWebsiteService.queryWebsiteId(queryWebsiteTrackDataArg);
    }

    @ApiOperation(value = "官网埋点接口")
    @RequestMapping(value = "/record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @Authentication
    public Result record(@RequestBody com.facishare.marketing.web.kis.arg.officialWebsite.RecordArg arg,HttpServletRequest request) {
        if (arg.isWrongParam()) {
            log.warn("OuterOfficialWebsiteController.record param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.contains(accessBlacklist, arg.getEa())) {
            log.warn("accessBlacklist contains this ea arg:{}", arg);
            return new Result<>(SHErrorCode.SERVER_BUSY);
        }
        RecordActionArg vo = BeanUtil.copy(arg, RecordActionArg.class);
        //设置IP地址和User-Agent
        String ipAddr = HttpUtil.getClientIpAddr(request);
        vo.setIpAddress(ipAddr);
        if (arg.getObjectType() == null) {
            vo.setObjectType(ObjectTypeEnum.OFFICIAL_WEBSITE_TRACK.getType());
        } else {
            vo.setObjectType(arg.getObjectType());
        }
        vo.setChannelType(MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType());
        vo.setMarketingScene(MarketingSceneEnum.OFFICIAL_WEBSITE.getCode());
        vo.setChannelValue(SpreadChannelEnum.OFFICIAL_WEBSITE.getCode());
        vo.setFingerPrint(arg.getWebSiteUid());
        return kisActionService.record(vo);
    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @ApiOperation(value = "官网数据埋点")
    @RequestMapping(value = "/fRecord", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result fRecord(com.facishare.marketing.web.kis.arg.RecordActionArg arg) {
        if (arg == null || arg.getActionType() == null) {
            log.warn("KisActionController record ParamError arg: {}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        if (!MarketingUserActionType.LOOK_UP_ACTION_ONLY_UPDATE_TYPES.contains(MarketingUserActionType.fromType(arg.getActionType()))) {
            return Result.newSuccess();
        }
        RecordActionArg vo = BeanUtil.copy(arg, RecordActionArg.class);
        if (arg.getObjectType() == null) {
            vo.setObjectType(ObjectTypeEnum.OFFICIAL_WEBSITE_TRACK.getType());
        } else {
            vo.setObjectType(arg.getObjectType());
        }
        vo.setChannelType(MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType());
        vo.setMarketingScene(MarketingSceneEnum.OFFICIAL_WEBSITE.getCode());
        vo.setChannelValue(SpreadChannelEnum.OFFICIAL_WEBSITE.getCode());
        vo.setFingerPrint(arg.getWebSiteUid());
        vo.setFRecord(true);
        return kisActionService.record(vo);
    }
    
    @RequestMapping(value = "/getMemberConfig", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<MemberConfigVO> getMemberConfig(@RequestBody EaArg arg) {
        if(arg.paramWrong()){
            log.warn("OuterOfficialWebsiteController.getMemberConfig param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return memberService.getMemberConfig(arg.getEa());
    }
    
    @RequestMapping(value = "/checkIsMember", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<String> checkIsMember(@RequestBody EaArg arg, HttpServletRequest httpServletRequest) {
        if(arg.paramWrong()){
            log.warn("OuterOfficialWebsiteController.checkIsMember param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Map<String, String> memberCookies =  HttpRequestInterceptor.extractMemberCookie(httpServletRequest);
        if (memberCookies.isEmpty()){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
      //  return memberService.checkH5UserIsMember(arg.getEa(), memberCookies);
        return memberService.checkH5UserHaveMemberAuthReview(arg.getEa(), null,null,memberCookies,false);
    }
    
    @RequestMapping(value = "/listMemberAccessibleWebsite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<List<MemberAccessibleWebsiteVo>> listMemberAccessibleWebsite(@RequestBody EaArg arg) {
        if(arg.paramWrong()){
            log.warn("OuterOfficialWebsiteController.listMemberAccessibleWebsite param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return memberService.listMemberAccessibleWebsite(arg.getEa());
    }
    
    @RequestMapping(value = "/memberEnroll", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<String> memberEnroll(@RequestBody MemberEnrollArg arg, HttpServletRequest httpServletRequest) {
        if(arg.isWrongParam()){
            log.warn("OuterOfficialWebsiteController.memberEnroll param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ipAddress = httpServletRequest.getRemoteAddr();
        arg.setIpAddr(ipAddress);
        Result<MemberEnrollResult> memberEnrollResultResult = memberService.h5MemberEnroll(HttpRequestInterceptor.extractMemberCookie(httpServletRequest), arg);
        if (!memberEnrollResultResult.isSuccess()) {
            return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
        }
        if (memberEnrollResultResult.getData() == null) {
            return Result.newSuccess(null);
        }
        return Result.newSuccess(memberEnrollResultResult.getData().getLeadId());
    }
    
    @RequestMapping(value = "/checkUserInMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<Boolean> checkUserInMarketingEvent(@RequestBody CheckUserInMarketingEventArg arg, HttpServletRequest httpServletRequest) {
        if(arg.isWrongParam()){
            log.warn("OuterOfficialWebsiteController.memberEnroll param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Map<String, String> memberCookies =  HttpRequestInterceptor.extractMemberCookie(httpServletRequest);
        String fingerPrint = HttpRequestInterceptor.extractFingerPrint(httpServletRequest);
        if (memberCookies.isEmpty()){
            return Result.newSuccess(false);
        }
        Result<MemberEnrollResult> memberEnrollResultResult = memberService.checkH5UserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), fingerPrint, memberCookies, arg.getMarketingEventId());
        if (!memberEnrollResultResult.isSuccess()) {
            return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
        }
        if (memberEnrollResultResult.getData() == null) {
            return Result.newSuccess(false);
        }
        return Result.newSuccess(memberEnrollResultResult.getData().getMemberEnroll());
    }

    @RequestMapping(value = "createOfficialWebsiteWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建官网引流公众号渠道二维码")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<OfficialWebsiteWxQrCodeResult> createOfficialWebsiteWxQrCode(@RequestBody CreateOfficialWebsiteWxQrCodeArg arg) {
        if (StringUtils.isEmpty(arg.getSceneId()) || StringUtils.isBlank(arg.getBrowserUserId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return wxOfficialAccountsService.createOfficialWebsiteWxQrCode(arg);
    }

    @ApiOperation(value = "创建官网引流吸粉二维码-官网上打开二维码，产生一个临时二维码")
    @RequestMapping(value = "/createOrUpdateWebsiteFanQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<QueryAddfanQrCodeResult> createOrUpdateWebsiteFanQrCode(@RequestBody CreateOrUpdateWebSiteFanQrCodeArg arg, HttpServletRequest request){
        //针对错误参数，不上报错误
        if (arg == null || StringUtils.isBlank(arg.getFanQrCodeId()) || !arg.checkWebsiteUserId()){
            log.info("OfficialWebsiteController.createOrUpdateWebsiteFanQrCode failed arg error arg:{}",arg);
            return Result.newSuccess();
        }
        String ipAddr = request.getRemoteAddr();
        arg.setIpAddr(ipAddr);
        CreateOrUpdateWebSiteFanQrCodeVO vo = BeanUtil.copy(arg, CreateOrUpdateWebSiteFanQrCodeVO.class);
        return qywxContactService.createOrUpdateWebsiteFanQrCode(vo);
    }


    @RequestMapping(value = "/recordUtmParam", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<Void> recordUtmParam(@RequestBody RecordUtmParamArg arg, HttpServletRequest request) {
//        if(arg==null || !arg.checkParam()){
//            return null;
//        }
//        //设置IP地址
//        arg.setIpAddress(request.getRemoteAddr());
//        return browserUserService.recordUtmParam(arg);
        // 这个接口合并到上面的record接口了
        return Result.newSuccess();
    }

    @RequestMapping(value = "/getFormDataById", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<CustomizeFormDataDetailResult> getCustomizeFormDataById(@RequestBody GetCustomizeFormDataByIdArg arg, HttpServletRequest httpServletRequest) {
        if (StringUtils.isBlank(arg.getId()) || StringUtils.isBlank(arg.getEa())) {
            log.warn("OuterOfficialWebsiteController.getCustomizeFormDataById param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        Result<Boolean> checkResult = isFromOfficialWebSite(arg.getEa(), arg.getReferer(), arg.getDebug());
        if (!checkResult.isSuccess()) {
            return Result.newError(checkResult.getErrCode(), checkResult.getErrMsg());
        }
        com.facishare.marketing.api.arg.GetCustomizeFormDataByIdArg customizeFormDataByIdArg = BeanUtil.copy(arg, com.facishare.marketing.api.arg.GetCustomizeFormDataByIdArg.class);
        Result<CustomizeFormDataDetailResult> result = marketingCustomizeFormDataService.getCustomizeFormDataById(customizeFormDataByIdArg);
        if (result.isSuccess() && result.getData() != null && !MapUtils.isEmpty(result.getData().getCustomizeApinameMapping())) {
            //返回表单组件的自定义apiname--只针对官网场景
            CustomizeFormDataDetailResult customizeFormDataDetail = result.getData();
            for (FieldInfo fieldInfo : customizeFormDataDetail.getFormBodySetting()) {
                if (StringUtils.isBlank(fieldInfo.getApiName())) {
                    continue;
                }
                if (customizeFormDataDetail.getCustomizeApinameMapping().get(fieldInfo.getApiName()) != null){
                    fieldInfo.setApiName(customizeFormDataDetail.getCustomizeApinameMapping().get(fieldInfo.getApiName()).toString());
                }
            }
        }
        return result;
    }

    @RequestMapping(value = "/formDataEnroll", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<CustomizeFormDataEnrollResult> CustomizeFormDataEnroll(@RequestBody com.facishare.marketing.web.kis.arg.CustomizeFormDataEnrollArg arg, HttpServletRequest request, HttpServletResponse response) {
        if (arg.isWrongParam() || StringUtils.isBlank(arg.getEa())) {
            log.warn("OuterCustomizeFormDataController.CustomizeFormDataEnroll param error arg{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        Result<Boolean> checkResult = isFromOfficialWebSite(arg.getEa(), arg.getReferer(), arg.getDebug());
        if (!checkResult.isSuccess()) {
            return Result.newError(checkResult.getErrCode(), checkResult.getErrMsg());
        }
        //设置IP地址和User-Agent
        String ipAddr = HttpUtil.getClientIpAddr(request);
        String userAgent = arg.getUserAgent();
        if (arg.getSpreadFsUid() != null && arg.getSpreadFsUid() == 0) {
            arg.setSpreadFsUid(null);
        }
        // 替换channelValue中的++ 为 %，适配小程序不能有%
        arg.setChannelValue(TextUtil.replaceText(arg.getChannelValue(), "++", "%"));
        arg.setChannelValue(UrlUtils.urlDecode(arg.getChannelValue()));
        arg.setMarketingActivityId(StringReplaceUtils.errorStrReplaceToNull(arg.getMarketingActivityId()));
        arg.setMarketingEventId(StringReplaceUtils.errorStrReplaceToNull(arg.getMarketingEventId()));
        arg.setIpAddr(ipAddr);
        //处理手机,邮箱,姓名等报名内容存在空格问题(2022.03.01)
        if (arg.getSubmitContent() != null) {
            CustomizeFormDataEnroll submitContent = arg.getSubmitContent();
            if (StringUtils.isNotEmpty(submitContent.getPhone())) {
                submitContent.setPhone(submitContent.getPhone().trim());
            }
            if (StringUtils.isNotEmpty(submitContent.getEmail())) {
                submitContent.setEmail(submitContent.getEmail().trim());
            }
            if (StringUtils.isNotEmpty(submitContent.getName())) {
                submitContent.setName(submitContent.getName().trim());
            }
            //第一次推广人&第一次推广物料名称
            if (submitContent.getSourceObjectType()!=null && StringUtils.isNotEmpty(submitContent.getSourceObjectId())) {
                submitContent.setSourceObjectType(submitContent.getSourceObjectType());
                submitContent.setSourceObjectId(submitContent.getSourceObjectId());
            }
            if (StringUtils.isNotEmpty(submitContent.getPassword())) {
                submitContent.setPassword(new String(Base64.getDecoder().decode(submitContent.getPassword()), StandardCharsets.UTF_8));
            }
            arg.setSubmitContent(submitContent);
        }
        // 无身份报名
        CustomizeFormDataEnrollArg customizeFormDataEnrollArg = BeanUtil.copy(arg, CustomizeFormDataEnrollArg.class);
        customizeFormDataEnrollArg.setFingerPrint(arg.getWebSiteUid());
        customizeFormDataEnrollArg.setAllEnterpriseMemberCookieMap(BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap());
        customizeFormDataEnrollArg.setUserAgent(userAgent);
        customizeFormDataEnrollArg.setIpAddr(ipAddr);
        customizeFormDataEnrollArg.setPartner(arg.getPartner() != null && arg.getPartner());
        Result<CustomizeFormDataEnrollResult> result = marketingCustomizeFormDataService.noIdentityFormDataEnroll(customizeFormDataEnrollArg);
        if (result.getData() != null){
            Optional<Cookie> cookieOptional = result.getData().buildCookie();
            if (cookieOptional.isPresent()){
                response.addCookie(cookieOptional.get());
            }
        }
        return result;
    }
    
    @RequestMapping(value = "/getAreaData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<AreaContainerResult> getAreaData(@RequestBody GetAreaDataArg arg, HttpServletRequest httpServletRequest) {
        if (StringUtils.isBlank(arg.getEa())) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        Result<Boolean> checkResult = isFromOfficialWebSite(arg.getEa(), arg.getReferer(), arg.getDebug());
        if (!checkResult.isSuccess()) {
            return Result.newError(checkResult.getErrCode(), checkResult.getErrMsg());
        }
        log.info("OuterOfficialWebsiteController.getAreaData start ea:{},referer:{}",  arg.getEa(), arg.getReferer());
        return marketingCustomizeFormDataService.getAreaData(arg);
    }

    @RequestMapping(value = "/getAreaNameByKeyword", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<AreaByKeywordResult> getAreaNameByKeyword(AreaNameByKeywordArg arg){
        return marketingCustomizeFormDataService.getAreaNameByKeyword(UserInfoKeeper.getEa(), arg.getKeyword(), arg.getAreaType());
    }

    @RequestMapping(value = "/getZoneByParent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<AreaByParentResult> getZoneByParent(ZoneByParentArg arg){
        return marketingCustomizeFormDataService.getZoneByParent(UserInfoKeeper.getEa(), arg.getParentId(), arg.getCascadeLevel());
    }

    @RequestMapping(value = "/batchQueryLocationInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    Result<LocationResult> batchQueryLocationInfo(BatchQueryLocationInfoArg arg){
       return marketingCustomizeFormDataService.batchQueryLocationInfo(UserInfoKeeper.getEa(), arg.getCodes());
    }

    @RequestMapping(value = "sendSMCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @Authentication
    public Result sendSMCode(@RequestBody SendSMCodeArg arg, HttpServletRequest httpServletRequest) {
        if (arg.isWrongParam() || StringUtils.isBlank(arg.getWebSiteUid()) || StringUtils.isBlank(arg.getEa())) {
            log.warn("OuterArticleController.queryArticleDetail param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.equals("56d1f00588d848bca1d49ef387074a8e", arg.getObjectId())) {
            log.info("OuterAccountController.sendSMCode 异常刷验证码 arg:{}", arg);
            Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        //验证手机格式
        if (!PhoneNumberCheck.isPhoneLegal(arg.getPhone())) {
            log.info("OuterAccountController.sendSMCode failed phone number is illegal phone:{}", arg.getPhone());
            return Result.newError(SHErrorCode.PHONE_NUMBER_ILLEGAL);
        }
        String value = jedisCmd.get(arg.getWebSiteUid());
        if (StringUtils.isNotBlank(value)) {
            return Result.newError(SHErrorCode.SEND_PHONE_CHECK_ONE_MINUTE_LATER);
        }

        Result<Boolean> checkResult = isFromOfficialWebSite(arg.getEa(), arg.getReferer(), arg.getDebug());
        if (!checkResult.isSuccess()) {
            return Result.newError(checkResult.getErrCode(), checkResult.getErrMsg());
        }

        // 如果设置成功 会返回OK
        String val = jedisCmd.set(arg.getWebSiteUid(), "1", SetParams.setParams().nx().ex(60L));
        if (StringUtils.isBlank(val)) {
            return Result.newError(SHErrorCode.SEND_PHONE_CHECK_ONE_MINUTE_LATER);
        }

        return accountService.sendSMCode(arg.getPhone(), arg.getObjectType(), arg.getObjectId());
    }

    private Result<Boolean> isFromOfficialWebSite(String ea, String referer, String debug) {
        if (StringUtils.isNotBlank(debug) && "yxtnb".equals(debug)) {
            return Result.newSuccess();
        }
        if (StringUtils.isBlank(referer)) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_443));
        }
        Result<List<QueryPageDataResult>> result = officialWebsiteService.queryByEa(ea);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_447));
        }
        URI uri = null;
        try {
            uri = new URI(referer);
        } catch (URISyntaxException e) {
            log.warn("isFromOfficialWebSite ea: {} referer: {}", ea, referer);
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_454));
        }
        String host = uri.getHost();
        for (QueryPageDataResult officialWebsite : result.getData()) {
            String websiteUrl = officialWebsite.getWebsiteUrl();
            if (websiteUrl.contains(host)) {
                return Result.newSuccess();
            }
        }
        return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_463));
    }

    @Deprecated
    @RequestMapping(value = "queryOfficialWebsiteLoginWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @ApiOperation(value = "查询官网的公众号登录二维码")
    public Result<OfficialWebsiteWxQrCodeResult> queryOfficialWebsiteLoginWxQrCode(@RequestBody QueryOfficialWebsiteWxQrCodeArg arg) {
        if (StringUtils.isEmpty(arg.getEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.queryOfficialWebsiteLoginWxQrCode(arg);
    }

    @Deprecated
    @RequestMapping(value = "createOfficialWebsiteLoginWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @ApiOperation(value = "创建官网登录公众号渠道二维码")
    public Result<OfficialWebsiteWxQrCodeResult> createOfficialWebsiteLoginWxQrCode(@RequestBody CreateOfficialWebsiteWxQrCodeArg arg) {
        if (StringUtils.isEmpty(arg.getSceneId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.createOfficialWebsiteLoginWxQrCode(arg);
    }

    @Deprecated
    @RequestMapping(value = "getMemberInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @ApiOperation(value = "获取会员信息")
    public Result<CustomizeFormDataEnrollResult> getMemberInfo(@RequestBody JSONObject arg) {
        String browserUserId = arg.getString("browserUserId");
        if (StringUtils.isEmpty(browserUserId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.getMemberInfo(browserUserId);
    }

    @RequestMapping(value = "queryCouponPlanList",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @ApiOperation(value = "根据优惠券id获取券方案列表")
    public Result<List<CouponPlanInfoResult>> queryCouponPlanList(@RequestBody QueryCouponPlanArg arg){
        QueryCouponPlanVO vo = new QueryCouponPlanVO();
        vo.setCouponNos(arg.getCouponNos());
        vo.setTenantId(arg.getTenantId());
        return wxCouponPayService.queryCouponPlanList(vo);
    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/queryCtaSimpleDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据ctaId获取cta详情")
    public Result<QueryCtaSimpleDetailResult> queryCtaSimpleDetail(@RequestBody com.facishare.marketing.web.kis.arg.cta.QueryCtaSimpleDetailArg arg) {
        QueryCtaSimpleDetailArg vo = BeanUtil.copy(arg, QueryCtaSimpleDetailArg.class);
        if(StringUtils.isNotBlank(arg.getWebSiteUid())) {
            vo.setBrowserUserId(arg.getWebSiteUid());
        }
        Result<QueryCtaSimpleDetailResult> result = ctaService.queryCtaSimpleDetail(arg.getEa(), vo);
        return result;
    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/createWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建关注公众号二维码")
    public Result<CreateCtaWxQrCodeResult> createWxQrCode(@RequestBody com.facishare.marketing.web.kis.arg.cta.CreateCtaWxQrCodeArg arg) {
        CreateCtaWxQrCodeArg vo = BeanUtil.copy(arg, CreateCtaWxQrCodeArg.class);
        if(vo.getFsUserId() == null) {
            vo.setFsUserId(-10000);
        }
        return ctaService.createWxQrCode(vo.getEa(), vo.getFsUserId(), vo);
    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/createQywxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建添加企业微信好友二维码")
    public Result<CreateCtaWxQrCodeResult> createQywxQrCode(@RequestBody com.facishare.marketing.web.kis.arg.cta.CreateCtaQywxQrCodeArg arg) {
        CreateCtaQywxQrCodeArg vo = BeanUtil.copy(arg, CreateCtaQywxQrCodeArg.class);
        if(vo.getFsUserId() == null) {
            vo.setFsUserId(-10000);
        }
        return ctaService.createQywxQrCode(vo.getEa(), vo.getFsUserId(), vo);
    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/pollingWxQrCodeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "轮询关注公众号二维码状态")
    public Result<Boolean> pollingWxQrCodeStatus(@RequestBody com.facishare.marketing.web.kis.arg.cta.PollingQrCodeStatusArg arg) {
        PollingQrCodeStatusArg vo = BeanUtil.copy(arg, PollingQrCodeStatusArg.class);
        return ctaService.pollingWxQrCodeStatus(arg.getEa(), arg.getFsUserId(), vo);
    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/pollingQywxQrCodeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "轮询添加企业微信好友二维码状态")
    public Result<Boolean> pollingQywxQrCodeStatus(@RequestBody com.facishare.marketing.web.kis.arg.cta.PollingQrCodeStatusArg arg) {
        PollingQrCodeStatusArg vo = BeanUtil.copy(arg, PollingQrCodeStatusArg.class);
        return ctaService.pollingQywxQrCodeStatus(arg.getEa(), arg.getFsUserId(), vo);
    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @ApiOperation(value = "创建二维码")
    @RequestMapping(value = "/createQRCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CreateQRCodeResult> createQRCode(@RequestBody CreateQRCodeArg arg) {
        if (arg.isPartner()) {
            return qrCodeService.createQRCode(arg.getEa(), null, arg.getType(), arg.getValue(), arg.getLengthOfSide(), null);
        } else {
            return qrCodeService.createQRCode(arg.getEa(), -10000, arg.getType(), arg.getValue(), arg.getLengthOfSide(), null);
        }
    }
}