package com.facishare.marketing.web.kis.controller.outer;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.arg.CreateCustomizeFormWXQrCodeArg;
import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.CustomizeFormDataShowSettingArg;
import com.facishare.marketing.api.arg.ExecuteEnrollCustomizeFunctionArg;
import com.facishare.marketing.api.result.CreateCustomizeFormWXQrCodeResult;
import com.facishare.marketing.api.result.CustomizeFormDataEnrollResult;
import com.facishare.marketing.api.result.CustomizeFormDataShowSettingResult;
import com.facishare.marketing.api.result.ExecuteEnrollCustomizeFunctionResult;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.common.annoation.Authentication;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.interceptor.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Optional;

/**
 * Created  By zhoux 2019/06/25
 **/
@RestController
@RequestMapping("/web/customizeFormData")
@Slf4j
@CrossOrigin
@FcpService("customizeFormData")
public class OuterCustomizeFormDataController {

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    /**
     * 表单显示设置
     * @param arg
     * @return
     */
    @ApiOperation(value = "表单显示设置")
    @RequestMapping(value = "/showSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<CustomizeFormDataShowSettingResult> CustomizeFormDataShowSetting(@RequestBody com.facishare.marketing.web.kis.arg.CustomizeFormDataShowSettingArg arg) {
        if(arg.isWrongParam()) {
            log.warn("OuterCustomizeFormDataController.CustomizeFormDataShowSetting param error arg{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            log.warn("OuterCustomizeFormDataController.CustomizeFormDataShowSetting error identityCheckType error identityCheckType is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            CustomizeFormDataShowSettingArg customizeFormDataShowSettingArg = BeanUtil.copy(arg, CustomizeFormDataShowSettingArg.class);
            customizeFormDataShowSettingArg.setEnrollUserEa(UserInfoKeeper.getEa());
            customizeFormDataShowSettingArg.setEnrollUserFsUid(UserInfoKeeper.getFsUserId());
            customizeFormDataShowSettingArg.setEa(UserInfoKeeper.getEa());
            return customizeFormDataService.customizeFormDataShowSetting(customizeFormDataShowSettingArg);
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            CustomizeFormDataShowSettingArg customizeFormDataShowSettingArg = BeanUtil.copy(arg, CustomizeFormDataShowSettingArg.class);
            customizeFormDataShowSettingArg.setOpenId(OuterUserInfoKeeper.getOpenId());
            customizeFormDataShowSettingArg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
            customizeFormDataShowSettingArg.setEa(UserInfoKeeper.getEa());
            return customizeFormDataService.customizeFormDataShowSetting(customizeFormDataShowSettingArg);
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            CustomizeFormDataShowSettingArg customizeFormDataShowSettingArg = BeanUtil.copy(arg, CustomizeFormDataShowSettingArg.class);
            customizeFormDataShowSettingArg.setFingerPrint(BrowserUserInfoKeeper.getFingerPrint());
            customizeFormDataShowSettingArg.setAllMemberCookieInfos(BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap());
            return customizeFormDataService.customizeFormDataShowSetting(customizeFormDataShowSettingArg);
        }
        log.warn("OuterCustomizeFormDataController.CustomizeFormDataShowSetting error identityCheckType error identityCheckType:{}", identityCheckType);
        return new Result<>(SHErrorCode.SYSTEM_ERROR);
    }

    /**
     * 推广表单报名
     * @param arg
     * @return
     */
    @ApiOperation(value = "推广表单报名")
    @RequestMapping(value = "/formDataEnroll", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @GetOuterIdTrigger
    @FcpMethod("formDataEnroll")
//    @Authentication
    public Result<CustomizeFormDataEnrollResult> CustomizeFormDataEnroll(@RequestBody com.facishare.marketing.web.kis.arg.CustomizeFormDataEnrollArg arg, HttpServletRequest request, HttpServletResponse response) {
        if (arg.isWrongParam()) {
            log.warn("OuterCustomizeFormDataController.CustomizeFormDataEnroll param error arg{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        //设置IP地址和User-Agent
        String ipAddr = HttpUtil.getClientIpAddr(request);
        String userAgent = arg.getUserAgent();
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (arg.getSpreadFsUid() != null && arg.getSpreadFsUid() == 0) {
            arg.setSpreadFsUid(null);
        }
        // 替换channelValue中的++ 为 %，适配小程序不能有%
        arg.setChannelValue(TextUtil.replaceText(arg.getChannelValue(), "++", "%"));
        arg.setChannelValue(UrlUtils.urlDecode(arg.getChannelValue()));
        arg.setMarketingActivityId(StringReplaceUtils.errorStrReplaceToNull(arg.getMarketingActivityId()));
        arg.setMarketingEventId(StringReplaceUtils.errorStrReplaceToNull(arg.getMarketingEventId()));
        arg.setIpAddr(ipAddr);
        //处理手机,邮箱,姓名等报名内容存在空格问题(2022.03.01)
        if (arg.getSubmitContent() != null) {
            CustomizeFormDataEnroll submitContent = arg.getSubmitContent();
            if (StringUtils.isNotEmpty(submitContent.getPhone())) {
                submitContent.setPhone(submitContent.getPhone().trim());
            }
            if (StringUtils.isNotEmpty(submitContent.getEmail())) {
                submitContent.setEmail(submitContent.getEmail().trim());
            }
            if (StringUtils.isNotEmpty(submitContent.getName())) {
                submitContent.setName(submitContent.getName().trim());
            }
            //第一次推广人&第一次推广物料名称
            if (submitContent.getSourceObjectType()!=null && StringUtils.isNotEmpty(submitContent.getSourceObjectId())) {
                submitContent.setSourceObjectType(submitContent.getSourceObjectType());
                submitContent.setSourceObjectId(submitContent.getSourceObjectId());
            }
            if (StringUtils.isNotEmpty(submitContent.getPassword())) {
                submitContent.setPassword(new String(Base64.getDecoder().decode(submitContent.getPassword()), StandardCharsets.UTF_8));
            }
            arg.setSubmitContent(submitContent);
        }
        if (identityCheckType == null) {
            log.warn("OuterCustomizeFormDataController.CustomizeFormDataEnroll error identityCheckType error identityCheckType is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            // 纷享身份报名
            CustomizeFormDataEnrollArg customizeFormDataEnrollArg = BeanUtil.copy(arg, CustomizeFormDataEnrollArg.class);
            customizeFormDataEnrollArg.setEnrollUserEa(UserInfoKeeper.getEa());
            customizeFormDataEnrollArg.setEnrollUserFsUid(UserInfoKeeper.getFsUserId());
            customizeFormDataEnrollArg.setUserAgent(userAgent);
            customizeFormDataEnrollArg.setIpAddr(ipAddr);
            customizeFormDataEnrollArg.setEa(UserInfoKeeper.getEa());
            customizeFormDataEnrollArg.setPartner(arg.getPartner() != null && arg.getPartner());
            Result<CustomizeFormDataEnrollResult> result = customizeFormDataService.fsCustomizeFormDataEnroll(customizeFormDataEnrollArg);
            if (result.getData() != null){
                Optional<Cookie> cookieOptional = result.getData().buildCookie();
                if (cookieOptional.isPresent()){
                    response.addCookie(cookieOptional.get());
                }
            }
            return result;
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            // 公众号营销
            CustomizeFormDataEnrollArg customizeFormDataEnrollArg = BeanUtil.copy(arg, CustomizeFormDataEnrollArg.class);
            customizeFormDataEnrollArg.setOpenId(OuterUserInfoKeeper.getOpenId());
            customizeFormDataEnrollArg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
            customizeFormDataEnrollArg.setUserAgent(userAgent);
            customizeFormDataEnrollArg.setIpAddr(ipAddr);
            customizeFormDataEnrollArg.setPartner(arg.getPartner() != null && arg.getPartner());
            return customizeFormDataService.wxCustomizeFormDataEnroll(customizeFormDataEnrollArg);
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            // 无身份报名
            CustomizeFormDataEnrollArg customizeFormDataEnrollArg = BeanUtil.copy(arg, CustomizeFormDataEnrollArg.class);
            String fingerPrint = BrowserUserInfoKeeper.getFingerPrint();
            if (StringUtils.isBlank(fingerPrint)) {
                if (!(StringUtils.isNotBlank(customizeFormDataEnrollArg.getFingerPrint())
                        || StringUtils.isNotBlank(customizeFormDataEnrollArg.getUid())
                        || (StringUtils.isNotBlank(customizeFormDataEnrollArg.getEnrollUserEa())))) {
                    fingerPrint = UUIDUtil.getUUID();
                }
            }
            customizeFormDataEnrollArg.setFingerPrint(fingerPrint);
            customizeFormDataEnrollArg.setAllEnterpriseMemberCookieMap(BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap());
            customizeFormDataEnrollArg.setUserAgent(userAgent);
            customizeFormDataEnrollArg.setIpAddr(ipAddr);
            customizeFormDataEnrollArg.setPartner(arg.getPartner() != null && arg.getPartner());
            log.info("fingerPrint is :{}  allEnterpriseMemberCookieMap is :{}", customizeFormDataEnrollArg.getFingerPrint(), customizeFormDataEnrollArg.getAllEnterpriseMemberCookieMap());
            Result<CustomizeFormDataEnrollResult> result = customizeFormDataService.noIdentityFormDataEnroll(customizeFormDataEnrollArg);
            if (result.getData() != null){
                Optional<Cookie> cookieOptional = result.getData().buildCookie();
                if (cookieOptional.isPresent()){
                    response.addCookie(cookieOptional.get());
                }
            }
            return result;
        }
        log.warn("OuterCustomizeFormDataController.CustomizeFormDataEnroll error identityCheckType error identityCheckType:{}", identityCheckType);
        return new Result<>(SHErrorCode.SYSTEM_ERROR);
    }

    /**
     * 创建带参数二维码
     * @param arg
     * @return
     */
    @ApiOperation(value = "创建带参数二维码")
    @RequestMapping(value = "/createCustomizeFormWXQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<CreateCustomizeFormWXQrCodeResult> createCustomizeFormWXQrCode(@RequestBody com.facishare.marketing.web.kis.arg.CreateCustomizeFormWXQrCodeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("OuterCustomizeFormDataController.createCustomizeFormWXQrCode param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        CreateCustomizeFormWXQrCodeArg createCustomizeFormWXQrCodeArg = BeanUtil.copy(arg, CreateCustomizeFormWXQrCodeArg.class);
        return customizeFormDataService.createCustomizeFormWXQrCode(createCustomizeFormWXQrCodeArg);
    }

    @ApiOperation(value = "执行报名函数")
    @RequestMapping(value = "/executeEnrollCustomizeFunction", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<ExecuteEnrollCustomizeFunctionResult> executeEnrollCustomizeFunction(@RequestBody com.facishare.marketing.web.kis.arg.ExecuteEnrollCustomizeFunctionArg arg) {
        if(arg.isWrongParam()) {
            log.warn("OuterCustomizeFormDataController.executeEnrollCustomizeFunction param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        ExecuteEnrollCustomizeFunctionArg executeEnrollCustomizeFunctionArg =  BeanUtil.copy(arg, ExecuteEnrollCustomizeFunctionArg.class);
        return customizeFormDataService.executeEnrollCustomizeFunction(executeEnrollCustomizeFunctionArg);
    }

}
