/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.AddMaterialArg;
import com.facishare.marketing.api.arg.ListMaterialsArg;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.service.OutLinkService;
import com.facishare.marketing.api.vo.ListOutLinkVO;
import com.facishare.marketing.api.vo.OutLinkVO;
import com.facishare.marketing.api.vo.OutQrCodeVO;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

public class OutLinkServiceTest extends BaseTest {

    @Autowired
    private OutLinkService outLinkService;

    @Autowired
    private MarketingEventService marketingEventService;

    @Test
    public void addOutLinkTest() {
        String ea = "74164";
        Integer fsUserId = 1123;
        OutLinkVO vo = new OutLinkVO();
        vo.setName("百度");
        vo.setUrl("https://www.baidu.com");
        vo.setIsSupportEmbedded(true);
        vo.setTitle("百度呀");
        vo.setDescribe("百度ya");
        vo.setCover("https://www.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D34275768014958831EA7EC97C532F1A92AA928BE4F2F13BE88F39A9EE76FC2DF0EF51FD38F092DE466E6BF6B0D45910E0E6BE51A7132289C9CFA5CE593D41777627F3");
        Result<OutLinkVO> result = outLinkService.saveOutLink(ea, fsUserId, vo);
        OutLinkVO outLinkVO = result.getData();
        AddMaterialArg arg = new AddMaterialArg();
        arg.setObjectId(outLinkVO.getId());
        arg.setObjectType(ObjectTypeEnum.OUT_LINK.getType());
        arg.setMarketingEventId("62fe6bd3cb87b0000189b41b");
        marketingEventService.addMaterial(ea, fsUserId, arg);
        ListMaterialsArg param = new ListMaterialsArg();
        param.setId("62fe6bd3cb87b0000189b41b");
        param.setObjectTypes(Collections.singletonList(9999));
        PageResult<AbstractMaterialData> materialResult = marketingEventService.listMaterials(ea, fsUserId, param);
        System.out.println(materialResult);
    }

    @Test
    public void isSupportEmbeddedTest() {
        String ea = "74164";
        Integer fsUserId = 1123;
        OutLinkVO vo = new OutLinkVO();
        vo.setUrl("http://www.baidu.com");
        Result<Boolean> supportEmbedded = outLinkService.isSupportEmbedded(ea, fsUserId, vo);
        System.out.println(supportEmbedded);
    }

    @Test
    public void listOutLinkTest() {
        String ea = "74164";
        Integer fsUserId = 1123;
        ListOutLinkVO vo = new ListOutLinkVO();
        vo.setPageNum(1);
        vo.setPageSize(10);
        Result<PageResult<OutLinkVO>> pageResultResult = outLinkService.listOutLink(ea, fsUserId, vo);
        System.out.println(pageResultResult);
    }

    @Test
    public void saveOrUpdateOutQrCode(){
        OutQrCodeVO vo = new OutQrCodeVO();
        vo.setDomainNameUrl("http://www.eeeee.com");
        Result<Void> voidResult = outLinkService.saveOrUpdateOutQrCode("74164", vo);
    }

}
