/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.qywx;

import com.facishare.marketing.api.result.qywx.MomentSendListResult;
import com.facishare.marketing.api.result.qywx.SendMomentCustomerResult;
import com.facishare.marketing.api.result.qywx.SendMomentListResult;
import com.facishare.marketing.api.service.QywxMomentService;
import com.facishare.marketing.api.service.qywx.GroupSendMessageService;
import com.facishare.marketing.api.vo.qywx.QuerySendGroupMessageDataVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class QywxMomentServiceImplTest extends BaseTest {
    @Autowired
    private QywxMomentService qywxMomentService;

    @Autowired
    private GroupSendMessageService groupSendMessageService;

    @Test
    public void listMomentStaticData(){
        Result<Map<String, Integer>> mp = qywxMomentService.listMomentStaticData("82450", 1071);
        log.info("listMomentStaticData map:{}", mp);
    }

    @Test
    public void getCustomerData(){
        List<TagName> tags = new ArrayList<>();
        TagName tagName1 = new TagName();
        tagName1.setFirstTagName("客户等级");
        tagName1.setSecondTagName("一般");
        tags.add(tagName1);
//        TagName tagName2 = new TagName();
//        tagName2.setFirstTagName("客户等级");
//        tagName2.setSecondTagName("测试");
//        tags.add(tagName2
        Result<SendMomentCustomerResult> momentSendCustomerData = qywxMomentService.getMomentSendCustomerData("88146", 1000, Lists.newArrayList("-999999"), null,Lists.newArrayList(), tags);
        log.info("momentSendCustomerData result:{}", momentSendCustomerData);
    }

    @Test
    public void getCustomerList(){
        List<TagName> tags = new ArrayList<>();
//        TagName tagName1 = new TagName();
//        tagName1.setFirstTagName("客户等级");
//        tagName1.setSecondTagName("重要");
//        TagName tagName2 = new TagName();
//        tagName2.setFirstTagName("客户等级");
//        tagName2.setSecondTagName("测试");
//        tags.add(tagName1);
//        tags.add(tagName2);
//        TagName tagName1 = new TagName();
//        tagName1.setFirstTagName("客户等级");
//        tagName1.setSecondTagName("核心");
//        tags.add(tagName1);
        Result<PageResult<MomentSendListResult>> pageResultResult = qywxMomentService.queryMomentSendCustomerList("83668", 1000, Lists.newArrayList("-999999"), null, Lists.newArrayList(),tags, 10, 1);
        log.info("momentSendCustomerData result:{}", pageResultResult);
    }

    @Test
    public void getGroupSendCustomerData(){
        List<TagName> tags = new ArrayList<>();
//        TagName tagName1 = new TagName();
//        tagName1.setFirstTagName("客户等级");
//        tagName1.setSecondTagName("核心");
//        tags.add(tagName1);
        QuerySendGroupMessageDataVO vo = new QuerySendGroupMessageDataVO();
//        List<Map<String, Object>> filters = new ArrayList<>();
//        Map<String, Object> map = new HashMap<>();
//        map.put("fieldName","last_modified_time");
//        map.put("fieldValues",Lists.newArrayList(1.6649856E12,1.668095999E12));
//        map.put("fieldType",new BigDecimal(4));
//        map.put("valueType",new BigDecimal(3));
//        map.put("operator","BETWEEN");
//        filters.add(map);
//        vo.setFilters(filters);
        vo.setMarketingUserGroupIds(Lists.newArrayList("b39e1c848885482b9e0446c0f708462e"));
        vo.setSendRange(2);
        vo.setTagIdList(tags);
        vo.setUserIds(Lists.newArrayList("-999999"));
        Result<SendMomentCustomerResult> sendMomentCustomerResultResult = groupSendMessageService.queryGroupMessageSendData("83668", 1042, vo);
        log.info("momentSendCustomerData result:{}", sendMomentCustomerResultResult);
    }

    @Test
    public void getGroupSendCustomerList(){
        List<TagName> tags = new ArrayList<>();
//        TagName tagName1 = new TagName();
//        tagName1.setFirstTagName("客户等级");
//        tagName1.setSecondTagName("核心");
//        tags.add(tagName1);
        QuerySendGroupMessageDataVO vo = new QuerySendGroupMessageDataVO();
        vo.setMarketingUserGroupIds(Lists.newArrayList("b39e1c848885482b9e0446c0f708462e"));
        vo.setSendRange(2);
        vo.setUserIds(Lists.newArrayList("-999999"));
        vo.setTagIdList(tags);
        Result<PageResult<MomentSendListResult>> pageResultResult = groupSendMessageService.queryGroupMessageSendList("83668", vo, 1, 10);
        log.info("momentSendCustomerData result:{}", pageResultResult);
    }

    @Test
    public void test1(){
        List<String> userIds = Lists.newArrayList("1","2","3","4","5","6","7","8","9","10","11","12","14");
        PageUtil pageUtil = new PageUtil(userIds,10);
        List<String> pagedList = pageUtil.getPagedList(2);
        System.out.println(pagedList);
    }

    @Test
    public void sendMomentList(){
        List<TagName> tags = new ArrayList<>();
//        TagName tagName1 = new TagName();
//        tagName1.setFirstTagName("客户等级");
//        tagName1.setSecondTagName("核心");
//        tags.add(tagName1);
        String userId = "wowx1mDAAAqNNMQTuLDU0p9WOl8BCHtw";
        Result<PageResult<SendMomentListResult>> pageResultResult = qywxMomentService.querySendMomentList("83668", 1000, userId, tags, 1, 10);
        System.out.println(pageResultResult);
    }

    @Test
    public void sendGroupEmployeeList(){
        List<TagName> tags = new ArrayList<>();
        QuerySendGroupMessageDataVO vo = new QuerySendGroupMessageDataVO();
        //vo.setMarketingUserGroupIds(Lists.newArrayList("b39e1c848885482b9e0446c0f708462e"));
        vo.setSendRange(0);
        vo.setTagIdList(tags);
        vo.setUserIds(Lists.newArrayList("wowx1mDAAAo4iB6I_48wL8lQ_9_ebZ6w","wowx1mDAAAqNNMQTuLDU0p9WOl8BCHtw"));
        Result<PageResult<SendMomentListResult>> result = groupSendMessageService.queryGroupMessageSendCustomerList("83668", 1000, "wowx1mDAAAqNNMQTuLDU0p9WOl8BCHtw", vo, 1, 10);
        System.out.println(result);
    }
}
