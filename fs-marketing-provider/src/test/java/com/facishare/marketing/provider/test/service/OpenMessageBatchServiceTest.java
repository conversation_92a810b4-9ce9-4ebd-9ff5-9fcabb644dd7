package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.qixin.api.constant.MessageType;
import com.facishare.qixin.api.model.open.arg.OpenSendMessageBatchArg;
import com.facishare.qixin.api.model.open.result.OpenSendMessageBatchResult;
import com.facishare.qixin.api.model.pushSession.arg.ExternalMessage;
import com.facishare.qixin.api.open.OpenMessageBatchService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OpenMessageBatchServiceTest extends BaseTest {

    @Autowired
    private OpenMessageBatchService openMessageBatchService;

    @Test
    public void testSend(){
        OpenSendMessageBatchArg arg = new OpenSendMessageBatchArg();
        arg.setAppId("FXKE");
        arg.setEnterpriseAccount("88146");
        arg.setMessageType(MessageType.TEXT);
        ExternalMessage externalMessage = new ExternalMessage();
        externalMessage.setMessgeType(MessageType.TEXT);
        externalMessage.setTextContent("hello");
        String messageContent = externalMessage.generateMessageContent(externalMessage);
        arg.setMessageContent(messageContent);
        arg.setToUserList(Lists.newArrayList(1000));
        arg.setPostId(UUIDUtil.getUUID());
        arg.setSource("system");
        OpenSendMessageBatchResult result = openMessageBatchService.sendMessageBatch(arg);
        System.out.printf("yes:" + result);
    }
}
