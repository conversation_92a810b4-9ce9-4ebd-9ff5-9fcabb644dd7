/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.api.arg.qywx.staff.QueryQywxStaffPageArg;
import com.facishare.marketing.api.result.qywx.staff.QueryQywxStaffPageDTO;
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity;
import com.facishare.marketing.provider.innerData.qywx.QywxEmployeeToCrmArg;
import com.facishare.marketing.provider.manager.qywx.QywxAddressBookManager;
import com.facishare.marketing.provider.manager.qywx.QywxEmployeeManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import groovy.util.logging.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/27
 **/
@Slf4j
public class QywxEmployeeManagerTest extends BaseTest {

    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;

    @Test
    public void testSyncQywxAddressBookToCrm(){
        String ea = "74164";
        qywxEmployeeManager.syncQywxAddressBookToCrm(ea);
    }

    @Test
    public void test1(){
        String ea = "88146";
        List<QyWxAddressBookEntity> result = qywxAddressBookManager.queryByEa(ea);
        System.out.println("yes:" + result);
    }

    @Test
    public void test2(){
        String ea = "88146";
        String phone = "18988781928";
        List<QyWxAddressBookEntity> result = qywxAddressBookManager.queryStaffByEaAndMobile(ea, Lists.newArrayList(phone));
        System.out.println("yes:" + result);
    }

    @Test
    public void test3(){
        String ea = "88146";
        ArrayList<String> strings = Lists.newArrayList("wowx1mDAAArWFwEEdvLuwwne8wfaMAAw", "wowx1mDAAAePixceEBBJ0prGVbXwJkAw");
        List<QyWxAddressBookEntity> result = qywxAddressBookManager.queryEaAndUserId(ea, strings);
        System.out.println("yes:" + result);
    }

    @Test
    public void test4(){
        String ea = "88146";
        QyWxAddressBookEntity result = qywxAddressBookManager.queryByEaAndUserId(ea, "wowx1mDAAArWFwEEdvLuwwne8wfaMAAw");
        System.out.println("yes:" + result);
    }

    @Test
    public void test5(){
        String ea = "88146";
        Integer result = qywxAddressBookManager.countByEa(ea);
        System.out.println("yes:" + result);
    }

    @Test
    public void test6(){
        String ea = "88146";
        String str = "4";
        String s1 = "\"";
        String s2 = "\"";
        String s4 = s1 + str + s2;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("\"").append(s1).append("\"");
        List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryStaffByEaAnddepartmentIds(ea, Lists.newArrayList("\"2\""));
        System.out.println("yes:" + qyWxAddressBookEntities);
    }

    @Test
    public void test7(){
        String ea = "88146";
        String qyUserId = "wowx1mDAAAUaLL5ytn8sL0XWIJbG6qag";
        qywxEmployeeManager.removeUser(ea, qyUserId);
    }

    @Test
    public void test8(){
        QueryQywxStaffPageArg queryQywxStaffPageArg = new QueryQywxStaffPageArg();
        queryQywxStaffPageArg.setFsEa("88146");
        queryQywxStaffPageArg.setKeyword("张");
        Page<Object> page = new Page<>();
        page.setPageNo(1);
        page.setPageSize(5);
        List<QueryQywxStaffPageDTO> queryQywxStaffPageDTOS = qywxAddressBookManager.queryQywxStaffPageWithoutJoin(queryQywxStaffPageArg, page);
        System.out.println("yes:" + queryQywxStaffPageDTOS);
    }

    @Test
    public void test9(){
        String ea = "88146";
        QywxEmployeeToCrmArg arg = new QywxEmployeeToCrmArg();
        arg.setUserId("wowx1mDAAA1wyB6yscJTbR9FQI6D_JjQ");
        qywxEmployeeManager.updateAll(ea, Lists.newArrayList(arg));
        System.out.println();
    }

    @Test
    public void updateMobile(){
        String ea = "88146";
        qywxAddressBookManager.updateMobile(ea, "wowx1mDAAAGrQDAL_lRWbZU-cFQUW8OQ", "110");
        System.out.println();
    }
}
