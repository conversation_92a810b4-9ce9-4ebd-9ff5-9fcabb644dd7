package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.customizedSpread.DeleteCustomizedSpreadChannelArg;
import com.facishare.marketing.api.arg.customizedSpread.ListCustomizedSpreadChannelArg;
import com.facishare.marketing.api.arg.customizedSpread.SaveCustomizedSpreadChannelArg;
import com.facishare.marketing.api.arg.customizedSpread.UpdateCustomizedSpreadChannelStatusArg;
import com.facishare.marketing.api.result.customizedSpread.ListCustomizedSpreadChannelResult;
import com.facishare.marketing.api.result.customizedSpread.SaveCustomizedSpreadChannelResult;
import com.facishare.marketing.api.service.customizedSpread.CustomizedSpreadChannelService;
import com.facishare.marketing.common.enums.EnableOrDisableStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class CustomizedSpreadChannelServiceTest extends BaseTest {

    @Autowired
    private CustomizedSpreadChannelService customizedSpreadChannelService;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @Test
    public void testSave(){
        SaveCustomizedSpreadChannelArg arg = new SaveCustomizedSpreadChannelArg();
        arg.setName("自定义通道1");
        arg.setRemark("自定义通道1说明");
        arg.setFuncApiName("函数apiName-1");
        arg.setSendObject("MARKETING_USER.phone");
        arg.setEa("88146");
        arg.setFsUserId(1000);
        Result<SaveCustomizedSpreadChannelResult> result = customizedSpreadChannelService.save(arg);
        System.out.printf("yes: %s", result);
    }

    @Test
    public void testUpdateStatus() {
        UpdateCustomizedSpreadChannelStatusArg arg = new UpdateCustomizedSpreadChannelStatusArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setId("be09d8924af24a9b9f5ffbe9d9e674a5");
        arg.setStatus(EnableOrDisableStatusEnum.ENABLE.getValue());
        Result<Void> voidResult = customizedSpreadChannelService.updateStatus(arg);
        System.out.printf("yes: %s", voidResult);
    }

    @Test
    public void testUpdateDescription() {
        marketingActivityRemoteManager.addCustomizedSpreadTypeOptions("88146", "mk_csc_476042e2", "自定义通道1");
    }

    @Test
    public void testDelete() {
        DeleteCustomizedSpreadChannelArg arg = new DeleteCustomizedSpreadChannelArg();
        arg.setEa("88146");
        arg.setId("55439ccc232d42b380e7fead476042e2");
        Result<Void> result = customizedSpreadChannelService.delete(arg);
        System.out.printf("yes: %s", result);
    }

    @Test
    public void testList() {
        ListCustomizedSpreadChannelArg arg = new ListCustomizedSpreadChannelArg();
        arg.setEa("88146");
        Result<List<ListCustomizedSpreadChannelResult>> result = customizedSpreadChannelService.list(arg);
        System.out.printf("yes: %s", result);
    }

}
