/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.qywx;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.qywx.QywxWelcomeMsgResult;
import com.facishare.marketing.api.vo.qywx.DeleteWelcomeMsgVO;
import com.facishare.marketing.api.vo.qywx.QueryMsgListVO;
import com.facishare.marketing.api.vo.qywx.UpdateWelcomeMsgVO;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxWelcomeMsgDAO;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxWelcomeMsgEntity;
import com.facishare.marketing.provider.innerData.qywx.ExternalUserMagCallBackData;
import com.facishare.marketing.provider.innerResult.qywx.UploadMediaResult;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxContactManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.google.common.collect.Lists;

import com.facishare.marketing.api.service.qywx.QywxWelcomeMsgService;
import com.facishare.marketing.api.vo.qywx.QywxWelcomeMsgVO;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/7 15:52
 */
@Slf4j
public class QywxWelcomeMsgTest extends BaseTest {

    @Autowired
    private QywxWelcomeMsgService qywxWelcomeMsgService;

    @Autowired
    private QywxContactManager qywxContactManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private HttpManager httpManager;

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxWelcomeMsgDAO qywxWelcomeMsgDAO;

    @Test
    public void saveMsg(){
        QywxWelcomeMsgVO vo = new QywxWelcomeMsgVO();
        List<String> userIds = new ArrayList<>();
        userIds.add("xiaoyanjing");
        vo.setEa("74164");
        vo.setWelcomeMsgName("测试部门看看");
        vo.setType(1);
        vo.setUserIdList(userIds);
        vo.setWelcomeMsgContent("很高兴添加你为好友,请多关照");
        vo.setContentType(1);
        vo.setLinkCover("www.baidu.com/pageId=12409404904");
        vo.setLinkCoverPath("A_3dkkfpgkgogph.jpg");
        vo.setLinkType(1);
        vo.setLinkContentUrl("www.baidu.com");
        vo.setOperator(1123);
        vo.setDepartmentIds(Lists.newArrayList(10002));
        Result<Void> result = qywxWelcomeMsgService.saveWelcomeMsg(vo);
        log.info("result: {} ", GsonUtil.getGson().toJson(result));
    }

    @Test
    public void queryMsgList() {
        QueryMsgListVO vo = new QueryMsgListVO();
        vo.setEa("88146");
        vo.setPageSize(10);
        vo.setPageNum(1);
        vo.setGroupId("-1");
        vo.setUserId(1000);
        Result<PageResult<QywxWelcomeMsgResult>> pageResultResult = qywxWelcomeMsgService.queryMsgList(vo);
    }

    @Test
    public void updateMsg(){
        UpdateWelcomeMsgVO vo = new UpdateWelcomeMsgVO();
        vo.setId("c611488210094ef5b8bf72e47a4d5fd4");
        vo.setWelcomeMsgName("我修改一下看看");
        List<String> userIds = new ArrayList<>();
        userIds.add("XiaoMaJia");
        vo.setUserIdList(userIds);
        vo.setDepartmentIds(Lists.newArrayList(10003));
        vo.setWelcomeMsgContent("我服了呀,哈哈哈");
        Result<Void> result = qywxWelcomeMsgService.updateWelcomeMsg(vo);
    }

    @Test
    public void save2Msg(){
        List<String> userIds = new ArrayList<>();
        userIds.add("heiheia");
        userIds.add("lululu");
        QywxWelcomeMsgVO vo = new QywxWelcomeMsgVO();
        vo.setEa("74164");
        vo.setWelcomeMsgName("测试欢迎2");
        vo.setType(1);
        vo.setUserIdList(userIds);
        vo.setWelcomeMsgContent("我不是一个欢迎你的人,别加我");
        vo.setContentType(1);
        vo.setLinkType(1);
        vo.setLinkContentUrl("www.fxiaoke.com");
        vo.setLinkTitle("分享欢迎你");
        vo.setLinkCover("www.fenxiao.com");
        vo.setOperator(1120);
        Result<Void> result = qywxWelcomeMsgService.saveWelcomeMsg(vo);
        log.info("result: {} ", GsonUtil.getGson().toJson(result));
    }

    @Test
    public void save3Msg(){
        List<String> userIds = new ArrayList<>();
        userIds.add("-999999");
        QywxWelcomeMsgVO vo = new QywxWelcomeMsgVO();
        vo.setEa("74164");
        vo.setWelcomeMsgName("还是测一下图片吧");
        vo.setType(1);
        vo.setUserIdList(userIds);
        vo.setWelcomeMsgContent("来吧,我欢迎你来完");
        vo.setContentType(3);
        vo.setVideoUrl("www.video.com");
        vo.setOperator(1100);
        Result<Void> result = qywxWelcomeMsgService.saveWelcomeMsg(vo);
        log.info("result: {} ", GsonUtil.getGson().toJson(result));
    }

    @Test
    public void sendWelcomeMsg(){
        ExternalUserMagCallBackData data = new ExternalUserMagCallBackData();
        data.setEa("74164");
        data.setToUserName("ahdifha");
        data.setFromUserName("");
        data.setCreateTime(0L);
        data.setMsgType("1112");
        data.setEvent("");
        data.setChangeType("2");
        data.setUserID("XiaoYanJingDemiko");
        data.setExternalUserID("helloworld");
        //data.setState("1646797051");
        data.setWelcomeCode("1373499494");
//        qywxContactManager.recvNewCustomerCallBack(data);
    }

    @Test
    public void uploadFile(){
        String accessToken = qywxManager.getAccessToken("88146");
        byte[] data = fileV2Manager.getByteDataByUrl(fileV2Manager.getUrlByPath(null, "C_202306_05_527276def25d42e9815430dea2079a08"));
        UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, "C_202306_05_527276def25d42e9815430dea2079a08", "image");
        if (mediaResult == null || mediaResult.getErrcode() != 0){
            log.warn("sendCustomerWelcomeMessage updateFile welcomeImagePath failed ea:{} id:{} result:{}", "74164", "", mediaResult);
            return;
        }
        UploadMediaResult mediaResult1 = httpManager.uploadFile(accessToken, data, "山水图", "image");
    }

    @Test
    public void queryWelMsgList(){
        List<QywxWelcomeMsgEntity> qywxWelcomeMsgEntities = qywxWelcomeMsgDAO.queryMsgList("74164", "halcyon");
        log.info("result:{}",GsonUtil.getGson().toJson(qywxWelcomeMsgEntities));
    }

    @Test
    public void editGroup(){
        EditObjectGroupArg arg = new EditObjectGroupArg();
        arg.setName("测试分组");
        arg.setObjectType(ObjectTypeEnum.QY_WELCOME_MSG.getType());
        qywxWelcomeMsgService.editQywxWelcomeMsgGroup("88146",1000,arg);
    }

    @Test
    public void listQywxWelcomeMsgGroup(){
        ListGroupArg arg = new ListGroupArg();
        arg.setUseType(0);
        arg.setObjectType(ObjectTypeEnum.QY_WELCOME_MSG.getType());
        qywxWelcomeMsgService.listQywxWelcomeMsgGroup("88146",1000,arg);
    }

    @Test
    public void getQywxWelcomeMsgGroupRole(){
        qywxWelcomeMsgService.getQywxWelcomeMsgGroupRole("-1");
    }

    @Test
    public void addQywxWelcomeMsgGroupRole(){
        SaveObjectGroupVisibleArg arg = new SaveObjectGroupVisibleArg();
        arg.setGroupId("-1");
        arg.setRoleIdList(Lists.newArrayList());
        qywxWelcomeMsgService.addQywxWelcomeMsgGroupRole("88146",1000,arg);
    }

    @Test
    public void cancelTopQywxWelcomeMsg(){
        CancelMaterialTopArg arg = new CancelMaterialTopArg();
        arg.setObjectId("cadafb7ceb10474fb6f09aa43d876cd8");
        qywxWelcomeMsgService.cancelTopQywxWelcomeMsg("88146",1000,arg);
    }

    @Test
    public void topQywxWelcomeMsg(){
        TopMaterialArg arg = new TopMaterialArg();
        arg.setObjectId("cadafb7ceb10474fb6f09aa43d876cd8");
        qywxWelcomeMsgService.topQywxWelcomeMsg("88146",1000,arg);
    }

    @Test
    public void setQywxWelcomeMsgGroup(){
        SetObjectGroupArg arg = new SetObjectGroupArg();
        arg.setGroupId("-1");
        arg.setObjectIdList(Lists.newArrayList("cadafb7ceb10474fb6f09aa43d876cd8"));
        arg.setObjectType(ObjectTypeEnum.QY_WELCOME_MSG.getType());
        qywxWelcomeMsgService.setQywxWelcomeMsgGroup("88146",1000,arg);
    }

    @Test
    public void deleteQywxWelcomeMsgGroup(){
        DeleteObjectGroupArg arg = new DeleteObjectGroupArg();
        arg.setId("-3");
        arg.setObjectType(ObjectTypeEnum.QY_WELCOME_MSG.getType());
        qywxWelcomeMsgService.deleteQywxWelcomeMsgGroup("88146",1000,arg);
    }

    @Test
    public void deleteQywxWelcomeMsg(){
        DeleteWelcomeMsgVO vo = new DeleteWelcomeMsgVO();
        vo.setId("b36ff90fd88e40f69eb703611b9393bf");
        qywxWelcomeMsgService.deleteWelcomeMsg(vo);
    }

}
