/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.consult.out.api.arg.marketing.SearchKnowledgeArg;
import com.facishare.consult.out.api.result.marketing.SearchKnowledgeResult;
import com.facishare.consult.out.api.service.marketing.MarketingSettingBizService;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.UrlEncoder;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.kis.GetRadarsInfoArg;
import com.facishare.marketing.api.arg.kis.ListSpreadRadarMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.GetMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.ListMarketingActivityArg;
import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.arg.qywx.department.QueryQywxDepartmentArg;
import com.facishare.marketing.api.arg.qywx.miniapp.ELoginArg;
import com.facishare.marketing.api.arg.qywx.staff.QueryQywxStaffaArg;
import com.facishare.marketing.api.arg.qywx.wxContact.QueryCrmUserIdArg;
import com.facishare.marketing.api.arg.wx.BatchGetWxMaterialArg;
import com.facishare.marketing.api.arg.wx.UpdateWxMaterialArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.kis.GetMarketingActivityResult;
import com.facishare.marketing.api.result.kis.GetRadarInfoResult;
import com.facishare.marketing.api.result.kis.TempListAllMarketingActivityResult;
import com.facishare.marketing.api.result.live.CreateMarketingLiveResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityResult;
import com.facishare.marketing.api.result.qywx.QueryEnterpriseQywxConfigResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryCustomerGroupListResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryGroupMemberListResult;
import com.facishare.marketing.api.result.qywx.department.QueryQywxDepartmentResult;
import com.facishare.marketing.api.result.qywx.miniapp.ELoginResult;
import com.facishare.marketing.api.result.qywx.staff.QueryQywxStaffResult;
import com.facishare.marketing.api.result.wxPublicPlatform.WxPublicPlatformAuthorizeUserInfo;
import com.facishare.marketing.api.result.wxPublicPlatform.WxPublicPlatformCheckFocus;
import com.facishare.marketing.api.service.EnterpriseSpreadStatisticService;
import com.facishare.marketing.api.service.MarketingReportService;
import com.facishare.marketing.api.service.MarketingTriggerService;
import com.facishare.marketing.api.service.WxOfficialAccountsService;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.api.service.qywx.*;
import com.facishare.marketing.api.service.sms.ApplyService;
import com.facishare.marketing.api.vo.live.CreateLiveVO;
import com.facishare.marketing.api.vo.qywx.config.QYWXConfigVO;
import com.facishare.marketing.api.vo.qywx.customerGroup.QueryGroupListVO;
import com.facishare.marketing.api.vo.qywx.customerGroup.QueryGroupMemberListVO;
import com.facishare.marketing.common.contstant.WxApiConstants;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.baidu.PasswordHelper;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingWxServiceDao;
import com.facishare.marketing.provider.dao.UserTagDao;
import com.facishare.marketing.provider.dao.WxPublicPlatformAuthorizeComponentDao;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsMappingDataDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpEaMappingDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.entity.MarketingWxServiceEntity;
import com.facishare.marketing.provider.entity.UserTagEntity;
import com.facishare.marketing.provider.entity.WxPublicPlatformAuthorizeComponentEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsMappingDataEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpEaMappingEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.manager.miniappLogin.WxMiniappLoginManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WxCloudRestManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.wxApiArg.BatchGetMaterialArg;
import com.facishare.marketing.provider.manager.wxthirdplatform.wxApiResult.BatchGetMaterialResult;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.service.WxOfficialAccountsServiceImpl;
import com.facishare.marketing.provider.service.live.LiveServiceImpl;
import com.facishare.marketing.provider.service.marketingactivity.WeChatServiceMarketingActivityServiceImpl;
import com.facishare.marketing.provider.service.usermarketingaccounttag.UserMarketingTagServiceImpl;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.wechat.dubborestouterapi.arg.WechatRequestDispatchArg;
import com.facishare.wechat.dubborestouterapi.data.EaBindInfoData;
import com.facishare.wechat.dubborestouterapi.result.WechatRequestDispatchResult;
import com.facishare.wechat.dubborestouterapi.service.union.EaBindInfoRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.union.core.api.model.result.OuterServiceResult;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.FindAllTagByBulkDataIdArg;
import com.fxiaoke.crmrestapi.common.data.DataIdAndMetadataTagData;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import shade.com.google.gson.Gson;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.Time;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/3/14
 * @apiNote
 */
public class YunTest extends BaseTest {

    @Autowired
    private WxCloudRestManager wxCloudRestManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private QYWXSettingService qywxSettingService;
    @Autowired
    private QywxStaffService queryQywxStaff;
    @Autowired
    private QyWxDepartmentService qyWxDepartmentService;
    @Autowired
    private EIEAConverter eieaConverter;
    @ReloadableProperty("host")
    private String host;
    @ReloadableProperty("center.host")
    private String centerHost;
    @Autowired
    private QywxStaffService qywxStaffService;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private QYWXCustomerGroupService qywxCustomerGroupService;
    @Autowired
    private MarketingActivityService marketingActivityService;
    @Autowired
    private MarketingReportService marketingReportService;
    @Autowired
    private WxOfficialAccountsService wxOfficialAccountsService;
    @Autowired
    private EnterpriseSpreadStatisticService enterpriseSpreadStatisticService;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private WeChatServiceMarketingActivityService weChatServiceMarketingActivityService;
    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;
    @Autowired
    private WxPublicPlatformAuthorizeComponentDao wxPublicPlatformAuthorizeComponentDao;
    @Autowired
    private MarketingTriggerService marketingTriggerService;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;

    @Autowired
    private MarketingWxServiceDao marketingWxServiceDao;
    // private OuterServiceWechatManager outerServiceWechatManager;
    @Autowired
    private EaBindInfoRestService eaBindInfoRestService;
    @Autowired
    private QYWXContactService qywxContactService;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingCrmManager marketingCrmManager;
    @Autowired
    private WxMiniappLoginManager wxMiniappLoginManager;
    @Autowired
    private AdLeadsMappingDataDAO adLeadsMappingDataDAO;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private UserTagDao userTagDao;
    @Autowired
    private UserMarketingTagServiceImpl userMarketingTagService;
    @Autowired
    private WxWorkTagSynchronizationManager wxWorkTagSynchronizationManager;
    @Autowired
    private WechatFanOuterTagSynchronizationManager wechatFanOuterTagSynchronizationManager;
    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private LiveService liveService;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private ApplyService applyService;


    @ReloadableProperty("zhihu_clue")
    private String zhihuClue;
    @ReloadableProperty("sogou_clue")
    private String sogouClue;
    @ReloadableProperty("kuaishou_clue")
    private String kuaishouClue;
    @ReloadableProperty("shenma_clue")
    private String shenmaClue;
    @ReloadableProperty("crm.url")
    private String crmUrl;
    @Value("${live.default.hexagon.id}")
    private String defaultHexagonIds;
    @ReloadableProperty("channels_transit_hexagon_id")
    private String transitId;
    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @ReloadableProperty("channels_transit_url")
    private String channelStransitUrl;
    @Autowired
    private MarketingSettingBizService marketingSettingBizService;


    private com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> getActionAddResultResult(AdLeadsMappingDataEntity adLeadsMappingDataEntity, String ea, String subMarketingEventId, Long thirdLeadsId, HashMap<String, Object> leadsMap) {
        Map<String, Object> objectMap = refreshDataManager.syncLeadCallFunc(ea, ObjectData.convert(leadsMap), adLeadsMappingDataEntity.getCustomFuncApiName());
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addLeadsResult = refreshDataManager.syncClueDataToCrmObj(ea, String.valueOf(thirdLeadsId), AdSourceEnum.SOURCE_TENCETN.getValue(), adLeadsMappingDataEntity, ObjectData.convert((Map<String, Object>) objectMap.get("functionResult")), subMarketingEventId);
        if (addLeadsResult == null || !addLeadsResult.isSuccess() || addLeadsResult.getData() == null || addLeadsResult.getData().getObjectData() == null) {
            return null;
        }
        return addLeadsResult;
    }

    @Test
    public void duoYunTest() {
//        ObjectData objectData = crmMetadataManager.getById("83668", -10000, CrmObjectApiNameEnum.CRM_LEAD.getName(), "62861026cb68f200019925c1");
//        System.out.println(objectData);
        //extracted8();
        SearchKnowledgeArg arg = new SearchKnowledgeArg();
        arg.setEa("83669");
        arg.setScene("wechat");
        arg.setContent("怎么使用连接器");

        com.facishare.consult.out.api.result.Result<List<SearchKnowledgeResult>> listResult = marketingSettingBizService.searchKnowledge(arg);
        System.out.println(listResult);
    }

    private void extracted8() {
        int result = wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited("83668", Lists.newArrayList("wm7_I5IwAA71-AyDefs54bK4vm0KSYZw"), true, null);
        System.out.println(result);
    }

    @Test
    public void extracted7() {
        //metadataTagManager.addTagsToObjectDatas("74164", CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),new ArrayList<>("62cd1dc1d3a30d00017f8247"),"");
        // extracted3();
//        extracted6();
        //       extracted5();
        //       weChatServiceMarketingActivityService.checkFocusOn("wx2e8a14a356c46c27","oFrqiwx9GFlb8sRTRU7RAon4jcas");
//
        //   noticeService.sendImageTextNotice("d3e0c77d353c46939758d015cefe6095","62f23ebc69e67a000193c418","62f0739c37cdfe00018bc4fd");

        List<TagName> tagNames = new ArrayList<>();
        TagName tagName = new TagName();
        tagName.setFirstTagName("客户等级");
        tagName.setSecondTagName("一般");
        TagName tagName1 = new TagName();
        tagName1.setFirstTagName("客户等级");
        tagName1.setSecondTagName("核心");
        tagNames.add(tagName);
        tagNames.add(tagName1);
        metadataTagManager.addTagsToObjectDatas("88146", CrmObjectApiNameEnum.CUSTOMER.getName(), ImmutableList.of("65dff8c6f2801b0007a5da0f"), tagNames);
    }

    private void extracted6() {
        String miniprogramPath = "/pages/verifyPage/verifyPage?liveId=" + "52a8b8c49bd14323832a06451e68c34d";
        try {
            miniprogramPath = URLEncoder.encode(miniprogramPath, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String viewUrl = channelStransitUrl + "?ea=" + "74164" + "&miniprogramPath=" + miniprogramPath;
        System.out.println(viewUrl);
    }

    private void extracted5() {
        String firstPageId = "56b782074ab249cd8371d4b6a8d04c12";
        //  String path ="pkgs/pkg-hexagon/pages/detail/hexagon-detail";
        String path = "https://crm.ceshi112.com/fsh5/train/release/index.html#/live/detail?channel=2&liveKey=52a8b8c49bd14323832a06451e68c34d";
        //  String query = "objectId="+firstPageId+"&objectType=26";
        QueryMiniAppForwardUrlArg arg = new QueryMiniAppForwardUrlArg();
        arg.setPath(path);
        //    arg.setQuery(query);
        arg.setExpireInterval(365);
//        String s = liveServiceimpl.queryMiniAppForwardUrl(arg, "74164");
//        System.out.println("url:"+s);

    }

    private void extracted4() {

        String a = "{" +
                "    \"title\": \"我再来一下20\",\n" +
                "    \"startTime\": 1658383644000,\n" +
                "    \"endTime\": 1658977200000,\n" +
                "    \"coverTaPath\": \"A_202207_26_6f932bf2661e421d8c253c8d57c36337.jpg\",\n" +
                "    \"ext\": \"\",\n" +
                "    \"lectureUserName\": \"我再来一下20\",\n" +
                "    \"livePlatform\": 5,\n" +
                "    \"chatOn\": 0,\n" +
                "    \"liveLimit\": 1,\n" +
                "    \"autoRecord\": 0,\n" +
                "    \"tagNames\": null,\n" +
                "    \"otherPlatformLiveUrl\": \"https://wxaurl.cn/lZpunhhGAMn\",\n" +
                "    \"showActivityList\": true,\n" +
                "    \"id\": \"98a4841c289a4f25a8af51bfc007e96b\",\n" +
                "    \"marketingEventId\": \"62dfb4f3b43f9700010280bd\",\n" +
                "    \"desc\": \"我再来一下13\",\n" +
                "    \"status\": 1,\n" +
                "    \"lectureUrl\": null,\n" +
                "    \"formHexagonId\": null,\n" +
                "    \"hasLecturePassword\": false,\n" +
                "    \"vhallId\": null,\n" +
                "    \"memberId\": null,\n" +
                "    \"memberPhone\": null,\n" +
                "    \"memberName\": null,\n" +
                "    \"editable\": true,\n" +
                "    \"xiaoetongLiveId\": null,\n" +
                "    \"xiaoetongUrl\": null,\n" +
                "    \"jumpObjectId\": null,\n" +
                "    \"jumpObjectType\": null,\n" +
                "    \"jumpUrl\": null,\n" +
                "    \"createObjectDataModel\": {\n" +
                "        \"objectData\": {\n" +
                "            \"field_v98jy__c__o\": \"\",\n" +
                "            \"object_describe_api_name\": \"MarketingEventObj\",\n" +
                "            \"object_describe_id\": \"5be18504319d19f44e9eab82\",\n" +
                "            \"marketing_plan\": \"我再来一下13\",\n" +
                "            \"field_Hn42N__c\": \"我再来一下13\",\n" +
                "            \"field_m79ej__c\": \"62a041edb52a090001b9cda8\",\n" +
                "            \"field_v98jy__c\": \"option1\",\n" +
                "            \"begin_time\": 1658887195000,\n" +
                "            \"end_time\": 1658977200000,\n" +
                "            \"name\": \"我再来一下13\",\n" +
                "            \"event_type\": \"live_marketing\",\n" +
                "            \"owner\": [\n" +
                "                \"1123\"\n" +
                "            ]\n" +
                "        }\n" +
                "    }\n" +
                "}";

        //  String a="{\"title\":\"测试微页面（微吼）225454\",\"startTime\":1665622800000,\"endTime\":1665630000000,\"coverTaPath\":\"A_202011_12_c7f270ec2c7f44a487267f851af8f467.jpg\",\"ext\":\"\",\"lectureUserName\":\"小王八\",\"lecturePassword\":\"0000\",\"livePlatform\":1,\"chatOn\":0,\"liveLimit\":1,\"maxLiveCount\":5000,\"autoRecord\":1,\"tagNames\":null,\"otherPlatformLiveUrl\":null,\"showActivityList\":true,\"id\":\"c43074b14c68465cbb09803ee884dfc9\",\"marketingEventId\":\"616e272a4b4cf700018b24e3\",\"desc\":\"\",\"status\":3,\"lectureUrl\":\"https://fs8.ceshi112.com/l23Qp2\",\"viewUrl\":\"https://fs8.ceshi112.com/qn3OHl\",\"formHexagonId\":\"fd6c25254de14e2a8095fc2a9e70ef52\",\"hasLecturePassword\":true,\"vhallId\":588429810,\"memberId\":null,\"memberPhone\":null,\"memberName\":null,\"editable\":true,\"xiaoetongLiveId\":null,\"xiaoetongUrl\":null,\"jumpObjectId\":null,\"jumpObjectType\":null,\"jumpUrl\":null,\"signUpSiteId\":null,\"transitSiteId\":null,\"createObjectDataModel\":{\"objectData\":{\"field_v98jy__c__o\":\"\",\"object_describe_api_name\":\"MarketingEventObj\",\"object_describe_id\":\"5be18504319d19f44e9eab82\",\"marketing_plan\":\"2\",\"field_Hn42N__c\":\"2\",\"field_m79ej__c\":\"628c926d4781e30001bf8ad8\",\"field_v98jy__c\":\"68k33I8fC\",\"begin_time\":1665622800000,\"end_time\":1665630000000,\"name\":\"测试微页面（微吼）22\",\"event_type\":\"live_marketing\",\"owner\":[\"1123\"]}}}";
        CreateLiveVO vo = JSON.parseObject(a, CreateLiveVO.class);
        vo.setFsUserId(1123);
        vo.setEa("74164");
        vo.setCorpId(74164);
        Result<CreateMarketingLiveResult> result = liveService.createLive(vo);
        System.out.println("createLive Result:" + result);
    }

    private void extracted3() {
        Result<Integer> result = liveService.getLiveStatus("8c69478ff02346c6a7e5e73695c731f8");
        Result<Integer> result1 = liveService.getLiveStatus("c2ebb8209e88447b9e71004f4bd095bd");
        Result<Integer> result2 = liveService.getLiveStatus("3fd76dbbcd6e4b7e8c12c81274df08e0");
        Result<Integer> result3 = liveService.getLiveStatus("992d7d1d4cdd416098c6de27dce0d9e9");
        Result<Integer> result4 = liveService.getLiveStatus("d9832ce8b4b64a0eba2714fe3339960c");
        System.out.println(result);
        System.out.println(result1);
        System.out.println(result2);
        System.out.println(result3);
        System.out.println(result4);
    }

    private void extracted2() {
        String[] middleIds = StringUtils.split(defaultHexagonIds, ",");
        String[] ids = Arrays.copyOf(middleIds, 2);
        if (5 == LivePlatformEnum.CHANNELS.getType()) {
            if (middleIds.length > 1) {
                ids[0] = middleIds[2];
            }
        }
        System.out.println(ids);
    }

    private void extracted1() {
        List<UserTagEntity> userTagEntities = userTagDao.listAllUsedFirstTagName("83668", "6023a4c7887b4e068206d17569052d31");
        List<UserTagEntity> userTagEntities1 = userTagDao.listAllUsedSeconfTagName("83668", "6023a4c7887b4e068206d17569052d31", "NONE");
        System.out.println(userTagEntities);
        System.out.println("--------------------------");
        System.out.println(userTagEntities1);
    }

    private void extracted() {
        String a = "{\"page_url\":\"https://mp.weixin.qq.com/promotion/res/htmledition/mobile/html/evoke_canvas.html?previewKey=%2F2Ax07eKYdsvpZUDktQ%2BR3yYaIGw8UZd%2BwvoY0KqWwhMTcypc4z1Y%2BL8MbElKkAb\",\"creative_name\":\"大中型1\",\"leads_follow_tag\":\"LEADS_FOLLOW_TAG_DEFAULT\",\"adcreative_id\":5417252895,\"leads_type\":\"LEADS_TYPE_FORM\",\"creative_id\":\"5417252895\",\"shop_address\":\"\",\"page_name\":\"原生推广页-无案例\",\"leads_user_wechat_appid\":\"\",\"leads_telephone\":\"***********\",\"campaign_id\":5417161444,\"leads_source\":\"WECHAT_CANVAS\",\"profession\":\"\",\"component_id\":\"\",\"id_number\":\"\",\"leads_potential_score\":\"0\",\"leads_action_time\":1650723635000,\"outer_leads_convert_type\":\"\",\"component_name\":\"\",\"agency_id\":\"6985731\",\"working_years\":\"\",\"leads_tags\":\"参与赔付\",\"shop_name\":\"\",\"leads_gender\":\"GENDER_TYPE_UNKNOWN\",\"ad_id\":5417252960,\"click_id\":\"wx0s4x5cnd5s2ef400\",\"adcreative_name\":\"大中型1\",\"adgroup_name\":\"自有人群-手动-原生落地页\",\"pos_type\":999,\"adgroup_id\":5417252760,\"newSave\":false,\"campaign_name\":\"公海库数据营销\",\"page_id\":\"**********\",\"leads_create_time\":*************,\"leads_id\":*********,\"leads_user_type\":\"\",\"leads_area\":\"\",\"leads_email\":\"\",\"leads_qq\":\"0\",\"leads_user_id\":\"\",\"leads_name\":\"王锤\",\"bundle\":\"{}\",\"outer_leads_ineffect_reason\":\"\",\"agency_name\":\"上海亦芯文化传媒有限公司\",\"address\":\"河北-衡水\",\"telephone_location\":\"河北-衡水\",\"ad_name\":\"大中型1\",\"leads_wechat\":\"\",\"account_id\":********,\"outer_leads_id\":\"\",\"age\":\"\",\"name\":\"王锤\",\"tel\":\"***********\",\"mobile\":\"***********\",\"create_time\":*************,\"remark\":\"参与赔付\",\"field_2CspT__c\":\"other\",\"field_F8We6__c\":\"option1\",\"field_7x4dJ__c\":\"**********\",\"data_own_department\":[\"999999\"],\"field_o52T2__c\":\"单选\",\"field_rGe21__c\":\"23232\",\"source\":\"g2f9L8zy2\"}";
        Map<String, Object> map = JSON.parseObject(a, HashMap.class);
        map.put("marketing_event_id", "6290af92a6cf860001160c5e");
        ObjectData objectData = ObjectData.convert(map);
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(objectData);
        ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
        optionInfo.setIsDuplicateSearch(false);
        arg.setOptionInfo(optionInfo);

        String url = crmUrl + "/v1/rest/object/LeadsObj/action/Add?isSpecifyTime=true";
        RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(arg));
        //  RequestBody requestBody = RequestBody.create(null, a);
        //     Map headerObj = refreshDataManager.createHeaderObj("74164", "-10000");
        Map<String, String> headerObj = new HashMap<>();
        headerObj.put("X-fs-ei", "74164");
        headerObj.put("X-fs-Enterprise-Id", "74164");
        headerObj.put("x-fs-userInfo", "-10000");
        headerObj.put("X-fs-Employee-Id", "-10000");
        headerObj.put("client_info", "rest-api");
        Result<ActionAddResult> o = httpManager.executePostHttpWithRequestBodyAndHeader(requestBody, url, new TypeToken<Result<ActionAddResult>>() {
        }, headerObj);
        System.out.println(o);
    }

    private Object getObject() {
        long beginTime = new Date().getTime();
        long endTime = beginTime + 24 * 60 * 60 * 1000;
        String url = null;
        if (host.contains("www.ceshi112.com")) {
            url = "http://**************:15056/versionRegisterService/addCrmOrder";
        } else if (host.contains("www.fxiaoke.com")) {
            url = "http://************:36337/versionRegisterService/addCrmOrder";
        } else {
            url = centerHost + "/fs-plat-webhook-provider/versionRegisterService/addCrmOrder";
        }
        String crmOrderDetailInfo = new Gson().toJson(new FormBody.Builder().add("orderTime", String.valueOf(beginTime)).add("orderId", UUIDUtil.getUUID()).add("enterpriseAccount",
                "74164").add("orderTpye", "2"));
        String crmOrderProductInfo = new Gson().toJson(new FormBody.Builder().add("orderAmount", "0.0").add("quantity", "1").add("productId",
                "628b3cf403287800011f042e").add("beginTime", String.valueOf(beginTime)).add("endTime", String.valueOf(endTime)).add("allResourceCount", "1"));
        FormBody requestBody1 =
                new FormBody.Builder().add("crmOrderDetailInfo", "fsjfhajk").add("crmOrderProductInfo", crmOrderProductInfo).build();
        FormBody requestBody =
                new FormBody.Builder().add("crmOrderDetailInfo", crmOrderDetailInfo).add("crmOrderProductInfo", crmOrderProductInfo).build();
        Map<String, Object> map = new HashMap<>();
        map.put("crmOrderDetailInfo", crmOrderDetailInfo);
        map.put("crmOrderProductInfo", crmOrderProductInfo);
        JSONObject jsonObject = new JSONObject(map);
        String s1 = jsonObject.toString();
        return httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<Result<String>>() {
        });
    }
}
