/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.api.result.LocationResult;
import com.facishare.marketing.provider.manager.AreaManager;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.result.QueryAreaInfoResult;
import com.fxiaoke.crmrestapi.common.result.ZoneByParentResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * Created  By zhoux 2020/05/18
 **/
@Slf4j
public class AreaManagerTest extends BaseTest {

    @Autowired
    private AreaManager areaManager;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Test
    public void buildAreaData() {
        areaManager.buildAreaData();
    }

    @Test
    public void getAreaNameByValue() {
        Map<String, String> map = areaManager.getAreaNameByValue("88146", Lists.newArrayList("238", "432209", "439087", "439363"), null);
        log.info("getAreaNameByValue result:{}", map);
    }

    @Test
    public void addCampaignMembersObjField() {
        campaignMergeDataManager.addCampaignMembersObjField("76301");
    }

    @Test
    public void getAreaNameByKeyword(){
        QueryAreaInfoResult result = areaManager.getAreaNameByKeyword("88146","北京");
        log.info("getAreaNameByKeyword result:{}", result);
    }

    @Test
    public void getZoneByParentId(){
        ZoneByParentResult result = areaManager.getZoneByParentId("88146","248", 1);
        log.info("getZoneByParentId result:{}", result);
    }

    @Test
    public void batchQueryLocationInfo(){
        List<String> codes = Lists.newArrayList();
        codes.add("238");
        codes.add("432209");
        codes.add("439087");
        codes.add("439363");

        LocationResult result = areaManager.batchQueryLocationInfo("88146",codes);
        log.info("batchQueryLocationInfo result:{}",result);
    }
}
