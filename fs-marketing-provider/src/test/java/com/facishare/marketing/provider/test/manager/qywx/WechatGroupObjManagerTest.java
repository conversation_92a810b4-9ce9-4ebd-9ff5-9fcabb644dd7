/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager.qywx;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.provider.innerArg.qywx.ExternalChatEvent;
import com.facishare.marketing.provider.innerResult.qywx.CustomerGroupDetailResult;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupObjDescribeManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class WechatGroupObjManagerTest extends BaseTest {

    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;

    @Test
    public void handleCrmStockExternalUserDataTest() {
        String ea = "82255";
//        String json = "{\"group_chat\":{\"admin_list\":[],\"chat_id\":\"wrQZ1uJQAAHk24372ElJ513qsNIH_GUA\",\"create_time\":**********,\"fxUserId\":\"1000\",\"member_list\":[{\"fxUserId\":\"1000\",\"group_nickname\":\"\",\"invitor\":{\"fxUserId\":\"1000\",\"userid\":\"wowx1mDAAAGjggRRDyPtMrZSqZ2rj8tQ\"},\"join_scene\":1,\"join_time\":**********,\"name\":\"周志军\",\"type\":1,\"userid\":\"wowx1mDAAAGjggRRDyPtMrZSqZ2rj8tQ\"},{\"avatar\":\"http://wx.qlogo.cn/mmhead/Q3auHgzwzM5763mOFlndsBb36demVpsUsicBF9LvKstRV4srvGcMPibQ/0\",\"fxUserId\":\"1001\",\"group_nickname\":\"\",\"invitor\":{\"fxUserId\":\"1000\",\"userid\":\"wowx1mDAAAGjggRRDyPtMrZSqZ2rj8tQ\"},\"join_scene\":1,\"join_time\":**********,\"name\":\"志军\",\"type\":2,\"userid\":\"wmwx1mDAAAkVcPTF_bDheJ1gbUswje3Q\"}],\"name\":\"测试6666\",\"owner\":\"wowx1mDAAAGjggRRDyPtMrZSqZ2rj8tQ\"}}";
        String json = "{\"group_chat\":{\"admin_list\":[],\"chat_id\":\"wrQZ1uJQAAHk24372ElJ513qsNIH_GUA\",\"create_time\":**********,\"fxUserId\":\"1000\",\"member_list\":[{\"fxUserId\":\"1000\",\"group_nickname\":\"\",\"invitor\":{\"fxUserId\":\"1000\",\"userid\":\"wowx1mDAAAGjggRRDyPtMrZSqZ2rj8tQ\"},\"join_scene\":1,\"join_time\":**********,\"name\":\"周志军\",\"type\":1,\"userid\":\"wowx1mDAAAGjggRRDyPtMrZSqZ2rj8tQ\"}],\"name\":\"测试6666\",\"owner\":\"wowx1mDAAAGjggRRDyPtMrZSqZ2rj8tQ\"}}";
        CustomerGroupDetailResult customerGroupDetailResult = JsonUtil.fromJson(json, CustomerGroupDetailResult.class);
        wechatGroupObjDescribeManager.handleScrmStockWechatGroupData(ea, customerGroupDetailResult);
    }

    @Test
    public void handleCrmWechatGroupChangeEvent() {
//        String ea = "82255";
//        ExternalChatEvent externalChatEvent = new ExternalChatEvent();
//        externalChatEvent.setFsEa(ea);
//        externalChatEvent.setAppId("dkdf3684b6720635f7");
//        externalChatEvent.setExternalChatDetail("{\"appId\":\"dkdf3684b6720635f7\",\"corpId\":\"wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA\",\"fsEa\":\"85666\",\"changeType\":\"update\",\"chatId\":\"wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg\",\"updateDetail\":\"del_member\",\"quitScene\":\"1\",\"memChangeCnt\":\"1\",\"externalChatDetail\":\"{\\\"errcode\\\":0,\\\"errmsg\\\":\\\"ok\\\",\\\"group_chat\\\":{\\\"chat_id\\\":\\\"wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg\\\",\\\"name\\\":\\\"纷享销客SCRM\\\",\\\"owner\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\",\\\"create_time\\\":1665543344,\\\"member_list\\\":[{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\",\\\"type\\\":1,\\\"join_time\\\":1665543344,\\\"join_scene\\\":1,\\\"invitor\\\":{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\"},\\\"group_nickname\\\":\\\"\\\",\\\"name\\\":\\\"陈宗鑫\\\"},{\\\"userid\\\":\\\"wowx1mDAAApWf0CHhWXTPUVdS_MrUYyA\\\",\\\"type\\\":1,\\\"join_time\\\":1665543344,\\\"join_scene\\\":1,\\\"invitor\\\":{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\"},\\\"group_nickname\\\":\\\"\\\",\\\"name\\\":\\\"杨贤杰\\\"},{\\\"userid\\\":\\\"wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ\\\",\\\"type\\\":2,\\\"join_time\\\":1665543344,\\\"join_scene\\\":1,\\\"invitor\\\":{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\"},\\\"group_nickname\\\":\\\"\\\",\\\"name\\\":\\\"出口\\\"}],\\\"admin_list\\\":[]}}\"}");

        ExternalChatEvent externalChatEvent1 = JSONObject.parseObject("{\n" +
                "    \"appId\":\"dkdf3684b6720635f7\",\n" +
                "    \"corpId\":\"wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA\",\n" +
                "    \"fsEa\":\"85666\",\n" +
                "    \"changeType\":\"update\",\n" +
                "    \"chatId\":\"wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg\",\n" +
                "    \"updateDetail\":\"del_member\",\n" +
                "    \"quitScene\":\"1\",\n" +
                "    \"memChangeCnt\":\"1\",\n" +
                "    \"externalChatDetail\":\"{\\\"errcode\\\":0,\\\"errmsg\\\":\\\"ok\\\",\\\"group_chat\\\":{\\\"chat_id\\\":\\\"wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg\\\",\\\"name\\\":\\\"纷享销客SCRM\\\",\\\"owner\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\",\\\"create_time\\\":1665543344,\\\"member_list\\\":[{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\",\\\"type\\\":1,\\\"join_time\\\":1665543344,\\\"join_scene\\\":1,\\\"invitor\\\":{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\"},\\\"group_nickname\\\":\\\"\\\",\\\"name\\\":\\\"陈宗鑫\\\"},{\\\"userid\\\":\\\"wowx1mDAAApWf0CHhWXTPUVdS_MrUYyA\\\",\\\"type\\\":1,\\\"join_time\\\":1665543344,\\\"join_scene\\\":1,\\\"invitor\\\":{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\"},\\\"group_nickname\\\":\\\"\\\",\\\"name\\\":\\\"杨贤杰\\\"},{\\\"userid\\\":\\\"wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ\\\",\\\"type\\\":2,\\\"join_time\\\":1665543344,\\\"join_scene\\\":1,\\\"invitor\\\":{\\\"userid\\\":\\\"wowx1mDAAArLI4aPJXTqJx_UJCABHILQ\\\"},\\\"group_nickname\\\":\\\"\\\",\\\"name\\\":\\\"出口\\\"}],\\\"admin_list\\\":[]}}\"\n" +
                "}", ExternalChatEvent.class);
        wechatGroupObjDescribeManager.handleScrmWechatGroupChangeEvent(externalChatEvent1);
    }

    @Test
    public void handleScrmWechatGroupDeleteEvent() {
        String ea = "82255";
        ExternalChatEvent externalChatEvent = new ExternalChatEvent();
        externalChatEvent.setFsEa(ea);
        externalChatEvent.setAppId("dkdf3684b6720635f7");
        externalChatEvent.setChatId("wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg");
        wechatGroupObjDescribeManager.handleScrmWechatGroupDeleteEvent(externalChatEvent);
    }


    @Test
    public void fixGroupOwnerName() {
        wechatGroupObjDescribeManager.fixGroupOwnerName("88146");
    }
    public static void main(String[] args) {
        String json = "{\"group_chat\":{\"admin_list\":[],\"chat_id\":\"wrQZ1uJQAAepp8KmQC8WKl529uPREm0g\",\"create_time\":1672140715,\"fxUserId\":\"1000\",\"member_list\":[{\"avatar\":\"https://wework.qpic.cn/wwhead/duc2TvpEgSQl4wxMPWAVticrdLFAyjq8AXIaBr1Tia1OHNHZ3SdjkGPaCOR1x5OIibflO1icgRA9exI/0\",\"group_nickname\":\"\",\"invitor\":{\"fxUserId\":\"1000\",\"userid\":\"wowx1mDAAA0H5NHlre-zbsXd-wyDR4IQ\"},\"join_scene\":1,\"join_time\":1672140715,\"name\":\"张洪华\",\"type\":2,\"userid\":\"wowx1mDAAA1aUHHW-8_JlkXuCMFxU-Rw\"},{\"fxUserId\":\"1000\",\"group_nickname\":\"\",\"invitor\":{\"fxUserId\":\"1000\",\"userid\":\"wowx1mDAAA0H5NHlre-zbsXd-wyDR4IQ\"},\"join_scene\":1,\"join_time\":1672140715,\"name\":\"熊涛\",\"type\":1,\"userid\":\"wowx1mDAAA0H5NHlre-zbsXd-wyDR4IQ\"},{\"avatar\":\"http://wx.qlogo.cn/mmhead/Q3auHgzwzM7VVJREDg6L2RyNLcX1GWRNnxSkhKA4052mYmlichTJkOQ/0\",\"group_nickname\":\"\",\"invitor\":{\"fxUserId\":\"1000\",\"userid\":\"wowx1mDAAA0H5NHlre-zbsXd-wyDR4IQ\"},\"join_scene\":1,\"join_time\":1672140715,\"name\":\"玉英\",\"type\":2,\"userid\":\"wmwx1mDAAA24yc1KJXhXP3evjqUiD-RQ\"}],\"name\":\"群6\",\"owner\":\"wowx1mDAAA0H5NHlre-zbsXd-wyDR4IQ\"}}";
        CustomerGroupDetailResult customerGroupDetailResult = JsonUtil.fromJson(json, CustomerGroupDetailResult.class);
        System.out.println(JsonUtil.toJson(customerGroupDetailResult));
    }
}
