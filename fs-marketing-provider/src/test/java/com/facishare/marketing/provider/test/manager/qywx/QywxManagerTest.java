/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager.qywx;

import com.facishare.marketing.api.vo.DingDingMarketingSpreadVO;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentSimpleResult;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.UpdateQywxObjDescribeManager;
import com.facishare.marketing.provider.mq.sender.DingDingMarketingSpreadSender;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created  By zhoux 2020/04/22
 **/
@Slf4j
public class QywxManagerTest extends BaseTest {

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private DingDingMarketingSpreadSender dingDingMarketingSpreadSender;

    @Autowired
    private UpdateQywxObjDescribeManager updateQywxObjDescribeManager;

    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Test
    public void queryAllStaff() {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa("82450");
        String accessToken = qywxManager.getAccessToken("82450");
        qywxManager.queryAllStaff("82450", accessToken, false, true);
    }

    @Test
    public void getUserDetailInfo() {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa("74164");
        String accessToken = qywxManager.getAccessToken("74164");
        qywxManager.getStaffDetail("74164", "linming", accessToken, false);
    }

    // DepartmentStaffResult.StaffInfo(userId=gavin, name=周鑫, department=[1], order=null, position=null, mobile=null, gender=null, avatar=null)

    @Test
    public void batchGetQywxCustomerExternalUserId(){
        String accessToken = "aaOcd7N8InHxe2SpKFFYyFvCvUcHLiUvDEsn72RWZkfkahwMHEjQKxJ5Z2I-XxFmdmAAKWV_E7zpkeJa7PDKRPV0LIJTAbQ3JIrvZklcXQUbwyQwFzxlfpHqqrnn_4RZl-_FOegWSlQuNxaAQzZsY66_R-3lwZElBzujPGZy8gIDtBapPK2CLMhs76vQPAxVrGbErk51-CD25boOlz4H1A";
        List<String> userIds = Lists.newArrayList("YunLuo", "ZhengHui", "LinDaiYuShiKeCao", "gray", "windmill","Fu","YiGeRenDeHuaLuo","linming","ZhongGuangYan","YeZi","gavin","XiaoMaJia","d41d8cd98f00b204e9800998ecf8427e","ZiWei","jenning","keanu","XiaoPanPan","PingSeng","ZhangShiFang","Guang","BingHongChaBuYaoBing","space","13762692877","llle","XiaoJieJie","YeZi01","HaoHao","0089814ede52f6a03f7e5d6a3cfc29fa","LinGuoWen","helloworld","vgei","XieShiWei","zy","nimo","A","truelight","JuBeiSongLiuNian","KouQiongcocoFenXiangXiaoKecrm","JuYue");
        Set<String> sets =  qywxManager.batchGetQywxCustomerExternalUserId(accessToken, userIds);
        log.info("add sets:{}", sets);
    }

    @Test
    public void queryStaffByEaAndMobileTest() {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa("74164");
        String accessToken = qywxManager.getAccessToken("74164");
        List<DepartmentStaffResult.StaffInfo> staffInfos = qywxManager.queryStaffByEaAndMobile("74164", "13163352321", accessToken);
        System.out.println(staffInfos);
    }

    @Test
    public void dealExternalUserIdByUnionId(){
        qywxManager.dealExternalUserIdByUnionId("eb950d26ce1947a48f1cb55a2a35005b","otnHP5bu5LjJgtvTIAeU-I3Pu9xI","olh2JjmBUkeztSdXjP6YwbG5wVVY","74164");
    }

    @Test
    public void getWxUnionId(){
        qywxManager.getWxUnionIdByExternalUserId("74164","wowx1mDAAAzsBL-HB2MvB_TaD406WvqQ");
    }
    @Test
    public void sendNoticeKsDing(){
        DingDingMarketingSpreadVO vo = new DingDingMarketingSpreadVO();
        vo.setTitle("测试发送一下");
        vo.setContent("钉钉推广看看情况");
        vo.setEa("74164");
        Map<String, String> userMap = Maps.newHashMap();
        userMap.put("12343","http://www.ceshi112.com/page?userId=12343");
        userMap.put("123344","http://www.ceshi112.com/page?userId=123344");
        vo.setUserSpreadUrlMap(userMap);
        dingDingMarketingSpreadSender.send(vo);
    }

    @Test
    public void updateQywxObjDescribe(){
        List<String> eaAll = enterpriseMetaConfigDao.findEaAll();
        for (String ea : eaAll) {
            updateQywxObjDescribeManager.updateQywxOjbDescribeFiled(ea);
        }
    }

    @Test
    public void queryDepartment(){
        String accessToken = "CpEUzltOwOjBwEafrcD7jYk_XW2wybFNiZqdKcoTuxm0g_stOmKHVG6XLNXPSjMCfkbYG4-RPbNfR-q315llBPoQm2wzd1xwP_44OGKKC34uUgDe-djyX_W9Nu2mU_fU1YYpoBmvz5J1ZeZck-nZxaP6ZacJYkt31bLA0R6EJJMxeCpZTA66LghSWYtrGU7D7RlZAXN_C7fdSLdkbEM2kA";
        DepartmentSimpleResult departmentSimpleResult = qywxManager.querySimpleDepartment(accessToken, 4);
        DepartmentSimpleResult departmentSimpleResult1 = qywxManager.querySimpleDepartment(accessToken, 5);
        List<DepartmentStaffResult.StaffInfo> staffByDepartmentId = qywxManager.getStaffByDepartmentId(accessToken, Lists.newArrayList(4, 5), true, true, "88146");
        System.out.println(departmentSimpleResult);
        System.out.println(departmentSimpleResult1);
        System.out.println(staffByDepartmentId);

    }

    @Test
    public void updateCreateTime() {
        agentConfigDAO.updateInfoAfterUpgradeSuccess("74164", "1c4e5844001244d893fea24015cdfd04");
    }

    @Test
    public void testGetSuiteAccessTokenByOtherCloud(){
        String suiteId = "dk0927f55c5f1950f9";
        String suitAccessTokenByOtherCloud = qywxManager.getSuitAccessToken4OtherCloud(suiteId);
        System.out.println("yes:" + suitAccessTokenByOtherCloud);
    }
}