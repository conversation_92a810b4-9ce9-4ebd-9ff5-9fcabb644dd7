/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.advertiser.AdBigScreenSettingUpdateArg;
import com.facishare.marketing.api.arg.advertiser.AdLeadDataArg;
import com.facishare.marketing.api.arg.advertiser.ExportUserMarketingGroupArg;
import com.facishare.marketing.api.result.advertiser.AdBigScreenResult;
import com.facishare.marketing.api.result.advertiser.AdBigScreenSettingResult;
import com.facishare.marketing.api.result.advertiser.AdLeadDataResult;
import com.facishare.marketing.api.service.advertiser.AdvertiserService;
import com.facishare.marketing.common.enums.ExportAdEncryptionEnum;
import com.facishare.marketing.common.enums.baidu.AccountStatusEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.bo.advertise.AdCampaignDataStatisticsBO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDataDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDataDAO;
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDataDAO;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignDataEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity;
import com.facishare.marketing.provider.manager.advertiser.AdBigScreenManager;
import com.facishare.marketing.provider.manager.advertiser.headlines.HeadlinesAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.facishare.marketing.provider.manager.advertiser.AdBigScreenManager.OPPORTUNITY_STG_CHANGED_TIME;
import static com.facishare.marketing.provider.manager.advertiser.AdBigScreenManager.OPPORTUNITY_WIN_STAGE;

@Slf4j
public class AdvertiserServicTest extends BaseTest {

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AdBigScreenManager adBigScreenManager;

    @Autowired
    private HeadlinesCampaignDataDAO headlinesCampaignDataDAO;

    @Autowired
    private RefreshDataManager refreshDataManager;

    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;

    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;

    @Autowired
    private BaiduAccountDAO accountDAO;

    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;

    @Autowired
    private HeadlinesAdMarketingManager headlinesAdMarketingManager;

    @Autowired
    private BaiduCampaignDataDAO baiduCampaignDataDAO;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;

    @Autowired
    private HeadlinesAdDataDAO headlinesAdDataDAO;

    @Autowired
    private TencentAdGroupDataDAO tencentAdGroupDataDAO;


    @Test
    public void getKeywordCostMapTest() {
        AdBigScreenManager.TimePeriod timePeriod = new AdBigScreenManager.TimePeriod();
        Date now = new Date();
        timePeriod.setBeginTime(DateUtil.parse("2023-07-13 00:00:00"));
        timePeriod.setEndTime(now);
        // 15  29
        Map<String, AdBigScreenManager.KeywordStatisticsData> bigDecimal = adBigScreenManager.getKeywordStatisticsMap("83668", Lists.newArrayList("6396fdfbc2c4da00013fbc67", "6396fd77c2c4da00013e5b44"), timePeriod.getBeginTime(), timePeriod.getEndTime());
        log.info("结果： {}", bigDecimal);
    }

    @Test
    public void getAdLeadDataTest() {
        AdLeadDataArg adLeadDataArg = new AdLeadDataArg();
        adLeadDataArg.setEa("83668");
        adLeadDataArg.setFsUserId(1000);
        adLeadDataArg.setPageNum(1);
        adLeadDataArg.setPageSize(100);
        Result<List<AdLeadDataResult>> result = advertiserService.getAdLeadData(adLeadDataArg);
        log.info("结果： {}", result);
    }


    @Test
    public void getSettingTest() {
        Result<AdBigScreenSettingResult> adBigScreenSettingResult = advertiserService.getBigScreenSetting("88146", 1000);
        log.info("结果： {}", JSON.toJSONString(adBigScreenSettingResult));
    }

    @Test
    public void updateSettingTest() {
        String json = "{\n" +
                "    \"id\": null,\n" +
                "    \"title\": \"广告营销数据大屏\",\n" +
                "    \"setting\": {\n" +
                "        \"customerAcquisitionCost\": {\n" +
                "            \"name\": \"广告获客成本\",\n" +
                "            \"fieldList\": [\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgClickPrice\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgLeadAcquisitionPrice\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgMQLAcquisitionPrice\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgSQLAcquisitionPrice\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgOpportunityAcquisitionPrice\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgWinOpportunityAcquisitionPrice\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"position\": null\n" +
                "        },\n" +
                "        \"launchEffectTrend\": {\n" +
                "            \"name\": \"广告投放效果趋势\",\n" +
                "            \"fieldList\": [\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"COST\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"LEADS\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"WECHAT_FANS\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"QYWX_EXTERNAL_CUSTOMER\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"position\": null\n" +
                "        },\n" +
                "        \"acquisitionCustomerConvertFunnel\": {\n" +
                "            \"name\": \"广告获客转化漏斗\",\n" +
                "            \"fieldList\": [\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"pv\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"clicks\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"leadsCount\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"leadsMQLCount\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"leadsSQLCount\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"opportunityCount\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"winOpportunityCount\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"mqlDefinition\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": \"select_many\",\n" +
                "                    \"fieldValueList\": [\n" +
                "                        {\n" +
                "                            \"value\": \"Lead\",\n" +
                "                            \"label\": \"潜在线索(Lead)\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"MQL\",\n" +
                "                            \"label\": \"市场认可线索(MQL)\",\n" +
                "                            \"selected\": true\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"SQL\",\n" +
                "                            \"label\": \"销售认可线索(SQL)\",\n" +
                "                            \"selected\": true\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"OPP\",\n" +
                "                            \"label\": \"转商机\",\n" +
                "                            \"selected\": true\n" +
                "                        }\n" +
                "                    ]\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"sqlDefinition\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": \"select_many\",\n" +
                "                    \"fieldValueList\": [\n" +
                "                        {\n" +
                "                            \"value\": \"Lead\",\n" +
                "                            \"label\": \"潜在线索(Lead)\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"MQL\",\n" +
                "                            \"label\": \"市场认可线索(MQL)\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"SQL\",\n" +
                "                            \"label\": \"销售认可线索(SQL)\",\n" +
                "                            \"selected\": true\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"OPP\",\n" +
                "                            \"label\": \"转商机\",\n" +
                "                            \"selected\": true\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ],\n" +
                "            \"position\": null,\n" +
                "            \"mqlDefinition\": {\n" +
                "                \"id\": null,\n" +
                "                \"name\": \"mqlDefinition\",\n" +
                "                \"canEdit\": false,\n" +
                "                \"hidden\": false,\n" +
                "                \"type\": \"select_many\",\n" +
                "                \"fieldValueList\": [\n" +
                "                    {\n" +
                "                        \"value\": \"Lead\",\n" +
                "                        \"label\": \"潜在线索(Lead)\",\n" +
                "                        \"selected\": false\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"MQL\",\n" +
                "                        \"label\": \"市场认可线索(MQL)\",\n" +
                "                        \"selected\": true\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"SQL\",\n" +
                "                        \"label\": \"销售认可线索(SQL)\",\n" +
                "                        \"selected\": true\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"OPP\",\n" +
                "                        \"label\": \"转商机\",\n" +
                "                        \"selected\": true\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            \"sqlDefinition\": {\n" +
                "                \"id\": null,\n" +
                "                \"name\": \"sqlDefinition\",\n" +
                "                \"canEdit\": false,\n" +
                "                \"hidden\": false,\n" +
                "                \"type\": \"select_many\",\n" +
                "                \"fieldValueList\": [\n" +
                "                    {\n" +
                "                        \"value\": \"Lead\",\n" +
                "                        \"label\": \"潜在线索(Lead)\",\n" +
                "                        \"selected\": false\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"MQL\",\n" +
                "                        \"label\": \"市场认可线索(MQL)\",\n" +
                "                        \"selected\": false\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"SQL\",\n" +
                "                        \"label\": \"销售认可线索(SQL)\",\n" +
                "                        \"selected\": true\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"OPP\",\n" +
                "                        \"label\": \"转商机\",\n" +
                "                        \"selected\": true\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        \"accountAcquisitionCustomerCompare\": {\n" +
                "            \"name\": \"广告账户获客对比\",\n" +
                "            \"fieldList\": [\n" +
                "                {\n" +
                "                    \"id\": \"b2f6f708cfd840d787db0879cbef070a\",\n" +
                "                    \"name\": \"用户*************\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"b3a98f88760344d3b64041727d579c97\",\n" +
                "                    \"name\": \"北京易动纷享科技有限责任公司\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"efc63ba004c04c5f91dad784d78c78e6\",\n" +
                "                    \"name\": \"北京易动纷享科技有限责任公司0\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"8b524863784b46f9a46b3b7f6557befb\",\n" +
                "                    \"name\": \"bj-fxiaoke\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"position\": null\n" +
                "        },\n" +
                "        \"accountInputOutputAnalysis\": {\n" +
                "            \"name\": \"广告账户投入产出分析\",\n" +
                "            \"fieldList\": [\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"cost\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"leadsCount\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"leadsSQLCount\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"opportunityCount\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgClickPrice\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgLeadAcquisitionPrice\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgMQLAcquisitionPrice\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgSQLAcquisitionPrice\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"avgOpportunityAcquisitionPrice\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"showDimension\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": \"select_one\",\n" +
                "                    \"fieldValueList\": [\n" +
                "                        {\n" +
                "                            \"value\": \"ACCOUNT\",\n" +
                "                            \"label\": null,\n" +
                "                            \"selected\": true\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"KEYWORD\",\n" +
                "                            \"label\": null,\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"CAMPAIGN\",\n" +
                "                            \"label\": null,\n" +
                "                            \"selected\": false\n" +
                "                        }\n" +
                "                    ]\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"top\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": \"select_one\",\n" +
                "                    \"fieldValueList\": [\n" +
                "                        {\n" +
                "                            \"value\": \"5\",\n" +
                "                            \"label\": null,\n" +
                "                            \"selected\": true\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"10\",\n" +
                "                            \"label\": null,\n" +
                "                            \"selected\": false\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ],\n" +
                "            \"position\": null,\n" +
                "            \"showDimension\": {\n" +
                "                \"id\": null,\n" +
                "                \"name\": \"showDimension\",\n" +
                "                \"canEdit\": false,\n" +
                "                \"hidden\": false,\n" +
                "                \"type\": \"select_one\",\n" +
                "                \"fieldValueList\": [\n" +
                "                    {\n" +
                "                        \"value\": \"ACCOUNT\",\n" +
                "                        \"label\": null,\n" +
                "                        \"selected\": true\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"KEYWORD\",\n" +
                "                        \"label\": null,\n" +
                "                        \"selected\": false\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"CAMPAIGN\",\n" +
                "                        \"label\": null,\n" +
                "                        \"selected\": false\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            \"top\": {\n" +
                "                \"id\": null,\n" +
                "                \"name\": \"top\",\n" +
                "                \"canEdit\": false,\n" +
                "                \"hidden\": false,\n" +
                "                \"type\": \"select_one\",\n" +
                "                \"fieldValueList\": [\n" +
                "                    {\n" +
                "                        \"value\": \"5\",\n" +
                "                        \"label\": null,\n" +
                "                        \"selected\": true\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"value\": \"10\",\n" +
                "                        \"label\": null,\n" +
                "                        \"selected\": false\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        \"overView\": {\n" +
                "            \"name\": \"概览设置\",\n" +
                "            \"fieldList\": [\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"costs\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"leadsCount\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"sqlCount\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": true,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"winOpportunityMoney\",\n" +
                "                    \"canEdit\": true,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": null,\n" +
                "                    \"fieldValueList\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"position\": null\n" +
                "        },\n" +
                "        \"timeSetting\": {\n" +
                "            \"timeRange\": \"THIS_SEASON\",\n" +
                "            \"compareTimeType\": \"YoY\",\n" +
                "            \"beginTime\": 1688140800000,\n" +
                "            \"endTime\": 1690369967109,\n" +
                "            \"compareBeginTime\": 1656604800000,\n" +
                "            \"compareEndTime\": 1664553599000\n" +
                "        },\n" +
                "        \"modulePositions\": [\n" +
                "            \"customerAcquisitionCost\",\n" +
                "            \"overView\",\n" +
                "            \"acquisitionCustomerConvertFunnel\",\n" +
                "            \"launchEffectTrendList\",\n" +
                "            \"leadsArealDistributions\",\n" +
                "            \"leadStageAndSaleSituation\",\n" +
                "            \"acquisitionCustomerKeywordList\",\n" +
                "            \"accountInputOutputAnalysisList\",\n" +
                "            \"accountAcquisitionCustomerCompareList\"\n" +
                "        ],\n" +
                "        \"leadsArealDistributions\": {\n" +
                "            \"name\": \"广告线索地域分布\",\n" +
                "            \"fieldList\": [\n" +
                "                {\n" +
                "                    \"id\": null,\n" +
                "                    \"name\": \"showDimension\",\n" +
                "                    \"canEdit\": false,\n" +
                "                    \"hidden\": false,\n" +
                "                    \"type\": \"select_one\",\n" +
                "                    \"fieldValueList\": [\n" +
                "                        {\n" +
                "                            \"value\": \"china\",\n" +
                "                            \"label\": \"全国\",\n" +
                "                            \"selected\": true\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"hebei\",\n" +
                "                            \"label\": \"河北省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"ningxia\",\n" +
                "                            \"label\": \"宁夏\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"guizhou\",\n" +
                "                            \"label\": \"贵州省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"xinjiang\",\n" +
                "                            \"label\": \"新疆\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"beijing\",\n" +
                "                            \"label\": \"北京市\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"fujian\",\n" +
                "                            \"label\": \"福建省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"hainan\",\n" +
                "                            \"label\": \"海南省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"guangdong\",\n" +
                "                            \"label\": \"广东省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"guangxi\",\n" +
                "                            \"label\": \"广西\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"heilongjiang\",\n" +
                "                            \"label\": \"黑龙江省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"zhejiang\",\n" +
                "                            \"label\": \"浙江省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"qinghai\",\n" +
                "                            \"label\": \"青海省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"jiangsu\",\n" +
                "                            \"label\": \"江苏省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"henan\",\n" +
                "                            \"label\": \"河南省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"shanxi\",\n" +
                "                            \"label\": \"山西省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"xizang\",\n" +
                "                            \"label\": \"西藏\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"yunnan\",\n" +
                "                            \"label\": \"云南省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"liaoning\",\n" +
                "                            \"label\": \"辽宁省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"hunan\",\n" +
                "                            \"label\": \"湖南省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"xianggang\",\n" +
                "                            \"label\": \"香港\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"aomen\",\n" +
                "                            \"label\": \"澳门\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"anhui\",\n" +
                "                            \"label\": \"安徽省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"jiangxi\",\n" +
                "                            \"label\": \"江西省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"tianjin\",\n" +
                "                            \"label\": \"天津\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"hubei\",\n" +
                "                            \"label\": \"湖北省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"chongqing\",\n" +
                "                            \"label\": \"重庆\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"gansu\",\n" +
                "                            \"label\": \"甘肃省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"shanxi1\",\n" +
                "                            \"label\": \"陕西省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"taiwan\",\n" +
                "                            \"label\": \"台湾\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"shandong\",\n" +
                "                            \"label\": \"山东省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"jilin\",\n" +
                "                            \"label\": \"吉林省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"shanghai\",\n" +
                "                            \"label\": \"上海\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"sichuan\",\n" +
                "                            \"label\": \"四川省\",\n" +
                "                            \"selected\": false\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value\": \"neimenggu\",\n" +
                "                            \"label\": \"内蒙古\",\n" +
                "                            \"selected\": false\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ],\n" +
                "            \"position\": null\n" +
                "        }\n" +
                "    },\n" +
                "    \"selectableTimeList\": [\n" +
                "        {\n" +
                "            \"timeRange\": \"THIS_YEAR\",\n" +
                "            \"beginTime\": 1672502400000,\n" +
                "            \"endTime\": 1690369967108,\n" +
                "            \"yoYBeginTime\": 1640966400000,\n" +
                "            \"yoYEndTime\": 1672502399000,\n" +
                "            \"momBeginTime\": 1640966400000,\n" +
                "            \"momEndTime\": 1672502399000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"LAST_YEAR\",\n" +
                "            \"beginTime\": 1640966400000,\n" +
                "            \"endTime\": 1672502399000,\n" +
                "            \"yoYBeginTime\": 1609430400000,\n" +
                "            \"yoYEndTime\": 1640966399000,\n" +
                "            \"momBeginTime\": 1609430400000,\n" +
                "            \"momEndTime\": 1640966399000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"LAST_HALF_YEAR\",\n" +
                "            \"beginTime\": 1672502400000,\n" +
                "            \"endTime\": 1688140799000,\n" +
                "            \"yoYBeginTime\": 1640966400000,\n" +
                "            \"yoYEndTime\": 1656604799000,\n" +
                "            \"momBeginTime\": 1656604800000,\n" +
                "            \"momEndTime\": 1672502399000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"NEXT_HALF_YEAR\",\n" +
                "            \"beginTime\": 1688140800000,\n" +
                "            \"endTime\": 1690369967109,\n" +
                "            \"yoYBeginTime\": 1656604800000,\n" +
                "            \"yoYEndTime\": 1672502399000,\n" +
                "            \"momBeginTime\": 1672502400000,\n" +
                "            \"momEndTime\": 1688140799000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"THIS_SEASON\",\n" +
                "            \"beginTime\": 1688140800000,\n" +
                "            \"endTime\": 1690369967109,\n" +
                "            \"yoYBeginTime\": 1656604800000,\n" +
                "            \"yoYEndTime\": 1664553599000,\n" +
                "            \"momBeginTime\": 1680278400000,\n" +
                "            \"momEndTime\": 1688140799000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"LAST_SEASON\",\n" +
                "            \"beginTime\": 1680278400000,\n" +
                "            \"endTime\": 1688140799000,\n" +
                "            \"yoYBeginTime\": 1648742400000,\n" +
                "            \"yoYEndTime\": 1656604799000,\n" +
                "            \"momBeginTime\": 1672502400000,\n" +
                "            \"momEndTime\": 1680191998000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"THIS_MONTH\",\n" +
                "            \"beginTime\": 1688140800000,\n" +
                "            \"endTime\": 1690369967109,\n" +
                "            \"yoYBeginTime\": 1656604800000,\n" +
                "            \"yoYEndTime\": 1659283199000,\n" +
                "            \"momBeginTime\": 1685548800000,\n" +
                "            \"momEndTime\": 1688140799000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"LAST_MONTH\",\n" +
                "            \"beginTime\": 1685548800000,\n" +
                "            \"endTime\": 1688140799000,\n" +
                "            \"yoYBeginTime\": 1654012800000,\n" +
                "            \"yoYEndTime\": 1656604799000,\n" +
                "            \"momBeginTime\": 1682870400000,\n" +
                "            \"momEndTime\": 1685462399000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"THIS_WEEK\",\n" +
                "            \"beginTime\": 1690128000000,\n" +
                "            \"endTime\": 1690369967109,\n" +
                "            \"yoYBeginTime\": 1658592000000,\n" +
                "            \"yoYEndTime\": 1659196799000,\n" +
                "            \"momBeginTime\": 1689523200000,\n" +
                "            \"momEndTime\": 1690127999000\n" +
                "        },\n" +
                "        {\n" +
                "            \"timeRange\": \"LAST_WEEK\",\n" +
                "            \"beginTime\": 1689523200000,\n" +
                "            \"endTime\": 1690127999000,\n" +
                "            \"yoYBeginTime\": 1657987200000,\n" +
                "            \"yoYEndTime\": 1658591999000,\n" +
                "            \"momBeginTime\": 1688918400000,\n" +
                "            \"momEndTime\": 1689523199000\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        //json = JSONObject.parseObject(json).getJSONObject("data").toJSONString();
        AdBigScreenSettingUpdateArg arg = JSONObject.parseObject(json, AdBigScreenSettingUpdateArg.class);
        arg.setEa("83668");
        Result<Void> result = advertiserService.updateBigScreenSetting(arg);
        log.info("结果： {}", result);

//        Result<AdBigScreenSettingResult> adBigScreenSettingResult = advertiserService.getBigScreenSetting("74164", 1000);
//        log.info("结果2： {}", JSON.toJSONString(adBigScreenSettingResult));
    }

    @Test
    public void bigScreenTest() {

        Result<AdBigScreenResult> adBigScreenSettingResult = advertiserService.bigScreen("83668", 1);
        log.info("结果： {}", JSON.toJSONString(adBigScreenSettingResult));
    }


    @Test
    public void testSQL() {
        String ea = "83668";
        Date beginTime = new Date();
        Date endTime = DateUtil.minusDay(beginTime, 200);
        Date relaBegin = DateUtil.minusDay(beginTime, 400);
        Date relaEnd = DateUtil.minusDay(beginTime, 200);
        List<Long> test = Lists.newArrayList(123456L, 333333L);
        List<AdCampaignDataStatisticsBO> baidu = baiduCampaignDataDAO.statisticsCampaignDataGroupByCampaign(ea, beginTime, endTime, relaBegin, relaEnd, test);

        List<AdCampaignDataStatisticsBO> toutiao1 = headlinesCampaignDataDAO.statisticsCampaignDataGroupByCampaign(ea, beginTime, endTime, relaBegin, relaEnd, test);

        List<AdCampaignDataStatisticsBO> toutiao2 = headlinesAdDataDAO.statisticsAdDataGroupByAdIdList(ea, beginTime, endTime, relaBegin, relaEnd, test);

        List<AdCampaignDataStatisticsBO> tecent = tencentAdGroupDataDAO.statisticsCampaignDataGroupByCampaign(ea, beginTime, endTime, relaBegin, relaEnd, test);

        List<String> marketingEventIdList = Lists.newArrayList("222");

        List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryByMarketingEventIdList(ea, marketingEventIdList);

        List<TencentAdGroupEntity> adGroupEntityList = tencentAdGroupDAO.queryBySubMarketingEventIdList(ea, marketingEventIdList);

        List<HeadlinesCampaignEntity> headlinesCampaignEntityList = headlinesCampaignDAO.getByMarketingEventIdList(ea, marketingEventIdList);

        List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryBySubMarketingIdList(ea, marketingEventIdList);


    }

    @Test
    public void refreshPrototypeRoomAccountDataTest() {
//        Map<String, Object> editLeadObjectData = Maps.newHashMap();
//        editLeadObjectData.put("_id", "64d5a637a47ecb0001403549");
//        editLeadObjectData.put("leads_stage", "MQL");
//        editLeadObjectData.put("changed_to_mql_time", System.currentTimeMillis());
//        editLeadObjectData.put("changed_to_mql_period", 1000 * 60 * 60 * 24 * 4);
//        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> result =  crmV2Manager.editObjectData("82255", CrmObjectApiNameEnum.CRM_LEAD.getName(), editLeadObjectData);
//        log.info("result22: {}", result);
        advertiserService.refreshPrototypeRoomAccountData();
    }

    @Test
    public void batchInsertTest() {
        List<BaiduCampaignDataEntity> campaignDataEntityList = Lists.newArrayList();
        BaiduCampaignDataEntity dataEntity = new BaiduCampaignDataEntity();
        dataEntity.setId(UUIDUtil.getUUID());
        dataEntity.setEa("88146");
        dataEntity.setAdAccountId(UUIDUtil.getUUID());
        dataEntity.setCampaignId(66666L);
        dataEntity.setLeads(1);
        dataEntity.setClick(1L);
        dataEntity.setPv(1L);
        dataEntity.setCost(1D);
        dataEntity.setLeads(1);
        dataEntity.setOcpcConversions(100L);
        dataEntity.setDeepOcpcConversions(200L);
        dataEntity.setActionDate(new Date());
        campaignDataEntityList.add(dataEntity);
        int count = baiduCampaignDataDAO.batchInsert(campaignDataEntityList);

    }

    public static void main(String[] args) {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setId(UUIDUtil.getUUID());
        adAccountEntity.setEa("82450");
        adAccountEntity.setUsername("样板间百度账号");
        adAccountEntity.setPassword("123456");
        adAccountEntity.setAccessKey("123456");
        adAccountEntity.setSecretKey("123456");
        adAccountEntity.setToken("123456");
        adAccountEntity.setRefreshToken("123456");
        adAccountEntity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        adAccountEntity.setStatus(AccountStatusEnum.NORMAL.getStatus());
        adAccountEntity.setAccountId(123456L);
        adAccountEntity.setBalance(52013.14D);
        adAccountEntity.setPcBalance(30000D);
        adAccountEntity.setMobileBalance(20013.14D);
        adAccountEntity.setCost(90013.56D);
        adAccountEntity.setBudget(150000D);
        System.out.println(JSON.toJSONString(adAccountEntity));
    }

    @Test
    public void getBigScreenSettingTest() {
        adBigScreenManager.getBigScreenSetting("83668");
    }

    @Test
    public void doAddTeamMemberToCrm() {
        com.fxiaoke.crmrestapi.common.result.Result<Void> result = crmV2Manager.doAddTeamMemberToCrm("88146",
                SuperUserConstants.USER_ID, Lists.newArrayList(1003, 1001), Lists.newArrayList("6454e60d1d184100012ddf77"),
                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), Lists.newArrayList("4"), "1");
        log.info("result: {}", result);

        result = crmV2Manager.doAddTeamMemberToCrm("88146",
                SuperUserConstants.USER_ID, Lists.newArrayList(1003, 1001, 1000), Lists.newArrayList("6454e60d1d184100012ddf77"),
                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), Lists.newArrayList("4"), "1");
        log.info("result: {}", result);

        result = crmV2Manager.doAddTeamMemberToCrm("88146",
                SuperUserConstants.USER_ID, Lists.newArrayList(1003, 1027), Lists.newArrayList("6454e60d1d184100012ddf77"),
                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), Lists.newArrayList("4"), "1");
        log.info("result: {}", result);
    }

    @Test
    public void replaceTeamMember() {
        crmV2Manager.replaceTeamMember("88146", Lists.newArrayList(1003, 1001), "6454e60d1d184100012ddf77",
                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());

        crmV2Manager.replaceTeamMember("88146", Lists.newArrayList(1000, 1027), "6454e60d1d184100012ddf77",
                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());

        crmV2Manager.replaceTeamMember("88146", Lists.newArrayList(1000, 1003), "6454e60d1d184100012ddf77",
                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());

        crmV2Manager.replaceTeamMember("88146", Lists.newArrayList(), "6454e60d1d184100012ddf77",
                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
    }

    @Test
    public void exportUserMarketingGroup() {
        ExportUserMarketingGroupArg arg = new ExportUserMarketingGroupArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setPhonePrefix("+86");
        arg.setMarketingUserGroupId("3ed43dd24d734e6daaddf95bb2226a0c");
        arg.setEncryptionType(ExportAdEncryptionEnum.MD5.getType());
        advertiserService.exportUserMarketingGroup(arg);
    }

    @Test
    public void syncHeadlinesCampaignToMarketingEventObj() {
        String ea = "83668";
        AdAccountEntity adAccountEntity = accountDAO.queryAccountById("","b2f6f708cfd840d787db0879cbef070a");
        headlinesAdMarketingManager.syncHeadlinesCampaignToMarketingEventObj(ea, adAccountEntity, AdSourceEnum.SOURCE_JULIANG.getSource());
    }

    @Test
    public void syncHeadlinesAdToSubMarketingEventObj() {
        String ea = "83668";
        AdAccountEntity adAccountEntity = accountDAO.queryAccountById("","b2f6f708cfd840d787db0879cbef070a");
        headlinesAdMarketingManager.syncHeadlinesAdToSubMarketingEventObj(ea, adAccountEntity, AdSourceEnum.SOURCE_JULIANG.getSource());
    }
}
