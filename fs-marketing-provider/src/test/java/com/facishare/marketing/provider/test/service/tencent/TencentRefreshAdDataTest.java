/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.tencent;

import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager;
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager;
import com.facishare.marketing.provider.service.advertiser.tencent.TencentAdServiceImpl;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

@Slf4j
public class TencentRefreshAdDataTest extends BaseTest {

    @Autowired
    private BaiduAccountDAO adAccountDAO;
    @Autowired
    private TencentAdMarketingManager tencentAdMarketingManager;
    @Autowired
    private AdTokenManager adTokenManager;
    @Autowired
    private TencentAdServiceImpl tencentAdService;

    @Test
    public void refreshAccountInfoTest() {
        String ea = "74164";
        Long accountId = 9524781L;
        AdAccountEntity entity = adAccountDAO.queryAccountByEaAndAccountId(ea, accountId, AdSourceEnum.SOURCE_TENCETN.getSource());
        tencentAdMarketingManager.refreshAccountInfo(entity);
    }

    @Test
    public void refreshTencentCampaignTest() {
        String ea = "74164";
        Long accountId = 24142584L;
        String adAccountId = "f178c512528045aeaff086e1eaf78ced";
        AdAccountEntity adAccountEntity = adAccountDAO.queryEnableAccountById(adAccountId);
        tencentAdMarketingManager.refreshTencentCampaign(ea, adAccountEntity);
    }

    @Test
    public void refreshTencentAdGroupTest() {
        String ea = "74164";
        Long accountId = 24142584L;
        String adAccountId = "f178c512528045aeaff086e1eaf78ced";
        AdAccountEntity adAccountEntity = adAccountDAO.queryEnableAccountById(adAccountId);
        tencentAdMarketingManager.refreshTencentAdGroup(ea, adAccountEntity, null);
    }

    @Test
    public void syncTencentCampaignToMarketingEventObjTest() {
        String ea = "88146";
        Long accountId = 24142584L;
        String adAccountId = "84d929dac8674c21a27928fde636e940";
        tencentAdMarketingManager.syncTencentCampaignToMarketingEventObj(ea, accountId, adAccountId);
    }

    @Test
    public void syncTencentAdGroupToSubMarketingEventObjTest() {
        String ea = "88146";
        String adAccountId = "4de8b77a709e4f1d8853ae5a089a1479";
        tencentAdMarketingManager.syncTencentAdGroupToSubMarketingEventObj(ea, adAccountId);
    }

    @Test
    public void syncTencentClueDataToCrmTest() {
        String ea = "74164";
        Long accountId = 24142584L;
        String adAccountId = "f178c512528045aeaff086e1eaf78ced";
        Long startTime = 1641008142000L;
        Long endTime = 1653881742000L;
        AdAccountEntity adAccountEntity = adAccountDAO.queryEnableAccountById(adAccountId);
        List<Long> timeList = DateUtil.initMonthTimeList(DateUtil.getLastYearFromTime(System.currentTimeMillis()), 3);
        for (int i = 0; i < timeList.size()-1; i++) {
            tencentAdMarketingManager.syncTencentClueDataToCrm(ea, adAccountEntity, adAccountEntity.getId(), timeList.get(i), timeList.get(i+1));
        }
        System.out.println("finish");
    }

    @Test
    public void refreshTencentAdGroupDataTest() {
        String ea = "83668";
        String adAccountId = "e0da465401a34e528a35c7907b1113ff";
        AdAccountEntity adAccountEntity = adAccountDAO.queryEnableAccountById(adAccountId);
        tencentAdMarketingManager.refreshTencentAdGroupData(ea, adAccountEntity, adAccountId, "2024-12-11", "2024-12-11");
    }

    @Test
    public void getTencentAccessTokenTest() {
        String ea = "74164";
        Long accountId = 24142584L;
        String adAccountId = "d134de4e361d44528b4ee3c76b572e08";
        AdAccountEntity adAccountEntity = adAccountDAO.queryEnableAccountById(adAccountId);
        Optional<String> tencentAccessToken = adTokenManager.getTencentAccessToken(adAccountEntity);
        System.out.println(tencentAccessToken);
    }

    @Test
    public void getBaiduAccessTokenTest() {

        String adAccountId = "95b1e884a2664ea4b101525bee5a2842";
        AdAccountEntity adAccountEntity = adAccountDAO.queryEnableAccountById(adAccountId);
        String accessToken = adTokenManager.getBaiduAccessToken(adAccountEntity);

        adAccountId = "3b7d66a5f84c4680abfceae242a9092e";
        adAccountEntity = adAccountDAO.queryEnableAccountById(adAccountId);
        accessToken = adTokenManager.getBaiduAccessToken(adAccountEntity);

        log.info("accessToken: {}", accessToken);
    }

    @Test
    public void refreshPrototypeRoomAccountDataTest() {
        tencentAdMarketingManager.refreshPrototypeRoomAccountData("82255", 1000);
    }
}
