/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.qywx;

import com.facishare.marketing.api.arg.qywx.card.QueryCardArg;
import com.facishare.marketing.api.service.qywx.CardService;
import com.facishare.marketing.api.vo.UpdateEnterpriseDefaultProductVO;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.manager.AccountManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.cardtemplate.template.*;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created  By zhoux 2020/01/16
 **/
@Slf4j
public class CardServiceTest extends BaseTest {

    @Autowired
    private CardService cardService;
    @Autowired
    private AccountManager accountManager;
    @Autowired
    BusinessCardTemplate businessCardTemplate;
    @Autowired
    private FramelessCardTemplate framelessCardTemplate;
    @Autowired
    private BlackGoldCardTemplate blackGoldCardTemplate;
    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private TraditionCardTemplate traditionCardTemplate;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Test
    public void queryMyBaseCardInfo() {
        QueryCardArg arg = new QueryCardArg();
//        arg.setUid("2d7124123f1148bd8a296dd7065c2825");
        arg.setUid("f3702dc215f94d45857912d7283c9ee1");
        arg.setCardUid("f3702dc215f94d45857912d7283c9ee1");
//        arg.setCardUid("2d7124123f1148bd8a296dd7065c2825");
        System.out.println(cardService.queryMyBaseCardInfo(arg));
    }

    @Test
    public void queryMyBaseCardInfoWithFsInfo() {
        cardService.queryMyBaseCardInfoWithFsInfo("77741", 1004);
    }

    @Test
    public void updateEnterpriseDefaultProduct() {
        UpdateEnterpriseDefaultProductVO vo = new UpdateEnterpriseDefaultProductVO();
        vo.setProductIds(Lists.newArrayList("1efcbc48c7644a69a5b3734ac8010dc9", "d8be233aee2b4fc398dee592aca061b5", "9a7ff4b6a5f341bfa5b0edf3aa211314", "a8baf0928b2a48b7b4d0ca49202ac7d6", "4ab07b8e6c48429bb45518c497e4d239"));
        vo.setEa("74164");
        cardService.updateEnterpriseDefaultProduct(vo);
    }

    @Test
    public void createAccountTest() {
        boolean result = accountManager.createAccount("2db507c7b7334b3587fdb502dd198322", "王振懿", 1, "", "***********", "74164", "84625", 2);
        log.info("result is:{}", result);
    }

    @Test
    public void testClassicDraw(){
//        CardEntity cardEntity = new CardEntity();
//        cardEntity.setAvatar("https://wework.qpic.cn/wwpic3az/49429_8g7GxEVlSpOziyY_1704011530/0");
//        cardEntity.setVocation("销售代表");
//        cardEntity.setCompanyName("湖南竞网数字科技集团有限公司");
//        cardEntity.setCompanyAddress("湖南省长沙市岳麓区尖山路18号B7栋");
//        cardEntity.setCardTemplateId("444ee051bd1d4a75ba586ab873207020");
//        CardEntity cardEntity = cardDAO.queryCardInfoById("0a144068e28f4468b139101529c4e662");
        CardEntity cardEntity = cardDAO.queryCardInfoById("4423c50fc160426fb535731f922fa3c9");
        blackGoldCardTemplate.drawTemplate("88146",cardEntity);
    }

    @Test
    public void testSimpleDraw(){
        CardEntity cardEntity = cardDAO.queryCardInfoById("4423c50fc160426fb535731f922fa3c9");
        //CardEntity cardEntity = cardDAO.queryCardInfoById("33635e80fef94b7e89fb1ec01d74292c");
        framelessCardTemplate.drawTemplate("88146",cardEntity);
    }

    @Test
    public void NewDepartmentCardTemplate(){
//        CardEntity cardEntity = new CardEntity();
//        cardEntity.setAvatar("https://wework.qpic.cn/wwpic3az/49429_8g7GxEVlSpOziyY_1704011530/0");
//        cardEntity.setVocation("销售代表");
//        cardEntity.setCompanyName("湖南竞网数字科技集团有限公司");
//        cardEntity.setCompanyAddress("湖南省长沙市岳麓区尖山路18号B7栋");
//        cardEntity.setCardTemplateId("444ee051bd1d4a75ba586ab873207020");
//        CardEntity cardEntity = cardDAO.queryCardInfoById("0a144068e28f4468b139101529c4e662");
        CardEntity cardEntity = cardDAO.queryCardInfoByUid("716d84b1256a48af9a0096b1a89b4aac");
        String string = traditionCardTemplate.drawTemplate("88146", cardEntity);
        String path = fileV2Manager.getUrlByPath("88146", string);
        System.out.println(path);
    }

}
