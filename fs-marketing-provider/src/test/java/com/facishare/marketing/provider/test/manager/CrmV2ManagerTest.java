/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.common.enums.CrmWechatWorkExternalUserFieldEnum;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.DuplicateSearchResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import shade.com.alibaba.fastjson.JSON;

import java.util.*;

@Slf4j
public class CrmV2ManagerTest extends BaseTest {
    @Autowired
    private CrmV2Manager crmV2Manager;

    @Test
    public void duplicateSearchResult(){
        String ea = "74164";
        String apiName = LeadsFieldContants.API_NAME;
        Map<String, Object> param = new HashMap<>();
        Integer pageNum = 1;
        Integer pageSize = 1;
        param.put("field_F8We6__c", "option1");
        param.put("createTime", "1627007390738");

        param.put("object_describe_api_name", apiName);
        param.put("object_describe_id", apiName);

        param.put("field_0i2O6__c", Lists.newArrayList("option1"));
        param.put("name", "哟");
        param.put("mobile", "13825276879");
        param.put("promotion_channel", "email");
        param.put("tel", "13825276879");
        param.put("company", "锦湖集团");
        param.put("source", "0fhn5ijS2");
        param.put("field_2CspT__c", "8uTJkgql2");
        param.put("email", "<EMAIL>");
        log.info("par");

        DuplicateSearchResult result = crmV2Manager.duplicateSearchResult(ea, apiName, param, pageNum, pageSize);
    }

    @Test
    public void addWechatFriendsRecordObjTest() {
        Map<String, Object> data = JSON.parseObject("{\"record_id\":\"c51756f281dd4724b14488e899d4399d\",\"name\":\"JIA°\",\"qywx_user_id\":\"wowx1mDAAAiRVtHMRHh5SRe7\nbLHqlECg\",\"add_time\":1659493312,\"external_user_id\":\"wmwx1mDAAAw7Gp9eg0lfXPCvXnbqRJLw\"}");
        crmV2Manager.addWechatFriendsRecordObj("74164", data, -10000);
    }

    @Test
    public void isEsixtField(){
        boolean existFiled = crmV2Manager.isExistFiled("88146", CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ, CrmWechatWorkExternalUserFieldEnum.WX_UNION_ID.getFieldName());
        System.out.println(existFiled);
    }

    @Test
    public void createLead(){
        String ea = "83668";
        Integer userId = 1000;
        Map<String, Object> params = new HashMap<>();
        params.put("marketing_event_id","63886d858cb6e800012d3df3");
        params.put("leads_pool_id", "other");
        params.put("from_marketing", true);
        params.put("name","张总工程师4");
        params.put("mobile","13500008884");
        params.put("promotion_channel","website");
        params.put("landing_page_id","6392b0e21ef3240001041c31");
        params.put("record_ty", true);
        CreateLeadResult result = crmV2Manager.createLead(ea, userId, params, false, false);
        log.info("result:{}", result);
    }

    @Test
    public void editCampaignObjs(){
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("marketing_save_status","2");
        dataMap.put("last_modified_time", System.currentTimeMillis() - (1000 * 60 * 2));
        crmV2Manager.editCampaignMembersObj("88146","65e59445bada240001659823",dataMap);
    }

    @Test
    public void testIsExistsObject(){
        String ea = "88162";
        String apiName = CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName();
        boolean existObject = crmV2Manager.isExistObject(ea, apiName);
        System.out.println("yes:" + existObject);
    }

    @Test
    public void testCount(){
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        List<PaasQueryArg.Condition> conditionFilters = new ArrayList<>();
        PaasQueryArg.Condition e1 = new PaasQueryArg.Condition("create_time",Lists.newArrayList("1"),PaasAndCrmOperatorEnum.BETWEEN.getCrmOperator());
        e1.setValueType(3);
        conditionFilters.add(e1);
        query.setFilters(conditionFilters);
        query.addFilter("status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("0"));
        filterArg.setQuery(query);
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        filterArg.setSelectFields(Arrays.asList("_id", "chat_id","leader_id"));
        int count = crmV2Manager.countCrmObjectByFilterV3("90364", -10000, filterArg);
        System.out.println(count);
    }

    @Test
    public void testEdit() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("_id", "6685358f43a1250008854066");
        map.put("custom_id", "662a40a7dbc4f200071cdee1");
        Result<ActionEditResult> result = crmV2Manager.editObjectData("88146", CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), map);
        log.info("结果： {}", result);
    }
}
