/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.arg.BulkDeleteArg;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.WechatFanFieldContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.BulkDeleteResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import org.hibernate.validator.internal.util.logging.Log_$logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by zhengh on 2020/11/12.
 */
public class MetadataControllerServiceManagerTest extends BaseTest {
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Test
    public void detail(){
        String marketingEventId = "5facd7539734690001cb222f";
        String ea = "74164";
        ControllerDetailArg arg = new  ControllerDetailArg ();
        arg.setObjectDataId(marketingEventId);
        arg.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        try {
            ObjectData objectData = metadataControllerServiceManager.detail(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), MarketingEventFieldContants.API_NAME, arg);
            System.out.print("objectData:" + objectData);
        }catch (Exception e){
            if (e instanceof OuterServiceRuntimeException){
                System.out.print("OuterServiceRuntimeException code:"+ ((OuterServiceRuntimeException) e).getCode());
                System.out.print("OuterServiceRuntimeException e:"+ e.getMessage());
                if (e.getMessage().equals("数据已作废或已删除")){
                    System.out.print("****************");
                }
            }
        }
    }

    @Test
    public void bulkDeleteTest() {
        String ea = "74164";
        Integer fsUserId = -10000;
        BulkDeleteArg arg = new BulkDeleteArg();
        arg.setDataIds(Lists.newArrayList("62a9269f7062e30001db9eee"));
        Result<BulkDeleteResult> result = objectDataService.bulkDelete(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), CrmObjectApiNameEnum.TERM_SERVING_LINES.getName(), arg, true);
        System.out.println(result);
    }

    @Test
    public void list() {
        ControllerListArg arg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.setOffset(0);
        searchQuery.addFilter("marketing_event_id", Lists.newArrayList("62734a38974c5c00013df510"), FilterOperatorEnum.EQ);
        arg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> result = metadataControllerServiceManager.listResults(new HeaderObj(eieaConverter.enterpriseAccountToId("74164"), 1135), WechatFanFieldContants.API_NAME, arg);
        System.out.println(result);
    }
}
