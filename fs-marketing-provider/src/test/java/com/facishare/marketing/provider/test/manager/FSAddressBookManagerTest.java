/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.api.arg.qywx.media.GetMediaIdAndMiniInfoArg;
import com.facishare.marketing.api.arg.qywx.staff.QueryQywxStaffPageArg;
import com.facishare.marketing.api.result.qywx.staff.QueryQywxStaffPageDTO;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.api.service.qywx.QYWXMediaService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.qywx.QYWXMomentTaskDAO;
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO;
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity;
import com.facishare.marketing.provider.entity.qywx.QywxMomentTaskEntity;
import com.facishare.marketing.provider.innerArg.qywx.QywxAgentMessageArg;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.fxiaoke.enterpriserelation2.arg.EmployeeQueryConditionArg;
import com.fxiaoke.enterpriserelation2.arg.GetDownstreamEmployeeInfoArg;
import com.fxiaoke.enterpriserelation2.arg.ListPublicEmployeeInfosArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.EmployeeCardResult;
import com.fxiaoke.enterpriserelation2.result.GetDownstreamEmployeeInfoResult;
import com.fxiaoke.enterpriserelation2.result.ListDownstreamEmployeesByDownstreamOuterTenantIdsResult;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class FSAddressBookManagerTest extends BaseTest {

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    QyWxAddressBookDAO qyWxAddressBookDAO;
    @Autowired
    LiveService liveService;

    @Autowired
    private PublicEmployeeService publicEmployeeService;

    @Test
    public void getEmployeeIdsByCircleIdsTest() {
        liveService.checkPolyvSubmit("b64c105f48b448b7ab4dccb14e16baec","18568276703");
    }

    @Test
    public void getEmployeeInfoTest() {
        FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo("74164", 1075);
        System.out.println(fsEmployeeMsg);
    }

    @Test
    public void getEmployeeIdsByEaTest() {
        List<Integer> employeeIds = fsAddressBookManager.getEmployeeIdsByEa("55487");
        Assert.assertTrue(employeeIds.size() > 0);
    }

    @Test
    public void getEmployeeInfoByEaTest() {
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByEa("74164");
        Assert.assertTrue(!fsEmployeeMsgMap.isEmpty());
    }

    @Test
    public void getEmployeeInfoByUserIdsTest() {
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds("74164", Arrays.asList(1057, 1033), true);
        FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(1033);
        //fsEmployeeMsg.buildNPathProfileImage2Url();
        System.out.println(fsEmployeeMsg);
        Assert.assertTrue(fsEmployeeMsgMap.size() == 2);
    }

    @Test
    public void getEnterpriseAdminListTest() {
        List<Integer> adminList = fsAddressBookManager.getEnterpriseAdminList("55487", 1004);
        Assert.assertTrue(adminList.size() > 0);
    }

    @Test
    public void test22(){
        String upstreamEA = "zhenju0111";
        List<Long> outerTenantIds = Lists.newArrayList(300016575L, 300019734L);
        Result<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> result = fsAddressBookManager.listDownstreamEmployeesByOuterTenantIds(upstreamEA, outerTenantIds);
        System.out.println("yes:" + result);
    }

    @Test
    public void a() {
        String ea = "74164";
        List<Integer> userIds = Lists.newArrayList();
        userIds.add(1071);
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> map = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);
        System.out.print("map:" + map);
    }

    @Test
    public void queryQywxStaffPage() {
        Page page = new Page();
        page.setPageSize(100);
        page.setPageNo(1);
        String ea = "74164";
        QueryQywxStaffPageArg arg = new QueryQywxStaffPageArg();
        arg.setFsEa(ea);
        arg.setFsUserId(1177);
        arg.setKeyword("亚捷");
        arg.setUserIdList(Lists.newArrayList("wowx1mDAAAkz1T_9lPa_hsSgwhP_hchQ"));
        arg.setActivatedStatus(2);
        arg.setExpireBeginTime(1670947100L);
        arg.setExpireEndTime(1670947300L);
        /*List<QueryQywxStaffPageDTO> list = qyWxAddressBookDAO.queryQywxStaffPage(arg, page);
        System.out.print("size:" + list.size());
        System.out.print("size:" + list.toString());*/
    }

    @Test
    public void aaaa() {

        HeaderObj headerMap = HeaderObj.newInstance(99);
        headerMap.setAppId("FSAID_1149101c");

        GetDownstreamEmployeeInfoArg arg = new GetDownstreamEmployeeInfoArg();
        arg.setUpstreamTenantId(88146);
        arg.setOuterUid(300097773L);
        arg.setLinkAppId("FSAID_1149101c");

        RestResult<GetDownstreamEmployeeInfoResult> downstreamEmployeeInfo = publicEmployeeService.getDownstreamEmployeeInfo(headerMap, arg);
        System.out.println(downstreamEmployeeInfo);
    }

    @Test
    public void getFsUserIdByDepartmentIdListTest() {
        List<Integer> list = fsAddressBookManager.getFsUserIdByDepartmentIdList("88146", Lists.newArrayList(999999), false, MainDepartment.MAIN);
        log.info("结果：{}",list);
    }

}
