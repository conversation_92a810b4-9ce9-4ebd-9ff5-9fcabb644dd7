/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.wxcoupon;

import com.facishare.marketing.api.result.wxcoupon.*;
import com.facishare.marketing.api.service.wxcoupon.PublicCouponService;
import com.facishare.marketing.api.vo.wxcoupon.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.coupon.PublicCouponManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public  class PublicCouponServiceTest extends BaseTest {

    @Autowired
    private PublicCouponService publicCouponService;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private PublicCouponManager publicCouponManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Test
    public void createPublicCoupon(){
        CreateWxCouponVO vo = new CreateWxCouponVO();
        vo.setMarketingEventId("654c4e68d89b440001dadae2");
        vo.setCreateCouponType(1);
        vo.setPricePolicyId("654c3f47d89b440001d42163");
        vo.setMaxCoupons(10);
        vo.setMaxCouponsPerUser(100);
        vo.setIsMember(2);
        vo.setStatus(1);
        vo.setEa("89875");
        vo.setStockName("升级后N方案");
        CreateWxCouponVO.AccountVisibilityVO vistibilityVo = new CreateWxCouponVO.AccountVisibilityVO();
        vistibilityVo.setType("FIXED");
        vistibilityVo.setValue("[{\"account_id\":\"65484e1b93448b0001eb47a4\",\"account_id__r\":\"客户N端\"}]");
        vo.setAccountVisibilityVO(vistibilityVo);
        vo.setIsParticipate(1);
        vo.setScene(2);
        Result<CreateCouponResult> publicCoupon = publicCouponService.createPublicCoupon(vo);
        System.out.println(publicCoupon);
    }

    @Test
    public void queryList(){
        QueryCouponListVo vo = new QueryCouponListVo();
        //vo.setMarketingEventId("654c4e68d89b440001dadae2");
        vo.setPageSize(10);
        vo.setPageNum(1);
        Result<PageResult<CouponResult>> pageResultResult = publicCouponService.queryCouponList("89875", vo);
        System.out.println(pageResultResult);

//        PaasQueryFilterArg arg = new PaasQueryFilterArg();
//        arg.setObjectAPIName("CouponObj");
//        PaasQueryArg query = new PaasQueryArg(0,500);
//        arg.setQuery(query);
//        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3("89877", -10000, arg, null, 500);
//        System.out.println(objectDataInnerPage);
    }

    @Test
    public void queryCouponDetail(){
        QueryCouponDetailVo vo = new QueryCouponDetailVo();
        vo.setObjectId("3a90ea5009f844f0893b3571910e3081");
        Result<CouponResult> couponResultResult = publicCouponService.queryCouponDetail("89875", vo);
        System.out.println(couponResultResult);
    }

    @Test
    public void queryUserCouponListToCustomer(){
        QueryUserCouponListVo vo = new QueryUserCouponListVo();
        vo.setERUpstreamEa("89829");
        vo.setEROuterTenantId("300111650");
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setEROuterUid("300398205");
        vo.setStatus("1");
        Result<PageResult<UserCouponListResult>> pageResultResult = publicCouponService.queryUserCouponListToCustomer(vo);
        System.out.println(pageResultResult);
    }

    @Test
    public void queryCouponStockStatistic(){
        Result<CouponDetailStatisticResult> stockStatistic = publicCouponService.queryCouponStockStatistic("f8bd863940a74c39bc6aa78a475e9efe", "89877");
        System.out.println(stockStatistic);
    }

    @Test
    public void checkCustomerPendingCoupon(){
        CheckCustomerPendingCouponVO vo = new CheckCustomerPendingCouponVO();
        vo.setEa("89875");
        vo.setCustomerId("65484e1b93448b0001eb47a4");
        Result<Boolean> booleanResult = publicCouponService.checkCustomerPendingCoupon(vo);
        System.out.println(booleanResult);
    }

    @Test
    public void queryCustomerPendingCouponList(){
        QueryCustomerPendingCouponVO vo = new QueryCustomerPendingCouponVO();
        vo.setCustomerId("65484e1b93448b0001eb47a4");
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setEa("89875");
        Result<PageResult<QueryCustomerCouponResult>> pageResultResult = publicCouponService.queryCustomerPendingCouponList(vo);
        System.out.println(pageResultResult);
    }

    @Test
    public void receiveSingleCoupon(){
        ReceiveSingleCouponVO vo = new ReceiveSingleCouponVO();
        vo.setCustomerId("65484f3a93448b0001ebd982");
        vo.setEa("89877");
        vo.setObjectDataId("654c524b0677b40001418056");
        Result<String> stringResult = publicCouponService.receiveSingleCoupon(vo);
        System.out.println(stringResult);
    }

    @Test
    public void getEnterpriseName() {
        Map<String, String> stringStringMap = publicCouponManager.queryBelongCompanyName(Sets.newHashSet("zhenju0111", "89877"));
        System.out.println(stringStringMap);
    }

    @Test
    public void queryFxList(){
        QueryFxCouponVO vo = new QueryFxCouponVO();
        vo.setEa("89877");
        vo.setPageSize(10);
        vo.setPageNum(1);
        Result<PageResult<CouponResult>> pageResultResult = publicCouponService.queryFxCouponList(vo);
        System.out.println(pageResultResult);
    }

    @Test
    public void importSendScope(){
        String voFile = "A_202404_10_f71ecf51dbd6449b83cb7d40fe6c636f";
        publicCouponManager.importSendScope("89875", "654c4e68d89b440001dadae2", "xlsx", voFile);
    }

    @Test
    public void getFilePath(){
        String urlByPath = fileV2Manager.getUrlByPath("89875", "A_202404_10_341419b9cc954a9b898d299ebbae6154.xlsx");
        System.out.println(urlByPath);
    }

    @Test
    public void handleRangeCoupon(){
        publicCouponManager.handleCouponReceiveRangeChange("89877","656ee2a293dfb500012e4d11",null,"u");
    }

    @Test
    public void getOwnerCoupon(){
        Map<String, Integer> enterpriseRelationOwner = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList("89829", "89912", "90076"), "89875");
        System.out.println(enterpriseRelationOwner);
    }

    @Test
    public void changeOwner(){
        boolean couponInstanceObj = publicCouponManager.updatePublicObjOwner("89829", 1005, "CouponInstanceObj", "66ac404b7da2360008d20b6e");
        System.out.println(couponInstanceObj);
    }

    @Test
    public void queryAccountOwner(){
        Integer accountOwner = publicCouponManager.getAccountOwner("89877", "89875");
        System.out.println(accountOwner);
    }

    @Test
    public void handleCouponReceiveRangeJob(){
        publicCouponManager.handleCouponReceiveRangeJob("89875");
    }
}
