/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager.ocpc;

import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.baidu.BaiduHttpManager;
import com.facishare.marketing.provider.baidu.GetOcpcTargetPackageRequest;
import com.facishare.marketing.provider.baidu.GetOcpcTargetPackageResult;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.mq.handler.dto.CrmEventDTO;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class AdOCPCUploadManagerTest extends BaseTest {

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private BaiduHttpManager baiduHttpManager;

    @Test
    public void testInsert() {
        adOCPCUploadManager.initRule("zhenju0111");
    }

    @Test
    public void getDescribe() {

        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult =
                objectDescribeService.getDescribe(new com.fxiaoke.crmrestapi.common.data.HeaderObj(74164, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CRM_LEAD.getName());
        log.info("api:[{}], data:[{}]", CrmObjectApiNameEnum.CRM_LEAD.getName(), getDescribeResultResult.getData());
        getDescribeResultResult = objectDescribeService.getDescribe(new com.fxiaoke.crmrestapi.common.data.HeaderObj(74164, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.QUOTE_OBJ.getName());
        log.info("api:[{}], data:[{}]", CrmObjectApiNameEnum.QUOTE_OBJ.getName(), getDescribeResultResult.getData());

        getDescribeResultResult =
                objectDescribeService.getDescribe(new com.fxiaoke.crmrestapi.common.data.HeaderObj(74164, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.SALES_ORDER.getName());
        log.info("api:[{}], data:[{}]", CrmObjectApiNameEnum.SALES_ORDER.getName(), getDescribeResultResult.getData());

        getDescribeResultResult =
                objectDescribeService.getDescribe(new com.fxiaoke.crmrestapi.common.data.HeaderObj(74164, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CONTRACT_OBJ.getName());
        log.info("api:[{}], data:[{}]", CrmObjectApiNameEnum.CONTRACT_OBJ.getName(), getDescribeResultResult.getData());

    }


    @Test
    public void getOrCreateLandingObjIdTest() {
        String url = "https://www.fxiaoke.com/ec/h5-landing/release/form.html?formId=1d22b9a17f5bf4c16be0565534c674d8c&objectId=ed0ff0f242714e18a75c88a95ca105c3&objectType=28&sourceType=7&needReport=true";
        adOCPCUploadManager.getOrCreateLandingObjId("74164", url, url, "6335173cd5e7e00001f449b0");
    }


    @Test
    public void bindCrmLandingPageToUserMarketingTest() {
        CrmEventDTO.CrmEvent crmEvent = new CrmEventDTO.CrmEvent();
        List<CrmEventDTO.Body> bodyList = Lists.newArrayList();
        CrmEventDTO.Body body = new CrmEventDTO.Body();
        body.setTriggerType("i");
        body.setObjectId("64337a13c3dbba0001a254b9");
        body.setEntityId(CrmObjectApiNameEnum.CRM_LEAD.getName());
        bodyList.add(body);
        crmEvent.setBody(bodyList);
        ObjectData objectData = crmMetadataManager.getById("83668", -10000, CrmObjectApiNameEnum.CRM_LEAD.getName(), "64337a13c3dbba0001a254b9");
        Map<String, ObjectData> map = new HashMap<>();
        map.put(objectData.getId(), objectData);
        adOCPCUploadManager.bindCrmLandingPageToUserMarketing("83668", crmEvent);
    }

    @Test
    public void getOcpcTargetPackageListTest() {
        String userName = "易动纷享01";
        String token = "eyJhbGciOiJIUzM4NCJ9.eyJzdWIiOiJhY2MiLCJhdWQiOiLnurfkuqvokKXplIDpgJoiLCJ1aWQiOjQxNzg1MTE3LCJhcHBJZCI6IjllNzY4OGZkMjg5ZGVkZGM2YzQwMzFmYTJlOTVmNDNjIiwiaXNzIjoi5ZWG5Lia5byA5Y-R6ICF5Lit5b-DIiwicGxhdGZvcm1JZCI6IjQ5NjAzNDU5NjU5NTg1NjE3OTQiLCJleHAiOjE3MTEzOTgyNTQsImp0aSI6IjgyMjQ0NDMzMjA0Nzk1MzEwMzUifQ.sty80m20tjE0gdZTWZEmzJAM4G5CGzDIWvbfxJ7Ala4ZWzTENW76AYmPABKbr48X";
        GetOcpcTargetPackageRequest arg = new GetOcpcTargetPackageRequest();
        arg.setLevel(2);
        arg.setTargetPackageTypeFields(Lists.newArrayList("targetPackageId", "targetPackageName",
                "ocpcBid", "ocpcBidType", "scope", "assistTransTypes", "packageStatus", "ocpcDeepCpa"));
        List<GetOcpcTargetPackageResult> result = baiduHttpManager.getOcpcTargetPackageList(userName, token, arg);
        log.info("结果 ： {}", JsonUtil.toJson(result));
    }
}
