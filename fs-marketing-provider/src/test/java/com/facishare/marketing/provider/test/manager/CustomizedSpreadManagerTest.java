package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.whatsapp.SendTypeEnum;
import com.facishare.marketing.provider.manager.customizedSpread.CustomizedSpreadManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class CustomizedSpreadManagerTest extends BaseTest {

    @Autowired
    private CustomizedSpreadManager customizedSpreadManager;

    @Test
    public void testDoAddAction() {
        AddMarketingActivityArg arg = new AddMarketingActivityArg();
        arg.setMarketingEventId("682ecbdd469c180007710d62");
        arg.setSpreadType(14);
        AddMarketingActivityArg.CustomizedSpreadArg customizedSpreadArg = new AddMarketingActivityArg.CustomizedSpreadArg();
        customizedSpreadArg.setCustomizedSpreadChannelId("be09d8924af24a9b9f5ffbe9d9e674a5");
        customizedSpreadArg.setSendType(SendTypeEnum.IMMEDIATELY.getValue());
        customizedSpreadArg.setSendTime(1749035266000L);
        customizedSpreadArg.setObjectId("0cfeba15f7514eebaaf87c4a27cc4280");
        customizedSpreadArg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
        customizedSpreadArg.setMarketingUserGroupIds(Lists.newArrayList("ec0df2fce0644dd5b6e8095b0daacadc"));
        arg.setCustomizedSpreadArg(customizedSpreadArg);
        customizedSpreadManager.doAddAction("88146", 1000, arg);
    }

    @Test
    public void testSendMessageByTaskId() {
        customizedSpreadManager.sendMessageByTaskId("88146", "2a409eb49836473d9a4781e8cc49818c");
    }
}
