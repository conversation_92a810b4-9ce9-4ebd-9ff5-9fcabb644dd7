/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.dao;

import com.facishare.marketing.provider.dao.ObjectDescriptionDAO;
import com.facishare.marketing.provider.entity.ObjectDescriptionEntity;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class ObjectDescriptionDaoTest extends BaseTest {
    @Autowired
    private ObjectDescriptionDAO descriptionDAO;

    @Test
    public void add() {
        ObjectDescriptionEntity entity = new ObjectDescriptionEntity();
        entity.setButtonContent("你好");
        entity.setApiName("try_out_product_form");
        entity.setButtonText("按钮");
        entity.setEa("123");
        entity.setSubmitFollowingActionSelect(0);
        entity.setSuccessPromptMsg("222提交成功提示语");
        entity.setId("d3d9446802a43259b55d38e6d163e821");
        descriptionDAO.add(entity);
    }
}
