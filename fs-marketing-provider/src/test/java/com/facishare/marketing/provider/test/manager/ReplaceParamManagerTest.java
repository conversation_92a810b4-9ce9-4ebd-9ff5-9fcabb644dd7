/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dto.EnvContext;
import com.facishare.marketing.provider.dto.UserContext;
import com.facishare.marketing.provider.manager.ReplaceParamManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2021-04-28.
 */

public class ReplaceParamManagerTest extends BaseTest {
	@Autowired
	private ReplaceParamManager replaceParamManager;
	
	@Test
	public void testReplaceParamsByContexts(){
		EnvContext envContext = new EnvContext();
		envContext.setConferenceId("3f6608aadcdb4effa32724fcc08b4e38");
		envContext.setMarketingEventId("604096665beb22000141f459");
		UserContext uc = new UserContext();
		uc.setCampaignId("3ad6b8b464684cee8b9b92c2743d64f0");
		uc.setMarketingUserId("cd2e16148b5e440cac5f38d137ab4cbc");
		Map<UserContext, String> replacement = replaceParamManager.replaceParamsByContexts("74164",
			"${LeadsObj.name}${LeadsObj.marketing_event_id__r}" +
				"${MarketingUser.name} ${MarketingUser.phone} ${MarketingUser.email}\n" +
				"${Campaign.ticketCode} ${Campaign.ticketUrl} ${Campaign.enrollName} ${Campaign.enrollPhone} ${Campaign.enrollEmail}" +
				"${Live.title?直播}${Live.startTime ? 开始时间 吧}${Live.endTime}${Live.lecture${Live.viewUrl} " +
				"${Conference.title}${Conference.startTime}${Conference.endTime}${Conference.location}",
			envContext, Lists.newArrayList(uc));
		System.out.println(replacement);
	}
}
