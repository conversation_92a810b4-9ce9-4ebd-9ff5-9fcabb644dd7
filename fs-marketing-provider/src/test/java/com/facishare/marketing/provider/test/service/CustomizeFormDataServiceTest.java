/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.customizeFormData.CountUnSaveEnrollDataArg;
import com.facishare.marketing.api.arg.customizeFormData.GetBingFormByObjectArg;
import com.facishare.marketing.api.arg.customizeFormData.GetFormImportTemplateArg;
import com.facishare.marketing.api.arg.customizeFormData.ImportFormEnrollDataArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.qywx.customizeFormData.GetAreaDataArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.customizeFormData.GetFormImportTemplateResult;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.enums.CustomizeFormDataStatusEnum;
import com.facishare.marketing.common.enums.CustomizeFormDataSuccessActionEnum;
import com.facishare.marketing.common.enums.ExcelConfigEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.QueryFormUserDataTypeEnum;
import com.facishare.marketing.common.enums.QueryMultipleFormUserDataTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.open.emailproxy.common.thread.ThreadUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created  By zhoux 2019/04/04
 **/
//@Ignore
@Slf4j
public class CustomizeFormDataServiceTest extends BaseTest {
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Test
    public void exportFormUserDataImg() {
        customizeFormDataService.exportFormUserDataImg("6a99a90398e6435e8ccc5b1a1191b850");
    }

    @Test
    public void addCustomizeFormData() {
        AddCustomizeFormDataArg addCustomizeFormDataArg = new AddCustomizeFormDataArg();
        FormHeadSetting formHeadSetting = new FormHeadSetting();
        formHeadSetting.setTitle("模板头部标题");
        formHeadSetting.setIntroduce("模板头部介绍");
        formHeadSetting.setHeadPhotoPath(Lists.newArrayList("模板头部图片1", "模板头部图片2"));
        addCustomizeFormDataArg.setFormHeadSetting(formHeadSetting);
        FieldInfoList fieldInfoList = new FieldInfoList();
        FieldInfo fieldInfo1 = new FieldInfo();
        fieldInfo1.setApiName("apiName1");
        fieldInfo1.setIsRequired(true);
        fieldInfo1.setLabel("label1");
        fieldInfo1.setHelpText("helpText1");
        FieldInfo fieldInfo2 = new FieldInfo();
        fieldInfo2.setApiName("apiName2");
        fieldInfo2.setIsRequired(false);
        fieldInfo2.setLabel("label2");
        fieldInfo2.setHelpText("helpText2");
        fieldInfoList.add(fieldInfo1);
        fieldInfoList.add(fieldInfo2);
        addCustomizeFormDataArg.setFormBodySetting(fieldInfoList);
        FormSuccessSetting formSuccessSetting = new FormSuccessSetting();
        formSuccessSetting.setAfterSuccessAction(CustomizeFormDataSuccessActionEnum.ADVISORY.getValue());
        formSuccessSetting.setButtonName("成功按钮");
        formSuccessSetting.setFilePath(Lists.newArrayList("模板文件1", "模板文件2"));
        addCustomizeFormDataArg.setFormSuccessSetting(formSuccessSetting);
        FormMoreSetting formMoreSetting = new FormMoreSetting();
        formMoreSetting.setFillInOnce(true);
        formMoreSetting.setChargePerson(true);
        addCustomizeFormDataArg.setFormMoreSetting(formMoreSetting);
        FieldMappingResult fieldMappingResult1 = new FieldMappingResult();
        fieldMappingResult1.setDefaultValue("KKK");
        fieldMappingResult1.setMankeepFieldName("name");
        fieldMappingResult1.setCrmFieldName("crm_name");
        fieldMappingResult1.setModifiable(false);
        FieldMappingResult fieldMappingResult2 = new FieldMappingResult();
        fieldMappingResult2.setDefaultValue("KKK");
        fieldMappingResult2.setMankeepFieldName("age");
        fieldMappingResult2.setCrmFieldName("crm_age");
        fieldMappingResult2.setModifiable(false);
        List<FieldMappingResult> fieldMappingResultList = Lists.newArrayList(fieldMappingResult1, fieldMappingResult2);
        addCustomizeFormDataArg.setCrmFormFieldMap(fieldMappingResultList);
        addCustomizeFormDataArg.setFsUserId(1024);
        addCustomizeFormDataArg.setEa("2");
        Result<AddCustomizeFormDataResult> addCustomizeFormDataResultResult = customizeFormDataService.addCustomizeFormData(addCustomizeFormDataArg, null,true);
        Assert.assertNotEquals(addCustomizeFormDataResultResult, null);
    }

    @Test
    public void updateCustomizeFormDataDetail() {
        UpdateCustomizeFormDataDetailArg updateCustomizeFormDataDetailArg = new UpdateCustomizeFormDataDetailArg();
        updateCustomizeFormDataDetailArg.setId("c31fe7f593da433fa8296ae3b6aec2aa");
        FormHeadSetting formHeadSetting = new FormHeadSetting();
        formHeadSetting.setTitle("头部标题2");
        formHeadSetting.setIntroduce("头部介绍2");
        formHeadSetting.setHeadPhotoPath(Lists.newArrayList("A_201904_08_3d9f314b8c0047ee946557f3da30f040.jpg", "A_201904_08_3d9f314b8c0047ee946557f3da30f040.jpg"));
        updateCustomizeFormDataDetailArg.setFormHeadSetting(formHeadSetting);
        FieldInfoList fieldInfoList = new FieldInfoList();
        FieldInfo fieldInfo1 = new FieldInfo();
        fieldInfo1.setApiName("apiName1");
        fieldInfo1.setIsRequired(true);
        fieldInfo1.setLabel("label1");
        fieldInfo1.setHelpText("helpText1");
        FieldInfo fieldInfo2 = new FieldInfo();
        fieldInfo2.setApiName("apiName2");
        fieldInfo2.setIsRequired(false);
        fieldInfo2.setLabel("label2");
        fieldInfo2.setHelpText("helpText2");
        fieldInfoList.add(fieldInfo1);
        fieldInfoList.add(fieldInfo2);
        updateCustomizeFormDataDetailArg.setFormBodySetting(fieldInfoList);
        FormSuccessSetting formSuccessSetting = new FormSuccessSetting();
        formSuccessSetting.setAfterSuccessAction(CustomizeFormDataSuccessActionEnum.ADVISORY.getValue());
        formSuccessSetting.setButtonName("成功按钮");
        formSuccessSetting.setFilePath(Lists.newArrayList("文件3", "文件4"));
        updateCustomizeFormDataDetailArg.setFormSuccessSetting(formSuccessSetting);
        FormMoreSetting formMoreSetting = new FormMoreSetting();
        formMoreSetting.setFillInOnce(true);
        formMoreSetting.setChargePerson(true);
        updateCustomizeFormDataDetailArg.setFormMoreSetting(formMoreSetting);
        FieldMappingResult fieldMappingResult1 = new FieldMappingResult();
        fieldMappingResult1.setDefaultValue("KKK");
        fieldMappingResult1.setMankeepFieldName("name");
        fieldMappingResult1.setCrmFieldName("crm_name");
        fieldMappingResult1.setModifiable(false);
        FieldMappingResult fieldMappingResult2 = new FieldMappingResult();
        fieldMappingResult2.setDefaultValue("KKK");
        fieldMappingResult2.setMankeepFieldName("age");
        fieldMappingResult2.setCrmFieldName("crm_age");
        fieldMappingResult2.setModifiable(false);
        FieldMappingResult fieldMappingResult3 = new FieldMappingResult();
        fieldMappingResult3.setDefaultValue("zzz");
        fieldMappingResult3.setMankeepFieldName("count");
        fieldMappingResult3.setCrmFieldName("crm_count");
        fieldMappingResult3.setModifiable(false);
        List<FieldMappingResult> fieldMappingResultList = Lists.newArrayList(fieldMappingResult1, fieldMappingResult2, fieldMappingResult3);
        updateCustomizeFormDataDetailArg.setCrmFormFieldMap(fieldMappingResultList);
        updateCustomizeFormDataDetailArg.setFsUserId(1024);
        updateCustomizeFormDataDetailArg.setEa("fktest");
        Result<UpdateCustomizeFormDataDetailResult> updateCustomizeFormDataDetailResultResult = customizeFormDataService.updateCustomizeFormDataDetail(updateCustomizeFormDataDetailArg);
        Assert.assertNotEquals(updateCustomizeFormDataDetailResultResult, null);
    }

    @Test
    public void updateCustomizeFormDataStatus() {
        UpdateCustomizeFormDataStatusArg arg = new UpdateCustomizeFormDataStatusArg();
        arg.setId("c31fe7f593da433fa8296ae3b6aec2aa");
        arg.setFsUser(1024);
        arg.setEa("fktest");
        arg.setStatus(CustomizeFormDataStatusEnum.DISABLE.getValue());
        Result result = customizeFormDataService.updateCustomizeFormDataStatus(arg);
        Assert.assertNotEquals(result, null);
    }

    @Test
    public void queryCustomizeFormData() {
        QueryCustomizeFormDataArg arg = new QueryCustomizeFormDataArg();
        arg.setEa("74164");
        arg.setPageNum(1);
        arg.setPageSize(10);
        Result<PageResult<QueryCustomizeFormDataResult>> result = customizeFormDataService.queryCustomizeFormData(arg);
        Assert.assertNotEquals(result, null);
    }

    @Test
    public void getCustomizeTemplateFormData() {
        Result<List<CustomizeFormDataDetailResult>> result = customizeFormDataService.getCustomizeTemplateFormData();
        Assert.assertNotEquals(result, null);
    }

    @Test
    public void getCustomizeFormDataById() {
        GetCustomizeFormDataByIdArg getCustomizeFormDataByIdArg = new GetCustomizeFormDataByIdArg();
        getCustomizeFormDataByIdArg.setId("9695c94197e1438e8dab0587e3c04a76");
        Result<CustomizeFormDataDetailResult> result = customizeFormDataService.getCustomizeFormDataById(getCustomizeFormDataByIdArg);
        Assert.assertNotEquals(result, null);
    }

    @Test
    public void customizeFormDataBindObject() {
        CustomizeFormDataBindObjectArg customizeFormDataBindObjectArg = new CustomizeFormDataBindObjectArg();
        customizeFormDataBindObjectArg.setFsUserId(1000);
        customizeFormDataBindObjectArg.setFormId("c31fe7f593da433fa8296ae3b6aec2aa");
        customizeFormDataBindObjectArg.setObjectId("c31fe7f593");
        customizeFormDataBindObjectArg.setObjectType(1);
        customizeFormDataBindObjectArg.setEa("fstest");
        Result result = customizeFormDataService.customizeFormDataBindObject(customizeFormDataBindObjectArg);
        Assert.assertNotEquals(result, null);
    }


   /* {"startDate":1601568000000,"endDate":1602259199000,"objectId":"50506d7ea47748fc9e266e897e6ad536","objectType":26,"pageNum":1,"pageSize":10,"needMarketingEventAndActivityDetail":true,"formUsage":1,"type":1}*/



   /* marketingActivityId: null
    marketingEventId: "605ef8bbc97e050025a871e3"
    objectId: "67d508143f9c422a87f14893e338d4f5"
    objectType: 16
    pageNum: 1
    pageSize: 5
    type: 4*/
    @Test
    public void queryFormUserData() {
        QueryFormUserDataArg arg = new QueryFormUserDataArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setEa("88146");
        arg.setFsUserId(1000);
//        arg.setMarketingActivityId("64a4d0eab11b890001f4e57a");
        arg.setMarketingActivityId("6822f806bb1c81000118a1ab");
        arg.setMarketingEventId("6822f7e419968c00076e9f44");
        arg.setObjectId("d7a352e338f14f0098c6f94c778b39e8");
        arg.setObjectType(16);
        arg.setType(QueryFormUserDataTypeEnum.MARKETING_EVENT.getType());
        Result<PageResult<QueryFormUserDataResult>> result = customizeFormDataService.queryFormUserData(arg);
        log.info("结果：{}", JSON.toJSONString(result));
    }

    @Test
    public void queryFormUserDataForEmployees() {
        queryFormUserDataForIdsArg arg = new queryFormUserDataForIdsArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setIds(Lists.newArrayList("d1530e9d14c94d66817533135749c4db","132d396bf86f41c0a06aed6207de166e","63f15edefeeb4078bea6d6eec4a45197","80e146e2fd5d4122b376d1c01a41845c"));
        Result<PageResult<QueryFormUserDataResult>> pageResultResult = customizeFormDataService.queryFormUserDataForIds(arg);
       // arg.setKeyword("dad");
        pageResultResult = customizeFormDataService.queryFormUserDataForIds(arg);
        System.out.println(pageResultResult);
    }
    @Test
    public void exportEnrollsData() throws InterruptedException {
        ExportEnrollsDataArg arg = new ExportEnrollsDataArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);
     //   arg.setObjectId("24e43201386842b9b0d90237ef231210");
     //   arg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
        arg.setType( QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType());
//        arg.setSourceType(CustomizeFormDataUserSourceTypeEnum.OFFICIAL_WEBSITE.getType());
        arg.setMarketingActivityId("677dee67f45a1900016575ed");
        //arg.setType(QueryFormUserDataTypeEnum.MARKETING_EVENT.getType());
        Result<ExportEnrollsDataResult> result = customizeFormDataService.exportEnrollsData(arg);
    //    Thread.sleep(10000L);
    }


    @Test
    public void customizeFormDataShowSetting() {
        CustomizeFormDataShowSettingArg customizeFormDataShowSettingArg = new CustomizeFormDataShowSettingArg();
        customizeFormDataShowSettingArg.setFormId("674180efa4d14b3c9eb5223829b050b2");
        customizeFormDataShowSettingArg.setObjectId("1bcd4a248a8e4d29818c60b7dc1508d5");
        customizeFormDataShowSettingArg.setObjectType(13);
        customizeFormDataShowSettingArg.setFingerPrint("7f0598b4c17c0c89871b73102533496e_10.113.19.2");
        Result<CustomizeFormDataShowSettingResult> result = customizeFormDataService.customizeFormDataShowSetting(customizeFormDataShowSettingArg);
        Assert.assertNotEquals(result, null);
    }


    @Test
    public void checkAddLeadsObjectAuth() {
        boolean result = customizeFormDataManager.checkAddLeadsObjectAuth("55732", 1048);
        Assert.assertEquals(result, true);
    }

    @Test
    public void getCustomizeFormDataQrCode() {
        Result<GetCustomizeFormDataQrCodeResult> result = customizeFormDataService.getCustomizeFormDataQrCode("6acfd011c5ef409089665586aa08059b", "5d481f4c830bdb19bea87417", "2");
        Assert.assertNotEquals(result, null);
    }

    @Test
    public void queryMultipleFormUserData() {
        QueryMultipleFormUserDataArg arg = new QueryMultipleFormUserDataArg();
        arg.setSourceType(QueryMultipleFormUserDataTypeEnum.OFFICIAL_WEBSITE.getType());
        arg.setSourceId("0e7dda30179d4427aa450425b9f5d96f");
        arg.setPageNum(3);
        arg.setPageSize(20);
        customizeFormDataService.queryMultipleFormUserData(arg);
    }

    @Test
    public void queryMultipleFormUserData3(){
        QueryMultipleFormUserDataArg arg = new QueryMultipleFormUserDataArg();
        arg.setSourceType(QueryMultipleFormUserDataTypeEnum.OFFICIAL_WEBSITE.getType());
        arg.setSourceId("0e7dda30179d4427aa450425b9f5d96f");
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setMobile("15");
        Result<PageResult<QueryFormUserDataResult>> pageResultResult = customizeFormDataService.queryMultipleFormUserData(arg);
        System.out.println("res: " + pageResultResult);
    }

    @Test
    public void queryMultipleFormUserData2() {
        QueryMultipleFormUserDataArg arg = new QueryMultipleFormUserDataArg();
        arg.setSourceType(QueryMultipleFormUserDataTypeEnum.MARKETING_EVENT.getType());
        arg.setSourceId("5db2917cf85484000164955f");
        arg.setKeyword("血红");
        arg.setViewLiveStatus(1);
        arg.setPageNum(1);
        arg.setPageSize(10);
        Result<PageResult<QueryFormUserDataResult>>  result = customizeFormDataService.queryMultipleFormUserData(arg);
        System.out.print("queryMultipleFormUserData2 result:"+result);
    }

    @Test
    public void exportMultipleFormEnrollsData() {
        ExportMultipleFormEnrollsDataArg arg = new ExportMultipleFormEnrollsDataArg();
        arg.setSourceType(QueryMultipleFormUserDataTypeEnum.OFFICIAL_WEBSITE.getType());
        arg.setSourceId("0e7dda30179d4427aa450425b9f5d96f");
        arg.setEa("74164");
        arg.setFsUserId(1177);
        customizeFormDataService.exportMultipleFormEnrollsData(arg);
  //      ThreadUtil.sleepIngore(10000L);
    }

    @Test
    public void getAreaData() {
        GetAreaDataArg arg = new GetAreaDataArg();
        Result<AreaContainerResult>  resultResult = customizeFormDataService.getAreaData(arg);
        log.info("resutl : {}", JSON.toJSONString(resultResult));
    }

    @Test
    public void executeEnrollCustomizeFunction() {
        ExecuteEnrollCustomizeFunctionArg executeEnrollCustomizeFunctionArg = new ExecuteEnrollCustomizeFunctionArg();
        executeEnrollCustomizeFunctionArg.setApiName("func_M6z8a__c");
        executeEnrollCustomizeFunctionArg.setEnrollId("3d03147e65d543d4a091b88ec075c91a");
        Map<String, Object> map = Maps.newHashMap();
        map.put("memberId", "5fa8dc7b5a8cb100012b86f3");
        map.put("type", "1");
        map.put("siteId", "48b9ae67d078438a8c35775facdd7d13");
        executeEnrollCustomizeFunctionArg.setAdditionalMsg(map);
        customizeFormDataService.executeEnrollCustomizeFunction(executeEnrollCustomizeFunctionArg);
    }

    @Test
    public void copyCustomizeForm() {
        customizeFormDataService.copyCustomizeForm("68f63c20523b4744ba43b4eba8c80927", "hhhhhh-副本5", "74164", 1177);
    }

    @Test
    public void buildCrmObjectByEnrollData() {
        BuildCrmObjectByEnrollDataArg buildCrmObjectByEnrollDataArg = new BuildCrmObjectByEnrollDataArg();
        buildCrmObjectByEnrollDataArg.setFsUserId(1000);
        buildCrmObjectByEnrollDataArg.setEa("74164");
        buildCrmObjectByEnrollDataArg.setEnrollId("881e6175fb5146b1a804a7577d23b9c1");
        customizeFormDataService.buildCrmObjectByEnrollData(buildCrmObjectByEnrollDataArg);
    }

    @Test
    public void bindEnrollDataAndCrmObject() {
        BindEnrollDataAndCrmObjectArg bindEnrollDataAndCrmObjectArg = new BindEnrollDataAndCrmObjectArg();
        bindEnrollDataAndCrmObjectArg.setEa("74164");
        bindEnrollDataAndCrmObjectArg.setFsUserId(1000);
        bindEnrollDataAndCrmObjectArg.setEnrollId("a15eed7592d0498f9062d7f3db6f1def");
        bindEnrollDataAndCrmObjectArg.setObjectApiName("AccountObj");
        bindEnrollDataAndCrmObjectArg.setObjectId("5f6962fda488540001008f90");
        customizeFormDataService.bindEnrollDataAndCrmObject(bindEnrollDataAndCrmObjectArg);
    }

    @Test
    public void getFormImportTemplate() throws IOException {
        GetFormImportTemplateArg getFormImportTemplateArg = new GetFormImportTemplateArg();
        getFormImportTemplateArg.setEa("74164");
        getFormImportTemplateArg.setFsUserId(1000);
        getFormImportTemplateArg.setFormId("9583cb8a0863455080a02f069b203fc3");
        Result<GetFormImportTemplateResult> formImportTemplate = customizeFormDataService.getFormImportTemplate(getFormImportTemplateArg);
        GetFormImportTemplateResult result = formImportTemplate.getData();
        StringBuilder filename = new StringBuilder(result.getFileName()).append("导入模板" + ".xlsx");
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename.toString());
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, result.getTitleList(), result.getDataList());
        ExcelUtil.fillScopeContent(xssfWorkbook, xssfSheet, result.getScopeContainerList());
        File file =new File("D:\\test.xlsx");
        OutputStream out=new FileOutputStream(file,true);
        xssfWorkbook.write(out);
        out.flush();
        out.close();
    }

    @Test
    public void importFormEnrollData() {
        ImportFormEnrollDataArg importFormEnrollDataArg = new ImportFormEnrollDataArg();
        importFormEnrollDataArg.setEa("88146");
        importFormEnrollDataArg.setFsUserId(1000);
        importFormEnrollDataArg.setFormId("df701b849a68445a8cdbb92b3aa3c369");
        importFormEnrollDataArg.setObjectId("7aa3f4331bd8407c9b138b227646faae");
        importFormEnrollDataArg.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
        importFormEnrollDataArg.setTaPath("TA_06c3de80849c4f09b6636ce353112d38.xlsx");
        customizeFormDataService.importFormEnrollData(importFormEnrollDataArg);
    }

    @Test
    public void getBingFormByObject() {
        GetBingFormByObjectArg arg = new GetBingFormByObjectArg();
        arg.setEa("74164");
        arg.setFsUserId(1135);
        arg.setObjectId("16cd20564e9d48feb28b5b694cd94722");
        arg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
        customizeFormDataService.getBingFormByObject(arg);
    }

    @Test
    public void jsonAdd() {
        String json = "{\"formHeadSetting\":{\"name\":\"模板产生的表单\",\"title\":\"\",\"shareDsc\":\"\",\"sharePicUrl\":\"\",\"shareTitle\":\"\"},\"formMoreSetting\":{\"synchronousCRM\":false,\"enrollLimit\":false,\"fillInOnce\":false,\"submitJumpType\":\"content\",\"conferenceId\":\"\",\"conferenceDetails\":\"\"},\"formSuccessSetting\":{\"afterSuccessAction\":99,\"payDescription\":\"\",\"totalFee\":0},\"type\":1,\"id\":null,\"formBodySetting\":[{\"id\":\"phone\",\"apiName\":\"phone\",\"label\":\"手机号\",\"type\":\"phone_number\",\"helpText\":\"请输入手机号\",\"isRequired\":true,\"isVerify\":false},{\"id\":\"name\",\"apiName\":\"name\",\"label\":\"姓名\",\"type\":\"text\",\"helpText\":\"请输入姓名\",\"isRequired\":true,\"isVerify\":false}],\"formUsage\":1,\"checkMapping\":false,\"contentStyle\":\"{\\\"id\\\":\\\"\\\",\\\"type\\\":\\\"page\\\",\\\"name\\\":\\\"\\\",\\\"title\\\":\\\"\\\",\\\"version\\\":\\\"4.2.0-12\\\",\\\"cover\\\":\\\"\\\",\\\"shareOpts\\\":{\\\"title\\\":\\\"\\\",\\\"desc\\\":\\\"\\\",\\\"link\\\":\\\"\\\",\\\"imgUrl\\\":\\\"\\\"},\\\"style\\\":{\\\"width\\\":375,\\\"backgroundColor\\\":\\\"#fff\\\",\\\"backgroundSize\\\":\\\"100%\\\",\\\"backgroundRepeat\\\":\\\"no-repeat\\\",\\\"backgroundImage\\\":\\\"\\\"},\\\"backgroundFillType\\\":\\\"filling\\\",\\\"dataSourceAction\\\":{},\\\"components\\\":[{\\\"id\\\":\\\"1626762560142\\\",\\\"name\\\":\\\"表单\\\",\\\"key\\\":\\\"form-container\\\",\\\"type\\\":\\\"container\\\",\\\"typeValue\\\":\\\"form\\\",\\\"components\\\":[{\\\"id\\\":\\\"1626762560143\\\",\\\"label\\\":\\\"提交\\\",\\\"name\\\":\\\"提交\\\",\\\"tip\\\":\\\"提交成功\\\",\\\"type\\\":\\\"button\\\",\\\"position\\\":\\\"none\\\",\\\"required\\\":false,\\\"isFormComp\\\":true,\\\"wrapStyle\\\":{\\\"position\\\":\\\"none\\\",\\\"background\\\":\\\"rgba(255,255,255,.9)\\\"},\\\"style\\\":{\\\"height\\\":45,\\\"width\\\":345,\\\"fontSize\\\":16,\\\"background\\\":\\\"#409EFF\\\",\\\"borderRadius\\\":0,\\\"color\\\":\\\"#fff\\\",\\\"letterSpacing\\\":0,\\\"lineHeight\\\":45,\\\"textAlign\\\":\\\"center\\\",\\\"margin\\\":\\\"0 auto\\\",\\\"borderWidth\\\":0,\\\"borderStyle\\\":\\\"none\\\",\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"position\\\":\\\"absolute\\\",\\\"left\\\":15,\\\"top\\\":500},\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"},\\\"sort\\\":0,\\\"action\\\":{\\\"type\\\":\\\"content\\\",\\\"id\\\":\\\"\\\",\\\"url\\\":\\\"\\\",\\\"query\\\":\\\"\\\",\\\"label\\\":\\\"跳转内容\\\",\\\"miniprogram\\\":{\\\"wechat\\\":{\\\"appId\\\":\\\"\\\",\\\"originalId\\\":\\\"\\\",\\\"path\\\":\\\"\\\"},\\\"baidu\\\":{\\\"appId\\\":\\\"\\\",\\\"path\\\":\\\"\\\"}},\\\"content\\\":{\\\"id\\\":\\\"6ce7a6b5af7e441ca24afb37a3291e59\\\",\\\"title\\\":\\\"首页\\\",\\\"contentType\\\":10,\\\"objectType\\\":26},\\\"phone\\\":\\\"\\\",\\\"chatTargetUid\\\":\\\"\\\",\\\"extendParams\\\":{},\\\"linkParams\\\":{\\\"marketingEventId\\\":\\\"\\\"}}},{\\\"id\\\":1626763556198,\\\"label\\\":\\\"手机号\\\",\\\"name\\\":\\\"手机号\\\",\\\"title\\\":\\\"\\\",\\\"type\\\":\\\"input\\\",\\\"typeValue\\\":\\\"number\\\",\\\"fieldName\\\":\\\"phone\\\",\\\"pattern\\\":\\\"^1[0-9]\\\\\\\\d{9}$\\\",\\\"defaultValue\\\":\\\"\\\",\\\"defaultValueOpen\\\":false,\\\"globalCacheField\\\":\\\"\\\",\\\"defaultValueType\\\":\\\"manual\\\",\\\"required\\\":true,\\\"verify\\\":false,\\\"weChatAuthorizationButton\\\":true,\\\"placeholder\\\":\\\"请输入手机号\\\",\\\"isFormComp\\\":true,\\\"weChatAuthorizationButtonStyle\\\":{\\\"color\\\":\\\"#fff\\\",\\\"background\\\":\\\"#09BB07\\\",\\\"fontSize\\\":14,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":0,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\"},\\\"verifyButtonStyle\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"background\\\":\\\"#ffffff\\\",\\\"fontSize\\\":14,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":1,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\"},\\\"titleStyle\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"fontSize\\\":14,\\\"lineHeight\\\":16,\\\"paddingBottom\\\":6,\\\"paddingTop\\\":6,\\\"whiteSpace\\\":\\\"normal\\\"},\\\"style\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"width\\\":345,\\\"fontSize\\\":14,\\\"paddingBottom\\\":0,\\\"paddingTop\\\":0,\\\"paddingLeft\\\":12,\\\"paddingRight\\\":12,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":1,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"left\\\":15,\\\"top\\\":32,\\\"position\\\":\\\"absolute\\\"},\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"height\\\":45,\\\"color\\\":\\\"#181c25\\\",\\\"background\\\":\\\"#fff\\\"},\\\"sort\\\":1},{\\\"id\\\":1626763564582,\\\"label\\\":\\\"姓名\\\",\\\"name\\\":\\\"姓名\\\",\\\"title\\\":\\\"\\\",\\\"type\\\":\\\"input\\\",\\\"typeValue\\\":\\\"text\\\",\\\"fieldName\\\":\\\"name\\\",\\\"defaultValueOpen\\\":false,\\\"defaultValue\\\":\\\"\\\",\\\"globalCacheField\\\":\\\"\\\",\\\"defaultValueType\\\":\\\"manual\\\",\\\"required\\\":true,\\\"placeholder\\\":\\\"请输入姓名\\\",\\\"isFormComp\\\":true,\\\"style\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"width\\\":345,\\\"fontSize\\\":14,\\\"paddingBottom\\\":0,\\\"paddingTop\\\":0,\\\"paddingLeft\\\":12,\\\"paddingRight\\\":12,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":1,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"left\\\":15,\\\"top\\\":101,\\\"position\\\":\\\"absolute\\\"},\\\"titleStyle\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"fontSize\\\":14,\\\"lineHeight\\\":16,\\\"paddingBottom\\\":6,\\\"paddingTop\\\":6,\\\"whiteSpace\\\":\\\"normal\\\"},\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"height\\\":45,\\\"color\\\":\\\"#181c25\\\",\\\"background\\\":\\\"#fff\\\"},\\\"sort\\\":2}],\\\"current\\\":0,\\\"slideIndex\\\":0,\\\"layout\\\":\\\"single\\\",\\\"fillType\\\":\\\"color\\\",\\\"fillMethod\\\":\\\"filling\\\",\\\"style\\\":{\\\"width\\\":375,\\\"height\\\":600,\\\"overflow\\\":\\\"hidden\\\",\\\"position\\\":\\\"relative\\\",\\\"backgroundColor\\\":\\\"\\\",\\\"backgroundImage\\\":\\\"\\\"},\\\"sort\\\":0}]}\"}";
        AddCustomizeFormDataArg arg = JSON.parseObject(json, AddCustomizeFormDataArg.class);
        arg.setType(3);
        arg.getFormHeadSetting().setName("这是会议的模板");
        arg.setFsUserId(1128);
        arg.setEa("74164");
        customizeFormDataService.addCustomizeFormData(arg, null,false);
    }

    public static void main(String[] args) {
        String targetJson = "{\n" + "  \"id\": \"3ec1e3de7bbb422f986e7d75067a32ec\",\n" + "  \"type\": \"page\",\n" + "  \"name\": \"报名表单\",\n" + "  \"title\": \"\",\n"
            + "  \"version\": \"4.2.0-8\",\n" + "  \"cover\": \"\",\n" + "  \"shareOpts\": {\n" + "    \"title\": \"\",\n" + "    \"desc\": \"\",\n" + "    \"link\": \"\",\n"
            + "    \"imgUrl\": \"\"\n" + "  },\n" + "  \"style\": {\n" + "    \"width\": 375,\n" + "    \"backgroundColor\": \"#fff\",\n" + "    \"backgroundSize\": \"100%\",\n"
            + "    \"backgroundRepeat\": \"no-repeat\",\n" + "    \"backgroundImage\": \"\"\n" + "  },\n" + "  \"backgroundFillType\": \"filling\",\n" + "  \"dataSourceAction\": {},\n"
            + "  \"components\": [\n" + "    {\n" + "      \"id\": 1586943155366,\n" + "      \"name\": \"自定义布局\",\n" + "      \"type\": \"container\",\n" + "      \"components\": [\n" + "        {\n"
            + "          \"id\": 1586943162828,\n" + "          \"label\": \"姓名\",\n" + "          \"name\": \"姓名\",\n" + "          \"type\": \"input\",\n" + "          \"typeValue\": \"text\",\n"
            + "          \"fieldName\": \"name\",\n" + "          \"required\": true,\n" + "          \"placeholder\": \"请输入姓名\",\n" + "          \"isFormComp\": true,\n" + "          \"style\": {\n"
            + "            \"color\": \"#181C25\",\n" + "            \"width\": 345,\n" + "            \"fontSize\": 14,\n" + "            \"paddingBottom\": 0,\n" + "            \"paddingTop\": 0,\n"
            + "            \"paddingLeft\": 12,\n" + "            \"paddingRight\": 12,\n" + "            \"borderStyle\": \"solid\",\n" + "            \"borderWidth\": 1,\n"
            + "            \"borderRadius\": 3,\n" + "            \"borderColor\": \"#e9edf5\",\n" + "            \"left\": 15,\n" + "            \"top\": 123,\n"
            + "            \"position\": \"absolute\"\n" + "          },\n" + "          \"placeholderStyle\": {\n" + "            \"color\": \"#cbcccf\"\n" + "          },\n"
            + "          \"inputStyle\": {\n" + "            \"height\": 45,\n" + "            \"color\": \"#181c25\",\n" + "            \"background\": \"#fff\"\n" + "          },\n"
            + "          \"sort\": 0\n" + "        },\n" + "        {\n" + "          \"id\": 1586943165896,\n" + "          \"label\": \"手机号\",\n" + "          \"name\": \"手机号\",\n"
            + "          \"type\": \"input\",\n" + "          \"typeValue\": \"number\",\n" + "          \"fieldName\": \"phone\",\n" + "          \"pattern\": \"^1[0-9]\\\\d{9}$\",\n"
            + "          \"required\": true,\n" + "          \"verify\": true,\n" + "          \"placeholder\": \"请输入手机号\",\n" + "          \"isFormComp\": true,\n"
            + "          \"weChatAuthorizationButton\": false,\n" + "          \"style\": {\n" + "            \"color\": \"#181C25\",\n" + "            \"width\": 345,\n"
            + "            \"fontSize\": 14,\n" + "            \"paddingBottom\": 0,\n" + "            \"paddingTop\": 0,\n" + "            \"paddingLeft\": 12,\n"
            + "            \"paddingRight\": 12,\n" + "            \"borderStyle\": \"solid\",\n" + "            \"borderWidth\": 1,\n" + "            \"borderRadius\": 3,\n"
            + "            \"borderColor\": \"#e9edf5\",\n" + "            \"left\": 15,\n" + "            \"top\": 178,\n" + "            \"position\": \"absolute\"\n" + "          },\n"
            + "          \"weChatAuthorizationButtonStyle\": {\n" + "            \"color\": \"#fff\",\n" + "            \"background\": \"rgba(28, 108, 247, 1)\",\n"
            + "            \"fontSize\": 14,\n" + "            \"borderStyle\": \"solid\",\n" + "            \"borderWidth\": 0,\n" + "            \"borderRadius\": 3,\n"
            + "            \"borderColor\": \"#e9edf5\"\n" + "          },\n" + "          \"verifyButtonStyle\": {\n" + "            \"color\": \"#181C25\",\n"
            + "            \"background\": \"#ffffff\",\n" + "            \"fontSize\": 14,\n" + "            \"borderStyle\": \"solid\",\n" + "            \"borderWidth\": 1,\n"
            + "            \"borderRadius\": 3,\n" + "            \"borderColor\": \"#e9edf5\"\n" + "          },\n" + "          \"placeholderStyle\": {\n" + "            \"color\": \"#cbcccf\"\n"
            + "          },\n" + "          \"inputStyle\": {\n" + "            \"height\": 45,\n" + "            \"color\": \"#181c25\",\n" + "            \"background\": \"#fff\"\n"
            + "          },\n" + "          \"sort\": 1\n" + "        },\n" + "        {\n" + "          \"id\": 1586943171094,\n" + "          \"label\": \"公司名称\",\n"
            + "          \"name\": \"公司名称\",\n" + "          \"type\": \"input\",\n" + "          \"typeValue\": \"text\",\n" + "          \"fieldName\": \"companyName\",\n"
            + "          \"required\": true,\n" + "          \"placeholder\": \"请输入公司名称\",\n" + "          \"isFormComp\": true,\n" + "          \"style\": {\n"
            + "            \"color\": \"#181C25\",\n" + "            \"width\": 345,\n" + "            \"fontSize\": 14,\n" + "            \"paddingBottom\": 0,\n" + "            \"paddingTop\": 0,\n"
            + "            \"paddingLeft\": 12,\n" + "            \"paddingRight\": 12,\n" + "            \"borderStyle\": \"solid\",\n" + "            \"borderWidth\": 1,\n"
            + "            \"borderRadius\": 3,\n" + "            \"borderColor\": \"#e9edf5\",\n" + "            \"left\": 15,\n" + "            \"top\": 344,\n"
            + "            \"position\": \"absolute\"\n" + "          },\n" + "          \"placeholderStyle\": {\n" + "            \"color\": \"#cbcccf\"\n" + "          },\n"
            + "          \"inputStyle\": {\n" + "            \"height\": 45,\n" + "            \"color\": \"#181c25\",\n" + "            \"background\": \"#fff\"\n" + "          },\n"
            + "          \"sort\": 2\n" + "        },\n" + "        {\n" + "          \"id\": 1586943175662,\n" + "          \"label\": \"职务\",\n" + "          \"name\": \"职务\",\n"
            + "          \"type\": \"input\",\n" + "          \"typeValue\": \"text\",\n" + "          \"fieldName\": \"position\",\n" + "          \"required\": true,\n"
            + "          \"placeholder\": \"请输入职务\",\n" + "          \"isFormComp\": true,\n" + "          \"style\": {\n" + "            \"color\": \"#181C25\",\n" + "            \"width\": 345,\n"
            + "            \"fontSize\": 14,\n" + "            \"paddingBottom\": 0,\n" + "            \"paddingTop\": 0,\n" + "            \"paddingLeft\": 12,\n"
            + "            \"paddingRight\": 12,\n" + "            \"borderStyle\": \"solid\",\n" + "            \"borderWidth\": 1,\n" + "            \"borderRadius\": 3,\n"
            + "            \"borderColor\": \"#e9edf5\",\n" + "            \"left\": 15,\n" + "            \"top\": 401,\n" + "            \"position\": \"absolute\"\n" + "          },\n"
            + "          \"placeholderStyle\": {\n" + "            \"color\": \"#cbcccf\"\n" + "          },\n" + "          \"inputStyle\": {\n" + "            \"height\": 45,\n"
            + "            \"color\": \"#181c25\",\n" + "            \"background\": \"#fff\"\n" + "          },\n" + "          \"sort\": 3\n" + "        },\n" + "        {\n"
            + "          \"id\": 1586943183605,\n" + "          \"label\": \"提交\",\n" + "          \"name\": \"提交\",\n" + "          \"tip\": \"提交成功\",\n" + "          \"type\": \"button\",\n"
            + "          \"position\": \"none\",\n" + "          \"required\": false,\n" + "          \"isFormComp\": true,\n" + "          \"wrapStyle\": {\n"
            + "            \"position\": \"none\",\n" + "            \"background\": \"rgba(255,255,255,.9)\"\n" + "          },\n" + "          \"style\": {\n" + "            \"height\": 45,\n"
            + "            \"width\": 345,\n" + "            \"fontSize\": 16,\n" + "            \"background\": \"rgba(28, 108, 247, 1)\",\n" + "            \"borderRadius\": 0,\n"
            + "            \"color\": \"#fff\",\n" + "            \"letterSpacing\": 0,\n" + "            \"lineHeight\": 45,\n" + "            \"textAlign\": \"center\",\n"
            + "            \"margin\": \"0 auto\",\n" + "            \"borderWidth\": 0,\n" + "            \"borderStyle\": \"none\",\n" + "            \"borderColor\": \"#e9edf5\",\n"
            + "            \"left\": 15,\n" + "            \"top\": 495,\n" + "            \"position\": \"absolute\",\n" + "            \"opacity\": 100\n" + "          },\n"
            + "          \"sort\": 4,\n" + "          \"action\": {\n" + "            \"type\": \"inside\",\n" + "            \"id\": \"948c8192a0454989b3468c2d2a3c2e2a\",\n"
            + "            \"url\": \"\",\n" + "            \"miniprogram\": {\n" + "              \"wechat\": {\n" + "                \"appKey\": \"\",\n" + "                \"pagepath\": \"\"\n"
            + "              },\n" + "              \"baidu\": {\n" + "                \"appKey\": \"\",\n" + "                \"pagepath\": \"\"\n" + "              }\n" + "            }\n"
            + "          },\n" + "          \"placeholderStyle\": {\n" + "            \"color\": \"#cbcccf\"\n" + "          },\n" + "          \"inputStyle\": {\n"
            + "            \"color\": \"#181c25\"\n" + "          }\n" + "        },\n" + "        {\n" + "          \"id\": 1586943197119,\n" + "          \"name\": \"文本\",\n"
            + "          \"type\": \"text\",\n"
            + "          \"value\": \"<p style=\\\"text-align: center;\\\"><span style=\\\"font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\\\">提交以下信息马上报名参与活动</span></p>\",\n"
            + "          \"style\": {\n" + "            \"paddingBottom\": 6,\n" + "            \"paddingLeft\": 0,\n" + "            \"paddingRight\": 0,\n" + "            \"paddingTop\": 6,\n"
            + "            \"background\": \"rgba(255, 255, 255, 0)\",\n" + "            \"fontSize\": 14,\n" + "            \"borderWidth\": 0,\n" + "            \"borderRadius\": 0,\n"
            + "            \"borderStyle\": \"none\",\n" + "            \"borderColor\": \"#e9edf5\",\n" + "            \"left\": 50,\n" + "            \"top\": 44,\n"
            + "            \"position\": \"absolute\",\n" + "            \"width\": 275\n" + "          },\n" + "          \"sort\": 5,\n" + "          \"placeholderStyle\": {\n"
            + "            \"color\": \"#cbcccf\"\n" + "          },\n" + "          \"inputStyle\": {\n" + "            \"color\": \"#181c25\"\n" + "          }\n" + "        },\n" + "        {\n"
            + "          \"id\": \"1625816426147\",\n" + "          \"label\": \"邮箱\",\n" + "          \"name\": \"邮箱\",\n" + "          \"title\": \"\",\n" + "          \"type\": \"input\",\n"
            + "          \"typeValue\": \"text\",\n" + "          \"fieldName\": \"email\",\n" + "          \"pattern\": \"^\\\\w+([-+.]\\\\w+)*@\\\\w+([-.]\\\\w+)*\\\\.\\\\w+([-.]\\\\w+)*$\",\n"
            + "          \"defaultValue\": \"\",\n" + "          \"defaultValueOpen\": false,\n" + "          \"globalCacheField\": \"\",\n" + "          \"defaultValueType\": \"manual\",\n"
            + "          \"required\": true,\n" + "          \"placeholder\": \"请输入邮箱\",\n" + "          \"isFormComp\": true,\n" + "          \"titleStyle\": {\n"
            + "            \"color\": \"#181C25\",\n" + "            \"fontSize\": 14,\n" + "            \"lineHeight\": 16,\n" + "            \"paddingBottom\": 6,\n"
            + "            \"paddingTop\": 6,\n" + "            \"whiteSpace\": \"normal\"\n" + "          },\n" + "          \"style\": {\n" + "            \"color\": \"#181C25\",\n"
            + "            \"width\": 345,\n" + "            \"fontSize\": 14,\n" + "            \"paddingBottom\": 0,\n" + "            \"paddingTop\": 0,\n" + "            \"paddingLeft\": 12,\n"
            + "            \"paddingRight\": 12,\n" + "            \"borderStyle\": \"solid\",\n" + "            \"borderWidth\": 1,\n" + "            \"borderRadius\": 3,\n"
            + "            \"borderColor\": \"#e9edf5\",\n" + "            \"left\": 15,\n" + "            \"top\": 289,\n" + "            \"position\": \"absolute\"\n" + "          },\n"
            + "          \"placeholderStyle\": {\n" + "            \"color\": \"#cbcccf\"\n" + "          },\n" + "          \"inputStyle\": {\n" + "            \"height\": 45,\n"
            + "            \"color\": \"#181c25\",\n" + "            \"background\": \"#fff\"\n" + "          },\n" + "          \"sort\": 6\n" + "        }\n" + "      ],\n" + "      \"style\": {\n"
            + "        \"width\": 375,\n" + "        \"height\": 600,\n" + "        \"x\": 482,\n" + "        \"y\": 153,\n" + "        \"overflow\": \"hidden\",\n"
            + "        \"position\": \"relative\"\n" + "      },\n" + "      \"sort\": 0,\n" + "      \"key\": \"form-container\",\n" + "      \"typeValue\": \"form\"\n" + "    }\n" + "  ],\n"
            + "  \"placeholderStyle\": {\n" + "    \"color\": \"#cbcccf\"\n" + "  },\n" + "  \"inputStyle\": {\n" + "    \"color\": \"#181c25\"\n" + "  },\n" + "  \"isHomepage\": 2\n" + "}";
        String templateJson = "{\"id\":\"\",\"type\":\"page\",\"name\":\"\",\"title\":\"\",\"version\":\"4.2.0-12\",\"cover\":\"\",\"shareOpts\":{\"title\":\"\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"\"},\"style\":{\"width\":375,\"backgroundColor\":\"#fff\",\"backgroundSize\":\"100%\",\"backgroundRepeat\":\"no-repeat\",\"backgroundImage\":\"\"},\"backgroundFillType\":\"filling\",\"dataSourceAction\":{},\"components\":[{\"id\":\"1626664604454\",\"name\":\"表单\",\"key\":\"form-container\",\"type\":\"container\",\"typeValue\":\"form\",\"components\":[{\"id\":\"1626664604455\",\"label\":\"提交\",\"name\":\"提交\",\"tip\":\"提交成功\",\"type\":\"button\",\"position\":\"none\",\"required\":false,\"isFormComp\":true,\"wrapStyle\":{\"position\":\"none\",\"background\":\"rgba(255,255,255,.9)\"},\"style\":{\"height\":45,\"width\":345,\"fontSize\":16,\"background\":\"#409EFF\",\"borderRadius\":0,\"color\":\"#fff\",\"letterSpacing\":0,\"lineHeight\":45,\"textAlign\":\"center\",\"margin\":\"0 auto\",\"borderWidth\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\",\"position\":\"absolute\",\"left\":15,\"top\":500}},{\"id\":1626685422672,\"label\":\"姓名\",\"name\":\"姓名\",\"title\":\"\",\"type\":\"input\",\"typeValue\":\"text\",\"fieldName\":\"name\",\"defaultValueOpen\":false,\"defaultValue\":\"\",\"globalCacheField\":\"\",\"defaultValueType\":\"manual\",\"required\":true,\"placeholder\":\"请输入姓名\",\"isFormComp\":true,\"style\":{\"color\":\"#181C25\",\"width\":345,\"fontSize\":14,\"paddingBottom\":0,\"paddingTop\":0,\"paddingLeft\":12,\"paddingRight\":12,\"borderStyle\":\"solid\",\"borderWidth\":1,\"borderRadius\":3,\"borderColor\":\"#e9edf5\",\"left\":15,\"top\":91,\"position\":\"absolute\"},\"titleStyle\":{\"color\":\"#181C25\",\"fontSize\":14,\"lineHeight\":16,\"paddingBottom\":6,\"paddingTop\":6,\"whiteSpace\":\"normal\"},\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"height\":45,\"color\":\"#181c25\",\"background\":\"#fff\"},\"sort\":1},{\"id\":1626685427544,\"label\":\"手机号\",\"name\":\"手机号\",\"title\":\"\",\"type\":\"input\",\"typeValue\":\"number\",\"fieldName\":\"phone\",\"pattern\":\"^1[0-9]\\\\d{9}$\",\"defaultValue\":\"\",\"defaultValueOpen\":false,\"globalCacheField\":\"\",\"defaultValueType\":\"manual\",\"required\":true,\"verify\":false,\"weChatAuthorizationButton\":true,\"placeholder\":\"请输入手机号\",\"isFormComp\":true,\"weChatAuthorizationButtonStyle\":{\"color\":\"#fff\",\"background\":\"#09BB07\",\"fontSize\":14,\"borderStyle\":\"solid\",\"borderWidth\":0,\"borderRadius\":3,\"borderColor\":\"#e9edf5\"},\"verifyButtonStyle\":{\"color\":\"#181C25\",\"background\":\"#ffffff\",\"fontSize\":14,\"borderStyle\":\"solid\",\"borderWidth\":1,\"borderRadius\":3,\"borderColor\":\"#e9edf5\"},\"titleStyle\":{\"color\":\"#181C25\",\"fontSize\":14,\"lineHeight\":16,\"paddingBottom\":6,\"paddingTop\":6,\"whiteSpace\":\"normal\"},\"style\":{\"color\":\"#181C25\",\"width\":345,\"fontSize\":14,\"paddingBottom\":0,\"paddingTop\":0,\"paddingLeft\":12,\"paddingRight\":12,\"borderStyle\":\"solid\",\"borderWidth\":1,\"borderRadius\":3,\"borderColor\":\"#e9edf5\",\"left\":15,\"top\":163,\"position\":\"absolute\"},\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"height\":45,\"color\":\"#181c25\",\"background\":\"#fff\"},\"sort\":2}],\"current\":0,\"slideIndex\":0,\"layout\":\"single\",\"fillType\":\"color\",\"fillMethod\":\"filling\",\"style\":{\"width\":375,\"height\":600,\"overflow\":\"hidden\",\"position\":\"relative\",\"backgroundColor\":\"\",\"backgroundImage\":\"\"},\"sort\":0}]}";

        JSONObject targetContent = JSON.parseObject(targetJson);
        JSONObject templateContent = JSON.parseObject(templateJson);

        JSONArray targetOutComponents = targetContent.getJSONArray("components");
        if (targetOutComponents != null && !targetOutComponents.isEmpty()) {
            JSONArray templateOutComponents = templateContent.getJSONArray("components");
            if (templateOutComponents == null || templateOutComponents.isEmpty()) {
                targetContent.put("components", new ArrayList<>());
            } else {
                JSONArray templateInComponents = templateOutComponents.getJSONObject(0).getJSONArray("components");
                JSONObject objInOutComponents = targetOutComponents.getJSONObject(0);
                if (templateInComponents == null || templateInComponents.isEmpty()) {
                    objInOutComponents.put("components", new ArrayList<>());
                } else {
                    JSONArray newComponents = new JSONArray();
                    for (Object templateInComponent : templateInComponents) {
                        JSONObject obj = (JSONObject) templateInComponent;
                        if (!obj.getString("type").equals("button")) {
                            newComponents.add(obj);
                        }
                    }
                    objInOutComponents.put("components", newComponents);
                }
            }
        } else {
            System.out.println("外面的Components为空");
        }
        System.out.println("=============");
    }

    @Test
    public void test() {
      //  CustomizeFormDataEnrollArg arg = JSON.parseObject("{\"ea\":\"74164\",\"objectId\":\"b386900f281f4018b68963a02c582ebd\",\"objectType\":27,\"formId\":\"dfd3ae136084443dafc74efe85823e7a\",\"submitContent\":{\"phone\":\"15216146853\",\"newSave\":false},\"marketingEventId\":\"642577f960d05d0001318b9b\",\"fingerPrint\":\"9a32e6b8b0df42c585429e777a3f7ca5\",\"channelValue\":\"ad\",\"allEnterpriseMemberCookieMap\":{},\"needSignIn\":false,\"partner\":false,\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"ipAddr\":\"*************\",\"landingUrl\":\"https://crm.ceshi112.com/ec/h5-landing/release/index.html?marketingEventId\\u003d642577f960d05d0001318b9b\\u0026spreadChannel\\u003dad\\u0026id\\u003d0a712459a8ec4ed3b9fd8a9da35d0aa3\\u0026type\\u003d1\",\"landingPageName\":\"首页\",\"landingPageUrl\":\"https://crm.ceshi112.com/ec/h5-landing/release/index.html?marketingEventId\\u003d642577f960d05d0001318b9b\\u0026spreadChannel\\u003dad\\u0026id\\u003d0a712459a8ec4ed3b9fd8a9da35d0aa3\\u0026type\\u003d1\",\"convertPageUrl\":\"https://crm.ceshi112.com/ec/h5-landing/release/index.html?marketingEventId\\u003d642577f960d05d0001318b9b\\u0026spreadChannel\\u003dad\\u0026id\\u003d0a712459a8ec4ed3b9fd8a9da35d0aa3\\u0026type\\u003d1\"}", CustomizeFormDataEnrollArg.class);
        CustomizeFormDataEnrollArg arg = JSON.parseObject("{\n" +
                "    \"formId\": \"3a03a212874a4b57a5582d06a19051a8\",\n" +
                "    \"objectType\": 27,\n" +
                "    \"objectId\": \"f55b6b685d6242e49e507773cebce915\",\n" +
                "    \"formUsage\": 1,\n" +
                "    \"marketingEventId\": \"6721f29ebd27340007797863\",\n" +
                "    \"channelValue\": \"ad\",\n" +
                "    \"needSignIn\": false,\n" +
                "    \"submitContent\": {\n" +
                "        \"name\": \"线索777\",\n" +
                "        \"phone\": \"15462369388\"\n" +
                "    },\n" +
                "    \"landingPageUrl\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6721f29ebd27340007797863&spreadChannel=ad&objectType=26&ea=88146&id=2b72bf004c94414cb5f215ebb2c9d022&type=1\",\n" +
                "    \"landingPageName\": \"首页\",\n" +
                "    \"convertPageUrl\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6721f29ebd27340007797863&spreadChannel=ad&objectType=26&ea=88146&id=2b72bf004c94414cb5f215ebb2c9d022&type=1\",\n" +
                "    \"client\": 2,\n" +
                "    \"browser\": \"Chrome 131.0.0.0\",\n" +
                "    \"operateSystem\": \"Windows 10\",\n" +
                "    \"landingUrl\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6721f29ebd27340007797863&spreadChannel=ad&objectType=26&ea=88146&id=2b72bf004c94414cb5f215ebb2c9d022&type=1\",\n" +
                "    \"ea\": \"88146\",\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36\",\n" +
                "    \"referer\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6721f29ebd27340007797863&spreadChannel=ad&objectType=26&ea=88146&id=2b72bf004c94414cb5f215ebb2c9d022&type=1\",\n" +
                "    \"timestamp\": 1736851655897,\n" +
                "    \"nonce\": \"05839199edab65d2\",\n" +
                "    \"sign\": \"a30a05bbf0175bd7e594ad8aea2047f9\"\n" +
                "}", CustomizeFormDataEnrollArg.class);
        arg.setFingerPrint(UUIDUtil.getUUID());
        CustomizeFormDataEnrollResult result = customizeFormDataService.noIdentityFormDataEnroll(arg).getData();
        log.info("结果: {}", result);
    }

    @Test
    public void reImportDataToCrmTest() {
        ReImportDataToCrmArg arg = new ReImportDataToCrmArg();
        arg.setEa("83668");
        arg.setUserId(-10000);
        arg.setIds(Lists.newArrayList("11dff3818f4f4c28ba5fc55dd725359f"));
        customizeFormDataService.reImportDataToCrm(arg);
    }


    @Test
    public void countUnSaveEnrollDataTest() {
        CountUnSaveEnrollDataArg arg = new CountUnSaveEnrollDataArg();
        arg.setEa("74164");
        arg.setSourceType(2);
        arg.setFsUserId(1135);
        arg.setSourceId("0e7dda30179d4427aa450425b9f5d96f");
        Result<EnrollDataCountResult> result = customizeFormDataService.countUnSaveEnrollData(arg);
        System.out.println(result);
    }

    @Test
    public void sendSaveClueFailMessage() {
        // 74164	5e97f3673b5dbf0001f78082	5e96f6f97c13d500013f5a9c ok
        // 74164		                        61e54a4201aa93000159495d ok
        // 74164	6267b0184c1bca00017edbe3                             ok bug no msg
        // 74164                                                         ok bug no msg
        SendSaveClueFailMessageArg arg = new SendSaveClueFailMessageArg();
        arg.setEa("74164");
        arg.setFormId(null);
        arg.setMarketingEventId(null);
        customizeFormDataManager.sendSaveClueFailMessage("74164", null, "6267d4b879490b000128b731", true);
    }

    @Test
    public void editCustomizeFormGroup() {
        String ea = "74164";
        int userId = 1177;
        EditObjectGroupArg arg = new EditObjectGroupArg();
        arg.setName("表单分组");
        Result<EditObjectGroupResult> result = customizeFormDataService.editCustomizeFormGroup(ea, userId, arg);
        log.info("== editCustomizeFormGroup result:{}", result);
        arg.setId(result.getData().getId());
        arg.setName("表单分组2");
        result = customizeFormDataService.editCustomizeFormGroup(ea, userId, arg);
        log.info("== editCustomizeFormGroup result:{}", result);
    }

    @Test
    public void deleteCustomizeFormGroup() {
        String ea = "74164";
        int userId = 1177;

        EditObjectGroupArg editObjectGroupArg = new EditObjectGroupArg();
        editObjectGroupArg.setName("Biaoge模板分组qq");
        Result<EditObjectGroupResult> addResult = customizeFormDataService.editCustomizeFormGroup(ea, userId, editObjectGroupArg);
        log.info("== deleteCustomizeFormGroup result:{}", addResult);
        DeleteObjectGroupArg arg = new DeleteObjectGroupArg();
        arg.setId(addResult.getData().getId());

        Result<Void> result = customizeFormDataService.deleteCustomizeFormGroup(ea, userId, arg);
        log.info("== deleteCustomizeFormGroup result:{}", result);
    }

    @Test
    public void setCustomizeFormGroup() {
        String ea = "74164";
        int userId = 1177;

        EditObjectGroupArg arg = new EditObjectGroupArg();
        arg.setName("hahaha");
        Result<EditObjectGroupResult> result = customizeFormDataService.editCustomizeFormGroup(ea, userId, arg);
        log.info("== setCustomizeFormGroup result:{}", result);

        SetObjectGroupArg setObjectGroupArg = new SetObjectGroupArg();
        setObjectGroupArg.setGroupId(result.getData().getId());
        List<String> objectIdList = new ArrayList<>();
        objectIdList.add("dc2258072adf48edab2b8a628490c42d");
        objectIdList.add("9d14682da6f142f8beca7eb8c9485e0d");
        setObjectGroupArg.setObjectIdList(objectIdList);
        Result<Void> result1 = customizeFormDataService.setCustomizeFormGroup(ea, userId, setObjectGroupArg);
        log.info("setCustomizeFormGroup result: {}", result1);
    }

    @Test
    public void deleteCustomizeFormBatch() {
        String ea = "74164";
        int userId = 1177;
        DeleteMaterialArg arg = new DeleteMaterialArg();
        List<String> objectIdList = new ArrayList<>();
        objectIdList.add("7d05996b6f41466ab49297436cd05aab");
        arg.setIdList(objectIdList);
        Result<Void> result1 = customizeFormDataService.deleteCustomizeFormBatch(ea, userId, arg);
        log.info("deleteCustomizeFormBatch result: {}", result1);
    }

    @Test
    public void topProduct() {
        String ea = "74164";
        int userId = 1177;
        TopMaterialArg arg = new TopMaterialArg();
        arg.setObjectId("ecb4230cfeec47a38d124360198fc794");
        Result<Void> result = customizeFormDataService.topCustomizeForm(ea, userId, arg);
        log.info("topHexagonSite result : {}", result);
    }

    @Test
    public void marketingPromotionSourceFormSubmitTest() {
        String json = "\n" +
                "{\"formId\":\"2b24464399834f7383292fe151ad567d\",\"objectType\":27,\"objectId\":\"7f2a73816b604570905d341946547513\",\"formUsage\":1,\"marketingEventId\":\"63db304442bc3e000191be57\",\"channelValue\":\"ad\",\"needSignIn\":false,\"submitContent\":{\"name\":\"fdffffff\",\"phone\":\"13677500001\"},\"landingUrl\":\"https://crm.ceshi112.com/ec/h5-landing/release/index.html?marketingEventId=63db304442bc3e000191be57&spreadChannel=ad&id=64a0730008d1445389998f4bda658383&type=1\"}";
        CustomizeFormDataEnrollArg ar5 = JSON.parseObject(json, CustomizeFormDataEnrollArg.class);
        ar5.getSubmitContent().setPhone("15655555519");
        ar5.getSubmitContent().setName("和合法的4");
        ar5.setFingerPrint("073eba3510aa4f22314935cb31q311cf");
        ar5.setEa("83668");
        CustomizeFormDataEnrollResult result1 = customizeFormDataService.noIdentityFormDataEnroll(ar5).getData();
        log.info("结果：{}", result1);
//        MarketingPromotionSourceArg marketingPromotionSourceArg = BeanUtil.copy(ar5, MarketingPromotionSourceArg.class);
//        String id = marketingPromotionSourceObjManager.querySameMarketingPromotionSourceObjId(marketingPromotionSourceArg);
//        log.info("结果：{}", id);
    }

    @Test
    public void queryFormUserDataForMarketingActivityIdAndSpreadFsUid() {

        QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg arg = new QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg();
        arg.setMarketingActivityId("618cbdc7d12c9900013430b7");
//        arg.setKeyword();
//        arg.setFsUid();
        arg.setStartDate(1648523823000L);
        arg.setEndDate(1680059823000L);
        arg.setPageNum(1);
        arg.setPageSize(10);
        Result<PageResult<QueryFormUserDataResult>> pageResultResult = customizeFormDataService.queryFormUserDataForMarketingActivityIdAndSpreadFsUid(arg);
        log.info("结果：{}", pageResultResult);
//        MarketingPromotionSourceArg marketingPromotionSourceArg = BeanUtil.copy(ar5, MarketingPromotionSourceArg.class);
//        String id = marketingPromotionSourceObjManager.querySameMarketingPromotionSourceObjId(marketingPromotionSourceArg);
//        log.info("结果：{}", id);
    }


    @Test
    public void addThirdPlatformFieldAndLeadField() {
//        marketingPromotionSourceObjManager.addThirdPlatformFieldAndLeadField("83668");
//        marketingPromotionSourceObjManager.addThirdPlatformFieldAndLeadField("74164");
    }

    @Test
    public void checkEnrollField() {
        CustomizeFormDataEntity customizeFormDataEntity = JSONObject.parseObject("{\"id\":\"f2385383e245455a92193e9573889281\",\"ea\":\"88146\",\"type\":2,\"formUsage\":1,\"formHeadSetting\":{\"name\":\"首页\",\"title\":\"首页\"},\"formBodySetting\":[{\"apiName\":\"name\",\"type\":\"text\",\"defineType\":\"custom\",\"label\":\"姓名\",\"isRequired\":true,\"isVerify\":false,\"helpText\":\"请输入姓名\",\"dateFormat\":\"yyyy-MM-dd HH:mm\"},{\"apiName\":\"phone\",\"type\":\"phone_number\",\"defineType\":\"custom\",\"label\":\"手机号\",\"isRequired\":true,\"isVerify\":true,\"helpText\":\"请输入手机号\",\"dateFormat\":\"yyyy-MM-dd HH:mm\"}],\"formFootSetting\":{\"buttonName\":\"提交\",\"fontColor\":\"#FFFFFF\",\"buttonColor\":\"#F8B05B\",\"buttonBorderColor\":\"#F8B05B\"},\"formSuccessSetting\":{\"afterSuccessAction\":99,\"payDescription\":\"\",\"totalFee\":0},\"formMoreSetting\":{\"synchronousCRM\":false,\"chargePerson\":false,\"fillInOnce\":false,\"checkMember\":false,\"enrollLimit\":false,\"syncToMember\":false,\"submitJumpType\":\"\",\"memberCheckType\":0,\"saveCrmObjectType\":0},\"crmFormFieldMapV2\":[],\"crmApiName\":\"LeadsObj\",\"createBy\":1000,\"updateBy\":1000,\"status\":0,\"customizeApinameMapping\":{}}", CustomizeFormDataEntity.class);
        CustomizeFormDataEnroll submitContent = JSONObject.parseObject("{\"name\":\"小张\",\"phone\":\"13762692877\",\"phoneVerifyCode\":\"\",\"newSave\":false}", CustomizeFormDataEnroll.class);
        customizeFormDataManager.checkEnrollField(customizeFormDataEntity, submitContent, 27, "4246771478514878b6de40b7818b7d38", null);
    }

    @Test
    public void getAreaNameByKeyword() {
        Result<AreaByKeywordResult> result = customizeFormDataService.getAreaNameByKeyword("88146","纽", 0);
        log.info("getAreaNameByKeyword result:{}", result.getData());
    }

    @Test
    public void getAreaNameByKeyword2() {
        Result<AreaByKeywordResult> result = customizeFormDataService.getAreaNameByKeyword("88146","纽", 1);
        log.info("getAreaNameByKeyword2 result:{}", result.getData());
    }

    @Test
    public void getZoneNameByParentId() {
        Result<AreaByParentResult> result = customizeFormDataService.getZoneByParent("88146",null, 1);
        log.info("getAreaNameByKeyword result:{}", result.getData());
    }

    @Test
    public void batchQueryLocationInfo() {
        List<String> codes = Lists.newArrayList("248");
        Result<LocationResult> result=customizeFormDataService.batchQueryLocationInfo("88146", codes);
        log.info("batchQueryLocationInfo result:{}", result);

    }
}
