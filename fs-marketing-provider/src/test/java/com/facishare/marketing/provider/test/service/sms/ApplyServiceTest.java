/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.sms;

import com.facishare.marketing.api.arg.sms.*;
import com.facishare.marketing.api.arg.sms.QueryTemplateVO;
import com.facishare.marketing.api.result.sms.ListMiniProgramsResult;
import com.facishare.marketing.api.result.sms.QueryTemplateResult;
import com.facishare.marketing.api.service.sms.ApplyService;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.TemplateDirectionEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.SmsContentParam;
import com.facishare.marketing.common.typehandlers.value.SmsContentParamList;
import com.facishare.marketing.provider.dao.sms.AppIdDAO;
import com.facishare.marketing.provider.dao.sms.SignatureDAO;
import com.facishare.marketing.provider.entity.sms.AppIdEntity;
import com.facishare.marketing.provider.test.BaseTest;
import java.util.List;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by zhengh on 2018/12/25.
 */
//@Ignore
@Slf4j

public class ApplyServiceTest extends BaseTest {
    @Autowired
    private ApplyService applyService;
    @Autowired
    private SignatureDAO signatureDAO;
    @Autowired
    private AppIdDAO appIdDAO;

    @Test
    public void moveTemplateTest() {
        MoveTemplateArg arg = new MoveTemplateArg();
        arg.setTemplateId("1111");
        arg.setDirection(TemplateDirectionEnum.BACKWARD.getDirection());
        arg.setEa("2");
        arg.setUserId(1000);
        System.out.println(applyService.moveTemplate(arg));
    }

    @Test
    public void applySignatureTest() {
        ApplySignatureArg arg = new ApplySignatureArg();
        arg.setEa("55487");
        arg.setRemark("申请短信签名");
        arg.setUserId(1004);
        arg.setSignature("sig-001");
        //arg.setTapath("TA_7f5d503c834d4403acb041bd4c3f7de4.jpg");
        System.out.println(applyService.applySignature(arg));
    }

    @Test
    public void queryTemplate() {
        QueryTemplateVO arg = new QueryTemplateVO();
        arg.setEa("74164");
        arg.setName("");
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setSceneType(null);
        Result<PageResult<QueryTemplateResult>> result = applyService.queryTemplate(arg);
        System.out.println(result);
    }

    @Test
    public void querySignature() {
        QuerySignatureArg arg = new QuerySignatureArg();
        arg.setEa("2");
        System.out.println(applyService.querySignature(arg));
    }

    @Test
    public void queryAllAppId() {
        List<AppIdEntity> appIdEntities = appIdDAO.queryAllAppId();
        System.out.println(appIdEntities);
    }

    @Test
    public void testa() {
        AppIdEntity appIdEntity = appIdDAO.getAvailableAppId();
        log.info("appIdEntity:{}", appIdEntity);
    }

    @Test
    public void applyTrial() {
        Result<Void> result = applyService.applyTrial("61364", 1000);
        Assert.assertTrue(result.isSuccess());

    }

    @Test
    public void editSmsTemplate() {
        ApplyTemplateVO arg = new ApplyTemplateVO();
        arg.setName("模板0110-1");
        arg.setContent("短信内容");
        arg.setSceneType(1);
        arg.setEa("74164");
        arg.setUserId(1164);
        arg.setRemark("备注信息");
        arg.setTplType(0);
        arg.setChannelType(ChannelTypeEnum.GENERAL.getType());
        arg.setRelationApiName("CaseObj");
        SmsContentParam smsContentParam = new SmsContentParam();
        smsContentParam.setKey("a");
        smsContentParam.setType("minip");
        smsContentParam.setValue("1");
        SmsContentParamList smsContentParams = new SmsContentParamList();
        smsContentParams.add(smsContentParam);
        arg.setSmsContentParam(smsContentParams);
        Result<QueryTemplateResult> result = applyService.applyTemplate(arg);
        System.out.println("yes: " + result);
    }

    @Test
    public void testListSmsTemplate(){
        QueryTemplateVO vo = new QueryTemplateVO();
        vo.setEa("74164");
        vo.setPageSize(10);
        vo.setPageNum(1);
        Result<PageResult<QueryTemplateResult>> result = applyService.listSmsTemplate(vo);
        System.out.println("yes:" + result);
    }

    @Test
    public void getTemplateDetail() {
        DeleteTemplateVO arg = new DeleteTemplateVO();
        arg.setTemplateId("aae2fac6244f46c287da110800f7b4f8");
        Result<QueryTemplateResult> templateDetail = applyService.getTemplateDetail(arg);
        System.out.println(templateDetail.getData());
    }

}

