/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.result.advertiser.headlines.*;
import com.facishare.marketing.api.result.baidu.GetDataOverviewResult;
import com.facishare.marketing.api.service.advertiser.headlines.HeadlinesService;
import com.facishare.marketing.api.service.baidu.AdAccountService;
import com.facishare.marketing.api.vo.advertiser.headlines.*;
import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO;
import com.facishare.marketing.common.enums.advertiser.headlines.*;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO;
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity;
import com.facishare.marketing.provider.manager.advertiser.headlines.HeadlinesAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.AccountApiManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Created by wangzhenyi on 2021/8/25 11:40 上午
 */
@Slf4j
public class HeadlinesServiceTest extends BaseTest {

    @Autowired
    private HeadlinesService headlinesService;
    @Autowired
    private AdAccountService accountService;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;
    @Autowired
    private HeadlinesAdMarketingManager headlinesAdMarketingManager;
    @Autowired
    private AccountApiManager accountApiManager;
    @Autowired
    private HeadlinesAdDataDAO headlinesAdDataDAO;
    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Autowired
    private BaiduAccountDAO accountDAO;

    @Test
    public void getHeadlinesCampaignListTest() {
        QueryHeadlinesCampaignListVO vo = new QueryHeadlinesCampaignListVO();
        vo.setEa("74164");
//        vo.setNameKey("头条");
        vo.setSource("3");
        vo.setPageNum(2);
        vo.setPageSize(10);
        vo.setAdAccountId("3d01871e214646c99fdf01b6b5c4289a");
        Result<PageResult<HeadlinesCampaignInfoResult>> pageResultResult = headlinesService.queryCampaignList(vo);
        log.info(String.valueOf(pageResultResult));
    }

    @Test
    public void getHeadlinesAdListTest() {
        QueryHeadlinesAdPlanListVO vo = new QueryHeadlinesAdPlanListVO();
        vo.setEa("74164");
        vo.setAdAccountId("9119e1274a694cdf9320e6b82bf9a696");
//        vo.setStatus(1);
       // vo.setSource("1");
      //  vo.setCampaignId(97773899L);
//        vo.setNameKey("核心词");
        vo.setPageNum(0);
        vo.setPageSize(10);
        vo.setGroupNameKey("通用词-cpc");
        Result<PageResult<HeadlinesAdPlanResult>> pageResultResult = headlinesService.queryAdPlanList(vo);
        log.info("结果： {}", pageResultResult);
    }

    @Test
    public void getAccountInfoDataOverView() {
        GetDataOverviewVO vo = new GetDataOverviewVO();
        vo.setEa("88146");
        vo.setStartTime(new Date(1733155200000L));
        vo.setEndTime(new Date(1735747199000L));
        vo.setAdAccountId("c83be783fa4a4a248a52df81262627ae");
        vo.setSource("3");
        //vo.setAdPlanName("通用词-cpc");
        Result<GetDataOverviewResult> dataOverview = accountService.getDataOverview(vo);
        log.info("结果 : {}", dataOverview);
    }

    @Test
    public void syncKeyword() {
//        refreshDataManager.syncAdKeywordToMarketingKeywordObj("74164", AdSourceEnum.getSourceByValue("3"));
    }

    @Test
    public void queryAdData() {
        QueryHeadlinesAdPlanListVO vo = new QueryHeadlinesAdPlanListVO();
        vo.setPageNum(0);
        vo.setPageSize(10);
        vo.setStartTime(new Date(1637376874000L));
        vo.setEndTime(new Date(1638157844628L));
        vo.setSource(AdSourceEnum.getSourceByValue("3"));
        vo.setEa("74164");
        vo.setAdId(****************L);
        vo.setAdAccountId("3d01871e214646c99fdf01b6b5c4289a");
        Result<HeadlinesAdPlanDataResult> result = headlinesService.queryAdData(vo);
        log.info("result:{}", result);
    }
    @Test
    public void getLeadsCount() {
        int count = campaignDataManager.getLeadsCountByMarketingEvent("74164", Lists.newArrayList("6127411b7b098b00017d4e6a"), new Date(1637424000000L).getTime(), new Date(1638028800000L).getTime(), "other");
        log.info("result:{}", count);
    }

    @Test
    public void getHeadlinesAdDetail() {
        QueryHeadlinesAdDetailVO vo = new QueryHeadlinesAdDetailVO();
        vo.setEa("74164");
        vo.setId("****************");
        vo.setAdAccountId("3d01871e214646c99fdf01b6b5c4289a");
//        vo.setStartTime(DateUtil.getSomeDay(new Date(), -30));
//        vo.setEndTime(DateUtil.getSomeDay(new Date(), -1));
        Result<HeadlinesAdDetailResult> detailResult = headlinesService.queryAdDetail(vo);
        log.info("result:{}", detailResult);
    }

    @Test
    public void getClueTrendGraph() {
        ClueTrendGraphDataVo vo = new ClueTrendGraphDataVo();
        vo.setEa("74164");
        vo.setMarketingEventId("612741347b098b00017d5201");
        vo.setStartTime(DateUtil.getSomeDay(new Date(), -6));
        vo.setEndTime(new Date());
        vo.setSource("巨量引擎");
        Result<List<CluesTrendGraphDataResult>> result = headlinesService.queryCluesTrendGraphList(vo);
        log.info("result:{}", result);
    }

    @Test
    public void getClueCount() {
        List<String> ids = headlinesAdDAO.getAdLinkSubMarketingEventIds("74164", "", "巨量引擎");
        int clueCount = campaignDataManager.getLeadsCountByMarketingEvent("74164", ids, null, null, "other");
        log.info("clueCount:{}", clueCount);
    }

    @Test
    public void EnumTest() {
        String result = HeadlinesCampaignStatusEnum.getDescByStatus(0);
        String sourceByValue = AdSourceEnum.getSourceByValue("3");
        log.info("result is:{}, sourceByValue:{}", result, sourceByValue);
    }

    @Test
    public void updateAdLeadsMappingDataTest() {
        AdLeadsMappingVO vo = new AdLeadsMappingVO();
        vo.setEa("74164");
        vo.setFsUserId(1);
        vo.setCrmPoolId("test123123");
        vo.setCrmRecordType("test_record_type");
        vo.setCustomFuncApiName("test_custom_api_name");
        vo.setSource("巨量引擎");
        vo.setAdAccountId("3d01871e214646c99fdf01b6b5c4289a");
        Result result = headlinesService.updateAdLeadsMappingData(vo);
        log.info("result:{}", result);
    }

    @Test
    public void queryAdLeadsMappingDataTest() {
        QueryAdLeadsMappingVO vo = new QueryAdLeadsMappingVO();
        vo.setEa("74164");
        vo.setSource("3");
        Result<QueryAdLeadsMappingDataResult> result = headlinesService.queryAdLeadsMappingData(vo);
        log.info("result:{}", result);
    }

    @Test
    public void syncClueTest() {
        headlinesAdMarketingManager.syncClueDataToClueObj("74164", "5e45f266ceec4841b10b08cb8e1f767b", "巨量引擎");
    }

    @Test
    public void appidReloadTest() {
        QueryHeadlinesAppIdVO vo = new QueryHeadlinesAppIdVO();
        Result<QueryHeadlinesAppIdResult> result = headlinesService.queryHeadlinesAppId(vo);
        log.info("result:{}", result);
    }

    @Test
    public void headlinesAdTest() {
//        List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.pageAd("74164", null, null, 1710345100552253L, null, null);
//        log.info("result:{}", headlinesAdEntityList);

        Integer count = headlinesAdDataDAO.queryClueCountByAdId("74164", "7fbaba87b9d2476d9c125413c4bcf78e", 1702629619026964L);
        log.info("count:{}", count);
    }


    @Test
    public void refreshDataTest() {
        AdObjectFieldMappingEntity marketingEventObjMappingEntity = adObjectFieldMappingDAO.getByApiName("74164", CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = refreshDataManager.createMarketingEventObjByCampaign("74164", "北京移动分享", "阿里-营销组", "巨量引擎", marketingEventObjMappingEntity, null, null, null, null, null);
        log.info("res:{}", result);
    }
    @Test
    public void queryNewHeadlinesCampaignList() throws ParseException {
        QueryHeadlinesAdPlanListVO  vo = new QueryHeadlinesAdPlanListVO();
        vo.setEa("88146");
        vo.setAdAccountId("c83be783fa4a4a248a52df81262627ae");
        vo.setSource("巨量引擎");
        vo.setPageNum(1);
        vo.setPageSize(20);
        vo.setStartTime(new Date(1733155200000L));
        vo.setEndTime(new Date(1735747199000L));
        Result<PageResult<HeadlinesAdPlanResult>> result = headlinesService.queryNewHeadlinesCampaignList(vo);
        log.info("res:{}", result);
    }

    @Test
    public void refreshHeadlinesProjectT() {

//        headlinesAdMarketingManager.refreshHeadlinesProject(accountDAO.queryAccountById("b2f6f708cfd840d787db0879cbef070a"));
        headlinesAdMarketingManager.refreshHeadlineProjectData(accountDAO.queryAccountById("","b2f6f708cfd840d787db0879cbef070a"), -1);
//        headlinesAdMarketingManager.refreshH
//        eadlinesPromotion(accountDAO.queryAccountById("b2f6f708cfd840d787db0879cbef070a"));
//        headlinesAdMarketingManager.refreshHeadlinePromotionData(accountDAO.queryAccountById("b2f6f708cfd840d787db0879cbef070a"), -1);
    }

    @Test
    public void refreshHeadlinesProjectData() {

        headlinesAdMarketingManager.refreshHeadlineProjectData(accountDAO.queryAccountById("","b2f6f708cfd840d787db0879cbef070a"), 26);

    }


    @Test
    public void refreshHeadlinesPromotionData() {

        headlinesAdMarketingManager.refreshHeadlinePromotionData(accountDAO.queryAccountById("","b2f6f708cfd840d787db0879cbef070a"), 26);

    }

    @Test
    public void test() {
        HeadlinesAdEntity headlinesAdEntity = new HeadlinesAdEntity();
        headlinesAdEntity.setId("737473e162d347cf91543bc8f02bad22");
        headlinesAdEntity.setAdId(1718116956591177L);
        headlinesAdEntity.setCampaignId(1730081992137999L);
        headlinesAdEntity.setEa("83668");
        headlinesAdEntity.setUpdateTime(new Date());
        headlinesAdEntity.setAdName("更新试试");
        headlinesAdEntity.setRefreshTime(new Date());
        Integer inventory = null;
        headlinesAdEntity.setInventoryCatalog(inventory == null ? -1 : inventory);
        Integer deliver = null;
        headlinesAdEntity.setDeliveryRange(deliver == null ? -1 : deliver);
        Integer optStatus = null;
        headlinesAdEntity.setOptStatus(optStatus == null ? -1 : optStatus);
        Integer status = null;
        headlinesAdEntity.setStatus(status == null ? -1 : status);
        headlinesAdEntity.setInventoryType(Lists.newArrayList(""));
        System.out.println(CollectionUtils.isEmpty(Lists.newArrayList("")));
        headlinesAdDAO.batchUpdateAd("83668", "b2f6f708cfd840d787db0879cbef070a", Lists.newArrayList(headlinesAdEntity));
    }

    @Test
    public void refreshPrototypeRoomAccountDataTest() {
        headlinesAdMarketingManager.refreshPrototypeRoomAccountData("82255", 1000);
    }
}
