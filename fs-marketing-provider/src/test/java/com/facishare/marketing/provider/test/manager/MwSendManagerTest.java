package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.SmsContentParam;
import com.facishare.marketing.common.typehandlers.value.SmsContentParamList;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.entity.sms.mw.MwSendDetailEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSendEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.innerArg.mw.CreateSendTaskArg;
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.open.emailproxy.common.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author JuneHua
 * Date 2020/9/20 11:11
 * Version 1.0
 */

@Slf4j
public class MwSendManagerTest extends BaseTest {

    @Autowired
    MwSendManager mwSendManager;

    @Autowired
    private MwSmsSendDao mwSmsSendDao;

    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;

    @Test
    public void test() {
        List<MwSendDetailEntity> list = new LinkedList<>();
        MwSendDetailEntity entity1 = new MwSendDetailEntity();
        MwSendDetailEntity entity12 = new MwSendDetailEntity();
        entity1.setPhone("10086");
        entity1.setContent("点击https://www.ceshi112.com/ec/cml-marketing/release/web/cml-marketing.html?byshare=1&_hash=/cml/h5/conference_detail&id=d246ba62c1c440679df3561a30e7162a&spreadFsUid=1112&fsUserId=1112&marketingActivityId=5f66bf5c86613800018128bd&  回T退订 ");
        list.add(entity1);
        entity12.setPhone("10010");
        entity12.setContent("点击https://www.ceshi112.com/ec/cml-marketing/release/web/cml-marketing.html?byshare=1&_hash=/cml/h5/conference_detail&id=d246ba62c1c440679df3561a30e7162a&spreadFsUid=1112&fsUserId=1112&marketingActivityId=5f66bf5c86613800018128bd&  回T退订 ");
        list.add(entity12);
        Result<List<MwSendDetailEntity>> listResult = Result.newSuccess(list);
        CreateSendTaskArg arg = new CreateSendTaskArg();
        List<String> longUrls = new LinkedList<>();
        longUrls.add("https://www.ceshi112.com/ec/cml-marketing/release/web/cml-marketing.html?byshare=1&_hash=/cml/h5/conference_detail&id=d246ba62c1c440679df3561a30e7162a&spreadFsUid=1112&fsUserId=1112&marketingActivityId=5f66bf5c86613800018128bd& ");
        arg.setLongUrls(longUrls);
//        Result<List<MwSendDetailEntity>> listResult1 = mwSendManager.changeLongUrl2ShortUrlWithPhone(listResult, arg);
//        System.out.println(listResult1);
    }

    @Test
    public void checkConferenceEnrollTemplate(){
        String templateId = "4aae6b9235464aebb95a0c5ffcb6db3f";
//        Result result = mwSendManager.checkConferenceEnrollTemplate(templateId);
//        System.out.print("checkConferenceEnrollTemplate result:"+result);
        /*String templateId = "4aae6b9235464aebb95a0c5ffcb6db3f";
        Result result = mwSendManager.checkConferenceEnrollTemplate(templateId);
        System.out.print("checkConferenceEnrollTemplate result:"+result);*/
    }

    @Test
    public void doTrackSmsLink() {
        List<MwSendDetailEntity> detailEntityList2 = new LinkedList<>();
        MwSendDetailEntity entity3 = new MwSendDetailEntity();
        entity3.setSendId("3bf9a92c07494d459e982629bad6f3dc");
        entity3.setPhone("18520875893");
        entity3.setContent("欢迎参见客户大会 https://fs8.ceshi112.com/o71Ws8 相关资料： 点击https://fs8.ceshi112.com/k63JL1 回T退订");
        detailEntityList2.add(entity3);
        MwSendDetailEntity entity4 = new MwSendDetailEntity();
        entity4.setSendId("3bf9a92c07494d459e982629bad6f3dc");
        entity4.setPhone("15627861090");
        entity4.setContent("点击https://wxaurl.cn/XYIUhrcam4e 回T退订点击https://fs8.ceshi112.com/zi1TTc 回T退订微页面小程序链接： 点击https://wxaurl.cn/QVLmyqAbWmb 回T退订点击https://wxaurl.cn/xmU81XH98Xl 回T退订");
        detailEntityList2.add(entity4);
        MwSendDetailEntity entity5 = new MwSendDetailEntity();
        entity5.setSendId("3bf9a92c07494d459e982629bad6f3dc");
        entity5.setPhone("13662621353");
        entity5.setContent("欢迎参见客户大会 https://fs8.ceshi112.com/o71Ws8 相关资料： 点击https://fs8.ceshi112.com/k63JL1 回T退订");
        detailEntityList2.add(entity5);
//        mwSendManager.doTrackSmsLink("74164", detailEntityList2);
        System.out.println("=============");
        detailEntityList2.forEach(System.out::println);
    }

    @Test
    public void insert() {
        MwSmsSendEntity mwSmsSendEntity = mwSmsSendDao.getSMSSendById("88146","6b9dd4ace02a46579349fec3074efd2b");
        log.info("原来的数据: {}", mwSmsSendEntity);
        mwSmsSendEntity.setId(UUIDUtil.getUUID());
        mwSmsSendEntity.setBusinessType("qq");
        mwSmsSendEntity.setReceiver("ww");
        mwSmsSendEntity.setSendNode("ee");
        mwSmsSendEntity.setNodeType("rr");
        mwSmsSendDao.insertSendEntity(mwSmsSendEntity);
        mwSmsSendEntity = mwSmsSendDao.getSMSSendById("88146", mwSmsSendEntity.getId());
        log.info("最新的数据: {}", mwSmsSendEntity);
    }

    @Test
    public void testUpdateObj() {
        List<MwSendDetailEntity> mwSendDetailEntities = mwSmsSendDao.querySendDetailBySendId("88146", "327adc27767f47bb9404ff78c0c4507a", null);
        mwSendDetailEntities = mwSendDetailEntities.stream().filter(e -> e.getId().equals("35458ea5c32046548fa660d355fb7e50")).collect(Collectors.toList());
        mwSendDetailEntities.forEach(e -> {
            e.setStatus(3);
            e.setErrCode("M2:0040");
        });
        Map<String, List<MwSendDetailEntity>> eaToErrorSendDetailMap = new HashMap<>();
        eaToErrorSendDetailMap.put("74164", mwSendDetailEntities);
        mwSendManager.sendSmsSendErrorToMQ(eaToErrorSendDetailMap);
     //   ThreadUtil.sleepIngore(1000000L);
    }

    @Test
    public void testHandleContent(){
        /*MwSmsSendEntity mwSmsSendEntity = new MwSmsSendEntity();
        mwSmsSendEntity.setObjectId("63ec9ecdf11e940001f21cde");//  63e0de3d29b4420001154187
        MwSmsTemplateEntity mwSmsTemplateEntity = new MwSmsTemplateEntity();
        mwSmsTemplateEntity.setContent("当前市场活动为${MarketingEventObj.name}，父级市场活动为${MarketingEventObj.parent_id.name}，单选2为${MarketingEventObj.field_0A1vx__c}，多选2为${MarketingEventObj.field_a148e__c}，" +
                "时间2为${MarketingEventObj.field_b66d1__c}，" +
                "部门2为${MarketingEventObj.field_yY3ta__c}， 百分数为${MarketingEventObj.field_25cSG__c}，创建人为${MarketingEventObj.created_by}，创建人手机为${MarketingEventObj.created_by.phone}");
        SmsContentParamList smsContentParams = new SmsContentParamList();
        smsContentParams.add(new SmsContentParam(".##key1", "h5obj", "http://www.h5obj.com"));
        smsContentParams.add(new SmsContentParam(".##key2", "minip", "http://www.minip.com"));
        smsContentParams.add(new SmsContentParam(".##key3", "surl", "http://www.surl.com"));
        mwSmsTemplateEntity.setSmsContentParam(smsContentParams);
        CreateSendTaskArg createSendTaskArg = new CreateSendTaskArg();
        createSendTaskArg.setEa("74164");
        String result = mwSendManager.handleContent(mwSmsSendEntity, mwSmsTemplateEntity, createSendTaskArg);
        System.out.println("yes:" + result);*/

        String templateId = "acdef759e08144a49c328458fb31c803";
        MwSmsTemplateEntity templateEntity = mwSmsTemplateDao.getTemplateById(templateId,"88146");
        MwSmsSendEntity mwSmsSendEntity = new MwSmsSendEntity();
        CreateSendTaskArg createSendTaskArg = new CreateSendTaskArg();
        createSendTaskArg.setEa("74164");
        String result = mwSendManager.handleContent(mwSmsSendEntity, templateEntity, createSendTaskArg);
        System.out.println("yes:" + result);
    }

    @Test
    @Ignore
    public void fixSmsSendRecordObjSendStatusTest() {
        mwSendManager.fixSmsSendRecordObjSendStatus(null);
    }
}
