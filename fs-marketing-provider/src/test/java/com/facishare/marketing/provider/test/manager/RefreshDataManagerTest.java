/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSONArray;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.bo.advertise.SyncKeywordServicePlanBO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.baidu.AdKeywordDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.AdKeywordEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * Created by zhengh on 2021/2/24.
 */
@Slf4j
public class RefreshDataManagerTest extends BaseTest{
    @Autowired
    private RefreshDataManager refreshDataManager;

    @Autowired
    private BaiduAccountDAO baiduAccountDAO;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;

    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;

    @Autowired
    private AdKeywordDAO adKeywordDAO;


    @Test
    public void refreshAdDataByEa(){
//        refreshDataManager.refreshAdDataByEa();
    }

    @Test
    public void queryDuplicateKeywordsReportDateByCampaign(){
//        AdAccountEntity adAccountEntity = baiduAccountDAO.queryAccountByEaAndSource("74164", "百度");
//        String source = "百度";
//        Long campaign =142682017L;
//        String lastDayDate = "2021-02-01";
//        Map<String, AdKeywordReportDataDTO> dto = refreshDataManager.queryDuplicateKeywordsReportDateByCampaign(adAccountEntity, source, campaign, lastDayDate);
//        System.out.print("queryDuplicateKeywordsReportDateByCampaign dto :"+ dto);
    }

    @Test
    public void a(){
       // Long[] keywords = [************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, 170157456094, 219897693164, 170157453906, 170157453977, 170157453976, 170157452495, 170157451588, 170157451563, 170157453825, 170157452660, 170157452682, 170157456135, 170157451574, 170157451530, 170157451528, 170157451535, 170157451533, 170157451472, 170157454866, 219897693176, 170157454865, 170157454871, 170157454747, 170157452465, 170157456144, 219897693185, 219897693232, 170157456149, 170157453944, 170157453951, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************];
    }

    //指定同步某天的数据
    @Test
    public void syncMarketingTermServingLinesData(){
//        AdAccountEntity adAccountEntity = baiduAccountDAO.queryAccountByEaAndSource("79398", "百度");
//        String source = "百度";
//        String[] lastDayDates = {"2021-03-16"};
//        for (int i = 0; i < lastDayDates.length; i++) {
//            refreshDataManager.syncMarketingTermServingLinesData(adAccountEntity, source, lastDayDates[i]);
//        }
    }

    @Test
    public void refreshBaiduData(){
//        String ea = "74164";
//        String source = "百度";
//        refreshDataManager.refreshBaiduData(ea,"", source);
    }

    @Test
    public void setBaiduDeleteCampaignDataStatus(){
        List<AdAccountEntity> adAccountEntities = baiduAccountDAO.queryAccountByEaAndSource("76301", "","百度");
        List<Long> campaignIds = Lists.newArrayList(172750424L, 172750426l);
      //  refreshDataManager.setBaiduDeleteCampaignDataStatus(campaignIds, adAccountEntity);
    }

    @Test
    public void syncMarketingTermServingLinesDataByKeywordTest() {
        ///AdAccountEntity adAccountEntity= baiduAccountDAO.queryAccountById("f266441754984118bca31f4e15b3d731");

        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("74164");
        adAccountEntity.setId("9ee872fcc56f410583a20c7894b327a8");
        String source = AdSourceEnum.SOURCE_BAIDU.getSource();
        adAccountEntity.setSource(source);
        String date = DateUtil.parse(DateUtil.getSomeDay(new Date(), -30), DateUtil.DATE_FORMAT_DAY);
        refreshDataManager.syncMarketingTermServingLinesDataByKeyword(adAccountEntity, source, date);
    }

    @Test
    public void queryCrmMarketingKeywordPlanByUserName() {
        String ea = "74164";
        Map<String, String> b = new HashMap<>();
        List<String> a = Lists.newArrayList(b.get("1"));
        Page<ObjectData> objectDataPage = refreshDataManager.queryCrmMarketingKeywordPlanByUserName(ea, "dd", "ddd", a, 6);
        log.info("dddddd {}", objectDataPage);
    }

    @Test
    public void queryByMarketingEventIdListTest() {
        String ea = "74164";
        List<BaiduCampaignEntity> objectDataPage = baiduCampaignDAO.queryByMarketingEventIdList(ea, Lists.newArrayList("619c66007ad42d00017c49f0","619c65fd7006310001a82764"));
        log.info("dddddd {}", objectDataPage);
    }

    @Test
    public void fixBaiduCampaignMarketingEventId() {
        refreshDataManager.fixBaiduCampaignMarketingEventId("88146");
    }

    @Test
    public void batchSyncCampaignToMarketingEventObjV2Test() {
        String ea = "83668";
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById("8b524863784b46f9a46b3b7f6557befb");
        HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignDAO.queryCampaignByCampaignId(ea, 7413179220161167386L);
        List<AdCampaignEntity> adCampaignEntityList = Lists.newArrayList(headlinesCampaignEntity);
        refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, adCampaignEntityList, AdSourceEnum.SOURCE_JULIANG.getSource());
    }

    @Test
    public void batchSyncAdGroupToSubMarketingEventObjTest() {
        String ea = "83668";
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById("8b524863784b46f9a46b3b7f6557befb");
        AdvertiserAdEntity  advertiserAdEntityList = headlinesAdDAO.queryAdByAdId(ea, "8b524863784b46f9a46b3b7f6557befb", 1718116956591163L);
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, Lists.newArrayList(advertiserAdEntityList), AdSourceEnum.SOURCE_JULIANG.getSource());
    }

    @Test
    public void batchSyncTencentAdGroupToSubMarketingEventObjTest() {
        String ea = "83668";
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById("b3a98f88760344d3b64041727d579c97");
        List<TencentAdGroupEntity>  tencentAdGroupEntityList = tencentAdGroupDAO.queryByNameList(ea, Lists.newArrayList("腾讯子-重名测试4"));
        List<AdvertiserAdEntity> advertiserAdEntityList = Lists.newArrayList();
        for (TencentAdGroupEntity tencentAdGroupEntity : tencentAdGroupEntityList) {
            AdvertiserAdEntity advertiserAdEntity = new AdvertiserAdEntity();
            advertiserAdEntity.setId(tencentAdGroupEntity.getId());
            advertiserAdEntity.setEa(tencentAdGroupEntity.getEa());
            advertiserAdEntity.setAdAccountId(tencentAdGroupEntity.getAdAccountId());
            advertiserAdEntity.setAdId(tencentAdGroupEntity.getAdgroupId());
            advertiserAdEntity.setAdName(tencentAdGroupEntity.getAdgroupName());
            advertiserAdEntity.setSubMarketingEventId(tencentAdGroupEntity.getSubMarketingEventId());
            advertiserAdEntity.setSource(AdSourceEnum.SOURCE_TENCETN.getSource());
            advertiserAdEntity.setCampaignId(tencentAdGroupEntity.getCampaignId());
            advertiserAdEntityList.add(advertiserAdEntity);
        }
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, advertiserAdEntityList, AdSourceEnum.SOURCE_TENCETN.getSource());
    }

    @Test
    public void getEachPlatformLeadCount() {
        refreshDataManager.getEachPlatformLeadCount("2023-01-01 00:00:00,2024-10-01 23:59:59");
    }

    @Test
    public void createCrmMarketingKeywordPlan() {
        refreshDataManager.createCrmMarketingKeywordPlan("88146", "aa", "", "","ttt", "66f93cbbf7d17900071db5e2", "dffff", null);
    }

    @Test
    public void syncKeywordServingPlanV2() {
       // refreshDataManager.syncKeywordServingPlanV2("88146", "e0da465401a34e528a35c7907bcae3ee", AdSourceEnum.SOURCE_TENCETN.getSource());
        refreshDataManager.syncKeywordServingPlanV2("88146", "8b524863784b46f9a46b3b7f6557qqq", AdSourceEnum.SOURCE_BAIDU.getSource());
    }

    @Test
    public void syncTermServingLinesByKeywordV2() {
        String accountId = "8b524863784b46f9a46b3b7f6557befb";
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(accountId);
        String json = "[\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\",\"************\"]\n";
        List<Long> keywordIds = JSONArray.parseArray(json, Long.class);
        List<AdKeywordEntity> adKeywordEntityList = adKeywordDAO.queryAdKeywordByIds("83668", accountId, keywordIds);
        refreshDataManager.syncTermServingLinesByKeywordV2(adAccountEntity, adAccountEntity.getSource(), adKeywordEntityList, "2024-12-04");
    }


    @Test
    public void syncMarketingTermServingLinesDataByKeyword() {
        String accountId = "8b524863784b46f9a46b3b7f6557qqq";
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(accountId);
        refreshDataManager.syncMarketingTermServingLinesDataByKeyword(adAccountEntity, adAccountEntity.getSource(), "2025-01-09");
    }
}
