/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.marketingactivity;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.marketingactivity.*;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.UpdateMarketingActivityResult;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.api.vo.qywx.QywxAttachmentsVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupSendMessageVO;
import com.facishare.marketing.common.enums.sms.mw.MwSendTaskTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SaveOrSendTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsGroupTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsSceneTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.manager.marketingactivity.SendGroupSmsManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.enterpriserelation2.arg.ListDownstreamEmployeesByDownstreamOuterTenantIdsArg;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.ListDownstreamEmployeesByDownstreamOuterTenantIdsResult;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.fxiaoke.paasauthrestapi.arg.FindTenantGroupDataByGroupIdArg;
import com.fxiaoke.paasauthrestapi.common.data.PaasTenantGroupContextData;
import com.fxiaoke.paasauthrestapi.result.TenantGroupDataResult;
import com.fxiaoke.paasauthrestapi.service.PaasTenantGroupService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/26.
 */
@Slf4j
public class MarketingActivityServiceTest extends BaseTest {
    private static final String ea = "74164";  //String  ea="fsceshi019";
    private static final int operatorFsUserId = -10000;
    @Autowired
    private MarketingActivityService marketingActivityService;
    @Autowired
    private SendGroupSmsManager sendGroupSmsManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private PublicEmployeeService publicEmployeeService;
    @Autowired
    private PaasTenantGroupService paasTenantGroupService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Test
    public void getidBygroup(){
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
        headerObj.setAppId("FSAID_1149101c");
            ListDownstreamEmployeesByDownstreamOuterTenantIdsArg listUserArg = new ListDownstreamEmployeesByDownstreamOuterTenantIdsArg();
            listUserArg.setUpstreamEa(ea);
            listUserArg.setDownstreamOuterTenantIds(Arrays.asList(300016575L));
//        根据下游外部企业账号获取对接人id
            /*RestResult<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> listUserResult = publicEmployeeService.listDownstreamEmployeesByDownstreamOuterTenantIds(headerObj, listUserArg);
            List<Long> userList = listUserResult.getData().stream().map(data -> data.getOuterUid()).collect(Collectors.toList());*/

        com.fxiaoke.paasauthrestapi.common.data.HeaderObj yiye = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));

        FindTenantGroupDataByGroupIdArg findTenantGroupDataByGroupIdArg = new FindTenantGroupDataByGroupIdArg();
        findTenantGroupDataByGroupIdArg.setGroupId("60a71142869ed20001e6007f");
        PaasTenantGroupContextData paasTenantGroupContextData = new PaasTenantGroupContextData();
        paasTenantGroupContextData.setTenantId("1");
        paasTenantGroupContextData.setUserId("1000");
        findTenantGroupDataByGroupIdArg.setContext(paasTenantGroupContextData);
        com.fxiaoke.paasauthrestapi.common.result.Result<List<TenantGroupDataResult>> tenantGroupDataByGroupId = paasTenantGroupService.findTenantGroupDataByGroupId(yiye, findTenantGroupDataByGroupIdArg);
        List<TenantGroupDataResult> result = tenantGroupDataByGroupId.getResult();
        System.out.println(666);
    }

    @Test
    public void add() {
        AddMarketingActivityArg arg = new AddMarketingActivityArg();
        arg.setSpreadType(3);
        arg.setMarketingEventId("5fa37639a2eca70001dc5b50");
        AddMarketingActivityArg.MarketingActivityGroupSenderVO vo = new AddMarketingActivityArg.MarketingActivityGroupSenderVO();
        arg.setMarketingActivityGroupSenderVO(vo);
        vo.setEventType(2);
        //vo.setLiveCustomizeFormDataUserIds(Lists.newArrayList("750259fc1b3449dba325a75162926d9d", "83ca04294518480592977a8f8a24bcc5", "60c0c326207b41a08b6dc4a4a66fc834"));
        vo.setSceneType(4);
        vo.setSendRange(6);
        vo.setTemplateContent("aaaaaaaaaaaaaa{title}");
        vo.setTemplateName("一一一直播一一一");
        vo.setType(1);
        marketingActivityService.addMarketingActivity("74164", 1112, arg, true);
    }

    @Test
    public void add1() {
        AddMarketingActivityArg arg = JSON.parseObject("{\n" +
                "    \"spreadType\": 2,\n" +
                "    \"wechatMessageType\": 1002,\n" +
                "    \"marketingEventId\": \"653b239cd530d600015f7a03\",\n" +
                "    \"materialInfos\": [\n" +
                "        {\n" +
                "            \"contentType\": 3,\n" +
                "            \"objectId\": \"e1d3d78bf84d4ac6a616e968761b2c87\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"marketingActivityAuditData\": {\n" +
                "        \"sendGroup\": \" 公总号人群 (2)\"\n" +
                "    },\n" +
                "    \"weChatTemplateMessageVO\": {\n" +
                "        \"appId\": \"FSAID_10b07bb8\",\n" +
                "        \"title\": \"111111222222\",\n" +
                "        \"sendObject\": {\n" +
                "            \"tagIdList\": [],\n" +
                "            \"sendRange\": 2,\n" +
                "            \"filters\": [],\n" +
                "            \"crowd\": [\n" +
                "                {\n" +
                "                    \"_parentIds\": [\n" +
                "                        \"-2\",\n" +
                "                        \"-3\"\n" +
                "                    ],\n" +
                "                    \"id\": \"27f76e76e1ff4cf3a656edcb280e30f5\",\n" +
                "                    \"name\": \" 公总号人群 (2)\",\n" +
                "                    \"type\": 2,\n" +
                "                    \"___visited___\": true,\n" +
                "                    \"_parentId\": \"-2\",\n" +
                "                    \"_domParentId\": \"-2\",\n" +
                "                    \"_selected\": true,\n" +
                "                    \"_time\": 3,\n" +
                "                    \"_semi\": false,\n" +
                "                    \"id2\": \"\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"weChatOfficialTemplateId\": \"m9IWmD8Bx4uzpqh0H3HV9Mj6Flz2OgnbnhfMoXyCoxk\",\n" +
                "        \"templateMessageDatas\": {\n" +
                "            \"title\": \"打卡完成通知\",\n" +
                "            \"dataList\": [\n" +
                "                {\n" +
                "                    \"key\": \"short_thing2\",\n" +
                "                    \"color\": null,\n" +
                "                    \"value\": \"{微信用户昵称}\",\n" +
                "                    \"title\": \"打卡天数\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"time3\",\n" +
                "                    \"color\": null,\n" +
                "                    \"value\": \"{职务1}\",\n" +
                "                    \"title\": \"打卡日期\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"thing5\",\n" +
                "                    \"color\": null,\n" +
                "                    \"value\": \"{市场活动名称}\",\n" +
                "                    \"title\": \"打卡人\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"thing7\",\n" +
                "                    \"color\": null,\n" +
                "                    \"value\": \"{手机}\",\n" +
                "                    \"title\": \"打卡项目\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"redirectType\": 1,\n" +
                "        \"tagIdList\": [],\n" +
                "        \"sendRange\": 4,\n" +
                "        \"filters\": [],\n" +
                "        \"marketingUserGroupIds\": [\n" +
                "            \"27f76e76e1ff4cf3a656edcb280e30f5\"\n" +
                "        ],\n" +
                "        \"type\": 1,\n" +
                "        \"fixedTime\": \"\",\n" +
                "        \"materialType\": 3,\n" +
                "        \"redirectUrl\": \"https://crm.ceshi112.com/proj/page/marketing&source=1#/conference/detail?marketingActivityId=!!marketingActivityId!!&marketingEventId=653b239cd530d600015f7a03&wxAppId=!!wxAppId!!&ea=88146&objectId=e1d3d78bf84d4ac6a616e968761b2c87\",\n" +
                "        \"materialId\": \"e1d3d78bf84d4ac6a616e968761b2c87\"\n" +
                "    },\n" +
                "    \"referer\": \"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/wechat/tplmsg?appId=FSAID_10b07bb8\",\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36\"\n" +
                "}", AddMarketingActivityArg.class);
        Result<AddMarketingActivityResult> addMarketingActivityResultResult = marketingActivityService.addMarketingActivity("88146", 1000, arg, true);
        System.out.println(addMarketingActivityResultResult);
    }
    @Test
    public void listMarketingActivity() {
        ListMarketingActivityArg activityArg = new ListMarketingActivityArg();
        activityArg.setPageNum(6);
        activityArg.setPageSize(1);
        activityArg.setSpreadType(2);
//        activityArg.setStatus(1);
//        activityArg.setMarketingEventName("");
//        activityArg.setName("1");
//          activityArg.setCreateTime(System.currentTimeMillis());
//          activityArg.setIds(Lists.newArrayList("5c7796966d94000001f9b283","5c78f1a464aab40001478420","5c7797096d94000001f9b2de"));
//        activityArg.setOwnerIds(Lists.newArrayList("1000","2243"));
//        activityArg.setIsMankeep(true);
//        activityArg.setIsAsc(false);
//        activityArg.setSortFieldName("createTime");
//        activityArg.setBeginTime(1551369600000L);
//        activityArg.setEndTime(1551887999999L);
        PageResult<MarketingActivityResult> result = marketingActivityService.listMarketingActivity("74164", 1123, activityArg).getData();
        System.out.println("----------------");
        System.out.println(result.getResult().size());
        System.out.println("------------");
        System.out.println(result);
        log.info("result:{}", result);
    }



    @Test
    public void addMarketingPartnerActivity() {
//615562ef1513ad000121e240
        AddMarketingActivityArg addMarketingActivityArg = new AddMarketingActivityArg();
        addMarketingActivityArg.setSpreadType(7);
        addMarketingActivityArg.setMarketingEventId("616e64982409130001e7a0df");
        AddMarketingActivityArg.MarketingActivityPartnerNoticeSendVO marketingActivityPartnerNoticeSendVO =  new AddMarketingActivityArg.MarketingActivityPartnerNoticeSendVO();
        List<AddMarketingActivityArg.MaterialInfo> m = new ArrayList<>();
        AddMarketingActivityArg.MaterialInfo am = new AddMarketingActivityArg.MaterialInfo();
        am.setObjectId("a013e6ee8ba2427b8dc9470f5f7aa97d");
        am.setContentType(3);
        m.add(am);
        addMarketingActivityArg.setMaterialInfos(m);
        marketingActivityPartnerNoticeSendVO.setTitle("h5");
        marketingActivityPartnerNoticeSendVO.setStartTime(1633683746000L);
        marketingActivityPartnerNoticeSendVO.setEndTime(1637303314000L);
        marketingActivityPartnerNoticeSendVO.setSendType(1);
//        marketingActivityPartnerNoticeSendVO.setContentType(1);
//        marketingActivityPartnerNoticeSendVO.setContent("3f78ec65a6e746a3a49328187e9006ba");


        marketingActivityPartnerNoticeSendVO.setContentType(3);
        marketingActivityPartnerNoticeSendVO.setContent("a013e6ee8ba2427b8dc9470f5f7aa97d");
        marketingActivityPartnerNoticeSendVO.setDescription("伙伴营销testD");
        marketingActivityPartnerNoticeSendVO.setCoverPath("A_202110_19_6e7a1c81552746c481c7e154655aaefc.jpg");
        AddMarketingActivityArg.PartnerNoticeVisibilityArg partnerNoticeVisibilityArg = new AddMarketingActivityArg.PartnerNoticeVisibilityArg();
        ArrayList<String> objects = new ArrayList<>();
        objects.add("300016575");
        partnerNoticeVisibilityArg.setEaList(objects);
        partnerNoticeVisibilityArg.setTenantGroupIdList(new ArrayList<>());

        marketingActivityPartnerNoticeSendVO.setPartnerNoticeVisibilityArg(partnerNoticeVisibilityArg);
        addMarketingActivityArg.setMarketingActivityPartnerNoticeSendVO(marketingActivityPartnerNoticeSendVO);

        AddMarketingActivityResult addMarketingActivityResult = marketingActivityService.addMarketingActivity("74164", 1145, addMarketingActivityArg, true).getData();
        log.info("result:{}", addMarketingActivityResult);
    }

    @Test
    public void addMarketingActivity() {
//
//        AddMarketingActivityArg addMarketingActivityArg = new AddMarketingActivityArg();
//        addMarketingActivityArg.setSpreadType(1);
//        addMarketingActivityArg.setMarketingEventId("6180d52e0ea6f4000118bbb1");
//        List<AddMarketingActivityArg.MaterialInfo> m = new ArrayList<>();
//        AddMarketingActivityArg.MaterialInfo am = new AddMarketingActivityArg.MaterialInfo();
//        am.setObjectId("f29a15cfac6e4d779ab693d4a0a23462");
//        am.setContentType(3);
//        m.add(am);
//        addMarketingActivityArg.setMaterialInfos(m);
//
//        AddMarketingActivityArg.MarketingActivityNoticeSendVO marketingActivityNoticeSendVO = new AddMarketingActivityArg.MarketingActivityNoticeSendVO();
//        marketingActivityNoticeSendVO.setTitle("857-1");
//        marketingActivityNoticeSendVO.setStartTime(1633683746000L);
//        marketingActivityNoticeSendVO.setEndTime(1634547746000L);
//        marketingActivityNoticeSendVO.setSendType(1);
//        marketingActivityNoticeSendVO.setContentType(1);
//        marketingActivityNoticeSendVO.setContent("3f78ec65a6e746a3a49328187e9006ba");
//        marketingActivityNoticeSendVO.setDescription("全员营销测试单元测试");
////        marketingActivityNoticeSendVO.setAddressBookType("2");
//        AddMarketingActivityArg.NoticeVisibilityArg noticeVisibilityArg = new AddMarketingActivityArg.NoticeVisibilityArg();
//        noticeVisibilityArg.setUserIds(Lists.newArrayList());
//        noticeVisibilityArg.setOutUserIds(Lists.newArrayList("254367034833902312","195817495426254224"));
//        noticeVisibilityArg.setDepartmentIds(Lists.newArrayList(557394041));
//        marketingActivityNoticeSendVO.setNoticeVisibilityArg(noticeVisibilityArg);
//        addMarketingActivityArg.setMarketingActivityNoticeSendVO(marketingActivityNoticeSendVO);
        //全员推广
        AddMarketingActivityArg addMarketingActivityArg = JSON.parseObject("{\n" +
                "    \"spreadType\": 1,\n" +
                "    \"marketingEventId\": \"66b07bd0d83cb80007600d7e\",\n" +
                "    \"marketingActivityAuditData\": {\n" +
                "        \"executor\": \"小张\",\n" +
                "        \"sendLink\": \"https://crm.ceshi112.com/proj/page/marketing/88146#/conference/detail?marketingActivityId=&wxAppId=&qrCodeId=!!qrCodeId!!&qrCodeCategory=official_account&ea=88146&objectId=ebb114f98e5643029ecc683001511920\"\n" +
                "    },\n" +
                "    \"marketingActivityNoticeSendVO\": {\n" +
                "        \"title\": \"我是全员推广867\",\n" +
                "        \"content\": \"ebb114f98e5643029ecc683001511920\",\n" +
                "        \"contentType\": 3,\n" +
                "        \"sendType\": 1,\n" +
                "        \"startTime\": *************,\n" +
                "        \"endTime\": *************,\n" +
                "        \"description\": \"我是全员推广861我是全员推广867我是全员推广867我是全员推广867我是全员推广867我是全员推广867我是全员推广867我是全员推广861我是全员推广867\",\n" +
                "        \"noticeVisibilityArg\": {\n" +
                "            \"departmentIds\": [],\n" +
                "            \"userIds\": [\n" +
                "                1000\n" +
                "            ],\n" +
                "            \"roles\": [],\n" +
                "            \"userGroups\": []\n" +
                "        },\n" +
                "        \"coverPath\": \"C_202408_05_eb37328005db4ac0abc2a9a67d6d4014\"\n" +
                "    },\n" +
                "    \"materialInfos\": [\n" +
                "        {\n" +
                "            \"objectId\": \"ebb114f98e5643029ecc683001511920\",\n" +
                "            \"contentType\": 3\n" +
                "        }\n" +
                "    ],\n" +
                "    \"referer\": \"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/promotion-activity/staff/create\",\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36\"\n" +
                "}", AddMarketingActivityArg.class);

        //伙伴推广
        addMarketingActivityArg = JSON.parseObject("{\n" +
                "    \"spreadType\": 7,\n" +
                "    \"marketingEventId\": \"66a9a011f9b6ee0007a6743c\",\n" +
                "    \"marketingActivityAuditData\": {\n" +
                "        \"executor\": \"纷享营销通\",\n" +
                "        \"sendLink\": \"https://crm.ceshi112.com/proj/page/marketing/88146#/conference/detail?marketingActivityId=&wxAppId=&qrCodeId=!!qrCodeId!!&qrCodeCategory=official_account&ea=88146&objectId=bc2a33bd4a1346d5bc674fed2923664a\"\n" +
                "    },\n" +
                "    \"marketingActivityPartnerNoticeSendVO\": {\n" +
                "        \"title\": \"我是全员推广862\",\n" +
                "        \"staffInfoShow\": 1,\n" +
                "        \"content\": \"bc2a33bd4a1346d5bc674fed2923664a\",\n" +
                "        \"activityDetailSiteId\": \"1c9b3f2beb3c43548aa01fac1db16203\",\n" +
                "        \"contentType\": 3,\n" +
                "        \"sendType\": 1,\n" +
                "        \"startTime\": *************,\n" +
                "        \"endTime\": *************,\n" +
                "        \"description\": \"我是全员推广862我是全员推广862我是全员推广862我是全员推广862我是全员推广862\",\n" +
                "        \"partnerNoticeVisibilityArg\": {\n" +
                "            \"eaList\": [\n" +
                "                *********\n" +
                "            ],\n" +
                "            \"tenantGroupIdList\": []\n" +
                "        },\n" +
                "        \"coverPath\": \"C_202407_31_f557f47dbb77491a87fa3e8932e61d8c\"\n" +
                "    },\n" +
                "    \"materialInfos\": [\n" +
                "        {\n" +
                "            \"objectId\": \"bc2a33bd4a1346d5bc674fed2923664a\",\n" +
                "            \"contentType\": 3\n" +
                "        }\n" +
                "    ],\n" +
                "    \"referer\": \"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/partner-marketing/staff/create\",\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36\"\n" +
                "}", AddMarketingActivityArg.class);
        AddMarketingActivityResult addMarketingActivityResult = marketingActivityService.addMarketingActivity("88146", 1000, addMarketingActivityArg, true).getData();
        log.info("result:{}", addMarketingActivityResult);
    }

    @Test
    public void addQywxGroupSendMarketingActivity() {

        AddMarketingActivityArg addMarketingActivityArg = new AddMarketingActivityArg();
        addMarketingActivityArg.setSpreadType(5);
        addMarketingActivityArg.setMarketingEventId("646c280a5d51fa000140729e");
//        List<AddMarketingActivityArg.MaterialInfo> m = new ArrayList<>();
//        AddMarketingActivityArg.MaterialInfo am = new AddMarketingActivityArg.MaterialInfo();
//        am.setObjectId("f29a15cfac6e4d779ab693d4a0a23462");
//        am.setContentType(3);
//        m.add(am);
//        addMarketingActivityArg.setMaterialInfos(m);

        QywxGroupSendMessageVO groupSendMessageVO = JSON.parseObject("{\n" +
                "\t\t\"userId\": 1019,\n" +
                "\t\t\"corpId\": 81791,\n" +
                "\t\t\"enterpriseAccount\": \"zhenju0111\",\n" +
                "\t\t\"title\": \"测试2下吧333\",\n" +
                "\t\t\"ownerList\": [],\n" +
                "\t\t\"text\": {\n" +
                "\t\t\t\"content\": \"duludulu\"\n" +
                "\t\t},\n" +
                "\t\t\"msgType\": 2,\n" +
                "\t\t\"type\": 1,\n" +
                "\t\t\"sendRange\": 0,\n" +
                "\t\t\"filters\": [],\n" +
                "\t\t\"tagIdList\": [],\n" +
                "\t\t\"qywxGroupSendMessageType\": 1,\n" +
                "\t\t\"qywxGroupSendObjectType\": 1,\n" +
                "\t\t\"tagIds\": [],\n" +
                "\t\t\"departmentIds\": [],\n" +
                "\t\t\"userIds\": [\"wowx1mDAAA1vU_zuHm-vtegmH8LZd5Vw\"]\n" +
                "\t}",QywxGroupSendMessageVO.class);



        String a = "[{\n" +
                "\t\"attachmentType\": 1,\n" +
                "\t\"link\": {\n" +
                "\t\t\"title\": \"获取讲师123\",\n" +
                "\t\t\"desc\": \"666\",\n" +
                "\t\t\"picUrl\": \"https://crm.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=AF11C89121D8B30580A33FD6042DA9A242DE945B6EC193529A13F7BE2B7F159E969F0B53EEA774063BC461DB56610075623589AD54A93A59A8FFC853884C45CA917F2D52E0068AA7\",\n" +
                "\t\t\"url\": \"https://crm.ceshi112.com/ec/h5-landing/release/index.html?marketingActivityId=646c5aaba55f1c0001d95e4b&wxAppId=&ea=zhenju0111&id=67f64d6ee2874755b8c874249e77ed11&type=1\"\n" +
                "\t}\n" +
                "}, {\n" +
                "\t\"attachmentType\": 2,\n" +
                "\t\"image\": {\n" +
                "\t\t\"imagePath\": \"C_202301_05_6a6af6aa8f6f4062bc9809e11b9309e2\"\n" +
                "\t}\n" +
                "}, {\n" +
                "\t\"attachmentType\": 2,\n" +
                "\t\"image\": {\n" +
                "\t\t\"imagePath\": \"C_202301_05_6a6af6aa8f6f4062bc9809e11b9309e2\"\n" +
                "\t}\n" +
                "}, {\n" +
                "\t\"attachmentType\": 2,\n" +
                "\t\"image\": {\n" +
                "\t\t\"imagePath\": \"C_202301_05_6a6af6aa8f6f4062bc9809e11b9309e2\"\n" +
                "\t}\n" +
                "}, {\n" +
                "\t\"attachmentType\": 1,\n" +
                "\t\"link\": {\n" +
                "\t\t\"title\": \"获取讲师bbt\",\n" +
                "\t\t\"desc\": \"666\",\n" +
                "\t\t\"picUrl\": \"https://crm.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=AF11C89121D8B30580A33FD6042DA9A242DE945B6EC193529A13F7BE2B7F159E969F0B53EEA774063BC461DB56610075623589AD54A93A59A8FFC853884C45CA917F2D52E0068AA7\",\n" +
                "\t\t\"url\": \"https://crm.ceshi112.com/ec/h5-landing/release/index.html?marketingActivityId=646c5aaba55f1c0001d95e4b&wxAppId=&ea=zhenju0111&id=67f64d6ee2874755b8c874249e77ed11&type=1\"\n" +
                "\t}\n" +
                "}, {\n" +
                "\t\"attachmentType\": 1,\n" +
                "\t\"link\": {\n" +
                "\t\t\"title\": \"获取讲师abc\",\n" +
                "\t\t\"url\": \"https://crm.ceshi112.com/ec/h5-landing/release/index.html?marketingActivityId=646c5aaba55f1c0001d95e4b&wxAppId=&ea=zhenju0111&id=67f64d6ee2874755b8c874249e77ed11&type=1\"\n" +
                "\t}\n" +
                "}, {\n" +
                "\t\"attachmentType\": 4,\n" +
                "\t\"miniprogram\": {\n" +
                "\t\t\"picUrl\": \"https://crm.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=6AA905C78D85C91D156EA080FBCF3C178916686AC9304C474F7111554117A94FAB85D36BC08459975D7FE817E795E614D1481D7CD8B706092A36E716DB0FBF64AC1140B757B2DDAD8CEC499E65EEBC0D\",\n" +
                "\t\t\"title\": \"会员注册1\",\n" +
                "\t\t\"url\": \"https://crm.ceshi112.com/ec/cml-marketing/release/web/cml-marketing.html?_hash=/cml/h5/conference_detail&marketingActivityId=!!marketingActivityId!!&wxAppId=!!wxAppId!!&ea=zhenju0111&id=37368a7b688c412fa9f2796cfc2a2ea3&byshare=1\",\n" +
                "\t\t\"picPath\": \"A_202305_23_eec5532014834bfc826211fe419ebf03.jpg\",\n" +
                "\t\t\"appId\": \"\",\n" +
                "\t\t\"materialId\": \"37368a7b688c412fa9f2796cfc2a2ea3\",\n" +
                "\t\t\"materialType\": 3,\n" +
                "\t\t\"objectType\": 13,\n" +
                "\t\t\"miniProgramType\": \"1\",\n" +
                "\t\t\"page\": \"/pages/share/share?ea=zhenju0111&objectId=37368a7b688c412fa9f2796cfc2a2ea3&objectType=13&isGroupSend=1&spreadType=2\"\n" +
                "\t}\n" +
                "}]";
        List<QywxAttachmentsVO> qywxAttachmentsVO = JSON.parseArray(a,QywxAttachmentsVO.class);

        groupSendMessageVO.setQywxAttachmentsVO(qywxAttachmentsVO);
        addMarketingActivityArg.setQywxGroupSendMessageVO(groupSendMessageVO);

        AddMarketingActivityResult addMarketingActivityResult = marketingActivityService.addMarketingActivity("zhenju0111", 1000, addMarketingActivityArg, false).getData();
        log.info("result:{}", addMarketingActivityResult);
    }

    @Test
    public void getMarketingActivity() {
        GetMarketingActivityArg getMarketingActivityArg = new GetMarketingActivityArg();
        //全员推广
//        getMarketingActivityArg.setId("5c85da41ce044800015203e0");

        //服务号推广
//        getMarketingActivityArg.setId("5c80cdb54efb2900019cbbe4");

        //短信推广
//        getMarketingActivityArg.setId("5f6ab72196683600017ef100");

        //微信公众号高级群发
//        getMarketingActivityArg.setId("5f6d5424afa3430001cf0356");

        //公众号模板消息
//        getMarketingActivityArg.setId("62bd90004e98a30001e44bdc");
        //伙伴推广
        getMarketingActivityArg.setId("677ca593f45a190001539622");

        GetMarketingActivityResult getMarketingActivityResult = marketingActivityService.getMarketingActivity("88146", 1000, getMarketingActivityArg).getData();
        log.info("result:{}", getMarketingActivityResult);
    }

    @Test
    public void updateMarketingActivityResult() {
        UpdateMarketingActivityArg updateMarketingActivityArg = new UpdateMarketingActivityArg();

        UpdateMarketingActivityResult updateMarketingActivityResult = marketingActivityService.updateMarketingActivity(ea, operatorFsUserId, updateMarketingActivityArg).getData();
        log.info("result:{}", updateMarketingActivityResult);
    }

    @Test
    @Ignore
    public void init() {
        Result<Integer> result = marketingActivityService.initCrmMarketingActivityByOldEa();
    }

    @Test
    public void getEaInitCrmMarketingActivity() {
        String ea = "55808";
        Result<Integer> result = marketingActivityService.getEaInitCrmMarketingActivity(ea);
    }

    @Test
    public void doActionGroupSms(){
        AddMarketingActivityArg activityArg = new AddMarketingActivityArg();
        activityArg.setSpreadType(3);
        activityArg.setMarketingEventId("5f9fd40c78e9dc0001db58a4");
        activityArg.setMaterialInfos(Lists.newArrayList());
        AddMarketingActivityArg.MarketingActivityGroupSenderVO groupSenderVO = new AddMarketingActivityArg.MarketingActivityGroupSenderVO();
        groupSenderVO.setTemplateId("6fc7e754bfe04b7590e04f1723e62975");
        groupSenderVO.setTemplateName("打工人打工魂");
        groupSenderVO.setEventType(SaveOrSendTypeEnum.SEND.getType());
//        groupSenderVO.setTemplateContent("哈哈哈哈哈哈 https://fs8.ceshi113.com/8n3kzi");
        groupSenderVO.setType(MwSendTaskTypeEnum.IMMEDIATELY_SEND.getType());
        List<PhoneContentResult> phones = Lists.newArrayList();
        PhoneContentResult phoneContent = new PhoneContentResult();
        phoneContent.setPhone("15627861090");
        phones.add(phoneContent);
        groupSenderVO.setPhones(phones);
        groupSenderVO.setSendRange(SmsGroupTypeEnum.PHONE_LIST.getType());
        groupSenderVO.setSceneType(SmsSceneTypeEnum.GENERAL.getType());
        Map<String, String> shortUrlMap = Maps.newHashMap();
        shortUrlMap.put("https://fs8.ceshi113.com/8n3kzi", "https://baidu.com?marketingActivityId=");
        groupSenderVO.setShortUrlMap(shortUrlMap);
        activityArg.setMarketingActivityGroupSenderVO(groupSenderVO);
        AddMarketingActivityResult result = sendGroupSmsManager.doAddAction(ea, 1112, activityArg);
        System.out.println(result);
    }

    @Test
    public void deleteMarketingActivity(){
        String ea = "74164";
        Integer fsUserId = 1071;
        String marketingActivityId = "5f61ffc20abef000012da787";
        Result<Void> result = marketingActivityService.deleteMarketingActivity(ea, fsUserId, marketingActivityId);
    }

    @Test
    public void testList(){
        ListMarketingActivityByMarketingUserGroupIdArg arg = new ListMarketingActivityByMarketingUserGroupIdArg();
        arg.setPageNo(1);
        arg.setPageSize(9999);
        arg.setMarketingUserGroupId("8f96428e1fa248e1a070534dd3f89e26");
        Result ret = marketingActivityService.listMarketingActivityByMarketingUserGroupId("74164", -10000, arg);
        System.out.println(ret);
    }

    @Test
    public void testRevokeSend(){
        Result ret = marketingActivityService.revokeSend("74164", "627a203292c85e000165d016");
        System.out.println(ret);
    }
}
