/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.whatsapp;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.arg.whatsapp.*;
import com.facishare.marketing.api.service.whatsapp.WhatsAppService;
import com.facishare.marketing.api.vo.whatsapp.BusinessPhoneVO;
import com.facishare.marketing.api.vo.whatsapp.SendDetailVO;
import com.facishare.marketing.api.vo.whatsapp.SpreadListVO;
import com.facishare.marketing.api.vo.whatsapp.TemplateVO;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.dao.mongo.WhatsAppMessageMongoDao;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;


@Slf4j
public class WhatsAppServiceTest extends BaseTest {

    @Autowired
    private WhatsAppService whatsAppService;

    @Autowired
    private WhatsAppMessageMongoDao whatsAppMessageMongoDao;

    @Test
    public void queryTemplateTest() {
        QueryTemplateArg queryTemplateArg = new QueryTemplateArg();
        queryTemplateArg.setFsEa("88146");
        queryTemplateArg.setBusinessPhone("8613520614030");
        queryTemplateArg.setLimit(10);
        Result<TemplateVO> result = whatsAppService.queryTemplate(queryTemplateArg);
        log.info("查询结果 {}", JsonUtil.toJson(result));
//        queryTemplateArg.setAfter(result.getData().getCursors().getAfter());
//        result = whatsAppService.queryTemplate(queryTemplateArg);
//        log.info("查询结果 {}", result);
//
//        queryTemplateArg.setAfter(result.getData().getCursors().getAfter());
//        result = whatsAppService.queryTemplate(queryTemplateArg);
//        log.info("查询结果 {}", result);

    }
    @Test
    public void getBusinessPhone() {
        Result<List<BusinessPhoneVO>>  result = whatsAppService.getBusinessPhone("82846");
        log.info("查询结果 {}", result);
    }

    @Test
    public void spreadList() {
        SpreadListArg spreadListArg = new SpreadListArg();
        spreadListArg.setEa("88146");
        spreadListArg.setFsUserId(1000);
        spreadListArg.setPageNum(1);
        spreadListArg.setPageSize(10);
        Result<PageResult<SpreadListVO>> result = whatsAppService.spreadList(spreadListArg);
        log.info("查询结果 {}", result);
    }
    @Test
    public void sendDetailList() {
        SendDetailArg sendDetailArg = new SendDetailArg();
        sendDetailArg.setEa("88146");
        sendDetailArg.setMarketingActivityId("65b0fa6c4c262e0001d643cc");
        sendDetailArg.setPhone("8617704071339");
        sendDetailArg.setPageNum(1);
        sendDetailArg.setPageSize(5);
        Result<PageResult<SendDetailVO>> result = whatsAppService.sendDetail(sendDetailArg);
        log.info("查询结果 {}", result);

//        sendDetailArg.setPageNum(2);
//        sendDetailArg.setPageSize(5);
//        result = whatsAppService.sendDetail(sendDetailArg);
//        log.info("查询结果 {}", result);
    }

    @Test
    public void directSendTemplateMessage() {
        String json = "{\n" +
                "    \"businessPhone\": \"8613520614030\",\n" +
                "    \"parameterList\": [\n" +
                "        \"33334\"\n" +
                "    ],\n" +
                "    \"phoneList\": [\n" +
                "        \"8615302608074\"\n" +
                "    ],\n" +
                "    \"templateLanguage\": \"zh_CN\",\n" +
                "    \"templateName\": \"conference_invite\",\n" +
                "    \"referer\": \"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/whatsapp/create\",\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36\"\n" +
                "}";
        DirectSendMessageArg directSendMessageArg = JsonUtil.fromJson(json, DirectSendMessageArg.class);
        directSendMessageArg.setEa("88146");
        whatsAppService.directSendTemplateMessage(directSendMessageArg);
    }

    @Test
    public void pLogin() {
        PLoginArg pLoginArg = new PLoginArg();
        pLoginArg.setEa("82447");
        pLoginArg.setWhatsAppUserId("8613762692877");
        pLoginArg.setFsUserId("1000");
        pLoginArg.setName("小张");
        pLoginArg.setMobile("8613762692877");
        whatsAppService.pLogin(pLoginArg);
    }

    @Test
    public void syncData() {
        SyncDataArg arg = new SyncDataArg();
        arg.setEa("88146");
        arg.setWhatsAppUserId("8618138406898");
        arg.setFsUserId(1037);
        arg.setObjectApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
//        arg.setObjectApiName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        List<ObjectData> dataList = Lists.newArrayList();
        ObjectData objectData = new ObjectData();
        objectData.put("pushname", "mecy");
        objectData.put("_id", "214242078769298@lid");
        objectData.put("phoneNumber", "<EMAIL>");
        dataList.add(objectData);

        objectData = new ObjectData();
        objectData.put("_id", "29090048418009@lid");
        objectData.put("phoneNumber", "<EMAIL>");
        dataList.add(objectData);

        objectData = new ObjectData();
        objectData.put("_id", "54576451448838@lid");
        objectData.put("phoneNumber", "<EMAIL>");
        dataList.add(objectData);
        arg.setObjectDataList(dataList);
//        String phone = "<EMAIL>";
//        phone = phone.split("@")[0];


        String dataStr = "{\"whatsAppUserId\":\"8613948330347\",\"ea\":\"88146\",\"fsUserId\":1017,\"objectApiName\":\"WechatWorkExternalUserObj\",\"objectDataList\":[{\"phoneNumber\":\"<EMAIL>\",\"contactHash\":\"BBxQ\",\"name\":\"Leon\",\"isContactSyncCompleted\":1,\"phoneNumberCreatedAt\":1741167803,\"id\":\"228784905175052@lid\",\"pnContactHash\":\"BBxQ\",\"_id\":\"<EMAIL>\",\"shortName\":\"Leon\",\"type\":\"in\",\"isAddressBookContact\":1,\"syncToAddressbook\":true},{\"phoneNumber\":\"<EMAIL>\",\"contactHash\":\"vh3Q\",\"isContactSyncCompleted\":1,\"phoneNumberCreatedAt\":1740706744,\"id\":\"72400901275889@lid\",\"pnContactHash\":\"vh3Q\",\"_id\":\"<EMAIL>\",\"isAddressBookContact\":0}]}";
        arg = JSON.parseObject(dataStr, SyncDataArg.class);
        whatsAppService.syncData(arg);
    }

    @Test
    public void syncWhatsAppGroupData() {
        SyncDataArg arg = new SyncDataArg();
        arg.setEa("88146");
        arg.setWhatsAppUserId("8618138406898");
        arg.setFsUserId(1037);
        arg.setObjectApiName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        List<ObjectData> dataList = Lists.newArrayList();
        ObjectData objectData = new ObjectData();
        objectData.put("_id", "<EMAIL>");
        objectData.put("subject", "WhatsApp Group 测试");
        objectData.put("creation", 1729243801);
        objectData.put("owner", "<EMAIL>");
        objectData.put("size", 3);
        dataList.add(objectData);


        arg.setObjectDataList(dataList);

        String dataStr = "{\"whatsAppUserId\":\"8613762692877\",\"ea\":\"82447\",\"fsUserId\":1000,\"objectApiName\":\"WechatGroupUserObj\",\"objectDataList\":[{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"staleType\":\"device\",\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"}]}";
        arg = JSON.parseObject(dataStr, SyncDataArg.class);
        whatsAppService.syncData(arg);
    }

    @Test
    public void syncWhatsAppGroupUserData() {
//        SyncDataArg arg = new SyncDataArg();
//        arg.setEa("88146");
//        arg.setWhatsAppUserId("8618138406898");
//        arg.setFsUserId(1037);
//        arg.setObjectApiName(CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName());
//        List<ObjectData> dataList = Lists.newArrayList();
//        ObjectData objectData = new ObjectData();
//        objectData.put("_id", "<EMAIL>");
//        objectData.put("groupId", "<EMAIL>");
////        objectData.put("participants", Lists.newArrayList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
//        objectData.put("participants", Lists.newArrayList("<EMAIL>", "<EMAIL>","<EMAIL>"));
//        dataList.add(objectData);
//        arg.setObjectDataList(dataList);
        String dataStr = "{\"whatsAppUserId\":\"8618138406898\",\"ea\":\"88146\",\"fsUserId\":1037,\"objectApiName\":\"WechatGroupUserObj\",\"objectDataList\":[{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\",\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"staleType\":\"device\",\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\",\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"staleType\":\"device\",\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\",\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"staleType\":\"device\",\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"staleType\":\"device\",\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\",\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\",\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\",\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"staleType\":\"device\",\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"},{\"groupId\":\"<EMAIL>\",\"senderKey\":{},\"participants\":[\"<EMAIL>\"],\"pastParticipants\":[\"<EMAIL>\"],\"admins\":[\"<EMAIL>\"],\"rotateKey\":false,\"staleType\":\"device\",\"superAdmins\":[\"<EMAIL>\"],\"_id\":\"<EMAIL>\"}]}";
        dataStr = "{\"whatsAppUserId\":\"8613762692877\",\"ea\":\"82447\",\"fsUserId\":1000,\"objectApiName\":\"WechatGroupUserObj\",\"objectDataList\":[{\"senderKey\":{},\"pastParticipants\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"rotateKey\":false,\"superAdmins\":[\"<EMAIL>\"],\"groupId\":\"<EMAIL>\",\"_id\":\"<EMAIL>\",\"admins\":[\"<EMAIL>\"],\"participants\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"]}]}";
        SyncDataArg arg = JSON.parseObject(dataStr, SyncDataArg.class);
        whatsAppService.syncData(arg);
    }

    @Test
    public void syncWhatsAppSessionData() {
        SyncDataArg arg = new SyncDataArg();
        arg.setEa("88146");
        arg.setWhatsAppUserId("8618138406898");
        arg.setFsUserId(1037);
        arg.setObjectApiName(CrmObjectApiNameEnum.WECHAT_SESSION_OBJ.getName());
        List<ObjectData> dataList = Lists.newArrayList();
        ObjectData objectData = new ObjectData();
        objectData.put("_id", "<EMAIL>");
        objectData.put("name", "测试");
        dataList.add(objectData);

        objectData = new ObjectData();
        objectData.put("_id", "<EMAIL>");
        dataList.add(objectData);

        arg.setObjectDataList(dataList);

        String dataStr = "{\"whatsAppUserId\":\"8613762692877\",\"ea\":\"82447\",\"fsUserId\":1000,\"objectApiName\":\"WechatSessionObj\",\"objectDataList\":[{\"unreadCount\":0,\"disappearingModeInitiator\":\"chat\",\"archive\":false,\"disappearingModeTrigger\":\"chat_settings\",\"ephemeralSettingTimestamp\":0,\"ephemeralDuration\":0,\"isReadOnly\":false,\"t\":1741158683,\"disappearingModeInitiatedByMe\":false,\"isLocked\":false,\"isAutoMuted\":false,\"name\":\"营销通测试\",\"muteExpiration\":0,\"endOfHistoryTransferType\":1,\"id\":\"<EMAIL>\",\"_id\":\"<EMAIL>\",\"isAnnounceGrpRestrict\":false,\"notSpam\":true,\"unreadMentionCount\":0},{\"unreadCount\":0,\"disappearingModeInitiator\":\"chat\",\"archive\":false,\"disappearingModeTrigger\":\"chat_settings\",\"ephemeralSettingTimestamp\":0,\"ephemeralDuration\":0,\"isReadOnly\":false,\"t\":1741253005,\"disappearingModeInitiatedByMe\":false,\"isLocked\":false,\"isAutoMuted\":false,\"name\":\"营销通前端群\",\"muteExpiration\":0,\"endOfHistoryTransferType\":1,\"id\":\"<EMAIL>\",\"_id\":\"<EMAIL>\",\"isAnnounceGrpRestrict\":false,\"notSpam\":true,\"unreadMentionCount\":0},{\"tcTokenTimestamp\":1741143348,\"unreadCount\":0,\"disappearingModeInitiator\":\"chat\",\"archive\":false,\"disappearingModeTrigger\":\"chat_settings\",\"ephemeralSettingTimestamp\":0,\"tcTokenSenderTimestamp\":1741143348,\"ephemeralDuration\":0,\"t\":1741143347,\"disappearingModeInitiatedByMe\":false,\"isLocked\":false,\"isAutoMuted\":false,\"muteExpiration\":0,\"endOfHistoryTransferType\":1,\"id\":\"<EMAIL>\",\"_id\":\"<EMAIL>\",\"tcToken\":{\"_data\":{},\"iv\":{\"0\":245,\"1\":146,\"2\":81,\"3\":76,\"4\":149,\"5\":115,\"6\":35,\"7\":227,\"8\":209,\"9\":204,\"10\":111,\"11\":240,\"12\":221,\"13\":232,\"14\":183,\"15\":124},\"_keyId\":1,\"_scheme\":1},\"notSpam\":true,\"unreadMentionCount\":0},{\"tcTokenTimestamp\":1741168745,\"unreadCount\":0,\"disappearingModeInitiator\":\"chat\",\"archive\":false,\"disappearingModeTrigger\":\"chat_settings\",\"ephemeralSettingTimestamp\":0,\"tcTokenSenderTimestamp\":1741228180,\"ephemeralDuration\":0,\"t\":1741228172,\"disappearingModeInitiatedByMe\":false,\"isLocked\":false,\"isAutoMuted\":false,\"muteExpiration\":0,\"endOfHistoryTransferType\":1,\"id\":\"<EMAIL>\",\"_id\":\"<EMAIL>\",\"tcToken\":{\"_data\":{},\"iv\":{\"0\":38,\"1\":12,\"2\":194,\"3\":41,\"4\":208,\"5\":242,\"6\":141,\"7\":67,\"8\":21,\"9\":118,\"10\":41,\"11\":205,\"12\":42,\"13\":87,\"14\":65,\"15\":75},\"_keyId\":1,\"_scheme\":1},\"notSpam\":true,\"unreadMentionCount\":0},{\"tcTokenTimestamp\":1741246869,\"unreadCount\":0,\"disappearingModeInitiator\":\"chat\",\"archive\":false,\"disappearingModeTrigger\":\"chat_settings\",\"ephemeralSettingTimestamp\":0,\"tcTokenSenderTimestamp\":1741246882,\"ephemeralDuration\":0,\"t\":1741254844,\"disappearingModeInitiatedByMe\":false,\"isLocked\":false,\"isAutoMuted\":false,\"muteExpiration\":0,\"endOfHistoryTransferType\":1,\"id\":\"<EMAIL>\",\"_id\":\"<EMAIL>\",\"tcToken\":{\"_data\":{},\"iv\":{\"0\":107,\"1\":221,\"2\":40,\"3\":114,\"4\":73,\"5\":29,\"6\":183,\"7\":242,\"8\":148,\"9\":157,\"10\":102,\"11\":122,\"12\":187,\"13\":135,\"14\":119,\"15\":140},\"_keyId\":1,\"_scheme\":1},\"notSpam\":true,\"unreadMentionCount\":0}]}";
        arg = JSON.parseObject(dataStr, SyncDataArg.class);
        whatsAppService.syncData(arg);
    }

    @Test
    public void syncWhatsAppMessageData() {
        whatsAppMessageMongoDao.deleteByMessageIds("88146", Lists.newArrayList(
                "8615001132230@c.us_70765A81DD49963B97",
                "<EMAIL>",
                "85256082089@c.us_04A78E6A411853FF710FE8AC70675693",
                "85256082089@c.us_3EB0D907358D729A2BC77F",
                "85256082089@c.us_63BBC61053166919A47653FD77AF2742",
                "85256082089@c.us_B86D5E4BD9B5DEC44DB22E3DA1E514BC",
                "85256082089@c.us_517EA56671C87979BA1522B6934C4516",
                "85256082089@c.us_809529B2751BB1FDD8D9C686D9D2D884",
                "85256082089@c.us_3EB00C5CA61D126659ED6B",
                "85256082089@c.us_3EB0EB1D8E3DDC736506A0",
                "85256082089@c.us_3EB00CC556C574ABE3617A"
        ));

//        SyncDataArg arg = new SyncDataArg();
//        arg.setEa("88146");
//        arg.setWhatsAppUserId("8618138406898");
//        arg.setFsUserId(1037);
//        arg.setObjectApiName(CrmObjectApiNameEnum.WHATSAPP_CHAT_MESSAGE_OBJ.getName());
//        List<ObjectData> dataList = Lists.newArrayList();
//        ObjectData objectData = new ObjectData();
//        objectData.put("chatId", "<EMAIL>");
//        objectData.put("_id", "false_8615001132230@c.us_70765A81DD49963B97");
//        objectData.put("message", "测试");
//        objectData.put("nickname", "Mecy");
//        objectData.put("datetime", "下午5:16, 2024年12月23日");
//        objectData.put("fromRole", 2);
//        objectData.put("phone", "8615001132230");
////        dataList.add(objectData);
//
//        String dataStr = "{\"chatId\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_517EA56671C87979BA1522B6934C4516\",\"_id\":\"true_85256082089@c.us_517EA56671C87979BA1522B6934C4516\",\"deprecatedMms3Url\":\"https://mmg.whatsapp.net/v/t62.15575-24/30022260_735986898846451_1804267232316669678_n.enc?ccb=11-4&oh=01_Q5AaIOj2SysW84x9zT27ezjjnAS83jA5pHLXp1TVFrP5QDIf&oe=67CBDA52&_nc_sid=5e03e0&mms3=true\",\"rowId\":999999987,\"type\":\"sticker\",\"t\":1738824734,\"from\":\"<EMAIL>\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"ack\":1,\"invis\":true,\"mimetype\":\"image/webp\",\"filehash\":\"7eoF5oZQBOcKcVRj2zvk/3q/Yp/cgSJ4Hw9j/mUJMd4=\",\"mediaKey\":\"dXhjyN+0H04ANCKUZqiWjBSDPAL0/Clg4cT+hdCI5mM=\",\"mediaKeyTimestamp\":1738824734,\"width\":512,\"height\":512,\"messageRangeIndex\":\"85256082089@c.us_2_767a45c1e\",\"encFilehash\":\"y56dhWiycbxMV4dExPakb/7tSIpQRhdVr/JuJjIllGY=\",\"hasReaction\":false,\"botTargetSenderJid\":null,\"bizBotType\":null,\"eventInvalidated\":false,\"viewMode\":\"VISIBLE\",\"isFromTemplate\":false,\"pollInvalidated\":false}";
//        objectData = JSON.parseObject(dataStr, ObjectData.class);
//        dataList.add(objectData);
//
//        arg.setObjectDataList(dataList);

        String dataString = "{\"whatsAppUserId\":\"8618138406898\",\"ea\":\"88146\",\"fsUserId\":1037,\"objectApiName\":\"WhatsAppChatMessageObj\",\"objectDataList\":[{\"invis\":true,\"isFromTemplate\":false,\"chatId\":\"<EMAIL>\",\"ack\":0,\"messageRangeIndex\":\"85256082089@c.us_3_7675fca2a\",\"pollInvalidated\":false,\"type\":\"e2e_notification\",\"viewMode\":\"VISIBLE\",\"rowId\":999999983,\"t\":1734330922,\"eventInvalidated\":false,\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_04A78E6A411853FF710FE8AC70675693\",\"_id\":\"true_85256082089@c.us_04A78E6A411853FF710FE8AC70675693\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"hasReaction\":false},{\"invis\":true,\"isFromTemplate\":false,\"chatId\":\"<EMAIL>\",\"ack\":1,\"messageRangeIndex\":\"85256082089@c.us_2_767860528\",\"pollInvalidated\":false,\"body\":\"对话\",\"type\":\"chat\",\"viewMode\":\"VISIBLE\",\"rowId\":999999984,\"t\":1736836392,\"eventInvalidated\":false,\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_3EB0D907358D729A2BC77F\",\"_id\":\"true_85256082089@c.us_3EB0D907358D729A2BC77F\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"hasReaction\":false},{\"invis\":true,\"isFromTemplate\":false,\"chatId\":\"<EMAIL>\",\"ack\":1,\"messageRangeIndex\":\"85256082089@c.us_2_767a45c12\",\"pollInvalidated\":false,\"body\":\"\uD83D\uDE2D\uD83C\uDF40\uD83E\uDD19\",\"type\":\"chat\",\"viewMode\":\"VISIBLE\",\"rowId\":999999985,\"t\":1738824722,\"eventInvalidated\":false,\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_63BBC61053166919A47653FD77AF2742\",\"_id\":\"true_85256082089@c.us_63BBC61053166919A47653FD77AF2742\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"hasReaction\":false},{\"invis\":true,\"isFromTemplate\":false,\"chatId\":\"<EMAIL>\",\"ack\":1,\"messageRangeIndex\":\"85256082089@c.us_2_767a45c16\",\"pollInvalidated\":false,\"body\":\"\uD83D\uDE0A\uD83D\uDE0A\uD83D\uDE18\",\"type\":\"chat\",\"viewMode\":\"VISIBLE\",\"rowId\":999999986,\"t\":1738824726,\"eventInvalidated\":false,\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_B86D5E4BD9B5DEC44DB22E3DA1E514BC\",\"_id\":\"true_85256082089@c.us_B86D5E4BD9B5DEC44DB22E3DA1E514BC\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"hasReaction\":false},{\"isFromTemplate\":false,\"deprecatedMms3Url\":\"https://mmg.whatsapp.net/v/t62.15575-24/30022260_735986898846451_1804267232316669678_n.enc?ccb\\u003d11-4\\u0026oh\\u003d01_Q5AaIOj2SysW84x9zT27ezjjnAS83jA5pHLXp1TVFrP5QDIf\\u0026oe\\u003d67CBDA52\\u0026_nc_sid\\u003d5e03e0\\u0026mms3\\u003dtrue\",\"chatId\":\"<EMAIL>\",\"ack\":1,\"encFilehash\":\"y56dhWiycbxMV4dExPakb/7tSIpQRhdVr/JuJjIllGY\\u003d\",\"pollInvalidated\":false,\"type\":\"sticker\",\"mediaKeyTimestamp\":1738824734,\"eventInvalidated\":false,\"mediaKey\":\"dXhjyN+0H04ANCKUZqiWjBSDPAL0/Clg4cT+hdCI5mM\\u003d\",\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_517EA56671C87979BA1522B6934C4516\",\"height\":512,\"invis\":true,\"messageRangeIndex\":\"85256082089@c.us_2_767a45c1e\",\"viewMode\":\"VISIBLE\",\"rowId\":999999987,\"t\":1738824734,\"width\":512,\"mimetype\":\"image/webp\",\"_id\":\"true_85256082089@c.us_517EA56671C87979BA1522B6934C4516\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"filehash\":\"7eoF5oZQBOcKcVRj2zvk/3q/Yp/cgSJ4Hw9j/mUJMd4\\u003d\",\"hasReaction\":false},{\"isFromTemplate\":false,\"deprecatedMms3Url\":\"https://mmg.whatsapp.net/o1/v/t62.9505-24/f2/m234/AQPDeak_Yye9xN5jEHcgb6RJssj0NAfYOxMZy2CdYWgQqkvGDdZCl0hhKrC9OZpeUdqxr61y_OZ9vh-XCWy5mlSVEj4rmbYQNmkr6Qo?ccb\\u003d9-4\\u0026oh\\u003d01_Q5AaIPQmVk1b9_FFbA8pupsFBLFJULkuPFdVhTxq-pFHgbak\\u0026oe\\u003d67CBE1CD\\u0026_nc_sid\\u003de6ed6c\\u0026mms3\\u003dtrue\",\"chatId\":\"<EMAIL>\",\"ack\":1,\"encFilehash\":\"JBwotB7IqYKJemuiSJQz7CSswQL9OIPDlvALa0uBhBM\\u003d\",\"staticUrl\":\"\",\"pollInvalidated\":false,\"body\":\"\",\"type\":\"video\",\"duration\":\"1\",\"mediaKeyTimestamp\":1738824745,\"eventInvalidated\":false,\"mediaKey\":\"AflyZyCIJ5yla/vm4leUJ23Bip3QF75q+vPj+ahRUPY\\u003d\",\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_809529B2751BB1FDD8D9C686D9D2D884\",\"height\":298,\"invis\":true,\"messageRangeIndex\":\"85256082089@c.us_2_767a45c26\",\"isViewOnce\":false,\"viewMode\":\"VISIBLE\",\"rowId\":999999988,\"interactiveAnnotations\":[],\"t\":1738824742,\"size\":81381,\"width\":320,\"mimetype\":\"video/mp4\",\"_id\":\"true_85256082089@c.us_809529B2751BB1FDD8D9C686D9D2D884\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"filehash\":\"TDxSM4hIzcQj2WBmEdM/8VwqMGBtVjBTtQj5Qf2gnUU\\u003d\",\"hasReaction\":false},{\"isFromTemplate\":false,\"deprecatedMms3Url\":\"https://mmg.whatsapp.net/v/t62.7161-24/22558070_1662475197956215_7485730844370994479_n.enc?ccb\\u003d11-4\\u0026oh\\u003d01_Q5AaIAlYFZz-W7TUart3cdcJ0ScCjloaf5LSfvYTKCQLRkFJ\\u0026oe\\u003d67CE2C1B\\u0026_nc_sid\\u003d5e03e0\\u0026mms3\\u003dtrue\",\"chatId\":\"<EMAIL>\",\"ack\":1,\"encFilehash\":\"4oB5SgW5DH1RbG7ia2ZYY4o+OBIzsqGV275XtvIvENM\\u003d\",\"pollInvalidated\":false,\"body\":\"/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADb/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/2wBDAQMDAwQDBAgEBAgQCwkLEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBD/wAARCABTAGQDASIAAhEBAxEB/8QAHAABAAICAwEAAAAAAAAAAAAAAAUGBAcCAwgJ/8QARhAAAgEDAwICBgUIBA8AAAAAAQIDAAQFBhESByETFRQiMVOR0RZBUXGUCDJCVmGB0tMkM1WhFzQ2RFJUV2JjZHODk5Wx/8QAGgEBAAIDAQAAAAAAAAAAAAAAAAIDAQQGBf/EADARAAIBAwMBBgILAAAAAAAAAAABAgMEEQUSMSEGQVFhcZEToRQVIjIzgbHB0eHx/9oADAMBAAIRAxEAPwD6p0pSgFKUoBSlKAUpSgFKUoBSlKA1xlIs/JqbOGzztgYRdRBIZcfIzQf0aHdOfjANud33Cgevt3IJKuvI4mGfVGoJ8lpjG7SXsRguFijaS5jFpAOchPfkHDoN/wBGNKVI2k+i/o2XSlKiaopSlAKUpQClKUApSlAKUpQGssteYKfVGehmys8c9tdxRTI9+0aqxtYHARQ42Xi6n2DuWP170qRvxfrqDMm8uYJYmuozarGhVoovR4d1c7nk3ieI2429VlG3bcqkbOFhfyXylR/nNt7uT4D5085tvdyfAfOomsSFKj/Obb3cnwHzp5zbe7k+A+dASFKj/Obb3cnwHzp5zbe7k+A+dASFKj/Obb3cnwHzp5zbe7k+A+dASFKj/Obb3cnwHzp5zbe7k+A+dASFKj/Obb3cnwHzp5zbe7k+A+dAUfLXJtdTZtbXTt9u91E0s4aIJcP6NCOa8pAdgoVO4HdD2+sq6chmEbUOa55OykUXUfCJ7lVe3X0aH1GAHYk7v377OPq2pUjZ+1hFt+mPT/8AWrT346D+Kn0x6ffrVp78dB/FUBmbzUdm9i2D0jcZyO5hvVmaK5SEQToR6OXLyoBG20itwV3BKHbbkapGmdYdX4bXH/T7pjFDPKL2adsWL2WOWKJ5GjEYXxGjcw+C204jLyCSNAfUYwyaxtb6Y9Pv1q09+Og/irEl1Xpm4v47aw1JpmWCaJl4pexm4M3sREUHZuXL29iCgADc901i2r+sWEw2Vv8AP9NoryPHYm5aG9x8V6zXWTSaSKGJbCNprjg58IniXjVCziZ19l205dasyWWe6yemIbTT15iYr2xuFmnS4guO3OGeKfhJuwYMu8SFODK4DEAZB1asvsxZ5HBPjczgrGxErDKLkZRHJJFzjIMW8bcmCiUbbqN2Hf7I4Z/ISY+3il1ZpGO5njuvTWiukJt2MZFv6OzRFW2bYsZIz7SQpHq1OXk8aXOUnvEvZoMdj47pYbPxDKxJmLBEjPKRiI1AXuSew7nvVLyPqRLrTFXWGbGtoxmuLbKQ3Fpk0yaTRi6HOJzMqGLnFCATGfEVuSEh0NWKmnyyt1HnoS1hl1s8VLDNr/A3t6sFwtvJdPbneU/1JlaKOMMB9fBE7dvWPrHqzmosnFBJDpPVWiZXDAxTZebc/ps3NYQoI3KIoGxUKXJcngJ3F2NzPdz2uSxl3BHFBDLHcjIS8ZXcvzi4CQkGMKm53Ibn222IrW/Ra86r5y9k1HrDTuLwuAucTj/Qmt79rqXL3D20Ust+y+qttuZDG0PhlucTtzZWVmy6eBGpkuseooxZx3E2qdLvkCkEcsCXCJaA7qZpFJRpC354VS2xAXfiSTUYM9l42x1uusdLyFo5ZMhcPNDwVwixxxxKEB2J3lJIPrchuqhUOwsPZW97Y+kXKu0hmmXcSMo2WRlHYHb2AVQNdZbqPp/WwsdO6QGT07PhDcxTxW088sd/HMRIkri4UBWjeARosZZmMjFlRHZKiw6Na6v1DjoTJoDI6OybOjzOmSu41ZJN0CxRhOA47Bzu7E7se42AOZqDVWchwwvNI5PRtxlZI0M9re3+9rAyglhCUjV5C2/HeRkA4qdhuRU/00fO6h0XY5TXWnziM80lzDe2cbTJHG8U8kYKB2JKMEDK2+zKwYdiKmr62htZRHCpCld+7Fu+5+2nAIC01VizawnJZ3S8N4UBuI4ZxIiPt3CsxUsP2lQT9gpWFIlvLlMlwnuHZbhBIpkYKjeDGdlHsA2IP3k0rG1+Jco4XHyMiTP6JmcyTZrBu59rNcwkn++uPnehP7XwP4iH51rvyOIHYw7/AGfs71yGCjDcTEP31V8XyIbDYPnehP7XwP4iH51hxa76WJdrbw6y0qtzyKqi5G2D8gDuAOW+4APwNaQ6yaoj6a6STOR2wae6vIrOIlOSqxDOSw3G44xsO31kV430n+Udmcv1szfR+zxNh5XbYm7yT5JvE9Le4W1ExK9wqjdgu3En1SQe428jVdUubGG+hR3pJuTcsJJfk8+iI6dc6feajLSqlRqsoqSSjlYbx1fdjp08/U+oOQz3T+6hF1lczp6aKFSwkuLiBlRfrO7HYDtVfymvegGEuRZZrWnT7H3BQSCK6yNlE5U7gNxZgdux7/srx/0z6gZnNakt9NXVyk8F3DON2QB14Qu42I237qN99+1efOoT6Qx+sc2j9PMVf3MF9PA91krq7uppijlAzM83c7KK8KHbKv8AHdvKh1Sz0l5478HUR7Mxk3uqfI+nH+FX8mr/AGkdM/8A3Fh/HUzpzXnRzIu8+ktZ6NunAKO+OyNq5A7EgmNvu7fdXx6vNZYu3BEHTHRK7ewnFsx/vevoJ+S/hrC56QYjJ2+JsrRryNJXS2hEca8kV+KgexQWbYfVvXp2faGvd3MbeVLamm87s8Y7l6lV7oFGztpXEam5ppYxjn/D0zJnNFzOZJcxhHY+1muIiT+/euEWZ0LOdocrgpCP9GeE/wDw1RvJYOymMbdie1YWgcW817epPjxEkVxGkL8w3jJ4RJfYfm+sWXY9/U39hFdJCe85yS2my/T9H/67hv8AyRfOsuLUGmbCxla3yWLBDg8EkiJPY+wb/dUMMFD9UYqiZix0Y02UFvl75ruMyG6tLFo0uWIZfE4s4Dkqse/Z91QersOAq+nSnVeIrJjdTj+JLCLlDczZS7vcjPgQyTyxtE0aRDdfBjB33YEkOHG+w3AG3buVY+jRh8BiXx2JsMjJa+kSTRy3F5buJhIefio5l3dH5cwf9477EEBVn0er4GXd0k8J59zyketnU5diupQPb/mNv/LriOtnU3f/AClH4G2/l0pWi0iwhNWax1DrmCC01VeRZCG3cvEr2sScWPYkcVB9lViGzscFLNlMZj7SK6EMkXiNbpJujLxZSHBBBHYgj2UpWrewjO1qRkspxf6ErenCFxGtFJS6de/35K7FmchgNW4rP4WSKzvZZrlWaKBAmzWs24Ee3AD7gK0nls1k83rjLHJ3PjePkbh5PUVdyZGJ/NA2pSuIs7elnftWdvOF4s6qFeq395+5PXWlsDIPXsAf+4/zr0H0d6oa505oq1w2GzYt7O1dooYvRYX4qqqAN2Qk9gPaaUrd0WKeoPK4Tx8i3WJS+r+eWv3LqOtnU7gW+ko3H1+hW/8ALrp0v1t6nW88hh1MFJZT/iVuf0X/AOH+2lK7eCwcVIss3XrqwkMjrqrYqpI/oFt9n/TrkOuvVTff6UDv/wAhbfy6UqZg4Yz8oDq3NPkI5NVqVguRHGPLrX1V8GNtv6v7WJ/fSlKzllrivA//2Q\\u003d\\u003d\",\"type\":\"video\",\"duration\":\"10\",\"mediaKeyTimestamp\":1738987178,\"eventInvalidated\":false,\"mediaKey\":\"DkzGy3v99vN91DL2qELq40qAbxAVOD+oYuVuHeLPaZo\\u003d\",\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_3EB00C5CA61D126659ED6B\",\"height\":276,\"invis\":true,\"messageRangeIndex\":\"85256082089@c.us_2_767a6d6c6\",\"isViewOnce\":false,\"viewMode\":\"VISIBLE\",\"rowId\":1000000006,\"t\":1738987178,\"size\":1062153,\"width\":332,\"mimetype\":\"video/mp4\",\"_id\":\"true_85256082089@c.us_3EB00C5CA61D126659ED6B\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"filehash\":\"B44XbgggYXyhKGw38cUSa8Ep/AmRwJ+fvalUeQdSTN4\\u003d\",\"hasReaction\":false},{\"isFromTemplate\":false,\"deprecatedMms3Url\":\"https://mmg.whatsapp.net/o1/v/t62.7118-24/f2/m231/AQPy9yCYVEW3cU0VVpsO61MQ-Vy07gxGM_0nEvOMiECInwnODOz-6CMNeE2tGbTzLyJbDBsyep38t0hUGbJGTzlJSXfa7o18WtjbfDLcrA?ccb\\u003d9-4\\u0026oh\\u003d01_Q5AaINtGzWgRPiNDhDLprNcs7ob3qwo4-qEF6G2UNUbFl2Lr\\u0026oe\\u003d67CE5954\\u0026_nc_sid\\u003de6ed6c\\u0026mms3\\u003dtrue\",\"chatId\":\"<EMAIL>\",\"ack\":1,\"encFilehash\":\"Ddyim+6h6T87Lj28gf7J/1acrTRGt6vnjEgv31z8vhg\\u003d\",\"pollInvalidated\":false,\"body\":\"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\\u003d\",\"type\":\"image\",\"mediaKeyTimestamp\":1738988132,\"eventInvalidated\":false,\"mediaKey\":\"3Wdv0wNbyhKF0qrsKfQJmlfXP1mCZ/ZvE/b0YFXB7JY\\u003d\",\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_3EB0EB1D8E3DDC736506A0\",\"height\":1600,\"invis\":true,\"messageRangeIndex\":\"85256082089@c.us_2_767a6da68\",\"isViewOnce\":false,\"viewMode\":\"VISIBLE\",\"rowId\":1000000007,\"t\":1738988132,\"size\":235649,\"width\":1200,\"mimetype\":\"image/jpeg\",\"_id\":\"true_85256082089@c.us_3EB0EB1D8E3DDC736506A0\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"filehash\":\"iZlYJ6Mi6vEQJZePkRULzVf0jkK+3ANMGSg9LVluSdM\\u003d\",\"hasReaction\":false},{\"isFromTemplate\":false,\"deprecatedMms3Url\":\"https://mmg.whatsapp.net/v/t62.7117-24/26134792_981193357262336_3936660606415668622_n.enc?ccb\\u003d11-4\\u0026oh\\u003d01_Q5AaIJMe1TteUbjBGjEwv0rod173o2D_NfaVPncANs_KNDbq\\u0026oe\\u003d67D371CA\\u0026_nc_sid\\u003d5e03e0\\u0026mms3\\u003dtrue\",\"chatId\":\"<EMAIL>\",\"ack\":1,\"encFilehash\":\"eHLSONPT6JrZJMsBAcYaMBQErDBtyxBbzma0Njc4xAw\\u003d\",\"pollInvalidated\":false,\"type\":\"ptt\",\"duration\":\"3\",\"mediaKeyTimestamp\":1739329374,\"eventInvalidated\":false,\"mediaKey\":\"U3LOomzKzSux2nZN0wFwjaLGYjW2UBzJmnV8qJg/Xrw\\u003d\",\"from\":\"<EMAIL>\",\"id\":\"true_85256082089@c.us_3EB00CC556C574ABE3617A\",\"invis\":true,\"messageRangeIndex\":\"85256082089@c.us_2_767ac1016\",\"isViewOnce\":false,\"viewMode\":\"VISIBLE\",\"rowId\":1000000010,\"t\":1739329368,\"size\":6677,\"mimetype\":\"audio/ogg; codecs\\u003dopus\",\"_id\":\"true_85256082089@c.us_3EB00CC556C574ABE3617A\",\"to\":{\"server\":\"c.us\",\"user\":\"85256082089\",\"_serialized\":\"<EMAIL>\"},\"filehash\":\"mVzIPcbDqtLeFLqXCdIWDZjMqY5vAA0hemeRvuQ/dFY\\u003d\",\"hasReaction\":false}]}";
        SyncDataArg arg = JSON.parseObject(dataString, SyncDataArg.class);
        whatsAppService.syncData(arg);
    }

    @Test
    public void convertDateTimeStamp() {
        String datetimeStr = "上午5:16, 2024年12月23日";
        SimpleDateFormat dateFormat = new SimpleDateFormat("aK:mm, yyyy年MM月dd日", Locale.CHINA);
        try {
            // 将字符串转换为Date对象
            Date date = dateFormat.parse(datetimeStr);

            // 将Date对象转换为时间戳
            long timestamp = date.getTime();

            System.out.println("时间戳: " + timestamp);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void convertDateTime() {
        // 时间字符串
        String timeStr = "上午5:16, 2024年12月23日";

        // 定义日期格式（中文格式）
        SimpleDateFormat sdf = new SimpleDateFormat("ahh:mm, yyyy年MM月dd日");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 设置为北京时间（UTC+8）

        try {
            // 解析时间字符串
            Date date = sdf.parse(timeStr);

            // 转换为时间戳（毫秒）
            long timestamp = date.getTime();
            System.out.println("时间戳: " + timestamp);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

}
