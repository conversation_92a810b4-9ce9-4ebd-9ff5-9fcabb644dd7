package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.provider.dao.mongo.EnterpriseSpreadRecordDao;
import com.facishare.marketing.provider.manager.EnterpriseSpreadRecordManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

/**
 * Created by zhengh on 2020/9/24.
 */
@Slf4j
public class EnterpriseSpreadRecordManagerTest extends BaseTest{
    @Autowired
    private EnterpriseSpreadRecordManager enterpriseSpreadRecordManager;
    @Autowired
    private EnterpriseSpreadRecordDao enterpriseSpreadRecordDao;

    @Test
    public void getValidSendList(){
        List<String> list = Lists.newArrayList();
        list.add("15989463965");
        list.add("13811111111");
        list.add("13211112222");
        String ea = "74164";
        int type = 3;
        int days = 7;
        List<String> filterList = enterpriseSpreadRecordManager.getValidSendList(list, ea, type, days);
        System.out.print("filterList:"+filterList);
    }

    @Test
    public void sendRecord(){
        List<String> list = Lists.newArrayList();
        list.add("15989463965");
        list.add("13811111111");
        list.add("13211113333");
        String ea = "74164";
        int type = 3;
        enterpriseSpreadRecordManager.sendRecord(list, ea, type);
    }

    @Test
    public void updateDateById(){
        List<ObjectId> ids = Lists.newArrayList();
        ids.add(new ObjectId("5f6c49e31f711710082664de"));

        int days = 7;
        enterpriseSpreadRecordDao.updateTimeByDays(ids,days );
    }

    @Test
    public void deleteDoc(){
        String id = "5f6c49e31f711710082664de";
        enterpriseSpreadRecordDao.deleteOneDoc(id);
    }

    @Test
    public void testDeleteByEaAndType(){
        String ea = "88146";
        int type = 3;
        String address = "19851624233";
        enterpriseSpreadRecordManager.deleteByEaAndPhones(ea, type, Lists.newArrayList(address));
    }
}
