/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager.ai;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.expcetion.ServiceException;
import com.facishare.ai.api.model.Function;
import com.facishare.ai.api.model.Message;
import com.facishare.ai.api.model.Tool;
import com.facishare.ai.api.model.service.FsAI;
import com.facishare.converter.EIEAConverter;
import com.facishare.eservice.base.result.EserviceResult;
import com.facishare.eservice.rest.common.Arg1;
import com.facishare.eservice.rest.common.HeaderObj;
import com.facishare.eservice.rest.online.model.SearchSceneKnowledgeModel;
import com.facishare.eservice.rest.online.service.KnowledgeService;
import com.facishare.marketing.api.result.ai.AgentExecuteResponse;
import com.facishare.marketing.api.result.ai.AiChatRecordResult;
import com.facishare.marketing.api.result.ai.AiChatSessionResult;
import com.facishare.marketing.api.service.ai.AiChatService;
import com.facishare.marketing.api.service.ai.ObjectQueryProxyService;
import com.facishare.marketing.api.vo.ai.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.manager.ai.AiChatManager;
import com.facishare.marketing.provider.manager.ai.PaaSAgentManager;
import com.facishare.marketing.provider.sharegpt.agent.tool.impl.KeywordIndexCommand;
import com.facishare.marketing.provider.sharegpt.manager.AutoGPTChatContext;
import com.facishare.marketing.provider.sharegpt.manager.AutoGPTChatManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import jetbrick.util.JSONUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class AiChatTest extends BaseTest {

    @Autowired
    private KnowledgeService knowledgeService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private AutoGPTChatManager autoGPTChatManager;

    @Autowired
    private KeywordIndexCommand keywordIndexCommand;

    @Autowired
    private AiChatService aiChatService;

    @Autowired
    private ObjectQueryProxyService objectQueryProxyService;

    @Autowired
    private AiChatManager aiChatManager;
    @Autowired
    private PaaSAgentManager paaSAgentManager;

    @Test
    public void searchKnowledgeResult() throws Exception {
        String ea = "82846";
        HeaderObj header = HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
        SearchSceneKnowledgeModel.SearchArg searchArg = SearchSceneKnowledgeModel.SearchArg.builder()
                .fsEa(ea)
                .content("测试")
                .scene("knowledgeDxVJUg")
                .matchType("title")
                .matchChatGPTContent(true)
                .build();
        EserviceResult<SearchSceneKnowledgeModel.SearchResult> searchResultEserviceResult = knowledgeService.searchKnowledgeResult(header, new Arg1<>(searchArg));
        System.out.println(searchResultEserviceResult);
    }

    @Test
    public void knowledge() {
        String ea = "zhenju0111";
        String prompt = "营销通是什么?";
        ChatCompleteVO.Property property = new ChatCompleteVO.Property();
        property.setDefaultHelperName("Copilot_7d7Ba__c");
        property.setNewApi(true);
        AutoGPTChatContext autoGPTChatContext = autoGPTChatManager.autoGPTChat(ea, 1000, property, null, prompt);
        System.out.println(autoGPTChatContext);
    }

    @Test
    public void autoGPTChat() {
        String ea = "zhenju0111";
        String prompt = "帮我画个奥特曼打怪兽";
        ChatCompleteVO.Property property = new ChatCompleteVO.Property();
        AutoGPTChatContext autoGPTChatContext = autoGPTChatManager.autoGPTChat(ea, 1000, property, null, prompt);
        System.out.println(autoGPTChatContext);
    }

    @Test
    public void agentExecute() {
        String ea = "zhenju0111";
        AgentInvokeRequest req = JSONObject.parseObject("{\"apiName\":\"Copilot_SFA\",\"buttonApiName\":\"WhatIsNextLeads\",\"sessionId\":\"67aef008596f600001c7f38a\",\"messages\":[],\"variables\":[{\"name\":\"id\",\"value\":\"66a9f88253846300079a4ea6\"},{\"name\":\"name\",\"value\":\"陆清怡\"},{\"name\":\"objectApiName\",\"value\":\"LeadsObj\"},{\"name\":\"objectName\",\"value\":\"销售线索\"},{\"name\":\"actionApiName\",\"value\":\"WhatIsNextLeads\"},{\"name\":\"actionLabel\",\"value\":\"What\\u0027s Next\"},{\"name\":\"sessionId\",\"value\":\"zhenju0111_1000_Copilot_SFA_LeadsObj_66a9f88253846300079a4ea6\"}],\"debug\":false}", AgentInvokeRequest.class);
        AgentExecuteResponse<AgentExecuteResponse.AgentExecuteResult> response = paaSAgentManager.agentExecute(ea, 1000, null, req);
        System.out.println(response);
    }

    @Test
    public void pageQuerySessions() {
        AiChatSessionVO vo = new AiChatSessionVO();
        vo.setPageNum(1);
        vo.setPageSize(20);
        Result<PageResult<AiChatSessionResult>> result = aiChatService.pageQuerySessions("zhenju0111", 1000, vo);
        System.out.println(JSONUtils.toJSONString(result));
    }

    @Test
    public void sdk() {
        BaseArgument context = new BaseArgument();
        context.setTenantId("88146");
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel("gpt-3.5-turbo");
        Message message = new Message();
        message.setRole("user");
        message.setContent("今天深圳天气如何?");
        arg.setMessages(Lists.newArrayList(message));
        Tool tool1 = new Tool();
        tool1.setType("function");
        Function fun1 = JSONObject.parseObject("{\n" +
                "    \"name\":\"get_current_weather\",\n" +
                "    \"description\":\"获取天气情况\",\n" +
                "    \"parameters\":{\n" +
                "        \"type\":\"object\",\n" +
                "        \"properties\":{\n" +
                "            \"location\":{\n" +
                "                \"type\":\"string\",\n" +
                "                \"description\":\"获取天气情况的城市或者国家，比如北京、东京、新加坡\"\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}", Function.class);
        tool1.setFunction(fun1);
        Tool tool2 = new Tool();
        tool2.setType("function");
        Function fun2 = JSONObject.parseObject("{\n" +
                "    \"name\":\"get_local_time\",\n" +
                "    \"description\":\"获取当地的时间\",\n" +
                "    \"parameters\":{\n" +
                "        \"type\":\"object\",\n" +
                "        \"properties\":{\n" +
                "            \"location\":{\n" +
                "                \"type\":\"string\",\n" +
                "                \"description\":\"获取当地时间的城市或者国家，比如北京、东京、新加坡\"\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}", Function.class);
        tool2.setFunction(fun2);
        arg.setTools(Lists.newArrayList(tool1, tool2));
        System.out.println("参数"+JSONObject.toJSONString(arg));
        OpenAIChatComplete.Result result = null;
        try {
            result = FsAI.openai().chatComplete(context, arg);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }

    @Test
    public void queryObjAbstractLayoutAndData() {
        {
            QueryObjDataVO vo = new QueryObjDataVO();
            vo.setObjectAPIName("AIHelperObj__c");
            vo.setId("65dea893f49f4c000143d06b");
            Result actionCallback = objectQueryProxyService.queryObjAbstractLayoutAndData("zhenju0111", 1000, vo);
            System.out.println("AIHelperObj__c --> " + GsonUtil.toJson(actionCallback));
        }

        {
            QueryObjDataVO vo = new QueryObjDataVO();
            vo.setObjectAPIName("PersonnelObj");
            vo.setId("5ffc2e6541ef940001d8e536");
            Result actionCallback = objectQueryProxyService.queryObjAbstractLayoutAndData("zhenju0111", 1000, vo);
            System.out.println("PersonnelObj --> " + GsonUtil.toJson(actionCallback));
        }
    }


    @Test
    public void querySessionRecordsByButtonApiName() {
        AiChatRecordVO vo = new AiChatRecordVO();
        vo.setSessionId("66457e1020333a0001a0ae1f");
        vo.setButtonApiName("button_newmethod");
        Result<List<AiChatRecordResult>> result = aiChatService.querySessionRecordsByButtonApiName("zhenju0111", 1000, vo);
        System.out.println("querySessionRecordsByButtonApiName: " + result);
    }

}
