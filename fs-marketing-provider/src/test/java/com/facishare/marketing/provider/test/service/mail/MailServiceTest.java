/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.mail;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.WebHookCallBackArg;
import com.facishare.marketing.api.result.mail.GetTaskDetailByIdResult;
import com.facishare.marketing.api.result.mail.ListMailMarketingResult;
import com.facishare.marketing.api.result.mail.PageQueryMailTemplateResult;
import com.facishare.marketing.api.result.mail.QueryMailUserSendDetailResult;
import com.facishare.marketing.api.service.mail.MailService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.vo.mail.*;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.mail.TemplateDataTypeEnum;
import com.facishare.marketing.common.enums.mail.WebHookEventEnum;
import com.facishare.marketing.common.exception.BusinessException;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.enums.mail.MailSendReplyTypeEnum;
import com.facishare.marketing.common.enums.mail.SendReplyStatusEnum;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.mail.MailSendTaskResultDAO;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * Created  By zhoux 2020/07/02
 **/
@Slf4j
public class MailServiceTest extends BaseTest {

    @Autowired
    private MailService mailService;

    @Autowired
    private MailManager mailManager;

    @Autowired
    private MailSendTaskResultDAO mailSendTaskResultDAO;

    @Test
    public void getCompanyEmailBaseInfo() {
        mailService.getCompanyEmailBaseInfo("74164");
    }

    @Test
    public void createEmailDomain() {
        CreateEmailDomainVO vo = new CreateEmailDomainVO();
        vo.setApiUser("harry08_test_LKm4uf");
        vo.setApiKey("vtl4B4ezimA7NPIr");
        vo.setDomain("email.ceshi112.com");
        vo.setEa("74164");
        vo.setUserId(1000);
        mailService.createEmailDomain(vo);
    }

    @Test
    public void getDomainDetail() {
        mailService.getDomainDetail("74164");
    }

    @Test
    public void checkDomainConfig() {
        mailService.checkDomainConfig("74164");
    }

    @Test
    public void createApiUser() {
        mailService.createApiUser("74164", 1000);
    }

    @Test
    public void listMailMarketing(){
        String ea = "88146";
        int pageNum = 1;
        int pageSize = 10;
        Result<PageResult<ListMailMarketingResult>> result = mailService.listMailMarketing(ea, 1000, "112111", pageNum, pageSize);
        System.out.print("listMailMarketing result:"+result);
    }

    @Test
    public void listSopMailNotice(){
        String ea = "88146";
        int pageNum = 1;
        int pageSize = 10;
        Result<PageResult<ListMailMarketingResult>> result = mailService.listSopMailNotice(ea, 1000, "652e7015c0c7dc00017a3e91", null, pageNum, pageSize);
        System.out.print("listSopMailNotice result:"+result);
    }

    @Test
    public void querySendReplyData() {
        QuerySendReplyDataVO vo = new QuerySendReplyDataVO();
        vo.setEa("74164");
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setType(MailSendReplyTypeEnum.SENDER.getType());
        mailService.querySendReplyData(vo);
    }

    @Test
    public void addSendReplyData() {
        AddSendReplyDataVO addSendReplyDataVO = new AddSendReplyDataVO();
        addSendReplyDataVO.setEa("74164");
        addSendReplyDataVO.setName("测试地址23");
        addSendReplyDataVO.setType(MailSendReplyTypeEnum.SENDER.getType());
        addSendReplyDataVO.setAddress("<EMAIL>");
        mailService.addSendReplyData(addSendReplyDataVO);
    }

    @Test
    public void updateSendReplyData() {
        UpdateSendReplyDataVO vo = new UpdateSendReplyDataVO();
        vo.setEa("74164");
        vo.setId("29ae69ca8563450592725cfb1c1acb52");
        vo.setAddress("<EMAIL>");
        vo.setName("测试地址2");
        vo.setDefaultValue(true);
        vo.setStatus(SendReplyStatusEnum.DELETE.getStatus());
        mailService.updateSendReplyData(vo);
    }

    @Test
    public void listPagerTemplate() {
        PageQueryTemplateVO vo = new PageQueryTemplateVO();
        vo.setEa("74164");
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setDataType(TemplateDataTypeEnum.COMMON_TEMPLATE.getType());
        mailService.listPagerTemplate(vo);
    }

    @Test
    public void queryMailUserSendDetail() {
        QueryMailUserSendDetailVO vo = new QueryMailUserSendDetailVO();
        vo.setEa("88146");
        vo.setPageNum(1);
        vo.setPageSize(40);
        vo.setTaskId("a194082f8003463ab3b8835786901c68");
        //vo.setEmail("<EMAIL>");
        vo.setFsUserId(1000);
//        vo.setStatus(0);
        mailService.queryMailUserSendDetail(vo);
    }

    @Test
    public void queryMailUserSendDetailWithSubStatus() {
        QueryMailUserSendDetailVO vo = new QueryMailUserSendDetailVO();
        vo.setEa("88146");
        vo.setPageNum(1);
        vo.setPageSize(40);
        vo.setTaskId("5fe3689411104017b5a83c00213bbcf8");
        vo.setFsUserId(1000);
        vo.setSubStatus(Arrays.asList(401, 402, 507));
//        vo.setStatus(0);
        Result<PageResult<QueryMailUserSendDetailResult>> result = mailService.queryMailUserSendDetail(vo);
        List<QueryMailUserSendDetailResult> data = result.getData().getResult();
        boolean match = data.stream().anyMatch(
                queryMailUserSendDetailResult -> Arrays.asList("无效邮件-SendCloud黑名单中", "无效邮件-取消订阅", "软退信-反垃圾邮件被拒")
                        .contains(queryMailUserSendDetailResult.getStatusDesc())
        );

        System.out.println(data);

        Assert.isTrue(match, "不match");
    }

    @Test
    public void queryMailUserSendDetailWithSubStatusSpam() {
        QueryMailUserSendDetailVO vo = new QueryMailUserSendDetailVO();
        vo.setEa("88146");
        vo.setPageNum(1);
        vo.setPageSize(40);
        vo.setTaskId("5fe3689411104017b5a83c00213bbcf8");
        vo.setFsUserId(1000);
        vo.setSubStatus(Arrays.asList(401, 402, 507));
        vo.setStatus(4);
        Result<PageResult<QueryMailUserSendDetailResult>> result = mailService.queryMailUserSendDetail(vo);
        List<QueryMailUserSendDetailResult> data = result.getData().getResult();
        boolean match = data.stream().anyMatch(
                queryMailUserSendDetailResult -> Arrays.asList("无效邮件-SendCloud黑名单中", "无效邮件-取消订阅", "软退信-反垃圾邮件被拒")
                        .contains(queryMailUserSendDetailResult.getStatusDesc())
        );

        System.out.println(data);

        Assert.isTrue(match, "不match");
    }

    @Test
    public void queryMailUserSendDetailWithSubStatusNoSpam() {
        QueryMailUserSendDetailVO vo = new QueryMailUserSendDetailVO();
        vo.setEa("88146");
        vo.setPageNum(1);
        vo.setPageSize(40);
        vo.setTaskId("5fe3689411104017b5a83c00213bbcf8");
        vo.setFsUserId(1000);
        vo.setSubStatus(Arrays.asList(401, 402, 506));
        vo.setStatus(4);
        Result<PageResult<QueryMailUserSendDetailResult>> result = mailService.queryMailUserSendDetail(vo);
        List<QueryMailUserSendDetailResult> data = result.getData().getResult();
        boolean match = data.stream().anyMatch(
                queryMailUserSendDetailResult -> Arrays.asList("无效邮件-SendCloud黑名单中", "无效邮件-取消订阅", "软退信-反垃圾邮件被拒")
                        .contains(queryMailUserSendDetailResult.getStatusDesc())
        );

        System.out.println(data);

        Assert.isTrue(match, "不match");
    }

    @Test
    public void queryLeadTransforInfo() {
        QueryLeadTransforInfoVO vo = new QueryLeadTransforInfoVO();
        vo.setEa("74164");
        //vo.setStartTime(1593532800000L);
        //vo.setEndTime(1610639999000L);
        mailService.queryLeadTransforInfo(vo);
    }

    @Test
    public void queryClickLinkDetail() {
        QueryClickLinkDetailVO vo = new QueryClickLinkDetailVO();
        vo.setEa("74164");
        vo.setTaskId("7dc578f363034050a61681c522c70032");
        mailService.queryClickLinkDetail(vo);
    }

    @Test
    public void updateTemplateDetail() {
        UpdateTemplateDetailVO vo = new UpdateTemplateDetailVO();
        vo.setEa("74164");
        vo.setTemplateId("76cf8855a89943bbaf1a92574b460ef8");
        vo.setHtml("更改数据");
        vo.setName("测试一下模板2020-07-15");
        mailService.updateTemplateDetail(vo);
    }

    @Test
    public void addTemplate() {
        AddTemplateVO vo = new AddTemplateVO();
        vo.setEa("74164");
        vo.setFsUserId(10000);
        vo.setContent("最近，一篇大陆解放台湾必攻澎湖的“美军事专家文章”被台湾媒体疯转。该文章极力渲染所谓“武统”的难度，称澎湖有“6万驻军”，“在任何场景中，时间都不站在大陆一边”，还说什么“如果未能占领澎湖，大陆‘武统’的构想可能要中断较长时间。”");
        vo.setTemplateName("测试一下模板2020-07-15");
        mailService.addTemplate(vo);
    }

    @Test
    public void deleteTemplate() {
        DeleteMailTemplateVO vo = new DeleteMailTemplateVO();
        vo.setEa("74164");
        vo.setId("76cf8855a89943bbaf1a92574b460ef8");
        mailService.deleteTemplate(vo);
    }

   /* {"event":"deliver","message":"Successfully delivered","apiUser":"FS_MARKETING
        _1593759510103_1","category":"FS_MARKETING_1593759510103_1","maillistTaskId":2865399,"emailId":"1594893884431_139353_3402_1593.sc-10_9_6_181-inbound0$<EMAIL>","recipient":"<EMAIL>","labelId":7730891,"timestamp":1594893885761,"token":"3
        mfyDuBHgYHjW9epFD6P9ZpmjTmsJPaa65YGJ0c8PzPHrzRk3J","signature":"f8fa2673e4303b1dd42df3522fc8f57ee9900d63bab73dc1b425f6329a944549","userHeaders":"{\"SC-Custom-Task-Id\":\"d6efd35399154744981aa90936c48573\",\"SC-Custom-EA\":\"74164\",\"SC-Custom-User-Group-Ids\":\"a64e8c5383e14f3485272fa96bc12059\"}"}*/

    @Test
    public void callBack() {
        WebHookCallBackVO vo  = JSON.parseObject("{\"event\":\"open\",\"message\":\"open email\",\"category\":\"FS_MARKETING_1615970594845_0\",\"maillistTaskId\":0,\"emailId\":\"1700556305327_146305_18229_3665.sc-10_9_121_197-inbound0$<EMAIL>\",\"recipient\":\"<EMAIL>\",\"labelId\":25307218,\"timestamp\":1700557097359,\"token\":\"****o2ESA47wa1qzyAm8yhoTBmhBUzKO0VzlKt5OVo2SoM****\",\"signature\":\"da016ce1c8aeb05ad2979f18c4fb2790b68d83827aa87ff2bfce6fd118ffd05c\",\"userHeaders\":\"{\\\"SC-Custom-Task-Id\\\":\\\"d522d8e5086f4e8aa2589507a8592bbe\\\",\\\"SC-Custom-EA\\\":\\\"88146\\\",\\\"SC-Custom-Task-Type\\\":\\\"SOP_MAIL_NOTICE\\\"}\"}", WebHookCallBackVO.class);
        mailService.callBack(vo);
    }

    @Test
    public void callBackToRequestBuffer() throws InterruptedException {
        WebHookCallBackVO vo  = new WebHookCallBackVO();
        vo.setEvent("deliver");
        vo.setMessage("Successfully delivered");
        vo.setApiUser("FS_MARKETING_1593759510103_1");
        vo.setCategory("FS_MARKETING_1593759510103_1");
        vo.setRecipient("<EMAIL>");
        vo.setLabelId(7730891);
        vo.setUserHeaders("{\"SC-Custom-Task-Id\":\"d6efd35399154744981aa90936c48573\",\"SC-Custom-EA\":\"74164\",\"SC-Custom-User-Group-Ids\":\"a64e8c5383e14f3485272fa96bc12059\"}");
        //mailService.callBackToRequestBuffer(vo);
    //    Thread.sleep(1000000L);
    }

    public static void main(String[] args) {
        WebHookCallBackArg vo = new WebHookCallBackArg();
        vo.setEvent("deliver");
        vo.setMessage("Successfully delivered");
        vo.setApiUser("FS_MARKETING_1593759510103_1");
        vo.setCategory("FS_MARKETING_1593759510103_1");
        vo.setMaillistTaskId(111L);
        vo.setEmailId("eeee");
        vo.setRecipient("<EMAIL>");
        vo.setLabelId(7730891);
        vo.setTimestamp(System.currentTimeMillis());
        vo.setToken("eeeee");
        vo.setSignature("eeeee");

        vo.setUserHeaders("{\"SC-Custom-Task-Id\":\"d6efd35399154744981aa90936c48573\",\"SC-Custom-EA\":\"74164\",\"SC-Custom-User-Group-Ids\":\"a64e8c5383e14f3485272fa96bc12059\"}");
        vo.setUrl("WWWW");
        vo.setSubStat(333);

        byte[] a = JSON.toJSONBytes(vo);
        WebHookCallBackVO webHookCallBackVO = JSON.parseObject(a, WebHookCallBackVO.class);
        System.out.println(webHookCallBackVO);
    }
    @Test
    public void createWebHook() {
        mailService.createWebHook("74164");
    }

    @Test
    public void addMailLinkContent() {
        Map<String, String> linkContent = Maps.newHashMap();
        linkContent.put("https://www.baidu.com/", "百度");
        linkContent.put("http://news.baidu.com/", "百度新闻");
        AddMailLinkContentVO addMailLinkContentVO = new AddMailLinkContentVO();
        addMailLinkContentVO.setLinkContent(linkContent);
        addMailLinkContentVO.setEa("74164");
        addMailLinkContentVO.setTaskId("d651f1a66e22456787320e99ae8470e4");
        mailService.addMailLinkContent(addMailLinkContentVO);
    }

    @Test
    public void querySendDetailsByType() {
        QuerySendDetailsByTypeVO vo = new QuerySendDetailsByTypeVO();
        vo.setEa("74164");
        vo.setFsUserId(1000);
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setTaskId("101ba599f1d049b68da634f98f538a1e");
        vo.setType(WebHookEventEnum.REPORT_SPAM.getMarketingUserType());
        mailService.querySendDetailsByType(vo);
    }

    @Test
    public void readSendEmailFromExcel() {
        mailManager.readSendEmailFromExcel("74164","TA_8bb698312aa74b298b9f4a4c8a570315.xlsx");
    }

    @Test
    public void querySendErrorMail() {
        QuerySendErrorMailVO vo = new QuerySendErrorMailVO();
        vo.setEa("74164");
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setFsUserId(1000);
        //vo.setEmail("li");
        //vo.setEventType();
        mailService.querySendErrorMail(vo);
    }

    @Test
    public void deleteSendErrorMail() {
        DeleteSendErrorMailVO vo = new DeleteSendErrorMailVO();
        vo.setIds(Lists.newArrayList("4b139402c05c42eabbbda0761f3053e0", "d9604c36c38c482b97d456169e41723f"));
        mailService.deleteSendErrorMail(vo);
    }

    @Test
    public void queryFilterAddress() {
        QueryFilterAddressVO vo = new QueryFilterAddressVO();
        vo.setEa("74164");
        vo.setFsUserId(1000);
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setTaskId("e67594272c634e918f5dc9b5a99c7013");
        mailService.queryFilterAddress(vo);
    }


    @Test
    public void exportMailUserSendDetail() {
        int pageSize = 2;
        QueryMailUserSendDetailVO vo = new QueryMailUserSendDetailVO();
        vo.setEa("74164");
        vo.setPageNum(1);
        vo.setPageSize(pageSize);
        //b4da28e2ae834a7992cf1e5affb1be4b
        vo.setTaskId("261e1256b1e54193af674c35826af2a1");
        //vo.setEmail("<EMAIL>");
        vo.setFsUserId(1177);
        Result<GetTaskDetailByIdResult> taskDetailResult = mailService.getTaskDetailById(vo.getTaskId());
        if (!taskDetailResult.isSuccess() || taskDetailResult.getData() == null) {
            throw new BusinessException(SHErrorCode.PARAMS_ERROR);
        }
        Result<PageResult<QueryMailUserSendDetailResult>> result = mailService.queryMailUserSendDetail(vo);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData().getResult())) {
            throw new BusinessException(SHErrorCode.SYSTEM_ERROR);
        }
        SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
        String sheetName = "邮件营销-" + taskDetailResult.getData().getSubject() + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm");
        SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
        sxssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillTitles(sxssfSheet, getTitleList());
        ExcelUtil.appendContent(sxssfSheet, buildDataList(result.getData().getResult()),1);


        Integer totalCount = result.getData().getTotalCount();
        int totalPage = totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize) + 1;
        if (totalPage > 1) {
            for (int i = 2; i <= totalCount; i++) {
                vo.setPageNum(i);
                result = mailService.queryMailUserSendDetail(vo);
                // 追加填充每一页的数据
                ExcelUtil.appendContent(sxssfSheet,buildDataList(result.getData().getResult()), (i - 1) * pageSize + 1);
            }
        }


        File file =new File("E:\\work\\test.xlsx");
        try {
            OutputStream out = new FileOutputStream(file,true);
            sxssfWorkbook.write(out);
            out.flush();
            out.close();
        } catch (FileNotFoundException e) {
            log.warn("exception:",  e);
        } catch (IOException e) {
            log.warn("exception:",  e);
        }
    }

    private List<List<Object>> buildDataList(List<QueryMailUserSendDetailResult> sendDetailResultList) {
        List<List<Object>> result = Lists.newArrayList();
        for (QueryMailUserSendDetailResult sendDetailResult : sendDetailResultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(StringUtils.defaultString(sendDetailResult.getName()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getPhone()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getEmail()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getCompanyName()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getStatus()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getStatusDesc()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getSendTime()));
            objectList.add(sendDetailResult.getOpen());
            objectList.add(sendDetailResult.getClick());
            result.add(objectList);
        }
        return result;
    }

    private List<I18nKeyEnum> getTitleList() {
        List<I18nKeyEnum> titleList = new ArrayList<>();
        return titleList;
    }

    @Test
    public void exportMailUserSendDetailTest2() {
        ExportMailUserSendDetailArg exportMailUserSendDetailArg = new ExportMailUserSendDetailArg();
        exportMailUserSendDetailArg.setTaskId("5fe3689411104017b5a83c00213bbcf8");
        exportMailUserSendDetailArg.setEa("88146");
        exportMailUserSendDetailArg.setFsUserId(1000);
        Result<Void>  result = mailService.exportMailUserSendDetail(exportMailUserSendDetailArg);
        log.info("result: {}", result);
    }

    @Test
    public void removeWebHookDeliverEventTest() {
        mailManager.removeWebHookDeliverEvent("88146");
    }

    @Test
    public void getDeliveredEmailTaskScheduleTest() {
        mailService.emailSendStatusTaskSchedule("test");
    }

    @Test
    public void testMailSendTaskResultDAO() {
        String ea = "74164";
        int totalCount = mailSendTaskResultDAO.countMailIdByByTaskId("d54dc2cfd24c43f98b947bef44a807c8", ea);
        int pageSize = 2;
        int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
        for (int i = 1; i <= totalPage; i++) {
            Page page = new Page(i, pageSize, false);
            List<String> emailIdList = mailSendTaskResultDAO.queryEmailIdByTaskId("d54dc2cfd24c43f98b947bef44a807c8", ea, page);
            if (CollectionUtils.isEmpty(emailIdList)) {
                break;
            }
            System.err.println("第 " + i + "页 ，data: " + emailIdList);
        }
    }
    @Test
    public void listPagerMergeTemplate(){
        PageQueryTemplateVO vo = new PageQueryTemplateVO();
        vo.setEa("88146");
        vo.setPageNum(1);
        vo.setPageSize(10);
        Result<PageResult<PageQueryMailTemplateResult>> result = mailService.listPagerMergeTemplate(vo);
        System.out.println(result);
    }

    @Test
    public void refreshMailEaStatisticsJob() {
        mailService.refreshMailEaStatisticsJob("88146");
    }

    @Test
    public void refreshMailTaskStatisticsJob() {
        String arr = "bf9157ee1bb042528ded04c3e41b21b1,1f30190a90be45c39a8b9f927d09a831,06d86a820fa74956a52d6902a012de44,a57cf5ed37e44041903da381e0987d26,338edbb28ab1443da258405b1aac5823,012a345179d94973bab16ea256a824d0,831ce47afb184941a969839cbf308c30,42fc16d03387424494541c2bc975d86d,dad08e21e6f04d4387fca4e871905d90,072dcf0ad7a04c3bbbb679a55f655559,014551e685694b67837f62b4656271f7,b8bd50efe3834ab4b72972e5c65f8b6b,41417d21c9b14bbf94aa7c2720bd3a30,b4b598f6052546248bb6d8556ade7efd,7e251540e35647c3a179efe6bc573c1b,59f1cd78e4b4420ca8d1663f470e1278,7aa6d1a1255848018c7ce557d003a48a,ba616e47b9de42be82f3aef2b24ca0ae,91b6778ef3b946f4a362a8e3f16f23bd,bc28e39ae30b4d2a95d68db1427c8df3,13893dd5fda3498cbcf48949e73d0648,24aebb7aac2b44cba03ac5416b1ffe60,ff99e53db72542508ac080ad9eb540d3,e7eb5a0741c4417fbb5078bb46a21603,003ca129c474450786d444ae92254d52,26c23ac01ddf4085975d1815b5bc1096,42b4e0fd2087477894b9b817f1441b43,c6ae4aa2c34b47e4a4de82535a571202,51f59fd645fe4b799894956d37ac617b,a84c2bab543949d8807854fcde647126,c6ca78ef59654bd28661f724dde1068d,8cccd166374e4af79e794e3b1e464585,fa98b5d933324581814679f93b1c76fe,bc537870abb547ef83eff5f7471869e7,ecfdbf33ab904a52ad06d63f38e4e656,d0706b9d4f1642679cb8d31f6956b425,8fb98fb10a8947daa21b5291987e3525,9f1005176a6845fd8cc7aa6be6d20458,dbaba4b8fc084ea59b0fe10361d433bd,b8638b96e16c48d4ab95880c1b2e2d22,b3ac1959c063485685c403e7d5740523,af378f14d6a5492fad2cb3ceb0c3dc43,f458c562638847898ba5a38dda916f38,3f77e7ac73df4b52891f63fb28d51e8f,fba04c6f5b0042119418d419a1c57e48,b491940fb7e34908b6b5b491d35f24c2,fdaf3a2103e14a4f85cc2d627dad2ed1,5e201e0db0ee4af7a32a73480a018642,6dc7e4a5cdfd453080a6f64de66c1a2f,f05aac52456b4c55ab6ade129d74f656,2b8e83d45be94f3cac8770615c4531e3,8ff4bee3142a49278d279938523444fe,e2e5e5caaa9a4f7a9a3830245ad735a5,8db86e404a0b43e3a37283fb62e89dc1,7df29b6921fa446a95fbca4e4c7b0469,51cfe6aabb6a4997ac6078486931edad,bcf78662ec8e41148dfec99deecde64c,42c2f5c8f49548d69937703b75a3e582,ca10fc678a224d86896ca192571c8abe,6001c1a846b243a09445801457098529,a326681bd18d4954a8f6e43860c34f83,c578de129404491e86c07cd952c42918,b0adc505b82b40568b80b54107606878";
        for (String taskId : arr.split(",")) {
            mailService.refreshMailTaskStatisticsJob("88146", taskId);
        }
    }
}
