/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.KfCallbackArg;
import com.facishare.marketing.api.arg.ListLeadInfosArg;
import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.arg.officialWebsite.*;
import com.facishare.marketing.api.result.OfficialWebsiteLeadsDataResult;
import com.facishare.marketing.api.result.officialWebsite.*;
import com.facishare.marketing.api.result.statistics.GetWebsiteLeadChartResult;
import com.facishare.marketing.api.service.OfficialWebsiteService;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.officialWebsite.OfficialWebsiteEventAttributesTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.BrowserUserThirdPlateformRelationDAO;
import com.facishare.marketing.provider.dao.statistic.MarketingObjectDayStatisticDao;
import com.facishare.marketing.provider.entity.BrowserUserThirdPlateformRelationEntity;
import com.facishare.marketing.provider.innerData.KfCustomerEventContentData;
import com.facishare.marketing.provider.manager.MarketingStatLogPersistorManger;
import com.facishare.marketing.provider.manager.OfficialWebsiteManager;
import com.facishare.marketing.provider.manager.OfficialWebsiteThirdPlateformEventManager;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.manager.officialWebsiteChart.linearGraph.LeadCountLinearGraph;
import com.facishare.marketing.provider.manager.officialWebsiteChart.pieChart.LeadSourceNamePieChart;
import com.facishare.marketing.provider.mq.consumer.handlers.OfficialWebsiteThirdPlateformEventHandler;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.emailproxy.common.thread.ThreadUtil;
import com.facishare.wechat.dubborestouterapi.arg.QueryStoreQrCodeArg;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import com.facishare.wechat.dubborestouterapi.service.union.WechatQrCodeRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.google.common.collect.Lists;

import java.util.*;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created  By zhoux 2019/12/02
 **/
public class OfficialWebsiteServiceTest extends BaseTest {

    @Autowired
    private OfficialWebsiteService officialWebsiteService;
    @Autowired
    private OfficialWebsiteManager officialWebsiteManager;
    @Autowired
    private OfficialWebsiteThirdPlateformEventManager officialWebsiteThirdPlateformEventManager;
    @Autowired
    private OfficialWebsiteThirdPlateformEventHandler officialWebsiteThirdPlateformEventHandler;
    @Autowired
    private WechatQrCodeRestService wechatQrCodeRestService;
    @Autowired
    private BrowserUserThirdPlateformRelationDAO browserUserThirdPlateformRelationDAO;
    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private LeadCountLinearGraph leadCountLinearGraph;
    @Autowired
    private LeadSourceNamePieChart leadSourceNamePieChart;
    @Autowired
    private MarketingObjectDayStatisticDao marketingObjectDayStatisticDao;

    @Test
    public void getSettingInfo() {
        Result<GetSettingInfoResult> getSettingInfoResultResult = officialWebsiteService.getSettingInfo("88146", 1000);
        System.out.println(getSettingInfoResultResult);
        Assert.assertNotEquals(getSettingInfoResultResult, getSettingInfoResultResult.isSuccess());
    }

    @Test
    public void addWebsiteData() {
        AddWebsiteDataArg addWebsiteDataArg = new AddWebsiteDataArg();
        addWebsiteDataArg.setWebsiteName("测试数据");
        addWebsiteDataArg.setWebsiteUrl("http://wiki.firstshare.cn/pages/viewpage.action?pageId=108856548&preview=/108856548/114102749/image2019-11-19_11-30-11.png");
        addWebsiteDataArg.setEa("2");
        addWebsiteDataArg.setFsUserId(1000);
        Result result = officialWebsiteService.addWebsiteData(addWebsiteDataArg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }

    @Test
    public void queryPageData() {
        QueryPageDataArg queryPageDataArg = new QueryPageDataArg();
        queryPageDataArg.setName("测试");
        queryPageDataArg.setEa("2");
        queryPageDataArg.setPageNum(1);
        queryPageDataArg.setPageSize(10);
        queryPageDataArg.setFsUserId(1000);
        Result<PageResult<QueryPageDataResult>> result = officialWebsiteService.queryPageData(queryPageDataArg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }


    @Test
    public void getWebsiteById() {
        GetWebsiteByIdArg getWebsiteByIdArg = new GetWebsiteByIdArg();
        getWebsiteByIdArg.setId("0e7dda30179d4427aa450425b9f5d96f");
        getWebsiteByIdArg.setEa("74164");
        getWebsiteByIdArg.setFsUserId(1000);
        Result<GetWebsiteByIdResult> result = officialWebsiteService.getWebsiteById(getWebsiteByIdArg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }


    @Test
    public void updateWebsiteData() {
        UpdateWebsiteDataArg updateWebsiteDataArg = new UpdateWebsiteDataArg();
        updateWebsiteDataArg.setId("e351e4640369432db1d39b52306b8902");
        updateWebsiteDataArg.setEa("2");
        updateWebsiteDataArg.setFsUserId(1002);
        updateWebsiteDataArg.setWebsiteName("修改数据");
        updateWebsiteDataArg.setWebsiteUrl("https://ceshi114.fxiaokeadmin.com/cas/login?service=https://ceshi114.fxiaokeadmin.com/cms/shiro-cas");
        Result result = officialWebsiteService.updateWebsiteData(updateWebsiteDataArg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }

    @Test
    public void queryWebsiteTrackData() {
        QueryWebsiteTrackDataArg queryWebsiteTrackDataArg = new QueryWebsiteTrackDataArg();
        queryWebsiteTrackDataArg.setEa("88146");
        queryWebsiteTrackDataArg.setFsUserId(1005);
        queryWebsiteTrackDataArg.setPageNum(1);
        queryWebsiteTrackDataArg.setPageSize(10);
        queryWebsiteTrackDataArg.setWebsiteId("5a40dfe4f6014a94bcc29e05fd963b75");
        queryWebsiteTrackDataArg.setStartTime(new Date().getTime()-1000L*3600*24*100);
        queryWebsiteTrackDataArg.setEndTime(new Date().getTime());
        Result<PageResult<QueryWebsiteTrackDataResult>> result = officialWebsiteService.queryWebsiteTrackData(queryWebsiteTrackDataArg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }

    @Test
    public void exportWebsiteTrackData() {
        QueryWebsiteTrackDataArg queryWebsiteTrackDataArg = new QueryWebsiteTrackDataArg();
        queryWebsiteTrackDataArg.setEa("83668");
        queryWebsiteTrackDataArg.setFsUserId(1005);
        queryWebsiteTrackDataArg.setPageNum(1);
        queryWebsiteTrackDataArg.setPageSize(10);
        Result<Void> result = officialWebsiteService.exportWebsiteTrackData(queryWebsiteTrackDataArg);
        System.out.println(result);
   //     ThreadUtil.sleepIngore(5000L);
        Assert.assertNotEquals(result, result.isSuccess());
    }

    @Test
    public void addWebsiteTrackData() {
        AddWebsiteTrackDataArg addWebsiteTrackDataArg = new AddWebsiteTrackDataArg();
        addWebsiteTrackDataArg.setWebsiteId("e351e4640369432db1d39b52306b8902");
        addWebsiteTrackDataArg.setTrackUrl("http://**************:31003/spinnaker/application/pipeline/");
        addWebsiteTrackDataArg.setTrackName("测试");
        addWebsiteTrackDataArg.setEa("2");
        addWebsiteTrackDataArg.setFsUserId(1000);
        Result result = officialWebsiteService.addWebsiteTrackData(addWebsiteTrackDataArg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }


    @Test
    public void updateWebsiteTrackData() {
        UpdateWebsiteTrackDataArg updateWebsiteTrackDataArg = new UpdateWebsiteTrackDataArg();
        updateWebsiteTrackDataArg.setId("faf0a645a21846e4ade7d78d00525c0f");
        updateWebsiteTrackDataArg.setEa("2");
        updateWebsiteTrackDataArg.setFsUserId(1002);
        updateWebsiteTrackDataArg.setTrackName("更改测试");
        updateWebsiteTrackDataArg.setTrackUrl("https://www.tapd.cn/my_worktable?left_tree=1#&filter_close=true");
        Result result = officialWebsiteService.updateWebsiteTrackData(updateWebsiteTrackDataArg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }

    @Test
    public void queryWebsiteFormData() {
        QueryWebsiteFormDataArg arg = new QueryWebsiteFormDataArg();
        arg.setEa("74164");
        arg.setPageNum(1);
        arg.setPageSize(100);
        arg.setId("0e7dda30179d4427aa450425b9f5d96f");
        Result<PageResult<QueryWebsiteFormDataResult>> result = officialWebsiteService.queryWebsiteFormData(arg);
        System.out.println(result);
        Assert.assertNotEquals(result, result.isSuccess());
    }

    @Test
    public void getWebsiteLeadChart() {
//        GetWebsiteLeadChartArg arg = new GetWebsiteLeadChartArg();
//        arg.setEa("74164");
//        arg.setFsUserId(1000);
        //arg.setChartType(ChartTypeEnum.LINEAR_GRAPH.getType());
        //arg.setRecentDate(30);
//        arg.setStartTime(1647075683551L);
//        arg.setEndTime(1649667683551L);
//        arg.setChartType(ChartTypeEnum.LEAD_SOURCE_LINEAR_GRAPH.getType());
//        arg.setId("0e7dda30179d4427aa450425b9f5d96f");
//        Result<GetWebsiteLeadChartResult> websiteLeadChart = officialWebsiteService.getWebsiteLeadChart(arg);
        GetWebsiteLeadChartResult result = leadCountLinearGraph.getChartStatisticsResultByRecentDate("0e7dda30179d4427aa450425b9f5d96f", 1647170865651L, 1649762865651L, "74164");
//        GetWebsiteLeadChartResult result = leadSourceNamePieChart.getChartStatisticsResultByRecentDate("0e7dda30179d4427aa450425b9f5d96f", null, null, "74164");
        System.out.println(result);
    }

    @Test
    public void addWebsiteEventAttributes() {
        AddWebsiteEventAttributesArg addWebsiteEventAttributesArg = new AddWebsiteEventAttributesArg();
        addWebsiteEventAttributesArg.setEa("74164");
        addWebsiteEventAttributesArg.setFsUserId(1000);
        addWebsiteEventAttributesArg.setDataType(OfficialWebsiteEventAttributesTypeEnum.EVENT.getType());
        List<BaseWebSiteAttributesArg> attributesData = Lists.newArrayList();
        BaseWebSiteAttributesArg testAttributesArg1 = new BaseWebSiteAttributesArg();
        testAttributesArg1.setCustomizeId("attributes1");
        testAttributesArg1.setName("测试属性1");
        BaseWebSiteAttributesArg testAttributesArg2 = new BaseWebSiteAttributesArg();
        testAttributesArg2.setCustomizeId("attributes2");
        testAttributesArg2.setName("测试属性2");
        attributesData.add(testAttributesArg1);
        attributesData.add(testAttributesArg2);
        addWebsiteEventAttributesArg.setAttributesData(attributesData);
        BaseWebSiteAttributesArg event = new BaseWebSiteAttributesArg();
        event.setName("测试事件1");
        event.setCustomizeId("event1");
        addWebsiteEventAttributesArg.setContent(event);
        officialWebsiteService.addWebsiteEventAttributes(addWebsiteEventAttributesArg);
    }

    @Test
    public void getEventAttributesDetailById() {
        officialWebsiteService.getEventAttributesDetailById("94d62971e4db41759a919de9f7ee6659");
    }

    @Test
    public void queryEventAttributesDetail() {
        QueryEventAttributesDetailArg arg = new QueryEventAttributesDetailArg();
        arg.setEa("74164");
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setType(OfficialWebsiteEventAttributesTypeEnum.ATTRIBUTES.getType());
        arg.setParentId("94d62971e4db41759a919de9f7ee6659");
        officialWebsiteService.queryEventAttributesDetail(arg);
    }

    @Test
    public void queryUtmCampaigLeadCount() {
        QueryUtmCampaigLeadCountArg queryUtmCampaigLeadCountArg = new QueryUtmCampaigLeadCountArg();
        queryUtmCampaigLeadCountArg.setEa("74164");
        queryUtmCampaigLeadCountArg.setPageNum(1);
        queryUtmCampaigLeadCountArg.setPageSize(20);
        queryUtmCampaigLeadCountArg.setId("0e7dda30179d4427aa450425b9f5d96f");
        queryUtmCampaigLeadCountArg.setEndTime(new Date().getTime());
        queryUtmCampaigLeadCountArg.setStartTime(1607677154000L);
        Result<PageResult<QueryUtmCampaigLeadCountResult>> pageResultResult = officialWebsiteService.queryUtmCampaigLeadCount(queryUtmCampaigLeadCountArg);
        System.out.println(pageResultResult);
    }

    @Test
    public void handlerTest() {
        String str = "{\"city\":\"\",\"channel\":\"\",\"from_page\":\"\",\"remark\":\"加微信没通过;***********\",\"edit_field\":\"\",\"type\":\"edit\",\"style_name\":\"作品集项目\",\"online_to_type\":2,\"land_page\":\"https://m.acgorg.com/yuanxiao/music.html?baidu-2hu-1202872&bd_vid=11191330984237235984\",\"number\":\"1\",\"se\":\"\",\"province\":\"\",\"guest_id\":\"************\",\"mobile3\":\"\",\"mobile2\":\"***********\",\"del_field\":\"\",\"guest_ip\":\"*************\",\"tag_id\":\"********\",\"tag\":\"不相关\",\"id\":\"72d425b18b865398311d75b2d1a61fbb\",\"first_add_account\":\"<EMAIL>\",\"email\":\"<EMAIL>\",\"qq\":\"***********\",\"add_field\":\"\",\"address\":\"\",\"guest_name\":\"11.23+合作\",\"company_id\":\"********\",\"first_add_id6d\":\"********\",\"baiduBCP\":\"efec179a9c252339bc886db9f9b25119\",\"mobile\":\"***********\",\"kw\":\"\",\"from_sys\":\"server\",\"first_add_time\":\"**********\",\"worker_id\":\"<EMAIL>\",\"token\":\"b252fb0a-8cc0-42c5-aa20-440336cde3b8\",\"zipcode\":\"\",\"talk_id\":*************,\"weixin\":\"***********\",\"guest_ip_info\":\"北京市北京市[中国移动]\",\"field\":\"\",\"phone\":\"***********\",\"id6d\":\"********\",\"talk_page\":\"https://m.acgorg.com/yuanxiao/music.html?baidu-2hu-1202872&bd_vid=11191330984237235984\",\"time\":\"**********\",\"cmd\":\"customer\",\"style_id\":\"*********\",\"device\":\"1\",\"field_type\":\"edit\",\"wechat\":\"\",\"aduserid\":\"\",\"kfname\":\"董婷\"}";
        KfCustomerEventContentData data = GsonUtil.fromJson(str, KfCustomerEventContentData.class);
        officialWebsiteThirdPlateformEventHandler.saveLeadsToCrm(data, "74164");
    }


    @Test
    public void queryStoreQrCodeTest() {
        QueryStoreQrCodeArg arg = new QueryStoreQrCodeArg();
        arg.setAppId("FSAID_10b07ac3");
        arg.setPageSize(10);
        arg.setCurrentPage(1);
        arg.setQrCodeName("");
        ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(arg);
        System.out.println(pagerModelResult);
    }


    @Test
    public void thirdPlateformRelationTest() {
        BrowserUserThirdPlateformRelationEntity entity = new BrowserUserThirdPlateformRelationEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa("11111111");
        entity.setThirdUserId(UUIDUtil.getUUID());
        entity.setBrowserUserId(UUIDUtil.getUUID());
        entity.setType(1);
        entity.setLandPage("wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww");
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        int i = browserUserThirdPlateformRelationDAO.insertBrowserUserThirdPlateformRelation(entity);
        System.out.println(i);
    }

    @Test
    public void buildUtmDataMapTest() {
        Map<String, String> map = officialWebsiteThirdPlateformEventHandler.buildUtmDataMap("https://www.acgorg.com/yuanxiao/art.html?utm_source=google_display&utm_campaign=目标受众&utm_term=父母&utm_content=图库图片&gclid=EAIaIQobChMInN6t6OS69gIVUrKWCh0r8QR0EAEYASAAEgKGw_D_BwE");
        System.out.println(map);
        baiduAdMarketingManager.syncCampaignMember("74164", "622acd2dc00f69000159480e", AdSourceEnum.SOURCE_BAIDU.getSource(), map.get("utm_campaign"), map.get("utm_term"), false, null, null, 1L, null);
        marketingStatLogPersistorManger.sendLeadData("74164", "622acd2dc00f69000159480e", null, MarketingStatLogPersistorManger.CHANNEL_AD_BAIDU_SYNC);

    }

    @Test
    public void syncCampaignMember() {

        baiduAdMarketingManager.syncCampaignMember("88146", "677fb2a3310c71000758980d", AdSourceEnum.SOURCE_TENCETN.getSource(), "搜索广告--研发测试", "CRM客户关系管理", false, null, null, null, null);

    }

    @Test
    public void getKfClueCount() {
        Result<GetChannelClueCountResult> result = officialWebsiteService.getLeadsCountBySpreadChannel("74164", "onlineservice");
        System.out.println(result);
    }

    @Test
    public void getKfClueList() {
        ListLeadInfosArg listLeadInfosArg = new ListLeadInfosArg();
        listLeadInfosArg.setChannelValue("onlineservice");
        PageArg pageArg = new PageArg();
        pageArg.setPageNo(1);
        pageArg.setPageSize(10);
//        PageResult<OfficialWebsiteLeadsDataResult> pageOfficialWebsiteLeadsDataResult = metadataControllerServiceManager.listCrmLeads(eieaConverter.enterpriseAccountToId("74164"), 1135, listLeadInfosArg, pageArg, Lists.newArrayList());
//        System.out.println(pageOfficialWebsiteLeadsDataResult);
    }

    @Test
    public void getOfficialLeadsList() {
        GetOfficialWebsiteLeadsArg arg = new GetOfficialWebsiteLeadsArg();
        arg.setPageNo(2);
        arg.setPageSize(10);
        arg.setEa("88162");
        arg.setFsUserId(1000);
       // arg.setKeyword("135");
        Result<PageResult<OfficialWebsiteLeadsDataResult>> result = officialWebsiteService.getOfficialWebsiteLeads(arg);
        System.out.println(result);
        result = officialWebsiteService.getOfficialWebsiteLeads(arg);
        System.out.println(result);
    }

    @Test
    public void getOfficialLineGraphTest() {
//        long start = System.currentTimeMillis();
//        Date endDate = new Date();
//        Date startDate = DateUtil.getSomeDay(endDate, -30);
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        ArrayList<Long> times = Lists.newArrayList();
//        while (endDate.after(startDate)) {
//            startDate = DateUtil.plusDay(startDate, 1);
//            Long time = DateUtil.getTimeStampFromStr(dateFormat.format(startDate));
//            times.add(time);
//        }
//
//        times.stream().sorted();
//        Map<String, Integer> map = officialWebsiteManager.getLeadsTotalByPromotionChannel("74164", times, "onlineservice");
//        long end = System.currentTimeMillis();
//        System.out.println(end - start);
//        System.out.println(map);
    }


    @Test
    public void getTotalOnlineServiceLeadsTest() {
        Map<String, Integer> map = officialWebsiteManager.getOnlineServiceLeadsTotalMap("74164", 1647067677000L, 1649746077000L);
        System.out.println(map);
    }

    @Test
    public void appendLeadMarketingPartnerTest() {
        officialWebsiteManager.appendLeadMarketingPartner("74164");
    }

    @Test
    public void updateLeadsObjStatusTest() {
        officialWebsiteManager.updateMarketingObjStatus("zhenju0111");
    }

    @Test
    public void importWebsiteTrackData() {
        ImportWebsiteTrackDataArg arg = new ImportWebsiteTrackDataArg();
        arg.setEa("74164");
        arg.setFsUserId(1177);
        arg.setTaPath("TA_bd0ceff991b14f959a7306470fa96f6b.xlsx");
        arg.setWebsiteId("0e7dda30179d4427aa450425b9f5d96f");
        Result<Void> result = officialWebsiteService.importWebsiteTrackData(arg);
        System.out.println(result);
    }

    @Test
    public void websiteLeadsCount() {
        GetWebsiteStatisticByIdArg arg = new GetWebsiteStatisticByIdArg();
        arg.setId("5a40dfe4f6014a94bcc29e05fd963b75");
        arg.setEa("88146");
        arg.setStartTime(1703692800000L);
        arg.setEndTime(1703779199999L);
        Integer i = officialWebsiteService.websiteLeadsCount(arg);
        System.out.println(i);
    }
    @Test
    public void officialWebsiteDataBriefing() {
        GetWebsiteStatisticByIdArg arg = new GetWebsiteStatisticByIdArg();
        arg.setId("5a40dfe4f6014a94bcc29e05fd963b75");
        arg.setEa("88146");
        arg.setStartTime(1703692800000L-3600L*1000*24);
        arg.setEndTime(1703779199999L-3600L*1000*24);
        Result<WebsiteDataBriefingResult> websiteDataBriefingResultResult = officialWebsiteService.officialWebsiteDataBriefing(arg);
        Result<List<WebsitesDataTrendResult>> listResult = officialWebsiteService.leadsTrend(arg);
        Result<List<WebsitesDataTrendResult>> trafficked = officialWebsiteService.trafficTrend(arg);
        arg.setSourceType(2);
        Result<TrafficSourceStatisticResult> trafficSourceStatisticResultResult = officialWebsiteService.trafficSourceStatistic(arg);
        System.out.println(websiteDataBriefingResultResult);
    }

    @Test
    public void trafficTrend() {
        GetWebsiteStatisticByIdArg arg = new GetWebsiteStatisticByIdArg();
        arg.setId("5a40dfe4f6014a94bcc29e05fd963b75");
        arg.setEa("88146");
        arg.setStartTime(1703692800000L);
        arg.setEndTime(1703779199999L);
        Result<List<WebsitesDataTrendResult>> trafficked = officialWebsiteService.trafficTrend(arg);
        System.out.println(trafficked);

    }
    @Test
    public void existOfficialWebStatisticsByTime() {
        GetWebsiteStatisticByIdArg arg = new GetWebsiteStatisticByIdArg();
        arg.setId("5a40dfe4f6014a94bcc29e05fd963b75");
        arg.setEa("88146");
        arg.setStartTime(1803692800000L);
        arg.setEndTime(1803779199999L);
        String result = marketingObjectDayStatisticDao.existOfficialWebStatisticsByTime(arg.getEa(), new Date(arg.getStartTime()), new Date(arg.getEndTime()));
        System.out.println(result);

    }

}
