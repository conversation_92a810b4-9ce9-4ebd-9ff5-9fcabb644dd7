/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.wxthirdplatform;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.CloudContextUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class WxThirdCallbackServiceTest extends BaseTest {

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Test
    public void testGetDomain() {
        fun("77740");
//        fun("fktest1356");
//        fun("beikecrm");
    }

    public void fun(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        System.out.println("ei = " + ei);
        GetEnterpriseDataResult getEaDataResult = CloudContextUtil.doInCloudContextWithReturn(String.valueOf(ei), () -> enterpriseEditionService.getEnterpriseData(new GetEnterpriseDataArg(ei, ea)));
        String domain = "abc";
        EnterpriseData enterpriseData = getEaDataResult.getEnterpriseData();
        if (enterpriseData != null) {
            System.out.println(enterpriseData);
            System.out.println(enterpriseData.getAddress());
            System.out.println(enterpriseData.getDomain());
        } else {
            System.out.println("enterpriseData为空");
        }
    }

}
