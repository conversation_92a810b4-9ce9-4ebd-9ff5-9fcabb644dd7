/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.api.data.SpreadChannelInfoData;
import com.facishare.marketing.api.data.SpreadChannelListData;
import com.facishare.marketing.api.data.SpreadChannelOptionData;
import com.facishare.marketing.api.vo.QuerySpreadChannelListVO;
import com.facishare.marketing.api.vo.SaveSpreadChannelInfoVO;
import com.facishare.marketing.api.vo.UpdateChannelVO;
import com.facishare.marketing.common.enums.SystemPromotionChannelEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.TextUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.UrlUtils;
import com.facishare.marketing.provider.manager.SpreadChannelManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.util.logging.Log_$logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by zhengh on 2020/10/27.
 */
@Slf4j
public class SpreadChannelManagerTest extends BaseTest {
    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Test
    public void querySpreadChannelList(){
        String ea = "74164";
        Integer fsUserId = 1074;
        Result<List<SpreadChannelOptionData>> result = spreadChannelManager.querySpreadChannelList(ea, fsUserId);
        System.out.print("querySpreadChannelList result:"+ result);

    }

    @Test
    public void addChannel(){
        String ea = "74164";
        Integer fsUserId = 1135;
        List<SpreadChannelManager.SpreadChannleOption> spreadChannleOptionList = Lists.newArrayList();
        SpreadChannelManager.SpreadChannleOption option = new SpreadChannelManager.SpreadChannleOption();
        option.setLabel("西瓜");
        option.setValue("watermelon");
        spreadChannleOptionList.add(option);
        spreadChannelManager.addChannel(ea,fsUserId,spreadChannleOptionList,true);
    }

    @Test
    public void delete(){
        String ea = "74164";
        Integer fsUserId = 1071;
        String channel = "360";
        spreadChannelManager.delChannel(ea, fsUserId, channel);
    }

    @Test
    public void enableMarketingPromotionChannel(){
        String ea = "74164";
        spreadChannelManager.enableMarketingPromotionChannel(ea);
    }

    @Test
    public void updateChannel(){
        UpdateChannelVO vo = new UpdateChannelVO();
        vo.setEa("74164");
        vo.setFsUserId(1071);
        vo.setValue("2pn0bzxFW");
        vo.setName("协会");
        spreadChannelManager.updateChannel(vo);
    }

    @Test
    public void saveSpreadChannelInfo() {
        SaveSpreadChannelInfoVO vo = new SaveSpreadChannelInfoVO();
        vo.setEa("74164");
        vo.setFsUserId(1135);
//        vo.setMarketingEventId("61b80971bf5ee7000171c560");
        vo.setHexagonSiteId("*****************");
        vo.setChannelValue("other: ++E5++AA++92++E4++BD++93++E2++80++94++E6++89++AC++E5++B8++86++E5++87++BA++E6++B5++B7");
        vo.setObjectType(26);
        vo.setObjectId("349ee6c75a414761bba4f84080402b07");
        vo.setH5QrCodeAPath("/FSC/EM/File/GetByPath?path=A_202112_27_5721c2a5032749c7bcef1c98621fa936.jpg");
        vo.setMiniappQrCodeAPath("/FSC/EM/File/GetByPath?path=A_202112_27_cfc454a93d7440f0ba0d7b1169373f5f.jpg");
        vo.setBaiduQrCodeAPath(null);
        vo.setShortUrl("https://fs8.ceshi112.com/kc8LKi");
        vo.setLongUrl("https://www.ceshi112.com/ec/cml-marketing/release/web/cml-marketing.html?byshare=1&_hash=/cml/h5/spread_product_detail&id=c7949d9cc4644fc0bc0511f62e2d9a87&marketingEventId=*****************&marketingActivityId=&spreadChannel=offiaccount&wxAppId=wx2e8a14a356c46c27");
        vo.setMiniappUrl("/pages/share/share?objectType=4&objectId=c7949d9cc4644fc0bc0511f62e2d9a87&marketingEventId=*****************&spreadChannel=offiaccount&hostType=fs&ea=74164");
        vo.setMiniappForwardUrl("https://wxaurl.cn/0rjmqQUflqb");
        vo.setBaiduQrUrl("https://fs8.ceshi112.com/kc8LKi");
        vo.setH5QrUrl("https://fs8.ceshi112.com/kc8LKi");
        vo.setMiniappQrUrl("https://fs8.ceshi112.com/kc8LKi");
        vo.setWxAppId("wx2e8a14a356c46c27");

        vo.setChannelValue(TextUtil.replaceText(vo.getChannelValue(), "++", "%"));
        vo.setChannelValue(UrlUtils.urlDecode(vo.getChannelValue()));
        if (org.apache.commons.lang3.StringUtils.equals(vo.getChannelValue(), SystemPromotionChannelEnum.OFFIACCOUNT.getValue()) && org.apache.commons.lang3.StringUtils.isNotBlank(vo.getWxAppId())) {
            vo.setChannelValue(vo.getChannelValue()+":"+vo.getWxAppId());
        }
        log.info("vo result:{}", vo);
      //  Result<Void> result = spreadChannelManager.saveSpreadChannelInfo(vo);
      //  log.info("result:{}", result);
    }

    @Test
    public void querySpreadChannelInfo() {
        Result<SpreadChannelInfoData> result = spreadChannelManager.querySpreadChannelInfo("88146", "f6ae135bfdbe4cfdb09e7d4b9a687aab");
        log.info("result:{}", result);
    }

    @Test
    public void querySpreadChannelListData() {
        QuerySpreadChannelListVO vo = new QuerySpreadChannelListVO();
        vo.setEa("74164");
        vo.setFsUserId(1177);
        vo.setMarketingEventId("62ff54cfcb87b00001a78cc7");
        //vo.setHexagonSiteId("*****************");
        vo.setObjectId("298341f4b10e4c88b179e0c093c78586");
        vo.setObjectType(26);
        Result<List<SpreadChannelListData>> result = spreadChannelManager.querySpreadChannelListData(vo);
        log.info("result:{}", result);
    }

    @Test
    public void matchChannelLabelTest() {
        String label = spreadChannelManager.matchChannelLabel("74164", 1177, "offiaccount", "wx2e8a14a356c46c27");
        log.info("res:{}", label);
    }
}
