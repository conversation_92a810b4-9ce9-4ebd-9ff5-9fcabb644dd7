/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.provider.innerArg.MetadataTagFindByTemplateArg;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MeatadataTagManagerTest extends BaseTest {
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Test
    public void getTagIdsByTagNames(){
        String ea = "88146";
        String describeApiName = "LeadsObj";
        List<TagName> tagNames = Lists.newArrayList();
        tagNames.add(new TagName("aabbcc", "ddeeff"));
     //   tagNames.add(new TagName("标签1", "标签1-4"));
        Map<TagName, String> mp = metadataTagManager.getTagIdsByTagNames(ea, describeApiName, tagNames);
        System.out.println("getTagIdsByTagNames:"+ mp);
    }

    @Test
    public void addTag(){
        List<TagName> tagNameList = new ArrayList<>();
        tagNameList.add(new TagName("测试等级","等级8"));
        metadataTagManager.addTagsToObjectDatas("zhenju0111", CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),Lists.newArrayList("64a3cfcdde12ee0001535bce"),tagNameList);
        System.out.println("success");
    }

    @Test
    public void bulkHangTag(){
        metadataTagManager.bulkHangTag("88164", 1000, "WechatWorkExternalUserObj", Lists.newArrayList("649bff4cdefe440001bdbef9"), Lists.newArrayList("64ddc694551b9500011b0ae7"));
    }

    @Test
    public void queryLists(){
        Map<String, List<TagName>> leadsObj = userMarketingAccountManager.listTagNameListByUserMarketingAccountIds("88146", Lists.newArrayList("LeadsObj"), Lists.newArrayList("08b0151303de4f82a0fc7b5f26d77a53"));
        System.out.println(leadsObj);
    }

    @Test
    public void scanObjectDataIdByTemplate() {
        MetadataTagFindByTemplateArg arg = new MetadataTagFindByTemplateArg();
        arg.setDescribeApiName("WechatWorkExternalUserObj");
        arg.setTagOperator("IN");
        arg.setConnector("and");
        arg.setTagIds(Lists.newArrayList("6763c05fe461930001629a05", "6763c1da6960490001359322"));
        List<TagName> tagNames = Lists.newArrayList(new TagName("客户等级", "一般"), new TagName("客户等级", "呵呵"));
        List<String> result = metadataTagManager.getObjectDataIdsByTagNameList("88146", 1000, "WechatWorkExternalUserObj", "", tagNames, 0, 0);
        System.out.println(result);
    }
}
