/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.mankeep.common.util.FileUtil;
import com.facishare.marketing.api.result.file.GenerateUploadFileOmitResult;
import com.facishare.marketing.common.util.HttpClientUtil;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.image.ImageCreator;
import com.facishare.marketing.provider.manager.image.ImageDrawer;
import com.facishare.marketing.provider.manager.image.ImageDrawerTypeEnum;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.stone.sdk.response.StoneSaveImageFromTempFileResponse;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.google.common.collect.Lists;
import com.ning.http.client.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.*;

/**
 * @ClassName FileV2ManagerTest
 * @Description
 * <AUTHOR>
 * @Date 2019/3/15 5:55 PM
 */
@Slf4j
public class FileV2ManagerTest extends BaseTest {

    @Autowired
    private FileV2Manager fileV2Manager;

//    @Autowired
//    private ImageCreator imageCreator;

    @Test
    public void zoom() {

        String url = fileV2Manager.zoom(
            "https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=6AA905C78D85C91DEA2363750A6B600D344477122BBD303B24F29247145D6B86F8FFF7E00CCB8214859E0E7D0BA10DEF01186A4CEEA4123F161A6D0644A722A9F78E322F64972E59",
            null, 128);
        System.out.println(url);
    }

    @Test
    public void uploadToTApathTest() {
        String ea = "74164";
        /*
        Integer userId= 1000;
        byte[] bytes = FileUtil.file2Bytes("C:\\Users\\<USER>\\Pictures\\新建文件夹\\12233.jpg");
        System.out.print("bytes size:"+ bytes.length);
        String tapath = fileV2Manager.uploadToTApathBybusiness(bytes, "jpg", ea, userId, "fs-marketing-provider");
        //String tapath = fileV2Manager.uploadToTApath(bytes, "jpg", ea, userId);
        System.out.println("tapath:"+tapath);
       */
        String tapath = "TA_4244ed76ce2c4737bb7ddad4740918d4.jpg";
        //byte[] xx = fileV2Manager.downloadAfile(tapath, 1071, ea);
        //System.out.print("xx:"+xx);

    }

    @Test
    public void uploadToNPath() {
        byte[] bytes = FileUtil.file2Bytes("C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\1.jpg");
        fileV2Manager.uploadToNPath(bytes, "jpg", "74164", null);
    }

    @Test
    public void downloadAFile(){
        String apath = "A_202105_08_46038544d9e34d798028a8d77bb85d86.png";
        Integer userId = 1000;
        String fsEa = "82526";
         byte[] bys = fileV2Manager.downloadAFile(apath,  userId, fsEa);
         if (bys == null){
             log.info("11111111111111");
         }else {
             log.info("********** size:{}", bys.length);
         }
    }

    @Test
    public void changeAWarehouseTempToPermanentBybusiness(){
        String ea = "74164";
        Integer userId = 1155;
        String fileExt = "png";
        String path = "TA_319a8060253645f3814081332c3f5d3e";
        String apath = fileV2Manager.changeAWarehouseTempToPermanentBybusiness(ea, userId, fileExt, path, "fs-marketing");
        log.info("changeAWarehouseTempToPermanentBybusiness apath:{}", apath);
    }

    @Test
    public void getFileTotalSize() throws IOException {
        String path = "A_202208_11_4ad6cd975fe243e1a93b6e72742b471e";
        long size = fileV2Manager.getFileTotalSize(Lists.newArrayList("A_202208_11_abf612b1529448cdaee451ce414f60af", "A_202208_11_4ad6cd975fe243e1a93b6e72742b471e"));
        log.info("dd size : {}", size);
    }

    @Test
    public void uploadCardTemplate(){
//        byte[] bytes1 = FileUtil.file2Bytes("C:\\Users\\<USER>\\Desktop\\名片多模板\\1.png");
//        String apath1 = fileV2Manager.uploadToApath(bytes1, "jpg", null);
//        String url1 = fileV2Manager.getUrlByPath(apath1, null, false);
//        System.out.println(url1);
//
//        byte[] bytes2 = FileUtil.file2Bytes("C:\\Users\\<USER>\\Desktop\\名片多模板\\2.png");
//        String apath2 = fileV2Manager.uploadToApath(bytes2, "jpg", null);
//        String url2 = fileV2Manager.getUrlByPath(apath2, null, false);
//        System.out.println(url2);
//
//        byte[] bytes3 = FileUtil.file2Bytes("C:\\Users\\<USER>\\Desktop\\名片多模板\\3.png");
//        String apath3 = fileV2Manager.uploadToApath(bytes3, "jpg", null);
//        String url3 = fileV2Manager.getUrlByPath(apath3, null, false);
//        System.out.println(url3);
//
//        byte[] bytes4 = FileUtil.file2Bytes("C:\\Users\\<USER>\\Desktop\\名片多模板\\4.png");
//        String apath4 = fileV2Manager.uploadToApath(bytes4, "jpg", null);
//        String url4 = fileV2Manager.getUrlByPath(apath4, null, false);
//        System.out.println(url4);
//
//        byte[] bytesLogo = FileUtil.file2Bytes("C:\\Users\\<USER>\\Desktop\\名片多模板\\logo.jpg");
//        String apathLogo = fileV2Manager.uploadToApath(bytesLogo, "jpg", null);
//        String urlLogo = fileV2Manager.getUrlByPath(apathLogo, null, false);
//        System.out.println(urlLogo);

        byte[] whiteBack = FileUtil.file2Bytes("C:\\Users\\<USER>\\Desktop\\名片多模板\\名片背景\\白底.jpg");
        String whiteBackPath = fileV2Manager.uploadToApath(whiteBack, "jpg", null);
        String image = fileV2Manager.getUrlByPath(whiteBackPath, null, false);
        System.out.println(image);
    }
//
//    @Test
//    public void drawCardTemplate(){
//        ImageDrawer imageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.CardShareCover);
//        Map<String, Object> params = new HashMap<>();
//        params.put("uid","7a92519a6447425b95adead3ad647cf7");
//        String path = imageDrawer.draw(params);
//        String url = fileV2Manager.getUrlByPath(path, null, false);
//        System.out.println(url);
//    }

    @Test
    public void test1(){
        String ea = "74164";
        Integer fsUserId = 1000;
        List<String> paths = Lists.newArrayList("A_202208_22_01d9ebfff716492893ceb1aec19142a0.xls", "A_202208_19_e4541aea9de74a579f7f43458188bcf1.7z", "A_202208_03_44964d9fa87d420b9acbc3da9a12ba57.xlsx");
        Map<String, String> stringStringMap = fileV2Manager.batchGetPreviewUrl(ea, fsUserId, paths);
        System.out.println("yes:" + stringStringMap);
    }

    @Test
    public void createTnFileFromAfile(){
        String ea = "88146";
        Integer fsUserId = 1000;
        String path = "A_202304_19_52d2b01a9649442a9e4e623a503b146d.pdf";
        fileV2Manager.CreateTNFileFromAFile(ea, fsUserId, path);
    }

    @Test
    public void fileExistInCurrentTenant(){
        String ea = "88146";
        String path = "C_202311_22_ca299b1232cd4a8bb6c8faa20b58357c.pdf";
        fileV2Manager.fileExistInCurrentTenant(ea, path);
    }

    @Test
    public void getCpathByPath(){
        String ea = "88146";
        String path = "A_202402_19_f7b6e89945744767b4aaa97d057c7dd4";
        String path1 = "A_202402_05_f004531e57f44e35bba6cb4faabfa906";
        String cpathByPath = fileV2Manager.getCpathByPath(ea, path);
        String cpathByPath1 = fileV2Manager.getCpathByPath(ea, path1);
        System.out.println(cpathByPath);
    }

    @Test
    public void changeListCWarehouseTempToPermanent(){
        String ea = "88146";
        ArrayList<String> list = Lists.newArrayList();
        list.add("TC_7deed8aadc6c427d84c26627b33cc3b4");
        list.add("TC_a64b33564a4b4c54aad6f877e9282b50");
        list.add("TC_a64b33564a4b4c54aad6f877e9282b50");
        list.add("TC_a64b33564a4b4c54aad6f877e9282b50");
        list.add("TC_a64b33564a4b4c54aad6f877e9282b50");
        Map<String, String> stringStringMap = fileV2Manager.changeListCWarehouseTempToPermanent(ea,  list);
        System.out.println(stringStringMap);
    }

    @Test
    public void uploadToTCpathOrTNpath() {
        String ea = "88146";
        String apath = "A_202105_08_46038544d9e34d798028a8d77bb85d86.png";
        Integer userId = 1000;
        byte[] bys = fileV2Manager.downloadAFile(apath,  userId, ea);
        String s = fileV2Manager.uploadToTCpathOrTNpath(ea, bys, true, "libai");
        System.out.println(fileV2Manager.getUrlByPath(ea,s));
        String s1 = fileV2Manager.uploadToTCpathOrTNpath(ea, bys, false, "libai2");
        System.out.println(fileV2Manager.getUrlByPath(ea,s1));
        Assert.assertTrue(StringUtils.isNotEmpty(s1));
    }

    @Test
    public void generateUploadFileOmit(){
        Optional<GenerateUploadFileOmitResult> result = fileV2Manager.generateUploadFileOmit("88146", 3600, "TC", "test", "jpg", 1024);
        log.info("generateUploadFileOmit result : {}", result.get());
        Assert.assertTrue(result.get() != null);
    }

    @Test
    public void getUrlByCPathNoCheckEa(){
        String ea = "88146";
        String path = "C_202311_22_ca299b1232cd4a8bb6c8faa20b58357c";
        String urlByCPathNoCheckEa = fileV2Manager.getUrlByCPathNoCheckEa(ea, path, null, null, null, null);
        System.out.println(urlByCPathNoCheckEa);
        urlByCPathNoCheckEa = fileV2Manager.getUrlByCPathNoCheckEa(ea, path, "100", "100", "100", "100");
        System.out.println(urlByCPathNoCheckEa);
    }

    @Test
    public void getFileSize(){
        Optional<Long> size = fileV2Manager.getFileSizeByPath("88146", null, "C_202405_15_32506fc271184701a484fc193c93812e");
//        Optional<Long> size = fileV2Manager.getFileSizeByPath("88146", null, "A_202003_27_af9c010bf7ee432dace3cf4eea9cfcaa.jpg");
        System.out.println(size.get());
    }
    @Test
    public void getUrl(){
        byte[] bytes = FileUtil.file2Bytes("/Users/<USER>/aliDownload/上线配置/名片多模板/1.png");
        String apath = fileV2Manager.uploadToApath(bytes, "png", null);
        System.out.println(apath);
    }

    @Test
    public void getPreviewUrl(){
        String urlByPath = fileV2Manager.getUrlByPath("A_202406_27_946a5ad934c1418aac5b4600d12501ba.png", null, false);
        System.out.println(urlByPath);
    }
    @Test
    public void copyCFileToCFile(){
        Optional<String> cFile = fileV2Manager.copyCFileToCFile("88146", "88146", 1000, "C_202405_15_32506fc271184701a484fc193c93812e", true);
        log.info("generateUploadFileOmit result : {}",cFile);
        copyAPathToCPath();
    }
    @Test
    public void copyAPathToCPath(){
        Optional<String> cFile = fileV2Manager.copyAPathToCPath("88146",  1000,"A_202105_08_46038544d9e34d798028a8d77bb85d86.png" );
        log.info("generateUploadFileOmit result : {}",cFile);
    }
}
