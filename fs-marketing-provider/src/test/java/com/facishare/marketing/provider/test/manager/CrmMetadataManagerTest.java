/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.mq.handler.SaleLeadTransferMessageHandler;
import com.facishare.marketing.provider.mq.handler.dto.CrmEventDTO;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.FindAllTagByBulkDataIdArg;
import com.fxiaoke.crmrestapi.arg.FindAllTagByDataIdArg;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.ContactFieldContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataTagDataService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.facishare.marketing.common.contstant.OperatorConstants.EQ;

@Slf4j
public class CrmMetadataManagerTest extends BaseTest {
    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private MetadataTagDataService metadataTagDataService;


    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;
    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private EIEAConverter eieaConverter;


    @Test
    public void batchGetByIdsV3(){
        List<String> ids = Lists.newArrayList();
        ids.add("6463309b5d91f00001edbd95");


        List<String> selectFields = Lists.newArrayList( "name", "enterprise_wechat_user_id", "leads_id__relation_ids", "leads_id");
        List<ObjectData>  result = crmMetadataManager.batchGetByIdsV3("83668", -10000, "LeadsObj", null, ids);
        log.info("batchGetByIdsV3 result:{}", result);
    }

    @Test
    public void findByIdV3(){
        String ea = "74164";
        Integer fsUserId = -10000;
        String apiName = "LeadsObj";
        List<String> selectFields = null;
        String id = "62e8c1c3511d6c00010c08dc";
        Object objectData = crmMetadataManager.findByIdV3(ea, fsUserId, apiName, Lists.newArrayList("_id", "tel"), id);
        log.info("findByIdV3 objectData:{}", objectData);
    }

    @Test
    public void listV3(){

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 10);
        paasQueryArg.addFilter("name", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("自动化提交+1716185637541"));
        paasQueryArg.setSearchSource("es");
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
        List<String> selectFields = Lists.newArrayList( "name", "_id");
        findByQueryV3Arg.setSelectFields(selectFields);
        InnerPage<ObjectData> result =  crmMetadataManager.listV3("88146", -10000, findByQueryV3Arg);
        log.info("batchGetByIdsV3 arg: {} result:{}", JsonUtil.toJson(findByQueryV3Arg), result);

    }

    public static void main(String[] args) {
        String rules = "[{\"fieldName\":\"name\",\"fieldValues\":[\"周大哈\"],\"operator\":\"LIKE\",\"fieldType\":1}]";
        List<PaasQueryArg.Condition> conditionList = GsonUtil.fromJson(rules, new TypeToken<List<PaasQueryArg.Condition>>() {
        }.getType());
        System.out.println(conditionList);
        conditionList = JSONObject.parseArray(rules, PaasQueryArg.Condition.class);
        System.out.println("---------");
        System.out.println(conditionList);

    }

    @Test
    public void testAdSendBack() {
        // update测试
//        CrmEventDTO.CrmEvent crmEvent = new CrmEventDTO.CrmEvent();
//        CrmEventDTO.Body body = new CrmEventDTO.Body();
//        ObjectData objectData = new ObjectData();
//        objectData.put("name", "发飞飞发而非22");
//        body.setAfterTriggerData(objectData);
//        body.setTriggerType("u");
//        body.setEntityId("LeadsObj");
//        body.setObjectId("647ea10c158be3000161fce9");
//        body.setContext(new CrmEventDTO.Context());
//        body.getContext().setUserId("1005");
//        crmEvent.setBody(Lists.newArrayList(body));
//        crmEvent.setTenantId("83668");
//        adOCPCUploadManager.handleAdDataSendBack(83668, crmEvent, null);

        // 标签测试
//        CrmEventDTO.CrmEvent crmEvent = new CrmEventDTO.CrmEvent();
//        crmEvent.setTenantId("83668");
//        CrmEventDTO.Body body = new CrmEventDTO.Body();
//        body.setEntityId(CrmObjectApiNameEnum.CRM_LEAD.getName());
//        body.setTriggerType(CrmEventDTO.Body.UPDATE);
//        body.setObjectId("647ea10c158be3000161fce9");
//        ObjectData objectData = new ObjectData();
//        objectData.put("tag", Lists.newArrayList("62cd161023f3480001fb7ae8"));
//        body.setAfterTriggerData(objectData);
//        crmEvent.setBody(Lists.newArrayList(body));
//        adOCPCUploadManager.handleAdDataSendBack(83668, crmEvent, null);

        // insert测试
//        CrmEventDTO.CrmEvent even = new CrmEventDTO.CrmEvent();
//        CrmEventDTO.Body body = new CrmEventDTO.Body();
//        body.setEntityId("LeadsObj");
//        body.setTriggerType(CrmEventDTO.Body.INSERT);
//        ObjectData afterTriggerData = new ObjectData();
//        afterTriggerData.put("tag", Lists.newArrayList("62cd161023f3480001fb7ae8"));
//        body.setAfterTriggerData(afterTriggerData);
//        body.setObjectId("647ea10c158be3000161fce9");
//        even.setBody(Lists.newArrayList(body));
//        even.setTenantId("83668");
//         adOCPCUploadManager.handleAdDataSendBack(83668, even, null);

//         转换测试
        SaleLeadTransferMessageHandler.SaleLeadTransferEven transferEven = new SaleLeadTransferMessageHandler.SaleLeadTransferEven();
        transferEven.setOperationType("transfer");
        transferEven.setTransferObjName(Lists.newArrayList(CrmObjectApiNameEnum.CUSTOMER.getName()));
        transferEven.setTenantId("83668");
        transferEven.setObjectApiName("LeadsObj");
        transferEven.setDataIds(Lists.newArrayList("647ea10c158be3000161fce9"));
        adOCPCUploadManager.handleAdDataSendBack(83668, null, transferEven);

    }

    @Test
    public void createLandingObjTest() {
        String ea = "74164";
        Map<String, Object> crmObjectData = new HashMap<>();
        crmObjectData.put("landing_page_type", "ad");
        crmObjectData.put("landing_page_url", "www.baidu.com");
        crmObjectData.put("marketing_event_id", "63203cca7cb448000181fd0b");
        List<String> owner = Lists.newArrayList("-10000");
        crmObjectData.put("owner", owner);
        crmObjectData.put("created_by", owner);
        crmObjectData.put("name", "落地页测试3");
        Map<String, Object> result = crmMetadataManager.addMetadata(ea, -10000, CrmObjectApiNameEnum.LANDING_PAGE_OBJ.getName(), crmObjectData);
        log.info("创建落地页，result:[{}]", result);
    }

    @Test
    public void queryLandingTest() {
        String ea = "74164";
//        FindByQueryV3Arg queryV3Arg = new FindByQueryV3Arg();
//        queryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.LANDING_PAGE_OBJ.getName());
//        queryV3Arg.setSelectFields(Lists.newArrayList("_id"));
//        PaasQueryArg query = new PaasQueryArg(0, 1);
//        query.addOrderByAsc("create_time", false);
//        query.addFilter("landing_page_url", "EQ", Lists.newArrayList("www.baidu.com"));
//        queryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(query));
//        InnerPage<ObjectData> landObjPage = crmMetadataManager.listV3(ea, -10000, queryV3Arg);
//        log.info("创建落地页，queryLandingTest:[{}]", landObjPage);
        String rules = "[{\"fieldName\":\"tag\",\"fieldValues\":[\"5e858ebc396058000184723e\"],\"operator\":\"EQ\",\"fieldType\":7}]";
        List<PaasQueryArg.Condition> conditionList = JSONObject.parseArray(rules, PaasQueryArg.Condition.class);
        PaasQueryArg.Condition condition = new PaasQueryArg.Condition("_id", Lists.newArrayList("632e69e093936f000109bf53"), EQ);
        conditionList.add(condition);

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.setFilters(conditionList);

        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
        InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(ea, -10000, findByQueryV3Arg);
        log.info("object result:[{}]", objectDataInnerPage);

        FindAllTagByDataIdArg findAllTagByDataIdArg = new FindAllTagByDataIdArg(
                74164, CrmObjectApiNameEnum.CRM_LEAD.getName(), "632e69e093936f000109bf53");
        findAllTagByDataIdArg.setDataId("632e69e093936f000109bf53");
        MetadataTagResult<List<MetadataTagData>> metadataTagResult = metadataTagDataService.findAllTagByDataId(findAllTagByDataIdArg);
        log.info("metadataTagResult result:[{}]", metadataTagResult);

        FindAllTagByBulkDataIdArg arg = new FindAllTagByBulkDataIdArg(74164, CrmObjectApiNameEnum.CRM_LEAD.getName(), Lists.newArrayList("632ebb7893936f00010ca637"));
        MetadataTagResult<List<DataIdAndMetadataTagData>> tagResult = metadataTagDataService.findAllTagByBulkDataId(arg);
        log.info("metadataTagResult result:[{}]", tagResult);
    }

    @Test
    public void updateLead() {
        String ea = "74164";
        Map<String, Object> objectData = new HashMap<>();
        objectData.put("landing_page_id", "6295e5c0bf6e9b00018795a3");
        CreateLeadResult createLeadResult = crmV2Manager.updateLead("632856d017378c0001972518", ea, -10000, objectData);
        log.info("createLeadResult ： {}", createLeadResult);
    }

    @Test
    public void updateFieldDesc() {
        Integer ei = eieaConverter.enterpriseAccountToId("83668");

        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> oldDescribeResult =
                objectDescribeService.getDescribe(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.AD_DATA_RETURN_DETAIL_OBJ.getName());
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("name");
        log.info("fieldDescribe ： {}", fieldDescribe);

        fieldDescribe.put("is_required", false);
        fieldDescribe.put("label", "编码");
        fieldDescribe.put("label_r", "编码");
        fieldDescribe.put("type", "auto_number");
        fieldDescribe.put("prefix", "{yyyy}{mm}{dd}-");
        fieldDescribe.put("postfix", "");
        fieldDescribe.put("serial_number", 6);
        fieldDescribe.put("start_number", 1);
        fieldDescribe.put("condition", "none");
        fieldDescribe.put("field_num", null);
        fieldDescribe.put("define_type", "package");
        fieldDescribe.put("is_index_field", false);
        fieldDescribe.put("is_single", false);
        fieldDescribe.put("max_length", 100);
        fieldDescribe.put("status", "release");

        Result<ControllerGetDescribeResult> result =  objectDescribeService.updateField(new HeaderObj(83668, -10000), CrmObjectApiNameEnum.AD_DATA_RETURN_DETAIL_OBJ.getName(), "name", fieldDescribe);

        log.info("result : {} after fieldDescribe ： {}", result, fieldDescribe);

        Map<String, Object> crmObjectData = new HashMap<>();
        crmObjectData.put("ad_account_id", "test");
        crmObjectData.put("ad_platform", "baidu");
        crmObjectData.put("marketing_event_id", "603742062f19250001958812");
        crmObjectData.put("lead_id", "632bc3061a22b20001fabc93");
        String reason = "ceshi";
        crmObjectData.put("send_back_reason", reason);
        crmObjectData.put("landing_page", "632bbd611a22b20001fa583c");
        crmObjectData.put("status", "success");
        crmObjectData.put("convent_type", 1);
        crmObjectData.put("send_back_time", System.currentTimeMillis());
        crmObjectData.put("remark", "");
        List<String> owner = Lists.newArrayList(String.valueOf(-10000));
        crmObjectData.put("owner", owner);
        crmObjectData.put("created_by", owner);
       // crmObjectData.put("name", getName());
       // log.info("创建广告回传明细,ea:[{}], param:[{}]", ea, crmObjectData);
        Map<String, Object> result2 = crmMetadataManager.addMetadata("83668", -10000, CrmObjectApiNameEnum.AD_DATA_RETURN_DETAIL_OBJ.getName(), crmObjectData);
        log.info("创建广告回传明细 result:[{}]", result2);
    }


}
