/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.miniAppSetting.GetMiniAppAutoUpgradeStatusArg;
import com.facishare.marketing.api.arg.usermarketingaccount.MarketingUserGroupCustomizeObjectMappingArg;
import com.facishare.marketing.api.result.GetBoundMiniappInfoResult;
import com.facishare.marketing.api.result.ListParamsBySceneAndSpreadTypeResult;
import com.facishare.marketing.api.result.MarketingUserGroupCustomizeObjectMappingResult;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.AccessChannel;
import com.facishare.marketing.common.typehandlers.value.CustomizeObjectMappings;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.typehandlers.value.IntegerList;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.service.SettingServiceImpl;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.webpage.customer.api.model.result.GetTenantBrandColorResult;
import com.fxiaoke.wechatrestapi.arg.CommitCodeArg;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SettingServiceTest extends BaseTest {
    @Autowired
    private SettingService settingService;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired SettingServiceImpl service;
    Gson gson = new Gson();

    @Test
    public void listAllConfig() {
        for (int i = 0; i < 50; i++) {
            Result<?> result = settingService.listAllConfig("58433", 1000);
            System.out.println(GsonUtil.toJson(result));
        }
    }

    @Test
    public void addUserRoles(){
        Result result = settingService.addUserRoles("74164", ImmutableSet.of(1000, 1001), ImmutableSet.of("super-administrator"));
        System.out.println(result);
    }

    @Test
    public void listParamsBySceneAndSpreadType() {
        ListParamsBySceneAndSpreadTypeArg arg = new ListParamsBySceneAndSpreadTypeArg();
        arg.setChannel(1);
        arg.setSceneType("yxzs_union_notice_wx_template");
        arg.setSpreadType(0);
        Result<ListParamsBySceneAndSpreadTypeResult> listParamsBySceneAndSpreadTypeResultResult = settingService.listParamsBySceneAndSpreadType("88146", arg);
        System.out.println("&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&");
        System.out.println(JSON.toJSONString(listParamsBySceneAndSpreadTypeResultResult));
    }

    @Test
    public void listRole(){
        String ea = "ddqybhzyl";
        settingService.listRole(ea);
    }

    @Test
    public void setMarketingUserGroupCustomizeObjectMapping(){
        String ea = "74164";
        Integer fsUserId = 1071;
        MarketingUserGroupCustomizeObjectMappingArg arg = new MarketingUserGroupCustomizeObjectMappingArg();
        arg.setObjectApiName("object_grr4H__c");
        arg.setObjectName("测试智能表");
        CustomizeObjectMappings customizeObjectMappings = new CustomizeObjectMappings();
        CustomizeObjectMappings.CustomizeObjectMapping mp1 = new CustomizeObjectMappings.CustomizeObjectMapping("name", "姓名", "name", "姓名");
        CustomizeObjectMappings.CustomizeObjectMapping mp2 = new CustomizeObjectMappings.CustomizeObjectMapping("phone","手机号", "mobile", "手机");
        CustomizeObjectMappings.CustomizeObjectMapping mp3 = new CustomizeObjectMappings.CustomizeObjectMapping("email","邮箱", "email", "邮件");

        customizeObjectMappings.add(mp1);
        customizeObjectMappings.add(mp2);
        customizeObjectMappings.add(mp3);
        arg.setCustomizeObjectMappings(customizeObjectMappings);
        settingService.setMarketingUserGroupCustomizeObjectMapping(ea, fsUserId, arg);
    }

    @Test
    public void getMarketingUserGroupCustomizeObjectMapping(){
        String ea = "74164";
        Result<List<MarketingUserGroupCustomizeObjectMappingResult>> list = settingService.getMarketingUserGroupCustomizeObjectMapping(ea);
        log.info("getMarketingUserGroupCustomizeObjectMapping list:{}", list);
    }

    @Test
    public void getBoundMiniappInfo(){
        Result<GetBoundMiniappInfoResult> result = settingService.getBoundMiniappInfo("83820", "FWT");
        log.info("getBoundMiniappInfo result:{}", result);
    }

    @Test
    public void requiredPrivateInfos(){
        CommitCodeArg commitCodeArg = new CommitCodeArg();
        commitCodeArg.setTemplateId("47");
        commitCodeArg.setUserVersion("8.0.28");
        commitCodeArg.setUserDescription("810，fix版本，购物车服务化，购物车新建订单后跳转到订单详情，库存精度问题，修复返利不能点击的问题，托管小程序功能标准化");
        Map<String, Object> extTopMap = new HashMap<>(1);
        extTopMap.put("extEnable", true);
        extTopMap.put("extAppid", "wx21a0bb68032a04b3");
        extTopMap.put("ext", new SettingServiceImpl.ExtAppInfo("wx21a0bb68032a04b3", "中饮食品订货端", "http://wx.qlogo.cn/mmopen/qt9dHySqMkyrfibl7tCZ4iboH9FKOM40hYXdFTXia1ksoBjk4icq3TibEgrDRxqjuKvQQzr5PkjLfEXaHEypxqcmwvlQ6ic7xE4GJ3/0", "https://www.fxiaoke.com", ""));
        extTopMap.put("requiredPrivateInfos", Lists.newArrayList("getLocation"));
        commitCodeArg.setExtJson(GsonUtil.toJson(extTopMap));
        String argGson = GsonUtil.toJson(commitCodeArg);
        log.info("argGson:{}", argGson);

        //curl: curl https://api.weixin.qq.com/wxa/commit?access_token=59_blJkAiYU8N8ueEKMAyYd1itHQ6KxEyd--ZCtGwwVAXy5CsX-Wxs4yMtJKBgdltLisgXvrqtB_JT0zq9oZlq_SPEsfHGnK-zwIqP21gZ5aHiPq1e3tevKCBtdfhmq-fOt26TmOcCFbgD1G3auEEFgADDTTU -d'{"template_id":"47","ext_json":"{\"ext\":{\"appId\":\"wx21a0bb68032a04b3\",\"appName\":\"中饮食品订货端\",\"appHeadImage\":\"http://wx.qlogo.cn/mmopen/qt9dHySqMkyrfibl7tCZ4iboH9FKOM40hYXdFTXia1ksoBjk4icq3TibEgrDRxqjuKvQQzr5PkjLfEXaHEypxqcmwvlQ6ic7xE4GJ3/0\",\"host\":\"https://www.fxiaoke.com\"},\"extEnable\":true,\"extAppid\":\"wx21a0bb68032a04b3\",\"requiredPrivateInfos\":[\"getLocation\"]}","user_version":"8.0.28","user_desc":"810，fix版本，购物车服务化，购物车新建订单后跳转到订单详情，库存精度问题，修复返利不能点击的问题，托管小程序功能标准化"}'

    }

    @Test
    public void getApiNameList(){
        Result<List<String>> apiNameList = settingService.getApiNameList("74164");
        Result<List<String>> apiNameList1 = settingService.getApiNameList("83668");
        Result<List<String>> apiNameList2 = settingService.getApiNameList("83668");
        Result<List<String>> apiNameList3 = settingService.getApiNameList("82559");
    }

    @Test
    public void queryTenantBrandColor(){
        TenantBrandColorArg arg = new TenantBrandColorArg();
        arg.setFsEa("88146");
        Result<String> result = settingService.queryTenantBrandColor(arg);
        arg.setObjectId("013e60dca77842779a428f96f946d325");
        arg.setObjectType(26);
        result = settingService.queryTenantBrandColor(arg);
        arg.setEa("74164");
        result = settingService.queryTenantBrandColor(arg);
        System.out.println(result);
    }

    @Test
    public void spreardReport(){

        service.spreardReport("88146");
        System.out.println(11);
    }

    @Test
    public void queryI18n(){
        QueryI18nArg arg = new QueryI18nArg();
        arg.setEa("88146");
        arg.setLang("en");
        arg.setNeedCustom(true);
        arg.setVersion(1701705879000L);
        arg.setTags(Lists.newArrayList("web","pre_object","bi_view"));
        Result<LinkedTreeMap<String, Object>> linkedTreeMapResult = service.queryI18n(arg);
        System.out.println(linkedTreeMapResult);
    }

    @Test
    public void addUserAccessible(){

        AddUserAccessibleArg arg = new AddUserAccessibleArg();
        arg.setEa("88146");
        arg.setUserId(1000);
        arg.setEmployeeIds(Lists.newArrayList(1000, 1001));
        arg.setAccessScope(1);
        IntegerList integers = new IntegerList();
        integers.add(1000);
        integers.add(1001);
        IntegerList integers1 = new IntegerList();
        integers1.add(100000);
        integers1.add(100006);
        arg.setAccessibleFsDepartments(integers);
        arg.setAccessibleQywxDepartments(integers1);
        AccessChannel channel = new AccessChannel();
        channel.setOfficialAccount(Lists.newArrayList(new AccessChannel.ChannelInfo("gongzonghao1",2),new AccessChannel.ChannelInfo("gongzonghao1",2)));
        channel.setAdvertisement(Maps.asMap(Sets.newHashSet("baidu","guge","tengxun"),
                key->Lists.newArrayList(new AccessChannel.ChannelInfo(key+"3",1),new AccessChannel.ChannelInfo(key+"4",2))));
        channel.setOfficialWebsite(Lists.newArrayList(new AccessChannel.ChannelInfo("guanwang1",1),new AccessChannel.ChannelInfo("guanwang2",2)));
        arg.setAccessChannel(channel);
        Result result = service.addUserAccessible(arg);
        System.out.println(result);
    }

    @Test
    public void listUserRoles(){

        Result result = service.listUserRoles("88146");
        System.out.println(result);
    }
    @Test
    public void queryUserAccessible(){
        UserAccessibleArg arg = new UserAccessibleArg();
        arg.setEa("88146");
        arg.setUserId(1000);
        Result result = service.queryUserAccessible(arg);
        System.out.println(result);
    }

    @Test
    public void getMiniAppAutoUpgradeStatus(){
        GetMiniAppAutoUpgradeStatusArg arg = new GetMiniAppAutoUpgradeStatusArg();
        arg.setWxAppId("wxbfa2c4d41b51f8da");
        arg.setPlatformId("YXT");
        settingService.getMiniAppAutoUpgradeStatus("88146", 1000, arg);
    }

}
