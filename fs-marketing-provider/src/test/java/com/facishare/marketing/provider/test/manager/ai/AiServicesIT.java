/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager.ai;

import com.facishare.marketing.provider.sharegpt.chain.ConversationalWithToolsChain;
import com.facishare.marketing.provider.sharegpt.model.chat.OpenAiChatModel;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.service.AiServices;
import org.junit.Test;

public class AiServicesIT extends BaseTest {

    OpenAiChatModel chatLanguageModel = OpenAiChatModel.builder().tenantId("88146").build();

    ChatMemory chatMemory = MessageWindowChatMemory.withMaxMessages(6);

    interface ChatWithMemory {
        String chat(String userMessage);
    }

    static class Calculator {
        @Tool("计算提供的数字的平方根")
        double calculator1(@P("要操作的数字") double number) {
            return Math.sqrt(number);
        }
    }

    static class Calculator2 {
        @Tool("计算提供的数字的1/3")
        double calculator2(@P("要操作的数字") double number) {
            return number / 3;
        }
    }

    @Test
    public void should_fail_when_user_message_is_null_or_blank() {
        Calculator calculator = new Calculator();
        Calculator2 calculator2 = new Calculator2();
        ConversationalWithToolsChain chain = ConversationalWithToolsChain.builder()
                .chatLanguageModel(chatLanguageModel)
                .chatMemory(chatMemory)
                .tools(Lists.newArrayList(calculator, calculator2))
                .build();
//        String result = chain.execute("科学记数法中16的平方根的1/3是多少?");
//        System.out.println("result->" + result);
    }
}
