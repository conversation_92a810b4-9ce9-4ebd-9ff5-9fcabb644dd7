/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.DeleteMaterialArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.fileLibrary.*;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.fileLibrary.ListFileByGroupResult;
import com.facishare.marketing.api.result.fileLibrary.ListFileGroupResult;
import com.facishare.marketing.api.service.fileLibrary.FileLibraryService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FileLibraryServiceImplTest extends BaseTest {
    @Autowired
    private FileLibraryService fileLibraryService;

    @Test
    public void uploadFile(){
        String ea = "74164";
        Integer fsUserId = 1071;
        UploadFileArg arg = new UploadFileArg();
        List<UploadFileArg.FileItem> files = Lists.newArrayList();
        arg.setFiles(files);
        UploadFileArg.FileItem fileItem1 = new UploadFileArg.FileItem();
        UploadFileArg.FileItem fileItem2 = new UploadFileArg.FileItem();
        files.add(fileItem1);
        files.add(fileItem2);
        fileItem1.setFileName("FILE1");
        fileItem1.setFilePath("TA_152069b079a447749ab5372aa0792424");
        fileItem1.setFileSize(218359l);
        fileItem1.setExt("pptx");

        fileItem2.setFileName("FILE2");
        fileItem2.setFilePath("TA_7b59db6070254c099c75847a0b1bdc96");
        fileItem2.setFileSize(175957L);
        fileItem2.setExt("pptx");
        fileLibraryService.uploadFile(ea, fsUserId, arg);
    }

    @Test
    public void editFileGroup(){
        String ea = "74164";
        Integer fsUserId = 1023;
        EditFileGroupArg arg = new EditFileGroupArg();
        arg.setName("第二个文件分组");
        Result<EditObjectGroupResult> result = fileLibraryService.editFileGroup(ea, fsUserId, arg);
        log.info("editFileGroup result:{}",result);
    }

    @Test
    public void editFileGroup2(){
        String ea = "74164";
        Integer fsUserId = 1023;
        EditFileGroupArg arg = new EditFileGroupArg();
        arg.setId("5c96a76b48dc4c3c9f6fa4e64b9f4696");
        arg.setName("第一个文件分组");
        Result<EditObjectGroupResult> result = fileLibraryService.editFileGroup(ea, fsUserId, arg);
        log.info("editFileGroup result:{}",result);
    }

    @Test
    public void listFileGroup(){
        String ea = "74164";
        Integer fsUserId = 1177;
        ListFileGroupArg arg = new ListFileGroupArg();
        arg.setUseType(0);
        Result<ObjectGroupListResult> result = fileLibraryService.listFileGroup(ea, fsUserId, arg);
        log.info("listFileGroup0,size: {} result:{}",result.getData().getObjectGroupList().size(), result);

        arg.setUseType(1);
        result = fileLibraryService.listFileGroup(ea, fsUserId, arg);
        log.info("listFileGroup1,size: {} result:{}",result.getData().getObjectGroupList().size(), result);

        arg.setUseType(2);
        result = fileLibraryService.listFileGroup(ea, fsUserId, arg);
        log.info("listFileGroup2,size: {} result:{}",result.getData().getObjectGroupList().size(), result);
    }

    @Test
    public void deleteGroup(){
        String ea = "74164";
        Integer fsUserId = 1071;
        DeleteFileGroupArg arg = new DeleteFileGroupArg();
        arg.setId("3de999f0822542a680e4a5e3934ab899");
        Result result = fileLibraryService.deleteFileGroup(ea, fsUserId, arg);
    }

    @Test
    public void listFileByGroup(){
        String ea = "88146";
        Integer fsUserId = 1000;
        ListFileByGroupArg arg = new ListFileByGroupArg();
        arg.setGroupId("-1");
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setMenuId("7ba54f86ac1e4df39bb7994c08ef346c");
        MaterialTagFilterArg materialTagFilterArg = new MaterialTagFilterArg();
        materialTagFilterArg.setType(1);
        materialTagFilterArg.setMaterialTagIds(Lists.newArrayList("7f2ce321525a43d88dfb5ceafb71a401"));
        arg.setMaterialTagFilter(materialTagFilterArg);
        Result<PageResult<ListFileByGroupResult>> result = fileLibraryService.listFileByGroup(ea, fsUserId, arg);
        log.info("listHexagonTemplateByGroup result1:{}", JsonUtil.toJson(result));

    }

    @Test
    public void setFileGroup(){
        String ea = "74164";
        Integer fsUserId = 1071;
        SetFileGroupArg arg = new SetFileGroupArg();
        arg.setFileId("d8c39f1d48a94f5eb0bed756249de776");
        arg.setGroupId("3a6f309887174de894269c9b68d188d3");
        fileLibraryService.setFileGroup(ea, fsUserId, arg);
    }

    @Test
    public void deleteFile(){
        String ea = "74164";
        Integer fsUserId = 1071;
        DeleteFileArg arg = new DeleteFileArg();
        arg.setId("3de999f0822542a680e4a5e3934ab899");
        fileLibraryService.deleteFile(ea, fsUserId, arg);
    }

    @Test
    public void copyFromNetShareDisk(){
        String ea = "74164";
        Integer fsUserId = 1089;
        CopyFromNetShareDiskArg arg = new CopyFromNetShareDiskArg();
        List<CopyFromNetShareDiskArg.FileItem> files = Lists.newArrayList();
        arg.setFiles(files);

        CopyFromNetShareDiskArg.FileItem fileItem1 = new CopyFromNetShareDiskArg.FileItem();
        CopyFromNetShareDiskArg.FileItem fileItem2 = new CopyFromNetShareDiskArg.FileItem();
        files.add(fileItem1);
        files.add(fileItem2);

        fileItem1.setFileName("新人指引");
        fileItem1.setPath("N_202104_28_847e590c2981404caaeb4aed3d87c8b6.doc");
        fileItem1.setFileSize(464384L);
        fileItem1.setExt("doc");

        fileItem2.setFileName("test");
        fileItem2.setPath("N_202104_28_66fa8ffad062466a9e3f574835027137.xls");
        fileItem2.setFileSize(35840L);
        fileItem2.setExt("xls");

        fileLibraryService.copyFromNetShareDisk(ea, fsUserId, arg);
    }

    @Test
    public void renameFile(){
        String ea = "74164";
        Integer fsUserId = 1071;
        RenameFileArg arg = new RenameFileArg();
        arg.setFileName("新名称");
        arg.setId("d24f6a4db92341a19d93b0f5e1e28b9d");
        Result result = fileLibraryService.renameFile(ea, fsUserId, arg);
    }

    @Test
    public void getPreviewUrl(){
        String ea = "74164";
        Integer fsUserId = 1071;
        GetPreviewUrlArg arg = new GetPreviewUrlArg();
        arg.setPath("N_202104_23_fdb24a3244f546e1a62da300161d98ca.pdf");
        Result<String> result = fileLibraryService.getPreviewUrl(ea, fsUserId, arg);
        log.info("getPreviewUrl result:{}", result);
    }

    @Test
    public void setFileTag(){
        String ea = "74164";
        Integer fsUserId = 1071;
        SetFileTagArg arg = new SetFileTagArg();
        arg.setFileId("6757b3b9b42b4d3aabcce6768f8fe6dc");
        TagNameList tagNameList = new TagNameList();
        TagName tagName1 = new TagName();
        TagName tagName2 = new TagName();
        tagName1.setFirstTagName("爱学习的人");
        tagName2.setFirstTagName("三好学生");
        tagNameList.add(tagName1);
        tagNameList.add(tagName2);
        arg.setTagNames(tagNameList);
        fileLibraryService.setFileTag(ea, fsUserId, arg);
    }

    @Test
    public void setFileGroupBatch() {
        String ea = "74164";
        int userId = 1177;

        SetObjectGroupArg setObjectGroupArg = new SetObjectGroupArg();
        setObjectGroupArg.setGroupId("672a34e7046449d8856ab51f95a32caf");
        List<String> objectIdList = new ArrayList<>();
        objectIdList.add("ecd1e04d44c247fba7e611fc7b738ea0");
        objectIdList.add("50323f19182345ab876d68ff2e63bb48");
        setObjectGroupArg.setObjectIdList(objectIdList);
        Result<Void> result1 = fileLibraryService.setFileGroupBatch(ea, userId, setObjectGroupArg);
        log.info("setFileGroupBatch result: {}", result1);
    }

    @Test
    public void deleteFileBatch() {
        String ea = "74164";
        int userId = 1177;

        DeleteMaterialArg arg = new DeleteMaterialArg();
        List<String> objectIdList = new ArrayList<>();
        objectIdList.add("f33072a5dae945748d53af07ec3aad2e");
        objectIdList.add("4dd1b6a53d7f40dc822303960c876cc9");
        arg.setIdList(objectIdList);
        Result<Void> result1 = fileLibraryService.deleteFileBatch(ea, userId, arg);
        log.info("deleteFileBatch result: {}", result1);
    }

    @Test
    public void topFile() {
        String ea = "74164";
        int userId = 1177;
        TopMaterialArg arg = new TopMaterialArg();
        arg.setObjectId("dbdb3b19dead4a0aa8718af4fa30b682");
        Result<Void> result = fileLibraryService.topFile(ea, userId, arg);
        log.info("topFile result : {}", result);
    }
}
