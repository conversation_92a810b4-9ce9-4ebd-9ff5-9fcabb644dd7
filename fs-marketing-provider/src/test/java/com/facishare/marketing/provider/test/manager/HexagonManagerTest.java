/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity;
import com.facishare.marketing.provider.manager.HexagonManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by zhengh on 2019/12/12.
 */
@Slf4j
public class HexagonManagerTest extends BaseTest{
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private HexagonSiteManager hexagonSiteManager;

    @Test
    public void getHexagonBaseInfoById(){
        String ea = "74164";
        List<String> ids = Lists.newArrayList();
        ids.add("f1a7e4e2c7b24d43ae79f0f961f7e134");
        Map<String, HexagonBaseInfoDTO>  map = hexagonManager.getHexagonBaseInfoById(ids, ea);
        Assert.assertNotEquals(0,map.size());
    }

    @Test
    public void updateCopiedHexagon(){
        String siteId = "cee2b6e15a2c4bd5b4d3317407b83882";
        String title = "直播测试";
        String coverPath =  "A_202004_08_276753114f2b4fb1ad565009e28cac67.jpg";
        String newContent = "h'a'hacc\"czb\"c";
        Date startTime = new Date();

       // desc = desc.replace("\"", "\\\"");
       // desc = desc.replaceAll("\"", '\\"');
        String regex = "\"";
        newContent = newContent.replaceAll("\"", "\\\\\"");
        System.out.println("newContent:"+newContent);

    }

    @Test
    public void copyPageFromTemplate() {
        String templatePageJson = "{\"id\":\"eeac780237bf4165941d7e49a81339b2\",\"ea\":\"74164\",\"name\":\"报名表单页面\",\"hexagonTemplateSiteId\":\"60729a8fa8f64d8a8e77eabcc3210e09\",\"formId\":\"49e00edbad6945f6a050135284bc7b33\",\"content\":\"{\\\"id\\\":\\\"eeac780237bf4165941d7e49a81339b2\\\",\\\"type\\\":\\\"page\\\",\\\"name\\\":\\\"报名表单页面\\\",\\\"title\\\":\\\"\\\",\\\"version\\\":\\\"4.3.0-6\\\",\\\"cover\\\":\\\"\\\",\\\"shareOpts\\\":{\\\"title\\\":\\\"\\\",\\\"desc\\\":\\\"\\\",\\\"link\\\":\\\"\\\",\\\"imgUrl\\\":\\\"\\\"},\\\"style\\\":{\\\"width\\\":375,\\\"backgroundColor\\\":\\\"#fff\\\",\\\"backgroundSize\\\":\\\"100%\\\",\\\"backgroundRepeat\\\":\\\"no-repeat\\\",\\\"backgroundImage\\\":\\\"\\\"},\\\"backgroundFillType\\\":\\\"filling\\\",\\\"dataSourceAction\\\":{},\\\"components\\\":[{\\\"id\\\":1627013018733,\\\"name\\\":\\\"表单\\\",\\\"key\\\":\\\"form-container\\\",\\\"type\\\":\\\"container\\\",\\\"typeValue\\\":\\\"form\\\",\\\"components\\\":[{\\\"0\\\":\\\"h\\\",\\\"1\\\":\\\"t\\\",\\\"2\\\":\\\"t\\\",\\\"3\\\":\\\"p\\\",\\\"4\\\":\\\"s\\\",\\\"5\\\":\\\":\\\",\\\"6\\\":\\\"/\\\",\\\"7\\\":\\\"/\\\",\\\"8\\\":\\\"w\\\",\\\"9\\\":\\\"w\\\",\\\"10\\\":\\\"w\\\",\\\"11\\\":\\\".\\\",\\\"12\\\":\\\"c\\\",\\\"13\\\":\\\"e\\\",\\\"14\\\":\\\"s\\\",\\\"15\\\":\\\"h\\\",\\\"16\\\":\\\"i\\\",\\\"17\\\":\\\"1\\\",\\\"18\\\":\\\"1\\\",\\\"19\\\":\\\"2\\\",\\\"20\\\":\\\".\\\",\\\"21\\\":\\\"c\\\",\\\"22\\\":\\\"o\\\",\\\"23\\\":\\\"m\\\",\\\"24\\\":\\\"/\\\",\\\"25\\\":\\\"a\\\",\\\"26\\\":\\\"p\\\",\\\"27\\\":\\\"p\\\",\\\"28\\\":\\\"m\\\",\\\"29\\\":\\\"a\\\",\\\"30\\\":\\\"r\\\",\\\"31\\\":\\\"k\\\",\\\"32\\\":\\\"e\\\",\\\"33\\\":\\\"t\\\",\\\"34\\\":\\\"i\\\",\\\"35\\\":\\\"n\\\",\\\"36\\\":\\\"g\\\",\\\"37\\\":\\\"/\\\",\\\"38\\\":\\\"w\\\",\\\"39\\\":\\\"e\\\",\\\"40\\\":\\\"b\\\",\\\"41\\\":\\\"/\\\",\\\"42\\\":\\\"f\\\",\\\"43\\\":\\\"i\\\",\\\"44\\\":\\\"l\\\",\\\"45\\\":\\\"e\\\",\\\"46\\\":\\\"/\\\",\\\"47\\\":\\\"g\\\",\\\"48\\\":\\\"e\\\",\\\"49\\\":\\\"t\\\",\\\"50\\\":\\\"F\\\",\\\"51\\\":\\\"i\\\",\\\"52\\\":\\\"l\\\",\\\"53\\\":\\\"e\\\",\\\"54\\\":\\\"B\\\",\\\"55\\\":\\\"y\\\",\\\"56\\\":\\\"S\\\",\\\"57\\\":\\\"p\\\",\\\"58\\\":\\\"l\\\",\\\"59\\\":\\\"i\\\",\\\"60\\\":\\\"c\\\",\\\"61\\\":\\\"e\\\",\\\"62\\\":\\\"U\\\",\\\"63\\\":\\\"r\\\",\\\"64\\\":\\\"l\\\",\\\"65\\\":\\\"?\\\",\\\"66\\\":\\\"p\\\",\\\"67\\\":\\\"a\\\",\\\"68\\\":\\\"t\\\",\\\"69\\\":\\\"h\\\",\\\"70\\\":\\\"=\\\",\\\"71\\\":\\\"A\\\",\\\"72\\\":\\\"_\\\",\\\"73\\\":\\\"2\\\",\\\"74\\\":\\\"0\\\",\\\"75\\\":\\\"2\\\",\\\"76\\\":\\\"1\\\",\\\"77\\\":\\\"0\\\",\\\"78\\\":\\\"6\\\",\\\"79\\\":\\\"_\\\",\\\"80\\\":\\\"1\\\",\\\"81\\\":\\\"8\\\",\\\"82\\\":\\\"_\\\",\\\"83\\\":\\\"9\\\",\\\"84\\\":\\\"3\\\",\\\"85\\\":\\\"b\\\",\\\"86\\\":\\\"5\\\",\\\"87\\\":\\\"e\\\",\\\"88\\\":\\\"3\\\",\\\"89\\\":\\\"7\\\",\\\"90\\\":\\\"9\\\",\\\"91\\\":\\\"0\\\",\\\"92\\\":\\\"b\\\",\\\"93\\\":\\\"d\\\",\\\"94\\\":\\\"b\\\",\\\"95\\\":\\\"4\\\",\\\"96\\\":\\\"b\\\",\\\"97\\\":\\\"4\\\",\\\"98\\\":\\\"9\\\",\\\"99\\\":\\\"b\\\",\\\"100\\\":\\\"c\\\",\\\"101\\\":\\\"4\\\",\\\"102\\\":\\\"7\\\",\\\"103\\\":\\\"d\\\",\\\"104\\\":\\\"a\\\",\\\"105\\\":\\\"c\\\",\\\"106\\\":\\\"5\\\",\\\"107\\\":\\\"e\\\",\\\"108\\\":\\\"0\\\",\\\"109\\\":\\\"9\\\",\\\"110\\\":\\\"1\\\",\\\"111\\\":\\\"c\\\",\\\"112\\\":\\\"f\\\",\\\"113\\\":\\\"5\\\",\\\"114\\\":\\\"d\\\",\\\"115\\\":\\\".\\\",\\\"116\\\":\\\"p\\\",\\\"117\\\":\\\"n\\\",\\\"118\\\":\\\"g\\\",\\\"119\\\":\\\"&\\\",\\\"120\\\":\\\"e\\\",\\\"121\\\":\\\"a\\\",\\\"122\\\":\\\"=\\\",\\\"123\\\":\\\"7\\\",\\\"124\\\":\\\"4\\\",\\\"125\\\":\\\"1\\\",\\\"126\\\":\\\"6\\\",\\\"127\\\":\\\"4\\\",\\\"id\\\":\\\"1627913405758\\\",\\\"name\\\":\\\"表单\\\",\\\"key\\\":\\\"form-container\\\",\\\"type\\\":\\\"container\\\",\\\"typeValue\\\":\\\"form\\\",\\\"components\\\":[{\\\"id\\\":\\\"1627013003926\\\",\\\"label\\\":\\\"提交\\\",\\\"name\\\":\\\"提交\\\",\\\"tip\\\":\\\"提交成功\\\",\\\"type\\\":\\\"button\\\",\\\"position\\\":\\\"none\\\",\\\"required\\\":false,\\\"isFormComp\\\":true,\\\"wrapStyle\\\":{\\\"position\\\":\\\"none\\\",\\\"background\\\":\\\"rgba(255,255,255,.9)\\\"},\\\"style\\\":{\\\"height\\\":45,\\\"width\\\":345,\\\"fontSize\\\":16,\\\"background\\\":\\\"rgba(28, 108, 247, 1)\\\",\\\"borderRadius\\\":0,\\\"color\\\":\\\"#fff\\\",\\\"letterSpacing\\\":0,\\\"lineHeight\\\":45,\\\"textAlign\\\":\\\"center\\\",\\\"margin\\\":\\\"0 auto\\\",\\\"borderWidth\\\":0,\\\"borderStyle\\\":\\\"none\\\",\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"position\\\":\\\"absolute\\\",\\\"left\\\":15,\\\"top\\\":536,\\\"opacity\\\":100},\\\"sort\\\":0,\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"},\\\"action\\\":{\\\"type\\\":\\\"inside\\\",\\\"id\\\":\\\"878c6b6cadd541fbb1d52f37948728e7\\\",\\\"url\\\":\\\"\\\",\\\"query\\\":\\\"\\\",\\\"label\\\":\\\"跳转内部页面\\\",\\\"miniprogram\\\":{\\\"wechat\\\":{\\\"appId\\\":\\\"\\\",\\\"originalId\\\":\\\"\\\",\\\"path\\\":\\\"\\\"},\\\"baidu\\\":{\\\"appId\\\":\\\"\\\",\\\"path\\\":\\\"\\\"}},\\\"content\\\":{\\\"id\\\":\\\"e31088d89a1249688e83a8321485a4e4\\\",\\\"title\\\":\\\"我要拼出世界冠军\\\",\\\"contentType\\\":1,\\\"objectType\\\":6},\\\"phone\\\":\\\"\\\",\\\"chatTargetUid\\\":\\\"\\\",\\\"extendParams\\\":{},\\\"fileOriginData\\\":{\\\"FileID\\\":\\\"d24f6a4db92341a19d93b0f5e1e28b9d\\\",\\\"FileName\\\":\\\"新名称\\\",\\\"FileSize\\\":4125,\\\"FileExtension\\\":\\\"xlsx\\\",\\\"FilePath\\\":\\\"N_202104_22_97912cc30d89416fa6417fadfad47418.xlsx\\\"},\\\"linkParams\\\":{\\\"marketingEventId\\\":\\\"\\\"}}},{\\\"id\\\":1627013034467,\\\"label\\\":\\\"姓名\\\",\\\"name\\\":\\\"姓名\\\",\\\"title\\\":\\\"\\\",\\\"type\\\":\\\"input\\\",\\\"typeValue\\\":\\\"text\\\",\\\"fieldName\\\":\\\"name\\\",\\\"defaultValueOpen\\\":false,\\\"defaultValue\\\":\\\"\\\",\\\"globalCacheField\\\":\\\"\\\",\\\"defaultValueType\\\":\\\"manual\\\",\\\"required\\\":true,\\\"placeholder\\\":\\\"请输入姓名\\\",\\\"isFormComp\\\":true,\\\"style\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"width\\\":345,\\\"fontSize\\\":14,\\\"paddingBottom\\\":0,\\\"paddingTop\\\":0,\\\"paddingLeft\\\":12,\\\"paddingRight\\\":12,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":1,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"left\\\":15,\\\"top\\\":113,\\\"position\\\":\\\"absolute\\\"},\\\"titleStyle\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"fontSize\\\":14,\\\"lineHeight\\\":16,\\\"paddingBottom\\\":6,\\\"paddingTop\\\":6,\\\"whiteSpace\\\":\\\"normal\\\"},\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"height\\\":45,\\\"color\\\":\\\"#181c25\\\",\\\"background\\\":\\\"rgba(235, 177, 168, 1)\\\"},\\\"sort\\\":1},{\\\"id\\\":1627013038135,\\\"label\\\":\\\"手机号\\\",\\\"name\\\":\\\"手机号\\\",\\\"title\\\":\\\"\\\",\\\"type\\\":\\\"input\\\",\\\"typeValue\\\":\\\"number\\\",\\\"fieldName\\\":\\\"phone\\\",\\\"pattern\\\":\\\"^1[0-9]\\\\\\\\d{9}$\\\",\\\"defaultValue\\\":\\\"\\\",\\\"defaultValueOpen\\\":false,\\\"globalCacheField\\\":\\\"\\\",\\\"defaultValueType\\\":\\\"manual\\\",\\\"required\\\":true,\\\"verify\\\":false,\\\"weChatAuthorizationButton\\\":false,\\\"placeholder\\\":\\\"请输入手机号\\\",\\\"isFormComp\\\":true,\\\"weChatAuthorizationButtonStyle\\\":{\\\"color\\\":\\\"#fff\\\",\\\"background\\\":\\\"#09BB07\\\",\\\"fontSize\\\":14,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":0,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\"},\\\"verifyButtonStyle\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"background\\\":\\\"#ffffff\\\",\\\"fontSize\\\":14,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":1,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\"},\\\"titleStyle\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"fontSize\\\":14,\\\"lineHeight\\\":16,\\\"paddingBottom\\\":6,\\\"paddingTop\\\":6,\\\"whiteSpace\\\":\\\"normal\\\"},\\\"style\\\":{\\\"color\\\":\\\"#181C25\\\",\\\"width\\\":345,\\\"fontSize\\\":14,\\\"paddingBottom\\\":0,\\\"paddingTop\\\":0,\\\"paddingLeft\\\":12,\\\"paddingRight\\\":12,\\\"borderStyle\\\":\\\"solid\\\",\\\"borderWidth\\\":1,\\\"borderRadius\\\":3,\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"left\\\":15,\\\"top\\\":166,\\\"position\\\":\\\"absolute\\\"},\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"height\\\":45,\\\"color\\\":\\\"#181c25\\\",\\\"background\\\":\\\"rgba(234, 178, 169, 1)\\\"},\\\"sort\\\":2},{\\\"id\\\":\\\"1627023467711\\\",\\\"name\\\":\\\"文本\\\",\\\"type\\\":\\\"text\\\",\\\"value\\\":\\\"<p style=\\\\\\\"text-align: center;\\\\\\\"><span style=\\\\\\\"font-size: 14px; color: rgb(145, 149, 158); font-family: Helvetica, Arial, sans-serif; line-height: 24px; letter-spacing: 0px;\\\\\\\">提交以下信息马上报名参与直播</span></p>\\\",\\\"style\\\":{\\\"paddingBottom\\\":6,\\\"paddingLeft\\\":0,\\\"paddingRight\\\":0,\\\"paddingTop\\\":6,\\\"background\\\":\\\"rgba(255, 255, 255, 0)\\\",\\\"fontSize\\\":14,\\\"borderWidth\\\":0,\\\"borderRadius\\\":0,\\\"borderStyle\\\":\\\"none\\\",\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"left\\\":15,\\\"top\\\":49,\\\"position\\\":\\\"absolute\\\",\\\"width\\\":345},\\\"sort\\\":5,\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"}},{\\\"name\\\":\\\"图片\\\",\\\"type\\\":\\\"image\\\",\\\"images\\\":[{\\\"url\\\":\\\"https://www.ceshi112.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202106_01_8c75243a772d446c87897377b62c95a4.png&ea=74164\\\",\\\"action\\\":{}}],\\\"imageGap\\\":4,\\\"style\\\":{\\\"display\\\":\\\"flex\\\",\\\"width\\\":375,\\\"height\\\":159,\\\"paddingBottom\\\":0,\\\"paddingLeft\\\":0,\\\"paddingRight\\\":0,\\\"paddingTop\\\":0,\\\"borderRadius\\\":0,\\\"background\\\":\\\"rgba(255, 255, 255, 0)\\\",\\\"backgroundRepeat\\\":\\\"no-repeat\\\",\\\"backgroundSize\\\":\\\"cover\\\",\\\"backgroundPosition\\\":\\\"center center\\\",\\\"borderWidth\\\":0,\\\"borderStyle\\\":\\\"none\\\",\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"left\\\":0,\\\"top\\\":229,\\\"position\\\":\\\"absolute\\\"},\\\"sort\\\":4,\\\"id\\\":\\\"1627913405894\\\",\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"}}],\\\"current\\\":0,\\\"slideIndex\\\":0,\\\"layout\\\":\\\"single\\\",\\\"fillType\\\":\\\"image\\\",\\\"fillMethod\\\":\\\"filling\\\",\\\"typesetting\\\":\\\"absolute\\\",\\\"style\\\":{\\\"width\\\":375,\\\"height\\\":600,\\\"overflow\\\":\\\"hidden\\\",\\\"position\\\":\\\"relative\\\",\\\"backgroundColor\\\":\\\"\\\",\\\"backgroundImage\\\":\\\"url(https://www.ceshi112.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202106_18_93b5e3790bdb4b49bc47dac5e091cf5d.png&ea=74164)\\\"}},{\\\"id\\\":\\\"1627913405758\\\",\\\"name\\\":\\\"表单\\\",\\\"key\\\":\\\"form-container\\\",\\\"type\\\":\\\"container\\\",\\\"typeValue\\\":\\\"form\\\",\\\"components\\\":[],\\\"current\\\":0,\\\"slideIndex\\\":0,\\\"layout\\\":\\\"single\\\",\\\"fillType\\\":\\\"color\\\",\\\"fillMethod\\\":\\\"filling\\\",\\\"typesetting\\\":\\\"absolute\\\",\\\"style\\\":{\\\"width\\\":375,\\\"height\\\":600,\\\"overflow\\\":\\\"hidden\\\",\\\"position\\\":\\\"relative\\\",\\\"backgroundColor\\\":\\\"\\\",\\\"backgroundImage\\\":\\\"\\\"}}],\\\"current\\\":0,\\\"slideIndex\\\":0,\\\"layout\\\":\\\"multiple\\\",\\\"fillType\\\":\\\"color\\\",\\\"fillMethod\\\":\\\"filling\\\",\\\"style\\\":{\\\"width\\\":375,\\\"overflow\\\":\\\"hidden\\\",\\\"backgroundColor\\\":\\\"\\\",\\\"backgroundImage\\\":\\\"url(https://www.ceshi112.com/appmarketing/web/file/getFileBySpliceUrl?path=A_202106_18_93b5e3790bdb4b49bc47dac5e091cf5d.png&ea=74164)\\\"},\\\"sort\\\":\\\"0\\\",\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"}}],\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"},\\\"isHomepage\\\":2}\",\"isHomepage\":1,\"status\":1,\"createBy\":-10000,\"createTime\":\"Jul 30, 2021 5:21:39 PM\",\"updateTime\":\"Aug 3, 2021 9:57:04 AM\"}";
        HexagonTemplatePageEntity templatePage = JSON.parseObject(templatePageJson, HexagonTemplatePageEntity.class);
        String newSiteId = "a2c1473d0d3a4291a69b3219e3f731d5";
        String formInfoJson = "{\"hexagonSiteId\":\"60729a8fa8f64d8a8e77eabcc3210e09\",\"formId\":\"49e00edbad6945f6a050135284bc7b33\",\"hexagonPageId\":\"eeac780237bf4165941d7e49a81339b2\"}";
        HexagonSiteListDTO formInfo = JSON.parseObject(formInfoJson, HexagonSiteListDTO.class);
        String newFormId = null;
        Map<String, String> buttonInsideAction = new HashMap<>();
        Map<String, Object> map = hexagonSiteManager.copyPageFromTemplate("74164", templatePage, newSiteId, formInfo, newFormId, buttonInsideAction);
        System.out.println("map = " + map);
        System.out.println("buttonInsideAction = " + buttonInsideAction);
    }

}
