/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.vo.function.UpdateQywxExternalUserRemarkVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.CrmWechatWorkExternalUserFieldEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.AppScopeEnum;
import com.facishare.marketing.common.enums.qywx.FsEnterpriseBindTypeEnum;
import com.facishare.marketing.common.enums.qywx.QywxBindStatusEnum;
import com.facishare.marketing.provider.entity.AccountEntity;
import com.facishare.marketing.provider.innerArg.UpdateWechatGroupObjOwnerMqArg;
import com.facishare.marketing.provider.innerArg.qywx.AddCorpTagArg;
import com.facishare.marketing.provider.innerResult.qywx.AddCorpTagResult;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult;
import com.facishare.marketing.provider.innerResult.qywx.ListActivatedAccountResult;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.qywx.ResetQywxAddressBookManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.test.BaseTest;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by ranluch on 2020/1/6.
 */
@Slf4j
public class QywxManagerTest extends BaseTest {
    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private ResetQywxAddressBookManager resetQywxAddressBookManager;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Test
    public void getAccessToken() {
    }

    @Test
    public void getCorpJsApiTicket() {
        String corpId = "ww6e8b982832137c03";
        String agentId = "1000003";
        String secret = "Brb_bcRt5DtCkaupM2OtyiRLRtqC99Sg6sSaPEsX7ks";
        String key = "55487554875548755487";
        String ticket = qywxManager.getCorpJsApiTicket(corpId, qywxManager.getAccessToken(corpId, agentId, secret), key);
        Assert.assertTrue(ticket != null);
    }

    @Test
    public void syncQywxBindInfo(){
        String ea = "88146";
        String corpId = "wpwx1mDAAAGKzPkC9dG-obZioNKAWXCg";
        String suitId ="dk0927f55c5f1950f9";
        String status = "0";
        qywxManager.syncQywxBindInfo(ea, corpId, suitId, status);
    }
    @Test
    public void getAgentJsApiTicket() {
        String corpId = "ww6e8b982832137c03";
        String agentId = "1000003";
        String secret = "Brb_bcRt5DtCkaupM2OtyiRLRtqC99Sg6sSaPEsX7ks";
        String key = "55487554875548755487";
        String ticket = qywxManager.getAgentJsApiTicket(corpId, agentId, qywxManager.getAccessToken(corpId, agentId, secret), key);
        Assert.assertTrue(ticket != null);
    }

    @Test
    public void getJsApiTicketSignature() {
        String corpId = "ww6e8b982832137c03";
        String agentId = "1000003";
        String secret = "Brb_bcRt5DtCkaupM2OtyiRLRtqC99Sg6sSaPEsX7ks";
        String key = "55487554875548755487";
        String ticket = qywxManager.getAgentJsApiTicket(corpId, agentId, qywxManager.getAccessToken(corpId, agentId, secret), key);
        String signature = qywxManager.getJsApiTicketSignature(ticket, "afasfagdga", System.currentTimeMillis()/ 1000, "http://fxiaoke.com/");
        Assert.assertTrue(signature != null);
    }

    @Test
    public void jsApiTicketSignature() {
        String signature = qywxManager.getJsApiTicketSignature("sM4AOVdWfPE4DxkXGEs8VMCPGGVi4C3VM0P37wVUCFvkVAy_90u5h9nbSlYy3-Sl-HhTdfl2fzFy1AOcHKP7qg", "Wm3WZYTPz0wzccnW", 1414587457L, "http://mp.weixin.qq.com?params=value");
        Assert.assertTrue(signature != null);
    }

    @Test
    public void queryAllStaffId() {
        List<String> strings = qywxManager.queryAllStaffId("74164");
    }

    @Test
    public void initQywxAddressBook() {
        resetQywxAddressBookManager.initQywxAddressBook("82450");
    }

    @Test
    public void getQywxUserInfoByPhoneTest() {
        DepartmentStaffResult.StaffInfo staffInfo = qywxUserManager.getQywxUserInfoByPhone("74164", "13163352321");
        System.out.println(staffInfo);
    }

    @Test
    public void fsEnterpriseBindType(){
        String ea = "hhaah";
        FsEnterpriseBindTypeEnum bindTypeEnum = qywxManager.fsEnterpriseBindType(ea);
        System.out.println("bindTypeEnum:" + bindTypeEnum);
    }

    @Test
    public void queryDepartment(){
        String accessToken = "R7knjViw5Op3dCoA95Lqq1GykJdi9u9nuYGqV86xeKsdayL-mNX8z5vfaeHqMquWJLCMplnAc8EFJGNa3ib-pwGA17-dmyJiwW8XXndyKqlrRG79Ey3Zy5UaC0EJXU1j8ZOc1Xrfx-KLl_wkxzDRlqnF7QThw2L9JrAowgzAMvR1cDX77LS4KbXPx8JZeF1u-OxMSEmtdWSDgCdhEoiuzA";
        DepartmentListResult rest = qywxManager.queryDepartment(accessToken);
    }

    @Test
    public void testMergeUser(){
        String ea = "74164";
        String phone = "***********";
        AccountEntity accountByPhone = qywxUserManager.getAccountByPhone(ea, phone);
        System.out.println("yes:" + accountByPhone);
    }

    @Test
    public void testListActivatedAccount() {
        ListActivatedAccountResult result = qywxManager.listActivatedAccount("wpwx1mDAAAlcwY0kl2kRP78SAEBQ0bRA", null);

        System.out.println("结果:" + JSON.toJSONString(result));
    }

    @Test
    public void updateWechatGroupObjOwnerTest() {
        UpdateWechatGroupObjOwnerMqArg arg = new UpdateWechatGroupObjOwnerMqArg();
        arg.setEa("83668");
        arg.setFsUserId(1016);
        arg.setQywxUserId("wowx1mDAAAqNNMQTuLDU0p9WOl8BCHtw");
        qywxUserManager.updateWechatGroupObjOwner(arg);
    }

    @Test
    public void getExternalUsrIdByUnionId(){
        String accessToken = "yS7vbNyPBJpmVHtZ-jb-daM9BwMdfrhw4vRovO79Xt7e0Mic7uSVJ7NDqb1U3cW4wnwfZZ9PIbVFTeL1pLV45u-EQFcj4pUgilBLbwVI1s5NWZDG75Lx9MsxhGp-87aqcPR3ZdZGE06v5XJqIxdn_gRRbn_f4_WVhoWMb2VrCqXMgLWd3mXwHyTkin70hSsFeqvHEVHt2ioalsk5yCOAOQ";
        String unionId = "oVUII6g7qfU02XLr5gvj4PgJPwv0";
        String openId = "oT-5T0XpVt7Lyxb6u9ziqWISX7t0";
        Optional<String> externalUserOpt = qywxManager.getExternalUsrIdByUnionId(accessToken, unionId, openId);
        System.out.println("yes:" + externalUserOpt.get());
    }

    @Test
    public void isOnSyncSCrmBlackList() {
        boolean a = qywxManager.isSyncCrmAppCallBackData("74164");
        boolean b = qywxManager.isSyncCrmAppCallBackData("82525");
        boolean c = qywxManager.isSyncCrmAppCallBackData("6666");
        boolean d = qywxManager.isSyncCrmAppCallBackData("741643");
        boolean e = qywxManager.isSyncCrmAppCallBackData("83668");
        boolean f = qywxManager.isSyncCrmAppCallBackData("78769");
        System.out.println(a);
    }

    @Test
    public void convertToOpenIdTest() {
        String ea = "mpgroup2020";
        String newCorpId = qywxManager.convertToNewCorpId(ea, "wxfe1b91dbc5e0ec5d");
        System.err.println("convertToOpenIdTest result newCorpId:" + newCorpId);

        Map<String, String> convertedToNewExternalUserIdMap = qywxManager.convertToNewExternalUserId(ea, Lists.newArrayList("wmymq0CAAA1u0J1_NAx4MXlSa1S72Mdw", "wmymq0CAAAxxUHquXx0L3uxOG_4-3GGw"));
        System.err.println("convertToOpenIdTest result convertedToNewExternalUserIdMap:" + convertedToNewExternalUserIdMap);

        Map<String, String> convertedToNewTagIdMap = qywxManager.convertToNewTagId(ea, Lists.newArrayList("etymq0CAAAORISXvBIprrfOG9VwEU9Aw", "etymq0CAAAXNcjCJcAv_l87PXZ1MLgVw"));
        System.err.println("convertToOpenIdTest result convertedToNewTagIdMap:" + convertedToNewTagIdMap);
    }

    @Test
    public void getQywxBindStatus() {
        QywxBindStatusEnum statusEnum = qywxManager.getQywxBindStatus("88146");
        log.info("结果:{}", statusEnum);
    }

    @Test
    public void addCorpTag() {
        AddCorpTagArg addCorpTagArg = new AddCorpTagArg();
        addCorpTagArg.setGroupName("测试测试");
        addCorpTagArg.setTags(Lists.newArrayList(new AddCorpTagArg.AddTagArg("dddd")));
        AddCorpTagResult addCorpTagResult = qywxManager.addCorpTag("88146", addCorpTagArg);
        System.out.println("r:"+addCorpTagResult);
    }

    @Test
    public void syncExternalUserRemark(){
        String ea = "92475";
        String userId = "wowx1mDAAAGWHX9sctqFei7wQ8hz3Y5g";
        String externalUserId = "wmwx1mDAAAS4_8nFhZJTM68f2McMzUqQ";
        String remark = "66来自包子的活码客户HelloWord";
        UpdateQywxExternalUserRemarkVO vo = new UpdateQywxExternalUserRemarkVO();
        vo.setUserId(userId);
        vo.setExternalUserId(externalUserId);
        vo.setRemark(remark);
        qywxManager.syncExternalUserRemark(ea, vo);

        //修改好友记录
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("external_user_id", OperatorConstants.EQ, Lists.newArrayList("68103a7dc50fa100010a92e4"));
        query.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, OperatorConstants.IN, Lists.newArrayList(AppScopeEnum.MARKETING.getValue()));
        query.addFilter("qywx_user_id", OperatorConstants.EQ, Lists.newArrayList(userId));
        List<String> selectFieldList = Lists.newArrayList("_id");
        List<ObjectData> wechatFriendRecordObjectList =  crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), selectFieldList, query);
        if(CollectionUtils.isEmpty(wechatFriendRecordObjectList)) {
            log.warn("找不到好友记录，请确认，ea: {} externalUserObjId: {}", ea, "68103a7dc50fa100010a92e4");
            return;
        }

        HeaderObj systemHeader = new HeaderObj(92475, -10000);
        ObjectData objectData = new ObjectData();
        objectData.put("enterprise_wechat_user_id", externalUserId);
        objectData.put("qywx_user_id", userId);
        objectData.put("_id", wechatFriendRecordObjectList.get(0).getId());
        objectData.put(CrmWechatWorkExternalUserFieldEnum.REMARK_NAME.getFieldName(), remark);
        ActionEditArg actionEditArg = new ActionEditArg();
        actionEditArg.setObjectData(objectData);
        objectData.setTenantId(92475);
        metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), true, true, actionEditArg);

        //修改企业微信好友
        objectData = new ObjectData();
        objectData.put("_id", "68103a7dc50fa100010a92e4");
        actionEditArg.setObjectData(objectData);
        objectData.setTenantId(92475);
        objectData.put(CrmWechatWorkExternalUserFieldEnum.REMARK_NAME.getFieldName(), remark);
        metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), true, true, actionEditArg);
    }
}
