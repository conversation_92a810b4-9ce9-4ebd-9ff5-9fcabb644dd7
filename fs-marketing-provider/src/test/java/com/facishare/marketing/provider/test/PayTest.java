/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.qywx.customizeFormData.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.qywx.customizeFormData.CustomizeFormDataShowSettingArg;
import com.facishare.marketing.api.service.qywx.CustomizeFormDataService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.advertiser.headlines.HeadlinesRequestResult;
import com.facishare.marketing.provider.advertiser.headlines.campaign.GetHeadlinesCampaignDataResponse;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.innerData.CreateFsPayOrderResult;
import com.facishare.marketing.provider.manager.advertiser.headlines.HeadlinesAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.CampaignApiManager;
import com.facishare.marketing.provider.manager.pay.FsPayOrderManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.pay.utils.SignUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class PayTest extends BaseTest {

    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("merchantCode", "**************");
        map.put("goodsId", String.valueOf(1016));
        map.put("toEA", String.valueOf(74164));
        map.put("merchantOrderNo", "1");
        map.put("amount", String.valueOf(1));
        String sign = SignUtils.sign(map);
        System.out.println(sign);
    }

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    @Autowired
    private CampaignApiManager campaignApiManager;

    @Autowired
    private HeadlinesAdMarketingManager headlinesAdMarketingManager;

    @Test
    public void campaignApiManager() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
//        adAccountEntity.setAccountId(1733857565308936L);
        HeadlinesRequestResult<GetHeadlinesCampaignDataResponse> result =  campaignApiManager.getHeadlinesCampaignData(adAccountEntity, "2022-09-13","2022-10-12",1,100, "7af9f952b941ca7d9136c757acd55a08a52a4675");
        System.out.println("campaignApiManager result" + result);
        //        headlinesAdMarketingManager.refreshHeadlinesCampaignData(adAccountEntity, "2022-09-13","2022-10-12");
    }


    @Test
    public void testUpdatePayOrder() {
        String str = "{\"appId\":\"wx3da71fd2988792c1\",\"corpId\":\"wwa546502834a79a0a\",\"formId\":\"c09b420b20f847a4add6ca63187dab11\",\"fsEa\":\"74164\",\"fsUserId\":1123,\"ipAddr\":\"0:0:0:0:0:0:0:1\",\"objectId\":\"6000874d5fe74fb0845892bd457d9a6e\",\"objectType\":27,\"openid\":\"oE3Rr5TFaIA0WBpu8O6uLE5D9CTo\",\"qrSource\":0,\"qyUserId\":\"***********\",\"sessionKey\":\"1Xa7dX5h0rH50nak0If/+w==\",\"submitContent\":{\"amount\":1,\"deviceInfo\":\"iOS 10.0.1\",\"goodsName\":\"充满爱的收割产品\",\"name\":\"Gavin\",\"newSave\":false,\"phone\":\"***********\",\"realIp\":\"0:0:0:0:0:0:0:1\"},\"token\":\"KWBynn0gFiu8hS5goUCd6W5LSwi9GzOJerTrfwA202By2BLGfzwNgHn8Tk6U3mBbEYnZoSlOl23idU4vhToxxBz4pzK7sRTj5Jq9_CmsZ73Ms1tvCG_0aRKTqIs7cTP25sqwDeNv1dXslbR90LXOC7RXaXBc5TsrZ_5MHWIz0_BKJrbzZta-4IsfrIckdu58chNZ2vrd0uv0J_EpITlCoGHdCltHTzNxeBlqCGcZ6vQUuap4NwVqgr0bJPY1eVFtiXWL-g4tA_1Y5PtZ8l5HqYNyWYzgBEVaTpJfl0e0PUHcVZw1TjIZuuBYi5cyVDNn1tc1LcRlghiZ87uR_q-PhQ\",\"uid\":\"625e036ef11a4f0f96d7e29413e65fc0\",\"userAgent\":\"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.05.2204250 MicroMessenger/8.0.5 Language/zh_CN webview/\",\"wrongParam\":false}";
        CustomizeFormDataEnrollArg arg = JSONObject.parseObject(str, CustomizeFormDataEnrollArg.class);
        customizeFormDataService.customizeFormDataEnroll(arg);
    }

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Test
    public void test() {
        crmV2Manager.getWechatFanObjByOpenId("74164", "wx883580c326233525", "ozSPb0bAl5Wevy5-GQyFl2aebJPo");
    }

    @Test
    public void test1() {

        CustomizeFormDataShowSettingArg arg = new CustomizeFormDataShowSettingArg();
        arg.setObjectId("cfbdbae840e04b25a4b1d4d967c5dfd4");
        arg.setObjectType(27);
        arg.setFormId("cf1a414b9b6e4526ab0d7cc79c72b4a0");
        arg.setUid("37cea29aa77f4166a3df26c4d8932f19");
        customizeFormDataService.customizeFormDataShowSetting(arg);
    }
}
