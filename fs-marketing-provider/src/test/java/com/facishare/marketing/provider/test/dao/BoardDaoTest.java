/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.dao;

import com.facishare.marketing.common.enums.BoardTypeEnum;
import com.facishare.marketing.common.enums.BoardVisibleRangeEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.BoardDao;
import com.facishare.marketing.provider.entity.BoardEntity;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/8/5 15:14
 * @Version 1.0
 */
@Slf4j
public class BoardDaoTest extends BaseTest {

    @Autowired
    BoardDao boardDao;

    @Test
    public void insertBoard(){
        System.out.println(boardDao);
        BoardEntity entity = new BoardEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa("77741");
        entity.setName("test2");
        entity.setDescription("test2");
        entity.setType(BoardTypeEnum.CUSTOMER_TYPE.getType());
        entity.setVisibleRange(BoardVisibleRangeEnum.PRIVATE_VISIBLE_RANGE.getVisibleRange());
        entity.setCreator(2);
        boardDao.insertBoard(entity);
    }

    @Test
    public void deleteBoardById(){
        boardDao.deleteBoardById("14a47ae64a6644599217d39b93e3301a");
    }

    @Test
    public void listBoardByIds(){
        List<String> ids = new LinkedList<>();
        ids.add("03f400448c0f462e95795928963c66a9");
        ids.add("bdf2f31148b04498ab75087e8a6e40a9");
        ids.add("07576e27d8604a8a8ef2310627d681c4");
        List<BoardEntity> boardEntities = boardDao.listBoardByIds(ids);
        System.out.println(boardEntities);
    }

    @Test
    public void updateBoardById(){
        BoardEntity entity = new BoardEntity();
        entity.setName("修改名称");
        entity.setDescription("修改描述");
        entity.setVisibleRange(BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange());
        entity.setType(BoardTypeEnum.PROJECT_TYPE.getType());
        entity.setId("ae70b96997a34192846fac5dacb1c57c");
        entity.setCreator(1);
//        boardDao.updateBoardById(entity);
    }

    @Test
    public void updateBoardById2(){
        BoardEntity entity = new BoardEntity();
        entity.setName("9999999999");
        entity.setId("927ed9656cd0438983e80d8cda68a802");
        entity.setCreator(2);
//        int row = boardDao.updateBoardById(entity);
//        System.out.println(row);
    }

    @Test
    public void updateBoardMenu() {
        BoardEntity boardEntity = new BoardEntity();
        boardEntity.setId("1bf47bbb7e7841b8a7f0fd66cbf8c368");
        boardEntity.setMarketingEventType("content_marketing");
        boardEntity.setAssociatedObjectType("marketing_event_data");
        Integer integer = boardDao.updateBoardMenu(boardEntity);
        System.out.println("=====================\n" + integer);
    }
}
