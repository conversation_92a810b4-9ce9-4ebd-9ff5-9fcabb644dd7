/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.common.contstant.pay.FeeType;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.entity.pay.PayOrderEntity;
import com.facishare.marketing.provider.innerData.CreateMiniAppPayOrderResult;
import com.facishare.marketing.provider.manager.pay.MerchantConfigManager;
import com.facishare.marketing.provider.manager.pay.PayOrderManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.ImmutableSet;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class PayOrderManagerTest extends BaseTest {
    @Autowired
    private PayOrderManager payOrderManager;
    @Autowired
    private MerchantConfigManager merchantConfigManager;

//    @Test
//    public void testMergeMerchantConfig(){
//        String p12B64="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";
//        System.out.println(p12B64.length());
//        merchantConfigManager.mergeMerchantConfig("zhanghui0916", "yd", "1413493002", "fasfasd07yuq94239479ffasdfas4544", "fasfasd07yuq94239479ffasdfas4544", p12B64);
//    }

    @Test
    public void testCreateMiniappPayOrder() throws Exception{
        String orderId = "test_1234dddddxxyyy123";
        String wxAppId = "wx065fa798eb6c3831";
        String wxOpenId = "oIv725SSOtvkV6H1eYLKka3xipLo";
        Result<CreateMiniAppPayOrderResult> result = payOrderManager.createMiniAppPayOrder(orderId, "zhanghui0916", wxAppId, wxOpenId, FeeType.CNY, 1, null, null, "商品XC", "*************", "dev");
        System.out.println(result);
    //    Thread.sleep(1000000);
    }

    @Test
    public void testRePay(){
        Result<CreateMiniAppPayOrderResult> result = payOrderManager.repay("a3f90a6426434ba99c50ba3285eb454b","88146");
        System.out.println(result);
    }

    @Test
    public void testSyncAndGetPayOrderByIds(){
        Map<String, PayOrderEntity> map = payOrderManager.syncAndGetPayOrderByIds(ImmutableSet.of("test_0000000000000019x"),"88146");
        System.out.println(map);
    }


}
