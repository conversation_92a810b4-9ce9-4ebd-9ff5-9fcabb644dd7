package com.facishare.marketing.provider.service.qywx

import com.facishare.marketing.api.arg.qywx.department.QueryQywxDepartmentArg
import com.facishare.marketing.api.result.qywx.department.QueryQywxDepartmentResult
import com.facishare.marketing.common.contstant.QywxUserConstants
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.util.BeanUtil
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity
import com.facishare.marketing.provider.innerResult.qywx.Department
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Test for QyWxDepartmentServiceImpl
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
class QyWxDepartmentServiceImplTest extends Specification {

    def qyWxDepartmentService = new QyWxDepartmentServiceImpl()

    def qywxCorpAgentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def qywxManager = Mock(QywxManager)
    def qywxCustomerAppInfoDAO = Mock(QywxCustomerAppInfoDAO)
    def dataPermissionManager = Mock(DataPermissionManager)

    def setup() {
        qyWxDepartmentService.qywxCorpAgentConfigDAO = qywxCorpAgentConfigDAO
        qyWxDepartmentService.qywxManager = qywxManager
        qyWxDepartmentService.qywxCustomerAppInfoDAO = qywxCustomerAppInfoDAO
        qyWxDepartmentService.dataPermissionManager = dataPermissionManager
    }

    @Unroll
    def "queryQywxDepartment - agentConfig为空"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> null

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.errCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    @Unroll
    def "queryQywxDepartment - 无应用信息且无secret"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: null)
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> []

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.isEmpty()
    }

    @Unroll
    def "queryQywxDepartment - accessToken为空"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> null

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result == null
    }

    @Unroll
    def "queryQywxDepartment - 成功获取部门信息"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department1 = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def department2 = new Department(id: 2, name: "技术部", parentId: 1, order: 2L)
        def departmentListResult = new DepartmentListResult(departmentList: [department1, department2])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 2
        result.data[0].id == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[0].parentId == 0 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[0].name == "总部"
        result.data[1].id == 2 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[1].parentId == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[1].name == "技术部"
    }

    @Unroll
    def "queryQywxDepartment - 开启权限控制"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department1 = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def department2 = new Department(id: 2, name: "技术部", parentId: 1, order: 2L)
        def departmentListResult = new DepartmentListResult(departmentList: [department1, department2])
        def accessibleDepartmentIds = [1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID]
        def filteredResults = [new QueryQywxDepartmentResult(id: 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID, name: "总部")]

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("ea", 1, false) >> accessibleDepartmentIds
        dataPermissionManager.queryAccessibleOrgStructure(_, Sets.newHashSet(accessibleDepartmentIds)) >> filteredResults

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 1
        result.data[0].name == "总部"
    }

    @Unroll
    def "queryQywxDepartment - 有应用信息的情况"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: null)
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 1
        result.data[0].id == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
    }

    @Unroll
    def "queryQywxDepartment - 有secret但无应用信息"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def department = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> []
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 1
        result.data[0].id == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
    }

    @Unroll
    def "queryQywxDepartment - 部门ID转换测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: originalId, name: "测试部门", parentId: originalParentId, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data[0].id == expectedId
        result.data[0].parentId == expectedParentId

        where:
        originalId | originalParentId | expectedId                                      | expectedParentId
        1          | 0                | 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID   | 0 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        5          | 1                | 5 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID   | 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        100        | 50               | 100 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID | 50 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
    }

    @Unroll
    def "queryQywxDepartment - 空部门列表"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def departmentListResult = new DepartmentListResult(departmentList: [])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.isEmpty()
    }

    // 测试 queryMiniAppQywxDepartment 方法

    @Unroll
    def "queryMiniAppQywxDepartment - agentConfig为空"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> null

        when:
        def result = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        result.errCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    @Unroll
    def "queryMiniAppQywxDepartment - accessToken为空"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea")
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("ea") >> null

        when:
        def result = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        result.errCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    @Unroll
    def "queryMiniAppQywxDepartment - 成功获取部门信息"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea")
        def department1 = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def department2 = new Department(id: 2, name: "技术部", parentId: 1, order: 2L)
        def departmentListResult = new DepartmentListResult(departmentList: [department1, department2])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("ea") >> "miniAppAccessToken"
        qywxManager.queryDepartment("miniAppAccessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 2
        result.data[0].id == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[0].parentId == 0 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[0].name == "总部"
        result.data[1].id == 2 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[1].parentId == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[1].name == "技术部"
    }

    @Unroll
    def "queryMiniAppQywxDepartment - 空部门列表"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea")
        def departmentListResult = new DepartmentListResult(departmentList: [])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("ea") >> "miniAppAccessToken"
        qywxManager.queryDepartment("miniAppAccessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        result.success
        result.data.isEmpty()
    }

    @Unroll
    def "queryMiniAppQywxDepartment - 部门ID转换测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea")
        def department = new Department(id: originalId, name: "测试部门", parentId: originalParentId, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("ea") >> "miniAppAccessToken"
        qywxManager.queryDepartment("miniAppAccessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        result.success
        result.data[0].id == expectedId
        result.data[0].parentId == expectedParentId

        where:
        originalId | originalParentId | expectedId                                      | expectedParentId
        1          | 0                | 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID   | 0 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        10         | 5                | 10 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID  | 5 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        999        | 100              | 999 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID | 100 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
    }

    // 测试 queryAllQywxDepartment 方法

    @Unroll
    def "queryAllQywxDepartment - agentConfig为空"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> null

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result.errCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    @Unroll
    def "queryAllQywxDepartment - 无应用信息且无secret"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: null)
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> []

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result.success
        result.data.isEmpty()
    }

    @Unroll
    def "queryAllQywxDepartment - accessToken为空"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> null

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result == null
    }

    @Unroll
    def "queryAllQywxDepartment - 成功获取所有部门信息"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department1 = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def department2 = new Department(id: 2, name: "技术部", parentId: 1, order: 2L)
        def department3 = new Department(id: 3, name: "销售部", parentId: 1, order: 3L)
        def departmentListResult = new DepartmentListResult(departmentList: [department1, department2, department3])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 3
        result.data[0].id == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[0].name == "总部"
        result.data[1].id == 2 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[1].name == "技术部"
        result.data[2].id == 3 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        result.data[2].name == "销售部"
    }

    @Unroll
    def "queryAllQywxDepartment - 有应用信息的情况"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: null)
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 1
        result.data[0].id == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
    }

    @Unroll
    def "queryAllQywxDepartment - 空部门列表"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def departmentListResult = new DepartmentListResult(departmentList: [])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result.success
        result.data.isEmpty()
    }

    // 第二批测试 - 边界条件和异常情况

    @Unroll
    def "queryQywxDepartment - 部门属性完整性测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(
            id: 1,
            name: "技术部",
            nameEn: "Tech Dept",
            parentId: 0,
            order: 100L
        )
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data[0].name == "技术部"
        result.data[0].nameEn == "Tech Dept"
        result.data[0].order == 100L
    }

    @Unroll
    def "queryQywxDepartment - 多层级部门结构测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def departments = [
            new Department(id: 1, name: "总部", parentId: 0, order: 1L),
            new Department(id: 2, name: "技术部", parentId: 1, order: 2L),
            new Department(id: 3, name: "前端组", parentId: 2, order: 3L),
            new Department(id: 4, name: "后端组", parentId: 2, order: 4L),
            new Department(id: 5, name: "销售部", parentId: 1, order: 5L)
        ]
        def departmentListResult = new DepartmentListResult(departmentList: departments)

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 5
        // 验证层级关系
        def techDept = result.data.find { it.name == "技术部" }
        def frontendGroup = result.data.find { it.name == "前端组" }
        def backendGroup = result.data.find { it.name == "后端组" }

        frontendGroup.parentId == techDept.id
        backendGroup.parentId == techDept.id
    }

    @Unroll
    def "queryQywxDepartment - 权限过滤测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def departments = [
            new Department(id: 1, name: "总部", parentId: 0, order: 1L),
            new Department(id: 2, name: "技术部", parentId: 1, order: 2L),
            new Department(id: 3, name: "销售部", parentId: 1, order: 3L)
        ]
        def departmentListResult = new DepartmentListResult(departmentList: departments)
        def accessibleDepartmentIds = [1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID, 2 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID]
        def filteredResults = [
            new QueryQywxDepartmentResult(id: 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID, name: "总部"),
            new QueryQywxDepartmentResult(id: 2 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID, name: "技术部")
        ]

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("ea", 1, false) >> accessibleDepartmentIds
        dataPermissionManager.queryAccessibleOrgStructure(_, Sets.newHashSet(accessibleDepartmentIds)) >> filteredResults

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 2
        result.data.find { it.name == "总部" } != null
        result.data.find { it.name == "技术部" } != null
        result.data.find { it.name == "销售部" } == null // 被权限过滤掉
    }

    @Unroll
    def "queryQywxDepartment - 空权限列表测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: 1, name: "总部", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("ea", 1, false) >> []
        dataPermissionManager.queryAccessibleOrgStructure(_, Sets.newHashSet([])) >> []

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.isEmpty()
    }

    @Unroll
    def "queryMiniAppQywxDepartment - 特殊字符部门名测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea")
        def department = new Department(
            id: 1,
            name: "技术部&研发中心（总部）",
            nameEn: "Tech & R&D Center (HQ)",
            parentId: 0,
            order: 1L
        )
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("ea") >> "miniAppAccessToken"
        qywxManager.queryDepartment("miniAppAccessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        result.success
        result.data[0].name == "技术部&研发中心（总部）"
        result.data[0].nameEn == "Tech & R&D Center (HQ)"
    }

    @Unroll
    def "queryAllQywxDepartment - 大量部门测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def departments = []
        // 创建100个部门
        for (int i = 1; i <= 100; i++) {
            departments.add(new Department(id: i, name: "部门${i}", parentId: i > 1 ? 1 : 0, order: i as Long))
        }
        def departmentListResult = new DepartmentListResult(departmentList: departments)

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result.success
        result.data.size() == 100
        result.data.every { it.id >= QywxUserConstants.BASE_QYWX_DEPARTMENT_ID }
        result.data.every { it.parentId >= QywxUserConstants.BASE_QYWX_DEPARTMENT_ID }
    }

    // 第三批测试 - 参数化测试和综合场景

    @Unroll
    def "queryQywxDepartment - 参数化测试各种配置组合"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: secret)
        def appInfoEntities = hasAppInfo ? [new QywxCustomerAppInfoEntity()] : []
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> appInfoEntities
        if (hasAppInfo || secret != null) {
            qywxManager.getAccessToken("ea") >> accessToken
            if (accessToken != null) {
                qywxManager.queryDepartment(accessToken) >> departmentListResult
                dataPermissionManager.getNewDataPermissionSetting("ea") >> false
            }
        }

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        if (expectedSuccess) {
            result.success
            if (shouldHaveData) {
                result.data.size() == 1
                result.data[0].id == 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
            } else {
                result.data.isEmpty()
            }
        } else {
            result == null || result.errCode != null
        }

        where:
        secret   | hasAppInfo | accessToken   | expectedSuccess | shouldHaveData
        "secret" | false      | "token"       | true            | true
        "secret" | true       | "token"       | true            | true
        null     | true       | "token"       | true            | true
        null     | false      | "token"       | true            | false
        "secret" | false      | null          | false           | false
        "secret" | true       | ""            | false           | false
    }

    @Unroll
    def "queryMiniAppQywxDepartment - 参数化测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: ea, fsUserId: userId)
        def agentConfig = agentConfigResult
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa(ea) >> agentConfig
        if (agentConfig != null) {
            qywxManager.getMiniAppAccessToken(ea) >> accessToken
            if (accessToken != null) {
                qywxManager.queryDepartment(accessToken) >> departmentListResult
            }
        }

        when:
        def result = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        result.errCode == expectedErrorCode || (result.success && expectedErrorCode == null)

        where:
        ea   | userId | agentConfigResult                                    | accessToken | expectedErrorCode
        "ea" | 1      | new QywxCorpAgentConfigEntity(corpid: "corp", ea: "ea") | "token"     | null
        "ea" | 1      | new QywxCorpAgentConfigEntity(corpid: "corp", ea: "ea") | null        | SHErrorCode.SYSTEM_ERROR.errorCode
        "ea" | 1      | null                                                 | null        | SHErrorCode.SYSTEM_ERROR.errorCode
    }

    @Unroll
    def "queryAllQywxDepartment - 参数化测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: ea, fsUserId: userId)
        def agentConfig = agentConfigResult
        def appInfoEntities = hasAppInfo ? [new QywxCustomerAppInfoEntity()] : []
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa(ea) >> agentConfig
        if (agentConfig != null) {
            qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.corpid, agentConfig.ea) >> appInfoEntities
            if (hasAppInfo || agentConfig.secret != null) {
                qywxManager.getAccessToken(ea) >> accessToken
                if (accessToken != null) {
                    qywxManager.queryDepartment(accessToken) >> departmentListResult
                }
            }
        }

        when:
        def result = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        if (expectedSuccess) {
            result.success
        } else {
            result == null || result.errCode != null
        }

        where:
        ea   | userId | agentConfigResult                                                      | hasAppInfo | accessToken | expectedSuccess
        "ea" | 1      | new QywxCorpAgentConfigEntity(corpid: "corp", ea: "ea", secret: "sec") | false      | "token"     | true
        "ea" | 1      | new QywxCorpAgentConfigEntity(corpid: "corp", ea: "ea", secret: "sec") | true       | "token"     | true
        "ea" | 1      | new QywxCorpAgentConfigEntity(corpid: "corp", ea: "ea", secret: null)  | true       | "token"     | true
        "ea" | 1      | new QywxCorpAgentConfigEntity(corpid: "corp", ea: "ea", secret: null)  | false      | "token"     | true
        "ea" | 1      | null                                                                   | false      | null        | false
    }

    @Unroll
    def "三个方法的一致性测试 - 相同输入应产生相似结果"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        // 为所有三个方法设置相同的Mock
        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.getMiniAppAccessToken("ea") >> "miniAppAccessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        qywxManager.queryDepartment("miniAppAccessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result1 = qyWxDepartmentService.queryQywxDepartment(arg)
        def result2 = qyWxDepartmentService.queryMiniAppQywxDepartment(arg)
        def result3 = qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        result1.success
        result2.success
        result3.success
        result1.data.size() == result2.data.size()
        result1.data.size() == result3.data.size()
        result1.data[0].name == result2.data[0].name
        result1.data[0].name == result3.data[0].name
        result1.data[0].id == result2.data[0].id
        result1.data[0].id == result3.data[0].id
    }

    @Unroll
    def "BeanUtil.copy 功能验证测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(
            id: 123,
            name: "完整测试部门",
            nameEn: "Complete Test Dept",
            parentId: 456,
            order: 789L
        )
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        def resultDept = result.data[0]
        // 验证BeanUtil.copy正确复制了所有属性
        resultDept.name == "完整测试部门"
        resultDept.nameEn == "Complete Test Dept"
        resultDept.order == 789L
        // 验证ID转换正确
        resultDept.id == 123 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
        resultDept.parentId == 456 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID
    }

    // 第四批测试 - 异常处理和Mock验证

    @Unroll
    def "queryQywxDepartment - Mock调用次数验证"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> permissionEnabled
        if (permissionEnabled) {
            dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("ea", 1, false) >> [1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID]
            dataPermissionManager.queryAccessibleOrgStructure(_, _) >> [new QueryQywxDepartmentResult(id: 1 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID, name: "测试部门")]
        }

        when:
        qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        1 * qywxCorpAgentConfigDAO.queryAgentByEa("ea")
        1 * qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea")
        1 * qywxManager.getAccessToken("ea")
        1 * qywxManager.queryDepartment("accessToken")
        1 * dataPermissionManager.getNewDataPermissionSetting("ea")
        if (permissionEnabled) {
            1 * dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("ea", 1, false)
            1 * dataPermissionManager.queryAccessibleOrgStructure(_, _)
        } else {
            0 * dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(_, _, _)
            0 * dataPermissionManager.queryAccessibleOrgStructure(_, _)
        }

        where:
        permissionEnabled << [true, false]
    }

    @Unroll
    def "queryMiniAppQywxDepartment - Mock调用次数验证"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea")
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("ea") >> "miniAppAccessToken"
        qywxManager.queryDepartment("miniAppAccessToken") >> departmentListResult

        when:
        qyWxDepartmentService.queryMiniAppQywxDepartment(arg)

        then:
        1 * qywxCorpAgentConfigDAO.queryAgentByEa("ea")
        1 * qywxManager.getMiniAppAccessToken("ea")
        1 * qywxManager.queryDepartment("miniAppAccessToken")
        // 小程序方法不调用权限相关方法
        0 * dataPermissionManager.getNewDataPermissionSetting(_)
        0 * dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(_, _, _)
        0 * dataPermissionManager.queryAccessibleOrgStructure(_, _)
    }

    @Unroll
    def "queryAllQywxDepartment - Mock调用次数验证"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult

        when:
        qyWxDepartmentService.queryAllQywxDepartment(arg)

        then:
        1 * qywxCorpAgentConfigDAO.queryAgentByEa("ea")
        1 * qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea")
        1 * qywxManager.getAccessToken("ea")
        1 * qywxManager.queryDepartment("accessToken")
        // queryAllQywxDepartment 不调用权限相关方法
        0 * dataPermissionManager.getNewDataPermissionSetting(_)
        0 * dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(_, _, _)
        0 * dataPermissionManager.queryAccessibleOrgStructure(_, _)
    }

    @Unroll
    def "queryQywxDepartment - 异常处理测试"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> { throw new RuntimeException("网络异常") }

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        thrown(RuntimeException)
    }

    @Unroll
    def "queryQywxDepartment - 部门列表为null的处理"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def departmentListResult = new DepartmentListResult(departmentList: null)

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> false

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data.isEmpty()
    }

    @Unroll
    def "queryQywxDepartment - 权限管理器返回null的处理"() {
        given:
        def arg = new QueryQywxDepartmentArg(fsEa: "ea", fsUserId: 1)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corpId", ea: "ea", secret: "secret")
        def appInfoEntity = new QywxCustomerAppInfoEntity()
        def department = new Department(id: 1, name: "测试部门", parentId: 0, order: 1L)
        def departmentListResult = new DepartmentListResult(departmentList: [department])

        qywxCorpAgentConfigDAO.queryAgentByEa("ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corpId", "ea") >> [appInfoEntity]
        qywxManager.getAccessToken("ea") >> "accessToken"
        qywxManager.queryDepartment("accessToken") >> departmentListResult
        dataPermissionManager.getNewDataPermissionSetting("ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("ea", 1, false) >> null
        dataPermissionManager.queryAccessibleOrgStructure(_, _) >> null

        when:
        def result = qyWxDepartmentService.queryQywxDepartment(arg)

        then:
        result.success
        result.data == null || result.data.isEmpty()
    }

    @Unroll
    def "所有方法的空参数测试"() {
        when:
        def result = qyWxDepartmentService."$methodName"(null)

        then:
        thrown(Exception)

        where:
        methodName << ["queryQywxDepartment", "queryMiniAppQywxDepartment", "queryAllQywxDepartment"]
    }
}
