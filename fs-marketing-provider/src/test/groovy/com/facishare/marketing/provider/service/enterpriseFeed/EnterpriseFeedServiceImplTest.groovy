package com.facishare.marketing.provider.service.enterpriseFeed

import com.facishare.mankeep.api.outService.result.card.QueryCardInfoListResult
import com.facishare.mankeep.api.outService.result.card.QueryCardInfoResult
import com.facishare.mankeep.api.outService.service.OutCardService
import com.facishare.mankeep.api.outService.service.OutEnterpriseFeedService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.api.arg.AddEnterpriseFeedArg
import com.facishare.marketing.api.arg.QueryEnterpriseFeedArg
import com.facishare.marketing.api.arg.QueryEnterpriseFeedCommentArg
import com.facishare.marketing.api.result.EnterpriseFeedCommentResult
import com.facishare.marketing.api.result.EnterpriseFeedResult
import com.facishare.marketing.api.result.EnterpriseInfoResult
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.provider.dao.ActivityDAO
import com.facishare.marketing.provider.dao.ArticleDAO
import com.facishare.marketing.provider.dao.PhotoDAO
import com.facishare.marketing.provider.dao.ProductDAO
import com.facishare.marketing.provider.dao.enterpriseFeed.EnterpriseFeedDao
import com.facishare.marketing.provider.entity.ActivityEntity
import com.facishare.marketing.provider.entity.ArticleEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.ProductEntity
import com.facishare.marketing.provider.entity.enterpriseFeed.EnterpriseFeedCommentEntity
import com.facishare.marketing.provider.entity.enterpriseFeed.EnterpriseFeedEntity
import com.facishare.marketing.provider.manager.FileV2Manager
import com.facishare.marketing.provider.manager.PhotoManager
import com.facishare.marketing.provider.manager.SettingManager
import spock.lang.*

/**
 * Test for EnterpriseFeedServiceImpl
 * <AUTHOR>
 * @date 2024/5/21 17:44
 */
class EnterpriseFeedServiceImplTest extends Specification {

    def enterpriseFeedServiceImpl = new EnterpriseFeedServiceImpl()

    def enterpriseFeedDao = Mock(EnterpriseFeedDao)
    def photoDAO = Mock(PhotoDAO)
    def articleDAO = Mock(ArticleDAO)
    def productDAO = Mock(ProductDAO)
    def activityDAO = Mock(ActivityDAO)
    def fileV2Manager = Mock(FileV2Manager)
    def outEnterpriseFeedService = Mock(OutEnterpriseFeedService)
    def photoManager = Mock(PhotoManager)
    def settingManager = Mock(SettingManager)
    def outCardService = Mock(OutCardService)

    def setup() {
        enterpriseFeedServiceImpl.enterpriseFeedDao = enterpriseFeedDao
        enterpriseFeedServiceImpl.photoDAO = photoDAO
        enterpriseFeedServiceImpl.articleDAO = articleDAO
        enterpriseFeedServiceImpl.productDAO = productDAO
        enterpriseFeedServiceImpl.activityDAO = activityDAO
        enterpriseFeedServiceImpl.fileV2Manager = fileV2Manager
        enterpriseFeedServiceImpl.outEnterpriseFeedService = outEnterpriseFeedService
        enterpriseFeedServiceImpl.photoManager = photoManager
        enterpriseFeedServiceImpl.settingManager = settingManager
        enterpriseFeedServiceImpl.outCardService = outCardService
    }

    @Unroll
    def "queryEnterpriseFeedResultTest"() {
        given:
        enterpriseFeedDao.queryEnterpriseFeedResult(*_) >> [new EnterpriseFeedEntity(id: "id7",objectType: 7),new EnterpriseFeedEntity(id: "id6",objectType: 6),new EnterpriseFeedEntity(id: "id4",objectType: 4),new EnterpriseFeedEntity(id: "id13",objectType: 13)]
        enterpriseFeedDao.countEnterpriseCommentFeedByFeedId(*_) >> 0
        articleDAO.queryArticleDetail(*_) >> new ArticleEntity()
        productDAO.queryProductDetailByEa(*_) >> new ProductEntity()
        activityDAO.getById(*_) >> new ActivityEntity(startTime: new Date(),endTime: new Date())
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]
        photoManager.queryArticlePhotoUrl(*_) >> "queryArticlePhotoUrlResponse"
        photoManager.queryActivityPhotoUrl(*_) >> "queryActivityPhotoUrlResponse"
        settingManager.queryEnterpriseInfo(*_) >> new Result<EnterpriseInfoResult>(0, "errMsg", new EnterpriseInfoResult())

        expect:
        enterpriseFeedServiceImpl.queryEnterpriseFeedResult(vo) 

        where:
        vo                           || expectedResult
        new QueryEnterpriseFeedArg(pageNum: 1, pageSize: 10) || new Result<PageResult<EnterpriseFeedResult>>(0, "errMsg", new PageResult<EnterpriseFeedResult>())
    }

    @Unroll
    def "deleteEnterpriseFeedTest"() {
        given:
        enterpriseFeedDao.updateEnterpriseFeed(*_) >> 0
        outEnterpriseFeedService.checkAndSyncFeedByEa(*_) >> new ModelResult(0, "errMsg", "data")

        expect:
        enterpriseFeedServiceImpl.deleteEnterpriseFeed(id, ea) 

        where:
        id   | ea   || expectedResult
        "id" | "ea" || new Result(0, "errMsg", "data")
    }

    @Unroll
    def "queryEnterpriseFeedCommentResultTest"() {
        given:
        enterpriseFeedDao.queryEnterpriseFeedCommentResult(*_) >> [new EnterpriseFeedCommentEntity(uid: "uid")]
        outCardService.queryCardInfoList(*_) >> new ModelResult<QueryCardInfoListResult>(0, "errMsg", new QueryCardInfoListResult(cardInfoResults: [new QueryCardInfoResult(uid: "uid")]))

        expect:
        enterpriseFeedServiceImpl.queryEnterpriseFeedCommentResult(vo) 

        where:
        vo                                  || expectedResult
        new QueryEnterpriseFeedCommentArg(pageNum: 1, pageSize: 10) || new Result<PageResult<EnterpriseFeedCommentResult>>(0, "errMsg", new PageResult<EnterpriseFeedCommentResult>())
    }

    @Unroll
    def "deleteEnterpriseCommentFeedTest"() {
        given:
        enterpriseFeedDao.updateEnterpriseCommentFeed(*_) >> 0

        expect:
        enterpriseFeedServiceImpl.deleteEnterpriseCommentFeed(id) 

        where:
        id   || expectedResult
        "id" || new Result(0, "errMsg", "data")
    }

    @Unroll
    def "addEnterpriseFeedTest"() {
        given:
        enterpriseFeedDao.addEnterpriseFeed(*_) >> 0
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        fileV2Manager.getApathByTApath(*_) >> new FileV2Manager.FileManagerPicResult()
        outEnterpriseFeedService.checkAndSyncFeedByEa(*_) >> new ModelResult(0, "errMsg", "data")

        expect:
        enterpriseFeedServiceImpl.addEnterpriseFeed(vo) 

        where:
        vo                         || expectedResult
        new AddEnterpriseFeedArg(ea: "ea",objectId: "objectId",objectType: 7,status: 1,userId: 1000,recommendation: "recommendation",photos: ["TA_sss"]) || new Result(0, "errMsg", "data")
        new AddEnterpriseFeedArg(ea: "ea",objectId: "objectId",objectType: 7,status: 1,userId: 1000,recommendation: "recommendation",photos: ["A_sss"]) || new Result(0, "errMsg", "data")

    }

}