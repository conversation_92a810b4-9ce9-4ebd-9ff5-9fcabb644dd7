package com.facishare.marketing.provider.service.photoLibrary

import com.facishare.marketing.api.arg.photoLibrary.UploadPhotoArg
import com.facishare.marketing.api.service.photoLibrary.PhotoLibraryService
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.provider.dao.photoLibrary.PhotoLibraryDAO
import com.facishare.marketing.provider.entity.PhotoLibraryEntity
import com.facishare.marketing.provider.manager.FileV2Manager
import spock.lang.Specification

class PhotoLibraryServiceImplSimpleSpec extends Specification {

    PhotoLibraryServiceImpl photoLibraryService
    PhotoLibraryDAO photoLibraryDAO = Mock()
    FileV2Manager fileV2Manager = Mock()

    def setup() {
        photoLibraryService = new PhotoLibraryServiceImpl()
        photoLibraryService.photoLibraryDAO = photoLibraryDAO
        photoLibraryService.fileV2Manager = fileV2Manager
    }

    def "should return error when photo name exists"() {
        given:
        def ea = "test_ea"
        def fsUserId = 1001
        def arg = new UploadPhotoArg()
        arg.photoName = "test.png"
        arg.source = PhotoLibraryService.PHOTO_LIBRARY_SOURCE

        when:
        photoLibraryDAO.getByPhotoName(ea, [arg.photoName]) >> [new PhotoLibraryEntity()]
        def result = photoLibraryService.uploadPhoto(ea, fsUserId, arg)

        then:
        result.code == SHErrorCode.PIC_NAME_EXIST.code
    }
} 