package com.facishare.marketing.provider.service

import com.alibaba.fastjson.JSON
import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.*
import com.facishare.marketing.api.arg.MarketingEventSetConfigArg.EventTypeAndColorData
import com.facishare.marketing.api.arg.marketingEvent.GetMarketingEventByObjectIdArg
import com.facishare.marketing.api.arg.marketingEvent.ListMultiVenueMarketingEventArg
import com.facishare.marketing.api.arg.marketingEvent.SaveSyncRulesArg
import com.facishare.marketing.api.data.MarketingEventData
import com.facishare.marketing.api.data.MarketingUserGroupData
import com.facishare.marketing.api.data.material.AbstractMaterialData
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult
import com.facishare.marketing.api.result.calendar.MarketingEventsResult
import com.facishare.marketing.api.result.marketingEvent.MultiVenueMarketingEventResult
import com.facishare.marketing.api.vo.QueryMarketingEventLevelDataVO
import com.facishare.marketing.api.vo.marketingevent.*
import com.facishare.marketing.common.enums.I18nKeyEnum
import com.facishare.marketing.common.enums.MarketingEventEnum
import com.facishare.marketing.common.enums.ObjectTypeEnum
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.FieldValue
import com.facishare.marketing.common.typehandlers.value.FieldValueList
import com.facishare.marketing.common.typehandlers.value.RuleGroup
import com.facishare.marketing.common.typehandlers.value.RuleGroupList
import com.facishare.marketing.common.util.I18nUtil
import com.facishare.marketing.common.util.UUIDUtil
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.entity.*
import com.facishare.marketing.provider.entity.pay.PayOrderEntity
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.pay.PayOrderManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager
import com.facishare.marketing.provider.remote.MarketingEventRemoteManager
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import org.mockito.Matchers

/**
 * Test for MarketingEventServiceImpl
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([UUIDUtil.class, I18nUtil.class, ThreadPoolUtils.class, JSON.class])
@PowerMockIgnore("javax.net.ssl.*")
class MarketingEventServiceImplTest extends Specification {

    def marketingEventService = new MarketingEventServiceImpl()

    // Mock all dependencies
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def contentMarketingEventMarketingUserGroupRelationDAO = Mock(ContentMarketingEventMarketingUserGroupRelationDAO)
    def marketingEventManager = Mock(MarketingEventManager)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def eieaConverter = Mock(EIEAConverter)
    def marketingEventRemoteManager = Mock(MarketingEventRemoteManager)
    def enterpriseMetaConfigDao = Mock(EnterpriseMetaConfigDao)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def objectDescribeService = Mock(ObjectDescribeService)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def payOrderManager = Mock(PayOrderManager)
    def campaignPayOrderDao = Mock(CampaignPayOrderDao)
    def marketingEventSyncRuleDao = Mock(MarketingEventSyncRuleDao)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def marketingActivityRemoteManager = Mock(MarketingActivityRemoteManager)
    def appVersionManager = Mock(AppVersionManager)
    def userRoleManager = Mock(UserRoleManager)
    def conferenceDAO = Mock(ConferenceDAO)
    def fileV2Manager = Mock(FileV2Manager)

    def setup() {
        // Inject mocked dependencies
        marketingEventService.contentMarketingEventMaterialRelationDAO = contentMarketingEventMaterialRelationDAO
        marketingEventService.contentMarketingEventMarketingUserGroupRelationDAO = contentMarketingEventMarketingUserGroupRelationDAO
        marketingEventService.marketingEventManager = marketingEventManager
        marketingEventService.campaignMergeDataManager = campaignMergeDataManager
        marketingEventService.eieaConverter = eieaConverter
        marketingEventService.marketingEventRemoteManager = marketingEventRemoteManager
        marketingEventService.enterpriseMetaConfigDao = enterpriseMetaConfigDao
        marketingEventService.customizeFormDataManager = customizeFormDataManager
        marketingEventService.crmV2Manager = crmV2Manager
        marketingEventService.objectDescribeService = objectDescribeService
        marketingEventService.fsAddressBookManager = fsAddressBookManager
        marketingEventService.payOrderManager = payOrderManager
        marketingEventService.campaignPayOrderDao = campaignPayOrderDao
        marketingEventService.marketingEventSyncRuleDao = marketingEventSyncRuleDao
        marketingEventService.campaignMergeDataDAO = campaignMergeDataDAO
        marketingEventService.marketingActivityRemoteManager = marketingActivityRemoteManager
        marketingEventService.appVersionManager = appVersionManager
        marketingEventService.userRoleManager = userRoleManager
        marketingEventService.conferenceDAO = conferenceDAO
        marketingEventService.fileV2Manager = fileV2Manager
        marketingEventService.defaultCoverPath = "/default/cover/path"

        // Mock static methods
        PowerMockito.mockStatic(UUIDUtil.class)
        PowerMockito.mockStatic(I18nUtil.class)
        PowerMockito.mockStatic(ThreadPoolUtils.class)
        PowerMockito.mockStatic(JSON.class)
    }

    def "listBriefMarketingEvents - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new ListBriefMarketingEventsArg()
        def enterpriseId = 123
        def expectedResults = [new MarketingEventsBriefResult()]

        eieaConverter.enterpriseAccountToId(ea) >> enterpriseId
        marketingEventManager.listMarketingEvents(enterpriseId, fsUserId, _) >> expectedResults

        when:
        def result = marketingEventService.listBriefMarketingEvents(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data == expectedResults
        1 * marketingEventManager.listMarketingEvents(enterpriseId, fsUserId, { ListBriefMarketingEventsArg it ->
            it.nlikeEventTypeList.contains(MarketingEventEnum.AD_MARKETING.getEventType())
        })
    }

    def "getMarketingEventsDetail - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def marketingEventId = "event-123"
        def expectedResult = new MarketingEventsResult()

        marketingEventManager.getMarketingEventsDetail(ea, fsUserId, marketingEventId) >> expectedResult

        when:
        def result = marketingEventService.getMarketingEventsDetail(ea, fsUserId, marketingEventId)

        then:
        result.isSuccess()
        result.data == expectedResult
    }

    def "addMaterial - marketing event not found"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new AddMaterialArg(marketingEventId: "event-123", objectType: 1, objectId: "obj-123")

        marketingEventManager.getMarketingEventData(ea, -10000, arg.marketingEventId) >> null

        when:
        def result = marketingEventService.addMaterial(ea, fsUserId, arg)

        then:
        !result.isSuccess()
        result.errorCode == SHErrorCode.NO_DATA.errorCode
    }

    def "addMaterial - success case with normal object type"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new AddMaterialArg(marketingEventId: "event-123", objectType: 1, objectId: "obj-123")
        def marketingEventData = new MarketingEventData()
        def uuid = "test-uuid"

        marketingEventManager.getMarketingEventData(ea, -10000, arg.marketingEventId) >> marketingEventData
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid)

        when:
        def result = marketingEventService.addMaterial(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data == uuid
        1 * contentMarketingEventMaterialRelationDAO.save({ ContentMarketingEventMaterialRelationEntity entity ->
            entity.id == uuid &&
            entity.ea == ea &&
            entity.objectId == arg.objectId &&
            entity.objectType == arg.objectType &&
            entity.marketingEventId == arg.marketingEventId &&
            entity.isMobileDisplay == false
        })
        // Should not call customizeFormDataManager for non-form object type
        0 * customizeFormDataManager.createCustomizeFormDataQRCode(_, _, _)
    }

    def "addMaterial - success case with customize form type (covers handleSpecialMaterial private method)"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new AddMaterialArg(marketingEventId: "event-123", objectType: ObjectTypeEnum.CUSTOMIZE_FORM.getType(), objectId: "form-123")
        def marketingEventData = new MarketingEventData()
        def uuid = "test-uuid"

        marketingEventManager.getMarketingEventData(ea, -10000, arg.marketingEventId) >> marketingEventData
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid)

        when:
        def result = marketingEventService.addMaterial(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data == uuid
        1 * contentMarketingEventMaterialRelationDAO.save({ ContentMarketingEventMaterialRelationEntity entity ->
            entity.id == uuid &&
            entity.ea == ea &&
            entity.objectId == arg.objectId &&
            entity.objectType == arg.objectType &&
            entity.marketingEventId == arg.marketingEventId &&
            entity.isMobileDisplay == false
        })
        // Should call customizeFormDataManager for form object type (handleSpecialMaterial private method)
        1 * customizeFormDataManager.createCustomizeFormDataQRCode(arg.objectId, arg.marketingEventId, ea)
    }

    def "checkAndAddMaterials - success case with new entities (covers getDistinctEntities and convert private methods)"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new AddMaterialsArg()
        arg.marketingEventId = "event-123"
        arg.materialInfos = [
            new AddMaterialsArg.MaterialInfo(1, "obj-1"),
            new AddMaterialsArg.MaterialInfo(2, "obj-2")
        ]
        def uuid1 = "uuid-1"
        def uuid2 = "uuid-2"

        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1).thenReturn(uuid2)

        // This will trigger the convert() private method to convert entities to ObjectInfo
        // and getDistinctEntities() private method to filter existing entities
        contentMarketingEventMaterialRelationDAO.batchGetByEaAndObjectInfos(ea, { List<ContentMarketingEventMaterialRelationEntity.ObjectInfo> objectInfos ->
            objectInfos.size() == 2 &&
            objectInfos[0].objectType == 1 && objectInfos[0].objectId == "obj-1" && objectInfos[0].marketingEventId == "event-123" &&
            objectInfos[1].objectType == 2 && objectInfos[1].objectId == "obj-2" && objectInfos[1].marketingEventId == "event-123"
        }) >> []

        when:
        def result = marketingEventService.checkAndAddMaterials(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data == true
        1 * contentMarketingEventMaterialRelationDAO.batchAdd({ List<ContentMarketingEventMaterialRelationEntity> entities ->
            entities.size() == 2 &&
            entities[0].id == uuid1 &&
            entities[1].id == uuid2
        })
    }

    def "checkAndAddMaterials - success case with no new entities (covers getDistinctEntities filtering logic)"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new AddMaterialsArg()
        arg.marketingEventId = "event-123"
        arg.materialInfos = [
            new AddMaterialsArg.MaterialInfo(1, "obj-1"),
            new AddMaterialsArg.MaterialInfo(2, "obj-2")
        ]
        def uuid1 = "uuid-1"
        def uuid2 = "uuid-2"

        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1).thenReturn(uuid2)

        // Create existing entities that match some of the new entities
        def existingEntity1 = new ContentMarketingEventMaterialRelationEntity()
        existingEntity1.ea = ea
        existingEntity1.objectType = 1
        existingEntity1.objectId = "obj-1"

        def existingEntity2 = new ContentMarketingEventMaterialRelationEntity()
        existingEntity2.ea = ea
        existingEntity2.objectType = 2
        existingEntity2.objectId = "obj-2"

        // This will test the filtering logic in getDistinctEntities private method
        contentMarketingEventMaterialRelationDAO.batchGetByEaAndObjectInfos(ea, _) >> [existingEntity1, existingEntity2]

        when:
        def result = marketingEventService.checkAndAddMaterials(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data == true
        // No entities should be added since all already exist
        0 * contentMarketingEventMaterialRelationDAO.batchAdd(_)
    }

    def "checkAndAddMaterials - partial existing entities (covers getDistinctEntities partial filtering)"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new AddMaterialsArg()
        arg.marketingEventId = "event-123"
        arg.materialInfos = [
            new AddMaterialsArg.MaterialInfo(1, "obj-1"),
            new AddMaterialsArg.MaterialInfo(2, "obj-2"),
            new AddMaterialsArg.MaterialInfo(3, "obj-3")
        ]
        def uuid1 = "uuid-1"
        def uuid2 = "uuid-2"
        def uuid3 = "uuid-3"

        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1).thenReturn(uuid2).thenReturn(uuid3)

        // Only obj-1 exists, obj-2 and obj-3 should be added
        def existingEntity = new ContentMarketingEventMaterialRelationEntity()
        existingEntity.ea = ea
        existingEntity.objectType = 1
        existingEntity.objectId = "obj-1"

        contentMarketingEventMaterialRelationDAO.batchGetByEaAndObjectInfos(ea, _) >> [existingEntity]

        when:
        def result = marketingEventService.checkAndAddMaterials(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data == true
        // Only 2 entities should be added (obj-2 and obj-3)
        1 * contentMarketingEventMaterialRelationDAO.batchAdd({ List<ContentMarketingEventMaterialRelationEntity> entities ->
            entities.size() == 2 &&
            entities.any { it.objectId == "obj-2" && it.objectType == 2 } &&
            entities.any { it.objectId == "obj-3" && it.objectType == 3 }
        })
    }

    def "listMaterials - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new ListMaterialsArg()
        arg.id = "event-123"
        arg.objectTypes = [1, 2]
        arg.pageNum = 1
        arg.pageSize = 10
        def expectedResult = new PageResult<AbstractMaterialData>()

        marketingEventManager.getMaterials(ea, arg.objectTypes, arg.id, arg.pageNum, arg.pageSize, false) >> expectedResult

        when:
        def result = marketingEventService.listMaterials(ea, fsUserId, arg)

        then:
        result == expectedResult
    }

    def "mergeUserGroupToMarketingEvent - success case with inserts and deletes"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new AddUserGroupToMarketingEventArg()
        arg.marketingEventId = "event-123"
        arg.userGroupIds = ["group-1", "group-2", "group-3"]

        def existingEntity1 = new ContentMarketingEventMarketingUserGroupRelationEntity()
        existingEntity1.marketingUserGroupId = "group-1"
        def existingEntity2 = new ContentMarketingEventMarketingUserGroupRelationEntity()
        existingEntity2.marketingUserGroupId = "group-4"

        def uuid1 = "uuid-1"
        def uuid2 = "uuid-2"

        contentMarketingEventMarketingUserGroupRelationDAO.list(ea, arg.marketingEventId) >> [existingEntity1, existingEntity2]
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1).thenReturn(uuid2)

        when:
        def result = marketingEventService.mergeUserGroupToMarketingEvent(ea, fsUserId, arg)

        then:
        result.isSuccess()
        1 * contentMarketingEventMarketingUserGroupRelationDAO.deleteByMarketingEventIdAndUserGroupIds(ea, arg.marketingEventId, ["group-4"])
        1 * contentMarketingEventMarketingUserGroupRelationDAO.batchInsert({ List<ContentMarketingEventMarketingUserGroupRelationEntity> entities ->
            entities.size() == 2 &&
            entities.any { it.marketingUserGroupId == "group-2" && it.id == uuid1 } &&
            entities.any { it.marketingUserGroupId == "group-3" && it.id == uuid2 }
        })
    }

    def "listUserGroupFromMarketingEvent - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def marketingEventId = "event-123"
        def expectedResult = [new MarketingUserGroupData()]

        marketingEventManager.getMarketingUserGroups(ea, marketingEventId) >> expectedResult

        when:
        def result = marketingEventService.listUserGroupFromMarketingEvent(ea, fsUserId, marketingEventId)

        then:
        result.isSuccess()
        result.data == expectedResult
    }

    def "deleteRelation - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def marketingEventId = "event-123"
        def objectType = 1
        def objectId = "obj-123"

        when:
        def result = marketingEventService.deleteRelation(ea, fsUserId, marketingEventId, objectType, objectId)

        then:
        result.isSuccess()
        1 * contentMarketingEventMaterialRelationDAO.deleteByEaAndMarketingEventIdAndObjectTypeAndObjectId(ea, marketingEventId, objectType, objectId)
    }

    def "setEventTypeAndColorConfig - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def eventTypeAndColorDataList = [
            new EventTypeAndColorData("type1", "color1"),
            new EventTypeAndColorData("type2", "color2")
        ]

        when:
        def result = marketingEventService.setEventTypeAndColorConfig(ea, fsUserId, eventTypeAndColorDataList)

        then:
        result.isSuccess()
        result.data == true
        1 * enterpriseMetaConfigDao.updateMarketingEventTypeColor(ea, { FieldValueList fieldValueList ->
            fieldValueList.size() == 2 &&
            fieldValueList[0].name == "type1" && fieldValueList[0].value == "color1" &&
            fieldValueList[1].name == "type2" && fieldValueList[1].value == "color2"
        })
    }

    def "getOrCreateEventTypeAndColorConfig - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def entity = new EnterpriseMetaConfigEntity()
        def fieldValueList = new FieldValueList()
        fieldValueList.add(new FieldValue("type1", "color1"))
        entity.marketingEventTypeColor = fieldValueList

        enterpriseMetaConfigDao.getByEa(ea) >> entity

        when:
        def result = marketingEventService.getOrCreateEventTypeAndColorConfig(ea, fsUserId)

        then:
        result.isSuccess()
        result.data.size() == 1
        result.data[0].eventType == "type1"
        result.data[0].color == "color1"
    }

    def "convert2FieldValueList - empty list"() {
        when:
        def result = marketingEventService.convert2FieldValueList([])

        then:
        result.isEmpty()
    }

    def "convert2FieldValueList - with valid data"() {
        given:
        def eventTypeAndColorDataList = [
            new EventTypeAndColorData("type1", "color1"),
            new EventTypeAndColorData("", "color2"), // empty eventType should be skipped
            new EventTypeAndColorData("type3", ""), // empty color should be skipped
            new EventTypeAndColorData("type4", "color4")
        ]

        when:
        def result = marketingEventService.convert2FieldValueList(eventTypeAndColorDataList)

        then:
        result.size() == 2
        result[0].name == "type1" && result[0].value == "color1"
        result[1].name == "type4" && result[1].value == "color4"
    }

    def "convert2EventTypeAndColorDataList - empty list"() {
        when:
        def result = marketingEventService.convert2EventTypeAndColorDataList(new FieldValueList())

        then:
        result.isEmpty()
    }

    def "convert2EventTypeAndColorDataList - with valid data"() {
        given:
        def fieldValueList = new FieldValueList()
        fieldValueList.add(new FieldValue("type1", "color1"))
        fieldValueList.add(new FieldValue("", "color2")) // empty name should be skipped
        fieldValueList.add(new FieldValue("type3", "")) // empty value should be skipped
        fieldValueList.add(new FieldValue("type4", "color4"))

        when:
        def result = marketingEventService.convert2EventTypeAndColorDataList(fieldValueList)

        then:
        result.size() == 2
        result[0].eventType == "type1" && result[0].color == "color1"
        result[1].eventType == "type4" && result[1].color == "color4"
    }

    def "updateIsApplyObject - success case"() {
        given:
        def relationId = "relation-123"
        def isApplyObject = true

        when:
        def result = marketingEventService.updateIsApplyObject(relationId, isApplyObject)

        then:
        result.isSuccess()
        1 * contentMarketingEventMaterialRelationDAO.updateIsApplyObject(relationId, isApplyObject)
    }

    def "listMaterialsByMarketingEvent - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new ListMaterialsByMarketingEventArg()
        arg.id = "event-123"
        arg.objectType = 1
        arg.title = "test title"
        arg.pageNum = 1
        arg.pageSize = 10
        def expectedResult = new PageResult<AbstractMaterialData>()

        marketingEventManager.listMaterialsByMarketingEvent(ea, arg.objectType, arg.id, arg.title, arg.pageNum, arg.pageSize) >> expectedResult

        when:
        def result = marketingEventService.listMaterialsByMarketingEvent(ea, fsUserId, arg)

        then:
        result == expectedResult
    }

    def "listPayOrdersByCampaignId - ea is null should throw exception"() {
        given:
        def arg = new ListPayOrdersByCampaignIdArg(campaignId: "campaign-123")

        when:
        marketingEventService.listPayOrdersByCampaignId(null, 12345, arg)

        then:
        thrown(IllegalArgumentException)
    }

    def "listPayOrdersByCampaignId - campaignId is null should throw exception"() {
        given:
        def arg = new ListPayOrdersByCampaignIdArg(campaignId: null)

        when:
        marketingEventService.listPayOrdersByCampaignId("test-ea", 12345, arg)

        then:
        thrown(IllegalArgumentException)
    }

    def "listPayOrdersByCampaignId - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new ListPayOrdersByCampaignIdArg(campaignId: "campaign-123")
        def payOrderIds = ["order-1", "order-2"]
        def payOrderEntity1 = new PayOrderEntity()
        payOrderEntity1.id = "order-1"
        payOrderEntity1.createTime = new Date(1000000L)
        def payOrderEntity2 = new PayOrderEntity()
        payOrderEntity2.id = "order-2"
        payOrderEntity2.createTime = new Date(2000000L)
        def payOrderMap = ["order-1": payOrderEntity1, "order-2": payOrderEntity2]

        campaignPayOrderDao.listPayOrderIdsByCampaignId(ea, arg.campaignId) >> payOrderIds
        payOrderManager.syncAndGetPayOrderByIds(payOrderIds) >> payOrderMap

        when:
        def result = marketingEventService.listPayOrdersByCampaignId(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data.size() == 2
        result.data[0].id == "order-1"
        result.data[0].createTime == 1000000L
        result.data[1].id == "order-2"
        result.data[1].createTime == 2000000L
    }

    def "queryMarketingEventLevelData - empty object data list"() {
        given:
        def vo = new QueryMarketingEventLevelDataVO()
        vo.ea = "test-ea"
        vo.marketingEventId = "event-123"

        crmV2Manager.getMarketingEventTreeByPath(vo.ea, "marketing_event_path", vo.marketingEventId, true) >> []

        when:
        def result = marketingEventService.queryMarketingEventLevelData(vo)

        then:
        result.isSuccess()
        result.data.dataList.isEmpty()
    }

    def "queryMarketingEventLevelData - success case"() {
        given:
        def vo = new QueryMarketingEventLevelDataVO()
        vo.ea = "test-ea"
        vo.marketingEventId = "event-123"

        def objectData1 = new ObjectData()
        objectData1.id = "obj-1"
        objectData1.name = "Object 1"
        objectData1.put("parent_id", "parent-1")

        def objectData2 = new ObjectData()
        objectData2.id = "obj-2"
        objectData2.name = "Object 2"
        objectData2.put("parent_id", "parent-2")

        crmV2Manager.getMarketingEventTreeByPath(vo.ea, "marketing_event_path", vo.marketingEventId, true) >> [objectData1, objectData2]

        when:
        def result = marketingEventService.queryMarketingEventLevelData(vo)

        then:
        result.isSuccess()
        result.data.dataList.size() == 2
        result.data.dataList[0].id == "obj-1"
        result.data.dataList[0].name == "Object 1"
        result.data.dataList[0].parentId == "parent-1"
        result.data.dataList[1].id == "obj-2"
        result.data.dataList[1].name == "Object 2"
        result.data.dataList[1].parentId == "parent-2"
    }

    def "relateSubMarketingEvent - check fails due to multivenue event type (covers checkSubMarketingEvent private method)"() {
        given:
        def vo = new RelateSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.fsUserId = 12345
        vo.marketingEventId = "event-123"
        vo.subMarketingEventIds = ["sub-1"]

        def marketingEventData = new MarketingEventData()
        marketingEventData.eventType = MarketingEventEnum.MULTIVENUE_MARKETING.getEventType()
        marketingEventData.parentId = null

        marketingEventManager.getMarketingEventData(vo.ea, vo.fsUserId, "sub-1") >> marketingEventData

        when:
        def result = marketingEventService.relateSubMarketingEvent(vo)

        then:
        !result.isSuccess()
        result.errorCode == SHErrorCode.MULTIVENUE_RELATE_SUB_ERROR.errorCode
    }

    def "relateSubMarketingEvent - check fails due to existing parent (covers checkSubMarketingEvent private method)"() {
        given:
        def vo = new RelateSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.fsUserId = 12345
        vo.marketingEventId = "event-123"
        vo.subMarketingEventIds = ["sub-1"]

        def marketingEventData = new MarketingEventData()
        marketingEventData.eventType = "normal_event"
        marketingEventData.parentId = "existing-parent-123"

        marketingEventManager.getMarketingEventData(vo.ea, vo.fsUserId, "sub-1") >> marketingEventData

        when:
        def result = marketingEventService.relateSubMarketingEvent(vo)

        then:
        !result.isSuccess()
        result.errorCode == SHErrorCode.MULTIVENUE_RELATE_SUB_ERROR.errorCode
    }

    def "relateSubMarketingEvent - check fails due to existing sub events (covers checkSubMarketingEvent private method)"() {
        given:
        def vo = new RelateSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.fsUserId = 12345
        vo.marketingEventId = "event-123"
        vo.subMarketingEventIds = ["sub-1"]

        def marketingEventData = new MarketingEventData()
        marketingEventData.eventType = "normal_event"
        marketingEventData.parentId = null

        def subEventData = new MarketingEventData()
        subEventData.id = "sub-sub-1"

        marketingEventManager.getMarketingEventData(vo.ea, vo.fsUserId, "sub-1") >> marketingEventData
        marketingEventManager.listMarketingEventDataByParentId(vo.ea, vo.fsUserId, "sub-1") >> [subEventData]

        when:
        def result = marketingEventService.relateSubMarketingEvent(vo)

        then:
        !result.isSuccess()
        result.errorCode == SHErrorCode.MULTIVENUE_RELATE_SUB_ERROR.errorCode
    }

    def "relateSubMarketingEvent - success case"() {
        given:
        def vo = new RelateSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.fsUserId = 12345
        vo.marketingEventId = "event-123"
        vo.subMarketingEventIds = ["sub-1"]

        def marketingEventData = new MarketingEventData()
        marketingEventData.eventType = "normal_event"
        marketingEventData.parentId = null

        marketingEventManager.getMarketingEventData(vo.ea, vo.fsUserId, "sub-1") >> marketingEventData
        marketingEventManager.listMarketingEventDataByParentId(vo.ea, vo.fsUserId, "sub-1") >> []

        when:
        def result = marketingEventService.relateSubMarketingEvent(vo)

        then:
        result.isSuccess()
    }

    def "unRelateSubMarketingEvent - success case"() {
        given:
        def vo = new UnRelateSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.marketingEventId = "event-123"

        when:
        def result = marketingEventService.unRelateSubMarketingEvent(vo)

        then:
        result.isSuccess()
        1 * marketingEventManager.updateParentIdToCrm(vo.ea, "", vo.marketingEventId)
    }

    def "getSubMarketingEvent - empty list"() {
        given:
        def vo = new GetSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.fsUserId = 12345
        vo.marketingEventId = "event-123"

        marketingEventManager.listMarketingEventDataByParentId(vo.ea, vo.fsUserId, vo.marketingEventId) >> []

        when:
        def result = marketingEventService.getSubMarketingEvent(vo)

        then:
        result.isSuccess()
        result.data == null
    }

    def "getSubMarketingEvent - success case"() {
        given:
        def vo = new GetSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.fsUserId = 12345
        vo.marketingEventId = "event-123"

        def marketingEventData1 = new MarketingEventData()
        marketingEventData1.id = "sub-1"
        marketingEventData1.name = "Sub Event 1"
        marketingEventData1.beginTime = 1000000L
        marketingEventData1.endTime = 1000000L
        marketingEventData1.eventType = "event_type_1"

        def marketingEventData2 = new MarketingEventData()
        marketingEventData2.id = "sub-2"
        marketingEventData2.name = "Sub Event 2"
        marketingEventData2.beginTime = 1000000L
        marketingEventData2.endTime = 1000000L
        marketingEventData2.eventType = "event_type_2"

        marketingEventManager.listMarketingEventDataByParentId(vo.ea, vo.fsUserId, vo.marketingEventId) >> [marketingEventData1, marketingEventData2]

        when:
        def result = marketingEventService.getSubMarketingEvent(vo)

        then:
        result.isSuccess()
        result.data.size() == 2
        result.data[0].id == "sub-1"
        result.data[0].name == "Sub Event 1"
        result.data[0].beginTime == 1000000L
        result.data[0].endTime == 1000000L
        result.data[0].eventType == "event_type_1"
        result.data[1].id == "sub-2"
        result.data[1].name == "Sub Event 2"
        result.data[1].beginTime == 1000000L
        result.data[1].endTime == 1000000L
        result.data[1].eventType == "event_type_2"
    }

    def "getMultiVenueMarketingEvent - success case"() {
        given:
        def vo = new GetMultiVenueMarketingEventVO()
        vo.ea = "test-ea"
        vo.fsUserId = 12345
        vo.marketingEventId = "event-123"
        def expectedResult = Result.newSuccess(new MultiVenueMarketingEventResult())

        marketingEventManager.getMultiVenueMarketingEventsDetail(vo.ea, vo.fsUserId, vo.marketingEventId) >> expectedResult

        when:
        def result = marketingEventService.getMultiVenueMarketingEvent(vo)

        then:
        result == expectedResult
    }

    def "syncDataToSubMarketingEvent - empty subMarketingEventIds"() {
        given:
        def vo = new SyncDataToSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.subMarketingEventIds = []
        vo.campaignIds = ["campaign-1"]

        when:
        def result = marketingEventService.syncDataToSubMarketingEvent(vo)

        then:
        result.isSuccess()
    }

    def "syncDataToSubMarketingEvent - success case (covers syncCampaignMember and buildSubCampaignMergeDataEntity private methods)"() {
        given:
        def vo = new SyncDataToSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.subMarketingEventIds = ["sub-1", "sub-2"]
        vo.campaignIds = ["campaign-1"]
        vo.auto = false

        def campaignMergeDataEntity = new CampaignMergeDataEntity()
        campaignMergeDataEntity.id = "campaign-1"
        campaignMergeDataEntity.campaignMembersObjId = "member-obj-1"
        campaignMergeDataEntity.bindCrmObjectType = 1
        campaignMergeDataEntity.bindCrmObjectId = "crm-obj-1"

        def uuid1 = "uuid-1"
        def uuid2 = "uuid-2"

        campaignMergeDataManager.listByIds(vo.ea, vo.campaignIds) >> [campaignMergeDataEntity]
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1).thenReturn(uuid2)
        crmV2Manager.addCampaignMembersObjWithoutLock(vo.ea, _, _, _, _) >> "new-member-id-1"

        when:
        def result = marketingEventService.syncDataToSubMarketingEvent(vo)

        then:
        result.isSuccess()
    }

    def "syncDataToSubMarketingEvent - auto=true case (covers autoCampaignMergeDataEntityToCampaignMergeObjMap private method)"() {
        given:
        def vo = new SyncDataToSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.subMarketingEventIds = ["sub-1"]
        vo.campaignIds = ["campaign-1"]
        vo.auto = true // This will trigger autoCampaignMergeDataEntityToCampaignMergeObjMap

        def campaignMergeDataEntity = new CampaignMergeDataEntity()
        campaignMergeDataEntity.id = "campaign-1"
        campaignMergeDataEntity.campaignMembersObjId = "member-obj-1"
        campaignMergeDataEntity.bindCrmObjectType = 1
        campaignMergeDataEntity.bindCrmObjectId = "crm-obj-1"
        campaignMergeDataEntity.marketingEventId = "sub-1"

        def uuid1 = "uuid-1"
        def enterpriseId = 123

        // Mock data for autoCampaignMergeDataEntityToCampaignMergeObjMap
        def campaignMemberObjectData = new ObjectData()
        campaignMemberObjectData.put("invitation_status", "invited")
        campaignMemberObjectData.put("participants_passcode", "12345")
        campaignMemberObjectData.put("sign_in_status", "signed")
        campaignMemberObjectData.put("custom_field", "custom_value")

        def fieldDescribe1 = new com.fxiaoke.crmrestapi.common.data.FieldDescribe()
        fieldDescribe1.apiName = "system_field"
        fieldDescribe1.defineType = "system"

        def fieldDescribe2 = new com.fxiaoke.crmrestapi.common.data.FieldDescribe()
        fieldDescribe2.apiName = "custom_field"
        fieldDescribe2.defineType = "custom"

        def describe = new com.fxiaoke.crmrestapi.common.data.ObjectDescribe()
        describe.fields = ["system_field": fieldDescribe1, "custom_field": fieldDescribe2]

        def controllerGetDescribeResult = new com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult()
        controllerGetDescribeResult.describe = describe

        def describeResult = new com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult>()
        describeResult.data = controllerGetDescribeResult

        def marketingEventObjectData = new ObjectData()
        marketingEventObjectData.put("event_type", MarketingEventEnum.MEETING_SALES.getEventType())

        def activityEntity = new ActivityEntity()
        activityEntity.enrollReview = true

        def bindObjectData = new ObjectData()
        bindObjectData.put("company", "Test Company")
        bindObjectData.name = "Test Name"

        campaignMergeDataManager.listByIds(vo.ea, vo.campaignIds) >> [campaignMergeDataEntity]
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1)
        eieaConverter.enterpriseAccountToId(vo.ea) >> enterpriseId

        // Mock calls for autoCampaignMergeDataEntityToCampaignMergeObjMap
        crmV2Manager.getDetail(vo.ea, -10000, com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), "member-obj-1") >> campaignMemberObjectData
        objectDescribeService.getDescribe(_, com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName()) >> describeResult
        crmV2Manager.getDetail(vo.ea, -10000, com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants.API_NAME, "sub-1") >> marketingEventObjectData
        conferenceDAO.getConferenceByMarketingEventId("sub-1", vo.ea) >> activityEntity

        // Mock calls for campaignMergeDataEntityToCampaignMergeObjMap
        crmV2Manager.getDetail(vo.ea, -10000, _, "crm-obj-1") >> bindObjectData
        crmV2Manager.addCampaignMembersObjWithoutLock(vo.ea, _, _, _, _) >> "new-member-id-1"

        when:
        def result = marketingEventService.syncDataToSubMarketingEvent(vo)

        then:
        result.isSuccess()
    }

    def "syncDataToSubMarketingEvent - auto=true with non-meeting event (covers autoCampaignMergeDataEntityToCampaignMergeObjMap approval_status removal)"() {
        given:
        def vo = new SyncDataToSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.subMarketingEventIds = ["sub-1"]
        vo.campaignIds = ["campaign-1"]
        vo.auto = true

        def campaignMergeDataEntity = new CampaignMergeDataEntity()
        campaignMergeDataEntity.id = "campaign-1"
        campaignMergeDataEntity.campaignMembersObjId = "member-obj-1"
        campaignMergeDataEntity.bindCrmObjectType = 1
        campaignMergeDataEntity.bindCrmObjectId = "crm-obj-1"
        campaignMergeDataEntity.marketingEventId = "sub-1"

        def uuid1 = "uuid-1"
        def enterpriseId = 123

        def campaignMemberObjectData = new ObjectData()
        campaignMemberObjectData.put("approval_status", "approved")

        def describe = new com.fxiaoke.crmrestapi.common.data.ObjectDescribe()
        describe.fields = [:]

        def controllerGetDescribeResult = new com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult()
        controllerGetDescribeResult.describe = describe

        def describeResult = new com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult>()
        describeResult.data = controllerGetDescribeResult

        def marketingEventObjectData = new ObjectData()
        marketingEventObjectData.put("event_type", "normal_event") // Not meeting event

        def bindObjectData = new ObjectData()
        bindObjectData.put("company", "Test Company")
        bindObjectData.name = "Test Name"

        campaignMergeDataManager.listByIds(vo.ea, vo.campaignIds) >> [campaignMergeDataEntity]
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1)
        eieaConverter.enterpriseAccountToId(vo.ea) >> enterpriseId

        crmV2Manager.getDetail(vo.ea, -10000, com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), "member-obj-1") >> campaignMemberObjectData
        objectDescribeService.getDescribe(_, com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName()) >> describeResult
        crmV2Manager.getDetail(vo.ea, -10000, com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants.API_NAME, "sub-1") >> marketingEventObjectData
        crmV2Manager.getDetail(vo.ea, -10000, _, "crm-obj-1") >> bindObjectData
        crmV2Manager.addCampaignMembersObjWithoutLock(vo.ea, _, _, _, _) >> "new-member-id-1"

        when:
        def result = marketingEventService.syncDataToSubMarketingEvent(vo)

        then:
        result.isSuccess()
    }

    def "syncDataToSubMarketingEvent - auto=true with exception (covers autoCampaignMergeDataEntityToCampaignMergeObjMap exception handling)"() {
        given:
        def vo = new SyncDataToSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.subMarketingEventIds = ["sub-1"]
        vo.campaignIds = ["campaign-1"]
        vo.auto = true

        def campaignMergeDataEntity = new CampaignMergeDataEntity()
        campaignMergeDataEntity.id = "campaign-1"
        campaignMergeDataEntity.campaignMembersObjId = "member-obj-1"
        campaignMergeDataEntity.bindCrmObjectType = 1
        campaignMergeDataEntity.bindCrmObjectId = "crm-obj-1"
        campaignMergeDataEntity.marketingEventId = "sub-1"

        def uuid1 = "uuid-1"

        def bindObjectData = new ObjectData()
        bindObjectData.put("company", "Test Company")
        bindObjectData.name = "Test Name"

        campaignMergeDataManager.listByIds(vo.ea, vo.campaignIds) >> [campaignMergeDataEntity]
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1)

        // Mock exception in autoCampaignMergeDataEntityToCampaignMergeObjMap
        crmV2Manager.getDetail(vo.ea, -10000, com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), "member-obj-1") >> { throw new RuntimeException("CRM error") }
        crmV2Manager.getDetail(vo.ea, -10000, _, "crm-obj-1") >> bindObjectData
        crmV2Manager.addCampaignMembersObjWithoutLock(vo.ea, _, _, _, _) >> "new-member-id-1"

        when:
        def result = marketingEventService.syncDataToSubMarketingEvent(vo)

        then:
        result.isSuccess() // Should still succeed despite exception in autoCampaignMergeDataEntityToCampaignMergeObjMap
    }

    def "syncDataToSubMarketingEvent - empty campaign entities (covers syncCampaignMember error case)"() {
        given:
        def vo = new SyncDataToSubMarketingEventVO()
        vo.ea = "test-ea"
        vo.subMarketingEventIds = ["sub-1"]
        vo.campaignIds = ["campaign-1"]
        vo.auto = false

        campaignMergeDataManager.listByIds(vo.ea, vo.campaignIds) >> []
        PowerMockito.when(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTSERVICEIMPL_712)).thenReturn("No campaign data found")

        when:
        def result = marketingEventService.syncDataToSubMarketingEvent(vo)

        then:
        !result.isSuccess()
        result.errorCode == SHErrorCode.PARAMS_ERROR.errorCode
        result.errorMessage == "No campaign data found"
    }

    def "syncDataToTargetMarketingEventByRule - success case (covers syncDataToTargetMarketingEventByRuleEntity private method)"() {
        given:
        def entity1 = new MarketingEventSyncRuleEntity()
        entity1.ea = "test-ea"
        entity1.marketingEventId = "event-123"
        entity1.targetMarketingEvents = '[{"marketingEventId":"target-1"}]'
        entity1.ruleGroupJson = new RuleGroupList()

        def entity2 = new MarketingEventSyncRuleEntity()
        entity2.ea = "test-ea2"
        entity2.marketingEventId = "event-456"
        entity2.targetMarketingEvents = '[{"marketingEventId":"target-2"}]'
        entity2.ruleGroupJson = new RuleGroupList()

        def targetMarketingEvent1 = new SaveSyncRulesArg.TargetMarketingEvent()
        targetMarketingEvent1.marketingEventId = "target-1"

        def targetMarketingEvent2 = new SaveSyncRulesArg.TargetMarketingEvent()
        targetMarketingEvent2.marketingEventId = "target-2"

        def objectData1 = new ObjectData()
        objectData1.put("_id", "campaign-obj-1")

        def objectData2 = new ObjectData()
        objectData2.put("_id", "campaign-obj-2")

        def innerPage1 = new InnerPage<ObjectData>()
        innerPage1.dataList = [objectData1]

        def innerPage2 = new InnerPage<ObjectData>()
        innerPage2.dataList = [objectData2]

        def campaignMergeDataEntity1 = new CampaignMergeDataEntity()
        campaignMergeDataEntity1.id = "campaign-1"
        campaignMergeDataEntity1.campaignMembersObjId = "member-obj-1"

        def campaignMergeDataEntity2 = new CampaignMergeDataEntity()
        campaignMergeDataEntity2.id = "campaign-2"
        campaignMergeDataEntity2.campaignMembersObjId = "member-obj-2"

        marketingEventSyncRuleDao.queryAll() >> [entity1, entity2]
        PowerMockito.when(JSON.parseArray(entity1.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([targetMarketingEvent1])
        PowerMockito.when(JSON.parseArray(entity2.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([targetMarketingEvent2])

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        crmV2Manager.concurrentListCrmObjectByFilterV3("test-ea", -10000, _) >> innerPage1
        crmV2Manager.concurrentListCrmObjectByFilterV3("test-ea2", -10000, _) >> innerPage2
        campaignMergeDataDAO.getCampaignMergeDataByCampaignMembersObjIds("test-ea", "event-123", ["campaign-obj-1"]) >> [campaignMergeDataEntity1]
        campaignMergeDataDAO.getCampaignMergeDataByCampaignMembersObjIds("test-ea2", "event-456", ["campaign-obj-2"]) >> [campaignMergeDataEntity2]

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventByRule()

        then:
        result.isSuccess()
    }

    def "syncDataToTargetMarketingEventByRule - empty entities"() {
        given:
        marketingEventSyncRuleDao.queryAll() >> []

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventByRule()

        then:
        result.isSuccess()
    }

    def "syncDataToTargetMarketingEventById - success case (covers syncDataToTargetMarketingEventByRuleEntity with campaignObjId)"() {
        given:
        def ea = "test-ea"
        def marketingEventId = "event-123"
        def campaignObjId = "campaign-obj-123"

        def entity = new MarketingEventSyncRuleEntity()
        entity.ea = ea
        entity.marketingEventId = marketingEventId
        entity.targetMarketingEvents = '[{"marketingEventId":"target-1"}]'
        entity.ruleGroupJson = new RuleGroupList()

        def targetMarketingEvent = new SaveSyncRulesArg.TargetMarketingEvent()
        targetMarketingEvent.marketingEventId = "target-1"

        def objectData = new ObjectData()
        objectData.put("_id", campaignObjId)

        def innerPage = new InnerPage<ObjectData>()
        innerPage.dataList = [objectData]

        def campaignMergeDataEntity = new CampaignMergeDataEntity()
        campaignMergeDataEntity.id = "campaign-1"
        campaignMergeDataEntity.campaignMembersObjId = "member-obj-1"

        marketingEventSyncRuleDao.queryAllByMarketingEventId(ea, marketingEventId) >> [entity]
        PowerMockito.when(JSON.parseArray(entity.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([targetMarketingEvent])

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, _) >> innerPage
        campaignMergeDataDAO.getCampaignMergeDataByCampaignMembersObjIds(ea, marketingEventId, [campaignObjId]) >> [campaignMergeDataEntity]

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventById(ea, marketingEventId, campaignObjId)

        then:
        result.isSuccess()
    }

    def "syncDataToTargetMarketingEventByRuleEntity - empty target marketing events"() {
        given:
        def entity = new MarketingEventSyncRuleEntity()
        entity.ea = "test-ea"
        entity.marketingEventId = "event-123"
        entity.targetMarketingEvents = '[]' // Empty target events
        entity.ruleGroupJson = new RuleGroupList()

        marketingEventSyncRuleDao.queryAll() >> [entity]
        PowerMockito.when(JSON.parseArray(entity.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([])

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventByRule()

        then:
        result.isSuccess()
        // Should not call crmV2Manager when target events are empty
        0 * crmV2Manager.concurrentListCrmObjectByFilterV3(_, _, _)
    }

    def "syncDataToTargetMarketingEventByRuleEntity - empty object data"() {
        given:
        def entity = new MarketingEventSyncRuleEntity()
        entity.ea = "test-ea"
        entity.marketingEventId = "event-123"
        entity.targetMarketingEvents = '[{"marketingEventId":"target-1"}]'
        entity.ruleGroupJson = new RuleGroupList()

        def targetMarketingEvent = new SaveSyncRulesArg.TargetMarketingEvent()
        targetMarketingEvent.marketingEventId = "target-1"

        def innerPage = new InnerPage<ObjectData>()
        innerPage.dataList = [] // Empty data list

        marketingEventSyncRuleDao.queryAll() >> [entity]
        PowerMockito.when(JSON.parseArray(entity.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([targetMarketingEvent])

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        crmV2Manager.concurrentListCrmObjectByFilterV3("test-ea", -10000, _) >> innerPage

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventByRule()

        then:
        result.isSuccess()
        // Should not call campaignMergeDataDAO when object data is empty
        0 * campaignMergeDataDAO.getCampaignMergeDataByCampaignMembersObjIds(_, _, _)
    }

    def "syncDataToTargetMarketingEventByRuleEntity - empty campaign merge data"() {
        given:
        def entity = new MarketingEventSyncRuleEntity()
        entity.ea = "test-ea"
        entity.marketingEventId = "event-123"
        entity.targetMarketingEvents = '[{"marketingEventId":"target-1"}]'
        entity.ruleGroupJson = new RuleGroupList()

        def targetMarketingEvent = new SaveSyncRulesArg.TargetMarketingEvent()
        targetMarketingEvent.marketingEventId = "target-1"

        def objectData = new ObjectData()
        objectData.put("_id", "campaign-obj-1")

        def innerPage = new InnerPage<ObjectData>()
        innerPage.dataList = [objectData]

        marketingEventSyncRuleDao.queryAll() >> [entity]
        PowerMockito.when(JSON.parseArray(entity.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([targetMarketingEvent])

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        crmV2Manager.concurrentListCrmObjectByFilterV3("test-ea", -10000, _) >> innerPage
        campaignMergeDataDAO.getCampaignMergeDataByCampaignMembersObjIds("test-ea", "event-123", ["campaign-obj-1"]) >> [] // Empty campaign data

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventByRule()

        then:
        result.isSuccess()
    }

    def "syncDataToTargetMarketingEventByRuleEntity - exception handling"() {
        given:
        def entity = new MarketingEventSyncRuleEntity()
        entity.ea = "test-ea"
        entity.marketingEventId = "event-123"
        entity.targetMarketingEvents = '[{"marketingEventId":"target-1"}]'
        entity.ruleGroupJson = new RuleGroupList()

        marketingEventSyncRuleDao.queryAll() >> [entity]
        PowerMockito.when(JSON.parseArray(entity.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenThrow(new RuntimeException("JSON parse error"))

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventByRule()

        then:
        result.isSuccess() // Should still succeed despite exception in private method
    }

    def "syncDataToTargetMarketingEventByRuleEntity - null paasQueryFilterArg (covers paasQueryFilterArg == null branch)"() {
        given:
        def entity = new MarketingEventSyncRuleEntity()
        entity.ea = "test-ea"
        entity.marketingEventId = "event-123"
        entity.targetMarketingEvents = '[{"marketingEventId":"target-1"}]'

        // Create a RuleGroupList that will result in null PaasQueryFilterArg after BeanUtil.copyByGson
        // This happens when the RuleGroup contains invalid data that can't be converted to PaasQueryFilterArg
        def ruleGroup = new RuleGroup()
        ruleGroup.objectAPIName = null // Invalid data that will cause BeanUtil.copyByGson to return null
        ruleGroup.query = null

        def ruleGroupList = new RuleGroupList()
        ruleGroupList.add(ruleGroup) // This will cause BeanUtil.copyByGson to return null, making args.get(0) null
        entity.ruleGroupJson = ruleGroupList

        def targetMarketingEvent = new SaveSyncRulesArg.TargetMarketingEvent()
        targetMarketingEvent.marketingEventId = "target-1"

        def objectData = new ObjectData()
        objectData.put("_id", "campaign-obj-1")

        def innerPage = new InnerPage<ObjectData>()
        innerPage.dataList = [objectData]

        def campaignMergeDataEntity = new CampaignMergeDataEntity()
        campaignMergeDataEntity.id = "campaign-1"
        campaignMergeDataEntity.campaignMembersObjId = "member-obj-1"

        marketingEventSyncRuleDao.queryAll() >> [entity]
        PowerMockito.when(JSON.parseArray(entity.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([targetMarketingEvent])

        // Mock ThreadPoolUtils to execute synchronously
        PowerMockito.when(ThreadPoolUtils.execute(Matchers.any(Runnable.class), Matchers.any())).then { invocation ->
            def runnable = invocation.arguments[0] as Runnable
            runnable.run() // Execute synchronously
            return null
        }

        crmV2Manager.concurrentListCrmObjectByFilterV3("test-ea", -10000, { PaasQueryFilterArg arg ->
            // Verify that a new PaasQueryFilterArg was created with default PaasQueryArg(0, 1)
            arg.query != null && arg.query.offset == 0 && arg.query.limit == 1
        }) >> innerPage
        campaignMergeDataDAO.getCampaignMergeDataByCampaignMembersObjIds("test-ea", "event-123", ["campaign-obj-1"]) >> [campaignMergeDataEntity]

        when:
        def result = marketingEventService.syncDataToTargetMarketingEventByRule()

        then:
        result.isSuccess()
        // This test covers the paasQueryFilterArg == null branch where a new PaasQueryFilterArg is created
        // with new PaasQueryArg(0, 1) as specified in lines 569-570 of the source code
    }

    def "getSyncRules - empty entities"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new IdArg(id: "event-123")

        marketingEventSyncRuleDao.queryAllByMarketingEventId(ea, arg.id) >> []

        when:
        def result = marketingEventService.getSyncRules(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data.isEmpty()
    }

    def "getSyncRules - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new IdArg(id: "event-123")

        def entity = new MarketingEventSyncRuleEntity()
        entity.targetMarketingEvents = '[{"marketingEventId":"target-1"}]'
        entity.ruleGroupJson = new RuleGroupList()

        marketingEventSyncRuleDao.queryAllByMarketingEventId(ea, arg.id) >> [entity]
        PowerMockito.when(JSON.parseArray(entity.targetMarketingEvents, SaveSyncRulesArg.TargetMarketingEvent.class))
                .thenReturn([new SaveSyncRulesArg.TargetMarketingEvent(marketingEventId: "target-1")])

        when:
        def result = marketingEventService.getSyncRules(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data.size() == 1
        result.data[0].targetMarketingEvents.size() == 1
        result.data[0].targetMarketingEvents[0].marketingEventId == "target-1"
    }

    def "saveSyncRules - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new SaveSyncRulesArg()
        arg.marketingEventId = "event-123"
        def syncRule = new SaveSyncRulesArg.SyncRule()
        syncRule.targetMarketingEvents = [new SaveSyncRulesArg.TargetMarketingEvent(marketingEventId: "target-1")]
        syncRule.ruleGroupJson = new RuleGroupList()
        arg.rules = [syncRule]
        def uuid = "test-uuid"

        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid)
        PowerMockito.when(JSON.toJSONString(syncRule.targetMarketingEvents)).thenReturn('[{"marketingEventId":"target-1"}]')

        when:
        def result = marketingEventService.saveSyncRules(ea, fsUserId, arg)

        then:
        result.isSuccess()
        1 * marketingEventSyncRuleDao.deleteAllByMarketingEventId(ea, arg.marketingEventId)
        1 * marketingEventSyncRuleDao.insert(uuid, ea, arg.marketingEventId, '[{"marketingEventId":"target-1"}]', syncRule.ruleGroupJson, fsUserId)
    }

    def "listMultiVenueMarketingEvent - ea is blank"() {
        given:
        def arg = new ListMultiVenueMarketingEventArg()
        arg.ea = ""

        when:
        def result = marketingEventService.listMultiVenueMarketingEvent(arg)

        then:
        !result.isSuccess()
        result.errorCode == SHErrorCode.PARAMS_ERROR.errorCode
    }

    def "listMultiVenueMarketingEvent - success case"() {
        given:
        def arg = new ListMultiVenueMarketingEventArg()
        arg.ea = "test-ea"
        arg.pageNo = 1
        arg.pageSize = 10
        arg.keyword = "test"
        def enterpriseId = 123

        def briefResult = new MarketingEventsBriefResult()
        briefResult.id = "event-1"
        briefResult.name = "Event 1"
        briefResult.eventType = "multivenue_marketing"
        briefResult.beginTime = 1000000L
        briefResult.endTime = 1000000L
        briefResult.cover = [[path: "/cover/path"]]

        def pageResult = new com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult>()
        pageResult.data = [briefResult]
        pageResult.totalCount = 1

        eieaConverter.enterpriseAccountToId(arg.ea) >> enterpriseId
        marketingEventManager.listMarketingEventsByPager(enterpriseId, -10000, _, _) >> pageResult
        fileV2Manager.batchGetUrlByPath(_, arg.ea, false) >> ["/cover/path": "http://cover.url", "/default/cover/path": "http://default.url"]

        when:
        def result = marketingEventService.listMultiVenueMarketingEvent(arg)

        then:
        result.isSuccess()
        result.data.result.size() == 1
        result.data.result[0].marketingEventId == "event-1"
        result.data.result[0].title == "Event 1"
        result.data.result[0].eventType == "multivenue_marketing"
        result.data.result[0].startTime == 1000000L
        result.data.result[0].endTime == 1000000L
        result.data.result[0].coverUrl == "http://cover.url"
        result.data.totalCount == 1
        result.data.pageNum == 1
        result.data.pageSize == 10
    }

    def "getMarketingEventByObjectId - success case"() {
        given:
        def ea = "test-ea"
        def fsUserId = 12345
        def arg = new GetMarketingEventByObjectIdArg()
        arg.objectType = 1
        arg.objectId = "obj-123"
        arg.pageNum = 1
        arg.pageSize = 10
        arg.time = 1000000L

        def relationEntity = new ContentMarketingEventMaterialRelationEntity()
        relationEntity.marketingEventId = "event-1"

        def objectData = new ObjectData()
        objectData.id = "event-1"
        objectData.name = "Event 1"
        objectData.put("event_type", "meeting")
        objectData.put("biz_status", "active")
        objectData.put("create_by", 12345)

        def crmPage = new com.fxiaoke.crmrestapi.common.data.Page<ObjectData>()
        crmPage.dataList = [objectData]

        def fsEmployeeMsg = new FsAddressBookManager.FSEmployeeMsg()
        fsEmployeeMsg.name = "Creator Name"

        contentMarketingEventMaterialRelationDAO.getByEaAndObjectTypeAndObjectId(ea, arg.objectType, arg.objectId, _) >> [relationEntity]
        crmV2Manager.countByFilters(ea, -10000, _, _) >> 1
        crmV2Manager.getList(ea, -10000, _, _) >> crmPage
        fsAddressBookManager.getEmployeeInfoByUserIds(ea, [12345], true) >> [12345: fsEmployeeMsg]

        when:
        def result = marketingEventService.getMarketingEventByObjectId(ea, fsUserId, arg)

        then:
        result.isSuccess()
        result.data.result.size() == 1
        result.data.result[0].marketingEventId == "event-1"
        result.data.result[0].marketingEventName == "Event 1"
        result.data.result[0].marketingEventType == "meeting"
        result.data.result[0].marketingEventBizStatus == "active"
        result.data.result[0].creator == "Creator Name"
        result.data.pageNum == 1
        result.data.pageSize == 10
        result.data.time == 1000000L
        result.data.totalCount == 1
    }

    def "handleSpecialMaterial - customize form type"() {
        given:
        def objectId = "form-123"
        def objectType = ObjectTypeEnum.CUSTOMIZE_FORM.getType()
        def marketingEventId = "event-123"
        def ea = "test-ea"

        when:
        // Use reflection to call private method
        def method = MarketingEventServiceImpl.class.getDeclaredMethod("handleSpecialMaterial", String.class, Integer.class, String.class, String.class)
        method.setAccessible(true)
        method.invoke(marketingEventService, objectId, objectType, marketingEventId, ea)

        then:
        1 * customizeFormDataManager.createCustomizeFormDataQRCode(objectId, marketingEventId, ea)
    }

    def "handleSpecialMaterial - non customize form type"() {
        given:
        def objectId = "obj-123"
        def objectType = 999 // Not customize form type
        def marketingEventId = "event-123"
        def ea = "test-ea"

        when:
        // Use reflection to call private method
        def method = MarketingEventServiceImpl.class.getDeclaredMethod("handleSpecialMaterial", String.class, Integer.class, String.class, String.class)
        method.setAccessible(true)
        method.invoke(marketingEventService, objectId, objectType, marketingEventId, ea)

        then:
        0 * customizeFormDataManager.createCustomizeFormDataQRCode(_, _, _)
    }

    def "getDistinctEntities - test private method"() {
        given:
        def ea = "test-ea"
        def entity1 = new ContentMarketingEventMaterialRelationEntity()
        entity1.ea = ea
        entity1.objectType = 1
        entity1.objectId = "obj-1"
        entity1.marketingEventId = "event-1"

        def entity2 = new ContentMarketingEventMaterialRelationEntity()
        entity2.ea = ea
        entity2.objectType = 2
        entity2.objectId = "obj-2"
        entity2.marketingEventId = "event-1"

        def existingEntity = new ContentMarketingEventMaterialRelationEntity()
        existingEntity.ea = ea
        existingEntity.objectType = 1
        existingEntity.objectId = "obj-1"

        contentMarketingEventMaterialRelationDAO.batchGetByEaAndObjectInfos(ea, _) >> [existingEntity]

        when:
        // Use reflection to call private method
        def method = MarketingEventServiceImpl.class.getDeclaredMethod("getDistinctEntities", String.class, List.class)
        method.setAccessible(true)
        def result = method.invoke(marketingEventService, ea, [entity1, entity2])

        then:
        result.size() == 1
        result[0].objectId == "obj-2"
    }

    def "convert - test private method"() {
        given:
        def entity1 = new ContentMarketingEventMaterialRelationEntity()
        entity1.objectType = 1
        entity1.objectId = "obj-1"
        entity1.marketingEventId = "event-1"

        def entity2 = new ContentMarketingEventMaterialRelationEntity()
        entity2.objectType = 2
        entity2.objectId = "obj-2"
        entity2.marketingEventId = "event-1"

        when:
        // Use reflection to call private method
        def method = MarketingEventServiceImpl.class.getDeclaredMethod("convert", List.class)
        method.setAccessible(true)
        def result = method.invoke(marketingEventService, [entity1, entity2])

        then:
        result.size() == 2
        result[0].objectType == 1
        result[0].objectId == "obj-1"
        result[0].marketingEventId == "event-1"
        result[1].objectType == 2
        result[1].objectId == "obj-2"
        result[1].marketingEventId == "event-1"
    }
}
