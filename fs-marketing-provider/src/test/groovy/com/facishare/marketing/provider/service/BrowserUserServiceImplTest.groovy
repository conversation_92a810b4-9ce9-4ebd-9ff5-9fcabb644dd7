package com.facishare.marketing.provider.service

import com.alibaba.fastjson.JSONObject
import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.RecordUtmParamArg
import com.facishare.marketing.common.enums.I18nKeyEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.util.I18nUtil
import com.facishare.marketing.common.util.UUIDUtil
import com.facishare.marketing.provider.dao.BrowserUserDao
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO
import com.facishare.marketing.provider.dao.UserMarketingBrowserUserRelationDao
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import java.nio.charset.StandardCharsets

/**
 * Test for BrowserUserServiceImpl
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([UUIDUtil.class, I18nUtil.class, JSONObject.class, StringUtils.class])
@PowerMockIgnore("javax.net.ssl.*")
class BrowserUserServiceImplTest extends Specification {

    def browserUserService = new BrowserUserServiceImpl()

    // Mock all dependencies
    def browserUserDao = Mock(BrowserUserDao)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def eieaConverter = Mock(EIEAConverter)
    def browserUserRelationManager = Mock(BrowserUserRelationManager)
    def redisManager = Mock(RedisManager)

    def setup() {
        // Inject mocked dependencies
        browserUserService.browserUserDao = browserUserDao
        browserUserService.userMarketingBrowserUserRelationDao = userMarketingBrowserUserRelationDao
        browserUserService.userMarketingAccountDAO = userMarketingAccountDAO
        browserUserService.eieaConverter = eieaConverter
        browserUserService.browserUserRelationManager = browserUserRelationManager
        browserUserService.redisManager = redisManager
        browserUserService.userAgentForbidTokenList = '["bot", "crawler"]'

        // Mock static methods
        PowerMockito.mockStatic(UUIDUtil.class)
        PowerMockito.mockStatic(I18nUtil.class)
        PowerMockito.mockStatic(JSONObject.class)
        PowerMockito.mockStatic(StringUtils.class)
    }

    @Unroll
    def "getIdentity - userAgent: '#userAgentDesc', should return error: #shouldReturnError"() {
        given:
        def userAgent = userAgentValue
        def forbidTokenList = ["bot", "crawler"]
        def uuid = "test-uuid"
        def encodedUserAgent = userAgent ?: userAgent

        PowerMockito.when(JSONObject.parseArray(browserUserService.userAgentForbidTokenList, String.class))
                .thenReturn(forbidTokenList)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid)
        PowerMockito.when(StringUtils.toEncodedString(_, _)).thenReturn(encodedUserAgent)

        when:
        def result = browserUserService.getIdentity(userAgent)

        then:
        if (shouldReturnError) {
            assert !result.isSuccess()
            assert result.errorCode == SHErrorCode.PARAMS_ERROR.errorCode
            0 * browserUserDao.insert(_, _)
        } else {
            assert result.isSuccess()
            assert result.data == uuid
            1 * browserUserDao.insert(uuid, encodedUserAgent)
        }

        where:
        userAgentValue                                    | userAgentDesc              | shouldReturnError
        null                                             | "null"                     | false
        "normal user agent"                              | "normal"                   | false
        "a" * 2049                                       | "too long"                 | true
        "Mozilla/5.0 bot"                               | "contains bot"             | true
        "Mozilla/5.0 crawler"                           | "contains crawler"         | true
        "Mozilla/5.0 /etc/passwd"                       | "contains /etc"            | true
        "Mozilla/5.0 ../"                               | "contains ../"             | true
        "Mozilla/5.0 ..\\"                              | "contains ..\\"            | true
    }

    def "createIdentityByUserMarketingId - userMarketingId is null should throw exception"() {
        given:
        def ua = "test user agent"
        def userMarketingId = null

        when:
        browserUserService.createIdentityByUserMarketingId(ua, userMarketingId)

        then:
        thrown(IllegalArgumentException)
    }

    def "createIdentityByUserMarketingId - userMarketingId is empty should throw exception"() {
        given:
        def ua = "test user agent"
        def userMarketingId = ""

        when:
        browserUserService.createIdentityByUserMarketingId(ua, userMarketingId)

        then:
        thrown(IllegalArgumentException)
    }

    def "createIdentityByUserMarketingId - userMarketingAccount not exists should throw exception"() {
        given:
        def ua = "test user agent"
        def userMarketingId = "valid-id"

        PowerMockito.when(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BROWSERUSERSERVICEIMPL_68))
                .thenReturn("User marketing account not found")

        userMarketingAccountDAO.getById(userMarketingId) >> null

        when:
        browserUserService.createIdentityByUserMarketingId(ua, userMarketingId)

        then:
        thrown(IllegalArgumentException)
    }

    def "createIdentityByUserMarketingId - success case"() {
        given:
        def ua = "test user agent"
        def userMarketingId = "valid-id"
        def uuid1 = "test-uuid-1"
        def uuid2 = "test-uuid-2"
        def ea = "test-ea"
        def tenantId = 12345

        def userMarketingAccount = new UserMarketingAccountEntity(tenantId: tenantId)

        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid1).thenReturn(uuid2)
        PowerMockito.when(StringUtils.toEncodedString(ua.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8))
                .thenReturn(ua)

        userMarketingAccountDAO.getById(userMarketingId) >> userMarketingAccount
        eieaConverter.enterpriseIdToAccount(tenantId) >> ea

        when:
        def result = browserUserService.createIdentityByUserMarketingId(ua, userMarketingId)

        then:
        assert result.isSuccess()
        assert result.data == uuid1
        1 * browserUserDao.insert(uuid1, ua)
        1 * userMarketingBrowserUserRelationDao.insertIgnore(uuid2, ea, userMarketingId, uuid1)
    }

    def "bindPhoneWithBrowserUserId - ea is null should throw exception"() {
        when:
        browserUserService.bindPhoneWithBrowserUserId(null, "browser-id", "phone")

        then:
        thrown(IllegalArgumentException)
    }

    def "bindPhoneWithBrowserUserId - ea is empty should throw exception"() {
        when:
        browserUserService.bindPhoneWithBrowserUserId("", "browser-id", "phone")

        then:
        thrown(IllegalArgumentException)
    }

    def "bindPhoneWithBrowserUserId - browserUserId is null should throw exception"() {
        when:
        browserUserService.bindPhoneWithBrowserUserId("test-ea", null, "phone")

        then:
        thrown(IllegalArgumentException)
    }

    def "bindPhoneWithBrowserUserId - browserUserId is empty should throw exception"() {
        when:
        browserUserService.bindPhoneWithBrowserUserId("test-ea", "", "phone")

        then:
        thrown(IllegalArgumentException)
    }

    def "bindPhoneWithBrowserUserId - phone is null should throw exception"() {
        when:
        browserUserService.bindPhoneWithBrowserUserId("test-ea", "browser-id", null)

        then:
        thrown(IllegalArgumentException)
    }

    def "bindPhoneWithBrowserUserId - phone is empty should throw exception"() {
        when:
        browserUserService.bindPhoneWithBrowserUserId("test-ea", "browser-id", "")

        then:
        thrown(IllegalArgumentException)
    }

    def "bindPhoneWithBrowserUserId - success case"() {
        given:
        def ea = "test-ea"
        def browserUserId = "browser-id"
        def phone = "phone"
        def bindResult = true

        browserUserRelationManager.bindPhoneWithBrowserUserId(ea, browserUserId, phone) >> bindResult

        when:
        def result = browserUserService.bindPhoneWithBrowserUserId(ea, browserUserId, phone)

        then:
        assert result.isSuccess()
        assert result.data == bindResult
    }



    @Unroll
    def "toEncodedString - ua: '#ua', encoding exception: #encodingException"() {
        given:
        def method = BrowserUserServiceImpl.class.getDeclaredMethod("toEncodedString", String.class)
        method.setAccessible(true)

        PowerMockito.when(StringUtils.toEncodedString(_, _)).then { invocation ->
            if (encodingException) {
                throw new RuntimeException("Encoding error")
            }
            return "encoded-" + new String(invocation.arguments[0] as byte[])
        }

        when:
        def result = method.invoke(browserUserService, ua)

        then:
        if (ua == null || ua.isEmpty()) {
            assert result == ua
        } else if (encodingException) {
            assert result == ua  // Should return original string on exception
        } else {
            assert result.startsWith("encoded-")
        }

        where:
        ua              | encodingException
        null            | false
        ""              | false
        "test ua"       | false
        "test ua"       | true
    }

    @Unroll
    def "getIdentity - integration test with toEncodedString - ua: '#ua'"() {
        given:
        def forbidTokenList = []
        def uuid = "test-uuid"

        PowerMockito.when(JSONObject.parseArray(browserUserService.userAgentForbidTokenList, String.class))
                .thenReturn(forbidTokenList)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid)
        PowerMockito.when(StringUtils.toEncodedString(_, _)).then { invocation ->
            return "encoded-" + new String(invocation.arguments[0] as byte[])
        }

        when:
        def result = browserUserService.getIdentity(ua)

        then:
        assert result.isSuccess()
        assert result.data == uuid
        1 * browserUserDao.insert(uuid, expectedEncodedValue)

        where:
        ua              | expectedEncodedValue
        null            | null
        ""              | ""
        "test ua"       | "encoded-test ua"
    }

    def "getIdentity - userAgent exactly 2048 characters should succeed"() {
        given:
        def userAgent = "a" * 2048
        def forbidTokenList = []
        def uuid = "test-uuid"

        PowerMockito.when(JSONObject.parseArray(browserUserService.userAgentForbidTokenList, String.class))
                .thenReturn(forbidTokenList)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn(uuid)
        PowerMockito.when(StringUtils.toEncodedString(_, _)).thenReturn(userAgent)

        when:
        def result = browserUserService.getIdentity(userAgent)

        then:
        assert result.isSuccess()
        assert result.data == uuid
        1 * browserUserDao.insert(uuid, userAgent)
    }

    def "recordUtmParam - arg is null should return null"() {
        when:
        def result = browserUserService.recordUtmParam(null)

        then:
        result == null
        0 * redisManager.setUtmParam(_, _)
    }

    def "recordUtmParam - arg with valid checkParam should call redisManager"() {
        given:
        def arg = new RecordUtmParamArg()
        arg.visitorId = "visitor-123"
        arg.ea = "test-ea"

        when:
        def result = browserUserService.recordUtmParam(arg)

        then:
        result == null
        1 * redisManager.setUtmParam("visitor-123", arg)
    }

    def "recordUtmParam - arg with invalid checkParam should not call redisManager"() {
        given:
        def arg = new RecordUtmParamArg()
        // Missing required fields to make checkParam return false

        when:
        def result = browserUserService.recordUtmParam(arg)

        then:
        result == null
        0 * redisManager.setUtmParam(_, _)
    }
}
