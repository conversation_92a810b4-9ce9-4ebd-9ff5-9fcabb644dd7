package com.facishare.marketing.provider.service

import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.typehandlers.value.ChannelData
import com.facishare.marketing.common.util.UUIDUtil
import com.facishare.marketing.outapi.arg.report.EnterpriseAmountStatisticReportArg
import com.facishare.marketing.outapi.arg.report.EnterpriseDayStatisticReportArg
import com.facishare.marketing.outapi.arg.report.EnterpriseEmployeeAmountStatisticReportArg
import com.facishare.marketing.outapi.arg.report.EnterpriseEmployeeDayStatisticReportArg
import com.facishare.marketing.outapi.arg.report.EnterpriseEmployeeObjectAmountStatisticReportArg
import com.facishare.marketing.outapi.arg.report.EnterpriseEmployeeObjectDayStatisticReportArg
import com.facishare.marketing.outapi.arg.report.EnterpriseObjectAmountStatisticReportArg
import com.facishare.marketing.outapi.arg.report.EnterpriseObjectDayStatisticReportArg
import com.facishare.marketing.provider.dao.EnterpriseAmountStatisticDao
import com.facishare.marketing.provider.dao.EnterpriseDayStatisticDao
import com.facishare.marketing.provider.dao.EnterpriseEmployeeAmountStatisticDao
import com.facishare.marketing.provider.dao.EnterpriseEmployeeDayStatisticDao
import com.facishare.marketing.provider.dao.EnterpriseEmployeeObjectAmountStatisticDao
import com.facishare.marketing.provider.dao.EnterpriseEmployeeObjectDayStatisticDao
import com.facishare.marketing.provider.dao.EnterpriseObjectAmountStatisticDao
import com.facishare.marketing.provider.dao.EnterpriseObjectDayStatisticDao
import com.facishare.marketing.provider.entity.EnterpriseAmountStatisticEntity
import com.facishare.marketing.provider.entity.EnterpriseDayStatisticEntity
import com.facishare.marketing.provider.entity.EnterpriseEmployeeAmountStatisticEntity
import com.facishare.marketing.provider.entity.EnterpriseEmployeeDayStatisticEntity
import com.facishare.marketing.provider.entity.EnterpriseEmployeeObjectAmountStatisticEntity
import com.facishare.marketing.provider.entity.EnterpriseEmployeeObjectDayStatisticEntity
import com.facishare.marketing.provider.entity.EnterpriseObjectAmountStatisticEntity
import com.facishare.marketing.provider.entity.EnterpriseObjectDayStatisticEntity
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import java.util.Date

/**
 * Test for EnterpriseStatisticDataReportServiceImpl
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([ChannelData.class, UUIDUtil.class])
@PowerMockIgnore("javax.net.ssl.*")
class EnterpriseStatisticDataReportServiceImplTest extends Specification {

    def enterpriseStatisticDataReportService = new EnterpriseStatisticDataReportServiceImpl()

    // Mock all DAO dependencies
    def enterpriseAmountStatisticDao = Mock(EnterpriseAmountStatisticDao)
    def enterpriseDayStatisticDao = Mock(EnterpriseDayStatisticDao)
    def enterpriseEmployeeAmountStatisticDao = Mock(EnterpriseEmployeeAmountStatisticDao)
    def enterpriseEmployeeDayStatisticDao = Mock(EnterpriseEmployeeDayStatisticDao)
    def enterpriseEmployeeObjectAmountStatisticDao = Mock(EnterpriseEmployeeObjectAmountStatisticDao)
    def enterpriseEmployeeObjectDayStatisticDao = Mock(EnterpriseEmployeeObjectDayStatisticDao)
    def enterpriseObjectAmountStatisticDao = Mock(EnterpriseObjectAmountStatisticDao)
    def enterpriseObjectDayStatisticDao = Mock(EnterpriseObjectDayStatisticDao)

    def setup() {
        // Inject mocked dependencies
        enterpriseStatisticDataReportService.enterpriseAmountStatisticDao = enterpriseAmountStatisticDao
        enterpriseStatisticDataReportService.enterpriseDayStatisticDao = enterpriseDayStatisticDao
        enterpriseStatisticDataReportService.enterpriseEmployeeAmountStatisticDao = enterpriseEmployeeAmountStatisticDao
        enterpriseStatisticDataReportService.enterpriseEmployeeDayStatisticDao = enterpriseEmployeeDayStatisticDao
        enterpriseStatisticDataReportService.enterpriseEmployeeObjectAmountStatisticDao = enterpriseEmployeeObjectAmountStatisticDao
        enterpriseStatisticDataReportService.enterpriseEmployeeObjectDayStatisticDao = enterpriseEmployeeObjectDayStatisticDao
        enterpriseStatisticDataReportService.enterpriseObjectAmountStatisticDao = enterpriseObjectAmountStatisticDao
        enterpriseStatisticDataReportService.enterpriseObjectDayStatisticDao = enterpriseObjectDayStatisticDao

        // Mock static methods
        PowerMockito.mockStatic(ChannelData.class)
        PowerMockito.mockStatic(UUIDUtil.class)
    }

    @Unroll
    def "reportEnterpriseDayStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseDayStatisticReportArg()
        arg.ea = "testEa"
        arg.date = new Date()
        arg.crmLeadIncrementCount = 5
        arg.crmCustomerIncrementCount = 3

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"
        
        def entity = entityExists ? new EnterpriseDayStatisticEntity() : null
        def newEntity = new EnterpriseDayStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseDayStatisticDao.getUnique(arg.ea, arg.date) >> entity
        if (!entityExists) {
            enterpriseDayStatisticDao.insertIgnore("test-uuid", arg.ea, arg.date) >> 1
            enterpriseDayStatisticDao.getUnique(arg.ea, arg.date) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseDayStatistic(arg)

        then:
        result.isSuccess()
        result.data == null
        
        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseDayStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }

    @Unroll
    def "reportEnterpriseAmountStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseAmountStatisticReportArg()
        arg.ea = "testEa"
        arg.crmLeadAccumulationCount = 10
        arg.crmCustomerAccumulationCount = 8

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"
        
        def entity = entityExists ? new EnterpriseAmountStatisticEntity() : null
        def newEntity = new EnterpriseAmountStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseAmountStatisticDao.getByEa(arg.ea) >> entity
        if (!entityExists) {
            enterpriseAmountStatisticDao.insertIgnore("test-uuid", arg.ea) >> 1
            enterpriseAmountStatisticDao.getByEa(arg.ea) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseAmountStatistic(arg)

        then:
        result.isSuccess()
        result.data == null
        
        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseAmountStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }

    @Unroll
    def "reportEnterpriseObjectAmountStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseObjectAmountStatisticReportArg()
        arg.ea = "testEa"
        arg.objectType = 1
        arg.objectId = "objectId123"
        arg.employeeForwardCount = 5
        arg.forwardEmployeeCount = 3
        arg.forwardOtherCount = 2

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"
        
        def entity = entityExists ? new EnterpriseObjectAmountStatisticEntity() : null
        def newEntity = new EnterpriseObjectAmountStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseObjectAmountStatisticDao.getUnique(arg.ea, arg.objectId) >> entity
        if (!entityExists) {
            enterpriseObjectAmountStatisticDao.insertIgnore("test-uuid", arg.ea, arg.objectType, arg.objectId) >> 1
            enterpriseObjectAmountStatisticDao.getUnique(arg.ea, arg.objectId) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseObjectAmountStatistic(arg)

        then:
        result.isSuccess()
        result.data == null
        
        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseObjectAmountStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }

    @Unroll
    def "reportEnterpriseObjectDayStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseObjectDayStatisticReportArg()
        arg.ea = "testEa"
        arg.objectType = 1
        arg.objectId = "objectId123"
        arg.date = new Date()
        arg.employeeForwardCount = 5
        arg.forwardEmployeeCount = 3
        arg.forwardOtherCount = 2

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"
        
        def entity = entityExists ? new EnterpriseObjectDayStatisticEntity() : null
        def newEntity = new EnterpriseObjectDayStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseObjectDayStatisticDao.getUnique(arg.ea, arg.objectId, arg.date) >> entity
        if (!entityExists) {
            enterpriseObjectDayStatisticDao.insertIgnore("test-uuid", arg.ea, arg.objectType, arg.objectId, arg.date) >> 1
            enterpriseObjectDayStatisticDao.getUnique(arg.ea, arg.objectId, arg.date) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseObjectDayStatistic(arg)

        then:
        result.isSuccess()
        result.data == null
        
        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseObjectDayStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }

    @Unroll
    def "reportEnterpriseEmployeeAmountStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseEmployeeAmountStatisticReportArg()
        arg.ea = "testEa"
        arg.fsUserId = 12345
        arg.spreadCount = 7
        arg.crmLeadAccumulationCount = 4
        arg.crmCustomerAccumulationCount = 2

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"

        def entity = entityExists ? new EnterpriseEmployeeAmountStatisticEntity() : null
        def newEntity = new EnterpriseEmployeeAmountStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseEmployeeAmountStatisticDao.getUnique(arg.ea, arg.fsUserId) >> entity
        if (!entityExists) {
            enterpriseEmployeeAmountStatisticDao.insertIgnore("test-uuid", arg.ea, arg.fsUserId) >> 1
            enterpriseEmployeeAmountStatisticDao.getUnique(arg.ea, arg.fsUserId) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseEmployeeAmountStatistic(arg)

        then:
        result.isSuccess()
        result.data == null

        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseEmployeeAmountStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }

    @Unroll
    def "reportEnterpriseEmployeeDayStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseEmployeeDayStatisticReportArg()
        arg.ea = "testEa"
        arg.fsUserId = 12345
        arg.date = new Date()
        arg.spreadCount = 6
        arg.crmLeadIncrementCount = 3
        arg.crmCustomerIncrementCount = 1

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"

        def entity = entityExists ? new EnterpriseEmployeeDayStatisticEntity() : null
        def newEntity = new EnterpriseEmployeeDayStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseEmployeeDayStatisticDao.getUnique(arg.ea, arg.fsUserId, arg.date) >> entity
        if (!entityExists) {
            enterpriseEmployeeDayStatisticDao.insertIgnore("test-uuid", arg.ea, arg.fsUserId, arg.date) >> 1
            enterpriseEmployeeDayStatisticDao.getUnique(arg.ea, arg.fsUserId, arg.date) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseEmployeeDayStatistic(arg)

        then:
        result.isSuccess()
        result.data == null

        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseEmployeeDayStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }

    @Unroll
    def "reportEnterpriseEmployeeObjectAmountStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseEmployeeObjectAmountStatisticReportArg()
        arg.ea = "testEa"
        arg.fsUserId = 12345
        arg.objectType = 1
        arg.objectId = "objectId123"
        arg.spreadCount = 4

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"

        def entity = entityExists ? new EnterpriseEmployeeObjectAmountStatisticEntity() : null
        def newEntity = new EnterpriseEmployeeObjectAmountStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseEmployeeObjectAmountStatisticDao.getUnique(arg.ea, arg.fsUserId, arg.objectId) >> entity
        if (!entityExists) {
            enterpriseEmployeeObjectAmountStatisticDao.insertIgnore("test-uuid", arg.ea, arg.fsUserId, arg.objectType, arg.objectId) >> 1
            enterpriseEmployeeObjectAmountStatisticDao.getUnique(arg.ea, arg.fsUserId, arg.objectId) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseEmployeeObjectAmountStatistic(arg)

        then:
        result.isSuccess()
        result.data == null

        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseEmployeeObjectAmountStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }

    @Unroll
    def "reportEnterpriseEmployeeObjectDayStatistic - entity exists: #entityExists"() {
        given:
        def arg = new EnterpriseEmployeeObjectDayStatisticReportArg()
        arg.ea = "testEa"
        arg.fsUserId = 12345
        arg.objectType = 1
        arg.objectId = "objectId123"
        arg.date = new Date()
        arg.spreadCount = 3

        def channelData = new ChannelData()
        channelData.channelKey = "mankeep"

        def entity = entityExists ? new EnterpriseEmployeeObjectDayStatisticEntity() : null
        def newEntity = new EnterpriseEmployeeObjectDayStatisticEntity()

        PowerMockito.when(ChannelData.fromObject(arg)).thenReturn(channelData)
        PowerMockito.when(UUIDUtil.getUUID()).thenReturn("test-uuid")

        enterpriseEmployeeObjectDayStatisticDao.getUnique(arg.ea, arg.fsUserId, arg.objectId, arg.date) >> entity
        if (!entityExists) {
            enterpriseEmployeeObjectDayStatisticDao.insertIgnore("test-uuid", arg.ea, arg.fsUserId, arg.objectType, arg.objectId, arg.date) >> 1
            enterpriseEmployeeObjectDayStatisticDao.getUnique(arg.ea, arg.fsUserId, arg.objectId, arg.date) >> newEntity
        }

        when:
        def result = enterpriseStatisticDataReportService.reportEnterpriseEmployeeObjectDayStatistic(arg)

        then:
        result.isSuccess()
        result.data == null

        1 * (entityExists ? entity : newEntity).addAndUpdateChannelData(channelData)
        1 * enterpriseEmployeeObjectDayStatisticDao.updateStatistic(entityExists ? entity : newEntity)

        where:
        entityExists << [true, false]
    }
}
