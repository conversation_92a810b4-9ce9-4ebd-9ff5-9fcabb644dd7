package com.facishare.marketing.provider.service.qywx

import com.alibaba.fastjson.JSONObject
import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.qywx.GetMomentSendCustomerArg
import com.facishare.marketing.api.result.qywx.*
import com.facishare.marketing.api.vo.qywx.ListMomentTaskVO
import com.facishare.marketing.api.vo.qywx.MomentMessageVO
import com.facishare.marketing.common.enums.AssociateIdTypeEnum
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.TagName
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao
import com.facishare.marketing.provider.dao.qywx.QYWXMomentSendResultDaO
import com.facishare.marketing.provider.dao.qywx.QYWXMomentTaskDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dto.qywx.QueryMomentSendResultDTO
import com.facishare.marketing.provider.entity.data.MomentMessageData
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxMomentTaskEntity
import com.facishare.marketing.provider.innerResult.qywx.*
import com.facishare.marketing.provider.manager.ExecuteTaskDetailManager
import com.facishare.marketing.provider.manager.FileV2Manager
import com.facishare.marketing.provider.manager.FsBindManager
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.qywx.*
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager
import com.facishare.marketing.provider.util.MarketingJobUtil
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.service.MetadataControllerService
import com.github.jedis.support.MergeJedisCmd
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ConcurrentHashMap

class QywxMomentServiceImplTest extends Specification {

    def qywxMomentService = new QywxMomentServiceImpl()

    // Mock all dependencies
    def momentManager = Mock(MomentManager)
    def qywxMomentTaskDAO = Mock(QYWXMomentTaskDAO)
    def qywxManager = Mock(QywxManager)
    def qywxMomentSendResultDaO = Mock(QYWXMomentSendResultDaO)
    def marketingActivityRemoteManager = Mock(MarketingActivityRemoteManager)
    def appVersionManager = Mock(AppVersionManager)
    def httpManager = Mock(HttpManager)
    def redisManager = Mock(RedisManager)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def fileV2Manager = Mock(FileV2Manager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def qywxUserManager = Mock(QywxUserManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def metadataTagManager = Mock(MetadataTagManager)
    def eieaConverter = Mock(EIEAConverter)
    def executeTaskDetailManager = Mock(ExecuteTaskDetailManager)
    def metadataControllerService = Mock(MetadataControllerService)
    def marketingActivityAuditManager = Mock(MarketingActivityAuditManager)
    def dataPermissionManager = Mock(DataPermissionManager)
    def fsBindManager = Mock(FsBindManager)
    def jedisCmd = Mock(MergeJedisCmd)
    def qywxActivatedAccountManager = Mock(QywxActivatedAccountManager)

    def setup() {
        qywxMomentService.momentManager = momentManager
        qywxMomentService.qywxMomentTaskDAO = qywxMomentTaskDAO
        qywxMomentService.qywxManager = qywxManager
        qywxMomentService.qywxMomentSendResultDaO = qywxMomentSendResultDaO
        qywxMomentService.marketingActivityRemoteManager = marketingActivityRemoteManager
        qywxMomentService.appVersionManager = appVersionManager
        qywxMomentService.httpManager = httpManager
        qywxMomentService.redisManager = redisManager
        qywxMomentService.agentConfigDAO = agentConfigDAO
        qywxMomentService.fileV2Manager = fileV2Manager
        qywxMomentService.crmMetadataManager = crmMetadataManager
        qywxMomentService.marketingActivityExternalConfigDao = marketingActivityExternalConfigDao
        qywxMomentService.qywxUserManager = qywxUserManager
        qywxMomentService.crmV2Manager = crmV2Manager
        qywxMomentService.metadataTagManager = metadataTagManager
        qywxMomentService.eieaConverter = eieaConverter
        qywxMomentService.executeTaskDetailManager = executeTaskDetailManager
        qywxMomentService.metadataControllerService = metadataControllerService
        qywxMomentService.marketingActivityAuditManager = marketingActivityAuditManager
        qywxMomentService.dataPermissionManager = dataPermissionManager
        qywxMomentService.fsBindManager = fsBindManager
        qywxMomentService.jedisCmd = jedisCmd
        qywxMomentService.qywxActivatedAccountManager = qywxActivatedAccountManager
    }

    @Unroll
    def "qywxMomentResultScheduleTest"() {
        given:
        momentManager.qywxMomentResultSchedule(*_) >> null

        when:
        qywxMomentService.qywxMomentResultSchedule()

        then:
        1 * momentManager.qywxMomentResultSchedule(_)
    }

    @Unroll
    def "qywxMomentResultScheduleTest1"() {
        given:
        momentManager.qywxMomentResultSchedule(*_) >> null
        qywxMomentService.scanAllTaskTime=new Date().getTime()

        when:
        qywxMomentService.qywxMomentResultSchedule()

        then:
        1 * momentManager.qywxMomentResultSchedule(_)
    }
    @Unroll
    def "qywxMomentTaskScheduleTest"() {
        given:
        qywxMomentTaskDAO.getMomentTaskResultTask() >> momentTaskResultTaskMock
        marketingActivityRemoteManager.enterpriseStop(*_) >> enterpriseStopMock
        appVersionManager.getCurrentAppVersion(*_) >> appVersionMock
        momentManager.getOrCreateAccessToken(*_) >> "accessToken123"

        when:
        qywxMomentService.qywxMomentTaskSchedule()

        then:
        expectedCalls * momentManager.getOrCreateAccessToken(_)

        where:
        momentTaskResultTaskMock                                                                    | enterpriseStopMock | appVersionMock | expectedCalls
        []                                                                                          | false              | "1.0"          | 0
        [new QywxMomentTaskEntity(id: "1", ea: "ea1")]                                             | true               | "1.0"          | 0
        [new QywxMomentTaskEntity(id: "1", ea: "ea1")]                                             | false              | null           | 0
        [new QywxMomentTaskEntity(id: "1", ea: "ea1")]                                             | false              | "1.0"          | 1
        [new QywxMomentTaskEntity(id: "1", ea: "ea1"), new QywxMomentTaskEntity(id: "2", ea: "ea2")] | false              | "1.0"          | 2
    }
    @Unroll
    def "listMomentTaskTest"() {
        given:
        def vo = new ListMomentTaskVO()
        def expectedResult = Result.newSuccess(new PageResult<ListMomentTaskResult>())
        momentManager.listMomentTask(*_) >> expectedResult

        when:
        def result = qywxMomentService.listMomentTask("ea123", 1000, vo)

        then:
        1 * momentManager.listMomentTask("ea123", 1000, vo)
        result != expectedResult
    }
    @Unroll
    def "listMomentStaticDataTest"() {
        given:
        qywxMomentTaskDAO.queryTaskByTimeRange(*_) >> taskCountMock
        qywxMomentSendResultDaO.emplyeeSendAndCusomerByMonth(*_) >> queryResultMock
        qywxMomentSendResultDaO.queryExceedFourMomentByMonth(*_) >> canNotSendMock
        qywxMomentSendResultDaO.getEmployeeSendTimes(*_) >> totalMock
        crmMetadataManager.countCrmObjectData(*_) >> totalQywxCountMock

        when:
        def result = qywxMomentService.listMomentStaticData("ea123", 1000)

        then:
        result.success
        result.data.taskCount == taskCountMock
        result.data.canSend == (totalQywxCountMock - canNotSendMock)
        result.data.receiveTotal == queryResultMock.customerCount
        result.data.sendTotal == queryResultMock.customerCount
        result.data.total == totalMock

        where:
        taskCountMock | queryResultMock                                                      | canNotSendMock | totalMock | totalQywxCountMock
        5             | new QueryMomentSendResultDTO(customerCount: 100, employeeCount: 10) | 2              | 15        | 200
        0             | new QueryMomentSendResultDTO(customerCount: 0, employeeCount: 0)     | 0              | 0         | 0
        10            | new QueryMomentSendResultDTO(customerCount: 50, employeeCount: 5)    | 5              | 25        | 100
    }
    @Unroll
    def "asyncGetMomentSendCustomerDataTest"() {
        given:
        def arg = new GetMomentSendCustomerArg()
        arg.calculationTaskId = calculationTaskIdMock
        jedisCmd.get(*_) >> resultStrMock

        when:
        def result = qywxMomentService.asyncGetMomentSendCustomerData("ea123", 1000, arg)

        then:
        result.success == expectedSuccess
        if (expectedSuccess && calculationTaskIdMock) {
            result.data.class == SendMomentCustomerResult.class
        }

        where:
        calculationTaskIdMock | resultStrMock                                                    | expectedSuccess
        null                  | null                                                             | true
        "task123"             | null                                                             | false
        "task123"             | '{"customerCount":10,"employeeCount":5,"selectEmployeeCount":3}' | true
        ""                    | null                                                             | true
    }
}