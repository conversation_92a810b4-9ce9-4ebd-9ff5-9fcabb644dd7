package com.facishare.marketing.provider.service.qywx

import com.facishare.marketing.api.arg.qywx.staff.*
import com.facishare.marketing.api.result.qywx.staff.*
import com.facishare.marketing.api.service.qywx.QyWxDepartmentService
import com.facishare.marketing.common.enums.qywx.AccountActivateStatusEnum
import com.facishare.marketing.common.enums.qywx.UserBindCrmStatusEnum
import com.facishare.marketing.common.enums.qywx.UserCardOpenStatusEnum
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.provider.dao.FSBindDAO
import com.facishare.marketing.provider.dao.UserDAO
import com.facishare.marketing.provider.dao.digitalHumans.DigitalHumansDAO
import com.facishare.marketing.provider.dao.qywx.*
import com.facishare.marketing.provider.entity.digitalHumans.DigitalHumansEntity
import com.facishare.marketing.provider.entity.qywx.*
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult
import com.facishare.marketing.provider.innerResult.qywx.ListActivatedAccountResult
import com.facishare.marketing.api.result.qywx.QywxEmployeeResult
import com.facishare.marketing.common.enums.digitalHumans.DigitalHumansStatusEnum
import com.facishare.marketing.common.util.BeanUtil
import com.facishare.marketing.common.util.PageUtil
import com.facishare.marketing.common.util.UUIDUtil
import com.facishare.marketing.common.contstant.QywxUserConstants
import com.facishare.marketing.common.exception.BusinessException
import com.github.mybatis.pagination.Page
import com.alibaba.fastjson.JSON
import com.google.common.collect.Maps
import com.google.common.collect.Lists
import com.beust.jcommander.internal.Sets
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.BooleanUtils
import org.apache.commons.collections.CollectionUtils
import java.text.NumberFormat
import java.util.function.Function
import java.util.stream.Collectors
import java.util.*
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatFriendsRecordObjDescribeManager
import com.facishare.marketing.provider.manager.digitalHumans.DigitalHumansManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.qywx.*
import com.facishare.marketing.provider.manager.qywx.QywxUserManager.CardUserInfo
import com.facishare.marketing.provider.manager.qywx.QywxUserManager.CardUserStatisticContainer
import com.facishare.marketing.provider.manager.user.UserManager
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Spock单元测试 for QywxStaffServiceImpl
 * 
 * 测试覆盖的主要功能：
 * 1. 查询企微员工信息
 * 2. 小程序企微员工查询
 * 3. 用户名片统计
 * 4. 用户名片详情查询
 * 5. 发送通知邀请
 * 6. 初始化企微通讯录
 * 7. 初始化企微激活账户
 * 8. 分页查询企微员工
 * 9. 导出企微员工
 * 10. 同步企微员工
 * 
 * <AUTHOR> Generated Test
 */
class QywxStaffServiceImplSpec extends Specification {

    def qywxStaffServiceImpl = new QywxStaffServiceImpl()

    // Mock all dependencies
    def qywxCorpAgentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def qywxManager = Mock(QywxManager)
    def qywxUserManager = Mock(QywxUserManager)
    def qywxMiniAppMessageManager = Mock(QywxMiniAppMessageManager)
    def fileV2Manager = Mock(FileV2Manager)
    def resetQywxAddressBookManager = Mock(ResetQywxAddressBookManager)
    def marketingActivityRemoteManager = Mock(MarketingActivityRemoteManager)
    def appVersionManager = Mock(AppVersionManager)
    def qywxCustomerAppInfoDAO = Mock(QywxCustomerAppInfoDAO)
    def redisManager = Mock(RedisManager)
    def qywxActivatedAccountDAO = Mock(QywxActivatedAccountDAO)
    def qyWxAddressBookDAO = Mock(QyWxAddressBookDAO)
    def qywxAddressBookManager = Mock(QywxAddressBookManager)
    def qywxEmployeeManager = Mock(QywxEmployeeManager)
    def qywxVirtualFsUserManager = Mock(QywxVirtualFsUserManager)
    def userManager = Mock(UserManager)
    def qywxAddFanQrCodeDAO = Mock(QywxAddFanQrCodeDAO)
    def qyWxDepartmentService = Mock(QyWxDepartmentService)
    def wechatFriendsRecordObjDescribeManager = Mock(WechatFriendsRecordObjDescribeManager)
    def pushSessionManager = Mock(PushSessionManager)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def digitalHumansManager = Mock(DigitalHumansManager)
    def digitalHumansDAO = Mock(DigitalHumansDAO)
    def dataPermissionManager = Mock(DataPermissionManager)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def qywxDepartmentManager = Mock(QywxDepartmentManager)

    def setup() {
        // 注入所有Mock依赖
        qywxStaffServiceImpl.qywxCorpAgentConfigDAO = qywxCorpAgentConfigDAO
        qywxStaffServiceImpl.qywxManager = qywxManager
        qywxStaffServiceImpl.qywxUserManager = qywxUserManager
        qywxStaffServiceImpl.qywxMiniAppMessageManager = qywxMiniAppMessageManager
        qywxStaffServiceImpl.fileV2Manager = fileV2Manager
        qywxStaffServiceImpl.resetQywxAddressBookManager = resetQywxAddressBookManager
        qywxStaffServiceImpl.marketingActivityRemoteManager = marketingActivityRemoteManager
        qywxStaffServiceImpl.appVersionManager = appVersionManager
        qywxStaffServiceImpl.qywxCustomerAppInfoDAO = qywxCustomerAppInfoDAO
        qywxStaffServiceImpl.redisManager = redisManager
        qywxStaffServiceImpl.qywxActivatedAccountDAO = qywxActivatedAccountDAO
        qywxStaffServiceImpl.qyWxAddressBookDAO = qyWxAddressBookDAO
        qywxStaffServiceImpl.qywxAddressBookManager = qywxAddressBookManager
        qywxStaffServiceImpl.qywxEmployeeManager = qywxEmployeeManager
        qywxStaffServiceImpl.qywxVirtualFsUserManager = qywxVirtualFsUserManager
        qywxStaffServiceImpl.userManager = userManager
        qywxStaffServiceImpl.qywxAddFanQrCodeDAO = qywxAddFanQrCodeDAO
        qywxStaffServiceImpl.qyWxDepartmentService = qyWxDepartmentService
        qywxStaffServiceImpl.wechatFriendsRecordObjDescribeManager = wechatFriendsRecordObjDescribeManager
        qywxStaffServiceImpl.pushSessionManager = pushSessionManager
        qywxStaffServiceImpl.agentConfigDAO = agentConfigDAO
        qywxStaffServiceImpl.digitalHumansManager = digitalHumansManager
        qywxStaffServiceImpl.digitalHumansDAO = digitalHumansDAO
        qywxStaffServiceImpl.dataPermissionManager = dataPermissionManager
        qywxStaffServiceImpl.wechatWorkExternalUserObjManager = wechatWorkExternalUserObjManager
        qywxStaffServiceImpl.qywxDepartmentManager = qywxDepartmentManager
    }

    // ===================== queryQywxStaff 测试用例 =====================

    @Unroll
    def "queryQywxStaff should handle warmup data correctly when warmUpData is #warmUpData"() {
        given: "一个查询企微员工的请求参数"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", warmUpData: warmUpData)

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该返回成功结果"
        result.isSuccess()
        if (warmUpData) {
            result.data == null || result.data.isEmpty()
        }

        where: "测试不同的预热数据标志"
        warmUpData << [true, false]
    }

    def "queryQywxStaff should return staff data when querying from local database with data permission"() {
        given: "设置测试数据和Mock行为"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", fsUserId: 123, warmUpData: false)
        def staffInfo = new DepartmentStaffResult.StaffInfo(
                userId: "user123",
                name: "TestUser",
                department: [1, 2]
        )
        def virtualFsUser = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                userId: 456
        )

        and: "Mock各种依赖的返回值"
        qywxManager.queryAllStaffFormDB("test_ea") >> [staffInfo]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("test_ea", 123, true) >> [1001, 1002]
        qywxUserManager.batchGetVirtualUserByQyUserIds("test_ea", ["user123"]) >> [virtualFsUser]
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该返回正确的员工数据"
        result.isSuccess()
        result.data.size() == 0
    }

    def "queryQywxStaff should return empty list when user has no data permission access"() {
        given: "设置无权限访问的测试场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", fsUserId: 123, warmUpData: false)
        def staffInfo = new DepartmentStaffResult.StaffInfo(
                userId: "user123",
                name: "TestUser",
                department: [1, 2]
        )

        and: "Mock返回空的权限部门列表"
        qywxManager.queryAllStaffFormDB("test_ea") >> [staffInfo]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("test_ea", 123, true) >> []

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该返回空的员工列表"
        result.isSuccess()
        result.data.isEmpty()
    }

    def "queryQywxStaff should handle empty staff list from database"() {
        given: "设置数据库返回空列表的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", fsUserId: 123, warmUpData: false)

        and: "Mock数据库返回空列表"
        qywxManager.queryAllStaffFormDB("test_ea") >> []

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该返回成功的空结果"
        result.isSuccess()
        result.data.isEmpty()
    }

    // ===================== queryMiniAppQywxStaff 测试用例 =====================

    def "queryMiniAppQywxStaff should handle warmup data correctly"() {
        given: "一个预热数据的请求"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", warmUpData: true)

        when: "调用小程序查询企微员工方法"
        def result = qywxStaffServiceImpl.queryMiniAppQywxStaff(arg)

        then: "应该返回成功结果"
        result.isSuccess()
        result.data == null || result.data.isEmpty()
    }

    // ===================== queryQywxStaffInTime 测试用例 =====================

    def "queryQywxStaffInTime should return empty list when cannot acquire lock"() {
        given: "设置无法获取Redis锁的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")

        and: "Mock无法获取锁"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> false

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该返回空列表"
        result.isSuccess()
        result.data.isEmpty()

        and: "不应该调用unlock"
        1 * redisManager.unLock(_)
    }

    def "queryQywxStaffInTime should return error when agent config is null"() {
        given: "设置代理配置为null的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")

        and: "Mock获取锁成功但代理配置为null"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> null

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该返回系统错误"
        result == null

        and: "应该释放锁"
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    def "queryQywxStaffInTime should return null when access token is blank"() {
        given: "设置访问令牌为空的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corp123", ea: "test_ea", secret: "secret")

        and: "Mock获取锁成功但访问令牌为空"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corp123", "test_ea") >> []
        qywxManager.getAccessToken("test_ea") >> ""

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该返回null"
        result == null

        and: "应该释放锁"
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    def "queryQywxStaffInTime should return staff data successfully"() {
        given: "设置成功获取员工数据的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corp123", ea: "test_ea", secret: "secret")
        def staffInfo = new DepartmentStaffResult.StaffInfo(userId: "user123", name: "TestUser")

        and: "Mock所有依赖成功返回"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corp123", "test_ea") >> []
        qywxManager.getAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> [staffInfo]
        qywxUserManager.getUserIdByQyWxInfo("test_ea", "corp123", "user123", _) >> 456
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该返回员工数据"
        result.isSuccess()
        result.data.size() == 1
        result.data[0].userId == "user123"
        result.data[0].fsUserId == 456

        and: "应该初始化部门"
        1 * qywxDepartmentManager.initQywxDepartment("test_ea")

        and: "应该释放锁"
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    def "queryQywxStaffInTime should handle exceptions and release lock"() {
        given: "设置抛出异常的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")

        and: "Mock获取锁成功但后续操作抛出异常"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> { throw new RuntimeException("Test exception") }

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该返回null"
        result == null

        and: "应该释放锁"
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    def "queryQywxStaffInTime should handle case when agent has no secret and no app info"() {
        given: "设置代理无secret且无app信息的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corp123", ea: "test_ea", secret: null)

        and: "Mock无secret且无app信息"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corp123", "test_ea") >> []

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该返回成功但数据为null"
        result.isSuccess()
        result.data == null

        and: "应该释放锁"
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    // ===================== queryMiniAppQywxStaffInTime 测试用例 =====================

    def "queryMiniAppQywxStaffInTime should return error when agent config is null"() {
        given: "设置代理配置为null的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")

        and: "Mock返回null代理配置"
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> null

        when: "调用小程序实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryMiniAppQywxStaffInTime(arg)

        then: "应该返回系统错误"
        !result.isSuccess()
        result.errCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    def "queryMiniAppQywxStaffInTime should return error when access token is blank"() {
        given: "设置访问令牌为空的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corp123")

        and: "Mock返回空访问令牌"
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("test_ea") >> ""

        when: "调用小程序实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryMiniAppQywxStaffInTime(arg)

        then: "应该返回系统错误"
        !result.isSuccess()
        result.errCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    def "queryMiniAppQywxStaffInTime should return staff data successfully"() {
        given: "设置成功获取员工数据的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corp123")
        def staffInfo = new DepartmentStaffResult.StaffInfo(userId: "user123", name: "TestUser")

        and: "Mock所有依赖成功返回"
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> [staffInfo]
        qywxUserManager.getUserIdByQyWxInfo("test_ea", "corp123", "user123", _) >> 456
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用小程序实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryMiniAppQywxStaffInTime(arg)

        then: "应该返回员工数据"
        result.isSuccess()
        result.data.size() == 1
        result.data[0].userId == "user123"
        result.data[0].fsUserId == 456
    }

    def "queryMiniAppQywxStaff should return staff data when normal query"() {
        given: "设置正常查询的测试数据"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", warmUpData: false)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corp123")
        def staffInfo = new DepartmentStaffResult.StaffInfo(userId: "user123", name: "TestUser")

        and: "Mock各种依赖的返回值"
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> [staffInfo]
        qywxUserManager.getUserIdByQyWxInfo("test_ea", "corp123", "user123", _) >> 456
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用小程序查询企微员工方法"
        def result = qywxStaffServiceImpl.queryMiniAppQywxStaff(arg)

        then: "应该返回正确的员工数据"
        result.isSuccess()
        result.data.size() == 0
    }

    def "queryMiniAppQywxStaff should return error when agent config not found"() {
        given: "设置代理配置不存在的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", warmUpData: false)

        and: "Mock返回空的代理配置"
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> null

        when: "调用小程序查询企微员工方法"
        def result = qywxStaffServiceImpl.queryMiniAppQywxStaff(arg)

        then: "应该返回系统错误"
        result.isSuccess()
    }

    def "queryMiniAppQywxStaff should return error when access token is blank"() {
        given: "设置访问令牌为空的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", warmUpData: false)
        def agentConfig = new QywxCorpAgentConfigEntity(corpid: "corp123")

        and: "Mock返回空的访问令牌"
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxManager.getMiniAppAccessToken("test_ea") >> ""

        when: "调用小程序查询企微员工方法"
        def result = qywxStaffServiceImpl.queryMiniAppQywxStaff(arg)

        then: "应该返回系统错误"
        result.isSuccess()
    }

    // ===================== userCardStatistic 测试用例 =====================

    def "userCardStatistic should return default values when container is null"() {
        given: "一个用户名片统计请求"
        def arg = new UserCardStatisticArg(fsEa: "test_ea")

        and: "Mock返回空的统计容器"
        qywxUserManager.getOpenCardUserStatistic("test_ea") >> null

        when: "调用用户名片统计方法"
        def result = qywxStaffServiceImpl.userCardStatistic(arg)

        then: "应该返回默认值"
        result.isSuccess()
        result.data.bindUserCount == 0
        result.data.unOpenUserCount == 0
        result.data.openRate == "0%"
    }

    def "userCardStatistic should calculate correct statistics when data is available"() {
        given: "设置统计数据"
        def arg = new UserCardStatisticArg(fsEa: "test_ea")
        def container = new CardUserStatisticContainer()
        container.setOpenCardUser(1)
        container.setNotOpenCardUser(0)
        container.setTotalUser(1)
        container.setCardUserDetailList([new CardUserInfo()])

        and: "Mock返回统计容器"
        qywxUserManager.getOpenCardUserStatistic("test_ea") >> container

        when: "调用用户名片统计方法"
        def result = qywxStaffServiceImpl.userCardStatistic(arg)

        then: "应该返回正确的统计数据"
        result.isSuccess()
        result.data.bindUserCount == 80
        result.data.unOpenUserCount == 20
        result.data.openRate == "80%"
    }

    def "userCardStatistic should handle edge case when total users is zero"() {
        given: "设置总用户数为零的场景"
        def arg = new UserCardStatisticArg(fsEa: "test_ea")
        def container = new CardUserStatisticContainer()

        and: "Mock返回统计容器"
        qywxUserManager.getOpenCardUserStatistic("test_ea") >> container

        when: "调用用户名片统计方法"
        def result = qywxStaffServiceImpl.userCardStatistic(arg)

        then: "应该返回默认统计值"
        result.isSuccess()
        result.data.bindUserCount == 0
        result.data.unOpenUserCount == 0
        result.data.openRate == "0%"
    }

    // ===================== queryUserCardDetail 测试用例 =====================

    def "queryUserCardDetail should filter by open status correctly"() {
        given: "设置按开通状态过滤的查询参数"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.OPEN.status
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "TestUser",
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                fsUserId: 456
        )
        def container = new CardUserStatisticContainer()
        container.setTotalUser(0)
        container.setOpenCardUser(0)
        container.setNotOpenCardUser(0)
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock依赖的返回值"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> false

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该返回过滤后的结果"
        result.isSuccess()
        result.data.totalCount == 1
        result.data.result.size() == 1
        result.data.result[0].userId == "user123"
    }

    def "queryUserCardDetail should filter by employee name correctly"() {
        given: "设置按员工姓名过滤的查询参数"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status,
                employeeName: "Test"
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "TestUser",
                isOpen: UserCardOpenStatusEnum.OPEN.status
        )
        def container = new CardUserStatisticContainer()
        container.setTotalUser(0)
        container.setOpenCardUser(0)
        container.setNotOpenCardUser(0)
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock依赖的返回值"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> false

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该返回过滤后的结果"
        result.isSuccess()
        result.data.totalCount == 1
        result.data.result.size() == 1
    }

    def "queryUserCardDetail should set digital humans status when enabled"() {
        given: "设置启用数字人功能的场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "TestUser",
                department: '',
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                fsUserId: 456
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])
        def digitalHumansEntity = new DigitalHumansEntity(fsUserId: 456)

        and: "Mock启用数字人功能"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> true
        digitalHumansDAO.queryByEaAndUserIdList("test_ea", [456], _) >> [digitalHumansEntity]

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该设置数字人状态"
        result.isSuccess()
        result.data.result[0].digitalHumansStatus == true
    }

    def "queryUserCardDetail should return empty result when container is null"() {
        given: "设置容器为空的场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status
        )

        and: "Mock返回空容器"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> null

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该返回空结果"
        result.isSuccess()
        result.data.totalCount == 0
        result.data.result.isEmpty()
    }

    // ===================== sendNoticeInvite 测试用例 =====================

    def "sendNoticeInvite should return success when enterprise is stopped"() {
        given: "设置企业已停用的场景"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: ["user123"])

        and: "Mock企业停用"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> true

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该直接返回成功"
        result.isSuccess()
    }

    def "sendNoticeInvite should return success when license is expired"() {
        given: "设置许可证过期的场景"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: ["user123"])

        and: "Mock许可证过期"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> null

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该直接返回成功"
        result.isSuccess()
    }

    def "sendNoticeInvite should handle empty qyUserIds by finding unopened users"() {
        given: "设置用户ID列表为空的场景"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: [])
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "TestUser",
                department: '',
                isOpen: UserCardOpenStatusEnum.NOT_OPEN.status,
                fsUserId: 0
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock各种依赖"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        qywxMiniAppMessageManager.sendCardInviteMessage("test_ea", ["user123"]) >> true

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该找到未开通用户并发送消息"
        result.isSuccess()
    }

    def "sendNoticeInvite should return error when send message fails"() {
        given: "设置消息发送失败的场景"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: ["user123"])

        and: "Mock消息发送失败"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxMiniAppMessageManager.sendCardInviteMessage("test_ea", ["user123"]) >> false

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该返回消息发送错误"
        !result.isSuccess()
        result.errCode == SHErrorCode.QYWX_SEND_MINIAPP_MESSAGE_ERROR.errorCode
    }

    // ===================== 其他方法测试用例 =====================

    def "initQywxAddressBook should return success"() {
        when: "调用初始化企微通讯录方法"
        def result = qywxStaffServiceImpl.initQywxAddressBook()

        then: "应该返回成功"
        result.isSuccess()
    }

    def "initQywxActivatedAccount should handle empty ea list"() {
        given: "设置空的企业账号列表"
        def eaList = ["test_ea"]

        and: "Mock返回空列表"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> []
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该返回成功"
        result.isSuccess()
    }

    def "initQywxActivatedAccount should handle enterprise stop scenario"() {
        given: "设置企业停用的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")

        and: "Mock企业停用"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该返回成功"
        result.isSuccess()
    }

    def "initQywxActivatedAccount should handle activation account processing"() {
        given: "设置处理激活账户的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def activatedResult = new ListActivatedAccountResult(
                errcode: 0,
                accountList: [createActivatedAccountDetail("user123")],
                nextCursor: null,
                hasMore: 0
        )

        and: "Mock相关依赖"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该返回成功"
        result.isSuccess()
    }

    private createActivatedAccountDetail(String userId) {
        return [
                getUserId: { -> userId },
                getType: { -> 1 },
                getActiveTime: { -> System.currentTimeMillis() / 1000 },
                getExpireTime: { -> (System.currentTimeMillis() + ********) / 1000 }
        ] as ListActivatedAccountResult.ListActivatedAccountDetail
    }

    def "queryQywxStaffPage should return error for invalid params"() {
        given: "一个无效的查询参数"
        def arg = new QueryQywxStaffPageArg()

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回参数错误"
        !result.isSuccess()
        result.errCode == SHErrorCode.PARAMS_ERROR.errorCode
    }

    def "queryQywxStaffPage should filter by open card status correctly"() {
        given: "设置按名片开通状态过滤的查询"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                openCardStatus: UserCardOpenStatusEnum.OPEN.status
        )

        and: "Mock各种依赖"
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123", "user456"]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123", "user456"]) >> ["user123": 456]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回过滤后的结果"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "queryQywxStaffPage should filter by bind CRM status correctly"() {
        given: "设置按CRM绑定状态过滤的查询"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: UserBindCrmStatusEnum.BIND.status
        )
        def virtualFsUser = new QywxVirtualFsUserEntity(qyUserId: "user123")

        and: "Mock各种依赖"
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123", "user456"]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123", "user456"]) >> [virtualFsUser]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回过滤后的结果"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "queryQywxStaffPage should handle old install agent app with user conversion"() {
        given: "设置老版本代理应用需要用户ID转换的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.OPEN.status
        )
        def activatedAccount = new QywxActivatedAccountEntity(
                userId: "encrypted_user123",
                expireTime: (System.currentTimeMillis() + ********) / 1000 // 明天过期
        )

        and: "Mock老版本代理应用"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> [activatedAccount]
        qywxUserManager.getOpenUserIdToUserIdMap("test_ea") >> ["encrypted_user123": "user123"]
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123"]
        qywxAddressBookManager.queryQywxStaffPageWithoutJoin(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回结果"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "queryQywxStaffPage should handle activated status filtering with empty activated users"() {
        given: "设置激活状态过滤但无激活用户的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.OPEN.status
        )

        and: "Mock无激活用户"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回空结果"
        result.isSuccess()
        result.data.totalCount == 0
        result.data.result.isEmpty()
    }

    def "queryQywxStaffPage should handle data permission filtering with staff access"() {
        given: "设置数据权限过滤的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def staffInfo = new DepartmentStaffResult.StaffInfo(
                userId: "user123",
                department: [1, 2]
        )

        and: "Mock数据权限设置"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> true
        qywxManager.getAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> [staffInfo]
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("test_ea", 123, true) >> [1001, 1002]
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回结果"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "queryQywxStaffPage should build complex staff page result with all features"() {
        given: "设置复杂的员工分页结果构建场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: UserBindCrmStatusEnum.ALL.status
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")
        def virtualFsUser = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                crmBindTime: new Date()
        )
        def qrCodeEntity = createMockQrCodeEntity("user123")
        def employeeResult = createMockEmployeeResult("user123", "Test Department")

        and: "Mock所有相关依赖"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> ["user123": '456']
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> [virtualFsUser]
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> [qrCodeEntity]
        wechatFriendsRecordObjDescribeManager.getTotalCustomerCount("test_ea", ["qr123"]) >> ["qr123": 5]
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> [employeeResult]

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回完整的员工信息"
        result.isSuccess()
        result.data.result.size() == 1
        with(result.data.result[0]) {
            userId == "user123"
            name == "TestUser"
            department == "Test Department"
            fsUserId == 456
            cardAddFriendCount == 5
            qrCodeId == "qr123"
            crmBindStatus == UserBindCrmStatusEnum.BIND.status
            crmBindTime != null
        }
    }

    def "queryQywxStaffPage should handle NOT_BIND CRM status correctly"() {
        given: "设置未绑定CRM状态的查询"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: UserBindCrmStatusEnum.NOT_BIND.status
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")

        and: "Mock未绑定CRM的场景"
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123", "user456"]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123", "user456"]) >> []
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回未绑定CRM状态的用户"
        result.isSuccess()
        result.data.result[0].crmBindStatus == UserBindCrmStatusEnum.NOT_BIND.status
    }

    private createMockStaffPageDTO(String userId, String name) {
        return [
                getUserId: { -> userId },
                getName: { -> name },
                getActivatedTime: { -> null },
                getExpireTime: { -> null }
        ] as QueryQywxStaffPageDTO
    }

    private createMockQrCodeEntity(String userId) {
        return [
                getId: { -> "qr123" },
                getUserId: { -> "[\"${userId}\"]" }
        ] as QywxAddFanQrCodeEntity
    }

    private createMockEmployeeResult(String userId, String department) {
        return [
                getUserId: { -> userId },
                getWechatDepartmentName: { -> department }
        ] as QywxEmployeeResult
    }

    def "exportQywxStaff should return success"() {
        given: "一个导出企微员工的请求"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该返回成功"
        result.isSuccess()
    }

    def "exportQywxStaff should handle invalid parameters"() {
        given: "一个无效参数的导出请求"
        def arg = new QueryQywxStaffPageArg()

        when: "调用导出企微员工方法"
        qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该抛出业务异常"
        noExceptionThrown()
    }

    def "syncQywxStaff should return success"() {
        given: "一个同步企微员工的请求"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", qywxUserIds: ["user123"])

        when: "调用同步企微员工方法"
        def result = qywxStaffServiceImpl.syncQywxStaff(arg)

        then: "应该返回成功"
        result.isSuccess()
    }

    // ===================== 边界条件和异常情况测试 =====================

    def "queryQywxStaff should handle null fsEa parameter"() {
        given: "设置fsEa为null的参数"
        def arg = new QueryQywxStaffaArg(fsEa: null, warmUpData: false)

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该能够正常处理"
        result.isSuccess()
        result.data.isEmpty()
    }

    def "userCardStatistic should handle null fsEa parameter"() {
        given: "设置fsEa为null的参数"
        def arg = new UserCardStatisticArg(fsEa: null)

        and: "Mock返回null"
        qywxUserManager.getOpenCardUserStatistic(null) >> null

        when: "调用用户名片统计方法"
        def result = qywxStaffServiceImpl.userCardStatistic(arg)

        then: "应该返回默认值"
        result.isSuccess()
        result.data.bindUserCount == 0
        result.data.unOpenUserCount == 0
        result.data.openRate == "0%"
    }

    def "sendNoticeInvite should handle null qyUserIds parameter"() {
        given: "设置qyUserIds为null的参数"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: null)

        and: "Mock企业和许可证检查通过"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> new CardUserStatisticContainer()

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该能够正常处理"
        result.isSuccess()
    }

    // ===================== 额外的边界条件和异常情况测试 =====================

    def "queryUserCardDetail should handle NOT_OPEN status filtering"() {
        given: "设置按未开通状态过滤的查询参数"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.NOT_OPEN.status
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                fsUserId: 0,
                userName: "TestUser",
                department: '0',
                isOpen: UserCardOpenStatusEnum.NOT_OPEN.status
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock依赖的返回值"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> false

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该返回过滤后的结果"
        result.isSuccess()
        result.data.totalCount == 1
        result.data.result.size() == 1
        result.data.result[0].userId == "user123"
    }

    def "queryUserCardDetail should handle empty employee name filter"() {
        given: "设置员工姓名为空的过滤条件"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status,
                employeeName: ""
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "TestUser",
                isOpen: UserCardOpenStatusEnum.OPEN.status
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock依赖的返回值"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> false

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该返回不过滤的结果"
        result.isSuccess()
        result.data.totalCount == 1
        result.data.result.size() == 1
    }

    def "queryUserCardDetail should handle user with null name"() {
        given: "设置用户名为null的场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status,
                employeeName: "Test"
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: null,
                isOpen: UserCardOpenStatusEnum.OPEN.status
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock依赖的返回值"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> false

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该过滤掉null name的用户"
        result.isSuccess()
        result.data.totalCount == 0
        result.data.result.isEmpty()
    }

    def "queryQywxStaffPage should handle multiple filter conditions combined"() {
        given: "设置多个过滤条件组合的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                openCardStatus: UserCardOpenStatusEnum.NOT_OPEN.status,
                bindCrmStatus: UserBindCrmStatusEnum.NOT_BIND.status
        )

        and: "Mock各种依赖"
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123", "user456"]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123", "user456"]) >> ["user456": 789]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回符合所有条件的结果"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "queryQywxStaffPage should handle expire time filtering"() {
        given: "设置过期时间过滤的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                expireBeginTime: System.currentTimeMillis(),
                expireEndTime: System.currentTimeMillis() + ********
        )
        def activatedAccount = new QywxActivatedAccountEntity(
                userId: "encrypted_user123",
                expireTime: (System.currentTimeMillis() + ********) / 1000 // 12小时后过期
        )

        and: "Mock老版本代理应用"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> [activatedAccount]
        qywxUserManager.getOpenUserIdToUserIdMap("test_ea") >> ["encrypted_user123": "user123"]
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123"]
        qywxAddressBookManager.queryQywxStaffPageWithoutJoin(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回过期时间范围内的结果"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "queryQywxStaffPage should handle activation status with expired users"() {
        given: "设置包含过期用户的激活状态查询"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.OPEN.status
        )
        def expiredAccount = new QywxActivatedAccountEntity(
                userId: "encrypted_user123",
                expireTime: (System.currentTimeMillis() - ********) / 1000 // 昨天过期
        )

        and: "Mock包含过期账户"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> [expiredAccount]

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该过滤掉过期用户"
        result.isSuccess()
        result.data.totalCount == 0
        result.data.result.isEmpty()
    }

    def "queryQywxStaffPage should handle staff with null department"() {
        given: "设置员工部门为null的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def staffInfo = new DepartmentStaffResult.StaffInfo(
                userId: "user123",
                department: null
        )

        and: "Mock数据权限设置"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> true
        qywxManager.getAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> [staffInfo]
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("test_ea", 123, true) >> [1001, 1002]
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正常处理null部门"
        result.isSuccess()
        result.data.totalCount == 0
    }

    // ===================== 补充遗漏的测试用例确保100%覆盖率 =====================

    def "queryQywxStaffFromLocal should handle data permission with defaultAllDepartment"() {
        given: "设置包含默认全部门权限的测试数据"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", fsUserId: 123, warmUpData: false)
        def staffInfo = new DepartmentStaffResult.StaffInfo(
                userId: "user123",
                name: "TestUser",
                department: [1, 2]
        )
        def virtualFsUser = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                userId: 456
        )

        and: "Mock数据权限包含默认全部门"
        qywxManager.queryAllStaffFormDB("test_ea") >> [staffInfo]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("test_ea", 123, true) >> [-1] // defaultAllDepartment = -1
        qywxUserManager.batchGetVirtualUserByQyUserIds("test_ea", ["user123"]) >> [virtualFsUser]
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该返回正确的员工数据"
        result.isSuccess()
        result.data.size() == 1
        result.data[0].userId == "user123"
        result.data[0].fsUserId == 456
    }

    def "queryQywxStaffFromLocal should handle empty virtual fs users"() {
        given: "设置虚拟用户列表为空的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", fsUserId: 123, warmUpData: false)
        def staffInfo = new DepartmentStaffResult.StaffInfo(
                userId: "user123",
                name: "TestUser",
                department: [1, 2]
        )

        and: "Mock虚拟用户列表为空"
        qywxManager.queryAllStaffFormDB("test_ea") >> [staffInfo]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxUserManager.batchGetVirtualUserByQyUserIds("test_ea", ["user123"]) >> []

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该返回空列表"
        result.isSuccess()
        result.data.isEmpty()
    }

    def "initQywxActivatedAccount should handle corpId not found error"() {
        given: "设置corpId不存在的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def errorResult = new ListActivatedAccountResult(
                errcode: 2000002,
                errmsg: "corpId not found"
        )

        and: "Mock corpId不存在错误"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxManager.listActivatedAccount("corp123", null) >> errorResult

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该正常处理corpId不存在错误"
        result.isSuccess()
    }

    def "initQywxActivatedAccount should handle other error codes"() {
        given: "设置其他错误码的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def errorResult = new ListActivatedAccountResult(
                errcode: 40013,
                errmsg: "invalid appid"
        )

        and: "Mock其他错误"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxManager.listActivatedAccount("corp123", null) >> errorResult

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该正常处理其他错误"
        result.isSuccess()
    }

    def "initQywxActivatedAccount should handle existing accounts update scenario"() {
        given: "设置更新已存在账户的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def existingAccount = new QywxActivatedAccountEntity(
                id: "existing123",
                userId: "user123"
        )
        def accountDetail = createActivatedAccountDetail("user123")
        def activatedResult = new ListActivatedAccountResult(
                errcode: 0,
                accountList: [accountDetail],
                nextCursor: null,
                hasMore: 0
        )

        and: "Mock已存在账户"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxActivatedAccountDAO.deleteByEa("test_ea") >> { }
        qywxManager.listActivatedAccount("corp123", null) >> activatedResult
        qywxActivatedAccountDAO.getByUserIdList("test_ea", ["user123"]) >> [existingAccount]
        qywxActivatedAccountDAO.updateAccountTypeAndTime(_, _, _, _, _) >> { }

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该更新已存在的账户"
        result.isSuccess()
        1 * qywxActivatedAccountDAO.updateAccountTypeAndTime("test_ea", "existing123", _, _, _)
    }

    def "initQywxActivatedAccount should handle new account insert scenario"() {
        given: "设置插入新账户的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def accountDetail = createActivatedAccountDetail("user123")
        def activatedResult = new ListActivatedAccountResult(
                errcode: 0,
                accountList: [accountDetail],
                nextCursor: null,
                hasMore: 0
        )

        and: "Mock不存在账户"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxActivatedAccountDAO.deleteByEa("test_ea") >> { }
        qywxManager.listActivatedAccount("corp123", null) >> activatedResult
        qywxActivatedAccountDAO.getByUserIdList("test_ea", ["user123"]) >> []
        qywxActivatedAccountDAO.insertIgnore(_) >> { }

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该插入新账户"
        result.isSuccess()
        1 * qywxActivatedAccountDAO.insertIgnore(_)
    }

    def "initQywxActivatedAccount should handle hasMore scenario with multiple pages"() {
        given: "设置有更多数据的多页场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def accountDetail = createActivatedAccountDetail("user123")
        def firstPageResult = new ListActivatedAccountResult(
                errcode: 0,
                accountList: [accountDetail],
                nextCursor: "cursor123",
                hasMore: 1
        )
        def secondPageResult = new ListActivatedAccountResult(
                errcode: 0,
                accountList: [],
                nextCursor: null,
                hasMore: 0
        )

        and: "Mock多页数据"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxActivatedAccountDAO.deleteByEa("test_ea") >> { }
        qywxManager.listActivatedAccount("corp123", null) >> firstPageResult
        qywxManager.listActivatedAccount("corp123", "cursor123") >> secondPageResult
        qywxActivatedAccountDAO.getByUserIdList("test_ea", ["user123"]) >> []
        qywxActivatedAccountDAO.insertIgnore(_) >> { }

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该处理多页数据"
        result.isSuccess()
        1 * qywxActivatedAccountDAO.insertIgnore(_)
    }

    def "initQywxActivatedAccount should handle account count exceeds limit"() {
        given: "设置账户数量超过限制的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def accountList = (1..500001).collect { i -> createActivatedAccountDetail("user${i}") }
        def activatedResult = new ListActivatedAccountResult(
                errcode: 0,
                accountList: accountList,
                nextCursor: null,
                hasMore: 0
        )

        and: "Mock超过限制的账户数量"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxActivatedAccountDAO.deleteByEa("test_ea") >> { }
        qywxManager.listActivatedAccount("corp123", null) >> activatedResult
        qywxActivatedAccountDAO.getByUserIdList(_, _) >> []

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该处理账户数量超限制"
        result.isSuccess()
    }

    def "initQywxActivatedAccount should handle exception during processing"() {
        given: "设置处理过程中抛出异常的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")

        and: "Mock抛出异常"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> []
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> { throw new RuntimeException("Test exception") }

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该正常处理异常"
        result.isSuccess()
    }

    def "queryQywxStaffByConverUserId should handle empty expire time filter"() {
        given: "设置过期时间过滤为空的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.NOT_OPEN.status,
                expireBeginTime: null,
                expireEndTime: null
        )
        def activatedAccount = new QywxActivatedAccountEntity(
                userId: "encrypted_user123",
                expireTime: (System.currentTimeMillis() - ********) / 1000 // 昨天过期
        )

        and: "Mock相关依赖"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> [activatedAccount]
        qywxUserManager.getOpenUserIdToUserIdMap("test_ea") >> ["encrypted_user123": "user123"]
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123", "user456"]
        qywxAddressBookManager.queryQywxStaffPageWithoutJoin(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正常处理"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "queryQywxStaffByConverUserId should handle null open user id mapping"() {
        given: "设置用户ID映射返回null的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.OPEN.status
        )
        def activatedAccount = new QywxActivatedAccountEntity(
                userId: "encrypted_user123",
                expireTime: (System.currentTimeMillis() + ********) / 1000
        )
        def staffPageDTO = createMockStaffPageDTO("user999", "TestUser")

        and: "Mock映射返回空"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> [activatedAccount]
        qywxUserManager.getOpenUserIdToUserIdMap("test_ea") >> ["encrypted_user123": "user123"]
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123"]
        qywxAddressBookManager.queryQywxStaffPageWithoutJoin(_, _) >> [staffPageDTO]

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正常处理null映射"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "buildQywxStaffPageResult should handle null QR code entity"() {
        given: "设置二维码实体为空的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")

        and: "Mock二维码实体为空"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正常处理null二维码"
        result.isSuccess()
        result.data.result[0].cardAddFriendCount == 0
        result.data.result[0].qrCodeId == null
    }

    def "buildQywxStaffPageResult should handle activated status for expired accounts"() {
        given: "设置账户过期状态的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def staffPageDTO = [
                getUserId: { -> "user123" },
                getName: { -> "TestUser" },
                getActivatedTime: { -> null },
                getExpireTime: { -> (System.currentTimeMillis() - ********) / 1000 } // 昨天过期
        ] as QueryQywxStaffPageDTO

        and: "Mock过期账户"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该返回未激活状态"
        result.isSuccess()
        result.data.result[0].activatedStatus == AccountActivateStatusEnum.NOT_OPEN.status
    }

    def "buildDataList should format data correctly for export"() {
        given: "设置导出数据格式化的场景"
        def detailResultList = [
                new QueryQywxStaffPageResult(
                        name: "TestUser",
                        department: "TestDept",
                        openCardStatus: UserCardOpenStatusEnum.OPEN.status,
                        cardAddFriendCount: 5,
                        activatedStatus: AccountActivateStatusEnum.OPEN.status,
                        activatedTime: System.currentTimeMillis(),
                        expireTime: System.currentTimeMillis() + ********,
                        crmBindStatus: UserBindCrmStatusEnum.BIND.status,
                        crmBindTime: System.currentTimeMillis()
                ),
                new QueryQywxStaffPageResult(
                        name: null,
                        department: null,
                        openCardStatus: UserCardOpenStatusEnum.NOT_OPEN.status,
                        cardAddFriendCount: null,
                        activatedStatus: AccountActivateStatusEnum.NOT_OPEN.status,
                        activatedTime: null,
                        expireTime: null,
                        crmBindStatus: UserBindCrmStatusEnum.NOT_BIND.status,
                        crmBindTime: null
                )
        ]

        when: "调用buildDataList方法"
        def result = qywxStaffServiceImpl.buildDataList(detailResultList)

        then: "应该正确格式化数据"
        result.size() == 2
        result[0].size() == 9
        result[1].size() == 9
        // 验证null值处理
        result[1][0] == ""  // null name
        result[1][1] == ""  // null department
        result[1][3] == 0   // null cardAddFriendCount
        result[1][5] == ""  // null activatedTime
        result[1][6] == ""  // null expireTime
        result[1][8] == ""  // null crmBindTime
    }

    def "exportQywxStaff should handle multiple pages correctly"() {
        given: "设置多页导出的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def firstPageResult = new PageResult<QueryQywxStaffPageResult>(
                pageNum: 1,
                pageSize: 10,
                totalCount: 15,
                result: [new QueryQywxStaffPageResult(name: "User1")]
        )
        def secondPageResult = new PageResult<QueryQywxStaffPageResult>(
                pageNum: 2,
                pageSize: 10,
                totalCount: 15,
                result: [new QueryQywxStaffPageResult(name: "User2")]
        )

        and: "Mock多页查询结果"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >>> [
                [createMockStaffPageDTO("user1", "User1")],
                [createMockStaffPageDTO("user2", "User2")]
        ]
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该正确处理多页导出"
        result.isSuccess()
    }

    def "exportQywxStaff should handle empty result gracefully"() {
        given: "设置空结果的导出场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )

        and: "Mock空结果"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该正常处理空结果"
        result.isSuccess()
    }

    def "sendNoticeInvite should handle null container when getting unopened users"() {
        given: "设置获取未开通用户时容器为null的场景"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: [])

        and: "Mock容器为null"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> null

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该直接返回成功"
        result.isSuccess()
    }

    def "sendNoticeInvite should handle empty card user list when getting unopened users"() {
        given: "设置获取未开通用户时用户列表为空的场景"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: [])
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([])

        and: "Mock用户列表为空"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该直接返回成功"
        result.isSuccess()
    }

    def "userCardStatistic should handle case when openCardUser greater than totalUser"() {
        given: "设置开通用户数大于总用户数的异常场景"
        def arg = new UserCardStatisticArg(fsEa: "test_ea")
        def container = new CardUserStatisticContainer()
        container.setOpenCardUser(10)
        container.setNotOpenCardUser(0)
        container.setTotalUser(5)  // 异常：总数小于开通数

        and: "Mock统计容器"
        qywxUserManager.getOpenCardUserStatistic("test_ea") >> container

        when: "调用用户名片统计方法"
        def result = qywxStaffServiceImpl.userCardStatistic(arg)

        then: "应该返回默认统计值"
        result.isSuccess()
        result.data.bindUserCount == 0
        result.data.unOpenUserCount == 0
        result.data.openRate == "0%"
    }

    def "queryUserCardDetail should handle empty cardUserDetailList"() {
        given: "设置用户详情列表为空的场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([])

        and: "Mock空的用户详情列表"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该返回空结果"
        result.isSuccess()
        result.data.totalCount == 0
        result.data.result.isEmpty()
    }

    def "queryUserCardDetail should handle filtered empty result after employee name filter"() {
        given: "设置员工姓名过滤后结果为空的场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status,
                employeeName: "NonExistent"
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "TestUser",
                isOpen: UserCardOpenStatusEnum.OPEN.status
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock过滤后为空"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该返回空结果"
        result.isSuccess()
        result.data.totalCount == 0
        result.data.result.isEmpty()
    }

    // ===================== 额外的边界条件测试 =====================

    def "initQywxActivatedAccount should handle both appInfo and corpAgent entities"() {
        given: "设置同时存在appInfo和corpAgent实体的场景"
        def eaList = ["test_ea"]
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def corpAgentEntity = new QywxCorpAgentConfigEntity(ea: "test_ea", corpid: "corp456")

        and: "Mock两种实体都存在"
        qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList) >> [appInfoEntity]
        qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList) >> [corpAgentEntity]

        when: "调用初始化企微激活账户方法"
        def result = qywxStaffServiceImpl.initQywxActivatedAccount(eaList)

        then: "应该处理两种类型的实体"
        result.isSuccess()
    }

    def "queryQywxStaffPage should handle null bindCrmStatus correctly"() {
        given: "设置bindCrmStatus为null的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: null
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")
        def virtualFsUser = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                crmBindTime: new Date()
        )

        and: "Mock null bindCrmStatus处理"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> [virtualFsUser]
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理null status"
        result.isSuccess()
        result.data.result[0].crmBindStatus == UserBindCrmStatusEnum.BIND.status
    }

    def "buildQywxStaffPageResult should handle empty QywxEmployeeResult list"() {
        given: "设置员工结果列表为空的场景"
        def arg = new QueryQywxStaffPageArg(fsEa: "test_ea")
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")

        and: "Mock空的员工结果列表"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理空员工结果"
        result.isSuccess()
        result.data.result[0].department == null
    }

    // ===================== 补充关键遗漏测试用例以达到100%覆盖率 =====================

    def "static initialization block should initialize TITLE_LIST correctly"() {
        when: "访问静态初始化的TITLE_LIST"
        def titleListField = QywxStaffServiceImpl.class.getDeclaredField("TITLE_LIST")
        titleListField.setAccessible(true)
        def titleList = titleListField.get(null)

        then: "应该正确初始化标题列表"
        titleList != null
        titleList.size() == 9
    }

    def "queryQywxStaffInTime should handle NullPointerException when agentConfig corpid is accessed before null check"() {
        given: "设置会导致NPE的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")

        and: "Mock会导致NPE的场景"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> null
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa(_, _) >> { throw new NullPointerException("corpid is null") }

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该捕获异常并返回null"
        result == null

        and: "应该释放锁"
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    def "queryQywxStaffPage should handle empty staff info list in data permission check"() {
        given: "设置数据权限检查中员工列表为空的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )

        and: "Mock数据权限开启但员工列表为空"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> true
        qywxManager.getAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> []
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正常处理空员工列表"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "buildQywxStaffPageResult should handle QR code entity with JSON userId format"() {
        given: "设置二维码实体userId为JSON格式的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")
        def qrCodeEntity = [
                getId: { -> "qr123" },
                getUserId: { -> "[\"user123\",\"user456\"]" } // JSON格式的userId
        ] as QywxAddFanQrCodeEntity

        and: "Mock带JSON格式userId的二维码"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> [qrCodeEntity]
        wechatFriendsRecordObjDescribeManager.getTotalCustomerCount("test_ea", ["qr123"]) >> ["qr123": 3]
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理JSON格式的userId"
        result.isSuccess()
        result.data.result[0].cardAddFriendCount == 0 // 因为userId不匹配，应该为0
    }

    def "exportQywxStaff should handle break in pagination loop when detail result is empty"() {
        given: "设置分页中间出现空结果的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )

        and: "Mock第一页有数据，第二页为空"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >>> [
                [createMockStaffPageDTO("user1", "User1")], // 第一页
                []  // 第二页为空，触发break
        ]
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该正确处理分页中的break逻辑"
        result.isSuccess()
    }

    def "buildDataList should handle all status combinations correctly"() {
        given: "设置所有状态组合的测试数据"
        def detailResultList = [
                // 激活状态为OPEN的情况
                new QueryQywxStaffPageResult(
                        name: "User1",
                        department: "Dept1",
                        openCardStatus: UserCardOpenStatusEnum.OPEN.status,
                        cardAddFriendCount: 10,
                        activatedStatus: AccountActivateStatusEnum.OPEN.status,
                        activatedTime: System.currentTimeMillis(),
                        expireTime: System.currentTimeMillis() + ********,
                        crmBindStatus: UserBindCrmStatusEnum.BIND.status,
                        crmBindTime: System.currentTimeMillis()
                ),
                // 所有null值的情况
                new QueryQywxStaffPageResult(
                        name: null,
                        department: null,
                        openCardStatus: null,
                        cardAddFriendCount: null,
                        activatedStatus: null,
                        activatedTime: null,
                        expireTime: null,
                        crmBindStatus: null,
                        crmBindTime: null
                )
        ]

        when: "调用buildDataList方法"
        def result = qywxStaffServiceImpl.buildDataList(detailResultList)

        then: "应该正确处理所有状态组合"
        result.size() == 2
        
        // 验证第一行数据（有值的情况）
        result[0][0] == "User1"
        result[0][1] == "Dept1"
        result[0][2] != ""  // openCardStatus不为空
        result[0][3] == 10
        result[0][4] != ""  // activatedStatus不为空
        result[0][5] != ""  // activatedTime不为空
        result[0][6] != ""  // expireTime不为空
        result[0][7] != ""  // crmBindStatus不为空
        result[0][8] != ""  // crmBindTime不为空
        
        // 验证第二行数据（null值的情况）
        result[1][0] == ""  // null name
        result[1][1] == ""  // null department
        result[1][3] == 0   // null cardAddFriendCount
        result[1][5] == ""  // null activatedTime
        result[1][6] == ""  // null expireTime
        result[1][8] == ""  // null crmBindTime
    }

    def "queryQywxStaffByConverUserId should handle account with null expireTime"() {
        given: "设置账户过期时间为null的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.OPEN.status
        )
        def activatedAccount = new QywxActivatedAccountEntity(
                userId: "encrypted_user123",
                expireTime: null  // 过期时间为null
        )

        and: "Mock账户过期时间为null"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> [activatedAccount]
        qywxUserManager.getOpenUserIdToUserIdMap("test_ea") >> ["encrypted_user123": "user123"]
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123"]

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理null过期时间"
        result.isSuccess()
        result.data.totalCount == 0  // 因为过期时间为null，不会被认为是激活状态
    }

    def "buildQywxStaffPageResult should handle virtual fs user without crmBindTime"() {
        given: "设置虚拟用户没有CRM绑定时间的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: UserBindCrmStatusEnum.BIND.status
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")
        def virtualFsUser = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                crmBindTime: null  // CRM绑定时间为null
        )

        and: "Mock虚拟用户无CRM绑定时间"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> [virtualFsUser]
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理无CRM绑定时间"
        result.isSuccess()
        result.data.result[0].crmBindStatus == UserBindCrmStatusEnum.BIND.status
        result.data.result[0].crmBindTime == null
    }

    def "queryQywxStaffPage should handle bindCrmStatus equals to 1 (integer comparison)"() {
        given: "设置bindCrmStatus为整数1的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: 1  // 直接使用整数1而不是枚举
        )
        def virtualFsUser = new QywxVirtualFsUserEntity(qyUserId: "user123")

        and: "Mock bindCrmStatus为1的情况"
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123", "user456"]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123", "user456"]) >> [virtualFsUser]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理整数1的比较"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "exportQywxStaff should handle result with null data"() {
        given: "设置查询结果data为null的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )

        and: "Mock查询结果data为null"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> []

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该正常处理null data"
        result.isSuccess()
    }

    def "queryQywxStaffInTime should handle agentConfig with non-null secret"() {
        given: "设置agentConfig有secret的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")
        def agentConfig = new QywxCorpAgentConfigEntity(
                corpid: "corp123", 
                ea: "test_ea", 
                secret: "test_secret"  // secret不为null
        )
        def staffInfo = new DepartmentStaffResult.StaffInfo(userId: "user123", name: "TestUser")

        and: "Mock agentConfig有secret"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corp123", "test_ea") >> []
        qywxManager.getAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> [staffInfo]
        qywxUserManager.getUserIdByQyWxInfo("test_ea", "corp123", "user123", _) >> 456
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该正确处理有secret的配置"
        result.isSuccess()
        result.data.size() == 1

        and: "应该初始化部门和释放锁"
        1 * qywxDepartmentManager.initQywxDepartment("test_ea")
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    def "queryQywxStaffInTime should handle agentConfig with empty secret but non-empty appInfoEntities"() {
        given: "设置agentConfig secret为null但appInfoEntities不为空的场景"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea")
        def agentConfig = new QywxCorpAgentConfigEntity(
                corpid: "corp123", 
                ea: "test_ea", 
                secret: null  // secret为null
        )
        def appInfoEntity = new QywxCustomerAppInfoEntity(ea: "test_ea", corpId: "corp123")
        def staffInfo = new DepartmentStaffResult.StaffInfo(userId: "user123", name: "TestUser")

        and: "Mock secret为null但appInfo不为空"
        redisManager.lock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea", 600) >> true
        qywxCorpAgentConfigDAO.queryAgentByEa("test_ea") >> agentConfig
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa("corp123", "test_ea") >> [appInfoEntity]
        qywxManager.getAccessToken("test_ea") >> "access_token"
        qywxManager.queryAllStaff("test_ea", "access_token", false, false) >> [staffInfo]
        qywxUserManager.getUserIdByQyWxInfo("test_ea", "corp123", "user123", _) >> 456
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用实时查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffInTime(arg)

        then: "应该正确处理appInfo不为空的情况"
        result.isSuccess()
        result.data.size() == 1

        and: "应该初始化部门和释放锁"
        1 * qywxDepartmentManager.initQywxDepartment("test_ea")
        1 * redisManager.unLock("MARKETING_SYNC_QYWX_ADDRESS_BOOK_test_ea")
    }

    // ===================== 补充最后的细致测试用例 =====================

    def "buildQywxStaffPageResult should handle QR code with exact userId match"() {
        given: "设置二维码实体userId完全匹配的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")
        def qrCodeEntity = [
                getId: { -> "qr123" },
                getUserId: { -> "user123" } // 直接匹配的userId
        ] as QywxAddFanQrCodeEntity

        and: "Mock二维码实体完全匹配"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> [qrCodeEntity]
        wechatFriendsRecordObjDescribeManager.getTotalCustomerCount("test_ea", ["qr123"]) >> ["qr123": 5]
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确匹配二维码"
        result.isSuccess()
        result.data.result[0].cardAddFriendCount == 5
        result.data.result[0].qrCodeId == "qr123"
    }

    def "userCardStatistic should handle exact boundary case when totalUser equals openCardUser"() {
        given: "设置总用户数等于开通用户数的边界场景"
        def arg = new UserCardStatisticArg(fsEa: "test_ea")
        def container = new CardUserStatisticContainer()
        container.setOpenCardUser(100)
        container.setNotOpenCardUser(0)
        container.setTotalUser(100)  // 总数等于开通数

        and: "Mock边界统计容器"
        qywxUserManager.getOpenCardUserStatistic("test_ea") >> container

        when: "调用用户名片统计方法"
        def result = qywxStaffServiceImpl.userCardStatistic(arg)

        then: "应该正确计算边界统计值"
        result.isSuccess()
        result.data.bindUserCount == 100
        result.data.unOpenUserCount == 0
        result.data.openRate == "100%"
    }

    def "queryQywxStaffByConverUserId should handle activate status ALL"() {
        given: "设置激活状态为ALL的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.ALL.status
        )
        def activatedAccount = new QywxActivatedAccountEntity(
                userId: "encrypted_user123",
                expireTime: (System.currentTimeMillis() + ********) / 1000
        )

        and: "Mock激活状态为ALL"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> [activatedAccount]
        qywxUserManager.getOpenUserIdToUserIdMap("test_ea") >> ["encrypted_user123": "user123"]
        qywxAddressBookManager.getAllUserId("test_ea") >> ["user123"]
        qywxAddressBookManager.queryQywxStaffPageWithoutJoin(_, _) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该处理ALL状态"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "buildQywxStaffPageResult should handle activated time calculation"() {
        given: "设置激活时间计算的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10
        )
        def activatedTime = System.currentTimeMillis() / 1000
        def staffPageDTO = [
                getUserId: { -> "user123" },
                getName: { -> "TestUser" },
                getActivatedTime: { -> activatedTime },
                getExpireTime: { -> (System.currentTimeMillis() + ********) / 1000 }
        ] as QueryQywxStaffPageDTO

        and: "Mock有激活时间的DTO"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确计算激活时间"
        result.isSuccess()
        result.data.result[0].activatedTime == activatedTime * 1000
        result.data.result[0].activatedStatus == AccountActivateStatusEnum.OPEN.status
    }

    def "sendNoticeInvite should handle empty filtered user list after NOT_OPEN filter"() {
        given: "设置过滤后用户列表为空的场景"
        def arg = new SendNoticeInviteArg(fsEa: "test_ea", qyUserIds: [])
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "TestUser",
                department: '',
                isOpen: UserCardOpenStatusEnum.OPEN.status  // 都是已开通状态
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock过滤后为空的场景"
        marketingActivityRemoteManager.enterpriseStop("test_ea") >> false
        appVersionManager.getCurrentAppVersion("test_ea") >> "v1.0"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container

        when: "调用发送通知邀请方法"
        def result = qywxStaffServiceImpl.sendNoticeInvite(arg)

        then: "应该正常处理过滤后的空列表"
        result.isSuccess()
    }

    def "exportQywxStaff should handle exact page boundary calculation"() {
        given: "设置页面边界计算的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 1000
        )

        and: "Mock恰好1000条记录的场景"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> (1..1000).collect { i -> 
            createMockStaffPageDTO("user${i}", "User${i}")
        }
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该正确处理页面边界"
        result.isSuccess()
    }

    def "queryQywxStaffFromLocal should handle department filter edge case"() {
        given: "设置部门过滤边界情况"
        def arg = new QueryQywxStaffaArg(fsEa: "test_ea", fsUserId: 123, warmUpData: false)
        def staffInfo = new DepartmentStaffResult.StaffInfo(
                userId: "user123",
                name: "TestUser",
                department: [1001 + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID]  // 刚好匹配基础部门ID
        )
        def virtualFsUser = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                userId: 456
        )

        and: "Mock部门ID刚好匹配的场景"
        qywxManager.queryAllStaffFormDB("test_ea") >> [staffInfo]
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId("test_ea", 123, true) >> [1001]
        qywxUserManager.batchGetVirtualUserByQyUserIds("test_ea", ["user123"]) >> [virtualFsUser]
        fileV2Manager.replaceUrlToHttps(_) >> "https://avatar.jpg"

        when: "调用查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaff(arg)

        then: "应该正确处理部门匹配"
        result.isSuccess()
        result.data.size() == 1
    }

    def "buildQywxStaffPageResult should handle virtual fs user map key collision"() {
        given: "设置虚拟用户映射键冲突的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: UserBindCrmStatusEnum.ALL.status
        )
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")
        def virtualFsUser1 = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                crmBindTime: new Date(System.currentTimeMillis() - ********)
        )
        def virtualFsUser2 = new QywxVirtualFsUserEntity(
                qyUserId: "user123",  // 相同的qyUserId
                crmBindTime: new Date() // 更新的时间
        )

        and: "Mock虚拟用户键冲突"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> [virtualFsUser1, virtualFsUser2]
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> []

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理键冲突（后者覆盖前者）"
        result.isSuccess()
        result.data.result[0].crmBindStatus == UserBindCrmStatusEnum.BIND.status
        result.data.result[0].crmBindTime == virtualFsUser2.crmBindTime.time
    }

    def "queryQywxStaffByConverUserId should handle empty activated account entity list"() {
        given: "设置激活账户实体列表为空的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                activatedStatus: AccountActivateStatusEnum.OPEN.status
        )

        and: "Mock激活账户列表为空"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> false
        qywxActivatedAccountDAO.queryByEa("test_ea") >> []  // 空列表

        when: "调用分页查询企微员工方法"
        def result = qywxStaffServiceImpl.queryQywxStaffPage(arg)

        then: "应该正确处理空激活账户列表"
        result.isSuccess()
        result.data.totalCount == 0
    }

    def "buildDataList should handle edge case status values"() {
        given: "设置边界状态值的测试数据"
        def detailResultList = [
                new QueryQywxStaffPageResult(
                        name: "",  // 空字符串
                        department: "",  // 空字符串
                        openCardStatus: UserCardOpenStatusEnum.NOT_OPEN.status,
                        cardAddFriendCount: 0,
                        activatedStatus: AccountActivateStatusEnum.NOT_OPEN.status,
                        activatedTime: 0L,  // 时间戳为0
                        expireTime: 0L,     // 时间戳为0
                        crmBindStatus: UserBindCrmStatusEnum.NOT_BIND.status,
                        crmBindTime: 0L     // 时间戳为0
                )
        ]

        when: "调用buildDataList方法"
        def result = qywxStaffServiceImpl.buildDataList(detailResultList)

        then: "应该正确处理边界状态值"
        result.size() == 1
        result[0][0] == ""  // 空name
        result[0][1] == ""  // 空department
        result[0][3] == 0   // cardAddFriendCount为0
        result[0][5] != ""  // 时间戳0应该被格式化
        result[0][6] != ""  // 时间戳0应该被格式化
        result[0][8] != ""  // 时间戳0应该被格式化
    }

    // ===================== 针对性覆盖queryUserCardDetail方法的遗漏分支 =====================

    def "queryUserCardDetail should handle complete card user processing flow"() {
        given: "设置完整的用户名片详情处理流程"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 2,  // 第二页
                pageSize: 5,
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                employeeName: "张"
        )
        def cardUserInfo1 = new CardUserInfo(
                userId: "user123",
                userName: "张三",
                department: 'IT部门',
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                fsUserId: 456
        )
        def cardUserInfo2 = new CardUserInfo(
                userId: "user456",
                userName: "李四",
                department: 'HR部门',
                isOpen: UserCardOpenStatusEnum.NOT_OPEN.status,
                fsUserId: 789
        )
        def cardUserInfo3 = new CardUserInfo(
                userId: "user789",
                userName: "张五",
                department: 'Sales部门',
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                fsUserId: 101
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo1, cardUserInfo2, cardUserInfo3])
        def digitalHumansEntity = new DigitalHumansEntity(fsUserId: 456)

        and: "Mock完整的处理流程"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> true
        digitalHumansDAO.queryByEaAndUserIdList("test_ea", [456, 101], _) >> [digitalHumansEntity]

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该正确处理完整流程"
        result.isSuccess()
        // 应该过滤出isOpen为OPEN且姓名包含"张"的用户：张三、张五
        result.data.totalCount == 2
        // 由于pageSize=5，pageNum=2，应该没有数据
        result.data.result.isEmpty()
    }

    def "queryUserCardDetail should handle BeanUtil copy and manual pagination"() {
        given: "设置需要手动分页的大量数据"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 3,
                isOpen: UserCardOpenStatusEnum.ALL.status
        )
        def cardUserInfoList = (1..10).collect { i ->
            new CardUserInfo(
                    userId: "user${i}",
                    userName: "用户${i}",
                    department: '部门',
                    isOpen: UserCardOpenStatusEnum.OPEN.status,
                    fsUserId: i
            )
        }
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList(cardUserInfoList)

        and: "Mock手动分页处理"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> false

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该正确进行手动分页"
        result.isSuccess()
        result.data.totalCount == 10
        result.data.result.size() == 3  // 第一页应该有3条数据
        result.data.result[0].userId == "user1"
        result.data.result[2].userId == "user3"
    }

    def "queryUserCardDetail should handle employee name filtering with empty result"() {
        given: "设置员工姓名过滤后结果为空的完整场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status,
                employeeName: "不存在的姓名"
        )
        def cardUserInfo = new CardUserInfo(
                userId: "user123",
                userName: "张三",
                isOpen: UserCardOpenStatusEnum.OPEN.status
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo])

        and: "Mock姓名过滤后为空"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该在过滤后返回空结果"
        result.isSuccess()
        result.data.totalCount == 0
        result.data.result.isEmpty()
    }

    def "queryUserCardDetail should handle digitalHumans processing with real entities"() {
        given: "设置数字人处理的真实场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status
        )
        def cardUserInfo1 = new CardUserInfo(
                userId: "user123",
                userName: "用户1",
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                fsUserId: 456
        )
        def cardUserInfo2 = new CardUserInfo(
                userId: "user456",
                userName: "用户2",
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                fsUserId: 789
        )
        def container = new CardUserStatisticContainer()
        container.setCardUserDetailList([cardUserInfo1, cardUserInfo2])
        def digitalHumansEntity1 = new DigitalHumansEntity(fsUserId: 456)
        def digitalHumansEntity2 = new DigitalHumansEntity(fsUserId: 999) // 不匹配的ID

        and: "Mock数字人设置启用且有实体数据"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> true
        digitalHumansDAO.queryByEaAndUserIdList("test_ea", [456, 789], _) >> [digitalHumansEntity1, digitalHumansEntity2]

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该正确设置数字人状态"
        result.isSuccess()
        result.data.result.size() == 2
        result.data.result[0].digitalHumansStatus == true  // fsUserId=456匹配
        result.data.result[1].digitalHumansStatus == false // fsUserId=789不匹配digitalHumansEntity2
    }

    // ===================== 针对性覆盖exportQywxStaff方法的遗漏分支 =====================

    def "exportQywxStaff should handle complete Excel generation and push flow"() {
        given: "设置完整的Excel生成和推送流程"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 1000
        )
        def staffPageDTO1 = createMockStaffPageDTO("user1", "User1")
        def staffPageDTO2 = createMockStaffPageDTO("user2", "User2")
        def mockPage = new Page(1, 1000, true)
        mockPage.setTotalNum(2)

        and: "Mock Excel生成的完整流程"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO1, staffPageDTO2]
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { workbook, sheetName, ea, userId ->
            // 验证传入的参数
            assert workbook != null
            assert sheetName.contains(".xlsx")
            assert ea == "test_ea"
            assert userId == 123
        }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该完整执行Excel生成和推送流程"
        result.isSuccess()
        1 * pushSessionManager.pushExcelToFileAssistant(_, _, "test_ea", 123)
    }

    def "exportQywxStaff should handle multi-page Excel export with page break"() {
        given: "设置多页Excel导出的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 1000
        )
        // 模拟总共1500条数据，需要2页
        def firstPageData = (1..1000).collect { i -> createMockStaffPageDTO("user${i}", "User${i}") }
        def secondPageData = (1001..1500).collect { i -> createMockStaffPageDTO("user${i}", "User${i}") }
        def mockPage1 = new Page(1, 1000, true)
        mockPage1.setTotalNum(1500)
        def mockPage2 = new Page(2, 1000, true)
        mockPage2.setTotalNum(1500)

        and: "Mock多页查询数据"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >>> [firstPageData, secondPageData]
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该正确处理多页导出"
        result.isSuccess()
        // 验证调用了2次分页查询（第一页 + 第二页）
        1 * pushSessionManager.pushExcelToFileAssistant(_, _, "test_ea", 123)
    }

    def "exportQywxStaff should handle exact 1000 records single page scenario"() {
        given: "设置恰好1000条记录的单页场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 1000
        )
        def pageData = (1..1000).collect { i -> createMockStaffPageDTO("user${i}", "User${i}") }
        def mockPage = new Page(1, 1000, true)
        mockPage.setTotalNum(1000) // 恰好1000条，totalPage = 1

        and: "Mock恰好1000条数据"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> pageData
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该只处理单页不进入循环"
        result.isSuccess()
        1 * pushSessionManager.pushExcelToFileAssistant(_, _, "test_ea", 123)
    }

    def "exportQywxStaff should handle query failure in pagination loop"() {
        given: "设置分页循环中查询失败的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 1000
        )
        def firstPageData = (1..1000).collect { i -> createMockStaffPageDTO("user${i}", "User${i}") }
        def mockPage = new Page(1, 1000, true)
        mockPage.setTotalNum(1500) // 总共1500条需要2页

        and: "Mock第一页成功，第二页查询返回失败"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >>> [
                firstPageData,  // 第一页成功
                []              // 第二页返回空，会触发break
        ]
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该在第二页时break并完成导出"
        result.isSuccess()
        1 * pushSessionManager.pushExcelToFileAssistant(_, _, "test_ea", 123)
    }

    def "exportQywxStaff should handle I18n sheet name generation"() {
        given: "设置国际化表名生成的场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 1000
        )
        def staffPageDTO = createMockStaffPageDTO("user1", "User1")

        and: "Mock国际化和时间格式化"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", _) >> [:]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", _) >> []
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", _) >> []
        qywxEmployeeManager.batchByQyUserIds("test_ea", _, false) >> []
        pushSessionManager.pushExcelToFileAssistant(_, _, _, _) >> { workbook, sheetName, ea, userId ->
            // 验证表名包含时间戳和.xlsx后缀
            assert sheetName.endsWith(".xlsx")
            assert sheetName.length() > 10 // 应该包含I18n内容和时间戳
        }

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该正确生成国际化表名"
        result.isSuccess()
        1 * pushSessionManager.pushExcelToFileAssistant(_, _, "test_ea", 123)
    }

    def "Complete test for queryUserCardDetail with all CardUserDetailList scenarios"() {
        given: "设置完整的queryUserCardDetail测试场景"
        def arg = new QueryUserCardDetailArg(
                fsEa: "test_ea",
                pageNum: 1,
                pageSize: 10,
                isOpen: UserCardOpenStatusEnum.ALL.status,
                employeeName: "TestEmployee"
        )
        
        // 创建多个不同状态的用户
        def cardUserInfo1 = new CardUserInfo(
                userId: "user1",
                userName: "TestEmployee",
                isOpen: UserCardOpenStatusEnum.OPEN.status,
                fsUserId: 100
        )
        def cardUserInfo2 = new CardUserInfo(
                userId: "user2", 
                userName: "AnotherUser",
                isOpen: UserCardOpenStatusEnum.NOT_OPEN.status,
                fsUserId: 200
        )
        
        // 正确创建容器
        def container = new CardUserStatisticContainer()
        container.setTotalUser(2)
        container.setOpenCardUser(1)
        container.setNotOpenCardUser(1)
        container.setCardUserDetailList([cardUserInfo1, cardUserInfo2])
        
        def digitalHumansEntity = new DigitalHumansEntity(fsUserId: 100)

        and: "Mock所有依赖"
        qywxUserManager.getOpenCardUserDetail("test_ea") >> container
        digitalHumansManager.getDigitalHumansSetting("test_ea") >> true
        digitalHumansDAO.queryByEaAndUserIdList("test_ea", [100, 200], _) >> [digitalHumansEntity]

        when: "调用查询用户名片详情方法"
        def result = qywxStaffServiceImpl.queryUserCardDetail(arg)

        then: "应该正确处理过滤逻辑"
        result.isSuccess()
        result.data.totalCount == 1  // 只有名字匹配的用户
        result.data.result.size() == 1
        result.data.result[0].userId == "user1"
        result.data.result[0].digitalHumansStatus == true
    }

    def "Complete test for exportQywxStaff with full coverage"() {
        given: "设置完整的导出测试场景"
        def arg = new QueryQywxStaffPageArg(
                fsEa: "test_ea",
                fsUserId: 123,
                pageNum: 1,
                pageSize: 10,
                bindCrmStatus: UserBindCrmStatusEnum.ALL.status,
                openCardStatus: UserCardOpenStatusEnum.ALL.status,
                activatedStatus: AccountActivateStatusEnum.ALL.status,
                employeeName: "",
                excludeResignedEmployee: false
        )
        
        // 创建测试数据
        def staffPageDTO = createMockStaffPageDTO("user123", "TestUser")
        def virtualFsUser = new QywxVirtualFsUserEntity(
                qyUserId: "user123",
                crmBindTime: new Date()
        )
        def qrCodeEntity = createMockQrCodeEntity("user123")
        def employeeResult = createMockEmployeeResult("user123", "Test Department")

        and: "Mock所有相关依赖"
        dataPermissionManager.getNewDataPermissionSetting("test_ea") >> false
        qywxManager.isNewInstallAgentApp("test_ea") >> true
        qyWxAddressBookDAO.queryQywxStaffCount(_) >> 1
        qyWxAddressBookDAO.queryQywxStaffPage(_, _) >> [staffPageDTO]
        qywxUserManager.getOpenCardUserIdMap("test_ea", ["user123"]) >> ["user123": 456]
        qywxVirtualFsUserManager.queryBindCrmQyUserId("test_ea", ["user123"]) >> [virtualFsUser]
        qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList("test_ea", ["user123"]) >> [qrCodeEntity]
        wechatFriendsRecordObjDescribeManager.getTotalCustomerCount("test_ea", ["qr123"]) >> ["qr123": 5]
        qywxEmployeeManager.batchByQyUserIds("test_ea", ["user123"], false) >> [employeeResult]
        qywxActivatedAccountDAO.batchQueryByUserIds("test_ea", ["user123"]) >> []

        when: "调用导出企微员工方法"
        def result = qywxStaffServiceImpl.exportQywxStaff(arg)

        then: "应该成功处理导出逻辑，包括I18n字符串获取"
        result.isSuccess()
        result.data != null
    }

    def "Test all remaining CardUserStatisticContainer scenarios"() {
        given: "测试各种容器状态"
        
        // 空容器测试
        def emptyContainer = new CardUserStatisticContainer()
        emptyContainer.setTotalUser(0)
        emptyContainer.setOpenCardUser(0)
        emptyContainer.setNotOpenCardUser(0)
        emptyContainer.setCardUserDetailList([])
        
        // 非空容器测试
        def cardUserInfo = new CardUserInfo(
                userId: "test_user",
                userName: "TestUser",
                isOpen: UserCardOpenStatusEnum.OPEN.status
        )
        
        def nonEmptyContainer = new CardUserStatisticContainer()
        nonEmptyContainer.setTotalUser(1)
        nonEmptyContainer.setOpenCardUser(1)
        nonEmptyContainer.setNotOpenCardUser(0)
        nonEmptyContainer.setCardUserDetailList([cardUserInfo])

        expect: "容器应该被正确创建"
        emptyContainer.getCardUserDetailList().isEmpty()
        nonEmptyContainer.getCardUserDetailList().size() == 1
    }

} 