package com.facishare.marketing.provider.manager.customizedSpread

import com.facishare.marketing.api.arg.GetMarketingActivityDetailData
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg
import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPreviewArg
import com.facishare.marketing.api.data.MarketingUserGroupData
import com.facishare.marketing.api.result.MarketingMessageSendCountResult
import com.facishare.marketing.api.result.MobileMarketingUserResult
import com.facishare.marketing.common.enums.MarketingMessageSendRecordSendStatusEnum
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum
import com.facishare.marketing.common.enums.whatsapp.SendTypeEnum
import com.facishare.marketing.common.enums.whatsapp.TaskSendStatusEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao
import com.facishare.marketing.provider.dao.customizedSpread.CustomizedSpreadChannelDAO
import com.facishare.marketing.provider.dao.customizedSpread.CustomizedSpreadSendTaskDAO
import com.facishare.marketing.provider.dto.MarketingUserWithEmail
import com.facishare.marketing.provider.entity.ExternalConfig
import com.facishare.marketing.provider.entity.customizedSpread.CustomizedSpreadChannelEntity
import com.facishare.marketing.provider.entity.customizedSpread.CustomizedSpreadSendTaskEntity
import com.facishare.marketing.provider.entity.data.CustomizedSpreadSendData
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.innerResult.crm.ExecuteCustomizeFunctionResult
import com.facishare.marketing.provider.manager.CustomizeFunctionManager
import com.facishare.marketing.provider.manager.MarketingUserGroupManager
import com.facishare.marketing.provider.manager.NoticeManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingMessageSendRecordObjManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingCrmManager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Test for CustomizedSpreadManager
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
class CustomizedSpreadManagerTest extends Specification {

    def customizedSpreadManager = new CustomizedSpreadManager()

    def customizedSpreadChannelDAO = Mock(CustomizedSpreadChannelDAO)
    def marketingActivityAuditManager = Mock(MarketingActivityAuditManager)
    def marketingActivityCrmManager = Mock(MarketingCrmManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def customizedSpreadSendTaskDAO = Mock(CustomizedSpreadSendTaskDAO)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def marketingUserGroupManager = Mock(MarketingUserGroupManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def objectManager = Mock(ObjectManager)
    def customizeFunctionManager = Mock(CustomizeFunctionManager)
    def marketingMessageSendRecordObjManager = Mock(MarketingMessageSendRecordObjManager)
    def noticeManager = Mock(NoticeManager)

    def setup() {
        customizedSpreadManager.customizedSpreadChannelDAO = customizedSpreadChannelDAO
        customizedSpreadManager.marketingActivityAuditManager = marketingActivityAuditManager
        customizedSpreadManager.marketingActivityCrmManager = marketingActivityCrmManager
        customizedSpreadManager.crmV2Manager = crmV2Manager
        customizedSpreadManager.customizedSpreadSendTaskDAO = customizedSpreadSendTaskDAO
        customizedSpreadManager.marketingActivityExternalConfigDao = marketingActivityExternalConfigDao
        customizedSpreadManager.marketingUserGroupManager = marketingUserGroupManager
        customizedSpreadManager.crmMetadataManager = crmMetadataManager
        customizedSpreadManager.objectManager = objectManager
        customizedSpreadManager.customizeFunctionManager = customizeFunctionManager
        customizedSpreadManager.marketingMessageSendRecordObjManager = marketingMessageSendRecordObjManager
        customizedSpreadManager.noticeManager = noticeManager
    }

    @Unroll
    def "doAddAction - 参数检验失败"() {
        given:
        def arg = new AddMarketingActivityArg()
        arg.customizedSpreadArg = customizedSpreadArg

        expect:
        def result = customizedSpreadManager.doAddAction("ea", 1, arg)
        result.errorCode == expectedErrCode

        where:
        customizedSpreadArg || expectedErrCode
        null || SHErrorCode.PARAMS_ERROR
        new AddMarketingActivityArg.CustomizedSpreadArg(sendType: "INVALID") || SHErrorCode.SEND_TYPE_CAN_NOT_NULL
        new AddMarketingActivityArg.CustomizedSpreadArg(sendType: SendTypeEnum.SCHEDULED.value, sendTime: null) || SHErrorCode.SEND_TIME_CAN_NOT_NULL
        new AddMarketingActivityArg.CustomizedSpreadArg(sendType: SendTypeEnum.IMMEDIATELY.value, marketingUserGroupIds: null) || SHErrorCode.PARAMS_ERROR
        new AddMarketingActivityArg.CustomizedSpreadArg(sendType: SendTypeEnum.IMMEDIATELY.value, marketingUserGroupIds: []) || SHErrorCode.PARAMS_ERROR
    }

    @Unroll
    def "doAddAction - 创建营销活动失败"() {
        given:
        def arg = new AddMarketingActivityArg()
        arg.customizedSpreadArg = new AddMarketingActivityArg.CustomizedSpreadArg(
            sendType: SendTypeEnum.IMMEDIATELY.value,
            marketingUserGroupIds: ["group1"],
            customizedSpreadChannelId: "channel1"
        )
        
        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity()
        objectManager.getObjectName(*_) >> "testContent"
        marketingActivityCrmManager.addMarketingActivity(*_) >> Result.newError(SHErrorCode.SYSTEM_ERROR)

        expect:
        def result = customizedSpreadManager.doAddAction("ea", 1, arg)
        result.crmErrorCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    @Unroll
    def "doAddAction - 成功创建立即发送"() {
        given:
        def arg = new AddMarketingActivityArg()
        arg.customizedSpreadArg = new AddMarketingActivityArg.CustomizedSpreadArg(
            sendType: SendTypeEnum.IMMEDIATELY.value,
            marketingUserGroupIds: ["group1"],
            customizedSpreadChannelId: "channel1",
            objectId: "obj1",
            objectType: 1
        )
        arg.marketingEventId = "event1"

        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(spreadTypeOptionValue: "option1")
        objectManager.getObjectName(*_) >> "testContent"
        marketingActivityCrmManager.addMarketingActivity(*_) >> Result.newSuccess("activity1")
        marketingActivityAuditManager.isNeedAudit(*_) >> false
        customizedSpreadSendTaskDAO.insert(*_) >> 1
        marketingActivityExternalConfigDao.insert(*_) >> 1

        def spy = Spy(customizedSpreadManager)
        spy.sendMessageByTaskId(*_) >> Result.newSuccess()

        expect:
        def result = spy.doAddAction("ea", 1, arg)
        result.marketingActivityId == "activity1"
    }

    @Unroll
    def "doAddAction - 成功创建定时发送"() {
        given:
        def arg = new AddMarketingActivityArg()
        arg.customizedSpreadArg = new AddMarketingActivityArg.CustomizedSpreadArg(
            sendType: SendTypeEnum.SCHEDULED.value,
            sendTime: System.currentTimeMillis() + 3600000,
            marketingUserGroupIds: ["group1"],
            customizedSpreadChannelId: "channel1",
            objectId: "obj1",
            objectType: 1
        )
        arg.marketingEventId = "event1"

        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(spreadTypeOptionValue: "option1")
        objectManager.getObjectName(*_) >> "testContent"
        marketingActivityCrmManager.addMarketingActivity(*_) >> Result.newSuccess("activity1")
        marketingActivityAuditManager.isNeedAudit(*_) >> false
        customizedSpreadSendTaskDAO.insert(*_) >> 1
        marketingActivityExternalConfigDao.insert(*_) >> 1

        expect:
        def result = customizedSpreadManager.doAddAction("ea", 1, arg)
        result.marketingActivityId == "activity1"
    }

    @Unroll
    def "doAddAction - 创建过程中异常"() {
        given:
        def arg = new AddMarketingActivityArg()
        arg.customizedSpreadArg = new AddMarketingActivityArg.CustomizedSpreadArg(
            sendType: SendTypeEnum.IMMEDIATELY.value,
            marketingUserGroupIds: ["group1"],
            customizedSpreadChannelId: "channel1",
            objectId: "obj1",
            objectType: 1
        )
        arg.marketingEventId = "event1"

        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(spreadTypeOptionValue: "option1")
        objectManager.getObjectName(*_) >> "testContent"
        marketingActivityCrmManager.addMarketingActivity(*_) >> Result.newSuccess("activity1")
        marketingActivityAuditManager.isNeedAudit(*_) >> false
        customizedSpreadSendTaskDAO.insert(*_) >> { throw new RuntimeException("Database error") }
        crmV2Manager.bulkInvalidIgnoreError(*_) >> null

        expect:
        def result = customizedSpreadManager.doAddAction("ea", 1, arg)
        result.errorCode == SHErrorCode.SYSTEM_ERROR
    }

    @Unroll
    def "sendMessageByTaskId - 任务不存在"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> null

        expect:
        def result = customizedSpreadManager.sendMessageByTaskId("ea", "taskId")
        result.errCode == SHErrorCode.NO_DATA.errorCode
    }

    @Unroll
    def "sendMessageByTaskId - 任务状态不是等待"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.SENT.status,
            createBy: 1
        )

        expect:
        def result = customizedSpreadManager.sendMessageByTaskId("ea", "taskId")
        result.errCode == SHErrorCode.PARAMS_ERROR.errorCode
    }

    @Unroll
    def "sendMessageByTaskId - 更新状态失败"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 0

        expect:
        def result = customizedSpreadManager.sendMessageByTaskId("ea", "taskId")
        result.success
    }

    @Unroll
    def "sendMessageByTaskId - 配置不存在"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 1
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> null

        expect:
        def result = customizedSpreadManager.sendMessageByTaskId("ea", "taskId")
        result.errCode == SHErrorCode.NO_DATA.errorCode
    }

    @Unroll
    def "sendMessageByTaskId - 推广数据为空"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 1
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(
            marketingActivityId: "activity1",
            marketingEventId: "event1",
            externalConfig: new ExternalConfig()
        )

        expect:
        def result = customizedSpreadManager.sendMessageByTaskId("ea", "taskId")
        result.errCode == SHErrorCode.NO_DATA.errorCode
    }

    @Unroll
    def "sendMessageByTaskId - 渠道不存在"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 1
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(
            marketingActivityId: "activity1",
            marketingEventId: "event1",
            externalConfig: new ExternalConfig(customizedSpreadSendData: new CustomizedSpreadSendData(
                customizedSpreadChannelId: "channel1"
            ))
        )
        customizedSpreadChannelDAO.getById(*_) >> null

        expect:
        def result = customizedSpreadManager.sendMessageByTaskId("ea", "taskId")
        result.errCode == SHErrorCode.NO_DATA.errorCode
    }

    @Unroll
    def "sendMessageByTaskId - 发送对象格式错误"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 1
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(
            marketingActivityId: "activity1",
            marketingEventId: "event1",
            externalConfig: new ExternalConfig(customizedSpreadSendData: new CustomizedSpreadSendData(
                customizedSpreadChannelId: "channel1",
                marketingUserGroupIds: ["group1"],
                objectId: "obj1",
                objectType: 1
            ))
        )
        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(
            funcApiName: "func1",
            name: "channel1",
            sendObject: "invalid_format"
        )

        expect:
        def result = customizedSpreadManager.sendMessageByTaskId("ea", "taskId")
        result.errCode == SHErrorCode.PARAMS_ERROR.errorCode
    }

    @Unroll
    def "sendMessageByTaskId - 成功发送"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 1
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(
            marketingActivityId: "activity1",
            marketingEventId: "event1",
            externalConfig: new ExternalConfig(customizedSpreadSendData: new CustomizedSpreadSendData(
                customizedSpreadChannelId: "channel1",
                marketingUserGroupIds: ["group1"],
                objectId: "obj1",
                objectType: 1
            ))
        )
        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(
            funcApiName: "func1",
            name: "channel1",
            sendObject: "MARKETING_USER.phone"
        )
        objectManager.getObjectName(*_) >> "testContent"
        objectManager.getObjectCover(*_) >> "cover.jpg"

        def spy = Spy(customizedSpreadManager)
        spy.getSendTargetData(*_) >> [new CustomizedSpreadManager.SendTargetData("123456", null, "user1")]

        customizeFunctionManager.executeCustomizeFunctionV2(*_) >> new ExecuteCustomizeFunctionResult(
            result: [sendStatus: MarketingMessageSendRecordSendStatusEnum.SEND_SUCCESS.status]
        )
        customizedSpreadSendTaskDAO.updateStatus(*_) >> 1
        crmV2Manager.updateMarketingActivityObj(*_) >> null
        marketingMessageSendRecordObjManager.createObj(*_) >> null

        expect:
        def result = spy.sendMessageByTaskId("ea", "taskId")
        result.success
    }

    @Unroll
    def "sendMessageByTaskId - 函数调用失败"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 1
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(
            marketingActivityId: "activity1",
            marketingEventId: "event1",
            externalConfig: new ExternalConfig(customizedSpreadSendData: new CustomizedSpreadSendData(
                customizedSpreadChannelId: "channel1",
                marketingUserGroupIds: ["group1"],
                objectId: "obj1",
                objectType: 1
            ))
        )
        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(
            funcApiName: "func1",
            name: "channel1",
            sendObject: "MARKETING_USER.phone"
        )
        objectManager.getObjectName(*_) >> "testContent"
        objectManager.getObjectCover(*_) >> "cover.jpg"

        def spy = Spy(customizedSpreadManager)
        spy.getSendTargetData(*_) >> [new CustomizedSpreadManager.SendTargetData("123456", null, "user1")]

        customizeFunctionManager.executeCustomizeFunctionV2(*_) >> new ExecuteCustomizeFunctionResult(
            errCode: SHErrorCode.SYSTEM_ERROR.errorCode,
            errMessage: "Function error"
        )
        customizedSpreadSendTaskDAO.updateStatus(*_) >> 1
        crmV2Manager.updateMarketingActivityObj(*_) >> null

        expect:
        def result = spy.sendMessageByTaskId("ea", "taskId")
        result.errCode == SHErrorCode.SYSTEM_ERROR.errorCode
    }

    @Unroll
    def "getSendTargetData - 营销用户手机号"() {
        given:
        marketingUserGroupManager.listMobileByMarketingUserGroupIds(*_) >> [
            new MobileMarketingUserResult(mobile: "123456", marketingUserId: "user1"),
            new MobileMarketingUserResult(mobile: "789012", marketingUserId: "user2")
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetData("ea", "MARKETING_USER", "phone", ["group1"])
        result.size() == 2
        result[0].receiveAccount == "123456"
        result[0].marketingUserId == "user1"
    }

    @Unroll
    def "getSendTargetData - 营销用户邮箱"() {
        given:
        marketingUserGroupManager.listEmailByMarketingUserGroupIds(*_) >> Sets.newHashSet([
            new MarketingUserWithEmail("user1", "<EMAIL>"),
            new MarketingUserWithEmail("user2", "<EMAIL>")
        ])

        expect:
        def result = customizedSpreadManager.getSendTargetData("ea", "MARKETING_USER", "email", ["group1"])
        result.size() == 2
    }

    @Unroll
    def "getSendTargetData - 营销用户无效字段"() {
        expect:
        def result = customizedSpreadManager.getSendTargetData("ea", "MARKETING_USER", "invalid", ["group1"])
        result == null
    }

    @Unroll
    def "getSendTargetData - 营销用户手机号为空"() {
        given:
        marketingUserGroupManager.listMobileByMarketingUserGroupIds(*_) >> []

        expect:
        def result = customizedSpreadManager.getSendTargetData("ea", "MARKETING_USER", "phone", ["group1"])
        result.isEmpty()
    }

    @Unroll
    def "getSendTargetData - CRM对象"() {
        given:
        def spy = Spy(customizedSpreadManager)
        spy.getSendTargetDataByCrmObj(*_) >> [
            new CustomizedSpreadManager.SendTargetData("<EMAIL>", "obj1", null)
        ]

        expect:
        def result = spy.getSendTargetData("ea", "CrmLead", "email", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "<EMAIL>"
        result[0].objectId == "obj1"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 线索对象"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1", "user2"])
        marketingUserGroupManager.doListCrmLeadIdsByMarketingUserIds(*_) >> ["lead1", "lead2"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "lead1", email: "<EMAIL>"]),
            new ObjectData([_id: "lead2", email: "<EMAIL>"])
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.CRM_LEAD.name, "email", ["group1"])
        result.size() == 2
        result[0].receiveAccount == "<EMAIL>"
        result[0].objectId == "lead1"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 客户对象"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmCustomerIdsByMarketingUserIds(*_) >> ["customer1"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "customer1", phone: "123456"])
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.CUSTOMER.name, "phone", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "123456"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 联系人对象"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmContactIdsByMarketingUserIds(*_) >> ["contact1"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "contact1", email: "<EMAIL>"])
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.CONTACT.name, "email", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "<EMAIL>"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 微信用户对象"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmWxUserByMarketingUserIds(*_) >> ["wx1"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "wx1", openid: "openid123"])
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.WECHAT.name, "openid", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "openid123"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 企微客户对象"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmWxWorkUserIdsByMarketingUserIds(*_) >> ["wxwork1"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "wxwork1", external_userid: "external123"])
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.name, "external_userid", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "external123"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 会员对象"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmMemberIdsByMarketingUserIds(*_) >> ["member1"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "member1", phone: "123456"])
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.MEMBER.name, "phone", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "123456"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 自定义对象"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmCustomizeObjectIdsByMarketingUserIds(*_) >> ["custom1"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "custom1", custom_field: "custom_value"])
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", "CustomObject", "custom_field", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "custom_value"
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 营销用户ID为空"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet()

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", "CrmLead", "email", ["group1"])
        result.isEmpty()
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 对象ID为空"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmLeadIdsByMarketingUserIds(*_) >> []

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.CRM_LEAD.name, "email", ["group1"])
        result.isEmpty()
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 对象数据为空"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmLeadIdsByMarketingUserIds(*_) >> ["lead1"]
        crmMetadataManager.batchGetByIdsV3(*_) >> []

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.CRM_LEAD.name, "email", ["group1"])
        result.isEmpty()
    }

    @Unroll
    def "getSendTargetDataByCrmObj - 过滤空字段值"() {
        given:
        marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(*_) >> Sets.newHashSet(["user1"])
        marketingUserGroupManager.doListCrmLeadIdsByMarketingUserIds(*_) >> ["lead1", "lead2"]
        crmMetadataManager.batchGetByIdsV3(*_) >> [
            new ObjectData([_id: "lead1", email: "<EMAIL>"]),
            new ObjectData([_id: "lead2", email: ""])  // 空字段值应该被过滤
        ]

        expect:
        def result = customizedSpreadManager.getSendTargetDataByCrmObj("ea", CrmObjectApiNameEnum.CRM_LEAD.name, "email", ["group1"])
        result.size() == 1
        result[0].receiveAccount == "<EMAIL>"
    }

    @Unroll
    def "doDetailAction - 成功获取详情"() {
        given:
        def detailData = new GetMarketingActivityDetailData(
            id: "activity1",
            name: "测试活动",
            spreadType: 14,
            status: 1,
            marketingEventId: "event1"
        )

        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity(
            associateId: "task1",
            externalConfig: new ExternalConfig(customizedSpreadSendData: new CustomizedSpreadSendData(
                marketingUserGroupIds: ["group1", "group2"]
            ))
        )

        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            id: "task1",
            customizedSpreadChannelId: "channel1",
            sendTime: new Date(),
            sendStatus: TaskSendStatusEnum.WAIT.status,
            sendType: SendTypeEnum.IMMEDIATELY.value
        )

        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(
            id: "channel1",
            name: "测试渠道"
        )

        noticeManager.getMarketingMessageSendCount(*_) >> new MarketingMessageSendCountResult(
            sendCount: 100,
            successCount: 80,
            failCount: 20
        )

        marketingUserGroupManager.batchGetMarketingUserGroupInfo(*_) >> [
            new MarketingUserGroupData(id: "group1", name: "群组1"),
            new MarketingUserGroupData(id: "group2", name: "群组2")
        ]

        expect:
        def result = customizedSpreadManager.doDetailAction("ea", 1, detailData)
        result.id == "activity1"
        result.name == "测试活动"
        result.customizedSpreadDetailResult.taskId == "task1"
        result.customizedSpreadDetailResult.channelName == "测试渠道"
        result.customizedSpreadDetailResult.sendCount == 100
        result.customizedSpreadDetailResult.successCount == 80
        result.customizedSpreadDetailResult.failCount == 20
        result.customizedSpreadDetailResult.marketingUserGroupList.size() == 2
    }

    @Unroll
    def "doDetailAction - 配置不存在"() {
        given:
        def detailData = new GetMarketingActivityDetailData(
            id: "activity1",
            name: "测试活动",
            spreadType: 14,
            status: 1,
            marketingEventId: "event1"
        )

        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> null

        expect:
        def result = customizedSpreadManager.doDetailAction("ea", 1, detailData)
        result.id == "activity1"
        result.name == "测试活动"
        result.customizedSpreadDetailResult == null
    }

    @Unroll
    def "doDetailAction - 任务不存在"() {
        given:
        def detailData = new GetMarketingActivityDetailData(
            id: "activity1",
            name: "测试活动",
            spreadType: 14,
            status: 1,
            marketingEventId: "event1"
        )

        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity(
            associateId: "task1"
        )
        customizedSpreadSendTaskDAO.getById(*_) >> null

        expect:
        def result = customizedSpreadManager.doDetailAction("ea", 1, detailData)
        result.id == "activity1"
        result.customizedSpreadDetailResult == null
    }

    @Unroll
    def "doDetailAction - 渠道不存在"() {
        given:
        def detailData = new GetMarketingActivityDetailData(
            id: "activity1",
            name: "测试活动",
            spreadType: 14,
            status: 1,
            marketingEventId: "event1"
        )

        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity(
            associateId: "task1"
        )
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            customizedSpreadChannelId: "channel1"
        )
        customizedSpreadChannelDAO.getById(*_) >> null

        expect:
        def result = customizedSpreadManager.doDetailAction("ea", 1, detailData)
        result.id == "activity1"
        result.customizedSpreadDetailResult == null
    }

    @Unroll
    def "doDeleteAction - 返回null"() {
        expect:
        def result = customizedSpreadManager.doDeleteAction("ea", 1, "activity1")
        result == null
    }

    @Unroll
    def "doUpdateAction - 返回null"() {
        expect:
        def result = customizedSpreadManager.doUpdateAction("ea", 1, new AddMarketingActivityArg())
        result == null
    }

    @Unroll
    def "getMarketingActivityAuditData - 返回null"() {
        expect:
        def result = customizedSpreadManager.getMarketingActivityAuditData("ea", 1, new AddMarketingActivityArg())
        result == null
    }

    @Unroll
    def "getPreviewData - 返回null"() {
        expect:
        def result = customizedSpreadManager.getPreviewData("ea", 1, new MarketingActivityPreviewArg())
        result == null
    }

    @Unroll
    def "sendMessageByTaskId - 发送失败状态处理"() {
        given:
        customizedSpreadSendTaskDAO.getById(*_) >> new CustomizedSpreadSendTaskEntity(
            sendStatus: TaskSendStatusEnum.WAIT.status,
            createBy: 1
        )
        customizedSpreadSendTaskDAO.updateSendingStatus(*_) >> 1
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(
            marketingActivityId: "activity1",
            marketingEventId: "event1",
            externalConfig: new ExternalConfig(customizedSpreadSendData: new CustomizedSpreadSendData(
                customizedSpreadChannelId: "channel1",
                marketingUserGroupIds: ["group1"],
                objectId: "obj1",
                objectType: 1
            ))
        )
        customizedSpreadChannelDAO.getById(*_) >> new CustomizedSpreadChannelEntity(
            funcApiName: "func1",
            name: "channel1",
            sendObject: "MARKETING_USER.phone"
        )
        objectManager.getObjectName(*_) >> "testContent"
        objectManager.getObjectCover(*_) >> "cover.jpg"

        def spy = Spy(customizedSpreadManager)
        spy.getSendTargetData(*_) >> [new CustomizedSpreadManager.SendTargetData("123456", null, "user1")]

        customizeFunctionManager.executeCustomizeFunctionV2(*_) >> new ExecuteCustomizeFunctionResult(
            result: [sendStatus: MarketingMessageSendRecordSendStatusEnum.SEND_FAILURE.status]
        )
        customizedSpreadSendTaskDAO.updateStatus(*_) >> 1
        crmV2Manager.updateMarketingActivityObj(*_) >> null
        marketingMessageSendRecordObjManager.createObj(*_) >> null

        expect:
        def result = spy.sendMessageByTaskId("ea", "taskId")
        result.success
    }
}
