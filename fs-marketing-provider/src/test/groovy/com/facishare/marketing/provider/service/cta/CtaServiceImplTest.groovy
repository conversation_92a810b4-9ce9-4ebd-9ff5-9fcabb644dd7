package com.facishare.marketing.provider.service.cta

import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg
import com.facishare.marketing.api.arg.cta.AddCtaArg
import com.facishare.marketing.api.arg.cta.CreateCtaQywxQrCodeArg
import com.facishare.marketing.api.arg.cta.CreateCtaWxQrCodeArg
import com.facishare.marketing.api.arg.cta.DeleteCtaArg
import com.facishare.marketing.api.arg.cta.ListCtaArg
import com.facishare.marketing.api.arg.cta.PollingQrCodeStatusArg
import com.facishare.marketing.api.arg.cta.QueryCtaDetailArg
import com.facishare.marketing.api.arg.cta.QueryCtaNameArg
import com.facishare.marketing.api.arg.cta.QueryCtaRelationCountArg
import com.facishare.marketing.api.arg.cta.QueryCtaSimpleDetailArg
import com.facishare.marketing.api.arg.cta.UpdateCtaStatusArg
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg
import com.facishare.marketing.api.result.EditObjectGroupResult
import com.facishare.marketing.api.result.ListObjectGroupResult
import com.facishare.marketing.api.result.cta.AddCtaResult
import com.facishare.marketing.api.result.cta.CreateCtaWxQrCodeResult
import com.facishare.marketing.api.result.cta.CtaListResult
import com.facishare.marketing.api.result.cta.CtaRelationListResult
import com.facishare.marketing.api.result.cta.QueryCtaDetailResult
import com.facishare.marketing.api.result.cta.QueryCtaNameResult
import com.facishare.marketing.api.result.cta.QueryCtaRelationCountResult
import com.facishare.marketing.api.result.cta.QueryCtaSimpleDetailResult
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.FormMoreSetting
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO
import com.facishare.marketing.provider.dao.UserDAO
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO
import com.facishare.marketing.provider.dao.UserMarketingBrowserUserRelationDao
import com.facishare.marketing.provider.dao.UserMarketingMiniappAccountRelationDao
import com.facishare.marketing.provider.dao.cta.CtaDAO
import com.facishare.marketing.provider.dao.cta.CtaQrCodeRelationDAO
import com.facishare.marketing.provider.dao.cta.CtaRelationDAO
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dto.cta.CtaEntityDTO
import com.facishare.marketing.provider.dto.cta.CtaRelationCountDTO
import com.facishare.marketing.provider.dto.cta.CtaRelationEntityDTO
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.entity.ObjectGroupEntity
import com.facishare.marketing.provider.entity.UserEntity
import com.facishare.marketing.provider.entity.cta.CtaEntity
import com.facishare.marketing.provider.entity.cta.CtaQrCodeRelationEntity
import com.facishare.marketing.provider.entity.cta.CtaRelationEntity
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity
import com.facishare.marketing.provider.innerResult.qywx.GetContactMeResult
import com.facishare.marketing.provider.manager.CustomizeFormDataManager
import com.facishare.marketing.provider.manager.ObjectGroupManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.fxiaoke.crmrestapi.result.AggregateQueryResult
import com.google.common.collect.Lists
import org.junit.Assert
import spock.lang.Specification

class CtaServiceImplTest extends Specification {
    def ctaService = new CtaServiceImpl()

    def ctaDAO = Mock(CtaDAO)
    def objectGroupManager = Mock(ObjectGroupManager)
    def objectGroupRelationVisibleManager = Mock( ObjectGroupRelationVisibleManager)
    def objectGroupDAO  = Mock(ObjectGroupDAO)
    def objectGroupRelationDAO = Mock( ObjectGroupRelationDAO)
    def mktContentMgmtLogObjManager = Mock( MktContentMgmtLogObjManager)
    def ctaRelationDAO = Mock(CtaRelationDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def ctaQrCodeRelationDAO = Mock(CtaQrCodeRelationDAO)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def qywxManager = Mock(QywxManager)
    def qywxAddFanQrCodeDAO = Mock(QywxAddFanQrCodeDAO)
    def eieaConverter = Mock(EIEAConverter)
    def wechatAccountManager = Mock(WechatAccountManager)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def userDAO = Mock(UserDAO)
    def userMarketingMiniappAccountRelationDao = Mock(UserMarketingMiniappAccountRelationDao)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)


    def setup() {
        ctaService.ctaDAO = ctaDAO
        ctaService.objectGroupManager = objectGroupManager
        ctaService.objectGroupRelationVisibleManager = objectGroupRelationVisibleManager
        ctaService.objectGroupDAO = objectGroupDAO
        ctaService.objectGroupRelationDAO = objectGroupRelationDAO
        ctaService.mktContentMgmtLogObjManager = mktContentMgmtLogObjManager
        ctaService.ctaRelationDAO = ctaRelationDAO
        ctaService.crmV2Manager = crmV2Manager
        ctaService.ctaQrCodeRelationDAO = ctaQrCodeRelationDAO
        ctaService.agentConfigDAO = agentConfigDAO
        ctaService.qywxManager = qywxManager
        ctaService.qywxAddFanQrCodeDAO = qywxAddFanQrCodeDAO
        ctaService.eieaConverter = eieaConverter
        ctaService.wechatAccountManager = wechatAccountManager
        ctaService.customizeFormDataDAO = customizeFormDataDAO
        ctaService.customizeFormDataManager = customizeFormDataManager
        ctaService.userDAO = userDAO
        ctaService.userMarketingMiniappAccountRelationDao = userMarketingMiniappAccountRelationDao
        ctaService.userMarketingAccountDAO = userMarketingAccountDAO
        ctaService.userMarketingBrowserUserRelationDao = userMarketingBrowserUserRelationDao
        ctaService.customizeFormDataUserDAO = customizeFormDataUserDAO

    }


    def "addCtaTest"() {
        given:
        ctaDAO.queryCtaByName(*_) >> queryResult
        ctaDAO.addCta(*_) >> insertResult
        when:
        Result<AddCtaResult> result = ctaService.addCta("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | queryResult | insertResult | mockResult
        null | null | false | Result.newError(SHErrorCode.PARAMS_ERROR)
        new AddCtaArg(name: "name") | new CtaEntity(id: "id") | false | Result.newError(SHErrorCode.CTA_NAME_EXISTS)
        new AddCtaArg(triggerSettings: "") | null | false | Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(triggerSettings: "{}") | null | false | Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(triggerSettings: "{\"onComponentClick\":{\"enabled\":false,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":false,\"duration\":20}}") | null | false | Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":-20}}") | null | false | Result.newError(SHErrorCode.PARAMS_ERROR)
        new AddCtaArg(triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "") | null | false | Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "[]") | null | false | Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":false,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "[{\"componentType\":1, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":2, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":3, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":4, \"allowSkip\":true, \"id\":\"abc123\"}]") | null | false | Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_FEWER_THAN_THREE)
        new AddCtaArg(triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "[{\"componentType\":1, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":2, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":3, \"allowSkip\":true, \"id\":\"abc123\"}]") | null | false | Result.newError(SHErrorCode.ADD_FAIL)
        new AddCtaArg(triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":false,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20},\"onTimeElapsed\":{\"enabled\":false,\"duration\":20},\"onExitIntent\":{\"enabled\":false}}", guideComponents: "[{\"componentType\":1, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":2, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":3, \"allowSkip\":true, \"id\":\"abc123\"}]") | null | true | Result.newSuccess(new AddCtaResult(id: "id"))
    }

    def "updateCtaTest"() {
        given:
        ctaDAO.updateCta(*_) >> daoResult
        when:
        Result<Boolean> result = ctaService.updateCta("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | mockResult
        new AddCtaArg() | false | Result.newError(SHErrorCode.PARAMS_ERROR)
//        new AddCtaArg(id: "id") | false | Result.newError(SHErrorCode.UPDATE_FAIL)
//        new AddCtaArg(id: "id") | true | Result.newSuccess(true)
        new AddCtaArg(id: "id", triggerSettings: "") | false | Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(id: "id", triggerSettings: "{}") | false | Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(id: "id", triggerSettings: "{\"onComponentClick\":{\"enabled\":false,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":false,\"duration\":20}}") | false | Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(id: "id", triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":-20}}") | false | Result.newError(SHErrorCode.PARAMS_ERROR)
        new AddCtaArg(id: "id", triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "") | false | Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(id: "id", triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":false,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "[]") | false | Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_REQUIRE_AT_LEAST_ONE)
        new AddCtaArg(id: "id", triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "[{\"componentType\":1, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":2, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":3, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":4, \"allowSkip\":true, \"id\":\"abc123\"}]") | false | Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_FEWER_THAN_THREE)
        new AddCtaArg(id: "id", triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":true,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20}}", guideComponents: "[{\"componentType\":1, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":2, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":3, \"allowSkip\":true, \"id\":\"abc123\"}]") | false | Result.newError(SHErrorCode.UPDATE_FAIL)
        new AddCtaArg(id: "id", triggerSettings: "{\"onComponentClick\":{\"enabled\":true,\"customButtonEnabled\":false,\"buttonConfig\":{\"color\":\"#fff\"}},\"onScrollProgress\":{\"enabled\":true,\"duration\":20},\"onTimeElapsed\":{\"enabled\":false,\"duration\":20},\"onExitIntent\":{\"enabled\":false}}", guideComponents: "[{\"componentType\":1, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":2, \"allowSkip\":true, \"id\":\"abc123\"}, {\"componentType\":3, \"allowSkip\":true, \"id\":\"abc123\"}]") | true | Result.newSuccess(true)
    }

    def "updateCtaStatusTest"() {
        given:
        ctaDAO.updateCtaStatus(*_) >> daoResult
        when:
        Result<Boolean> result = ctaService.updateCtaStatus("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | mockResult
        new UpdateCtaStatusArg() | false | Result.newError(SHErrorCode.PARAMS_ERROR)
        new UpdateCtaStatusArg(id: "id", status: 1) | false | Result.newError(SHErrorCode.UPDATE_FAIL)
        new UpdateCtaStatusArg(id: "id", status: 1) | true | Result.newSuccess(true)
    }

    def "deleteCtaTest"() {
        given:
        ctaDAO.deleteCta(*_) >> daoResult
        ctaRelationDAO.getByCtaIds(*_) >> relationResult
        when:
        Result<Boolean> result = ctaService.deleteCta("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | relationResult | mockResult
        new DeleteCtaArg() | false | [] | Result.newError(SHErrorCode.PARAMS_ERROR)
        new DeleteCtaArg(id: "id") | false | [new CtaRelationEntity()] | Result.newError(SHErrorCode.CTA_EXISTS_RELATION.getErrorCode(), String.format(SHErrorCode.CTA_EXISTS_RELATION.getErrorMessage(), 1))
        new DeleteCtaArg(id: "id") | false | [] | Result.newError(SHErrorCode.DEL_FAIL)
        new DeleteCtaArg(id: "id") | true | [] | Result.newSuccess(true)
    }

    def "queryCtaDetailTest"() {
        given:
        ctaDAO.queryCtaDetail(*_) >> daoResult
        crmV2Manager.aggregateQuery(*_)>>countResult
        when:
        Result<QueryCtaDetailResult> result = ctaService.queryCtaDetail("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | countResult | mockResult
        new QueryCtaDetailArg() | null | null | Result.newError(SHErrorCode.CTA_NOT_FOUND)
        new QueryCtaDetailArg(id: "id") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date()) | null | Result.newSuccess(new QueryCtaDetailResult())
        new QueryCtaDetailArg(id: "id") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date()) | new com.fxiaoke.crmrestapi.common.result.Result<AggregateQueryResult>(code: 0, message: "", data: new AggregateQueryResult(dataList: Lists.newArrayList(["sum_action_count": 1.0D, "cta_id": "id", "action_type": "authorize_wechat_nickname"],["sum_action_count": 1.0D, "cta_id": "id", "action_type": "authorize_wechat_phone"],["sum_action_count": 1.0D, "cta_id": "id", "action_type": "login_member_account"],["sum_action_count": 1.0D, "cta_id": "id", "action_type": "register_member"],["sum_action_count": 1.0D, "cta_id": "id", "action_type": "scan_wx_service_qr_code"],["sum_action_count": 1.0D, "cta_id": "id", "action_type": "follow_wx_service"],["sum_action_count": 1.0D, "cta_id": "id", "action_type": "add_qywx_employee"],["sum_action_count": 1.0D, "cta_id": "id", "action_type": "enroll_form_new"]))) | Result.newSuccess(new QueryCtaDetailResult())
    }

    def "queryCtaSimpleDetailTest"() {
        given:
        ctaDAO.queryCtaDetail(*_) >> daoResult
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> customizeFormDataEntity
        customizeFormDataUserDAO.countCustomizeFormDataUserByFormId(*_) >> countResult
        customizeFormDataManager.checkWxAppUserIsEnrolledWithNoHandleActivity(*_) >> "enrollId"
        customizeFormDataManager.checkUserIsEnrolledWithNoHandleActivity(*_) >>Result.newError(SHErrorCode.USER_HAS_ENROLL)
        userDAO.queryByUid(*_) >> userEntity
        userDAO.queryByOpenId(*_) >> userEntity
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity(userMarketingId: "userMarketingId")
        userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(*_) >> new UserMarketingBrowserUserRelationEntity(userMarketingId: "userMarketingId")
        crmV2Manager.getWechatFanByOpenId(*_) >> Optional.of(new ObjectData())
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity(phone: "phone")
        ctaQrCodeRelationDAO.getByUserMarketingId(*_) >> Lists.newArrayList(new CtaQrCodeRelationEntity())
        when:
        Result<QueryCtaSimpleDetailResult> result = ctaService.queryCtaSimpleDetail("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | customizeFormDataEntity | countResult | userEntity | mockResult
        new QueryCtaSimpleDetailArg() | null | null | null | null | Result.newError(SHErrorCode.CTA_NOT_FOUND)
        new QueryCtaSimpleDetailArg(id: "id") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date()) | null | null | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId") | null | null | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 2 | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id", uid: "uid") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 1 | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id", uid: "uid") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId", guideComponentTypes: ",1,") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 1 | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id", uid: "uid") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId", guideComponentTypes: ",3,") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 1 | new UserEntity(avatar: "avatar", name: "name") | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id", openId: "openId") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId", guideComponentTypes: ",4,") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 1 | new UserEntity(avatar: "avatar", name: "name") | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id", browserUserId: "browserUserId", objectType: 1, objectId: "objectId") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId", guideComponentTypes: ",3,4,5,6,") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 1 | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id", browserUserId: "browserUserId", objectType: 1, objectId: "objectId") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId", guideComponentTypes: ",3,4,5,") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 1 | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
        new QueryCtaSimpleDetailArg(id: "id", browserUserId: "browserUserId", objectType: 1, objectId: "objectId") | new CtaEntity(id: "id", updateTime: new Date(), createTime: new Date(), formId: "formId", guideComponentTypes: ",6,") | new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting(fillInOnce: true, enrollLimit: true, enrollLimitNum: 2)) | 1 | null | Result.newSuccess(new QueryCtaSimpleDetailResult())
    }

    def "queryCtaRelationCountTest"() {
        given:
        ctaRelationDAO.getRelationCountByCtaIds(*_) >> daoResult
        when:
        Result<QueryCtaRelationCountResult> result = ctaService.queryCtaRelationCount("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | mockResult
        new QueryCtaRelationCountArg(ids: Lists.newArrayList("id")) | [] | Result.newSuccess(new QueryCtaRelationCountResult())
        new QueryCtaRelationCountArg(ids: Lists.newArrayList("id")) | Lists.newArrayList(new CtaRelationCountDTO(ctaId: "id", ctaRelationCount: 2)) | Result.newSuccess(new QueryCtaRelationCountResult())
    }

    def "queryCtaRelationListTest"() {
        given:
        ctaRelationDAO.getRelationDetailsByCtaId(*_) >> daoResult
        crmV2Manager.aggregateQuery(*_)>>countResult
        when:
        Result<QueryCtaDetailResult> result = ctaService.queryCtaRelationList("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | countResult | mockResult
        new QueryCtaDetailArg() | null | null | new Result<CtaRelationListResult>(SHErrorCode.SUCCESS, new CtaRelationListResult())
        new QueryCtaDetailArg(id: "id") | Lists.newArrayList(new CtaRelationEntityDTO(id: "id", objectType: 27, objectName: "name", ctaId: "ctaId", objectId: "objectId")) | null | Result.newSuccess(new QueryCtaDetailResult())
        new QueryCtaDetailArg(id: "id") | Lists.newArrayList(new CtaRelationEntityDTO(id: "id", objectType: 27, objectName: "name", ctaId: "ctaId", objectId: "objectId")) | new com.fxiaoke.crmrestapi.common.result.Result<AggregateQueryResult>(code: 0, message: "", data: new AggregateQueryResult(dataList: Lists.newArrayList(["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "authorize_wechat_nickname"],["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "authorize_wechat_phone"],["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "login_member_account"],["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "register_member"],["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "scan_wx_service_qr_code"],["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "follow_wx_service"],["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "add_qywx_employee"],["sum_action_count": 1.0D, "object_id": "objectId", "object_type": "hexagon", "action_type": "enroll_form_new"]))) | Result.newSuccess(new QueryCtaDetailResult())
    }

    def "listCtaTest"() {
        given:
        ctaDAO.createByMePage(*_) >> daoResult
        ctaDAO.noGroupPage(*_) >> [new CtaEntityDTO(id: "id")]
        ctaDAO.getAccessiblePage(*_) >> [new CtaEntityDTO(id: "id")]
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> groupResult
        objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(*_) >> ["groupId"]
        when:
        Result<CtaListResult> result = ctaService.listCta("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | groupResult | mockResult
        new ListCtaArg(pageSize: 20, pageNum: 1, groupId: "-2") | [] | [] | Result.newSuccess(new CtaListResult())
        new ListCtaArg(pageSize: 20, pageNum: 1, groupId: "-2") | [new CtaEntityDTO(id: "id")] | [] | Result.newSuccess(new CtaListResult())
        new ListCtaArg(pageSize: 20, pageNum: 1, groupId: "-3") | [new CtaEntityDTO(id: "id")] | [] | Result.newSuccess(new CtaListResult())
        new ListCtaArg(pageSize: 20, pageNum: 1, groupId: "") | [new CtaEntityDTO(id: "id")] | [] | Result.newSuccess(new CtaListResult())
        new ListCtaArg(pageSize: 20, pageNum: 1, groupId: "groupId", triggerTypes: "1,3") | [new CtaEntityDTO(id: "id")] | [] | Result.newSuccess(new CtaListResult())
        new ListCtaArg(pageSize: 20, pageNum: 1, groupId: "groupId") | [new CtaEntityDTO(id: "id")] | [new ObjectGroupEntity(id: "groupId")] | Result.newSuccess(new CtaListResult())
    }

    def "queryCtaNameTest"() {
        given:
        ctaDAO.getByIds(*_) >> daoResult
        when:
        Result<QueryCtaNameResult> result = ctaService.queryCtaName("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | mockResult
        new QueryCtaNameArg(ids: Lists.newArrayList()) | null | Result.newSuccess(new QueryCtaNameResult())
        new QueryCtaNameArg(ids: Lists.newArrayList("id1", "id2")) | Lists.newArrayList(new CtaEntity("id": "id", name: "name")) | Result.newSuccess(new QueryCtaNameResult())
    }

    def "listCtaGroupTest"() {
        given:
        objectGroupManager.getShowGroup(*_) >> groupResult
        when:
        Result<ObjectGroupListResult> result = ctaService.listCtaGroup("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | groupResult | mockResult
        new ListGroupArg(useType: 0) | new ObjectGroupListResult(objectGroupList: Lists.newArrayList()) | Result.newSuccess(new ObjectGroupListResult())
        new ListGroupArg(useType: 0) | new ObjectGroupListResult(objectGroupList: Lists.newArrayList(new ListObjectGroupResult(groupId: "groupId"))) | Result.newSuccess(new ObjectGroupListResult())
    }

    def "getGroupRoleTest"() {
        given:
        objectGroupRelationVisibleManager.getRoleRelationByGroupId(*_) >> Lists.newArrayList()
        when:
        Result<ObjectGroupListResult> result = ctaService.getGroupRole("ea", "groupId")
        then:
        Assert.assertTrue(result.isSuccess())
    }

    def "addCtaGroupRoleTest"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> isAppAdmin
        when:
        Result<Void> result = ctaService.addCtaGroupRole("ea", -10000, new SaveObjectGroupVisibleArg())
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        isAppAdmin | mockResult
        false | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN)
        true | Result.newSuccess()
    }

    def "editCtaGroupTest"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> isAppAdmin
        objectGroupManager.editGroup(*_) >> Result.newSuccess(new EditObjectGroupResult())
        when:
        Result<EditObjectGroupResult> result = ctaService.editCtaGroup("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | isAppAdmin | mockResult
        new EditObjectGroupArg() | false | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN)
        new EditObjectGroupArg(name: "全部") | true | Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME)
        new EditObjectGroupArg(name: "name") | true | Result.newSuccess(new EditObjectGroupResult())
    }

    def "deleteCtaGroupTest"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> isAppAdmin
        objectGroupManager.deleteGroup(*_) >> Result.newSuccess()
        when:
        Result<Void> result = ctaService.deleteCtaGroup("ea", -10000, new DeleteObjectGroupArg())
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        isAppAdmin | mockResult
        false | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN)
        true | Result.newSuccess()
    }

    def "setCtaGroupTest"() {
        given:
        ctaDAO.getByIds(*_) >> daoResult
        objectGroupDAO.getById(*_) >> groupResult
        when:
        Result<ObjectGroupListResult> result = ctaService.setCtaGroup("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg |daoResult|groupResult|mockResult
        new SetObjectGroupArg(objectIdList: Lists.newArrayList("id1"))|[]|null|Result.newError(SHErrorCode.CTA_NOT_FOUND);
        new SetObjectGroupArg(objectIdList: Lists.newArrayList("id1", "id2"))|[new CtaEntity(id: "id1")]|null|Result.newError(SHErrorCode.PART_CTA_NOT_FOUND);
        new SetObjectGroupArg(objectIdList: Lists.newArrayList("id1"))|[new CtaEntity(id: "id1")]|null|Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        new SetObjectGroupArg(objectIdList: Lists.newArrayList("id1"))|[new CtaEntity(id: "id1")]|new ObjectGroupEntity()|Result.newSuccess();
    }

    def "createWxQrCodeTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        wechatAccountManager.dispatch(*_) >> dispatchResult
        when:
        Result<CreateCtaWxQrCodeResult> result = ctaService.createWxQrCode("ea", -10000, new CreateCtaWxQrCodeArg(fsUserId: 1, sceneId: "sceneId", objectId: "objectId", objectType: 1, wxAppId: "wxAppId", ctaId: "ctaId"))
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        dispatchResult  | mockResult
        null | Result.newSuccess(new CreateCtaWxQrCodeResult())
        ["ticket": "qrTicket"] | Result.newSuccess(new CreateCtaWxQrCodeResult())
    }

    def "createQywxQrCodeTest"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >> agentConfig
        qywxAddFanQrCodeDAO.getById(*_) >> addFanQrCodeEntity
        qywxManager.getAccessToken(*_) >> "accessToken"
        qywxManager.setContactMeConfig(*_) >> configId
        qywxManager.getContanctMe(*_) >> contactMeResult
        when:
        Result<CreateCtaWxQrCodeResult> result = ctaService.createQywxQrCode("ea", -10000, new CreateCtaQywxQrCodeArg(fsUserId: 1, sceneId: "sceneId"))
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        agentConfig | addFanQrCodeEntity | configId | contactMeResult | mockResult
        null | null | null | null | Result.newSuccess(new CreateCtaWxQrCodeResult())
        new QywxCorpAgentConfigEntity() | new QywxAddFanQrCodeEntity(type: 2, remark: "remark", userId: "[\"userId\"]", channelDesc: "") | null | null | Result.newSuccess(new CreateCtaWxQrCodeResult())
        new QywxCorpAgentConfigEntity() | new QywxAddFanQrCodeEntity(type: 2, remark: "remark", userId: "[\"userId\"]", channelDesc: "") | "configId" | null | Result.newSuccess(new CreateCtaWxQrCodeResult())
        new QywxCorpAgentConfigEntity() | new QywxAddFanQrCodeEntity(type: 2, remark: "remark", userId: "[\"userId\"]", channelDesc: "") | "configId" | new GetContactMeResult(contactWay: new GetContactMeResult.ContactWay(qrCode: "qrCode")) | Result.newSuccess(new CreateCtaWxQrCodeResult())
    }

    def "pollingWxQrCodeStatusTest"() {
        given:
        ctaQrCodeRelationDAO.getById(*_) >> daoResult
        when:
        Result<Boolean> result = ctaService.pollingWxQrCodeStatus("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | mockResult
        new PollingQrCodeStatusArg(qrCodeId: "id") | null | Result.newSuccess(true)
        new PollingQrCodeStatusArg(qrCodeId: "id") | new CtaQrCodeRelationEntity("id": "id", status: 0) | Result.newSuccess(false)
    }

    def "pollingQywxQrCodeStatusTest"() {
        given:
        ctaQrCodeRelationDAO.getById(*_) >> daoResult
        when:
        Result<Boolean> result = ctaService.pollingQywxQrCodeStatus("ea", -10000, arg)
        then:
        Assert.assertTrue(mockResult.isSuccess()==result.isSuccess() && mockResult.errMsg==result.errMsg)
        where:
        arg | daoResult | mockResult
        new PollingQrCodeStatusArg(qrCodeId: "id") | null | Result.newSuccess(true)
        new PollingQrCodeStatusArg(qrCodeId: "id") | new CtaQrCodeRelationEntity("id": "id", status: 0) | Result.newSuccess(false)
    }
}