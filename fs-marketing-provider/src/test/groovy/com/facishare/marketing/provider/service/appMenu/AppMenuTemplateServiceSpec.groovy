package com.facishare.marketing.provider.service.appMenu


import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.appMenu.CopyTemplateArg
import com.facishare.marketing.api.arg.appMenu.CreateTemplateArg
import com.facishare.marketing.api.arg.appMenu.DeleteMenuArg
import com.facishare.marketing.api.arg.appMenu.DeleteTemplateArg
import com.facishare.marketing.api.arg.appMenu.GetShowAppMenuTemplateArg
import com.facishare.marketing.api.arg.appMenu.MenuDetailArg
import com.facishare.marketing.api.arg.appMenu.QueryTemplateArg
import com.facishare.marketing.api.arg.appMenu.QueryTemplateDetailArg
import com.facishare.marketing.api.arg.appMenu.SortMenuArg
import com.facishare.marketing.api.arg.appMenu.TemplateUserListArg
import com.facishare.marketing.api.arg.appMenu.UpdateAppMenuArg
import com.facishare.marketing.api.arg.appMenu.UpdateTemplateBaseInfoArg
import com.facishare.marketing.api.arg.appMenu.UpdateTemplateScopeArg
import com.facishare.marketing.api.arg.appMenu.UpdateTemplateStatusArg
import com.facishare.marketing.api.result.userrelation.UserRelationVO
import com.facishare.marketing.api.vo.appMenu.AppMenuDetailVO
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.typehandlers.value.Filter
import com.facishare.marketing.provider.bo.appMenu.UserMenuTemplateStatisticsBO
import com.facishare.marketing.provider.dao.UserTagDao
import com.facishare.marketing.provider.dao.appMenu.AppMenuDetailDAO
import com.facishare.marketing.provider.dao.appMenu.AppMenuTemplateDAO
import com.facishare.marketing.provider.dao.appMenu.UserAppMenuTemplateRelationDAO
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO
import com.facishare.marketing.provider.dao.material.MaterialShowSettingDAO
import com.facishare.marketing.provider.dto.PartnerUserDTO
import com.facishare.marketing.provider.dto.UserRelationDTO
import com.facishare.marketing.provider.entity.ObjectGroupEntity
import com.facishare.marketing.provider.entity.UserTagEntity
import com.facishare.marketing.provider.entity.appMenu.AppMenuDetailEntity
import com.facishare.marketing.provider.entity.appMenu.AppMenuTemplateEntity
import com.facishare.marketing.provider.entity.appMenu.UserAppMenuTemplateRelationEntity
import com.facishare.marketing.provider.entity.material.MaterialShowSettingEntity
import com.facishare.marketing.provider.entity.user.UserRelationEntity
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.qywx.QywxUserManager
import com.facishare.marketing.provider.manager.user.UserRelationManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg
import com.facishare.organization.adapter.api.model.biz.department.Department
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult
import com.facishare.organization.api.model.department.DepartmentDto
import com.facishare.organization.api.model.type.EmployeeEntityStatus
import com.fxiaoke.crmrestapi.common.data.FieldDescribe
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import com.fxiaoke.paasauthrestapi.result.OuterTenantIdGroupIdsData
import com.fxiaoke.paasauthrestapi.result.TenantGroupResult
import com.google.common.collect.Lists
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class AppMenuTemplateServiceSpec extends Specification {

    // 模拟依赖项
    // 模拟所有依赖项
    def appMenuTemplateDAO = Mock(AppMenuTemplateDAO)
    def userAppMenuTemplateRelationDAO = Mock(UserAppMenuTemplateRelationDAO)
    def appMenuDetailDAO = Mock(AppMenuDetailDAO)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def qywxUserManager = Mock(QywxUserManager)
    def userRelationManager = Mock(UserRelationManager)
    def memberMarketingManager = Mock(MemberMarketingManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def authManager = Mock(AuthManager)
    def eieaConverter = Mock(EIEAConverter)
    def qywxManager = Mock(QywxManager)
    def materialShowSettingDAO = Mock(MaterialShowSettingDAO)
    def objectGroupManager = Mock(ObjectGroupManager)
    def userTagDao = Mock(UserTagDao)
    def objectManager = Mock(ObjectManager)
    def redisManager = Mock(RedisManager)
    def objectGroupDAO = Mock(ObjectGroupDAO)
    def objectGroupRelationVisibleManager = Mock(ObjectGroupRelationVisibleManager)
    def objectDescribeService = Mock(ObjectDescribeService)
    def authPartnerManager = Mock(AuthPartnerManager)

    // 待测系统
    def appMenuTemplateService = new AppMenuTemplateServiceImpl(
            appMenuTemplateDAO: appMenuTemplateDAO,
            userAppMenuTemplateRelationDAO: userAppMenuTemplateRelationDAO,
            appMenuDetailDAO: appMenuDetailDAO,
            fsAddressBookManager: fsAddressBookManager,
            qywxUserManager: qywxUserManager,
            userRelationManager: userRelationManager,
            memberMarketingManager: memberMarketingManager,
            crmV2Manager: crmV2Manager,
            authManager: authManager,
            eieaConverter: eieaConverter,
            qywxManager: qywxManager,
            materialShowSettingDAO: materialShowSettingDAO,
            objectGroupManager: objectGroupManager,
            userTagDao: userTagDao,
            objectManager: objectManager,
            redisManager: redisManager,
            objectGroupDAO: objectGroupDAO,
            objectGroupRelationVisibleManager: objectGroupRelationVisibleManager,
            objectDescribeService: objectDescribeService,
            authPartnerManager: authPartnerManager
    )


    @Unroll
    def "isApplyForKIS"() {
        given:
        appMenuTemplateDAO.getByName(*_) >> appMenuTemplateEntity
        appMenuTemplateDAO.batchInsert(*_) >> 1
        appMenuDetailDAO.getMaxOrderNum(*_) >> 1
        appMenuDetailDAO.batchInsert(*_) >> 1
        when:
        appMenuTemplateService.createTemplate(arg)
        then:
        noExceptionThrown()
        where:
        arg                                 | appMenuTemplateEntity
        new CreateTemplateArg()             | new AppMenuTemplateEntity()
        new CreateTemplateArg(name: "test")             | new AppMenuTemplateEntity()
        new CreateTemplateArg(name: "name", userType: "EMPLOYEE") | null
        new CreateTemplateArg(name: "name", userType: "EMPLOYEE") | null
        new CreateTemplateArg(name: "name", userType: "PARTNER") | null
        new CreateTemplateArg(name: "name", userType: "MEMBER") | null
        new CreateTemplateArg(name: "name", userType: "other") | null
    }

    @Unroll
    def "list"() {
        given:
        appMenuTemplateDAO.list(*_) >> templateEntityList
        userAppMenuTemplateRelationDAO.statisticsByTemplate(*_) >> [new UserMenuTemplateStatisticsBO(templateId: "id", count: 1)]
        eieaConverter.enterpriseAccountToId(*_) >> 1
        qywxManager.getAccessToken(*_) >> "token"
        qywxManager.queryDepartment(*_) >> new DepartmentListResult(departmentList: [new Department(id: 2, name: "name2")])
        authManager.batchGetByDepartmentIds(*_) >> [new DepartmentDto(departmentId: 1L, name: "name1")]
        authPartnerManager.batchGetTenantGroupByIds(*_) >> [new TenantGroupResult(id:"tenantId1",name:"tenantName1")]
        authPartnerManager.batchGetOutTenantByIds(*_) >> [new ObjectData(id: "id1", name: "tenantName2")]
        when:
        appMenuTemplateService.list(arg)
        then:
        noExceptionThrown()
        where:
        arg                                            | templateEntityList
        new QueryTemplateArg()                         | []
        new QueryTemplateArg(pageNum: 1, pageSize: 10) | []
        new QueryTemplateArg(pageNum: 1, pageSize: 10) | [new AppMenuTemplateEntity(id: "id", name: "test", type: "CUSTOMIZE", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[999999]},\"qywxAddressBookScope\":{\"departmentIdList\":[999999]},\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}")]
        new QueryTemplateArg(pageNum: 1, pageSize: 10) | [new AppMenuTemplateEntity(id: "id", name: "test", type: "SYSTEM_EMPLOYEE", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[999999]},\"qywxAddressBookScope\":{\"departmentIdList\":[999999]},\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}")]
        new QueryTemplateArg(pageNum: 1, pageSize: 10) | [new AppMenuTemplateEntity(id: "id", name: "mark.app.menu.template.system", type: "SYSTEM_EMPLOYEE", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[999999]},\"qywxAddressBookScope\":{\"departmentIdList\":[999999]},\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}")]
        new QueryTemplateArg(pageNum: 1, pageSize: 10) | [new AppMenuTemplateEntity(id: "id", name: "mark.app.menu.template.system", type: "SYSTEM_EMPLOYEE", scope: "{\"enterpriseRelationScope\":{\"outTenantIdList\":[1122],\"outTenantGroupIdList\":[\"aaa\"]},\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}")]

    }

    @Unroll
    def "detail"() {
        given:
        qywxManager.getAccessToken(*_) >> "token"
        qywxManager.queryDepartment(*_) >> new DepartmentListResult(departmentList: [new Department(id: 2, name: "name2")])
        authManager.batchGetByDepartmentIds(*_) >> [new DepartmentDto(departmentId: 1L, name: "name1")]
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuDetailDAO.getOrderlyByTemplateId(*_) >> appMenuDetailEntityList
        appMenuDetailDAO.getMaxOrderNum(*_) >> 1
        fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(*_) >> [1: new FsAddressBookManager.FSEmployeeMsg(name: "name")]
        appMenuDetailDAO.batchInsert(*_) >> 1
        when:
        appMenuTemplateService.detail(arg)
        then:
        noExceptionThrown()
        where:
        arg                                  | appMenuTemplateEntity                                                                                                                                                                                                                                                                                                                                                                   | appMenuDetailEntityList
        new QueryTemplateDetailArg()         | null                                                                                                                                                                                                                                                                                                                                                                                    | []
        new QueryTemplateDetailArg(id: "id") | null                                                                                                                                                                                                                                                                                                                                                                                    | []
        new QueryTemplateDetailArg(id: "id") | new AppMenuTemplateEntity(id: "id", name: "test", createUserId: -10000, updateUserId: -10000, type: "CUSTOMIZE", userType: "EMPLOYEE", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[999999]},\"qywxAddressBookScope\":{\"departmentIdList\":[999999]},\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}") | [new AppMenuDetailEntity(id: "id1", targetMaterialType: 4, status: "NORMAL", orderNum: 1), new AppMenuDetailEntity(id: "id1", targetMaterialType: 4, status: "DELETED", orderNum: 2)]
        new QueryTemplateDetailArg(id: "id") | new AppMenuTemplateEntity(id: "id", name: "test", createUserId: 1, updateUserId: 1, type: "CUSTOMIZE", userType: "PARTNER", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[999999]},\"qywxAddressBookScope\":{\"departmentIdList\":[999999]},\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}")           | [new AppMenuDetailEntity(id: "id1", targetMaterialType: 4, status: "NORMAL", orderNum: 1), new AppMenuDetailEntity(id: "id1", targetMaterialType: 4, status: "DELETED", orderNum: 2)]
        new QueryTemplateDetailArg(id: "id") | new AppMenuTemplateEntity(id: "id", name: "test", createUserId: 1, updateUserId: 1, type: "CUSTOMIZE", userType: "MEMBER", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[999999]},\"qywxAddressBookScope\":{\"departmentIdList\":[999999]},\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}")           | [new AppMenuDetailEntity(id: "id1", targetMaterialType: 4, status: "NORMAL", orderNum: 1), new AppMenuDetailEntity(id: "id1", targetMaterialType: 4, status: "DELETED", orderNum: 2)]

    }

    @Unroll
    def "updateBaseInfo"() {
        given:
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuTemplateDAO.getByName(*_) >> sameNameTemplateEntity
        appMenuTemplateDAO.update(*_) >> 1
        when:
        Result<Void> result = appMenuTemplateService.updateBaseInfo(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                                                                                | appMenuTemplateEntity                    | sameNameTemplateEntity               | expect
        new UpdateTemplateBaseInfoArg()                                                    | null                                     | null                                 | false
        new UpdateTemplateBaseInfoArg(id: "id", name: "name", title: "title", iconType: 1) | null                                     | null                                 | false
        new UpdateTemplateBaseInfoArg(id: "id", name: "name", title: "title", iconType: 1) | new AppMenuTemplateEntity(name: "name2") | new AppMenuTemplateEntity(id: "id2") | false
        new UpdateTemplateBaseInfoArg(id: "id", name: "name", title: "title", iconType: 1) | new AppMenuTemplateEntity(name: "name2") | null                                 | true
    }

    @Unroll
    def "updateScope"() {
        given:
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuTemplateDAO.update(*_) >> 1
        userAppMenuTemplateRelationDAO.getFsUserIdByTemplateId(*_) >> deleteUserIdList
        userAppMenuTemplateRelationDAO.deleteByTemplateId(*_) >> 1
        fsAddressBookManager.getFsUserIdByDepartmentIdList(*_) >> fsUserIdList
        userAppMenuTemplateRelationDAO.batchInsert(*_) >> 1
        qywxManager.getAccessToken(*_) >> "token"
        qywxManager.queryDepartment(*_) >> new DepartmentListResult(departmentList: [new Department(id: 2, name: "name2")])
        qywxUserManager.getQywxUserIdByQywxDepartment(*_) >> ["userId"]
        userRelationManager.getByQywxUserIdList(*_) >> [new UserRelationEntity(fsUserId: 1)]
        objectDescribeService.getDescribe(*_) >> getDescribeResultResult
        memberMarketingManager.getPassQueryFilterArg(*_) >> new PaasQueryFilterArg()
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 1
        crmV2Manager.listCrmObjectScanByIdV3(*_) >> objectDataInnerPage
        userRelationManager.getByMemberIdList(*_) >> [new UserRelationEntity(fsUserId: 1), new UserRelationEntity(fsUserId: 100000000), new UserRelationEntity(fsUserId: 300000000)]
        userAppMenuTemplateRelationDAO.getIdByTemplateLimit(*_) >>> [["id1"], null]
        userAppMenuTemplateRelationDAO.deleteByIds(*_) >> 1
        authPartnerManager.getTenantIdsByGroupIds(*_) >> [new OuterTenantIdGroupIdsData(outTenantId: "333", groupIds:["222"])]
        authPartnerManager.getPartnerUserByTenantIds(*_) >> [new PartnerUserDTO(100000000,  300000000)]
        userAppMenuTemplateRelationDAO.batchInsert(*_) >> 1
        when:
        Result<Void> result = appMenuTemplateService.updateScope(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                                                                                                                                                      | appMenuTemplateEntity                              | deleteUserIdList | fsUserIdList | getDescribeResultResult                                                                                                                                                     | objectDataInnerPage                         | expect
        new UpdateTemplateScopeArg()                                                                                                                             | null                                               | []               | []           | null                                                                                                                                                                        | null                                        | false
        new UpdateTemplateScopeArg(id: "id", fsAddressBookScope: new UpdateTemplateScopeArg.FsAddressBookScope(departmentIdList: [1]))                           | null                                               | []               | []           | null                                                                                                                                                                        | null                                        | false
        new UpdateTemplateScopeArg(id: "id", fsAddressBookScope: new UpdateTemplateScopeArg.FsAddressBookScope(departmentIdList: []))                            | new AppMenuTemplateEntity(type: "SYSTEM-EMPLOYEE") | []               | []           | null                                                                                                                                                                        | null                                        | false
        new UpdateTemplateScopeArg(id: "id", fsAddressBookScope: new UpdateTemplateScopeArg.FsAddressBookScope(departmentIdList: []))                            | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | []               | []           | null                                                                                                                                                                        | null                                        | true
        new UpdateTemplateScopeArg(id: "id", enterpriseRelationScope: new UpdateTemplateScopeArg.EnterpriseRelationScope())                            | new AppMenuTemplateEntity(type: "CUSTOMIZE", userType: "EMPLOYEE")       | []               | []           | null                                                                                                                                                                        | null                                        | false
        new UpdateTemplateScopeArg(id: "id", fsAddressBookScope: new UpdateTemplateScopeArg.FsAddressBookScope(departmentIdList: []))                            | new AppMenuTemplateEntity(type: "CUSTOMIZE", userType: "PARTNER")       | []               | []           | null                                                                                                                                                                        | null                                        | false
        new UpdateTemplateScopeArg(id: "id", fsAddressBookScope: new UpdateTemplateScopeArg.FsAddressBookScope(departmentIdList: []))                            | new AppMenuTemplateEntity(type: "CUSTOMIZE", userType: "MEMBER")       | []               | []           | null                                                                                                                                                                        | null                                        | false
        new UpdateTemplateScopeArg(id: "id", fsAddressBookScope: new UpdateTemplateScopeArg.FsAddressBookScope(departmentIdList: [1]))                           | new AppMenuTemplateEntity(type: "CUSTOMIZE", userType: "EMPLOYEE")       | []               | []           | null                                                                                                                                                                        | null                                        | true
        new UpdateTemplateScopeArg(id: "id", fsAddressBookScope: new UpdateTemplateScopeArg.FsAddressBookScope(departmentIdList: [1]))                           | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | []               | [1]          | null                                                                                                                                                                        | null                                        | true
        new UpdateTemplateScopeArg(id: "id", qywxAddressBookScope: new UpdateTemplateScopeArg.QywxAddressBookScope(departmentIdList: [999999]))                  | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | [1]              | [1]          | null                                                                                                                                                                        | null                                        | true
        new UpdateTemplateScopeArg(id: "id", memberScope: new UpdateTemplateScopeArg.MemberScope(filters: [new Filter("name", "EQ", Lists.newArrayList("zj"))])) | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | [1]              | [1]          | null                                                                                                                                                                        | null                                        | true
        new UpdateTemplateScopeArg(id: "id", memberScope: new UpdateTemplateScopeArg.MemberScope(filters: [new Filter("name", "EQ", Lists.newArrayList("zj"))])) | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | [1]              | [1]          | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: ["name": new FieldDescribe()])))        | null                                        | true
        new UpdateTemplateScopeArg(id: "id", memberScope: new UpdateTemplateScopeArg.MemberScope(filters: [new Filter("name", "EQ", Lists.newArrayList("zj"))])) | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | [1]              | [1]          | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: ["member_type": new FieldDescribe()]))) | null                                        | true
        new UpdateTemplateScopeArg(id: "id", memberScope: new UpdateTemplateScopeArg.MemberScope(filters: [new Filter("name", "EQ", Lists.newArrayList("zj"))])) | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | [1]              | [1]          | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: ["member_type": new FieldDescribe()]))) | new InnerPage(dataList: [new ObjectData()]) | true

        new UpdateTemplateScopeArg(id: "id", enterpriseRelationScope:new UpdateTemplateScopeArg.EnterpriseRelationScope(outTenantIdList:["111"], outTenantGroupIdList:["222"])) | new AppMenuTemplateEntity(type: "CUSTOMIZE") | [1] | [1] | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: ["member_type": new FieldDescribe()]))) | new InnerPage(dataList: [new ObjectData()]) | true

    }

    @Unroll
    def "createOrUpdateMenu"() {
        given:
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuDetailDAO.getByTemplateIdAndName(*_) >> sameNameEntity
        appMenuDetailDAO.getMaxOrderNum(*_) >> 1
        appMenuDetailDAO.batchInsert(*_) >> 1
        appMenuTemplateDAO.update(*_) >> 1
        appMenuDetailDAO.update(*_) >> 1
        appMenuDetailDAO.getById(*_) >> appMenuDetailEntity
        when:
        Result<Void> result = appMenuTemplateService.createOrUpdateMenu(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                                                                                                                                | appMenuTemplateEntity       | sameNameEntity                                    | appMenuDetailEntity                           | expect
        new UpdateAppMenuArg()                                                                                                             | null                        | null                                              | null                                          | false
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "rule")                                                                   | null                        | null                                              | null                                          | false
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 10000)                                | null                        | null                                              | null                                          | false
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1)                                    | null                        | null                                              | null                                          | false
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1)                                    | new AppMenuTemplateEntity() | new AppMenuDetailEntity()                         | null                                          | false
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1)                                    | new AppMenuTemplateEntity() | null                                              | null                                          | true
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1, id: "id")                          | new AppMenuTemplateEntity() | null                                              | null                                          | false
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1, id: "id")                          | new AppMenuTemplateEntity() | new AppMenuDetailEntity(name: "name", id: "id33") | new AppMenuDetailEntity(id: "id", name: "33") | false
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1, id: "id")                          | new AppMenuTemplateEntity() | null                                              | new AppMenuDetailEntity()                     | true
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "OBJECT_FILTER", targetMaterialType: 1, id: "id", filters: [])            | new AppMenuTemplateEntity() | null                                              | new AppMenuDetailEntity()                     | true
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "TAG", targetMaterialType: 1, id: "id", tagIdList: ["1"], tagOperator: 1) | new AppMenuTemplateEntity() | null                                              | new AppMenuDetailEntity()                     | true
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SPECIFIC", targetMaterialType: 1, id: "id", objectIdList: ["1"])         | new AppMenuTemplateEntity() | null                                              | new AppMenuDetailEntity()                     | true
        new UpdateAppMenuArg(name: "name", objectAccessibleRule: "SPECIFIC", targetMaterialType: 15, id: "id", agentList: [["name": "BI-AI"]]) | new AppMenuTemplateEntity() | null                                              | new AppMenuDetailEntity()                     | true
    }

    @Unroll
    def "updateStatus"() {
        given:
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuDetailDAO.update(*_) >> 1
        userAppMenuTemplateRelationDAO.getFsUserIdByTemplateId(*_) >> ["id"]
        when:
        Result<Void> result = appMenuTemplateService.updateStatus(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                                                     | appMenuTemplateEntity                              | expect
        new UpdateTemplateStatusArg()                           | null                                               | false
        new UpdateTemplateStatusArg(id: "id", status: "STATUS") | null                                               | false
        new UpdateTemplateStatusArg(id: "id", status: "ENABLE") | null                                               | false
        new UpdateTemplateStatusArg(id: "id", status: "ENABLE") | new AppMenuTemplateEntity(type: "SYSTEM_EMPLOYEE") | false
        new UpdateTemplateStatusArg(id: "id", status: "ENABLE") | new AppMenuTemplateEntity(type: "CUSTOMIZE")       | true
    }

    @Unroll
    def "delete"() {
        given:
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuTemplateDAO.delete(*_) >> 1
        appMenuDetailDAO.deleteByTemplateId(*_) >> { printf "dd" }
        userAppMenuTemplateRelationDAO.getFsUserIdByTemplateId(*_) >> ["id"]
        userAppMenuTemplateRelationDAO.deleteByTemplateId(*_) >> 1
        when:
        Result<Void> result = appMenuTemplateService.delete(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                     | appMenuTemplateEntity                                           | expect
        new DeleteTemplateArg() | null                                                            | false
        new DeleteTemplateArg() | new AppMenuTemplateEntity(type: "CUSTOMIZE")                    | false
        new DeleteTemplateArg() | new AppMenuTemplateEntity(type: "SYSTEM_EMPLOYEE")              | false
        new DeleteTemplateArg() | new AppMenuTemplateEntity(type: "CUSTOMIZE", status: "ENABLE")  | false
        new DeleteTemplateArg() | new AppMenuTemplateEntity(type: "CUSTOMIZE", status: "DISABLE") | true
    }

    @Unroll
    def "copy"() {
        given:
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuTemplateDAO.batchInsert(*_) >> 1
        appMenuDetailDAO.batchInsert(*_) >> 1
        userAppMenuTemplateRelationDAO.batchInsert(*_) >> 1
        appMenuDetailDAO.getOrderlyByTemplateId(*_) >> [new AppMenuDetailEntity()]
        userAppMenuTemplateRelationDAO.getByTemplateId(*_) >> [new UserAppMenuTemplateRelationEntity()]
        when:
        Result<Void> result = appMenuTemplateService.copy(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                                 | appMenuTemplateEntity                        | expect
        new CopyTemplateArg()               | null                                         | false
        new CopyTemplateArg(sourceId: "id") | null                                         | false
        new CopyTemplateArg(sourceId: "id") | new AppMenuTemplateEntity(type: "CUSTOMIZE") | true
    }

    @Unroll
    def "createSystemTemplate"() {
        given:
        appMenuTemplateDAO.getByType(*_) >> exsitSystemTemplateList
        fsAddressBookManager.getFsUserIdByDepartmentIdList(*_) >> [1]
        userAppMenuTemplateRelationDAO.batchInsert(*_) >> 1
        qywxManager.getAccessToken(*_) >> "token"
        qywxManager.queryDepartment(*_) >> new DepartmentListResult(departmentList: [new Department(id: 2, name: "name2")])
        qywxUserManager.getQywxUserIdByQywxDepartment(*_) >> ["userId"]
        userRelationManager.getByQywxUserIdList(*_) >> [new UserRelationEntity(fsUserId: 1)]
        objectDescribeService.getDescribe(*_) >> null
        materialShowSettingDAO.queryMaterialSettingListByEa(*_) >> exsitMaterialShowSettingList
        appMenuDetailDAO.getMaxOrderNum(*_) >> 1
        appMenuDetailDAO.batchInsert(*_) >> 1

        when:
        Result<Void> result = appMenuTemplateService.createSystemTemplate("ea", type)
        then:
        result.isSuccess() == expect
        where:
        exsitSystemTemplateList       | type | exsitMaterialShowSettingList                                                                                         | expect
        [new AppMenuTemplateEntity()] |"SYSTEM_EMPLOYEE" | []                                                                                                                   | true
        []                            | "SYSTEM_EMPLOYEE" |[]                                                                                                                   | true
        []                            | "SYSTEM_EMPLOYEE" | [new MaterialShowSettingEntity(type: 4, showStatus: true), new MaterialShowSettingEntity(type: 5, showStatus: true)] | true
        []                            | "SYSTEM_PARTNER" | [new MaterialShowSettingEntity(type: 4, showStatus: true), new MaterialShowSettingEntity(type: 5, showStatus: true)] | true

    }

    @Unroll
    def "menuDetail"() {
        given:
        appMenuDetailDAO.getById(*_) >> appMenuDetailEntity
        objectGroupManager.getByIdList(*_) >> [new ObjectGroupEntity()]
        userTagDao.getByIdList(*_) >> [new UserTagEntity(id: "id1", parentTagId: "id2"), new UserTagEntity(id: "id2", parentTagId: "NONE"), new UserTagEntity(id: "id3", parentTagId: "NONE")]
        objectManager.getObjectName(*_) >> ["id": "name"]
        when:
        Result<AppMenuDetailVO> result = appMenuTemplateService.menuDetail(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                         | appMenuDetailEntity                                                                                                                                                | expect
        new MenuDetailArg()         | null                                                                                                                                                               | false
        new MenuDetailArg(id: "id") | null                                                                                                                                                               | false
        new MenuDetailArg(id: "id") | new AppMenuDetailEntity(targetMaterialType: 1000)                                                                                                                  | false
        new MenuDetailArg(id: "id") | new AppMenuDetailEntity(targetMaterialType: 1, objectAccessibleRule: "rule")                                                                                       | false
        new MenuDetailArg(id: "id") | new AppMenuDetailEntity(targetMaterialType: 1, objectAccessibleRule: "SINGLE_GROUP", objectAccessibleTarget: "[\"id\"]")                                           | true
        new MenuDetailArg(id: "id") | new AppMenuDetailEntity(targetMaterialType: 1, objectAccessibleRule: "TAG", objectAccessibleTarget: "{\"tagIdList\":[\"id1\",\"id2\",\"id3\"],\"tagOperator\":1}") | true
        new MenuDetailArg(id: "id") | new AppMenuDetailEntity(targetMaterialType: 1, objectAccessibleRule: "SPECIFIC", objectAccessibleTarget: "[\"id\"]")                                               | true
        new MenuDetailArg(id: "id") | new AppMenuDetailEntity(targetMaterialType: 15, objectAccessibleRule: "SPECIFIC", objectAccessibleTarget: '[["name":"BI_AI"]]')                                               | true
        new MenuDetailArg(id: "id") | new AppMenuDetailEntity(targetMaterialType: 1, objectAccessibleRule: "OBJECT_FILTER", objectAccessibleTarget: "[]")                                                | true
    }

    @Unroll
    def "deleteMenu"() {
        given:
        appMenuDetailDAO.getById(*_) >> appMenuDetailEntity
        appMenuDetailDAO.update(*_) >> 1
        appMenuTemplateDAO.update(*_) >> 1
        when:
        Result<Void> result = appMenuTemplateService.deleteMenu(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                         | appMenuDetailEntity       | expect
        new DeleteMenuArg()         | null                      | false
        new DeleteMenuArg(id: "id") | null                      | false
        new DeleteMenuArg(id: "id") | new AppMenuDetailEntity() | true
    }

    @Unroll
    def "sortMenu"() {
        given:
        appMenuDetailDAO.batchUpdateOrderNum(*_) >> 1
        appMenuTemplateDAO.update(*_) >> 1
        when:
        Result<Void> result = appMenuTemplateService.sortMenu(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                                                                   | expect
        new SortMenuArg()                                                     | false
        new SortMenuArg(sortList: [new SortMenuArg.Sort()], templateId: "id") | true
    }

    @Unroll
    def "getShowAppMenuTemplate"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        redisManager.get(*_) >> cacheTemplateId
        appMenuTemplateDAO.getById(*_) >> appMenuTemplateEntity
        appMenuDetailDAO.getOrderlyByTemplateId(*_) >> appMenuDetailEntityList
        redisManager.set(*_) >> true
        userAppMenuTemplateRelationDAO.getByFsUserId(*_) >> userTemplateRelationList
        appMenuTemplateDAO.getByType(*_) >> [new AppMenuTemplateEntity()]
        appMenuTemplateDAO.getByIdList(*_) >> appMenuTemplateEntityList
        authManager.getUserMainDepartmentsOrderly(*_) >> [1, 2, 3]
        userRelationManager.getByFsUserId(*_) >> userRelationEntity
        qywxUserManager.getUserMainDepartmentsOrderly(*_) >> [1, 2, 3]
        when:
        appMenuTemplateService.getShowAppMenuTemplate(new GetShowAppMenuTemplateArg(ea: "ea", fsUserId: 1))
        then:
        noExceptionThrown()
        where:
        cacheTemplateId | appMenuTemplateEntity                       | appMenuTemplateEntityList                                                                                                                                                                                                                                                                                                                                                                            | userTemplateRelationList                                                           | appMenuDetailEntityList                                                                 | userRelationEntity
        "id"            | new AppMenuTemplateEntity(status: "ENABLE") | null                                                                                                                                                                                                                                                                                                                                                                                                 | null                                                                               | null                                                                                    | null
        "id"            | new AppMenuTemplateEntity(status: "ENABLE") | null                                                                                                                                                                                                                                                                                                                                                                                                 | null                                                                               | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        "id"            | null                                        | []                                                                                                                                                                                                                                                                                                                                                                                                   | []                                                                                 | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        "id"            | null                                        | [new AppMenuTemplateEntity()]                                                                                                                                                                                                                                                                                                                                                                        | [new UserAppMenuTemplateRelationEntity()]                                          | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity()]                                                                                                                                                                                                                                                                                                                                                                        | [new UserAppMenuTemplateRelationEntity()]                                          | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "DISABLE"), new AppMenuTemplateEntity(status: "DISABLE")]                                                                                                                                                                                                                                                                                                         | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "ENABLE"), new AppMenuTemplateEntity(status: "DISABLE")]                                                                                                                                                                                                                                                                                                          | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "ENABLE", type: "SYSTEM_EMPLOYEE"), new AppMenuTemplateEntity(status: "ENABLE", type: "SYSTEM_EMPLOYEE")]                                                                                                                                                                                                                                                         | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "ENABLE", type: "SYSTEM_EMPLOYEE"), new AppMenuTemplateEntity(status: "ENABLE", type: "CUSTOMIZE")]                                                                                                                                                                                                                                                               | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[1,2]}}"), new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[11,22]}}")]                                                                                   | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[10001]}}"), new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[10001]}}")]                                                                             | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | null
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[10001]}}"), new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[10001]}}")]                                                                             | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | new UserRelationEntity(qywxUserId: "id")
        null            | new AppMenuTemplateEntity(status: "ENABLE") | [new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[10001]}}"), new AppMenuTemplateEntity(status: "ENABLE", createTime: new Date(), type: "CUSTOMIZE", scope: "{\"memberScope\":{\"filters\":[{\"field_name\":\"name\",\"operator\":\"LIKE\",\"field_values\":[\"模板测试\"],\"fieldType\":\"1\"}]}}")] | [new UserAppMenuTemplateRelationEntity(), new UserAppMenuTemplateRelationEntity()] | [new AppMenuDetailEntity(status: "NORMAL"), new AppMenuDetailEntity(status: "DELETED")] | new UserRelationEntity(qywxUserId: "id")
    }


    @Unroll
    def "getSpecificObjectIdList"() {
        given:
        appMenuDetailDAO.getById(*_) >> appMenuDetailEntity
        when:
        Result<List<String>> result = appMenuTemplateService.getSpecificObjectIdList("ea", menuId, objectType)
        then:
        result.isSuccess() == expect
        where:
        menuId | objectType | appMenuDetailEntity                                                                                                  | expect
        null   | 1          | null                                                                                                                 | false
        "id"   | 1          | null                                                                                                                 | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "rule")                                                                | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "TAG")                                                                 | false
        "id"   | 2          | new AppMenuDetailEntity(objectAccessibleRule: "SPECIFIC", targetMaterialType: 1)                                     | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "SPECIFIC", targetMaterialType: 1, objectAccessibleTarget: "[\"Id\"]") | true
    }

    @Unroll
    def "getMenuTagRule"() {
        given:
        appMenuDetailDAO.getById(*_) >> appMenuDetailEntity
        when:
        Result<AppMenuTagVO> result = appMenuTemplateService.getMenuTagRule("ea", menuId, objectType)
        then:
        result.isSuccess() == expect
        where:
        menuId | objectType | appMenuDetailEntity                                                                                                                                     | expect
        null   | 1          | null                                                                                                                                                    | false
        "id"   | 1          | null                                                                                                                                                    | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "rule")                                                                                                   | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "SPECIFIC")                                                                                               | false
        "id"   | 2          | new AppMenuDetailEntity(objectAccessibleRule: "TAG", targetMaterialType: 1)                                                                             | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "TAG", targetMaterialType: 1, objectAccessibleTarget: "{\"tagIdList\":[\"demoData\"],\"tagOperator\":1}") | true
    }


    @Unroll
    def "getMenuObjectGroupRule"() {
        given:
        appMenuDetailDAO.getById(*_) >> appMenuDetailEntity
        objectGroupManager.getAllSubGroupWithoutPermission(*_) >> [new ObjectGroupEntity()]
        objectGroupDAO.listGroupByEa(*_) >> [new ObjectGroupEntity()]
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> [new ObjectGroupEntity()]
        when:
        Result<List<String>> result = appMenuTemplateService.getMenuObjectGroupRule("ea", 1, menuId, objectType)
        then:
        result.isSuccess() == expect
        where:
        menuId | objectType | appMenuDetailEntity                                                                                                                 | expect
        null   | 1          | null                                                                                                                                | false
        "id"   | 1          | null                                                                                                                                | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "TAG")                                                                                | false
        "id"   | 2          | new AppMenuDetailEntity(objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1)                                                | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "SINGLE_GROUP", targetMaterialType: 1, objectAccessibleTarget: "[\"id\"]")            | true
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "ALL", targetMaterialType: 1, objectAccessibleTarget: "[\"id\"]")                     | true
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "OBJECT_GROUP_ACCESSIBLE", targetMaterialType: 1, objectAccessibleTarget: "[\"id\"]") | true
    }

    @Unroll
    def "getPaasObjectRule"() {
        given:
        appMenuDetailDAO.getById(*_) >> appMenuDetailEntity
        objectGroupManager.getAllSubGroupWithoutPermission(*_) >> [new ObjectGroupEntity()]
        objectGroupDAO.listGroupByEa(*_) >> [new ObjectGroupEntity()]
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> [new ObjectGroupEntity()]
        when:
        Result<List<String>> result = appMenuTemplateService.getPaasObjectRule("ea", menuId, objectType)
        then:
        result.isSuccess() == expect
        where:
        menuId | objectType | appMenuDetailEntity                                                                                                                                     | expect
        null   | 1          | null                                                                                                                                                    | false
        "id"   | 1          | null                                                                                                                                                    | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "TAG")                                                                                                    | false
        "id"   | 2          | new AppMenuDetailEntity(objectAccessibleRule: "TAG", targetMaterialType: 1)                                                                             | false
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "TAG", targetMaterialType: 1, objectAccessibleTarget: "{\"tagIdList\":[\"demoData\"],\"tagOperator\":1}") | true
        "id"   | 1          | new AppMenuDetailEntity(objectAccessibleRule: "OBJECT_FILTER", targetMaterialType: 1, objectAccessibleTarget: "[]")                                     | true
    }

    @Unroll
    def "needStrictCheckGroup"() {
        given:
        objectGroupManager.getAllSubGroupWithoutPermission(*_) >> [new ObjectGroupEntity()]
        objectGroupDAO.listGroupByEa(*_) >> [new ObjectGroupEntity()]
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> [new ObjectGroupEntity()]
        def spy = Spy(appMenuTemplateService)
        spy.menuDetail(*_) >> new Result(errCode: code, data: data)
        when:
        boolean result = spy.needStrictCheckGroup("ea", 1, menuId)
        then:
        result == expect
        where:
        menuId | code | data                                                                 | expect
        null   | -1   | null                                                                 | false
        "Id"   | -1   | null                                                                 | false
        "Id"   | 0    | new AppMenuDetailVO(objectAccessibleRule: "TAG")                     | false
        "Id"   | 0    | new AppMenuDetailVO(objectAccessibleRule: "SINGLE_GROUP")            | true
        "Id"   | 0    | new AppMenuDetailVO(objectAccessibleRule: "OBJECT_GROUP_ACCESSIBLE") | false
    }

    @Unroll
    def "userList"() {
        given:
        userAppMenuTemplateRelationDAO.list(*_) >> userRelationDTOList
        userRelationManager.buildUserRelationVoList(*_) >> []
        when:
        Result<PageResult<UserRelationVO>> result = appMenuTemplateService.userList(arg)
        then:
        result.isSuccess() == expect
        where:
        arg                                                                | userRelationDTOList     | expect
        new TemplateUserListArg()                                          | []                      | false
        new TemplateUserListArg(pageNum: 1, pageSize: 1, templateId: "id") | []                      | true
        new TemplateUserListArg(pageNum: 1, pageSize: 1, templateId: "id") | [new UserRelationDTO()] | true
    }

    @Unroll
    def "handleFsEmployeeChangeEvent"() {
        given:
        fsAddressBookManager.getFsEmployeeInfoByUserIds(*_) >> fsEmployeeMsgMap
        userAppMenuTemplateRelationDAO.deleteByFsUserId(*_) >> 1
        appMenuTemplateDAO.scanById(*_) >>> [appMenuTemplateEntityList, []]
        userAppMenuTemplateRelationDAO.getAllMenuTemplateIdByUserId(*_) >> ["id"]
        userAppMenuTemplateRelationDAO.batchInsert(*_) >> 1
        eieaConverter.enterpriseAccountToId(*_) >> 1
        redisManager.batchDelete(*_) >> { printf "ddd" }
        when:
        Result<Void> result = appMenuTemplateService.handleFsEmployeeChangeEvent("ea", 1)
        then:
        result.isSuccess() == expect
        where:
        fsEmployeeMsgMap                                                                                         | appMenuTemplateEntityList                                                                           | expect
        [2: new FsAddressBookManager.FSEmployeeMsg()]                                                            | []                                                                                                  | true
        [1: new FsAddressBookManager.FSEmployeeMsg(status: EmployeeEntityStatus.STOP)]                           | []                                                                                                  | true
        [1: new FsAddressBookManager.FSEmployeeMsg(status: EmployeeEntityStatus.NORMAL)]                         | []                                                                                                  | true
        [1: new FsAddressBookManager.FSEmployeeMsg(status: EmployeeEntityStatus.NORMAL, mainDepartmentIds: [1])] | [new AppMenuTemplateEntity(scope: "{}")]                                                            | true
        [1: new FsAddressBookManager.FSEmployeeMsg(status: EmployeeEntityStatus.NORMAL, mainDepartmentIds: [1])] | [new AppMenuTemplateEntity(scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[999999]}}")]      | true
        [1: new FsAddressBookManager.FSEmployeeMsg(status: EmployeeEntityStatus.NORMAL, mainDepartmentIds: [1])] | [new AppMenuTemplateEntity(id: "id", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[2]}}")] | true
        [1: new FsAddressBookManager.FSEmployeeMsg(status: EmployeeEntityStatus.NORMAL, mainDepartmentIds: [1])] | [new AppMenuTemplateEntity(id: "id", scope: "{\"fsAddressBookScope\":{\"departmentIdList\":[1]}}")] | true
    }

    @Unroll
    def "handleQywxEmployeeChangeEvent"() {
        given:
        userRelationManager.getByQywxUserIdList(*_) >> userRelationEntityList
        userAppMenuTemplateRelationDAO.deleteByFsUserId(*_) >> 1
        qywxManager.getAccessToken(*_) >> "token"
        qywxManager.getStaffDetail(*_) >> staffDetail
        appMenuTemplateDAO.scanById(*_) >>> [appMenuTemplateEntityList, []]
        userAppMenuTemplateRelationDAO.getAllMenuTemplateIdByUserId(*_) >> ["id"]
        userAppMenuTemplateRelationDAO.batchInsert(*_) >> 1
        eieaConverter.enterpriseAccountToId(*_) >> 1
        redisManager.batchDelete(*_) >> { printf "ddd" }
        when:
        Result<Void> result = appMenuTemplateService.handleQywxEmployeeChangeEvent("ea", "userId", changeType)
        then:
        result.isSuccess() == expect
        where:
        changeType    | userRelationEntityList                | staffDetail                               | appMenuTemplateEntityList                                                                                   | expect
        "delete_user" | []                                    | null                                      | []                                                                                                          | true
        "delete_user" | [new UserRelationEntity(fsUserId: 1)] | null                                      | []                                                                                                          | true
        "delete_user" | [new UserRelationEntity(fsUserId: 1)] | new StaffDetailResult()                   | []                                                                                                          | true
        "update_user" | [new UserRelationEntity(fsUserId: 1)] | null                                      | [new AppMenuTemplateEntity(scope: "{}")]                                                                    | true
        "update_user" | [new UserRelationEntity(fsUserId: 1)] | new StaffDetailResult(department: [1, 2]) | [new AppMenuTemplateEntity(scope: "{}")]                                                                    | true
        "update_user" | [new UserRelationEntity(fsUserId: 1)] | new StaffDetailResult(department: [1, 2]) | [new AppMenuTemplateEntity(id: "id2", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[999999]}}")] | true
        "update_user" | [new UserRelationEntity(fsUserId: 1)] | new StaffDetailResult(department: [1, 2]) | [new AppMenuTemplateEntity(id: "id", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[999999]}}")]  | true
        "update_user" | [new UserRelationEntity(fsUserId: 1)] | new StaffDetailResult(department: [1, 2]) | [new AppMenuTemplateEntity(id: "id", scope: "{\"qywxAddressBookScope\":{\"departmentIdList\":[4]}}")]       | true
    }

    @Shared
    def shareObjectData = new ObjectData()

    @Unroll
    def "handleMemberChangeEvent"() {
        given:
        crmV2Manager.getDetailIgnoreError(*_) >> memberObject
        userRelationManager.getByMemberId(*_) >> userRelationEntity
        userAppMenuTemplateRelationDAO.deleteByFsUserId(*_) >> 1
        appMenuTemplateDAO.scanById(*_) >>> [[new AppMenuTemplateEntity(id: "id", scope: "{}"), new AppMenuTemplateEntity(id: "id2", scope: "{\"memberScope\":{}}"), new AppMenuTemplateEntity(id: "id3", scope: "{\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}"), new AppMenuTemplateEntity(id: "id4", scope: "{\"memberScope\":{\"filters\":[{\"field_name\":\"member_type\",\"operator\":\"IN\",\"field_values\":[\"employee\"]}]}}")], []]
        userAppMenuTemplateRelationDAO.getAllMenuTemplateIdByUserId(*_) >> ["id3"]
        memberMarketingManager.getPassQueryFilterArg(*_) >> new PaasQueryFilterArg()
        crmV2Manager.countCrmObjectByFilterV3(*_) >> totalCount
        userAppMenuTemplateRelationDAO.batchInsert(*_) >> 1
        shareObjectData.put("member_type", memberType)
        when:
        Result<Void> result = appMenuTemplateService.handleMemberChangeEvent("ea", "memberId", fsUserId)
        then:
        result.isSuccess() == expect
        where:
        fsUserId | memberObject    | userRelationEntity                          | memberType | totalCount | expect
        null     | null            | null                                        | null       | 0          | true
        null     | null            | new UserRelationEntity(fsUserId: 1)         | null       | 0          | true
        null     | shareObjectData | new UserRelationEntity(fsUserId: 1)         | null       | 0          | true
        null     | shareObjectData | new UserRelationEntity(fsUserId: 1)         | "employee" | 0          | true
        null     | shareObjectData | new UserRelationEntity(fsUserId: 1)         | "employee" | 1          | true
        null     | shareObjectData | new UserRelationEntity(fsUserId: 100000000) | "employee" | 1          | true
        null     | shareObjectData | new UserRelationEntity(fsUserId: 300000000) | "employee" | 1          | true
    }
}
