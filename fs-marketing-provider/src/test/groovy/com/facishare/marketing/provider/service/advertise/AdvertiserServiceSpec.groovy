package com.facishare.marketing.provider.service.advertise

import com.facishare.marketing.api.arg.advertiser.AdBigScreenSettingDetailArg
import com.facishare.marketing.api.arg.advertiser.AdBigScreenSettingUpdateArg
import com.facishare.marketing.api.arg.advertiser.AdLeadDataArg
import com.facishare.marketing.api.arg.advertiser.ExportUserMarketingGroupArg
import com.facishare.marketing.common.enums.ExportAdEncryptionEnum
import com.facishare.marketing.common.enums.baidu.AdSourceEnum
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils
import com.facishare.marketing.provider.bo.advertise.AdBigScreenSettingDetailBO
import com.facishare.marketing.provider.dao.MarketingUserGroupDao
import com.facishare.marketing.provider.dao.MarketingUserGroupToUserRelationDao
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity
import com.facishare.marketing.provider.entity.advertiser.AdBigScreenSettingEntity
import com.facishare.marketing.provider.entity.advertiser.AdLeadDataEntity
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.manager.PushSessionManager
import com.facishare.marketing.provider.manager.advertiser.*
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.service.advertiser.AdvertiserServiceImpl
import com.github.trace.TraceContext
import com.google.common.collect.Maps
import spock.lang.Specification
import spock.lang.Unroll

import java.util.Date

class AdvertiserServiceSpec extends Specification {

    // 模拟依赖项
    def adBigScreenManager = Mock(AdBigScreenManager)
    def adAccountManager = Mock(AdAccountManager)
    def adLeadDataManager = Mock(AdLeadDataManager)
    def adMarketingHandlerActionManager = Mock(AdMarketingHandlerActionManager)
    def adCommonManager = Mock(AdCommonManager)
    def marketingUserGroupDao = Mock(MarketingUserGroupDao)
    def marketingUserGroupToUserRelationDao = Mock(MarketingUserGroupToUserRelationDao)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def pushSessionManager = Mock(PushSessionManager)


    // 待测系统
    def advertiserService = new AdvertiserServiceImpl(
            "adBigScreenManager": adBigScreenManager,
            "adAccountManager": adAccountManager,
            "adLeadDataManager": adLeadDataManager,
            "adMarketingHandlerActionManager": adMarketingHandlerActionManager,
            "adCommonManager": adCommonManager,
            "marketingUserGroupDao": marketingUserGroupDao,
            "marketingUserGroupToUserRelationDao": marketingUserGroupToUserRelationDao,
            "userMarketingAccountManager": userMarketingAccountManager,
            "pushSessionManager": pushSessionManager
    )


    @Unroll
    def "initFieldCanEdit"() {
        given:
        AdBigScreenSettingDetailBO defaultSettingDetailBO = new AdBigScreenSettingDetailBO()
        defaultSettingDetailBO.setCustomerAcquisitionCost(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        defaultSettingDetailBO.setLaunchEffectTrend(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        defaultSettingDetailBO.setAcquisitionCustomerConvertFunnel(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        defaultSettingDetailBO.setAccountAcquisitionCustomerCompare(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        defaultSettingDetailBO.setAccountInputOutputAnalysis(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        defaultSettingDetailBO.setOverView(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        defaultSettingDetailBO.setLeadsArealDistributions(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        defaultSettingDetailBO.setConvertPeriod(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        AdBigScreenSettingDetailBO adBigScreenSettingDetailBO = new AdBigScreenSettingDetailBO()
        adBigScreenSettingDetailBO.setCustomerAcquisitionCost(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        adBigScreenSettingDetailBO.setLaunchEffectTrend(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        adBigScreenSettingDetailBO.setAcquisitionCustomerConvertFunnel(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        adBigScreenSettingDetailBO.setAccountAcquisitionCustomerCompare(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        adBigScreenSettingDetailBO.setAccountInputOutputAnalysis(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        adBigScreenSettingDetailBO.setOverView(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        adBigScreenSettingDetailBO.setLeadsArealDistributions(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        adBigScreenSettingDetailBO.setConvertPeriod(new AdBigScreenSettingDetailBO.Setting(fieldList: []))
        when:
        advertiserService.initFieldCanEdit(defaultSettingDetailBO, adBigScreenSettingDetailBO)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "exportUserMarketingGroup"() {
        given:
        marketingUserGroupDao.getById(*_) >> marketingUserGroupEntity
        marketingUserGroupToUserRelationDao.pageListMarketingUserIdsV2(*_) >> userMarketingIdList
        when:
        advertiserService.exportUserMarketingGroup(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                   | marketingUserGroupEntity       | userMarketingIdList
        new ExportUserMarketingGroupArg()                                                     | null                           | null
        new ExportUserMarketingGroupArg(marketingUserGroupId: "id", encryptionType: "sha111") | null                           | null
        new ExportUserMarketingGroupArg(marketingUserGroupId: "id", encryptionType: "sha256") | null                           | null
        new ExportUserMarketingGroupArg(marketingUserGroupId: "id", encryptionType: "sha256") | new MarketingUserGroupEntity() | null
        new ExportUserMarketingGroupArg(marketingUserGroupId: "id", encryptionType: "sha256") | new MarketingUserGroupEntity() | ["id"]
    }

    @Unroll
    def "pushUserMarketingPhoneToFileAssistant"() {
        given:
        marketingUserGroupToUserRelationDao.pageListMarketingUserIdsV2(*_) >>> [["id"], []]
        userMarketingAccountManager.getMarketingUserPhonesV2(*_) >> userMarketingIdToPhoneMap
        pushSessionManager.pushFileToFileAssistant(*_) >> { println "ffff" }
        when:
        advertiserService.pushUserMarketingPhoneToFileAssistant(new ExportUserMarketingGroupArg(), exportAdEncryptionEnum)
        then:
        noExceptionThrown()
        where:
        exportAdEncryptionEnum        | userMarketingIdToPhoneMap
        ExportAdEncryptionEnum.SHA256 | Maps.newHashMap()
        ExportAdEncryptionEnum.SHA256 | ["id": "110"]
        ExportAdEncryptionEnum.MD5    | ["id": "110"]
    }


    @Unroll
    def "bigScreen"() {
        given:
        adAccountManager.queryAccountByEa(*_) >> adAccountList
        adBigScreenManager.bigScreen(*_) >> null
        when:
        advertiserService.bigScreen(ea, 1)
        then:
        noExceptionThrown()
        where:
        ea   | adAccountList
        null | []
        "ea" | []
        "ea" | [new AdAccountEntity()]
    }

    @Unroll
    def "getBigScreenSetting"() {
        given:
        adBigScreenManager.getBigScreenSetting(*_) >> new AdBigScreenSettingEntity(setting: "{}")
        adBigScreenManager.getSelectableTimeList(*_) >> []
        when:
        advertiserService.getBigScreenSetting(ea, 1)
        then:
        noExceptionThrown()
        where:
        ea << [null, "ea"]
    }

    @Unroll
    def "getAdLeadData"() {
        given:
        adBigScreenManager.getBigScreenSetting(*_) >> new AdBigScreenSettingEntity(setting: "{\"accountAcquisitionCustomerCompare\":{\"fieldList\":[{\"canEdit\":true,\"hidden\":false,\"id\":\"8b524863784b46f9a46b3b7f6557befb\",\"name\":\"bj-fxiaoke\"},{\"canEdit\":true,\"hidden\":false,\"id\":\"b2f6f708cfd840d787db0879cbef070a\",\"name\":\"用户*************\"},{\"canEdit\":true,\"hidden\":false,\"id\":\"b3a98f88760344d3b64041727d579c97\",\"name\":\"北京易动纷享科技有限责任公司\"},{\"canEdit\":true,\"hidden\":false,\"id\":\"efc63ba004c04c5f91dad784d78c78e6\",\"name\":\"北京易动纷享科技有限责任公司0\"}],\"name\":\"广告账户获客对比\"},\"accountInputOutputAnalysis\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"cost\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsCount\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsSQLCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"opportunityCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgClickPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgLeadAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgMQLAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgSQLAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgOpportunityAcquisitionPrice\"},{\"canEdit\":false,\"fieldValueList\":[{\"selected\":true,\"value\":\"ACCOUNT\"},{\"selected\":false,\"value\":\"KEYWORD\"},{\"selected\":false,\"value\":\"CAMPAIGN\"}],\"hidden\":false,\"name\":\"showDimension\",\"type\":\"select_one\"},{\"canEdit\":false,\"fieldValueList\":[{\"selected\":true,\"value\":\"5\"},{\"selected\":false,\"value\":\"10\"}],\"hidden\":false,\"name\":\"top\",\"type\":\"select_one\"}],\"name\":\"广告账户投入产出分析\"},\"acquisitionCustomerConvertFunnel\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"pv\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"clicks\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsCount\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsMQLCount\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsSQLCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"opportunityCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"winOpportunityCount\"},{\"canEdit\":false,\"fieldValueList\":[{\"label\":\"潜在线索(Lead)\",\"selected\":false,\"value\":\"Lead\"},{\"label\":\"市场认可线索(MQL)\",\"selected\":true,\"value\":\"MQL\"},{\"label\":\"销售认可线索(SQL)\",\"selected\":true,\"value\":\"SQL\"},{\"label\":\"转商机\",\"selected\":false,\"value\":\"OPP\"}],\"hidden\":false,\"name\":\"mqlDefinition\",\"type\":\"select_many\"},{\"canEdit\":false,\"fieldValueList\":[{\"label\":\"潜在线索(Lead)\",\"selected\":false,\"value\":\"Lead\"},{\"label\":\"市场认可线索(MQL)\",\"selected\":false,\"value\":\"MQL\"},{\"label\":\"销售认可线索(SQL)\",\"selected\":false,\"value\":\"SQL\"},{\"label\":\"转商机\",\"selected\":true,\"value\":\"OPP\"}],\"hidden\":false,\"name\":\"sqlDefinition\",\"type\":\"select_many\"}],\"name\":\"广告获客转化漏斗\"},\"convertPeriod\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"avgLeadToMQL\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgLeadMQLToSQL\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgSQLToWinOpportunity\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"leadToWinOpportunity\"}],\"name\":\"转化周期\"},\"customerAcquisitionCost\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"avgClickPrice\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgLeadAcquisitionPrice\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgMQLAcquisitionPrice\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgSQLAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgOpportunityAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgWinOpportunityAcquisitionPrice\"}],\"name\":\"广告获客成本\"},\"launchEffectTrend\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"COST\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"LEADS\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"WECHAT_FANS\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"QYWX_EXTERNAL_CUSTOMER\"}],\"name\":\"广告投放效果趋势\"},\"leadsArealDistributions\":{\"fieldList\":[{\"canEdit\":false,\"fieldValueList\":[{\"label\":\"全国\",\"selected\":true,\"value\":\"china\"},{\"label\":\"河北省\",\"selected\":false,\"value\":\"hebei\"},{\"label\":\"宁夏\",\"selected\":false,\"value\":\"ningxia\"},{\"label\":\"贵州省\",\"selected\":false,\"value\":\"guizhou\"},{\"label\":\"新疆\",\"selected\":false,\"value\":\"xinjiang\"},{\"label\":\"福建省\",\"selected\":false,\"value\":\"fujian\"},{\"label\":\"海南省\",\"selected\":false,\"value\":\"hainan\"},{\"label\":\"广东省\",\"selected\":false,\"value\":\"guangdong\"},{\"label\":\"广西\",\"selected\":false,\"value\":\"guangxi\"},{\"label\":\"黑龙江省\",\"selected\":false,\"value\":\"heilongjiang\"},{\"label\":\"浙江省\",\"selected\":false,\"value\":\"zhejiang\"},{\"label\":\"青海省\",\"selected\":false,\"value\":\"qinghai\"},{\"label\":\"江苏省\",\"selected\":false,\"value\":\"jiangsu\"},{\"label\":\"河南省\",\"selected\":false,\"value\":\"henan\"},{\"label\":\"山西省\",\"selected\":false,\"value\":\"shanxi\"},{\"label\":\"西藏\",\"selected\":false,\"value\":\"xizang\"},{\"label\":\"云南省\",\"selected\":false,\"value\":\"yunnan\"},{\"label\":\"辽宁省\",\"selected\":false,\"value\":\"liaoning\"},{\"label\":\"湖南省\",\"selected\":false,\"value\":\"hunan\"},{\"label\":\"安徽省\",\"selected\":false,\"value\":\"anhui\"},{\"label\":\"江西省\",\"selected\":false,\"value\":\"jiangxi\"},{\"label\":\"湖北省\",\"selected\":false,\"value\":\"hubei\"},{\"label\":\"甘肃省\",\"selected\":false,\"value\":\"gansu\"},{\"label\":\"陕西省\",\"selected\":false,\"value\":\"shanxi1\"},{\"label\":\"台湾\",\"selected\":false,\"value\":\"taiwan\"},{\"label\":\"山东省\",\"selected\":false,\"value\":\"shandong\"},{\"label\":\"吉林省\",\"selected\":false,\"value\":\"jilin\"},{\"label\":\"四川省\",\"selected\":false,\"value\":\"sichuan\"},{\"label\":\"内蒙古\",\"selected\":false,\"value\":\"neimenggu\"}],\"hidden\":false,\"name\":\"showDimension\",\"type\":\"select_one\"}],\"name\":\"广告线索地域分布\"},\"modulePositions\":[\"customerAcquisitionCost\",\"overView\",\"acquisitionCustomerConvertFunnel\",\"launchEffectTrendList\",\"leadsArealDistributions\",\"leadStageAndSaleSituation\",\"acquisitionCustomerKeywordList\",\"accountInputOutputAnalysisList\",\"convertPeriod\"],\"overView\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"costs\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsCount\"},{\"canEdit\":true,\"hidden\":true,\"name\":\"sqlCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"winOpportunityMoney\"}],\"name\":\"概览设置\"},\"timeSetting\":{\"beginTime\":*************,\"compareBeginTime\":*************,\"compareEndTime\":*************,\"compareTimeType\":\"YoY\",\"endTime\":*************,\"timeRange\":\"THIS_YEAR\"}}")
        adLeadDataManager.getByLeadCreateTimePage(*_) >> leadList

        when:
        advertiserService.getAdLeadData(arg)
        then:
        noExceptionThrown()
        where:
        arg                                        | leadList
        new AdLeadDataArg()                        | []
        new AdLeadDataArg(pageSize: 1, pageNum: 1) | []
        new AdLeadDataArg(pageSize: 1, pageNum: 1) | [new AdLeadDataEntity()]
    }

    @Unroll
    def "updateBigScreenSetting"() {
        given:
        adBigScreenManager.getDefaultBigScreenSetting(*_) >> mockDefaultBigScreenSetting
        adBigScreenManager.updateSetting(*_) >> null

        when:
        def result = advertiserService.updateBigScreenSetting(arg)

        then:
        noExceptionThrown()
        where:
        desc                                 | arg                                                                   | mockDefaultBigScreenSetting     | expectedErrorCode
        "参数校验失败"                       | createArgWithCheckParamFalse()                                        | null                            | "PARAMS_ERROR"
        "时间设置校验失败"                   | createArgWithTimeSettingCheckParamFalse()                             | null                            | "TIME_SETTING_ERROR"
        "比较时间类型错误"                   | createArgWithInvalidCompareTimeType()                                 | null                            | "COMPARE_TIME_TYPE_ERROR"
        "自定义时间超过365天"                | createArgWithCustomizeTimeTooLong()                                   | null                            | "TIME_DIFF_TOO_LONG"
        "自定义时间对比时间超过365天"        | createArgWithCustomizeCompareTimeTooLong()                            | null                            | "TIME_DIFF_TOO_LONG"
        "自定义时间间隔不一致"               | createArgWithCustomizeTimeDayNotEqual()                               | null                            | "CUSTOMIZE_TIME_BETWEEN_DAY_ERROR"
        "自定义时间正常情况"                 | createArgWithValidCustomizeTime()                                     | createDefaultBigScreenSetting() | "CUSTOMER_ACQUISITION_COST_ARG_ERROR"
        "标题过长"                           | createArgWithTitleTooLong()                                           | null                            | "TITLE_TOO_LONG"
        "获客成本设置为null"                 | createArgWithNullCustomerAcquisitionCost()                            | createDefaultBigScreenSetting() | "CUSTOMER_ACQUISITION_COST_ARG_ERROR"
        "获客成本设置名称为空"               | createArgWithEmptyCustomerAcquisitionCostName()                       | createDefaultBigScreenSetting() | "CUSTOMER_ACQUISITION_COST_ARG_ERROR"
        "获客成本设置字段列表为空"           | createArgWithEmptyCustomerAcquisitionCostFieldList()                  | createDefaultBigScreenSetting() | "CUSTOMER_ACQUISITION_COST_ARG_ERROR"
        "获客成本设置字段名为空"             | createArgWithEmptyCustomerAcquisitionCostFieldName()                  | createDefaultBigScreenSetting() | "FIELD_NAME_IS_NULL"
        "获客成本设置修改不可编辑字段"       | createArgWithChangedCannotEditCustomerAcquisitionCostField()          | createDefaultBigScreenSetting() | "FORBID_EDIT_FIELD"
        "投放效果趋势设置为null"             | createArgWithNullLaunchEffectTrend()                                  | createDefaultBigScreenSetting() | "LAUNCH_EFFECT_TREND_ARG_ERROR"
        "投放效果趋势设置名称为空"           | createArgWithEmptyLaunchEffectTrendName()                             | createDefaultBigScreenSetting() | "LAUNCH_EFFECT_TREND_ARG_ERROR"
        "投放效果趋势设置字段列表为空"       | createArgWithEmptyLaunchEffectTrendFieldList()                        | createDefaultBigScreenSetting() | "LAUNCH_EFFECT_TREND_ARG_ERROR"
        "投放效果趋势设置字段名为空"         | createArgWithEmptyLaunchEffectTrendFieldName()                        | createDefaultBigScreenSetting() | "FIELD_NAME_IS_NULL"
        "投放效果趋势修改不可编辑字段"       | createArgWithChangedCannotEditLaunchEffectTrendField()                | createDefaultBigScreenSetting() | "FORBID_EDIT_FIELD"
        "转化漏斗设置为null"                 | createArgWithNullAcquisitionCustomerConvertFunnel()                   | createDefaultBigScreenSetting() | "LAUNCH_EFFECT_TREND_ARG_ERROR"
        "转化漏斗设置名称为空"               | createArgWithEmptyAcquisitionCustomerConvertFunnelName()              | createDefaultBigScreenSetting() | "LAUNCH_EFFECT_TREND_ARG_ERROR"
        "转化漏斗设置字段列表为空"           | createArgWithEmptyAcquisitionCustomerConvertFunnelFieldList()         | createDefaultBigScreenSetting() | "LAUNCH_EFFECT_TREND_ARG_ERROR"
        "转化漏斗设置字段名为空"             | createArgWithEmptyAcquisitionCustomerConvertFunnelFieldName()         | createDefaultBigScreenSetting() | "FIELD_NAME_IS_NULL"
        "转化漏斗修改不可编辑字段"           | createArgWithChangedCannotEditAcquisitionCustomerConvertFunnelField() | createDefaultBigScreenSetting() | "FORBID_EDIT_FIELD"
        "转化漏斗字段值列表为空"             | createArgWithEmptyFieldValueList()                                    | createDefaultBigScreenSetting() | "FIELD_VALUE_LIST_NOT_FOUND"
        "转化漏斗选择字段值为空"             | createArgWithNoSelectedFieldValue()                                   | createDefaultBigScreenSetting() | "SELECT_FIELD_VALUE_IS_EMPTY"
        "单选字段选择多个值"                 | createArgWithSelectOneFieldMultipleValues()                           | createDefaultBigScreenSetting() | "SELECT_ONE_FIELD_VALUE_ERROR"
        "账户获客对比设置为null"             | createArgWithNullAccountAcquisitionCustomerCompare()                  | createDefaultBigScreenSetting() | "ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR"
        "账户获客对比设置名称为空"           | createArgWithEmptyAccountAcquisitionCustomerCompareName()             | createDefaultBigScreenSetting() | "ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR"
        "账户获客对比设置字段列表为空"       | createArgWithEmptyAccountAcquisitionCustomerCompareFieldList()        | createDefaultBigScreenSetting() | "ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR"
        "账户获客对比设置字段名为空"         | createArgWithEmptyAccountAcquisitionCustomerCompareFieldName()        | createDefaultBigScreenSetting() | "FIELD_NAME_IS_NULL"
        "账户获客对比设置字段ID为空"         | createArgWithEmptyAccountAcquisitionCustomerCompareFieldId()          | createDefaultBigScreenSetting() | "FIELD_ID_IS_NULL"
        "账户投入产出分析设置为null"         | createArgWithNullAccountInputOutputAnalysis()                         | createDefaultBigScreenSetting() | "ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR"
        "账户投入产出分析设置名称为空"       | createArgWithEmptyAccountInputOutputAnalysisName()                    | createDefaultBigScreenSetting() | "ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR"
        "账户投入产出分析设置字段列表为空"   | createArgWithEmptyAccountInputOutputAnalysisFieldList()               | createDefaultBigScreenSetting() | "ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR"
        "账户投入产出分析字段值列表为空"     | createArgWithEmptyAccountInputOutputAnalysisFieldValueList()          | createDefaultBigScreenSetting() | "FIELD_VALUE_LIST_NOT_FOUND"
        "账户投入产出分析选择字段值为空"     | createArgWithNoSelectedAccountInputOutputAnalysisFieldValue()         | createDefaultBigScreenSetting() | "SELECT_FIELD_VALUE_IS_EMPTY"
        "账户投入产出分析单选字段选择多个值" | createArgWithSelectOneAccountInputOutputAnalysisFieldMultipleValues() | createDefaultBigScreenSetting() | "SELECT_ONE_FIELD_VALUE_ERROR"
        "账户投入产出分析设置字段名为空"     | createArgWithEmptyAccountInputOutputAnalysisFieldName()               | createDefaultBigScreenSetting() | "FIELD_NAME_IS_NULL"
        "账户投入产出分析修改不可编辑字段"   | createArgWithChangedCannotEditAccountInputOutputAnalysisField()       | createDefaultBigScreenSetting() | "FORBID_EDIT_FIELD"
        "模块位置为空"                       | createArgWithEmptyModulePositions()                                   | createDefaultBigScreenSetting() | "MODULE_POSITION_NOT_FOUND"
        "概览设置为null"                     | createArgWithNullOverView()                                           | createDefaultBigScreenSetting() | "OVER_VIEW_SETTING_NOT_FOUND"
        "概览设置修改不可编辑字段"           | createArgWithChangedCannotEditOverViewField()                         | createDefaultBigScreenSetting() | "FORBID_EDIT_FIELD"
        "地域分布设置为null"                 | createArgWithNullLeadsArealDistributions()                            | createDefaultBigScreenSetting() | "AREAL_DISTRIBUTE_SETTING_NOT_FOUND"
        "地域分布设置修改不可编辑字段"       | createArgWithChangedCannotEditLeadsArealDistributionsField()          | createDefaultBigScreenSetting() | "FORBID_EDIT_FIELD"
        "地域分布字段值列表为空"             | createArgWithEmptyLeadsArealDistributionsFieldValueList()             | createDefaultBigScreenSetting() | "FIELD_VALUE_LIST_NOT_FOUND"
        "地域分布选择字段值为空"             | createArgWithNoSelectedLeadsArealDistributionsFieldValue()            | createDefaultBigScreenSetting() | "SELECT_FIELD_VALUE_IS_EMPTY"
        "地域分布单选字段选择多个值"         | createArgWithSelectOneLeadsArealDistributionsFieldMultipleValues()    | createDefaultBigScreenSetting() | "SELECT_ONE_FIELD_VALUE_ERROR"
        "转化周期设置为null"                 | createArgWithNullConvertPeriod()                                      | createDefaultBigScreenSetting() | "CONVERT_PERIOD_SETTING_NOT_FOUND"
        "转化周期设置修改不可编辑字段"       | createArgWithChangedCannotEditConvertPeriodField()                    | createDefaultBigScreenSetting() | "FORBID_EDIT_FIELD"
        "成功情况"                           | createValidArg()                                                      | createDefaultBigScreenSetting() | null
    }

    @Unroll
    def "refreshPrototypeRoomAccountData"() {
        given: "Mock静态方法和依赖"
        // Mock TraceContext静态方法
        def mockTraceContext = Mock(TraceContext)
        GroovyMock(TraceContext, global: true)
        TraceContext.get() >> mockTraceContext

        // Mock ThreadPoolUtils静态方法
        GroovyMock(ThreadPoolUtils, global: true)

        // 模拟内部方法调用所需的依赖
        adAccountManager.getAllAdPrototypeRoomAccount() >> []

        when: "调用方法"
        def result = advertiserService.refreshPrototypeRoomAccountData()

        then: "验证结果和调用"
        noExceptionThrown()
    }

    @Unroll
    def "refreshPrototypeRoomAccountDataInner"() {
        given:
        adAccountManager.getAllAdPrototypeRoomAccount() >> adAccountList

        if (adAccountList) {
            // 为每个账号模拟 AdMarketingActionManager
            adMarketingHandlerActionManager.getAdMarketingActionManager(*_) >> new BaiduAdMarketingManager(adAccountManager: adAccountManager)
            adAccountManager.getAdPrototypeRoomAccount(*_) >> []
            adCommonManager.createAdPrototypeRoomLeadData(*_) >> null
        }

        when:
        advertiserService.refreshPrototypeRoomAccountDataInner()

        then:
        noExceptionThrown()
        where:
        desc             | adAccountList
        "账号列表为空"   | []
        "账号列表为null" | null
        "单个ea单个账号" | [createAdAccountEntity("ea1", 1, "baidu")]
        "单个ea多个账号" | [createAdAccountEntity("ea1", 1, "baidu"), createAdAccountEntity("ea1", 1, "baidu")]
        "多个ea各有账号" | [createAdAccountEntity("ea1", 1, "baidu"), createAdAccountEntity("ea2", 1, "juliang")]
        "多种平台账号"   | [createAdAccountEntity("ea1", 1, "baidu"), createAdAccountEntity("ea1", 1, "juliang"), createAdAccountEntity("ea1", 1, "tencent")]
    }

    def "refreshPrototypeRoomAccountDataInner 详细测试覆盖所有分支"() {
        given: "准备测试数据"
        def adAccount1 = createAdAccountEntity("ea1", 1, "baidu")
        def adAccount2 = createAdAccountEntity("ea1", 1, "juliang")
        def adAccount3 = createAdAccountEntity("ea2", 2, "tencent")
        def adAccountList = [adAccount1, adAccount2, adAccount3]

        adAccountManager.getAllAdPrototypeRoomAccount() >> adAccountList

        adMarketingHandlerActionManager.getAdMarketingActionManager(*_) >> new BaiduAdMarketingManager(adAccountManager:  adAccountManager)
        adAccountManager.getAdPrototypeRoomAccount(*_) >> []
        adCommonManager.createAdPrototypeRoomLeadData(*_) >> null

        when: "调用方法"
        advertiserService.refreshPrototypeRoomAccountDataInner()

        then: "验证各种调用"
        noExceptionThrown()
    }

    // 辅助方法：创建各种测试参数
    private AdBigScreenSettingUpdateArg createArgWithCheckParamFalse() {
        return new AdBigScreenSettingUpdateArg() {
            @Override
            boolean checkParam() {
                return false
            }
        }
    }

    private AdBigScreenSettingUpdateArg createArgWithTimeSettingCheckParamFalse() {
        def arg = new AdBigScreenSettingUpdateArg()
        arg.setSetting(new AdBigScreenSettingDetailArg())
        arg.getSetting().setTimeSetting(new AdBigScreenSettingDetailArg.TimeSetting() {
            @Override
            boolean checkParam() {
                return false
            }
        })
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithInvalidCompareTimeType() {
        def arg = new AdBigScreenSettingUpdateArg()
        arg.setSetting(new AdBigScreenSettingDetailArg())
        arg.getSetting().setTimeSetting(new AdBigScreenSettingDetailArg.TimeSetting())
        arg.getSetting().getTimeSetting().setCompareTimeType("INVALID")
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithCustomizeTimeTooLong() {
        // 从有效参数开始，然后修改时间设置
        def arg = createValidArg()

        // 修改为自定义时间，但时间跨度超过365天
        arg.getSetting().getTimeSetting().setTimeRange("CUSTOMIZE")
        arg.getSetting().getTimeSetting().setCompareTimeType("YoY")

        Date now = new Date()
        Date yearAgo = new Date(now.time - 400L * 24 * 60 * 60 * 1000) // 400天前
        arg.getSetting().getTimeSetting().setBeginTime(yearAgo)
        arg.getSetting().getTimeSetting().setEndTime(now)
        arg.getSetting().getTimeSetting().setCompareBeginTime(yearAgo)
        arg.getSetting().getTimeSetting().setCompareEndTime(now)

        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithCustomizeTimeDayNotEqual() {
        // 从有效参数开始，然后修改时间设置
        def arg = createValidArg()

        // 修改为自定义时间，但两个时间段天数不一致
        arg.getSetting().getTimeSetting().setTimeRange("CUSTOMIZE")
        arg.getSetting().getTimeSetting().setCompareTimeType("YoY")

        Date now = new Date()
        Date tenDaysAgo = new Date(now.time - 10L * 24 * 60 * 60 * 1000)
        Date fiveDaysAgo = new Date(now.time - 5L * 24 * 60 * 60 * 1000)

        arg.getSetting().getTimeSetting().setBeginTime(tenDaysAgo)
        arg.getSetting().getTimeSetting().setEndTime(now)
        arg.getSetting().getTimeSetting().setCompareBeginTime(tenDaysAgo)
        arg.getSetting().getTimeSetting().setCompareEndTime(fiveDaysAgo)

        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithCustomizeCompareTimeTooLong() {
        // 从有效参数开始，然后修改时间设置
        def arg = createValidArg()

        // 修改为自定义时间，主时间范围正常，但对比时间范围超过365天
        arg.getSetting().getTimeSetting().setTimeRange("CUSTOMIZE")
        arg.getSetting().getTimeSetting().setCompareTimeType("YoY")

        Date now = new Date()
        Date tenDaysAgo = new Date(now.time - 10L * 24 * 60 * 60 * 1000)
        Date yearAgo = new Date(now.time - 400L * 24 * 60 * 60 * 1000) // 400天前

        // 主时间范围正常（10天），但对比时间范围超过365天
        arg.getSetting().getTimeSetting().setBeginTime(tenDaysAgo)
        arg.getSetting().getTimeSetting().setEndTime(now)
        arg.getSetting().getTimeSetting().setCompareBeginTime(yearAgo)
        arg.getSetting().getTimeSetting().setCompareEndTime(now)

        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithValidCustomizeTime() {
        // 从有效参数开始，然后修改时间设置
        def arg = createValidArg()

        // 修改为自定义时间，时间范围合理
        arg.getSetting().getTimeSetting().setTimeRange("CUSTOMIZE")
        arg.getSetting().getTimeSetting().setCompareTimeType("YoY")

        Date now = new Date()
        Date tenDaysAgo = new Date(now.time - 10L * 24 * 60 * 60 * 1000) // 10天前
        Date twentyDaysAgo = new Date(now.time - 20L * 24 * 60 * 60 * 1000) // 20天前
        Date thirtyDaysAgo = new Date(now.time - 30L * 24 * 60 * 60 * 1000) // 30天前

        // 两个时间段都是10天，符合要求
        arg.getSetting().getTimeSetting().setBeginTime(tenDaysAgo)
        arg.getSetting().getTimeSetting().setEndTime(now)
        arg.getSetting().getTimeSetting().setCompareBeginTime(thirtyDaysAgo)
        arg.getSetting().getTimeSetting().setCompareEndTime(twentyDaysAgo)

        // 故意设置一个会触发错误的情况，以确保能命中自定义时间的分支而不是直接返回成功
        // 这里我们设置获客成本为null，这样就会在通过自定义时间验证后触发"CUSTOMER_ACQUISITION_COST_ARG_ERROR"
        arg.getSetting().setCustomerAcquisitionCost(null)

        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithTitleTooLong() {
        def arg = createValidArg()
        arg.setTitle("这是一个超过十五个字符的标题用于测试")
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNullCustomerAcquisitionCost() {
        def arg = createValidArg()
        arg.getSetting().setCustomerAcquisitionCost(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyCustomerAcquisitionCostName() {
        def arg = createValidArg()
        arg.getSetting().getCustomerAcquisitionCost().setName("")
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyCustomerAcquisitionCostFieldList() {
        def arg = createValidArg()
        arg.getSetting().getCustomerAcquisitionCost().setFieldList([])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyCustomerAcquisitionCostFieldName() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("")
        arg.getSetting().getCustomerAcquisitionCost().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithChangedCannotEditCustomerAcquisitionCostField() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("avgClickPrice") // 这是不可编辑的字段
        field.setHidden(true) // 修改隐藏状态
        arg.getSetting().getCustomerAcquisitionCost().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNullLaunchEffectTrend() {
        def arg = createValidArg()
        arg.getSetting().setLaunchEffectTrend(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyLaunchEffectTrendName() {
        def arg = createValidArg()
        arg.getSetting().getLaunchEffectTrend().setName("")
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyLaunchEffectTrendFieldList() {
        def arg = createValidArg()
        arg.getSetting().getLaunchEffectTrend().setFieldList([])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyLaunchEffectTrendFieldName() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("")
        arg.getSetting().getLaunchEffectTrend().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithChangedCannotEditLaunchEffectTrendField() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("COST") // 这是不可编辑的字段
        field.setHidden(true) // 修改隐藏状态
        arg.getSetting().getLaunchEffectTrend().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNullAcquisitionCustomerConvertFunnel() {
        def arg = createValidArg()
        arg.getSetting().setAcquisitionCustomerConvertFunnel(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAcquisitionCustomerConvertFunnelName() {
        def arg = createValidArg()
        arg.getSetting().getAcquisitionCustomerConvertFunnel().setName("")
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAcquisitionCustomerConvertFunnelFieldList() {
        def arg = createValidArg()
        arg.getSetting().getAcquisitionCustomerConvertFunnel().setFieldList([])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAcquisitionCustomerConvertFunnelFieldName() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("")
        arg.getSetting().getAcquisitionCustomerConvertFunnel().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithChangedCannotEditAcquisitionCustomerConvertFunnelField() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("pv") // 这是不可编辑的字段
        field.setHidden(true) // 修改隐藏状态
        arg.getSetting().getAcquisitionCustomerConvertFunnel().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyFieldValueList() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("mqlDefinition")
        field.setType("select_many")
        field.setFieldValueList([])
        arg.getSetting().getAcquisitionCustomerConvertFunnel().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNoSelectedFieldValue() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("mqlDefinition")
        field.setType("select_many")
        def fieldValue = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue.setSelected(false)
        field.setFieldValueList([fieldValue])
        arg.getSetting().getAcquisitionCustomerConvertFunnel().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithSelectOneFieldMultipleValues() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("mqlDefinition")
        field.setType("select_one")
        def fieldValue1 = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue1.setSelected(true)
        def fieldValue2 = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue2.setSelected(true)
        field.setFieldValueList([fieldValue1, fieldValue2])
        arg.getSetting().getAcquisitionCustomerConvertFunnel().setFieldList([field])
        return arg
    }

    // 账户获客对比相关方法
    private AdBigScreenSettingUpdateArg createArgWithNullAccountAcquisitionCustomerCompare() {
        def arg = createValidArg()
        arg.getSetting().setAccountAcquisitionCustomerCompare(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountAcquisitionCustomerCompareName() {
        def arg = createValidArg()
        arg.getSetting().getAccountAcquisitionCustomerCompare().setName("")
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountAcquisitionCustomerCompareFieldList() {
        def arg = createValidArg()
        arg.getSetting().getAccountAcquisitionCustomerCompare().setFieldList([])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountAcquisitionCustomerCompareFieldName() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("")
        field.setId("test-id")
        arg.getSetting().getAccountAcquisitionCustomerCompare().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountAcquisitionCustomerCompareFieldId() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("test-name")
        field.setId("")
        arg.getSetting().getAccountAcquisitionCustomerCompare().setFieldList([field])
        return arg
    }

    // 账户投入产出分析相关方法
    private AdBigScreenSettingUpdateArg createArgWithNullAccountInputOutputAnalysis() {
        def arg = createValidArg()
        arg.getSetting().setAccountInputOutputAnalysis(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountInputOutputAnalysisName() {
        def arg = createValidArg()
        arg.getSetting().getAccountInputOutputAnalysis().setName("")
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountInputOutputAnalysisFieldList() {
        def arg = createValidArg()
        arg.getSetting().getAccountInputOutputAnalysis().setFieldList([])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountInputOutputAnalysisFieldValueList() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("showDimension")
        field.setType("select_one")
        field.setFieldValueList([])
        arg.getSetting().getAccountInputOutputAnalysis().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNoSelectedAccountInputOutputAnalysisFieldValue() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("showDimension")
        field.setType("select_one")
        def fieldValue = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue.setSelected(false)
        field.setFieldValueList([fieldValue])
        arg.getSetting().getAccountInputOutputAnalysis().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithSelectOneAccountInputOutputAnalysisFieldMultipleValues() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("showDimension")
        field.setType("select_one")
        def fieldValue1 = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue1.setSelected(true)
        def fieldValue2 = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue2.setSelected(true)
        field.setFieldValueList([fieldValue1, fieldValue2])
        arg.getSetting().getAccountInputOutputAnalysis().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyAccountInputOutputAnalysisFieldName() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("")
        arg.getSetting().getAccountInputOutputAnalysis().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithChangedCannotEditAccountInputOutputAnalysisField() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("cost") // 这是不可编辑的字段
        field.setHidden(true) // 修改隐藏状态
        arg.getSetting().getAccountInputOutputAnalysis().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyModulePositions() {
        def arg = createValidArg()
        arg.getSetting().setModulePositions([])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNullOverView() {
        def arg = createValidArg()
        arg.getSetting().setOverView(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithChangedCannotEditOverViewField() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("costs") // 这是不可编辑的字段
        field.setHidden(true) // 修改隐藏状态
        arg.getSetting().getOverView().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNullLeadsArealDistributions() {
        def arg = createValidArg()
        arg.getSetting().setLeadsArealDistributions(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithChangedCannotEditLeadsArealDistributionsField() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("showDimension") // 这是不可编辑的字段
        field.setHidden(true) // 修改隐藏状态
        arg.getSetting().getLeadsArealDistributions().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithEmptyLeadsArealDistributionsFieldValueList() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("showDimension")
        field.setType("select_one")
        field.setFieldValueList([])
        arg.getSetting().getLeadsArealDistributions().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNoSelectedLeadsArealDistributionsFieldValue() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("showDimension")
        field.setType("select_one")
        def fieldValue = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue.setSelected(false)
        field.setFieldValueList([fieldValue])
        arg.getSetting().getLeadsArealDistributions().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithSelectOneLeadsArealDistributionsFieldMultipleValues() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("showDimension")
        field.setType("select_one")
        def fieldValue1 = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue1.setSelected(true)
        def fieldValue2 = new AdBigScreenSettingDetailArg.FieldValue()
        fieldValue2.setSelected(true)
        field.setFieldValueList([fieldValue1, fieldValue2])
        arg.getSetting().getLeadsArealDistributions().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithNullConvertPeriod() {
        def arg = createValidArg()
        arg.getSetting().setConvertPeriod(null)
        return arg
    }

    private AdBigScreenSettingUpdateArg createArgWithChangedCannotEditConvertPeriodField() {
        def arg = createValidArg()
        def field = new AdBigScreenSettingDetailArg.Field()
        field.setName("avgLeadToMQL") // 这是不可编辑的字段
        field.setHidden(true) // 修改隐藏状态
        arg.getSetting().getConvertPeriod().setFieldList([field])
        return arg
    }

    private AdBigScreenSettingUpdateArg createValidArg() {
        def arg = new AdBigScreenSettingUpdateArg()
        arg.setEa("test-ea")
        arg.setTitle("测试标题")

        def setting = new AdBigScreenSettingDetailArg()
        arg.setSetting(setting)

        // 时间设置
        def timeSetting = new AdBigScreenSettingDetailArg.TimeSetting()
        timeSetting.setCompareTimeType("YoY")
        timeSetting.setTimeRange("THIS_YEAR")
        setting.setTimeSetting(timeSetting)

        // 获客成本设置
        def customerAcquisitionCost = new AdBigScreenSettingDetailArg.Setting()
        customerAcquisitionCost.setName("广告获客成本")
        def costField = new AdBigScreenSettingDetailArg.Field()
        costField.setName("avgOpportunityAcquisitionPrice")
        customerAcquisitionCost.setFieldList([costField])
        setting.setCustomerAcquisitionCost(customerAcquisitionCost)

        // 投放效果趋势设置
        def launchEffectTrend = new AdBigScreenSettingDetailArg.Setting()
        launchEffectTrend.setName("广告投放效果趋势")
        def trendField = new AdBigScreenSettingDetailArg.Field()
        trendField.setName("WECHAT_FANS")
        launchEffectTrend.setFieldList([trendField])
        setting.setLaunchEffectTrend(launchEffectTrend)

        // 转化漏斗设置
        def acquisitionCustomerConvertFunnel = new AdBigScreenSettingDetailArg.Setting()
        acquisitionCustomerConvertFunnel.setName("广告获客转化漏斗")
        def funnelField = new AdBigScreenSettingDetailArg.Field()
        funnelField.setName("opportunityCount")
        acquisitionCustomerConvertFunnel.setFieldList([funnelField])
        setting.setAcquisitionCustomerConvertFunnel(acquisitionCustomerConvertFunnel)

        // 账户获客对比设置
        def accountAcquisitionCustomerCompare = new AdBigScreenSettingDetailArg.Setting()
        accountAcquisitionCustomerCompare.setName("广告账户获客对比")
        def compareField = new AdBigScreenSettingDetailArg.Field()
        compareField.setName("test-account")
        compareField.setId("test-id")
        accountAcquisitionCustomerCompare.setFieldList([compareField])
        setting.setAccountAcquisitionCustomerCompare(accountAcquisitionCustomerCompare)

        // 账户投入产出分析设置
        def accountInputOutputAnalysis = new AdBigScreenSettingDetailArg.Setting()
        accountInputOutputAnalysis.setName("广告账户投入产出分析")
        def analysisField = new AdBigScreenSettingDetailArg.Field()
        analysisField.setName("opportunityCount")
        accountInputOutputAnalysis.setFieldList([analysisField])
        setting.setAccountInputOutputAnalysis(accountInputOutputAnalysis)

        // 模块位置
        setting.setModulePositions(["customerAcquisitionCost", "overView"])

        // 概览设置
        def overView = new AdBigScreenSettingDetailArg.Setting()
        overView.setName("概览设置")
        def overViewField = new AdBigScreenSettingDetailArg.Field()
        overViewField.setName("winOpportunityMoney")
        overView.setFieldList([overViewField])
        setting.setOverView(overView)

        // 地域分布设置
        def leadsArealDistributions = new AdBigScreenSettingDetailArg.Setting()
        leadsArealDistributions.setName("广告线索地域分布")
        def arealField = new AdBigScreenSettingDetailArg.Field()
        arealField.setName("showDimension")
        leadsArealDistributions.setFieldList([arealField])
        setting.setLeadsArealDistributions(leadsArealDistributions)

        // 转化周期设置
        def convertPeriod = new AdBigScreenSettingDetailArg.Setting()
        convertPeriod.setName("转化周期")
        def periodField = new AdBigScreenSettingDetailArg.Field()
        periodField.setName("avgSQLToWinOpportunity")
        convertPeriod.setFieldList([periodField])
        setting.setConvertPeriod(convertPeriod)

        return arg
    }

    private AdBigScreenSettingEntity createDefaultBigScreenSetting() {
        def entity = new AdBigScreenSettingEntity()
        entity.setSetting("""{
            "customerAcquisitionCost": {
                "name": "广告获客成本",
                "fieldList": [
                    {"name": "avgClickPrice", "canEdit": false, "hidden": false},
                    {"name": "avgOpportunityAcquisitionPrice", "canEdit": true, "hidden": false}
                ]
            },
            "launchEffectTrend": {
                "name": "广告投放效果趋势",
                "fieldList": [
                    {"name": "COST", "canEdit": false, "hidden": false},
                    {"name": "WECHAT_FANS", "canEdit": true, "hidden": false}
                ]
            },
            "acquisitionCustomerConvertFunnel": {
                "name": "广告获客转化漏斗",
                "fieldList": [
                    {"name": "pv", "canEdit": false, "hidden": false},
                    {"name": "opportunityCount", "canEdit": true, "hidden": false}
                ]
            },
            "accountAcquisitionCustomerCompare": {
                "name": "广告账户获客对比",
                "fieldList": [
                    {"name": "test-account", "canEdit": true, "hidden": false}
                ]
            },
            "accountInputOutputAnalysis": {
                "name": "广告账户投入产出分析",
                "fieldList": [
                    {"name": "cost", "canEdit": false, "hidden": false},
                    {"name": "opportunityCount", "canEdit": true, "hidden": false},
                    {"name": "showDimension", "canEdit": false, "hidden": false, "type": "select_one", "fieldValueList": [{"selected": true, "value": "ACCOUNT"}]}
                ]
            },
            "overView": {
                "name": "概览设置",
                "fieldList": [
                    {"name": "costs", "canEdit": false, "hidden": false},
                    {"name": "winOpportunityMoney", "canEdit": true, "hidden": false}
                ]
            },
            "leadsArealDistributions": {
                "name": "广告线索地域分布",
                "fieldList": [
                    {"name": "showDimension", "canEdit": false, "hidden": false, "type": "select_one", "fieldValueList": [{"selected": true, "value": "china"}]}
                ]
            },
            "convertPeriod": {
                "name": "转化周期",
                "fieldList": [
                    {"name": "avgLeadToMQL", "canEdit": false, "hidden": false},
                    {"name": "avgSQLToWinOpportunity", "canEdit": true, "hidden": false}
                ]
            }
        }""")
        return entity
    }

    // 辅助方法：创建广告账号实体
    private AdAccountEntity createAdAccountEntity(String ea, long accountId, String source) {
        def entity = new AdAccountEntity()
        entity.setEa(ea)
        entity.setAccountId(accountId)

        // 根据source设置对应的平台源
        switch (source) {
            case "baidu":
                entity.setSource("baidu")
                break
            case "juliang":
                entity.setSource("juliang")
                break
            case "tencent":
                entity.setSource("tencent")
                break
            default:
                entity.setSource("baidu")
        }

        return entity
    }

}
