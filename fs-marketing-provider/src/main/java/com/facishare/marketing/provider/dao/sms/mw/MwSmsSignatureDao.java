package com.facishare.marketing.provider.dao.sms.mw;

import com.facishare.marketing.provider.entity.sms.mw.MwApplyFileEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwApplyWaybillEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSignatureEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

/**
 * Created by ranluch on 2019/2/18.
 */
public interface MwSmsSignatureDao {
    @Insert("INSERT INTO mw_sms_signature(id, ea, svr_type, svr_name, remark, status, creator_user_id, creator_name, account_id, marketing_account_id, industry) VALUES(" +
        "        #{obj.id},\n" +
        "        #{obj.ea},\n" +
        "        #{obj.svrType},\n" +
        "        #{obj.svrName},\n" +
        "        #{obj.remark},\n" +
        "        #{obj.status},\n" +
        "        #{obj.creatorUserId},\n" +
        "        #{obj.creatorName},\n" +
        "        #{obj.accountId},\n" +
        "        #{obj.marketingAccountId},\n" +
        "        #{obj.industry} )")
    boolean addSignature(@Param("obj") MwSmsSignatureEntity signatureEntity);

    @Select("SELECT wss.* FROM mw_sms_signature wss join mw_account wa on wa.id=wss.account_id WHERE wss.ea = #{ea} and wa.type = 1")
    MwSmsSignatureEntity getSignatureByEa(@Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_signature WHERE id = #{id}")
    MwSmsSignatureEntity getSignatureById(@Param("id") String id,@Param("ea") String ea);



    @Update("UPDATE mw_sms_signature SET status = 3  WHERE ea=#{ea}")
    boolean deleteSignatureByEa(@Param("ea") String ea);

    @Update("<script>"
        + "UPDATE mw_sms_signature \n"
        + "SET \n"
        + "<if test=\"reply != null\">\n"
        + "reply = #{reply}, \n"
        + "</if>\n"
        + "<if test=\"remark != null \">\n"
        + "remark = #{remark}, \n"
        + "</if>\n"
        + "<if test=\"svrType != null\">\n"
        + "svr_type = #{svrType}, \n"
        + "</if>\n"
        + "<if test=\"svrName != null\">\n"
        + "svr_name = #{svrName}, \n"
        + "</if>\n"
        + "<if test=\"status != null\">\n"
        + "status = #{status}, \n"
        + "</if>\n"
        + "update_time = now()\n"
        + "WHERE id = #{id}"
        + "</script>")
    boolean updateSignature(@Param("id") String id, @Param("reply") String reply, @Param("svrType") String svrType, @Param("svrName") String svrName, @Param("remark") String remark, @Param("status") Integer status,@Param("ea") String ea);

    @Insert("INSERT INTO mw_apply_file(id, ea, name, apath, type, waybill_id, creator_user_id, creator_name) VALUES(" +
        "        #{obj.id},\n" +
        "        #{obj.ea},\n" +
        "        #{obj.name},\n" +
        "        #{obj.apath},\n" +
        "        #{obj.type},\n" +
        "        #{obj.waybillId},\n" +
        "        #{obj.creatorUserId},\n" +
        "        #{obj.creatorName}\n" +
        "        )")
    boolean addSignatureApplyFile(@Param("obj") MwApplyFileEntity applyFileEntity);

    @Insert("INSERT INTO mw_apply_waybill(id, ea, contact, phone, waybill_no, creator_user_id, creator_name) VALUES(" +
        "        #{obj.id},\n" +
        "        #{obj.ea},\n" +
        "        #{obj.contact},\n" +
        "        #{obj.phone},\n" +
        "        #{obj.waybillNo},\n" +
        "        #{obj.creatorUserId},\n" +
        "        #{obj.creatorName}\n" +
        "        )")
    boolean addSignatureApplyWaybill(@Param("obj") MwApplyWaybillEntity waybillEntity);


    @Update("<script>"
        + "UPDATE mw_apply_file \n"
        + "SET \n"
        + "<if test=\"name != null\">\n"
        + "name = #{name}, \n"
        + "</if>\n"
        + "<if test=\"apath != null \">\n"
        + "apath = #{apath}, \n"
        + "</if>\n"
        + "<if test=\"waybillId != null\">\n"
        + "waybill_id = #{waybillId}, \n"
        + "</if>\n"
        + "update_time = now()\n"
        + "WHERE id = #{id}"
        + "</script>")
    boolean updateSignatureApplyFile(@Param("id") String id, @Param("name") String name, @Param("apath") String apath, @Param("waybillId") String waybillId,@Param("ea") String ea);

    @Select("SELECT * FROM mw_apply_file WHERE ea = #{ea}")
    List<MwApplyFileEntity> getApplyFileByEa(@Param("ea") String ea);

    @Select("SELECT * FROM mw_apply_file WHERE ea = #{ea} and type = #{type}")
    MwApplyFileEntity getApplyFileByEaAndType(@Param("ea") String ea, @Param("type") Integer type);


    @Select("SELECT * FROM mw_apply_waybill WHERE ea = #{ea} order by create_time desc")
    List<MwApplyWaybillEntity> getApplyWaybillByEa(@Param("ea") String ea);


    @Select("SELECT * FROM mw_apply_waybill WHERE ea = #{ea} and waybill_no = #{waybillNo}")
    MwApplyWaybillEntity getApplyWaybillByEaAndWaybill(@Param("ea") String ea, @Param("waybillNo") String waybillNo);

    @Update("<script>"
        + "UPDATE mw_apply_waybill \n"
        + "SET \n"
        + "<if test=\"contact != null\">\n"
        + "contact = #{contact}, \n"
        + "</if>\n"
        + "<if test=\"phone != null \">\n"
        + "phone = #{phone}, \n"
        + "</if>\n"
        + "<if test=\"waybillNo != null\">\n"
        + "waybill_no = #{waybillNo}, \n"
        + "</if>\n"
        + "update_time = now()\n"
        + "WHERE id = #{id}"
        + "</script>")
    boolean updateSignatureApplyWaybill(@Param("id") String id, @Param("contact") String contact, @Param("phone") String phone, @Param("waybillNo") String waybillNo,@Param("ea") String ea);

    @Select("<script> SELECT DISTINCT  ea   FROM mw_sms_signature where status = 0 </script>")
    List<String> findEaAll(@Param("ea") String ea);

    @Select("<script> SELECT DISTINCT ea FROM mw_sms_signature where status = 0 and ea in\n"
            + "<foreach collection = 'eaList' item = 'ea' open = '(' separator = ',' close = ')'>"
            +     " #{ea}"
            + "</foreach> "
            + "</script>")
    List<String> findApplyEaByEas(@Param("eaList") Set<String> eaList, @Param("ea") String ea);
}
