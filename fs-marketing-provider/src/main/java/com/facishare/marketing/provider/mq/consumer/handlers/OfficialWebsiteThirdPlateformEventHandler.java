/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.mq.consumer.handlers;

import com.facishare.marketing.common.contstant.CrmConstants;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.KfCallbackArg;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.contstant.CustomizeFormDataConstants;
import com.facishare.marketing.common.enums.BrowserUserThirdPlateFormTypeEnum;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.ThirdKFCallbackEventEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2FieldTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.PhoneNumberCheck;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.dao.BrowserUserThirdPlateformRelationDAO;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsDAO;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDAO;
import com.facishare.marketing.provider.entity.BrowserUserThirdPlateformRelationEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity;
import com.facishare.marketing.provider.innerArg.CreateOrUpdateMarketingLeadSyncRecordObjArg;
import com.facishare.marketing.provider.innerData.KfCustomerEventContentData;
import com.facishare.marketing.provider.innerData.KfTalkInfoEventContentData;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.manager.ClueManagementManager;
import com.facishare.marketing.provider.manager.MarketingStatLogPersistorManger;
import com.facishare.marketing.provider.manager.OfficialWebsiteThirdPlateformEventManager;
import com.facishare.marketing.provider.manager.SpreadChannelManager;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.paas.crm.vo.base.CrmDuplicateSearchVo;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.TypeHashMap;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.DuplicateSearchResult;
import com.fxiaoke.crmrestapi.result.DuplicatesearchQueryResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * @author: wangzhy
 * @date: 2022/3/6
 * @Description:
 */
@Slf4j
@Service
public class OfficialWebsiteThirdPlateformEventHandler {

    @Autowired
    private OfficialWebsiteThirdPlateformEventManager officialWebsiteThirdPlateformEventManager;
    @Autowired
    private BrowserUserThirdPlateformRelationDAO browserUserThirdPlateformRelationDAO;
    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;

    @Autowired
    private OfficialWebsiteDAO officialWebsiteDAO;

    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    @Autowired
    private AdLeadsDAO adLeadsDAO;
    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private ClueManagementManager clueManagementManager;

    public void handleKfCallbackArg(KfCallbackArg value) {
        // 整体消息
        if (StringUtils.isNotEmpty(value.getCmd()) && ThirdKFCallbackEventEnum.TALK_INFO.getEvent().equals(value.getCmd()) && StringUtils.isNotEmpty(value.getContent())) {
            // 只有talk_info消息才会携带营销通的访客id
            KfTalkInfoEventContentData data = GsonUtil.fromJson(value.getContent(), KfTalkInfoEventContentData.class);
            log.info("OfficialWebsiteThirdPlateformEventHandler talk_info content:{}", data);
            KfTalkInfoEventContentData.KfTalkInfoSession session = data.getSession();
            if (session == null) {
                log.info("OfficialWebsiteThirdPlateformEventHandler talk_info session is null, data:{}", data);
                return;
            }
            String browserUserId = session.getUcust_id();
            String guestId = session.getGuest_id();
            if (StringUtils.isBlank(browserUserId) || StringUtils.isBlank(guestId)) {
                log.info("OfficialWebsiteThirdPlateformEventHandler talk_info session or ucust_id or guest_id is null, data:{}", data);
                return;
            }
            // 关联53kf的访客和营销通访客关系
            officialWebsiteThirdPlateformEventManager.relateThirdUserIdRelation(value.getEa(), browserUserId, guestId, null, null, data.getSession().getLand_page());
            // 将53kf的访客对应的营销通访客和线索关联起来
            officialWebsiteThirdPlateformEventManager.associateUserMarketingBy53kfGuestId(value.getEa(), guestId);
            return;
        }

        // 客服信息
        if (StringUtils.isNotEmpty(value.getCmd()) && ThirdKFCallbackEventEnum.CUSTOMER.getEvent().equals(value.getCmd()) && StringUtils.isNotEmpty(value.getContent())) {
            KfCustomerEventContentData data = null;
            try{
                data = GsonUtil.fromJson(value.getContent(), KfCustomerEventContentData.class);
                log.info("OfficialWebsiteThirdPlateformEventHandler customer content:{}", data);
                if (data == null || (StringUtils.isEmpty(data.getWechat()) && StringUtils.isEmpty(data.getWeixin()) && StringUtils.isEmpty(data.getPhone()) && StringUtils.isEmpty(data.getMobile()))) {
                    return;
                }
                saveLeadsToCrm(data, value.getEa());
            } catch (Exception e) {
                log.warn("OfficialWebsiteThirdPlateformEventHandler customer error, data: {}", data, e);
            }
        }
    }

    public Result<Void> saveLeadsToCrm(KfCustomerEventContentData data, String ea) {
        data.setEa(ea);
        log.info("OfficialWebsiteThirdPlateformEventHandler saveLeadsToCrm data:{}", data);
        String thirdUserId = data.getGuest_id();
        String leadId = null;
        Integer createBy = null;
        String adSource = "53KF";
        try {
            if (StringUtils.isNotBlank(data.getId())) {
                AdLeadsEntity entity = adLeadsDAO.queryLeadsByEaAndSourceLeadIdAndSource(ea, data.getId(), adSource);
                if (entity != null) {
                    log.info("OfficialWebsiteThirdPlateformEventHandler clue is already sync data:{}", data);
                    tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_175));
                    return Result.newSuccess();
                }
            }
            Map<String, Object> params = buildLeadsFieldData(data, ea);
            if (params == null) {
                log.info("OfficialWebsiteThirdPlateformEventHandler buildLeadsFieldData objectData is null");
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_182));
                return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_183));
            }
            //设置线索来源渠道promotion_channel
            params.put("promotion_channel", SpreadChannelManager.promotionChannelMap.get("在线客服"));
            params.put("from_marketing", true);
            // 官网访问来源
            String sourceName = getSourceName(ea, data.getFrom_page());
            params.put("marketing_source_name__c", sourceName);
            data.setSourceName(sourceName);
            ObjectData objectData = new ObjectData();
            objectData.putAll(params);
            objectData.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectData.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());

            // 这里推送过来的是有land_page这个字段的，为什么还要去查一遍？ 如果整体消息推送比客户消息推送要慢，那必然在表是查不到的
            List<BrowserUserThirdPlateformRelationEntity> browserUserThirdPlateformRelationEntityList = browserUserThirdPlateformRelationDAO.queryRelationByThirdUserId(ea, thirdUserId, BrowserUserThirdPlateFormTypeEnum.KF.getType());
            String landPage;
            if (CollectionUtils.isEmpty(browserUserThirdPlateformRelationEntityList)) {
                landPage = data.getLand_page();
            } else {
                landPage = browserUserThirdPlateformRelationEntityList.get(0).getLandPage();
            }
            Map<String, String> utmDataMap = new HashMap<>();
            if (StringUtils.isNotBlank(landPage)) {
                Map<String, String> map = buildUtmDataMap(landPage);
                utmDataMap.putAll(map);
            }
            data.setUtmDataMap(utmDataMap);
            String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByKfCustomerEventContentData(data);
            objectData.put("marketing_promotion_source_id", marketingPromotionSourceId);
            // 获取线索创建人
            createBy = clueDefaultSettingService.getClueCreator(null, ea, ClueDefaultSettingTypeEnum.ONLINE_SERVICE.getType());
            List<String> dataOwnOrganizationList = clueManagementManager.getDataOwnOrganization(ea, createBy);
            if (CollectionUtils.isNotEmpty(dataOwnOrganizationList)) {
                objectData.put(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION, dataOwnOrganizationList);
            }
            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectData);

            ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
            optionInfo.setIsDuplicateSearch(true);
            actionAddArg.setOptionInfo(optionInfo);

            CreateLeadResult createLeadResult = new CreateLeadResult();
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = null;
            result = metadataActionService.add(createHeaderObj(ea, createBy), LeadsFieldContants.API_NAME, false, actionAddArg);
            if (result != null && result.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
                leadId = (String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName());
                log.info("OfficialWebsiteThirdPlateformEventHandler saveLeadsToCrm success data:{} leadId:{}", data, leadId);
                // 关联53kf的访客和线索的关联关系
                officialWebsiteThirdPlateformEventManager.relateThirdUserIdRelation(ea, null, thirdUserId, leadId, data.getPhone(), landPage);
                // 将53kf的访客对应的营销通访客和线索关联起来
                officialWebsiteThirdPlateformEventManager.associateUserMarketingBy53kfGuestId(ea, thirdUserId);
                insertAdLeadEntity(data, ea, adSource, leadId);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, leadId, MarketingLeadSyncRecordObjManager.SUCCESS_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_750));
            } else if (result != null && result.getCode() == CrmConstants.REPEAT_CODE) {
                log.info("OfficialWebsiteThirdPlateformEventHandler saveLeadsToCrm failed duplicate lead, data:{} result: {}", data, result);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_738));
                return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_738));
            } else {
                String message = I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR);
                if (result != null && StringUtils.isNotBlank(result.getMessage())) {
                    message = result.getMessage();
                }
                createLeadResult.setMessage(message);
                log.info("OfficialWebsiteThirdPlateformEventHandler saveLeadsToCrm failed data:{} message:{}", data, message);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
                return Result.newError(-1, "message");
            }
            if (StringUtils.isNotEmpty(leadId)) {
                //上报神策系统
                marketingStatLogPersistorManger.sendLeadData(ea, leadId, null, MarketingStatLogPersistorManger.CHANNEL_AD_BAIDU_SYNC);
                //生成活动成员
                Object eKeywordId = utmDataMap.get("e_keywordid");
                Long keywordId = null;
                if (eKeywordId != null) {
                    try {
                        keywordId = Long.parseLong(eKeywordId.toString());
                    } catch (Exception e) {
                        log.error("parse keyword id error, ea: {} dataMap: {}", ea, utmDataMap);
                    }
                }
                baiduAdMarketingManager.syncCampaignMember(ea, leadId, AdSourceEnum.SOURCE_BAIDU.getSource(), utmDataMap.get("utm_campaign"), utmDataMap.get("utm_term"), false, utmDataMap.get("unitId"), utmDataMap.get("accountId"), keywordId,null);
            }
            return Result.newSuccess();
        } catch (Exception e) {
            log.error("OfficialWebsiteThirdPlateformEventHandler saveLeadsToCrm error data:{} ", data, e);
            String message = e.getMessage() == null ? I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR) : e.getMessage();
            tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
            return Result.newError(-1, message);
        }
    }

    private void insertAdLeadEntity(KfCustomerEventContentData data, String ea, String adSource, String leadId) {
        if (StringUtils.isBlank(data.getId())) {
            return;
        }
        try {
            //保存线索id
            AdLeadsEntity adLeadsEntity = new AdLeadsEntity();
            adLeadsEntity.setId(UUIDUtil.getUUID());
            adLeadsEntity.setEa(ea);
            adLeadsEntity.setSource(adSource);
            adLeadsEntity.setSource_lead_id(data.getId());
            adLeadsEntity.setLeadId(leadId);
            adLeadsDAO.addAdLeads(adLeadsEntity);
        } catch (Exception e) {
            log.error("53kf insertAdLeadEntity, ea: {} leadId: {} data: {}", ea, leadId, data, e);
        }
    }

    private void tryCreateOrUpdateMarketingLeadSyncRecordObj(KfCustomerEventContentData arg, String leadId, String syncStatus, String remark) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg leadSyncRecordObjArg = buildCreateMarketingLeadSyncRecordObjArg(arg, leadId);
        leadSyncRecordObjArg.setSyncStatus(syncStatus);
        leadSyncRecordObjArg.setRemark(remark);
        leadSyncRecordObjArg.setLeadId(leadId);
        leadSyncRecordObjArg.setMarketingLeadSyncRecordObjId(arg.getMarketingLeadSyncRecordObjId());
        marketingLeadSyncRecordObjManager.tryCreateOrUpdateMarketingLeadSyncRecordObj(leadSyncRecordObjArg);
    }

    private CreateOrUpdateMarketingLeadSyncRecordObjArg buildCreateMarketingLeadSyncRecordObjArg(KfCustomerEventContentData arg, String leadId) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg marketingLeadSyncRecordObjArg = new CreateOrUpdateMarketingLeadSyncRecordObjArg();
        marketingLeadSyncRecordObjArg.setEa(arg.getEa());
        marketingLeadSyncRecordObjArg.setSyncData(JsonUtil.toJson(arg));
        marketingLeadSyncRecordObjArg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_283));
        marketingLeadSyncRecordObjArg.setOutPlatformType(MarketingLeadSyncRecordObjManager.KF_53_PLATFORM);
        marketingLeadSyncRecordObjArg.setOutPlatformDataId(arg.getId());

        String mobile = arg.getMobile();
        String name = arg.getGuest_name();
        if (StringUtils.isNotBlank(mobile) && !PhoneNumberCheck.isPhoneLegal(mobile)) {
            mobile = null;
        }
        marketingLeadSyncRecordObjArg.setMobile(mobile);
        marketingLeadSyncRecordObjArg.setLeadName(name);
        marketingLeadSyncRecordObjArg.setLeadId(leadId);
        return marketingLeadSyncRecordObjArg;
    }

    private String getSourceName(String ea, String fromPage) {
        if (StringUtils.isBlank(fromPage)) {
            return "other";
        }
        if (fromPage.contains("zhihu.com")) {
            return "zhihu";
        } else if (fromPage.contains("weibo.cn")) {
            return "weibo";
        } else if (fromPage.contains("weixin.qq.com")) {
            return "wechat";
        } else if (fromPage.contains("news.163.com")) {
            return "news_163";
        } else if (fromPage.contains("ifeng.com")) {
            return "ifeng";
        } else if (fromPage.contains("sohu.com")) {
            return "sohu_news";
        } else if (fromPage.contains("news.qq.com")) {
            return "tx_news";
        } else if (fromPage.contains("toutiao.com")) {
            return "toutiao";
        } else if (fromPage.contains("sm.cn")) {
            return "sm";
        } else if (fromPage.contains("bing.com")) {
            return "bing";
        } else if (fromPage.contains("so.com")) {
            return "so360";
        } else if (fromPage.contains("sogou.com")) {
            return "sogou";
        } else if (fromPage.contains("baidu.com") || fromPage.contains("weijianzhan")) {
            return "baidu";
        }
        try {
            OfficialWebsiteEntity officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(ea);
            if (officialWebsiteEntity != null && StringUtils.isNotBlank(officialWebsiteEntity.getWebsiteUrl())) {
                String url = officialWebsiteEntity.getWebsiteUrl();
                url = getDomainHost(url);
                String pageDomain = getDomainHost(fromPage);
                if (url != null && url.equals(pageDomain)) {
                    return "guanwang";
                }
            }
        } catch (Exception e) {
          log.warn("53快服 getSourceName error, formPage:[{}]", fromPage, e);
        }
        return "other";

    }
    public static  String getDomainHost(String url){
        String pattern = "^((http://)|(https://))?([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,6}";

        Pattern p = Pattern.compile(pattern);
        String line = url;
        Matcher m = p.matcher(line);
        if(m.find()){
            //匹配结果
            String domain = m.group();
            domain = domain.replace("https","http");
            return domain;
        }
        return null;
    }

    /**
     * 根据landPage构建utm数据
     * @param landPage          落地页包含了utm数据
     * @return                  utmDataMap
     */
    public Map<String, String> buildUtmDataMap(String landPage) {
        String[] split = landPage.split("\\?");
        Map<String, String> data = new HashMap<>();
        if (split != null && split.length == 2 && StringUtils.isNotEmpty(split[1])) {
            String baseStr = split[1];
            String[] strings = baseStr.split("&");
            for (String s : strings) {
                if (s.contains("utm_source")) {
                    String regex = "utm_source=";
                    String substring = s.substring(s.indexOf(regex) + regex.length());
                    data.put("utm_source", substring);
                }
                if (s.contains("utm_campaign")) {
                    String regex = "utm_campaign=";
                    String substring = s.substring(s.indexOf(regex) + regex.length());
                    data.put("utm_campaign", substring);
                }
                if (s.contains("utm_term")) {
                    String regex = "utm_term=";
                    String substring = s.substring(s.indexOf(regex) + regex.length());
                    data.put("utm_term", substring);
                }
                if (s.contains("utm_content")) {
                    String regex = "utm_content=";
                    String substring = s.substring(s.indexOf(regex) + regex.length());
                    data.put("utm_content", substring);
                }
                if (s.contains("unitId=")) {
                    String regex = "unitId=";
                    String substring = s.substring(s.indexOf(regex) + regex.length());
                    data.put("unitId", substring);
                }
                if (s.contains("accountId=")) {
                    String regex = "accountId=";
                    String substring = s.substring(s.indexOf(regex) + regex.length());
                    data.put("accountId", substring);
                }
                if (s.contains("e_keywordid=")) {
                    String regex = "e_keywordid=";
                    String substring = s.substring(s.indexOf(regex) + regex.length());
                    data.put("e_keywordid", substring);
                }
            }
            log.info("OfficialWebsiteThirdPlateformEventHandler buildUtmDataMap dataMap:{}", data);
        }
        return data;
    }

    /**
     * 构建同步线索数据
     * @param data      53kf推送的客户信息
     * @param ea
     * @return          ObjectData
     */
    private Map<String, Object> buildLeadsFieldData(KfCustomerEventContentData data, String ea) {
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPluginFieldMap(ea, MarketingPluginTypeEnum.CUSTOMER_SERVICE.getType(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (marketingPluginConfigEntity == null || marketingPluginConfigEntity.getCrmFormFieldMap() == null) {
            log.info("OfficialWebsiteThirdPlateformEventHandler buildLeadsFieldData marketingPluginConfigEntity is null, ea:{}", ea);
            return null;
        }
        FieldMappings crmFormFieldMap = marketingPluginConfigEntity.getCrmFormFieldMap();
        if (StringUtils.isEmpty(data.getPhone()) && StringUtils.isNotEmpty(data.getMobile())) {
            data.setPhone(data.getMobile());
        }
        if (StringUtils.isEmpty(data.getMobile()) && StringUtils.isNotEmpty(data.getPhone())) {
            data.setMobile(data.getPhone());
        }
        if (StringUtils.isEmpty(data.getWeixin()) && StringUtils.isNotEmpty(data.getWechat())) {
            data.setWeixin(data.getWechat());
        }
        if (StringUtils.isEmpty(data.getWechat()) && StringUtils.isNotEmpty(data.getWeixin())) {
            data.setWechat(data.getWeixin());
        }
        Map<String, Object> param = createObjectDataToCrmLeadFieldDataMap(ea, data, crmFormFieldMap, marketingPluginConfigEntity.getCrmPoolId(), marketingPluginConfigEntity.getCrmRecordType());
        return param;
    }


    /**
     * 53kf客户信息to线索字段
     *
     * @param ea                企业账户
     * @param data              53kf客户信息
     * @param crmFormFieldMap   crm线索映射map
     * @param crmPoolId
     * @param crmRecordType
     * @return
     */
    private Map<String, Object> createObjectDataToCrmLeadFieldDataMap(String ea, KfCustomerEventContentData data, FieldMappings crmFormFieldMap, String crmPoolId, String crmRecordType) {
            Map<String, Object> crmData = new HashMap<>();
            // 获取线索描述
            Map<String, CrmUserDefineFieldVo> fieldTypeMap = Maps.newHashMap();
            try {
                List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD);
                if (CollectionUtils.isNotEmpty(crmUserDefineFieldVoList)) {
                    fieldTypeMap = crmUserDefineFieldVoList.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, Function.identity(), (v1, v2) -> v1));
                }
            } catch (Exception e) {
                log.warn("OfficialWebsiteThirdPlateformEventHandler.createObjectDataToCrmLeadFieldDataMap error e:{}", e);
                return null;
            }

            Map<String, CrmUserDefineFieldVo> finalFieldTypeMap = fieldTypeMap;
            for (FieldMappings.FieldMapping fieldMapping : crmFormFieldMap){
                Object fieldValue = data.getFieldValueByName(fieldMapping.getMankeepFieldName());
                if (fieldValue == null) {
                    String fieldType = finalFieldTypeMap.get(fieldMapping.getCrmFieldName()) == null ? null : finalFieldTypeMap.get(fieldMapping.getCrmFieldName()).getFieldTypeName();
                    if (StringUtils.isNotBlank(fieldType) && fieldType.equals(CrmV2FieldTypeEnum.SelectMany.getName())) {
                        fieldValue = Lists.newArrayList(fieldMapping.getDefaultValue());
                    } else {
                        fieldValue = fieldMapping.getDefaultValue();
                    }
                }

                log.info("this fieldMapping:{},  fieldValue:{}", fieldMapping, fieldValue);
                if (!Strings.isNullOrEmpty(fieldMapping.getCrmFieldName())) {
                    crmData.put(fieldMapping.getCrmFieldName(), fieldValue);
                }
            }
            if (StringUtils.isNotBlank(crmPoolId)) {
                crmData.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), crmPoolId);
            }
            crmData.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), crmPoolId);
            crmData.put(CrmV2LeadFieldEnum.RecordType.getNewFieldName(), crmRecordType);
            log.info("53kf createObjectDataToCrmLeadFieldDataMap crmData:{}", crmData);
            return crmData;
    }

    protected HeaderObj createHeaderObj(String ea, Integer userId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        if (null == tenantId) {
            throw new CrmBusinessException(-1000, "enterpriseAccountToId failed, ea=" + ea);
        }

        if (null == userId) {
            userId = -10000;
        }

        return new HeaderObj(tenantId, userId);
    }

}