package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.dto.IdEaDTO;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendGroupResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendTaskEntity;
import com.github.mybatis.mapper.ICrudMapper;
import java.util.List;
import java.util.Map;

import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 22:56 2020/2/11
 * @ModifyBy
 */
public interface QywxGroupSendTaskDAO extends ICrudMapper<QywxGroupSendTaskEntity> {
    @Insert("INSERT INTO qywx_group_send_task(id,ea,corp_id,fs_user_id,title,chat_type, group_msg_sender_ids,msg_type,content,send_range,marketing_user_group_ids,"
            + "filters,tag_id_list,customer_count,actual_completed_count,need_send_count,read_count,send_type,fixed_time,create_time,"
            + "update_time,status,errcode,errmsg,fail_list,msgid,sender,image_path,link_title,link_pic_path,link_desc,"
            + "link_url,mini_title,mini_pic_path,object_id,object_type,mini_page,app_id,marketing_activity_id, filter_nday_sent_user,qywx_group_list,user_id,department_id,tag_id,chat_group_filters,allow_select)"
            +" VALUES(#{entity.id},#{entity.ea},#{entity.corpId},#{entity.fsUserId},#{entity.title},#{entity.chatType}, #{entity.groupMsgSenderIds}, #{entity.msgType},#{entity.content},#{entity.sendRange},"
            + "#{entity.marketingUserGroupIds},#{entity.filters},#{entity.tagIdList},#{entity.customerCount},#{entity.actualCompletedCount},"
            + "#{entity.needSendCount},#{entity.readCount},#{entity.sendType},#{entity.fixedTime},#{entity.createTime},#{entity.updateTime},#{entity.status},"
            + "#{entity.errcode},#{entity.errmsg},#{entity.failList},#{entity.msgid},#{entity.sender},#{entity.imagePath},#{entity.linkTitle},"
            + "#{entity.linkPicPath},#{entity.linkDesc},#{entity.linkUrl},#{entity.miniTitle},#{entity.miniPicPath},#{entity.objectId},"
            + "#{entity.objectType},#{entity.miniPage},#{entity.appId},#{entity.marketingActivityId}, #{entity.filterNDaySentUser}, #{entity.qywxGroupList}, #{entity.userId}, #{entity.departmentId}, #{entity.tagId}, #{entity.chatGroupFilters}, #{entity.allowSelect})")
    int insertEntity(@Param("entity")QywxGroupSendTaskEntity entity);

    @Select("SELECT * FROM qywx_group_send_task WHERE status=0 AND fixed_time<=(extract(epoch FROM now()) * 1000)")
    List<QywxGroupSendTaskEntity> getNeedSendTask(@Param("ea") String ea);

    @Select("SELECT * FROM qywx_group_send_task WHERE status=0 AND ea=#{ea} AND fixed_time<=(extract(epoch FROM now()) * 1000) " +
            "and fixed_time >= (extract(epoch FROM now()) * 1000 - 60 * 60 * 1000)")
    List<QywxGroupSendTaskEntity> getNeedSendTaskByEa(@Param("ea") String ea);

    @Update("<script>"
        +"UPDATE qywx_group_send_task SET status=1, update_time=now() WHERE id = #{id} and status=#{status}"
        + "</script>")
    boolean updateTaskStatusToRunningById(@Param("id") String id, @Param("status") Integer status,@Param("ea") String ea);

    @Update("<script>"
        +"UPDATE qywx_group_send_task SET status=#{status}, update_time=now() WHERE id IN"
        + "<foreach collection='ids' item='item' open='(' separator=',' close=')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    void updateTimedTaskStatusByIds(@Param("ids") List<Integer> ids, @Param("status") Integer status,@Param("ea") String ea);

    @Update("UPDATE qywx_group_send_task SET status=#{status} WHERE id=#{id}")
    void updateStatusById(@Param("id")String id, @Param("status")Integer status,@Param("ea") String ea);

    @Update("UPDATE qywx_group_send_task SET status=#{status},errcode=#{errCode} WHERE id=#{id}")
    void updateStatusAndErrorCodeById(@Param("id")String id, @Param("status")Integer status, @Param("errCode")Integer errCode,@Param("ea") String ea);

    @Update("<script>"
        +"UPDATE qywx_group_send_task SET status=#{status}, errcode=#{errcode}, errmsg=#{errmsg}, msgid=#{msgid}, fail_list=#{failList}, update_time=now() WHERE id = #{id}"
        + "</script>")
    void updateSendResult(@Param("id") String id, @Param("status") Integer status, @Param("errcode") Integer errcode, @Param("errmsg") String errmsg, @Param("msgid") String msgid, @Param("failList") String failList,@Param("ea") String ea);

    //获取最近7天的发送任务
    @Select("SELECT * FROM qywx_group_send_task WHERE chat_type = 1 AND msgid is not null AND fixed_time>=((extract(epoch FROM now()) - 7 * 24 * 3600) * 1000)")
    List<QywxGroupSendTaskEntity> getNeedSendSingleResultTask(@Param("ea") String ea);

    @FilterLog
    @Select("SELECT * FROM qywx_group_send_task WHERE chat_type = 1 AND msgid is not null AND fixed_time>=((extract(epoch FROM now()) - 1 * 24 * 3600) * 1000)")
    List<QywxGroupSendTaskEntity> getNeedSendSingleResultTaskThisDay(@Param("ea") String ea);

    @FilterLog
    @Select("SELECT * FROM qywx_group_send_task WHERE marketing_activity_id = #{marketingActivityId}")
    QywxGroupSendTaskEntity querySendTaskByMarketingAcivityId(@Param("marketingActivityId") String marketingActivityId,@Param("ea") String ea);

    @Update("<script>"
        +" UPDATE qywx_group_send_task SET customer_count=#{customerCount}, "
        + "    <if test=\"status != null\">\n"
        + "        status = #{status},\n"
        + "    </if>\n"
        + "    <if test=\"errcode != null\">\n"
        + "        errcode = #{errcode},\n"
        + "    </if>\n"
        + "    <if test=\"errmsg != null\">\n"
        + "        errmsg = #{errmsg},\n"
        + "    </if>\n"
        + " update_time=now()\n"
        + " WHERE id = #{id}"
        + "</script>")
    void updateCustomerCount(@Param("id") String id, @Param("customerCount") Integer customerCount, @Param("status") Integer status, @Param("errcode")Integer errcode, @Param("errmsg")String errmsg,@Param("ea") String ea);

    @FilterLog
    @Select("SELECT * FROM qywx_group_send_task WHERE id=#{id}")
    QywxGroupSendTaskEntity queryById(@Param("id")String id,@Param("ea") String ea);

    @Select("<script>"
            + "SELECT * FROM qywx_group_send_task WHERE ea=#{ea} AND marketing_activity_id IN\n"
            +   "<foreach collection = 'marketingActivityIds' item = 'marketingActivityId' open = '(' separator = ',' close = ')'>"
            +       "#{marketingActivityId}"
            +   "</foreach>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<QywxGroupSendTaskEntity> listGroupMessageByMarketingActivityIds(@Param("ea") String ea, @Param("title") String title, @Param("marketingActivityIds")List<String> marketingActivityIds);

    @Select("SELECT * FROM qywx_group_send_task WHERE marketing_activity_id=#{marktingActivityId}")
    QywxGroupSendTaskEntity getByMarketingActivityId(@Param("marktingActivityId")String marktingActivityId,@Param("ea") String ea);

    @Select("SELECT * FROM qywx_group_send_task WHERE chat_type=2 order by create_time desc")
    List<QywxGroupSendTaskEntity> getBy(@Param("Page") Page page,@Param("ea") String ea);
    @Update("UPDATE public.qywx_group_send_task SET fs_user_id = #{entity.fsUserId}, msg_type = #{entity.msgType}, content = #{entity.content}, send_range = #{entity.sendRange}, marketing_user_group_ids = #{entity.marketingUserGroupIds}, filters = #{entity.filters}, tag_id_list = #{entity.tagIdList},\n" +
            " fixed_time = #{entity.fixedTime}, update_time = now(), status = #{entity.status}, image_path = #{entity.imagePath}, link_title = #{entity.linkTitle}, link_pic_path = #{entity.linkPicPath}, link_desc = #{entity.linkDesc}, link_url = #{entity.linkUrl}, mini_title = #{entity.miniTitle}, mini_pic_path = #{entity.miniPicPath}, object_id = #{entity.objectId}, object_type = #{entity.objectType},\n" +
            "mini_page = #{entity.miniPage}, title = #{entity.title}, group_msg_sender_ids = #{entity.groupMsgSenderIds}, chat_type = #{entity.chatType}, send_type = #{entity.sendType}, filter_nday_sent_user = #{entity.filterNDaySentUser},app_id = #{entity.appId},user_id = #{entity.userId},department_id = #{entity.departmentId},tag_id = #{entity.tagId},chat_group_filters = #{entity.chatGroupFilters},allow_select = #{entity.allowSelect} WHERE marketing_activity_id = #{entity.marketingActivityId}")
    int updateEntity(@Param("entity")QywxGroupSendTaskEntity entity);

    @Select("<script>" +
            "select distinct a.* from qywx_group_send_task a " +
            "left join marketing_activity_external_config b on a.ea =b.ea and a.marketing_activity_id = b.marketing_activity_id " +
            "left join marketing_activity_object_relation c on b.ea = a.ea and b.marketing_activity_id = c.marketing_activity_id " +
            "where a.ea = #{ea} and a.fixed_time between #{startDate} and #{endDate} " +
            "<if test=\"marketingEventId != null and marketingEventId != ''\">" +
            "and b.marketing_event_id = #{marketingEventId} " +
            "</if>" +
            "<if test =\"objectIds != null and objectIds.size != 0\">" +
            "and c.object_id = " +
            "ANY(ARRAY<foreach collection = 'objectIds' index='idx' open = '[' separator = ',' close = ']'>#{objectIds[${idx}]}</foreach>)" +
            "</if>" +
            "</script>")
    List<QywxGroupSendTaskEntity> listActivityIdsInDateRangeWithObjectIds(@Param("ea") String ea, @Param("startDate") Long startDate, @Param("endDate") Long endDate,
                                                                                 @Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds);


    @Select("select count(*) from qywx_group_send_task qgst right join qywx_attachments_relation qar on qgst.id = qar.target_id where qgst.marketing_activity_id = #{marketingActivityId}")
    int getGroupSendAttachments(@Param("marketingActivityId")String marketingActivityId,@Param("ea") String ea);

    @Select("SELECT * FROM qywx_group_send_task WHERE ea=#{ea} and chat_type = 1 and fail_list is not null and fail_list like  CONCAT('%', #{externalUserId}, '%')")
    List<QywxGroupSendTaskEntity> getFailExternalUserIdList(@Param("ea") String ea, @Param("externalUserId")String externalUserId);

    @Update("<script>"
            +" update qywx_group_send_task set fail_list = #{failList}, "
            + " update_time=now()\n"
            + " where id = #{id} and ea = #{ea}"
            + "</script>")
    int updateFailList(@Param("ea") String ea, @Param("id") String id, @Param("failList")String failList);

    @Select("SELECT id,group_msg_sender_ids,user_id,sender FROM qywx_group_send_task WHERE ea=#{ea} and chat_type = 1 and (group_msg_sender_ids like  CONCAT('%', #{employeeId}, '%') or user_id like  CONCAT('%', #{employeeId}, '%') or sender = #{employeeId})")
    List<QywxGroupSendTaskEntity> getAllEmployIdByEmployId(@Param("ea") String ea, @Param("employeeId")String employeeId);

    @Update("<script>"
            +" update qywx_group_send_task set "
            + "<if test=\"groupMsgSenderIds != null and groupMsgSenderIds != ''\">"
            + " group_msg_sender_ids = #{groupMsgSenderIds}, "
            + "</if>"
            + "<if test=\"userId != null and userId != ''\">"
            + " user_id = #{userId},"
            + "</if>"
            + "<if test=\"sender != null and sender != ''\">"
            + " sender = #{sender},"
            + "</if>"
            + " update_time=now()\n"
            + " where id = #{id} and ea = #{ea}"
            + "</script>")
    int updateEmployeeField(@Param("ea") String ea, @Param("id") String id, @Param("groupMsgSenderIds") String groupMsgSenderIds, @Param("userId") String userId, @Param("sender") String sender);

    @Select("<script>"
            + "select * from qywx_group_send_task where id =\n"
            + "ANY(ARRAY<foreach collection = 'ids' index='idx' open = '[' separator = ',' close = ']'>#{ids[${idx}]}</foreach>)\n"
            + "</script>")
    List<QywxGroupSendTaskEntity> getByIds(@Param("ids") List<String> ids,@Param("ea") String ea);

    @Select("<script>"
            + "select id, ea from qywx_group_send_task where id =\n"
            + "ANY(ARRAY<foreach collection = 'ids' index='idx' open = '[' separator = ',' close = ']'>#{ids[${idx}]}</foreach>)\n"
            + "</script>")
    @MapKey("id")
    List<IdEaDTO> getEaMapByIds(@Param("ids") List<String> ids);
}
