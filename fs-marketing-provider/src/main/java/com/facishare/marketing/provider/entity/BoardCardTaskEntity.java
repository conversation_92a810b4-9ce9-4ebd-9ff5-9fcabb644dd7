package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BoardCardTaskEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String boardId;
    private String boardCardId;
    private String name;
    private String description;
    private Integer executor;
    private Date startTime;
    private Date endTime;
    private Boolean taskStartNotifySent;
    private Boolean taskEndNotifySent;
    private String status;
    private Integer creator;
    //0表示新建状态（数据库默认状态）、1表示删除状态
    private int lifeStatus;
    private Date createTime;
    private Date updateTime;
}
