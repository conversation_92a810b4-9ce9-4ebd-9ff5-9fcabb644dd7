/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.service.cta.CtaService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.result.BaseUserInfoResult;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.customizeFormData.*;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.qywx.customizeFormData.GetAreaDataArg;
import com.facishare.marketing.api.data.excel.ScopeContainer;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.customizeFormData.GetBingFormByObjectResult;
import com.facishare.marketing.api.result.customizeFormData.GetFormImportTemplateResult;
import com.facishare.marketing.api.result.customizeFormData.ImportFormEnrollDataResult;
import com.facishare.marketing.api.service.*;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.vo.UpdateCustomizeFormDataMoreSettingVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.*;
import com.facishare.marketing.common.contstant.material.MaterialTypeEnum;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.conference.ConferenceNotificationTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.hexagon.MemberCheckTypeEnum;
import com.facishare.marketing.common.enums.hexagon.SaveCrmObjectTypeEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.service.BrowserUserMarketingAccountAssociationService;
import com.facishare.marketing.outapi.service.ConferenceService;
import com.facishare.marketing.outapi.service.MarketingFlowInstanceOuterService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.dao.manager.CustomizeFormDataDAOManager;
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO;
import com.facishare.marketing.provider.dao.member.MemberMarketingEventCrmConfigDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendTaskDAO;
import com.facishare.marketing.provider.dao.statistic.MarketingObjectAmountStatisticDao;
import com.facishare.marketing.provider.dto.CreateFormDataDTO;
import com.facishare.marketing.provider.dto.CustomizeFormDataUserStatisticDTO;
import com.facishare.marketing.provider.dto.MemberCookieInfo;
import com.facishare.marketing.provider.dto.ObjectStatisticData;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.pay.PayOrderEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendTaskEntity;
import com.facishare.marketing.provider.entity.sms.ExtraSmsParamObject;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmLeadAccountRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerArg.crm.ExecuteCustomizeFunctionArg;
import com.facishare.marketing.provider.innerArg.crm.ExecuteCustomizeFunctionArg.Parameters;
import com.facishare.marketing.provider.innerArg.crm.MktContentMgmtLogCreateArg;
import com.facishare.marketing.provider.innerData.BehaviorSendEventData;
import com.facishare.marketing.provider.innerData.CreateFsPayOrderResult;
import com.facishare.marketing.provider.innerData.CreateMiniAppPayOrderResult;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.AreaManager.AreaContainer;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager.CustomizeFormQrCodeContainer;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.conference.ImportManager;
import com.facishare.marketing.provider.manager.conference.ImportManager.ImportLineArgContainer;
import com.facishare.marketing.provider.manager.conference.ImportManager.ImportLineResultContainer;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.cusomerDev.CustomerCustomizeFormDataManager;
import com.facishare.marketing.provider.manager.customizeFormDataUserManager.FormDataUserManager;
import com.facishare.marketing.provider.manager.customizeFormDataUserManager.QueryMultipleFormUserDataManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.pay.FsPayOrderManager;
import com.facishare.marketing.provider.manager.pay.PayOrderManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.sms.VerificationCodeManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.mq.sender.BehaviorSendEventSender;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.util.ReadExcelUtil;
import com.facishare.marketing.statistic.common.model.PageArg;
import com.facishare.marketing.statistic.outapi.arg.FromUserMarketingStatisticSpreadRankingArg;
import com.facishare.marketing.statistic.outapi.arg.UserMarketingActionStatisticQueryArg;
import com.facishare.marketing.statistic.outapi.result.UserMarketingActionStatisticResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.constants.QrActionType;
import com.facishare.wechat.proxy.model.result.CreateParamQrCodeResult;
import com.facishare.wechat.proxy.model.vo.CreateParamQrCodeVO;
import com.facishare.wechat.proxy.service.QrCodeService;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.QueryAreaInfoResult;
import com.fxiaoke.crmrestapi.common.result.ZoneByParentResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.otherrestapi.integral.constant.ActionApiNameConstant;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created  By zhoux 2019/04/04
 **/
@Service("customizeFormDataService")
@Slf4j
public class CustomizeFormDataServiceImpl implements CustomizeFormDataService{

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private NoticeDAO noticeDAO;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private CustomizeFormDataObjectDAO customizeFormDataObjectDAO;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private PolyvManager polyvManager;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private CrmV2MappingManager crmV2MappingManager;

    @Autowired
    private FormDataUserManager formDataUserManager;

    @Autowired
    private QueryMultipleFormUserDataManager queryMultipleFormUserDataManager;

    @Autowired
    private QrCodeService qrCodeService;

    @Autowired
    private OuterServiceWechatService outerServiceWechatService;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private CustomizeTicketManager customizeTicketManager;

    @Autowired
    private MarketingFlowInstanceOuterService marketingFlowInstanceOuterService;

    @Autowired
    private ObjectTagManager objectTagManager;

    @Autowired
    private MetadataTagManager metadataTagManager;

    @Autowired
    private WxTicketManager wxTicketManager;

    @Autowired
    private CustomerCustomizeFormDataManager customerCustomizeFormDataManager;

    @Autowired
    private ConferenceService conferenceOutService;

    @Autowired
    private CustomizeFormDataDAOManager customizeFormDataDAOManager;

    @Autowired
    private QywxGroupSendTaskDAO qywxGroupSendTaskDAO;

    @Autowired
    private PayOrderManager payOrderManager;

    @Autowired
    private AreaManager areaManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private BrowserUserMarketingAccountAssociationService browserUserMarketingAccountAssociationService;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private BehaviorSendEventSender behaviorSendEventSender;

    @Autowired
    private CustomizeFunctionManager customizeFunctionManager;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    @Autowired
    private MemberConfigDao memberConfigDao;

    @Autowired
    private VerificationCodeManager verificationCodeManager;

    @Autowired
    private MemberManager memberManager;

    @Autowired
    private WxServiceUserMemberBindDao wxServiceUserMemberBindDao;
    @ReloadableProperty("cookieDomain")
    private String cookieDomain;

    @Value("${host}")
    private String host;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private ObjectTagDAO objectTagDAO;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private MemberService memberService;

    @Autowired
    private ImportManager importManager;

    @Autowired
    private MarketingObjectAmountStatisticDao marketingObjectAmountStatisticDao;

    @Autowired
    private ExecuteTaskDetailManager executeTaskDetailManager;

    @Autowired
    private DingAuthService dingAuthService;

    @Autowired
    private MemberAccessibleCampaignDAO memberAccessibleCampaignDAO;

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private DingManager dingManager;

    @Autowired
    private MemberMarketingEventCrmConfigDAO memberMarketingEventCrmConfigDAO;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private ActivityEnrollTimeConfigDAO activityEnrollTimeConfigDAO;
    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;
    @Autowired
    private SaveClueFailNoticeConfigDAO saveClueFailNoticeConfigDAO;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private FsBindService fsBindService;
    @Autowired
    private FsPayOrderManager fsPayOrderManager;

    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;

    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;
    @Autowired
    private PushSessionManager pushSessionManager;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private CrmService crmService;

    @Autowired
    private QRPosterDAO qrPosterDAO;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private KmUserManager kmUserManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;

    @Autowired
    private EgressApiManger egressApiManger;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    @Autowired
    private CtaService ctaService;

    public static final String DOWNLOAD_IMG_FILE_PATH_PREFIX = "/tmp";

    public static final String DOWNLOAD_IMG_ZIP_FILE_PATH_DIR = "/tmp/img_zip";

    public static final ImmutableSet<String> eas = ImmutableSet.of("74164", "fktest085", "megvii123");
    public static final String CHINA_AREA_CODE = "248";

    @Autowired
    private SafetyManagementManager safetyManagementManager;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private QywxGroupSendTaskDAO groupSendTaskDAO;

    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Override
    public Result<File> exportFormUserDataImg(String customizeFormDataId, String ea) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataId,ea);
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.exportFormUserDataImg customizeFormDataEntity is empty");
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        if (!eas.contains(ea)) {
            return Result.newError(SHErrorCode.NOT_SUPPORT_DOWNLOAD_PIC_FILE_EA);
        }
        Optional<List<CustomizeFormDataEnroll>> optionOfCustomizeFormDataEnrollList = customizeFormDataManager.getAllCustomizeFormDataEnrollByFormId(customizeFormDataId, ea);
        if (!optionOfCustomizeFormDataEnrollList.isPresent() || CollectionUtils.isEmpty(optionOfCustomizeFormDataEnrollList.get())) {
            return Result.newError(SHErrorCode.PIC_FILE_FAILED);
        }
        List<CustomizeFormDataEnroll> customizeFormDataEnrollList = optionOfCustomizeFormDataEnrollList.get();
        Map<String, Integer> name2SeqMap = new HashMap<>(customizeFormDataEnrollList.size() * 2);
        long now = System.currentTimeMillis();
        String dirPath = DOWNLOAD_IMG_FILE_PATH_PREFIX + now;
        // 文件名为： 姓名prefixFileName + 序号seq
        for (CustomizeFormDataEnroll customizeFormDataEnroll : customizeFormDataEnrollList) {
            String prefixFileName = customizeFormDataEnroll.getName(); // 姓名
            if (StringUtils.isEmpty(prefixFileName) || MapUtils.isEmpty(customizeFormDataEnroll.getPicMap())) {
                continue;
            }
            for (Map.Entry<String, List<PicContainer>> picMap : customizeFormDataEnroll.getPicMap().entrySet()) {
                if (CollectionUtils.isEmpty(picMap.getValue())) {
                    continue;
                }
                for (PicContainer picContainer : picMap.getValue()) {
                    String prefixNPath = picContainer.getPath();
                    String suffixNPath = picContainer.getExt();
                    if (StringUtils.isBlank(prefixNPath) || StringUtils.isBlank(suffixNPath)) {
                        continue;
                    }
                    byte[] bytes = fileV2Manager.downloadNFile(prefixNPath + "." + suffixNPath, ea, false);
                    if (bytes == null || bytes.length == 0) {
                        continue;
                    }
                    Integer seq = name2SeqMap.get(prefixFileName); // 序号
                    FileUtil.bytes2File(bytes, dirPath, seq == null ? prefixFileName + "." + suffixNPath : prefixFileName + "-" + seq + "." + suffixNPath);
                    name2SeqMap.put(prefixFileName, seq == null ? 1 : ++seq);
                }
            }
        }
        File zipFile = FileUtil.createZipFile(dirPath, DOWNLOAD_IMG_ZIP_FILE_PATH_DIR, now + ".zip");
        if (zipFile == null) {
            return Result.newError(SHErrorCode.PIC_FILE_FAILED);
        }
        return Result.newSuccess(zipFile);
    }

    @Override
    public Result<GetFormImportTemplateResult> getFormImportTemplate(GetFormImportTemplateArg arg) {
        GetFormImportTemplateResult result = new GetFormImportTemplateResult();
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(),arg.getEa());
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.getFormImportTemplate error param error arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        List<String> titleList = Lists.newArrayList();
        for (FieldInfo fieldInfo : customizeFormDataEntity.getFormBodySetting()) {
            if (fieldInfo.getIsRequired() != null && fieldInfo.getIsRequired()) {
                titleList.add(fieldInfo.getLabel() + I18nUtil.getStaticByKey(ImportConstants.REQUIRED));
            } else {
                titleList.add(fieldInfo.getLabel());
            }
        }
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_513));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_687));
        List<ScopeContainer> scopeContainerList = Lists.newArrayList();
        // 单选选项配置
        List<ScopeContainer> singleChoiceList = importManager.buildSingleChoiceScopeContainer(customizeFormDataEntity.getFormBodySetting());
        if (CollectionUtils.isNotEmpty(singleChoiceList)) {
            scopeContainerList.addAll(singleChoiceList);
        }
        // 省市区选项配置
        List<ScopeContainer> areaList = importManager.buildAreaScopeContainer(customizeFormDataEntity.getFormBodySetting());
        if (CollectionUtils.isNotEmpty(areaList)) {
            scopeContainerList.addAll(areaList);
        }
       /* // 渠道选项配置
        ScopeContainer scopeContainer = importManager.buildChannelScopeContainer(arg.getEa(), titleList.size() - 2);
        if (scopeContainer != null) {
            scopeContainerList.add(scopeContainer);
        }*/
        if(CollectionUtils.isNotEmpty(scopeContainerList)) {
            result.setScopeContainerList(scopeContainerList);
        }
        result.setDataList(importManager.buildImportTemplateDescriptionData(titleList.size(), ImportConstants.IMPORT_FORM_ENROLL_DESCRIPTION));
        result.setTitleList(titleList);
        result.setFileName(customizeFormDataEntity.getFormHeadSetting().getName());
        return Result.newSuccess(result);
    }


    @Override
    public Result<ImportFormEnrollDataResult> importFormEnrollData(ImportFormEnrollDataArg arg) {
        // 若为微页面传入站点转换为对应页面
        if (arg.getObjectType().equals(ObjectTypeEnum.HEXAGON_SITE.getType())) {
            List<HexagonSiteListDTO> hexagonSiteList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(arg.getObjectId()), arg.getEa());
            if (CollectionUtils.isNotEmpty(hexagonSiteList)) {
                arg.setObjectType(ObjectTypeEnum.HEXAGON_PAGE.getType());
                arg.setObjectId(hexagonSiteList.get(0).getHexagonPageId());
            } else {
                return Result.newError(SHErrorCode.OBJECT_NOT_BIND_CUSTOMIZE_FORM);
            }
        }
        // 校验基础数据
        Result checkImportResult = checkImportArg(arg.getEa(), arg.getFormId(), arg.getObjectId(), arg.getObjectType());
        if (!checkImportResult.isSuccess()) {
            return checkImportResult;
        }
        byte[] excelByte = fileV2Manager.downloadAFile(arg.getTaPath(), null);
        if (excelByte == null) {
            log.warn("CustomizeFormDataServiceImpl.importFormEnrollData excelByte arg:{}", arg);
            return Result.newError(SHErrorCode.IMPORT_CONFERENC_PARTICIPANT_FILE_FAIL);
        }
        List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(arg.getTaPath(), excelByte);
        Result checkImportData = checkImportData(excelData);
        if (!checkImportData.isSuccess()) {
            log.warn("CustomizeFormDataServiceImpl.importFormEnrollData checkImportData error:{}", checkImportData);
            return Result.newError(SHErrorCode.EXCEL_HAS_DIFFERENCE_TO_FORM);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(),arg.getEa());
        // 校验title
        // 自定义字段（渠道）
        FieldInfo channelField = new FieldInfo();
        channelField.setApiName(ImportConstants.IMPORT_CHANNEL_API_NAME);
        channelField.setType(ImportConstants.IMPORT_CUSTOMIZE_TYPE);
        channelField.setIsRequired(false);
        channelField.setIsVerify(false);
        channelField.setLabel(I18nUtil.getStaticByKey(ImportConstants.IMPORT_CHANNEL_LABEL));
        List<FieldInfo> titleCheckResult = importManager.checkFormImportTitleData(Arrays.asList(excelData.get(0)), customizeFormDataEntity, Lists.newArrayList(channelField));
        if (CollectionUtils.isEmpty(titleCheckResult)) {
            return Result.newError(SHErrorCode.EXCEL_HAS_DIFFERENCE_TO_FORM);
        }

        if (executeTaskDetailManager.checkTaskAndAddIfNotExist(arg.getEa(), ExecuteTaskDetailTypeEnum.IMPORT_FORM_ENROLL_DATA, arg.getFormId())) {
            return Result.newError(SHErrorCode.TASK_IN_PROGRESS);
        }

        //异步处理数据
        ThreadPoolUtils.executeWithTraceContext(() -> {
            try {
                asyncImportFormEnrollData(arg, excelData, titleCheckResult);
            }catch (Exception e){
                log.warn("CustomizeFormDataServiceImpl.importFormEnrollData error e:{}", e);
            }finally {
                executeTaskDetailManager.taskComplete(arg.getEa(), ExecuteTaskDetailTypeEnum.IMPORT_FORM_ENROLL_DATA, arg.getFormId());
            }
        },ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return new Result<>(SHErrorCode.SUCCESS);
    }

    private void asyncImportFormEnrollData(ImportFormEnrollDataArg arg, List<String[]> excelData, List<FieldInfo> titleCheckResult){
        if (arg.getObjectType() == ObjectTypeEnum.ACTIVITY.getType() || arg.getObjectType() == ObjectTypeEnum.ACTIVITY_INVITATION.getType()) {
            String activityId = activityManager.getActivityIdByObject(arg.getObjectId(), arg.getObjectType(), null, null);
            ActivityEntity activityEntity = activityDAO.getById(activityId,arg.getEa());
            arg.setMarketingEventId(activityEntity.getMarketingEventId());
        }
        if (StringUtils.isBlank(arg.getMarketingEventId()) && StringUtils.isNotBlank(arg.getMarketingActivityId())) {
            arg.setMarketingEventId(marketingActivityExternalConfigDao.getMarketingEventIdByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId()));
        }
        /*
        byte[] excelByte = fileV2Manager.downloadAFile(arg.getTaPath(), null);
        if (excelByte == null) {
            log.warn("CustomizeFormDataServiceImpl.importFormEnrollData excelByte arg:{}", arg);
            return;
        }
        List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(arg.getTaPath(), excelByte);
        Result checkImportData = checkImportData(excelData);
        if (!checkImportData.isSuccess()) {
            log.warn("CustomizeFormDataServiceImpl.importFormEnrollData checkImportData error:{}", checkImportData);
            return;
        }

        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId());
        // 校验title
        // 自定义字段（渠道）
        FieldInfo channelField = new FieldInfo();
        channelField.setApiName(ImportConstants.IMPORT_CHANNEL_API_NAME);
        channelField.setType(ImportConstants.IMPORT_CUSTOMIZE_TYPE);
        channelField.setIsRequired(false);
        channelField.setIsVerify(false);
        channelField.setLabel(ImportConstants.IMPORT_CHANNEL_LABEL);
        List<FieldInfo> titleCheckResult = importManager.checkFormImportTitleData(Arrays.asList(excelData.get(0)), customizeFormDataEntity, Lists.newArrayList(channelField));
        if (CollectionUtils.isEmpty(titleCheckResult)) {
            return;
        }
        */
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(), arg.getEa());

        // 导入数据
        ImportLineArgContainer argContainer = new ImportLineArgContainer();
        argContainer.setEa(arg.getEa());
        argContainer.setTitleField(titleCheckResult);
        argContainer.setExcelData(excelData);
        argContainer.setFormId(arg.getFormId());
        argContainer.setObjectId(arg.getObjectId());
        argContainer.setObjectType(arg.getObjectType());
        argContainer.setMarketingActivity(arg.getMarketingActivityId());
        argContainer.setMarketingEventId(arg.getMarketingEventId());
        List<ImportLineResultContainer> importLineResultContainerList = importManager.saveFormEnrollData(argContainer);
        // 生成问题文件
        Long failNum = importLineResultContainerList.stream().filter(data -> !ImportErrorEnum.SUCCESS.getDesc().equals(data.getImportDesc())).count();
        XSSFWorkbook errorFileWorkbook = null;
        if(failNum != 0) {
            errorFileWorkbook = importManager.buildErrorExcelFileForForm(excelData, importLineResultContainerList);
        }
        // 保存CRM
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = importLineResultContainerList.stream().filter(data -> data.getCustomizeFormDataUserEntity() != null)
                .map(ImportLineResultContainer::getCustomizeFormDataUserEntity).collect(Collectors.toList());
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(arg.getMarketingEventId(), arg.getEa());
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            // 批量查询报名数据
            customizeFormDataManager.saveEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
            log.info("saveEnrollDataToCrm customizeFormDataUserEntity data:{}",customizeFormDataUserEntity);
            String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(arg.getEa(), customizeFormDataUserEntity.getId(), false);
            customizeTicketManager.createConferenceCustomizeTicket(arg.getObjectType(), arg.getObjectId(), campaignId, arg.getEa(), arg.getMarketingEventId());
            // 会议导入才会进入报名表
            if (activityEntity != null) {
                customerCustomizeFormDataManager.addEnrollObject(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getId());
            }
        }
        CustomizeFormDataUserEntity customizeFormDataUserEntity = new CustomizeFormDataUserEntity();
        customizeFormDataUserEntity.setMarketingEventId(arg.getMarketingEventId());
        customizeFormDataUserEntity.setMarketingActivityId(arg.getMarketingActivityId());
        customizeFormDataUserEntity.setFormId(arg.getFormId());
        customizeFormDataManager.sendNotice(arg.getEa(), customizeFormDataUserEntity);
        long success = importLineResultContainerList.size() - failNum;
        //发送消息到文件助手
        String text = null;
        if (failNum > 0) {
            text = "您于" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "导入的表单数据，导入成功"+success+"条，导入失败"+failNum+"条，具体请查看以下导入结果。";
        }else {
            text = "您于" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "导入的表单数据，已全部导入成功，共导入" + success + "条。";
        }
        pushSessionManager.pushTextToFileAssistant(text, arg.getEa(), arg.getFsUserId());
        if (failNum > 0) {
            String formattedDate =  new SimpleDateFormat("yyyyMMdd").format(new Date());
            String sheetName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_660) + formattedDate + ".xlsx";
            pushSessionManager.pushExcelToFileAssistant(errorFileWorkbook, sheetName, arg.getEa(), arg.getFsUserId());
        }
    }

    @Override
    public Result<GetBingFormByObjectResult> getBingFormByObject(GetBingFormByObjectArg arg) {
        GetBingFormByObjectResult getBingFormByObjectResult = new GetBingFormByObjectResult();
        // 微页面站点特殊处理
        if (arg.getObjectType().equals(ObjectTypeEnum.HEXAGON_SITE.getType())) {
            List<HexagonSiteListDTO> hexagonSiteList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(arg.getObjectId()), arg.getEa());
            if (CollectionUtils.isNotEmpty(hexagonSiteList)) {
                getBingFormByObjectResult.setFormId(hexagonSiteList.get(0).getFormId());
            }
        } else {
            CustomizeFormDataEntity bindFormDataByObject = customizeFormDataManager.getBindFormDataByObject(arg.getEa(), arg.getObjectId(), arg.getObjectType());
            if (bindFormDataByObject != null) {
                getBingFormByObjectResult.setFormId(bindFormDataByObject.getId());
            }
        }
        return Result.newSuccess(getBingFormByObjectResult);
    }

    @Override
    public Result<Void> sendSaveClueFailMessage(SendSaveClueFailMessageArg arg) {
        customizeFormDataManager.sendSaveClueFailMessage(arg.getEa(), arg.getFormId(), arg.getMarketingEventId(), true);
        return Result.newSuccess();
    }

    @Override
    public Result<EnrollDataCountResult> countUnSaveEnrollData(CountUnSaveEnrollDataArg arg) {
        if(arg.getFormUsage() == null){
            arg.setFormUsage(FormDataUsage.COLLECT_LEADS.getUsage());
        }
        return queryMultipleFormUserDataManager.countUnSaveEnrollData(arg);
    }

    private Result checkImportArg(String ea, String formId, String objectId, Integer objectType) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId,ea);
        if (customizeFormDataEntity == null) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        if (customizeFormDataEntity.getStatus().equals(CustomizeFormDataStatusEnum.DELETE.getValue())) {
            return new Result(SHErrorCode.CUSTOMIZE_FORM_DATA_DELETE);
        }
        if (!objectType.equals(ObjectTypeEnum.CUSTOMIZE_FORM.getType())) {
            CustomizeFormDataObjectEntity customizeFormDataObjectEntity = customizeFormDataObjectDAO.accurateAccessCustomizeFormDataObject(ea, formId, objectId, objectType);
            if (customizeFormDataObjectEntity != null) {
                return Result.newSuccess();
            }
            //如果是文章或者产品，表单可能挂在文章或产品关联的微页面上
            if (ObjectTypeEnum.PRODUCT.getType() == objectType || ObjectTypeEnum.ARTICLE.getType() == objectType) {
                HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(ea, objectId, objectType);
                if (hexagonSiteObjectEntity == null){
                    return Result.newError(SHErrorCode.OBJECT_NOT_BIND_CUSTOMIZE_FORM);
                }
                List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(hexagonSiteObjectEntity.getSiteId()), ea);
                if (CollectionUtils.isEmpty(hexagonSiteListDTOList) || StringUtils.isBlank(hexagonSiteListDTOList.get(0).getFormId())) {
                    return Result.newError(SHErrorCode.OBJECT_NOT_BIND_CUSTOMIZE_FORM);
                }
                customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(hexagonSiteListDTOList.get(0).getFormId(),ea);
                if (customizeFormDataEntity == null) {
                    return Result.newError(SHErrorCode.OBJECT_NOT_BIND_CUSTOMIZE_FORM);
                }
            }
        }
        return Result.newSuccess();
    }

    private Result checkImportData(List<String[]> excelData) {
        if (CollectionUtils.isEmpty(excelData)) {
            return Result.newError(SHErrorCode.IMPORT_FILE_NO_DATA);
        }
        if (excelData.size() > ImportConstants.IMPORT_LIMIT) {
            return Result.newError(SHErrorCode.IMPORT_DATA_EXCEED_THE_LIMIT);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<AddCustomizeFormDataResult> addCustomizeFormData(AddCustomizeFormDataArg addCustomizeFormDataArg, String copyFormId, boolean checkCrmField) {
        addCustomizeFormDataArg.setCrmApiName(addCustomizeFormDataArg.getCrmApiName() == null ? CrmObjectApiNameEnum.CRM_LEAD.getName() : addCustomizeFormDataArg.getCrmApiName());
        if(FormDataUsage.getByType(addCustomizeFormDataArg.getFormUsage()) == FormDataUsage.COLLECT_ORDER){
            Preconditions.checkArgument(addCustomizeFormDataArg.getFormSuccessSetting() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_741));
            Preconditions.checkArgument(!Strings.isNullOrEmpty(addCustomizeFormDataArg.getFormSuccessSetting().getPayDescription()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_742));
            Preconditions.checkArgument(addCustomizeFormDataArg.getFormSuccessSetting().getTotalFee() != null && addCustomizeFormDataArg.getFormSuccessSetting().getTotalFee() > 0, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_743));
        }

        //自定义表单不能重名
        if (addCustomizeFormDataArg.getType() != null && addCustomizeFormDataArg.getType() == CustomizeFormDataTypeEnum.CUSTOMIZE.getValue()) {
            int count = customizeFormDataDAO.queryCustomizeFormCountByName(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getFormHeadSetting().getName());
            if (count > 0){
                return Result.newError(SHErrorCode.CUSTOMZIE_FORM_SITE_NAME_EXIST);
            }
        }


        if (CustomizeFormDataTypeEnum.CONFERENCE_SIGN_UP.getValue().equals(addCustomizeFormDataArg.getType()) || CustomizeFormDataTypeEnum.LIVE_SIGN_UP.getValue().equals(addCustomizeFormDataArg.getType())) {
            CustomizeFormDataEntity template = customizeFormDataDAO.getEaSignUpTemplate(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getType());
            Preconditions.checkArgument(template == null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_757));
        }
        // 首先检验映射值是否正确
        if (checkCrmField) {
            if (CollectionUtils.isNotEmpty(addCustomizeFormDataArg.getCrmFormFieldMap()) && addCustomizeFormDataArg.getFormMoreSetting().isSynchronousCRM()) {
                List<FieldMappings.FieldMapping> fieldMappings = BeanUtil.copy(addCustomizeFormDataArg.getCrmFormFieldMap(), FieldMappings.FieldMapping.class);
                String validateResult = crmV2MappingManager
                        .doVerifyMankeepCrmObjectFieldMappingEntities(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getCrmApiName(), FieldMappings.newInstance(fieldMappings),
                                addCustomizeFormDataArg.getFormBodySetting());
                if (!CrmV2MappingManager.FieldValidateMessage.FIELD_SUCCESS.equals(validateResult)) {
                    return new Result<>(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR.getErrorCode(), validateResult);
                }
            }
        }
        AddCustomizeFormDataResult addCustomizeFormDataResult = new AddCustomizeFormDataResult();
        CreateFormDataDTO createFormDataDTO = BeanUtil.copy(addCustomizeFormDataArg, CreateFormDataDTO.class);
        String formId = customizeFormDataManager.createFormData(createFormDataDTO);
        if (StringUtils.isNotBlank(formId) && StringUtils.isNotBlank(addCustomizeFormDataArg.getMarketingEventId())) {
            // 创建二维码
            CustomizeFormQrCodeContainer customizeFormQrCodeContainer = customizeFormDataManager.createCustomizeFormDataQRCode(formId, addCustomizeFormDataArg.getMarketingEventId(), addCustomizeFormDataArg.getEa());
            addCustomizeFormDataResult.setH5QrUrl(customizeFormQrCodeContainer.getH5Url());
            addCustomizeFormDataResult.setH5QrPath(customizeFormQrCodeContainer.getH5Path());
            addCustomizeFormDataResult.setMiniAppQrUrl(customizeFormQrCodeContainer.getMiniAppUrl());
            addCustomizeFormDataResult.setMiniAppQrPath(customizeFormQrCodeContainer.getMiniAppPath());
        } else if (StringUtils.isBlank(formId)) {
            log.warn("CustomizeFormDataServiceImpl.addCustomizeFormData createFormData error addCustomizeFormDataArg:{}", addCustomizeFormDataArg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        if (addCustomizeFormDataArg.getTagNameList() != null) {
            objectTagManager.addOrUpdateObjectTag(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getFsUserId(), formId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), addCustomizeFormDataArg.getTagNameList());
        }
        addCustomizeFormDataResult.setId(formId);
        // 微页面的表单就不用创建日志了
        if (addCustomizeFormDataArg.getType() != null && addCustomizeFormDataArg.getType() != 2) {
            if (createFormDataDTO.getFormHeadSetting() != null) {
                String name = createFormDataDTO.getFormHeadSetting().getName();
                mktContentMgmtLogObjManager.createByOperateTypeWithObjName(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getFsUserId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), name, OperateTypeEnum.ADD);
            } else {
                mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getFsUserId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), formId, OperateTypeEnum.ADD);
            }
        }
        if(StringUtils.isNotBlank(addCustomizeFormDataArg.getFormHeadSetting().getOriginalImageAPath()) && CollectionUtils.isNotEmpty(addCustomizeFormDataArg.getFormHeadSetting().getCutOffsetList())){
            List<com.facishare.marketing.api.arg.PhotoCutOffset> copy = BeanUtil.copy(addCustomizeFormDataArg.getFormHeadSetting().getCutOffsetList(), com.facishare.marketing.api.arg.PhotoCutOffset.class);
            for ( com.facishare.marketing.api.arg.PhotoCutOffset cutOffset : copy){
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_MINIAPP_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(addCustomizeFormDataArg.getEa(),PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_MINIAPP_COVER, addCustomizeFormDataResult.getId(), cutOffset, addCustomizeFormDataArg.getFormHeadSetting().getOriginalImageAPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_H5_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(addCustomizeFormDataArg.getEa(),PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_H5_COVER, addCustomizeFormDataResult.getId(), cutOffset, addCustomizeFormDataArg.getFormHeadSetting().getOriginalImageAPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_ORDINARY_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(addCustomizeFormDataArg.getEa(),PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_ORDINARY_COVER, addCustomizeFormDataResult.getId(), cutOffset, addCustomizeFormDataArg.getFormHeadSetting().getOriginalImageAPath());
                }
            }
        }
        //神策埋点
        marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getFsUserId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, addCustomizeFormDataResult.getId()));
        ctaRelationDaoManager.addCtaRelation(addCustomizeFormDataArg.getEa(), addCustomizeFormDataArg.getCtaIds(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), addCustomizeFormDataResult.getId());
        return new Result<>(SHErrorCode.SUCCESS, addCustomizeFormDataResult);
    }

    @Override
    public Result<UpdateCustomizeFormDataDetailResult> updateCustomizeFormDataDetail(UpdateCustomizeFormDataDetailArg updateCustomizeFormDataDetailArg) {
        updateCustomizeFormDataDetailArg.setCrmApiName(updateCustomizeFormDataDetailArg.getCrmApiName() == null ? CrmObjectApiNameEnum.CRM_LEAD.getName() : updateCustomizeFormDataDetailArg.getCrmApiName());
        CustomizeFormDataEntity oldCustomerFormData = customizeFormDataDAO.getCustomizeFormDataById(updateCustomizeFormDataDetailArg.getId(), updateCustomizeFormDataDetailArg.getEa());
        Preconditions.checkArgument(FormDataUsage.getByType(oldCustomerFormData.getFormUsage()) == FormDataUsage.getByType(updateCustomizeFormDataDetailArg.getFormUsage()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_821));
        if(FormDataUsage.getByType(updateCustomizeFormDataDetailArg.getFormUsage()) == FormDataUsage.COLLECT_ORDER){
            Preconditions.checkArgument(updateCustomizeFormDataDetailArg.getFormSuccessSetting() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_741));
            Preconditions.checkArgument(!Strings.isNullOrEmpty(updateCustomizeFormDataDetailArg.getFormSuccessSetting().getPayDescription()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_742));
            Preconditions.checkArgument(updateCustomizeFormDataDetailArg.getFormSuccessSetting().getTotalFee() != null && updateCustomizeFormDataDetailArg.getFormSuccessSetting().getTotalFee() > 0, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_743));
        }
        //自定义表单不能重名
        if (oldCustomerFormData.getType() != null && oldCustomerFormData.getType() == CustomizeFormDataTypeEnum.CUSTOMIZE.getValue()) {
            if (!StringUtils.equals(oldCustomerFormData.getFormHeadSetting().getName(), updateCustomizeFormDataDetailArg.getFormHeadSetting().getName())) {
                int count = customizeFormDataDAO.queryCustomizeFormCountByName(updateCustomizeFormDataDetailArg.getEa(), updateCustomizeFormDataDetailArg.getFormHeadSetting().getName());
                if (count > 0) {
                    return Result.newError(SHErrorCode.CUSTOMZIE_FORM_SITE_NAME_EXIST);
                }
            }
        }

        // 首先检验映射值是否正确
        List<FieldMappings.FieldMapping> fieldMappingList = BeanUtil.copy(updateCustomizeFormDataDetailArg.getCrmFormFieldMap(), FieldMappings.FieldMapping.class);
        FieldMappings fieldMappings = FieldMappings.newInstance(fieldMappingList);
        if (CollectionUtils.isNotEmpty(updateCustomizeFormDataDetailArg.getCrmFormFieldMap()) && updateCustomizeFormDataDetailArg.getFormMoreSetting().isSynchronousCRM() && updateCustomizeFormDataDetailArg.getCheckMapping()) {
            String validateResult = crmV2MappingManager.doVerifyMankeepCrmObjectFieldMappingEntities(updateCustomizeFormDataDetailArg.getEa(), updateCustomizeFormDataDetailArg.getCrmApiName(), fieldMappings,
                updateCustomizeFormDataDetailArg.getFormBodySetting());
            if (!CrmV2MappingManager.FieldValidateMessage.FIELD_SUCCESS.equals(validateResult)) {
                return new Result<>(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR.getErrorCode(), validateResult);
            }
        }
        //检验活动成员映射值是否正确
        FieldMappings filedCampaignMappings = null;
        if (CollectionUtils.isNotEmpty(updateCustomizeFormDataDetailArg.getCampaignMemberMap())) {
            List<FieldMappings.FieldMapping> filedCampaignMappingsList = BeanUtil.copy(updateCustomizeFormDataDetailArg.getCampaignMemberMap(), FieldMappings.FieldMapping.class);
            filedCampaignMappings = FieldMappings.newInstance(filedCampaignMappingsList);
//            if (CollectionUtils.isNotEmpty(updateCustomizeFormDataDetailArg.getCrmFormFieldMap()) && updateCustomizeFormDataDetailArg.getFormMoreSetting().isSynchronousCRM() && updateCustomizeFormDataDetailArg.getCheckMapping()) {
//                String validateResult = crmV2MappingManager.doVerifyMankeepCrmObjectFieldMappingEntities(updateCustomizeFormDataDetailArg.getEa(), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), filedCampaignMappings,
//                        updateCustomizeFormDataDetailArg.getFormBodySetting());
//                if (!CrmV2MappingManager.FieldValidateMessage.FIELD_SUCCESS.equals(validateResult)) {
//                    return new Result<>(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR.getErrorCode(), validateResult);
//                }
//            }
        }
//
//        // 会员信息自动填写表单映射处理
//        FieldMappings memberToFormMappings = null;
//        List<FieldMappingResult> memberToFormMappingList = updateCustomizeFormDataDetailArg.getMemberToFormMapping();
//        if (CollectionUtils.isNotEmpty(memberToFormMappingList)) {
//            List<FieldMappings.FieldMapping> copyList = BeanUtil.copy(memberToFormMappingList, FieldMappings.FieldMapping.class);
//            memberToFormMappings = FieldMappings.newInstance(copyList);
//        }

        CustomizeFormDataEntity customizeFormDataEntity = BeanUtil.copy(updateCustomizeFormDataDetailArg, CustomizeFormDataEntity.class);
        customizeFormDataEntity.setCrmFormFieldMapV2(fieldMappings);
        customizeFormDataEntity.setCampaignMemberMap(filedCampaignMappings);
//        customizeFormDataEntity.setMemberToFormMapping(memberToFormMappings);
        customizeFormDataEntity.setUpdateBy(updateCustomizeFormDataDetailArg.getFsUserId());
        if (customizeFormDataEntity.getFormHeadSetting() != null && CollectionUtils.isNotEmpty(customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath())) {
            List<String> headPhotoPath = customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath();
            customizeFormDataEntity.getFormHeadSetting().setHeadPhotoPath(customizeFormDataManager.convertTaPathToApath(headPhotoPath, updateCustomizeFormDataDetailArg.getEa(), updateCustomizeFormDataDetailArg.getFsUserId()));
        }
        if (customizeFormDataEntity.getFormMoreSetting().isSynchronousCRM()) {
            customizeFormDataEntity.setCrmApiName(updateCustomizeFormDataDetailArg.getCrmApiName());
        }
        FormFootSetting formFootSetting = BeanUtil.copy(updateCustomizeFormDataDetailArg.getFormFootSetting(), FormFootSetting.class);
        if (formFootSetting == null) {
            formFootSetting  = new FormFootSetting();
            formFootSetting.setButtonName(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_1090));
            formFootSetting.setFontColor("#FFFFFF");
            formFootSetting.setButtonColor("#F8B05B");
            formFootSetting.setButtonBorderColor("#F8B05B");
        }
        customizeFormDataEntity.setFormFootSetting(formFootSetting);
        customizeFormDataEntity.setContentStyle(updateCustomizeFormDataDetailArg.getContentStyle());
        int result = customizeFormDataDAOManager.updateCustomizeFormDataDetailNotMember(customizeFormDataEntity);
        if (result == 1) {
            UpdateCustomizeFormDataDetailResult updateCustomizeFormDataDetailResult = new UpdateCustomizeFormDataDetailResult();
            // 获取二维码
            //updateCustomizeFormDataDetailResult.setQrUrl(customizeFormDataManager.getCustomizeFormDataQRCode(customizeFormDataEntity.getId(), null));
            updateCustomizeFormDataDetailResult.setId(customizeFormDataEntity.getId());
            objectTagManager.addOrUpdateObjectTag(updateCustomizeFormDataDetailArg.getEa(), updateCustomizeFormDataDetailArg.getFsUserId(), customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), updateCustomizeFormDataDetailArg.getTagNameList());
            createMktContentMgmtLog(updateCustomizeFormDataDetailArg, oldCustomerFormData);
            if(StringUtils.isNotBlank(updateCustomizeFormDataDetailArg.getFormHeadSetting().getOriginalImageAPath()) && CollectionUtils.isNotEmpty(updateCustomizeFormDataDetailArg.getFormHeadSetting().getCutOffsetList())){
                List<com.facishare.marketing.api.arg.PhotoCutOffset> copy = BeanUtil.copy(updateCustomizeFormDataDetailArg.getFormHeadSetting().getCutOffsetList(), com.facishare.marketing.api.arg.PhotoCutOffset.class);
                for ( com.facishare.marketing.api.arg.PhotoCutOffset cutOffset : copy){
                    if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_MINIAPP_COVER.getType()){
                        photoManager.addOrUpdatePhotoByCutOffset(updateCustomizeFormDataDetailArg.getEa(),PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_MINIAPP_COVER, updateCustomizeFormDataDetailArg.getId(), cutOffset, updateCustomizeFormDataDetailArg.getFormHeadSetting().getOriginalImageAPath());
                    }
                    if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_H5_COVER.getType()){
                        photoManager.addOrUpdatePhotoByCutOffset(updateCustomizeFormDataDetailArg.getEa(),PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_H5_COVER, updateCustomizeFormDataDetailArg.getId(), cutOffset, updateCustomizeFormDataDetailArg.getFormHeadSetting().getOriginalImageAPath());
                    }
                    if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_ORDINARY_COVER.getType()){
                        photoManager.addOrUpdatePhotoByCutOffset(updateCustomizeFormDataDetailArg.getEa(),PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_ORDINARY_COVER, updateCustomizeFormDataDetailArg.getId(), cutOffset, updateCustomizeFormDataDetailArg.getFormHeadSetting().getOriginalImageAPath());
                    }
                }
            }else{
                photoManager.deleteByTargetIdAndTypes(updateCustomizeFormDataDetailArg.getId(),Lists.newArrayList(PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_MINIAPP_COVER.getType(),PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_H5_COVER.getType()
                        ,PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_ORDINARY_COVER.getType()),updateCustomizeFormDataDetailArg.getEa());
            }
            ctaRelationDaoManager.addCtaRelation(updateCustomizeFormDataDetailArg.getEa(), updateCustomizeFormDataDetailArg.getCtaIds(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), customizeFormDataEntity.getId());
            return new Result<>(SHErrorCode.SUCCESS, updateCustomizeFormDataDetailResult);
        } else {
            log.warn("CustomizeFormDataServiceImpl.updateCustomizeFormDataDetail updateCustomizeFormDataDetail error updateCustomizeFormDataDetailArg:{}", updateCustomizeFormDataDetailArg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    private void createMktContentMgmtLog(UpdateCustomizeFormDataDetailArg updateCustomizeFormDataDetailArg, CustomizeFormDataEntity oldCustomerFormData) {
        try {
            if (updateCustomizeFormDataDetailArg.isSetting()) {
                createSettingLog(updateCustomizeFormDataDetailArg, oldCustomerFormData);
            } else if ( oldCustomerFormData.getType() != null && oldCustomerFormData.getType() != 2) {
                String ea = updateCustomizeFormDataDetailArg.getEa();
                int userId = updateCustomizeFormDataDetailArg.getFsUserId();
                mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(ea, userId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), updateCustomizeFormDataDetailArg.getId(), OperateTypeEnum.EDIT);
            }
        } catch (Exception e) {
            log.error("createMktContentMgmtLog error", e);
        }
    }

    private void createSettingLog(UpdateCustomizeFormDataDetailArg updateCustomizeFormDataDetailArg, CustomizeFormDataEntity oldCustomerFormData) {
        List<String> msgList = Lists.newArrayList();
        String crmPoolId = updateCustomizeFormDataDetailArg.getCrmPoolId();
        String ea = updateCustomizeFormDataDetailArg.getEa();
        int userId = updateCustomizeFormDataDetailArg.getFsUserId();
        if (StringUtils.isNotBlank(crmPoolId)) {
            String poolName = crmPoolId;
            Result<List<LeadPoolResult>> leadsPoolDataList = crmService.listLeadPools(ea, userId);
            if (leadsPoolDataList != null && CollectionUtils.isNotEmpty(leadsPoolDataList.getData())) {
                Optional<LeadPoolResult> poolDataOptional = leadsPoolDataList.getData().stream().filter(e -> crmPoolId.equals(e.getId())).findFirst();
                if (poolDataOptional.isPresent()) {
                    poolName = poolDataOptional.get().getName();
                }
            }
            msgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_951) + poolName);
        } else {
            msgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_953));
        }
        List<FieldMappingResult> fieldMappingResultList = updateCustomizeFormDataDetailArg.getCrmFormFieldMap();
        if (CollectionUtils.isNotEmpty(fieldMappingResultList)) {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult =
                    objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CRM_LEAD.getName());
            if (getDescribeResultResult != null && getDescribeResultResult.getData() != null) {
                ControllerGetDescribeResult describeResult = getDescribeResultResult.getData();
                FieldInfoList fieldInfoList = updateCustomizeFormDataDetailArg.getFormBodySetting();
                List<String> tempMsgList = Lists.newArrayList();
                Map<String, String> apiNameToLabelMap = fieldInfoList.stream().collect(Collectors.toMap(FieldInfo::getApiName, FieldInfo::getLabel, (v1, v2) -> v1));
                for (FieldMappingResult fieldMappingResult : fieldMappingResultList) {
                    String manKeepLabel = apiNameToLabelMap.get(fieldMappingResult.getMankeepFieldName());
                    FieldDescribe fieldDescribe = describeResult.getDescribe().getFields().get(fieldMappingResult.getCrmFieldName());
                    if (fieldDescribe == null || StringUtils.isBlank(manKeepLabel)) {
                        continue;
                    }
                    String crmLabel = fieldDescribe.getLabel();
                    tempMsgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_972) + manKeepLabel + I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_972_1) + crmLabel);
                }
                msgList.add(String.join("、", tempMsgList));
            }
        }
        FormMoreSetting formMoreSetting = updateCustomizeFormDataDetailArg.getFormMoreSetting();
        if (formMoreSetting != null) {
            if (formMoreSetting.isSynchronousCRM()) {
                msgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_980));
            }
            if (formMoreSetting.isFillInOnce()) {
                msgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_983));
            }
            if (formMoreSetting.getCheckMember()) {
                msgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_986));
            }
            if (formMoreSetting.isEnrollLimit()) {
                msgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_989) + formMoreSetting.getEnrollLimitNum());
                msgList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_990) + formMoreSetting.getEnrollLimitText());
            }
        }
        if (CollectionUtils.isNotEmpty(msgList)) {
            String msg = String.join(",", msgList);
            MktContentMgmtLogCreateArg mktContentMgmtLogCreateArg = new MktContentMgmtLogCreateArg();
            mktContentMgmtLogCreateArg.setEa(ea);
            mktContentMgmtLogCreateArg.setUserId(userId);
            mktContentMgmtLogCreateArg.setMaterialType(MaterialTypeEnum.FORM.getCode());
            mktContentMgmtLogCreateArg.setOperateType(OperateTypeEnum.SETTING.getOperateType());
            mktContentMgmtLogCreateArg.setMaterialName(oldCustomerFormData.getFormHeadSetting().getName());
            if (oldCustomerFormData.getType() != null && oldCustomerFormData.getType() == 2) {
                List<CustomizeFormDataObjectEntity> formDataObjectEntities = customizeFormDataObjectDAO.queryCustomizeFormDataObjectByFormId(oldCustomerFormData.getId(), ea);
                if (CollectionUtils.isNotEmpty(formDataObjectEntities)) {
                    CustomizeFormDataObjectEntity formDataObjectEntity = formDataObjectEntities.get(0);
                    HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getById(ea, formDataObjectEntity.getObjectId());
                    if (hexagonPageEntity != null) {
                        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(hexagonPageEntity.getHexagonSiteId(), updateCustomizeFormDataDetailArg.getEa());
                        mktContentMgmtLogCreateArg.setMaterialType(MaterialTypeEnum.HEXAGON_SITE.getCode());
                        mktContentMgmtLogCreateArg.setOperateType(OperateTypeEnum.SETTING.getOperateType());
                        mktContentMgmtLogCreateArg.setMaterialName(hexagonSiteEntity.getName());
                    }
                }
            }
            mktContentMgmtLogCreateArg.setOperateDetail(msg);
            mktContentMgmtLogObjManager.createObj(mktContentMgmtLogCreateArg);
        }
    }

    @Override
    public Result updateCustomizeFormDataStatus(UpdateCustomizeFormDataStatusArg updateCustomizeFormDataStatusArg) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(updateCustomizeFormDataStatusArg.getId(), updateCustomizeFormDataStatusArg.getEa());
        if (customizeFormDataEntity == null) {
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        List<CustomizeFormDataObjectEntity> customizeFormDataObjectEntityList = customizeFormDataObjectDAO.queryCustomizeFormDataObjectByFormId(updateCustomizeFormDataStatusArg.getId(), updateCustomizeFormDataStatusArg.getEa());
        if (CollectionUtils.isNotEmpty(customizeFormDataObjectEntityList) && updateCustomizeFormDataStatusArg.getStatus().equals(CustomizeFormDataStatusEnum.DISABLE.getValue())) {
            // 停用时解除关联物料关系
           customizeFormDataObjectDAO.deleteCustomizeFormDataObjectByFormId(updateCustomizeFormDataStatusArg.getId(), updateCustomizeFormDataStatusArg.getEa());
        } else if (customizeFormDataEntity.getStatus().equals(CustomizeFormDataStatusEnum.NORMAL.getValue()) && updateCustomizeFormDataStatusArg.getStatus().equals(CustomizeFormDataStatusEnum.DELETE.getValue())) {
            // 删除前需先停用
            return new Result<>(SHErrorCode.UNBINDING_BEFORE_DELETING);
        }

        if (customizeFormDataEntity.getStatus() == CustomizeFormDataStatusEnum.DISABLE.getValue() && updateCustomizeFormDataStatusArg.getStatus() == CustomizeFormDataStatusEnum.NORMAL.getValue()) {
            int count = customizeFormDataDAO.getEnableCustomizeFormByName(updateCustomizeFormDataStatusArg.getEa(), CustomizeFormDataStatusEnum.NORMAL.getValue(), customizeFormDataEntity.getFormHeadSetting().getName());
            if (count > 0) {
                return Result.newError(SHErrorCode.CUSTOMZIE_FORM_SITE_NAME_EXIST);
            }
        } else if (updateCustomizeFormDataStatusArg.getStatus().equals(CustomizeFormDataStatusEnum.DELETE.getValue())) {
            objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(updateCustomizeFormDataStatusArg.getEa(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), Collections.singletonList(updateCustomizeFormDataStatusArg.getId()));
            //删除置顶数据
            objectTopManager.deleteByObjectIdAndObjectType(updateCustomizeFormDataStatusArg.getEa(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), Collections.singletonList(updateCustomizeFormDataStatusArg.getId()));
        }
        int result =  customizeFormDataDAOManager.updateCustomizeFormDataStatus(updateCustomizeFormDataStatusArg.getEa(),updateCustomizeFormDataStatusArg.getId(), updateCustomizeFormDataStatusArg.getFsUser(), updateCustomizeFormDataStatusArg.getStatus());
        if (result == 1) {
            OperateTypeEnum operateTypeEnum = null;
            int status = updateCustomizeFormDataStatusArg.getStatus();
            if (status == CustomizeFormDataStatusEnum.NORMAL.getValue()) {
                operateTypeEnum = OperateTypeEnum.SET_TO_START;
            } else if (status == CustomizeFormDataStatusEnum.DISABLE.getValue()) {
                operateTypeEnum = OperateTypeEnum.SET_TO_STOP;
            } else if (status == CustomizeFormDataStatusEnum.DELETE.getValue()) {
                operateTypeEnum = OperateTypeEnum.DELETE;
            }
            if (operateTypeEnum != null) {
                mktContentMgmtLogObjManager.createByOperateTypeWithObjName(updateCustomizeFormDataStatusArg.getEa(), updateCustomizeFormDataStatusArg.getFsUser(),
                        ObjectTypeEnum.CUSTOMIZE_FORM.getType(), customizeFormDataEntity.getFormHeadSetting().getName(), operateTypeEnum);
            }
            return new Result<>(SHErrorCode.SUCCESS);
        } else {
            log.warn("CustomizeFormDataServiceImpl.updateFormDataStatus updateFormDataStatus error updateFormDataStatusArg:{}", updateCustomizeFormDataStatusArg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @FilterLog
    public Result<PageResult<QueryCustomizeFormDataResult>> queryCustomizeFormData(QueryCustomizeFormDataArg arg) {
        List<QueryCustomizeFormDataResult> queryCustomizeFormDataResultList = Lists.newArrayList();
        PageResult<QueryCustomizeFormDataResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(queryCustomizeFormDataResultList);
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormData(arg.getEa(), arg.getName(), arg.getStatus(), BooleanUtils.isTrue(arg.getIncludeHexagonForm()), page);
        queryCustomizeFormDataResultList = BeanUtil.copy(customizeFormDataEntityList, QueryCustomizeFormDataResult.class);
        if (CollectionUtils.isEmpty(customizeFormDataEntityList)) {
            return new Result<>(SHErrorCode.SUCCESS, pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = customizeFormDataEntityList.stream().collect(Collectors.toMap(CustomizeFormDataEntity::getId, data -> data));
        // 查询创建人姓名
        List<Integer> fsUserIds =  queryCustomizeFormDataResultList.stream().map(QueryCustomizeFormDataResult::getCreateBy).collect(Collectors.toList());
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsUserMap = fsAddressBookManager.getEmployeeInfoByUserIds(arg.getEa(), fsUserIds, true);
        // 设置表单绑定数据
        List<String> formIds = queryCustomizeFormDataResultList.stream().map(QueryCustomizeFormDataResult::getId).collect(Collectors.toList());
        Map<String, Map<Integer, Integer>> statisticsMapList =  customizeFormDataManager.bindObjectStatistics(formIds, arg.getEa());
        // 设置表单报名数据
        List<CustomizeFormDataUserStatisticDTO> customizeFormDataUserStatisticDTOList = customizeFormDataUserDAO.queryCustomizeFormDataUserStatistic(formIds, arg.getEa());
        Map<String, Integer> customizeFormDataUserStatisticMap = customizeFormDataUserStatisticDTOList.stream().filter(data -> (StringUtils.isNotBlank(data.getFormId()) && data.getCount() != null))
            .collect(Collectors.toMap(CustomizeFormDataUserStatisticDTO::getFormId, CustomizeFormDataUserStatisticDTO::getCount));
        // 访问人数
        List<ObjectStatisticData> objectStatisticDatas = marketingObjectAmountStatisticDao.listStatisticData(arg.getEa(), formIds);
        Map<String, ObjectStatisticData> marketingObjectActivityStatisticDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(objectStatisticDatas)) {
            marketingObjectActivityStatisticDTOMap = objectStatisticDatas.stream().collect(Collectors.toMap(ObjectStatisticData::getObjectId, data -> data));
        }
        for (QueryCustomizeFormDataResult data : queryCustomizeFormDataResultList) {
            QueryCustomizeFormDataResult.BindObjectStatistics bindObjectStatistics = new QueryCustomizeFormDataResult.BindObjectStatistics();
            Map<Integer, Integer> statisticsMap = statisticsMapList.get(data.getId());
            if (MapUtils.isNotEmpty(statisticsMap)) {
                bindObjectStatistics.setArticleCount(statisticsMap.get(ObjectTypeEnum.ARTICLE.getType()) != null ? statisticsMap.get(ObjectTypeEnum.ARTICLE.getType()) : 0);
                bindObjectStatistics.setActivityCount(statisticsMap.get(ObjectTypeEnum.ACTIVITY.getType()) != null ? statisticsMap.get(ObjectTypeEnum.ACTIVITY.getType()) : 0);
                bindObjectStatistics.setProductCount(statisticsMap.get(ObjectTypeEnum.PRODUCT.getType()) != null ? statisticsMap.get(ObjectTypeEnum.PRODUCT.getType()) : 0);
            } else {
                bindObjectStatistics.setArticleCount(0);
                bindObjectStatistics.setActivityCount(0);
                bindObjectStatistics.setProductCount(0);
            }
            data.setCreateByName(fsUserMap.get(data.getCreateBy()) != null ? fsUserMap.get(data.getCreateBy()).getName() : null);
            if (data.getCreateBy() == -10000) {
                data.setCreateByName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
            }
            // 设置报名总数
            data.setEnrollCount(customizeFormDataUserStatisticMap.get(data.getId()) != null ? customizeFormDataUserStatisticMap.get(data.getId()) : 0);
            data.setBindObjectStatistics(bindObjectStatistics);
            data.setCreateTimeStamp(data.getCreateTime().getTime());
            data.setUpdateTimeStamp(data.getUpdateTime().getTime());

            // 设置图片
            if (CollectionUtils.isNotEmpty(data.getFormHeadSetting().getHeadPhotoPath()) && CollectionUtils.isEmpty(data.getFormHeadSetting().getHeadPhotoUrl())) {
                data.getFormHeadSetting().setHeadPhotoUrl(customizeFormDataManager.setCustomizeFormDataHeadPhoto(data.getFormHeadSetting().getHeadPhotoPath()));
            }
            // 访问人数
            ObjectStatisticData objectStatisticData = marketingObjectActivityStatisticDTOMap.get(data.getId());
            data.setAccessCount(objectStatisticData != null ? objectStatisticData.getLookUpCount() : 0);
            data.setHadCrmMapping(false);
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataEntityMap.get(data.getId());
            if (customizeFormDataEntity != null && CollectionUtils.isNotEmpty(customizeFormDataEntity.getCrmFormFieldMapV2())) {
                data.setHadCrmMapping(true);
            }
        }
        pageResult.setResult(queryCustomizeFormDataResultList);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<CustomizeFormDataDetailResult> getCustomizeFormDataById(GetCustomizeFormDataByIdArg getCustomizeFormDataByIdArg) {
        return customizeFormDataManager.getCustomizeFormDataById(getCustomizeFormDataByIdArg);
    }

    @Override
    public Result<List<CustomizeFormDataDetailResult>> getCustomizeTemplateFormData(String ea) {
        List<CustomizeFormDataDetailResult> getCustomizeTemplateFormDataResultList = Lists.newArrayList();
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryTemplateCustomizeFormData(ea);
        if (CollectionUtils.isNotEmpty(customizeFormDataEntityList)) {
            for (CustomizeFormDataEntity customizeFormDataEntity : customizeFormDataEntityList) {
                CustomizeFormDataDetailResult customizeFormDataDetailResult = BeanUtil.copy(customizeFormDataEntity, CustomizeFormDataDetailResult.class);
                customizeFormDataDetailResult.setCrmFormFieldMap(customizeFormDataEntity.getCrmFormFieldMapV2());
                // 设置头图
                customizeFormDataDetailResult = customizeFormDataManager.setCustomizeFormDataHeadPhoto(customizeFormDataDetailResult);
                // 设置标题
                if (StringUtils.isBlank(customizeFormDataDetailResult.getFormHeadSetting().getTemplateName())) {
                    customizeFormDataDetailResult.setTemplateFormDataTitle(customizeFormDataDetailResult.getFormHeadSetting().getTitle());
                } else {
                    customizeFormDataDetailResult.setTemplateFormDataTitle(customizeFormDataDetailResult.getFormHeadSetting().getTemplateName());
                }
                getCustomizeTemplateFormDataResultList.add(customizeFormDataDetailResult);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, getCustomizeTemplateFormDataResultList);
    }

    @Override
    public Result customizeFormDataBindObject(CustomizeFormDataBindObjectArg customizeFormDataBindObjectArg) {
        customizeFormDataManager.bindCustomizeFormDataObject(customizeFormDataBindObjectArg.getFormId(), customizeFormDataBindObjectArg.getObjectId(), customizeFormDataBindObjectArg.getObjectType(), customizeFormDataBindObjectArg.getEa(),customizeFormDataBindObjectArg.getFsUserId(), FormStyleTypeEnum.BOTTOM.getType(), null, null);
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result customizeFormDataUnBindObject(CustomizeFormDataUnBindObjectArg arg) {
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(arg.getEa(), arg.getFormId(), arg.getObjectId(), arg.getObjectType());
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<PageResult<QueryFormUserDataResult>> queryFormUserData(QueryFormUserDataArg queryFormUserDataArg) {
        if(queryFormUserDataArg.getFormUsage() == null){
            queryFormUserDataArg.setFormUsage(FormDataUsage.COLLECT_LEADS.getUsage());
        }
        PageResult<QueryFormUserDataResult> result;
        String objectId = queryFormUserDataArg.getObjectId();
        Integer objectType = queryFormUserDataArg.getObjectType();
        if (queryFormUserDataArg.getType().equals(QueryFormUserDataTypeEnum.MARKETING_ACTIVITY.getType())) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                .getByMarketingActivityIdAndAssociateIdTypeList(queryFormUserDataArg.getEa(), queryFormUserDataArg.getMarketingActivityId(), Lists.newArrayList(AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType(), AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType()));
            if (marketingActivityExternalConfigEntity == null) {
                log.warn("CustomizeFormDataManager.getCustomizeFormDataUser marketingActivityExternalConfigEntity is null marketingActivityId:{}", queryFormUserDataArg.getMarketingActivityId());
                return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
            }
            NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId(), queryFormUserDataArg.getEa());
            if (noticeEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.queryFormUserData noticeEntity is null arg:{}", queryFormUserDataArg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            objectType = objectManager.convertNoticeContentTypeToObjectType(noticeEntity.getContentType());
            objectId = noticeEntity.getContent();
            QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, queryFormUserDataArg.getEa());
            if (queryFormUserContainer == null) {
                return new Result<>(SHErrorCode.SUCCESS);
            }
            objectType = queryFormUserContainer.getObjectType();
            objectId = queryFormUserContainer.getObjectId();
        } else if (queryFormUserDataArg.getType().equals(QueryFormUserDataTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType())) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getByMarketingActivityIdAndAssociateIdType(queryFormUserDataArg.getEa(), queryFormUserDataArg.getMarketingActivityId(), AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType());
            if (marketingActivityExternalConfigEntity == null) {
                log.warn("CustomizeFormDataManager.getCustomizeFormDataUser marketingActivityExternalConfigEntity is null marketingActivityId:{}", queryFormUserDataArg.getMarketingActivityId());
                return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
            }
            int count = groupSendTaskDAO.getGroupSendAttachments(queryFormUserDataArg.getMarketingActivityId(), queryFormUserDataArg.getEa());
            //如果是旧群发,走旧逻辑
            if(count>0) {
                //只有一条,走旧逻辑
                List<MarketingActivityObjectRelationEntity> relationEntities = marketingActivityObjectRelationDAO.queryByMarketingActivityId(queryFormUserDataArg.getEa(), queryFormUserDataArg.getMarketingActivityId());
                if(relationEntities.size()==1){
                    objectType = relationEntities.get(0).getObjectType();
                    objectId = relationEntities.get(0).getObjectId();
                    if(objectType==ObjectTypeEnum.FILE.getType() || objectType==ObjectTypeEnum.VIDEO.getType()){
                        return Result.newSuccess();
                    }
                    QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, queryFormUserDataArg.getEa());
                    if (queryFormUserContainer == null) {
                        return new Result<>(SHErrorCode.SUCCESS);
                    }
                    objectType = queryFormUserContainer.getObjectType();
                    objectId = queryFormUserContainer.getObjectId();
                }else {
                    //走新逻辑
                    objectType=ObjectTypeEnum.CUSTOMIZE_FORM.getType();
                }
            }else {
                QywxGroupSendTaskEntity qywxGroupSendTaskEntity = qywxGroupSendTaskDAO.queryById(marketingActivityExternalConfigEntity.getAssociateId(), queryFormUserDataArg.getEa());
                if (qywxGroupSendTaskEntity == null) {
                    log.warn("CustomizeFormDataServiceImpl.queryFormUserData qywxGroupSendTaskEntity is null arg:{}", queryFormUserDataArg);
                    return new Result<>(SHErrorCode.SYSTEM_ERROR);
                }
                objectType = qywxGroupSendTaskEntity.getObjectType();
                objectId = qywxGroupSendTaskEntity.getObjectId();
                QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, queryFormUserDataArg.getEa());
                if (queryFormUserContainer == null) {
                    return new Result<>(SHErrorCode.SUCCESS);
                }
                objectType = queryFormUserContainer.getObjectType();
                objectId = queryFormUserContainer.getObjectId();
            }
        } else if (queryFormUserDataArg.getType().equals(QueryFormUserDataTypeEnum.OBJECT.getType())) {
            QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, queryFormUserDataArg.getEa());
            if (queryFormUserContainer == null) {
                return new Result<>(SHErrorCode.SUCCESS);
            }
            objectId = queryFormUserContainer.getObjectId();
            objectType = queryFormUserContainer.getObjectType();
        }else if(queryFormUserDataArg.getType().equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType())){
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getByMarketingActivityIdAndAssociateIdType(queryFormUserDataArg.getEa(), queryFormUserDataArg.getMarketingActivityId(), AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType());
            if (marketingActivityExternalConfigEntity == null) {
                log.warn("CustomizeFormDataManager.getCustomizeFormDataUser marketingActivityExternalConfigEntity is null marketingActivityId:{}", queryFormUserDataArg.getMarketingActivityId());
                return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
            }
            NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId(), queryFormUserDataArg.getEa());
            if (noticeEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.queryFormUserData noticeEntity is null arg:{}", queryFormUserDataArg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            objectType = objectManager.convertNoticeContentTypeToObjectType(noticeEntity.getContentType());
            objectId = noticeEntity.getContent();
            QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, queryFormUserDataArg.getEa());
            if (queryFormUserContainer == null) {
                return new Result<>(SHErrorCode.SUCCESS);
            }
            objectType = queryFormUserContainer.getObjectType();
            objectId = queryFormUserContainer.getObjectId();
        }
        FormDataUserArgContainer formDataUserArgContainer = FormDataUserArgContainer.builder()
            .ea(queryFormUserDataArg.getEa())
            .objectId(objectId)
            .objectType(objectType)
            .marketingActivityId(queryFormUserDataArg.getMarketingActivityId())
            .marketingEventId(queryFormUserDataArg.getMarketingEventId())
            .type(queryFormUserDataArg.getType())
            .usage(queryFormUserDataArg.getFormUsage())
            .sourceType(queryFormUserDataArg.getSourceType())
            .pageNum(queryFormUserDataArg.getPageNum())
            .pageSize(queryFormUserDataArg.getPageSize())
            .needMarketingEventAndActivityDetail(queryFormUserDataArg.getNeedMarketingEventAndActivityDetail())
            .websiteId(queryFormUserDataArg.getWebsiteId())
            .build();
        CustomizeFormDataUserService customizeFormDataUserService = formDataUserManager
            .getCustomizeFormDataUserServiceByName(CustomizeFormDataUserServiceEnum.getActionName(objectType));
        //获取用户报名数据
        result = customizeFormDataUserService.getCustomizeFormDataUserByObject(formDataUserArgContainer);
        if (null != result && null != result.getResult()) {
            List<Integer> spreadFsUserList = result.getResult().stream().map(QueryFormUserDataResult::getSpreadFsUid).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Integer, String> outUserInfoByUpstreamAndUserId = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(queryFormUserDataArg.getEa(), spreadFsUserList);
            for (QueryFormUserDataResult queryFormUserDataResult : result.getResult()) {
                if(StringUtils.isBlank(queryFormUserDataResult.getSpreadUserName())){
                    queryFormUserDataResult.setSpreadUserName(outUserInfoByUpstreamAndUserId.get(queryFormUserDataResult.getSpreadFsUid()));
                }
            }
        }
        //线索id和用户报名数据组成的map
//        Map<String,QueryFormUserDataResult> leadIdAndQueryFormUserDataResultMap = result.getResult().stream()
//                .filter(val->StringUtils.isNotEmpty(val.getLeadId()))
//                .collect(
//                        Collectors.toMap(QueryFormUserDataResult::getLeadId,val->val,(v1,v2)->v1)
//                );
//        if (MapUtils.isNotEmpty(leadIdAndQueryFormUserDataResultMap)){
//            Set<String> leadIds = leadIdAndQueryFormUserDataResultMap.keySet();
//            //根据线索id查询线索对应的标签
//            List<ObjectDataIdAndTagNameListData>  objectDataIdAndTagNameListDataList = metadataTagManager.getObjectDataIdAndTagNameListDatasByObjectDataIds(arg.getEa(), LeadsFieldContants.API_NAME,Lists.newArrayList(leadIds));
//            //线索id和标签名称列表组成的map
//            Map<String, TagNameList> leadIdAndTagNameListDataMap = objectDataIdAndTagNameListDataList.stream()
//                    .collect(Collectors.toMap(ObjectDataIdAndTagNameListData::getDataId,ObjectDataIdAndTagNameListData::getTagNameList));
//            //填充线索标签
//            leadIdAndQueryFormUserDataResultMap.forEach((key,value)->{
//                value.setTagNameList(leadIdAndTagNameListDataMap.get(key));
//            });
//        }
        //修改之前使用java8 转map leadId相同,会替换对象问题 (7.4版本修复)
        Set<String> leadIds = result.getResult().stream().map(QueryFormUserDataResult::getLeadId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        //根据线索id查询线索对应的标签
        List<ObjectDataIdAndTagNameListData>  objectDataIdAndTagNameListDataList = metadataTagManager.getObjectDataIdAndTagNameListDatasByObjectDataIds(queryFormUserDataArg.getEa(), LeadsFieldContants.API_NAME,Lists.newArrayList(leadIds));
        //线索id和标签名称列表组成的map
        if (CollectionUtils.isNotEmpty(objectDataIdAndTagNameListDataList)) {
            Map<String, TagNameList> leadIdAndTagNameListDataMap = objectDataIdAndTagNameListDataList.stream()
                    .collect(Collectors.toMap(ObjectDataIdAndTagNameListData::getDataId,ObjectDataIdAndTagNameListData::getTagNameList));
            //填充线索标签
            result.getResult().forEach(queryFormUserDataResult -> {
                if (StringUtils.isNotBlank(queryFormUserDataResult.getLeadId()) && leadIdAndTagNameListDataMap.containsKey(queryFormUserDataResult.getLeadId())) {
                    queryFormUserDataResult.setTagNameList(leadIdAndTagNameListDataMap.get(queryFormUserDataResult.getLeadId()));
                }
            });
        }
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<PageResult<QueryFormUserDataResult>> queryFormUserDataForIds(queryFormUserDataForIdsArg arg) {
        PageResult<QueryFormUserDataResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        if (arg.getTime() == null) {
            arg.setTime(new Date().getTime());
        }
        pageResult.setTime(arg.getTime());
        List<CustomizeFormDataUserEntity> customizeFormIds = Lists.newArrayList();
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        if(arg!=null&&arg.getIds() != null){
            customizeFormIds = customizeFormDataUserDAO.getCustomizeFormIds(arg.getIds(), arg.getKeyword(),page,arg.getEa());
        }
        if(customizeFormIds.size()==0){
            return new Result<>(SHErrorCode.SUCCESS, pageResult);
        }

        List<QueryFormUserDataResult> result = getQueryFormUserDataResults(customizeFormIds,arg.getEa());

        pageResult.setResult(result);
        pageResult.setTotalCount(page.getTotalNum());
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    private List<QueryFormUserDataResult> getQueryFormUserDataResults(List<CustomizeFormDataUserEntity> customizeFormIds, String ea) {
        customizeFormDataManager.buildAreaInfoByEnrollData(customizeFormIds);
        // 转换sourceName与sourceType
        customizeFormDataManager.buildSourceNameAndType(customizeFormIds);
        // 转换utmMedium与source
//        customizeFormDataManager.buildUtmMediumAndSource(customizeFormIds, customizeFormIds.get(0).getEa());
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        if (turnOnPhoneNumberSensitive) {
            // 脱敏
            safetyManagementManager.phoneNumberSensitive(customizeFormIds);
        }
        // 报名者信息
        List<String> uidList = customizeFormIds.stream().filter(customizeFormDataUserEntity -> StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())).map(CustomizeFormDataUserEntity::getUid).collect(Collectors.toList());
        List<String> formIds = customizeFormIds.stream().filter(customizeFormDataUserEntity -> StringUtils.isNotBlank(customizeFormDataUserEntity.getFormId())).map(CustomizeFormDataUserEntity::getFormId).collect(Collectors.toList());
        List<CustomizeFormDataEntity> customizeFormDataEntities = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds,ea);
        Map<String, CustomizeFormDataEntity> formDataEntityMap = customizeFormDataEntities.stream().collect(Collectors.toMap(CustomizeFormDataEntity::getId, k -> k));
        Map<String, BaseUserInfoResult> basicInfoMap = kmUserManager.batchGetBaseUserInfo(uidList,ea);
        List<QueryFormUserDataResult> result = customizeFormIds.stream().map(customizeFormDataUserEntity -> {
            QueryFormUserDataResult copy = BeanUtil.copy(customizeFormDataUserEntity, QueryFormUserDataResult.class);
            copy.setEnrollUserName(customizeFormDataUserEntity.getParamValueMap().get("enroll.name"));
            if(StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())){
                copy.setEnrollUserAvatar(StringUtils.isNotEmpty(basicInfoMap.get(customizeFormDataUserEntity.getUid()).getAvatar())? basicInfoMap.get(customizeFormDataUserEntity.getUid()).getAvatar():null);
            }
            CustomizeFormDataEntity customizeFormDataById = customizeFormDataDAO.getCustomizeFormDataById(copy.getFormId(),ea);
            copy.setFormBodySetting(customizeFormDataById.getFormBodySetting());
            Map<String, Object> enrollResult = customizeFormDataManager.generateEnrollData(customizeFormDataUserEntity, customizeFormDataById.getFormBodySetting(), null, true);
            copy.setSubmitContentResult(enrollResult);
            if (formDataEntityMap.containsKey(customizeFormDataUserEntity.getFormId())) {
                CustomizeFormDataEntity customizeFormDataEntity = formDataEntityMap.get(customizeFormDataUserEntity.getFormId());
                if (Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(),SaveCrmObjectTypeEnum.OBJ.getType())) {
                    copy.setApiName(customizeFormDataEntity.getCrmApiName());
                    copy.setExtraDataId(customizeFormDataUserEntity.getExtraDataId());
                }
            }
            return copy;
        }).collect(Collectors.toList());
        return result;
    }

    private Result<List<QueryFormUserDataResult>> queryFormUserDataForEmployees(QueryFormUserDataArg arg) {
        if(arg.getFormUsage() == null){
            arg.setFormUsage(FormDataUsage.COLLECT_LEADS.getUsage());
        }
        String objectId = arg.getObjectId();
        Integer objectType = arg.getObjectType();
        if (arg.getType().equals(QueryFormUserDataTypeEnum.MARKETING_ACTIVITY.getType())) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getByMarketingActivityIdAndAssociateIdType(arg.getEa(), arg.getMarketingActivityId(), AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType());
            if (marketingActivityExternalConfigEntity == null) {
                log.warn("CustomizeFormDataManager.getCustomizeFormDataUser marketingActivityExternalConfigEntity is null marketingActivityId:{}", arg.getMarketingActivityId());
                return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
            }
            NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId(), arg.getEa());
            if (noticeEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.queryFormUserData noticeEntity is null arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            objectType = objectManager.convertNoticeContentTypeToObjectType(noticeEntity.getContentType());
            objectId = noticeEntity.getContent();
            QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, arg.getEa());
            if (queryFormUserContainer == null) {
                return new Result<>(SHErrorCode.SUCCESS);
            }
            objectType = queryFormUserContainer.getObjectType();
            objectId = queryFormUserContainer.getObjectId();
        } else if (arg.getType().equals(QueryFormUserDataTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType())) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getByMarketingActivityIdAndAssociateIdType(arg.getEa(), arg.getMarketingActivityId(), AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType());
            if (marketingActivityExternalConfigEntity == null) {
                log.warn("CustomizeFormDataManager.getCustomizeFormDataUser marketingActivityExternalConfigEntity is null marketingActivityId:{}", arg.getMarketingActivityId());
                return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
            }
            QywxGroupSendTaskEntity qywxGroupSendTaskEntity = qywxGroupSendTaskDAO.queryById(marketingActivityExternalConfigEntity.getAssociateId(), arg.getEa());
            if (qywxGroupSendTaskEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.queryFormUserData qywxGroupSendTaskEntity is null arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            objectType = qywxGroupSendTaskEntity.getObjectType();
            objectId = qywxGroupSendTaskEntity.getObjectId();
            QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, arg.getEa());
            if (queryFormUserContainer == null) {
                return new Result<>(SHErrorCode.SUCCESS);
            }
            objectType = queryFormUserContainer.getObjectType();
            objectId = queryFormUserContainer.getObjectId();
        } else if (arg.getType().equals(QueryFormUserDataTypeEnum.OBJECT.getType())) {
            QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, arg.getEa());
            if (queryFormUserContainer == null) {
                return new Result<>(SHErrorCode.SUCCESS);
            }
            objectId = queryFormUserContainer.getObjectId();
            objectType = queryFormUserContainer.getObjectType();
        }else if(arg.getType().equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType())){
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getByMarketingActivityIdAndAssociateIdType(arg.getEa(), arg.getMarketingActivityId(), AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType());
            if (marketingActivityExternalConfigEntity == null) {
                log.warn("CustomizeFormDataManager.getCustomizeFormDataUser marketingActivityExternalConfigEntity is null marketingActivityId:{}", arg.getMarketingActivityId());
                return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
            }
            NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId(), arg.getEa());
            if (noticeEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.queryFormUserData noticeEntity is null arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            objectType = objectManager.convertNoticeContentTypeToObjectType(noticeEntity.getContentType());
            objectId = noticeEntity.getContent();
            QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, arg.getEa());
            if (queryFormUserContainer == null) {
                return new Result<>(SHErrorCode.SUCCESS);
            }
            objectType = queryFormUserContainer.getObjectType();
            objectId = queryFormUserContainer.getObjectId();
        }
        List<CustomizeFormDataUserEntity> liveEmployeesCustomizeFormDataUser;
        if (arg.getStartDate() == null || arg.getEndDate() == null) {
            liveEmployeesCustomizeFormDataUser = customizeFormDataUserDAO.getEmployeesCustomizeFormDataUser(arg.getMarketingActivityId(), objectId, objectType, arg.getFsUserId(), arg.getKeyword(), null,null,null,false, arg.getEa());
        } else {
            liveEmployeesCustomizeFormDataUser = customizeFormDataUserDAO.getEmployeesCustomizeFormDataUser(arg.getMarketingActivityId(), objectId, objectType, arg.getFsUserId(), arg.getKeyword(), null,new Date(arg.getStartDate()),new Date(arg.getEndDate()),true, arg.getEa());
        }
        if(liveEmployeesCustomizeFormDataUser.size()==0){
            return Result.newSuccess();
        }
        // 报名者信息
        List<String> uidList = liveEmployeesCustomizeFormDataUser.stream().filter(customizeFormDataUserEntity -> StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())).map(CustomizeFormDataUserEntity::getUid).collect(Collectors.toList());
        Map<String, BaseUserInfoResult> basicInfoMap = kmUserManager.batchGetBaseUserInfo(uidList,arg.getEa());
        List<QueryFormUserDataResult> result = liveEmployeesCustomizeFormDataUser.stream().map(customizeFormDataUserEntity -> {
            QueryFormUserDataResult copy = BeanUtil.copy(customizeFormDataUserEntity, QueryFormUserDataResult.class);
            copy.setEnrollUserName(customizeFormDataUserEntity.getParamValueMap().get("enroll.name"));
            if(StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())){
                copy.setEnrollUserAvatar(StringUtils.isNotEmpty(basicInfoMap.get(customizeFormDataUserEntity.getUid()).getAvatar())? basicInfoMap.get(customizeFormDataUserEntity.getUid()).getAvatar():null);
            }
            return copy;
        }).collect(Collectors.toList());
        return Result.newSuccess(result);
    }
    private QueryFormUserContainer buildQueryFormUserContainerData (String objectId, Integer objectType, String ea) {
        QueryFormUserContainer queryFormUserContainer = new QueryFormUserContainer();
        if (objectId == null || objectType == null) {
            return null;
        }

        if (ObjectTypeEnum.HEXAGON_SITE.getType() == objectType) {
            List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(objectId), ea);
            if (CollectionUtils.isEmpty(hexagonSiteListDTOList)) {
                log.warn("CustomizeFormDataManager.buildQueryFormUserContainerData hexagonSiteListDTOList is null objectId:{}, objectType:{}", objectId, objectType);
                return null;
            }
            objectType = ObjectTypeEnum.HEXAGON_PAGE.getType();
            objectId = hexagonSiteListDTOList.get(0).getHexagonPageId();
        }
        queryFormUserContainer.setObjectId(objectId);
        queryFormUserContainer.setObjectType(objectType);
        return queryFormUserContainer;
    }

    @Override
    public Result<ExportEnrollsDataResult> exportEnrollsData(ExportEnrollsDataArg exportEnrollsDataArg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            if(exportEnrollsDataArg.getFormUsage() == null){
                exportEnrollsDataArg.setFormUsage(FormDataUsage.COLLECT_LEADS.getUsage());
            }
            String objectId = exportEnrollsDataArg.getObjectId();
            Integer objectType = exportEnrollsDataArg.getObjectType();
            if (exportEnrollsDataArg.getType().equals(QueryFormUserDataTypeEnum.MARKETING_ACTIVITY.getType())) {
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                        .getByMarketingActivityIdAndAssociateIdTypeList(exportEnrollsDataArg.getEa(), exportEnrollsDataArg.getMarketingActivityId(), Lists.newArrayList(AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType(), AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType()));
                if (marketingActivityExternalConfigEntity == null) {
                    log.warn("CustomizeFormDataManager.exportEnrollsData marketingActivityExternalConfigEntity is null marketingActivityId:{}", exportEnrollsDataArg.getMarketingActivityId());
                    return ;
                }
                NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId(), exportEnrollsDataArg.getEa());
                if (noticeEntity == null) {
                    log.warn("CustomizeFormDataServiceImpl.queryFormUserData noticeEntity is null arg:{}", exportEnrollsDataArg);
                    return ;
                }
                objectType = objectManager.convertNoticeContentTypeToObjectType(noticeEntity.getContentType());
                objectId = noticeEntity.getContent();
                QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, exportEnrollsDataArg.getEa());
                if (queryFormUserContainer == null) {
                    return ;
                }
                objectType = queryFormUserContainer.getObjectType();
                objectId = queryFormUserContainer.getObjectId();
            } else if (exportEnrollsDataArg.getType().equals(QueryFormUserDataTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType())) {
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                        .getByMarketingActivityIdAndAssociateIdType(exportEnrollsDataArg.getEa(), exportEnrollsDataArg.getMarketingActivityId(), AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType());
                if (marketingActivityExternalConfigEntity == null) {
                    log.warn("CustomizeFormDataManager.exportEnrollsData marketingActivityExternalConfigEntity is null marketingActivityId:{}", exportEnrollsDataArg.getMarketingActivityId());
                    return ;
                }
                int count = groupSendTaskDAO.getGroupSendAttachments(exportEnrollsDataArg.getMarketingActivityId(), exportEnrollsDataArg.getEa());
                //如果是旧群发,走旧逻辑
                if(count>0) {
                    //只有一条,走旧逻辑
                    List<MarketingActivityObjectRelationEntity> relationEntities = marketingActivityObjectRelationDAO.queryByMarketingActivityId(exportEnrollsDataArg.getEa(), exportEnrollsDataArg.getMarketingActivityId());
                    if(relationEntities.size()==1){
                        objectType = relationEntities.get(0).getObjectType();
                        objectId = relationEntities.get(0).getObjectId();
                        QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, exportEnrollsDataArg.getEa());
                        if (queryFormUserContainer == null) {
                            return ;
                        }
                        objectType = queryFormUserContainer.getObjectType();
                        objectId = queryFormUserContainer.getObjectId();
                    }else {
                        //走新逻辑
                        objectType=ObjectTypeEnum.CUSTOMIZE_FORM.getType();
                    }
                }else {
                    QywxGroupSendTaskEntity qywxGroupSendTaskEntity = qywxGroupSendTaskDAO.queryById(marketingActivityExternalConfigEntity.getAssociateId(), exportEnrollsDataArg.getEa());
                    if (qywxGroupSendTaskEntity == null) {
                        log.warn("CustomizeFormDataServiceImpl.exportEnrollsData qywxGroupSendTaskEntity is null arg:{}", exportEnrollsDataArg);
                        return ;
                    }
                    objectType = qywxGroupSendTaskEntity.getObjectType();
                    objectId = qywxGroupSendTaskEntity.getObjectId();
                    QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, exportEnrollsDataArg.getEa());
                    if (queryFormUserContainer == null) {
                        return ;
                    }
                    objectType = queryFormUserContainer.getObjectType();
                    objectId = queryFormUserContainer.getObjectId();
                }
            } else if (exportEnrollsDataArg.getType().equals(QueryFormUserDataTypeEnum.OBJECT.getType())) {
                QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, exportEnrollsDataArg.getEa());
                if (queryFormUserContainer == null) {
                    return ;
                }
                objectType = queryFormUserContainer.getObjectType();
                objectId = queryFormUserContainer.getObjectId();
            }else if(exportEnrollsDataArg.getType().equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType())){
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                        .getByMarketingActivityIdAndAssociateIdType(exportEnrollsDataArg.getEa(), exportEnrollsDataArg.getMarketingActivityId(), AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType());
                if (marketingActivityExternalConfigEntity == null) {
                    log.warn("CustomizeFormDataManager.exportEnrollsData marketingActivityExternalConfigEntity is null marketingActivityId:{}", exportEnrollsDataArg.getMarketingActivityId());
                    return ;
                }
                NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId(),exportEnrollsDataArg.getEa());
                if (noticeEntity == null) {
                    log.warn("CustomizeFormDataServiceImpl.queryFormUserData noticeEntity is null arg:{}", exportEnrollsDataArg);
                    return ;
                }
                objectType = objectManager.convertNoticeContentTypeToObjectType(noticeEntity.getContentType());
                objectId = noticeEntity.getContent();
                QueryFormUserContainer queryFormUserContainer = buildQueryFormUserContainerData(objectId, objectType, exportEnrollsDataArg.getEa());
                if (queryFormUserContainer == null) {
                    return ;
                }
                objectType = queryFormUserContainer.getObjectType();
                objectId = queryFormUserContainer.getObjectId();
                if (ObjectTypeEnum.QR_POSTER.getType() == objectType) {
                    QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(objectId,exportEnrollsDataArg.getEa());
                    if (qrPosterEntity != null) {
                        ObjectTypeEnum objectTypeEnum = QRPosterForwardTypeEnum.toObjectTypeEnum(qrPosterEntity.getForwardType());
                        if (objectTypeEnum != null) {
                            objectId = qrPosterEntity.getTargetId();
                            objectType = objectTypeEnum.getType();
                        }
                    }
                }
            }
            ExportEnrollsDataResult result =  customizeFormDataManager.buildExportEnrollsData(exportEnrollsDataArg.getEa(), objectId, objectType, exportEnrollsDataArg.getMarketingActivityId(), exportEnrollsDataArg.getMarketingEventId(), exportEnrollsDataArg.getType(), exportEnrollsDataArg.getFormUsage(), exportEnrollsDataArg.getSourceType());
            if(result == null) {
                log.warn("CustomizeFormDataServiceImpl.exportEnrollsData error");
                return ;
            }

            String filename = result.getFileName() + I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_1632) + ".xlsx";
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            ExcelUtil.fillContent(xssfSheet, result.getTitleList(), result.getEnrollInfoList());
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, filename, exportEnrollsDataArg.getEa(), exportEnrollsDataArg.getFsUserId());
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<CustomizeFormDataShowSettingResult> customizeFormDataShowSetting(CustomizeFormDataShowSettingArg arg) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(), arg.getEa());
        Result checkResult = customizeFormDataManager.checkCustomizeFormDataStatus(customizeFormDataEntity, arg.getFormId(), false);
        if(!checkResult.isSuccess()) {
            return checkResult;
        }
        CustomizeFormDataShowSettingResult result = new CustomizeFormDataShowSettingResult();
        //校验会议营销是否开启报名审核,设置审核状态
        customizeFormDataManager.checkShowSettingConferenceEnrollReview(customizeFormDataEntity.getEa(),arg.getMarketingEventId(),arg.getObjectId(),arg.getObjectType(),
                arg.getFormId(),arg.getWxAppId(),arg.getOpenId(),arg.getFingerPrint(),null,result);
        // 校验是否是会员
        //Boolean checkMember = customizeFormDataEntity.getFormMoreSetting().getCheckMember();
        Integer memberCheckType = customizeFormDataEntity.getFormMoreSetting().getMemberCheckType();
        result.setMemberCheckType(memberCheckType);
        result.setMemberToFormMapping(customizeFormDataEntity.getMemberToFormMapping());
        if (memberCheckType != null && (MemberCheckTypeEnum.CHECK.getType() == memberCheckType || MemberCheckTypeEnum.CHECK_FILL.getType() == memberCheckType)) {
            if (StringUtils.isNotBlank(arg.getOpenId()) && StringUtils.isNotBlank(arg.getWxAppId())) {
                Result<MemberCheckResult> memberResult = memberService.checkWxServiceUserHaveMemberResultAuth(arg.getObjectType(), arg.getObjectId(), arg.getWxAppId(), arg.getOpenId(), false);
                if (memberResult.isSuccess() && memberResult.getData() != null) {
                    MemberCheckResult memberResultData = memberResult.getData();
                    if (MemberCheckTypeEnum.CHECK.getType() == memberCheckType) {
                        // 判断是否需要自动执行一键报名
                        result.setNeedMemberEnroll(memberManager.memberNeedMemberEnroll(customizeFormDataEntity.getEa(), arg.getMarketingEventId(), memberResultData.getId()));
                        result.setShowResultPage(true);
                        return new Result<>(SHErrorCode.SUCCESS, result);
                    }
                    result.setMemberCheckResult(memberResultData);
                }
            } else if (StringUtils.isNotBlank(arg.getFingerPrint())) {
                Result<MemberCheckResult> memberResult = memberService.checkH5UserHaveMemberResultAuth(arg.getObjectType(), arg.getObjectId(), arg.getAllMemberCookieInfos(), false);
                if (memberResult.isSuccess() && memberResult.getData() != null) {
                    MemberCheckResult memberResultData = memberResult.getData();
                    if (MemberCheckTypeEnum.CHECK.getType() == memberCheckType) {
                        // 判断是否需要自动执行一键报名
                        result.setNeedMemberEnroll(memberManager.memberNeedMemberEnroll(customizeFormDataEntity.getEa(), arg.getMarketingEventId(), memberResultData.getId()));
                        result.setShowResultPage(true);
                        return new Result<>(SHErrorCode.SUCCESS, result);
                    }
                    result.setMemberCheckResult(memberResultData);
                }
            }
        }
        // 是否只能报名一次
        boolean fillInOnce = customizeFormDataEntity.getFormMoreSetting().isFillInOnce();
        result.setShowResultPage(false);
        // 若是会议或会议邀请函则只能报名一次
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = null;
        UserMarketingAccountEntity userMarketingAccountEntity = null;
        boolean customizeObject = StringUtils.isNotBlank(customizeFormDataEntity.getCrmApiName()) && !Objects.equals(customizeFormDataEntity.getCrmApiName(),CrmObjectApiNameEnum.CRM_LEAD.getName());
        List<String> linkList = new ArrayList<>();
        if ((arg.getObjectType() == ObjectTypeEnum.ACTIVITY.getType() || arg.getObjectType() == ObjectTypeEnum.ACTIVITY_INVITATION.getType()) && fillInOnce) {
            linkList = activityManager.getActivityLinkObject(arg.getObjectId(), arg.getObjectType(), Lists.newArrayList(ObjectTypeEnum.ACTIVITY_INVITATION), customizeFormDataEntity.getEa());
        }
        if (fillInOnce) {
            if (StringUtils.isNotBlank(arg.getOpenId()) && StringUtils.isNotBlank(arg.getWxAppId())) {
                // 根据微信公众号身份
                userMarketingAccountEntity = userMarketingWxServiceAccountRelationDao.getMarketingUserAccountByEaAndWxAppIdAndWxOpenId(customizeFormDataEntity.getEa(), arg.getWxAppId(), arg.getOpenId());
                //兼容自定义对象查询
                if (customizeObject) {
                    if (Objects.equals(ObjectTypeEnum.HEXAGON_SITE.getType(),arg.getObjectType())) {
                        List<HexagonPageEntity> hexagonPageEntities = hexagonPageDAO.getByHexagonSiteId(arg.getEa(), arg.getObjectId());
                        if (CollectionUtils.isNotEmpty(hexagonPageEntities)) {
                            List<String> formIds = hexagonPageEntities.stream().map(HexagonPageEntity::getFormId).collect(Collectors.toList());
                            customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeDataUserByEaAndMarketingEventIdAndFormIds(customizeFormDataEntity.getEa(), arg.getMarketingEventId(),null,null,arg.getWxAppId(),arg.getOpenId(),formIds);
                        }
                    } else {
                        customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByObjectIdAndUserInfo(customizeFormDataEntity.getEa(), null,null,arg.getWxAppId(),arg.getOpenId(), arg.getObjectId(), arg.getObjectType(), arg.getMarketingEventId());
                    }
                }
            } else if (StringUtils.isNotBlank(arg.getFingerPrint())) {
                // 根据浏览器身份
                userMarketingAccountEntity = userMarketingBrowserUserRelationDao.getMarketingUserAccountByEaAndBrowserUserId(customizeFormDataEntity.getEa(), arg.getFingerPrint());
                //兼容自定义对象查询
                if (customizeObject) {
                    if (Objects.equals(ObjectTypeEnum.HEXAGON_SITE.getType(),arg.getObjectType())) {
                        List<HexagonPageEntity> hexagonPageEntities = hexagonPageDAO.getByHexagonSiteId(arg.getEa(), arg.getObjectId());
                        if (CollectionUtils.isNotEmpty(hexagonPageEntities)) {
                            List<String> formIds = hexagonPageEntities.stream().map(HexagonPageEntity::getFormId).collect(Collectors.toList());
                            customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeDataUserByEaAndMarketingEventIdAndFormIds(customizeFormDataEntity.getEa(), arg.getMarketingEventId(),arg.getFingerPrint(),null,null,null,formIds);
                        }
                    } else {
                        customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByObjectIdAndUserInfo(customizeFormDataEntity.getEa(), arg.getFingerPrint(),null,null,null, arg.getObjectId(), arg.getObjectType(), arg.getMarketingEventId());
                    }
                }
            } else if (StringUtils.isNotBlank(arg.getEnrollUserEa()) && arg.getEnrollUserFsUid() != null) {
                customizeFormDataUserEntityList = customizeFormDataUserDAO
                    .queryCustomizeFormDataUsersByFsUserInfoAndObjectIds(arg.getMarketingEventId(), arg.getEnrollUserEa(), arg.getEnrollUserFsUid(), arg.getFormId(), linkList, null,arg.getEa());
            }
        }
        if (customizeObject && CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
            result.setShowResultPage(true);
            result.setEnrollId(customizeFormDataUserEntityList.get(0).getId());
            return new Result<>(SHErrorCode.SUCCESS, result);
        }
        if (userMarketingAccountEntity != null) {
            String userMarketingId = userMarketingAccountEntity.getId();
            List<UserMarketingCrmLeadAccountRelationEntity> userMarketingCrmLeadAccountRelationEntities = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(customizeFormDataEntity.getEa(), Lists.newArrayList(userMarketingId));
            List<String> crmLeadIds = userMarketingCrmLeadAccountRelationEntities.stream().map(UserMarketingCrmLeadAccountRelationEntity::getCrmLeadId).collect(Collectors.toList());
            if (!crmLeadIds.isEmpty()) {
                //根据用户的身份信息和物料id得到用户提交的表单信息
                List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.queryCustomizeFormDataUsersByCrmLeadIds(crmLeadIds, arg.getMarketingEventId(), customizeFormDataEntity.getId(), linkList,arg.getEa());
                if (customizeFormDataUserEntities != null && !customizeFormDataUserEntities.isEmpty()) {
                    customizeFormDataUserEntityList = customizeFormDataUserEntities;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
            result.setShowResultPage(true);
            result.setEnrollId(customizeFormDataUserEntityList.get(0).getId());
        }
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<CustomizeFormDataEnrollResult> wxCustomizeFormDataEnroll(CustomizeFormDataEnrollArg arg) {
        CustomizeFormDataEnrollResult customizeFormDataEnrollResult = new CustomizeFormDataEnrollResult();
        // 校验物料合法性
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isBlank(ea)) {
            log.warn("CustomizeFormDataServiceImpl.customizeFormDataEnroll ea is null, arg:{}", arg);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_BINDING_OBJECT_NOT_FOUND);
        }
        arg.setEa(ea);
        //文件tnpath转path
        changeTNpathFileToNpath(arg.getEa(), arg.getSubmitContent().getFileAttachmentMap());
        //如果有来源营销身份,获取是否是会员
        if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
            Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                    .getBaseInfosByIds(arg.getEa(), 1000, Lists.newArrayList(arg.getFromUserMarketingId()), InfoStateEnum.DETAIL);
            UserMarketingAccountData marketingAccountData = userMarketingAccountDataMap.get(arg.getFromUserMarketingId());
            if(marketingAccountData!=null&& CollectionUtils.isNotEmpty(marketingAccountData.getCrmMemberInfos())){
                arg.setSpreadUserIdentifyId(marketingAccountData.getCrmMemberInfos().get(0).getId());
                arg.setSpreadUserType(ChannelEnum.CRM_MEMBER.getType());
            }
        }
        //查询是否是会议营销,并处理审核状态
        customizeFormDataManager.checkConferenceEnrollReview(ea,arg.getMarketingEventId(),customizeFormDataEnrollResult);
        // 查询表单数据
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(),ea);
        Result checkResult = customizeFormDataManager.checkCustomizeFormDataStatus(customizeFormDataEntity, arg.getFormId(), false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        Result enrollFieldCheckResult = customizeFormDataManager.checkEnrollField(customizeFormDataEntity, arg.getSubmitContent(), arg.getObjectType(), arg.getObjectId(), arg.getStepFormComponentId());
        if (!enrollFieldCheckResult.isSuccess()) {
            return enrollFieldCheckResult;
        }
        if (!Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) && !Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())){
            boolean phoneVerified = verificationCodeManager.checkSMCode(arg.getSubmitContent().getPhone(), arg.getSubmitContent().getPhoneVerifyCode()).isSuccess();
            if (!phoneVerified){
                return Result.newError(SHErrorCode.PHONE_VERIFY_CODE_ERROR);
            }
        }
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == arg.getObjectType()){
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            if (memberConfig != null) {
                HexagonPageEntity hexagonPage = hexagonPageDAO.getById(ea, arg.getObjectId());
                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getRegistrationSiteId())){
                    return doWxServiceUserMemberRegister(ea, arg);
                }
                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getLoginSiteId())){
                    return doWxServiceUserMemberLogin(ea, arg);
                }
                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getUpdateInfoSiteId())) {
                    return doWxServiceUserMemberUpdateInfo(ea, arg);
                }
            }
        }
        //文件tnpath转path
        changeTNpathFileToNpath(arg.getEa(), arg.getSubmitContent().getFileAttachmentMap());
        // 特殊数据校验
        if (StringUtils.isBlank(arg.getMarketingEventId())) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
            if (externalConfigEntity != null) {
                arg.setMarketingEventId(externalConfigEntity.getMarketingEventId());
            }
        }
        Result objectCheckResult = this.checkObjectLegitimacy(ea, arg.getMarketingEventId(), arg.getObjectId(), arg.getObjectType());
        if (!objectCheckResult.isSuccess()) {
            return objectCheckResult;
        }
        // 转换userId
        if(!arg.isPartner()){
            arg.setSpreadFsUid(qywxUserManager.convertOldUserId(ea, arg.getSpreadFsUid()));
        }
        Boolean fillInOnce = customizeFormDataEntity.getFormMoreSetting().isFillInOnce();
        Boolean synchronousCRM = customizeFormDataEntity.getFormMoreSetting().isSynchronousCRM();
        Boolean enrollLimit = customizeFormDataEntity.getFormMoreSetting().isEnrollLimit();
        if (StringUtils.isBlank(arg.getEnrollId())) {
            if(fillInOnce) {
                Result<CustomizeFormDataUserEntity> enrollCheck = customizeFormDataManager.checkUserIsEnrolledWithNoHandleActivity(arg.getMarketingEventId(), customizeFormDataEntity, arg.getObjectId(), arg.getObjectType(), arg.getWxAppId(), arg.getOpenId(), null, null, null, arg.getSubmitContent().getPhone());
                if (!enrollCheck.isSuccess()) {
                    CustomizeFormDataUserEntity customizeFormDataUserEntity = enrollCheck.getData();
                    if (customizeFormDataUserEntity != null) {
                        customizeFormDataEnrollResult.setEnrollId(customizeFormDataUserEntity.getId());
                        ThreadPoolUtils.execute(() -> {
                            Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
                            Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getWxAppId()));
                            Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getOpenId()));
                            AssociationArg associationArg = new AssociationArg();
                            associationArg.setEa(ea);
                            associationArg.setPhone(arg.getSubmitContent().getPhone());
                            associationArg.setEmail(arg.getSubmitContent().getEmail());
                            associationArg.setWxAppId(arg.getWxAppId());
                            associationArg.setAssociationId(arg.getOpenId());
                            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
                            associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
                            associationArg.setEmail(arg.getSubmitContent().getEmail());
                            associationArg.setTriggerAction("wxCustomizeFormDataEnroll");
                            try {
                                userMarketingAccountAssociationManager.associate(associationArg);
                            } catch (Exception e) {
                                log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll error arg:{}", JSON.toJSONString(associationArg), e);
                            }
                        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
                    }
                    return new Result<>(SHErrorCode.USERS_HAVE_REGISTERED, customizeFormDataEnrollResult);
                }
            }
            if (enrollLimit) { // 是否开启报名限制
                long countResult = customizeFormDataUserDAO.countCustomizeFormDataUserByFormId(customizeFormDataEntity.getId(), customizeFormDataEntity.getEa());
                if (countResult >= customizeFormDataEntity.getFormMoreSetting().getEnrollLimitNum()) {
                    return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_FULL.getErrorCode(), customizeFormDataEntity.getFormMoreSetting().getEnrollLimitText());
                }
            }
        }

        String crmMemberId = null;
        if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER && !customizeFormDataEntity.getFormMoreSetting().isSyncToMember()) {
            Result<String> memberResult = fsPayOrderManager.getMemberResult(arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount(), arg.getObjectType(), arg.getObjectId(),
                    arg.getOpenId(), arg.getWxAppId(),
                    arg.getFingerPrint(), arg.getAllEnterpriseMemberCookieMap(),
                    null);
            // 获取会员ID
            if (memberResult.isSuccess() && StringUtils.isNotBlank(memberResult.getData())) {
                crmMemberId = memberResult.getData();
            } else {
                return Result.newError(memberResult.getErrCode(), memberResult.getErrMsg());
            }
        }

        // 插入数据
        String customizeFormDataUserId = UUIDUtil.getUUID();
        buildMarketingEventIdByEnrollData(arg);
        CustomizeFormDataUserEntity customizeFormDataUserEntity = BeanUtil.copy(arg, CustomizeFormDataUserEntity.class);
        // 创建营销推广来源对象
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
        customizeFormDataUserEntity.setEa(ea);
        if (StringUtils.isBlank(arg.getEnrollId())) {
            if(arg.isPartner()){
                customizeFormDataUserEntity.setOutTenantId(arg.getOuterTenantId());
                customizeFormDataUserEntity.setOutUid(String.valueOf(arg.getOuterUid()));
            }
            customizeFormDataUserEntity.setId(customizeFormDataUserId);
            customizeFormDataUserEntity.setUid("");
            customizeFormDataUserEntity.setSourceType(customizeFormDataManager.getEnrollType(customizeFormDataUserEntity));
            customizeFormDataUserEntity.setChannelValue(customizeFormDataManager.getSystemPromotionChannelType(customizeFormDataUserEntity));
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceType(customizeFormDataManager.getMarketingSourceType(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceName(customizeFormDataManager.getMarketingSourceName(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            //判断是否有映射crm字段
            if (!synchronousCRM) {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_1908));
            }
            boolean saveResult = customizeFormDataUserDAO.insertCustomizeFormDataUser(customizeFormDataUserEntity) == 1;
            if (!saveResult) {
                log.warn("CustomizeFormDataServiceImpl.wxCustomizeFormDataEnroll insertCustomizeFormDataUser error customizeFormDataUserEntity:{}", customizeFormDataUserEntity);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            customizeFormDataEnrollResult.setEnrollId(customizeFormDataUserId);
            //上报神策埋点-表单提交
            marketingStatLogPersistorManger.sendCustomizeFormDataUserData(ea, customizeFormDataUserEntity.getId(), arg.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_OFFICIAL_ACCOUNTS);
        } else {
            // 更新数据
            customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getEnrollId(), arg.getEa());
            if (customizeFormDataUserEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.wxCustomizeFormDataEnroll oldData is null enrollId:{}", arg.getEnrollId());
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            BeanUtil.appendCopyCustomizeFormDataEnrollIgnoreNull(arg.getSubmitContent(), customizeFormDataUserEntity.getSubmitContent());
            // 更新报名数据
            customizeFormDataUserDAO.updateCustomizeFormDataEnrollDataById(customizeFormDataUserEntity);
            customizeFormDataEnrollResult.setEnrollId(arg.getEnrollId());
        }
        // 特殊物料逻辑处理
        CustomizeFormDataUserEntity finalCustomizeFormDataUserEntity = customizeFormDataUserEntity;
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                customizeFormDataManager.handlerSpecialTypeEnrollData(finalCustomizeFormDataUserEntity);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        //单独处理存入其他任意对象
        if (Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(), SaveCrmObjectTypeEnum.OBJ.getType())) {
            CustomizeFormDataUserEntity finalSaveCustomizeFormDataUserEntity = customizeFormDataUserEntity;
            ThreadPoolUtils.executeWithTraceContext(()->{
                boolean updateData = !StringUtils.isEmpty(finalSaveCustomizeFormDataUserEntity.getExtraDataId());
                customizeFormDataManager.saveObjectToCrm(customizeFormDataEntity, finalSaveCustomizeFormDataUserEntity,updateData);
//                if(StringUtils.isNotBlank(arg.getCtaId())) {
//                    ctaService.recordCustomerDataEnroll(MarketingUserActionChannelType.WECHAT_SERVICE, arg);
//                }
            },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
           return new Result<>(SHErrorCode.SUCCESS, customizeFormDataEnrollResult);
        }
        // 处理保利威直播无需报名可看直播
        ThreadPoolUtils.execute(() -> {
            polyvManager.handlePolyvUser(arg.getMarketingEventId(), arg.getSubmitContent(),ea);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        // 将数据同步至CRM线索
        if (synchronousCRM) {
            try {
                if (StringUtils.isBlank(customizeFormDataUserEntity.getLeadId())) {
                    boolean success = customizeFormDataManager.saveEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                    if (success && customizeFormDataEntity.getFormMoreSetting().isSyncToMember()){
                        if (!memberManager.getWxServiceUserBindMemberId(ea, arg.getWxAppId(), arg.getOpenId()).isPresent()){
                            MemberConfigEntity memberConfig = memberManager.tryInitMemberConfig(ea);
                            CustomizeFormDataUserEntity dbData = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserId, ea);
                            Optional<String> optionalMember = memberManager.saveLeadToMemberAndSpreadData(ea, dbData,null);
                            optionalMember.ifPresent(memberId -> wxServiceUserMemberBindDao.insertIgnore(ea, arg.getWxAppId(), arg.getOpenId(), memberId));
                            if (optionalMember.isPresent()) {
                                if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER) {
                                    Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createFsPayOrder(
                                            customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(),
                                            customizeFormDataEntity.getCreateBy(), crmMemberId, arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
                                    if (!createPayOrderResult.isSuccess()) {
                                        return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
                                    }
                                    CreateFsPayOrderResult data = createPayOrderResult.getData();
                                    BeanUtils.copyProperties(data, customizeFormDataEnrollResult);
                                    customizeFormDataUserEntity.setPayOrderId(data.getOrderNo());
                                    customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(data.getOrderNo(), customizeFormDataUserEntity.getId(), ea);
                                }
                            }
                        }
                    }
                    if (success){
                        marketingStatLogPersistorManger.sendCustomizeFormDataUserData(ea, customizeFormDataUserEntity.getId(), arg.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_OFFICIAL_ACCOUNTS);
                    }
                } else {
                    // 更新crm数据
                    customizeFormDataManager.updateEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                }
            } catch (Exception e) {
                log.warn("CustomizeFormDataServiceImpl.wxCustomizeFormDataEnroll error e:{}", e);
            }
        }
        CustomizeFormDataUserEntity sendNoticeEntity = customizeFormDataUserEntity;
        ThreadPoolUtils.execute(() -> {
            // 存入线索表成功但缺失crm映射字段无法同步需要发送失败通知
            customizeFormDataManager.sendNotice(ea, sendNoticeEntity);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        handleCampaignData(customizeFormDataEntity.getEa(), customizeFormDataEnrollResult.getEnrollId(), null, null, customizeFormDataUserEntity, arg.getWxAppId(), arg.getOpenId(), null, arg.getNeedSignIn(),arg.getTagId());
        if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER) {
            Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createFsPayOrder(
                    customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(),
                    customizeFormDataEntity.getCreateBy(), crmMemberId, arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
            if (!createPayOrderResult.isSuccess()) {
                return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
            }
            CreateFsPayOrderResult data = createPayOrderResult.getData();
            BeanUtils.copyProperties(data, customizeFormDataEnrollResult);
            customizeFormDataUserEntity.setPayOrderId(data.getOrderNo());
            customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(data.getOrderNo(), customizeFormDataUserEntity.getId(), ea);
        }

//        if(StringUtils.isNotBlank(arg.getCtaId())) {
//            ThreadPoolUtils.execute(() -> ctaService.recordCustomerDataEnroll(MarketingUserActionChannelType.WECHAT_SERVICE, arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
//        }
        return new Result<>(SHErrorCode.SUCCESS, customizeFormDataEnrollResult);
    }

    private Result<CustomizeFormDataEnrollResult> doMemberForgotPassword(String ea, CustomizeFormDataEnrollArg arg) {
        if (Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) || Strings.isNullOrEmpty(arg.getSubmitContent().getPassword())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (!optionalMember.isPresent()) {
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED);
        }
        memberManager.saveMemberPassword(ea, optionalMember.get().getId(), AESUtil.encode(AESUtil.randomKeyByPassword(optionalMember.get().getId()), arg.getSubmitContent().getPassword()));
        return Result.newSuccess();
    }

    @Override
    public Result<CustomizeFormDataEnrollResult> noIdentityFormDataEnroll(CustomizeFormDataEnrollArg arg) {
        // 处理utm被编码问题
        handleUtmEncode(arg);
        //文件tnpath转path
        changeTNpathFileToNpath(arg.getEa(), arg.getSubmitContent().getFileAttachmentMap());
        // 处理写死的官网登录页面逻辑
        if (ObjectTypeEnum.MEMBER_LOGIN_TEMPLATE.getType() == arg.getObjectType() || ObjectTypeEnum.MEMBER_REGISTER_TEMPLATE.getType() == arg.getObjectType()){
            String ea = arg.getSubmitContent().getFixEa();
            //如果有来源营销身份,获取是否是会员
            if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
                Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                        .getBaseInfosByIds(arg.getEa(), 1000, Lists.newArrayList(arg.getFromUserMarketingId()), InfoStateEnum.DETAIL);
                UserMarketingAccountData marketingAccountData = userMarketingAccountDataMap.get(arg.getFromUserMarketingId());
                if(marketingAccountData!=null&& CollectionUtils.isNotEmpty(marketingAccountData.getCrmMemberInfos())){
                    arg.setSpreadUserIdentifyId(marketingAccountData.getCrmMemberInfos().get(0).getId());
                    arg.setSpreadUserType(ChannelEnum.CRM_MEMBER.getType());
                }
            }
            if (StringUtils.isBlank(ea)) {
                log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll fixea is null, arg:{}", arg);
                return new Result<>(SHErrorCode.PARAMS_ERROR);
            }
            if (ObjectTypeEnum.MEMBER_REGISTER_TEMPLATE.getType() == arg.getObjectType()){
                return doNoIdentityMemberRegister(ea, arg);
            }
            if (ObjectTypeEnum.MEMBER_LOGIN_TEMPLATE.getType() == arg.getObjectType()){
                return doNoIdentityMemberLogin(ea, arg);
            }
        }
        CustomizeFormDataEnrollResult customizeFormDataEnrollResult = new CustomizeFormDataEnrollResult();
        // 校验物料合法性
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isBlank(ea)) {
            log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll ea is null, arg:{}", arg);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_BINDING_OBJECT_NOT_FOUND);
        }
        arg.setEa(ea);
        //如果有来源营销身份,获取是否是会员
        if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
            Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                    .getBaseInfosByIds(arg.getEa(), 1000, Lists.newArrayList(arg.getFromUserMarketingId()), InfoStateEnum.DETAIL);
            UserMarketingAccountData marketingAccountData = userMarketingAccountDataMap.get(arg.getFromUserMarketingId());
            if(marketingAccountData!=null&& CollectionUtils.isNotEmpty(marketingAccountData.getCrmMemberInfos())){
                arg.setSpreadUserIdentifyId(marketingAccountData.getCrmMemberInfos().get(0).getId());
                arg.setSpreadUserType(ChannelEnum.CRM_MEMBER.getType());
            }
        }
        //查询是否是会议营销,并处理审核状态
        customizeFormDataManager.checkConferenceEnrollReview(ea,arg.getMarketingEventId(),customizeFormDataEnrollResult);
        // 查询表单数据
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(),ea);
        Result checkResult = customizeFormDataManager.checkCustomizeFormDataStatus(customizeFormDataEntity, arg.getFormId(), false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        Result enrollFieldCheckResult = customizeFormDataManager.checkEnrollField(customizeFormDataEntity, arg.getSubmitContent(), arg.getObjectType(), arg.getObjectId(), arg.getStepFormComponentId());
        if (!enrollFieldCheckResult.isSuccess()) {
            return enrollFieldCheckResult;
        }
        if (!Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) && !Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())){
            boolean phoneVerified = verificationCodeManager.checkSMCode(arg.getSubmitContent().getPhone(), arg.getSubmitContent().getPhoneVerifyCode()).isSuccess();
            if (!phoneVerified){
                return Result.newError(SHErrorCode.PHONE_VERIFY_CODE_ERROR);
            }
        }
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == arg.getObjectType()){
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            if (memberConfig != null) {
                HexagonPageEntity hexagonPage = hexagonPageDAO.getById(ea, arg.getObjectId());
                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getRegistrationSiteId())){
                    return doNoIdentityMemberRegister(ea, arg);
                }
                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getLoginSiteId())){
                    return doNoIdentityMemberLogin(ea, arg);
                }
                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getUpdateInfoSiteId())) {
                    return doNoIdentityMemberUpdateInfo(ea, arg);
                }
                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getForgotPasswordSiteId())) {
                    return doMemberForgotPassword(ea, arg);
                }
            }
        }
        // 特殊数据校验
        if (StringUtils.isBlank(arg.getMarketingEventId())) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
            if (externalConfigEntity != null) {
                arg.setMarketingEventId(externalConfigEntity.getMarketingEventId());
            }
        }
        Result objectCheckResult = this.checkObjectLegitimacy(ea, arg.getMarketingEventId(), arg.getObjectId(), arg.getObjectType());
        if (!objectCheckResult.isSuccess()) {
            return objectCheckResult;
        }
        // 转换userId
        if (!arg.isPartner()) {
            arg.setSpreadFsUid(qywxUserManager.convertOldUserId(ea, arg.getSpreadFsUid()));
        }
        Boolean fillInOnce = customizeFormDataEntity.getFormMoreSetting().isFillInOnce();
        Boolean synchronousCRM = customizeFormDataEntity.getFormMoreSetting().isSynchronousCRM();
        Boolean enrollLimit = customizeFormDataEntity.getFormMoreSetting().isEnrollLimit();

        if (StringUtils.isBlank(arg.getEnrollId())) {
            if (fillInOnce) {
                String phone = arg.getSubmitContent().getPhone();
                String email = arg.getSubmitContent().getEmail();
                Result<CustomizeFormDataUserEntity> enrollCheck = customizeFormDataManager
                        .checkUserIsEnrolledWithNoHandleActivity(arg.getMarketingEventId(), customizeFormDataEntity, arg.getObjectId(), arg.getObjectType(), null, null, arg.getFingerPrint(), null, null, phone);
                if (!enrollCheck.isSuccess()) {
                    CustomizeFormDataUserEntity customizeFormDataUserEntity = enrollCheck.getData();
                    if (customizeFormDataUserEntity != null) {
                        customizeFormDataEnrollResult.setEnrollId(customizeFormDataUserEntity.getId());
                        ThreadPoolUtils.executeWithTraceContext(() -> {
                            Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
                            Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getFingerPrint()));
                            AssociationArg associationArg = new AssociationArg();
                            associationArg.setEa(ea);
                            associationArg.setPhone(phone);
                            associationArg.setEmail(email);
                            associationArg.setAssociationId(arg.getFingerPrint());
                            associationArg.setEmail(arg.getSubmitContent().getEmail());
                            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
                            associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
                            associationArg.setTriggerAction("noIdentityFormDataEnroll");
                            try {
                                userMarketingAccountAssociationManager.associate(associationArg);
                            } catch (Exception e) {
                                log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll error arg:{}", JSON.toJSONString(associationArg), e);
                            }
                        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
                    }
                    return new Result<>(enrollCheck.getErrCode(), enrollCheck.getErrMsg(), customizeFormDataEnrollResult);
                }
            }
            if (enrollLimit) { // 是否开启报名限制
                long countResult = customizeFormDataUserDAO.countCustomizeFormDataUserByFormId(customizeFormDataEntity.getId(), ea);
                if (countResult >= customizeFormDataEntity.getFormMoreSetting().getEnrollLimitNum()) {
                    return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_FULL.getErrorCode(), customizeFormDataEntity.getFormMoreSetting().getEnrollLimitText());
                }
            }
        }

        String crmMemberId = null;
        if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER && !customizeFormDataEntity.getFormMoreSetting().isSyncToMember()) {
            Result<String> memberResult = fsPayOrderManager.getMemberResult(arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount(), arg.getObjectType(), arg.getObjectId(),
                    arg.getOpenId(), arg.getWxAppId(),
                    arg.getFingerPrint(), arg.getAllEnterpriseMemberCookieMap(),
                    null);
            // 获取会员ID
            if (memberResult.isSuccess() && StringUtils.isNotBlank(memberResult.getData())) {
                crmMemberId = memberResult.getData();
            } else {
                return Result.newError(memberResult.getErrCode(), memberResult.getErrMsg());
            }
        }

        // 插入数据
        String customizeFormDataUserId = UUIDUtil.getUUID();
        buildMarketingEventIdByEnrollData(arg);
        CustomizeFormDataUserEntity customizeFormDataUserEntity = BeanUtil.copy(arg, CustomizeFormDataUserEntity.class);
        customizeFormDataUserEntity.setOutUid(arg.getOuterUid() == null ? null : String.valueOf(arg.getOuterUid()));
        // 创建营销推广来源对象
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
        customizeFormDataUserEntity.setEa(ea);
        if (StringUtils.isBlank(arg.getEnrollId())) {
            customizeFormDataUserEntity.setId(customizeFormDataUserId);
            customizeFormDataUserEntity.setUid("");
            if(arg.isPartner()){
                customizeFormDataUserEntity.setOutUid(String.valueOf(arg.getOuterUid()));
                customizeFormDataUserEntity.setOutTenantId(arg.getOuterTenantId());
            }else{
                customizeFormDataUserEntity.setSpreadFsUid(arg.getSpreadFsUid());
            }
            customizeFormDataUserEntity.setSourceType(customizeFormDataManager.getEnrollType(customizeFormDataUserEntity));
            String channelValue = customizeFormDataManager.getSystemPromotionChannelType(customizeFormDataUserEntity);
            customizeFormDataUserEntity.setChannelValue(channelValue);
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceType(customizeFormDataManager.getMarketingSourceType(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceName(customizeFormDataManager.getMarketingSourceName(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            //判断是否有映射crm字段
            if (!synchronousCRM) {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_1908));
            }
            boolean saveResult = customizeFormDataUserDAO.insertCustomizeFormDataUser(customizeFormDataUserEntity) == 1;
            if (!saveResult) {
                log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll insertCustomizeFormDataUser error customizeFormDataUserEntity:{}", customizeFormDataUserEntity);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            customizeFormDataEnrollResult.setEnrollId(customizeFormDataUserId);
            //上报神策埋点-表单提交
            marketingStatLogPersistorManger.sendCustomizeFormDataUserData(ea, customizeFormDataUserEntity.getId(), arg.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_H5);
        } else {
            // 更新数据
            customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getEnrollId(), ea);
            if (customizeFormDataUserEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll oldData is null enrollId:{}", arg.getEnrollId());
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            BeanUtil.appendCopyCustomizeFormDataEnrollIgnoreNull(arg.getSubmitContent(), customizeFormDataUserEntity.getSubmitContent());
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            // 更新报名数据
            customizeFormDataUserDAO.updateCustomizeFormDataEnrollDataById(customizeFormDataUserEntity);
            customizeFormDataEnrollResult.setEnrollId(arg.getEnrollId());
        }
        // 特殊物料逻辑处理
        CustomizeFormDataUserEntity finalCustomizeFormDataUserEntity = customizeFormDataUserEntity;
        ThreadPoolUtils.executeWithTraceContext(new Runnable() {
            @Override
            public void run() {
                customizeFormDataManager.handlerSpecialTypeEnrollData(finalCustomizeFormDataUserEntity);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        String leadId = null;
        // 如果有落地页 获取或者创建落地页的id
        String landingName = adCommonManager.getLandingName(arg);
        String landingObjId = adOCPCUploadManager.getOrCreateLandingObjId(ea, arg.getLandingUrl(), landingName, arg.getMarketingEventId());
        //单独处理存入其他任意对象
        if (Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(), SaveCrmObjectTypeEnum.OBJ.getType())) {
            CustomizeFormDataUserEntity finalSaveCustomizeFormDataUserEntity = customizeFormDataUserEntity;
            ThreadPoolUtils.executeWithTraceContext(()->{
                boolean updateData = !StringUtils.isEmpty(finalSaveCustomizeFormDataUserEntity.getExtraDataId());
                customizeFormDataManager.saveObjectToCrm(customizeFormDataEntity, finalSaveCustomizeFormDataUserEntity,updateData);
//                if(StringUtils.isNotBlank(arg.getCtaId())) {
//                    ctaService.recordCustomerDataEnroll(MarketingUserActionChannelType.H5, arg);
//                }
            },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            return new Result<>(SHErrorCode.SUCCESS, customizeFormDataEnrollResult);
        }
        // 处理保利威直播无需报名可看直播
        ThreadPoolUtils.execute(() -> {
            polyvManager.handlePolyvUser(arg.getMarketingEventId(), arg.getSubmitContent(),ea);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        // 将数据同步至CRM线索
        if (synchronousCRM) {
            try {
                if (StringUtils.isBlank(customizeFormDataUserEntity.getLeadId())) {
                    customizeFormDataUserEntity.setLandingObjId(landingObjId);
                    boolean success = customizeFormDataManager.saveEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                    if (success && !Strings.isNullOrEmpty(arg.getFingerPrint())) {
                        asyncBackTraceLookupWebsiteBehavior(ea, arg.getFingerPrint(), customizeFormDataUserId);
                    }
                    //上报神策埋点-创建线索
                    leadId = customizeFormDataUserEntity.getSubmitContent() == null ? null : customizeFormDataUserEntity.getSubmitContent().getLeadId();
                    if (success){
                        marketingStatLogPersistorManger.sendLeadData(ea, leadId, arg.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_H5);
                    }
                    // 自动创建会员并登录(数据保存到了CRM并且勾选了自动创建会员按钮)
                    if (success && customizeFormDataEntity.getFormMoreSetting().isSyncToMember()){
                        //会员id
                        Optional<String> memberId = memberManager.getH5LoginMemberId(ea, arg.getAllEnterpriseMemberCookieMap());
                        if (arg.getAllEnterpriseMemberCookieMap() == null || !memberId.isPresent()){
                            MemberConfigEntity memberConfig = memberManager.tryInitMemberConfig(ea);
                            CustomizeFormDataUserEntity dbData = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserId, ea);
                            //创建会员
                            Optional<String> optionalMember = memberManager.saveLeadToMemberAndSpreadData(ea, dbData,null);
                            //如果本身不是会员，则用新生成的会员身份的会员id来创建cookie
                            memberId = optionalMember;
                        }
                        //勾选了自动创建会员就返回cookie
                        if (memberId.isPresent()){
                            if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER) {
                                Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createFsPayOrder(
                                        customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(),
                                        customizeFormDataEntity.getCreateBy(), crmMemberId, arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
                                if (!createPayOrderResult.isSuccess()) {
                                    return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
                                }
                                CreateFsPayOrderResult data = createPayOrderResult.getData();
                                BeanUtils.copyProperties(data, customizeFormDataEnrollResult);
                                customizeFormDataUserEntity.setPayOrderId(data.getOrderNo());
                                customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(data.getOrderNo(), customizeFormDataUserEntity.getId(), ea);
                            }
                            CustomizeFormDataEnrollResult result = doConvertMemberIdToCookieResult(ea, memberId.get());
                            customizeFormDataEnrollResult.setMemberCookieKey(result.getMemberCookieKey());
                            customizeFormDataEnrollResult.setMemberCookie(result.getMemberCookie());
                            customizeFormDataEnrollResult.setMemberCookieDomain(result.getMemberCookieDomain());
                            customizeFormDataEnrollResult.setMemberCookieExpireAtMillisecond(result.getMemberCookieExpireAtMillisecond());
                        }
                    }

                } else {
                    // 更新crm数据
                    customizeFormDataManager.updateEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                }
            } catch (Exception e) {
                log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll error e:", e);
            }
        }
        adOCPCUploadManager.bindEnrollDataLandingObj(ea, landingObjId, leadId, customizeFormDataEnrollResult.getEnrollId(), arg.getMarketingEventId(), arg.getLandingUrl());
        CustomizeFormDataUserEntity finalSendNoticeEntity = customizeFormDataUserEntity;
        ThreadPoolUtils.execute(() -> {
            // 存入线索表成功但缺失crm映射字段无法同步到crm需要发送失败通知
            customizeFormDataManager.sendNotice(ea, finalSendNoticeEntity);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        handleCampaignData(customizeFormDataEntity.getEa(), customizeFormDataEnrollResult.getEnrollId(), arg.getQrSource(), arg.getQrSourceId(), customizeFormDataUserEntity, null, null, arg.getFingerPrint(), arg.getNeedSignIn(),arg.getTagId());
        if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER) {
            Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createFsPayOrder(
                    customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(),
                    customizeFormDataEntity.getCreateBy(), crmMemberId, arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
            if (!createPayOrderResult.isSuccess()) {
                return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
            }
            CreateFsPayOrderResult data = createPayOrderResult.getData();
            BeanUtils.copyProperties(data, customizeFormDataEnrollResult);
            customizeFormDataUserEntity.setPayOrderId(data.getOrderNo());
            customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(data.getOrderNo(), customizeFormDataUserEntity.getId(), ea);
        }
//        if(StringUtils.isNotBlank(arg.getCtaId())) {
//            ThreadPoolUtils.execute(() -> ctaService.recordCustomerDataEnroll(MarketingUserActionChannelType.H5, arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
//        }
        return new Result<>(SHErrorCode.SUCCESS, customizeFormDataEnrollResult);
    }

    private static void handleUtmEncode(CustomizeFormDataEnrollArg arg) {
        if (arg == null || arg.getSubmitContent() == null) {
            return;
        }
        String utmSource = arg.getSubmitContent().getUtmSource();
        try {
            if (StringUtils.isNotBlank(utmSource)) {
                String newUtmSource = URLDecoder.decode(utmSource, "UTF-8");
                if (!newUtmSource.equals(utmSource)) {
                    arg.getSubmitContent().setUtmSource(newUtmSource);
                    log.info("replace utm source old: {} new : {}", utmSource, newUtmSource);
                }
            }
        } catch (Exception e) {
            log.error("decode utm source error, arg: {}", utmSource, e);
        }

        String utmMedium = arg.getSubmitContent().getUtmMedium();
        try {
            if (StringUtils.isNotBlank(utmMedium)) {
                String newUtmMedium = URLDecoder.decode(utmMedium, "UTF-8");
                if (!newUtmMedium.equals(utmMedium)) {
                    arg.getSubmitContent().setUtmMedium(newUtmMedium);
                    log.info("replace utm medium old: {} new : {}", utmMedium, newUtmMedium);
                }
            }
        } catch (Exception e) {
            log.error("decode utm medium error, arg: {}", utmSource, e);
        }

        String utmCampaign = arg.getSubmitContent().getUtmCampaig();
        try {
            if (StringUtils.isNotBlank(utmCampaign)) {
                String newUtmCampaign = URLDecoder.decode(utmCampaign, "UTF-8");
                if (!newUtmCampaign.equals(utmCampaign)) {
                    arg.getSubmitContent().setUtmCampaig(newUtmCampaign);
                    log.info("replace utm campaign old: {} new : {}", utmCampaign, newUtmCampaign);
                }
            }
        } catch (Exception e) {
            log.error("decode utm campaign error, arg: {}", utmSource, e);
        }

        String utmTerm = arg.getSubmitContent().getUtmTerm();
        try {
            if (StringUtils.isNotBlank(utmTerm)) {
                String newUtmTerm = URLDecoder.decode(utmTerm, "UTF-8");
                if (!newUtmTerm.equals(utmTerm)) {
                    arg.getSubmitContent().setUtmTerm(newUtmTerm);
                    log.info("replace utm term old: {} new : {}", utmTerm, newUtmTerm);
                }
            }
        } catch (Exception e) {
            log.error("decode utm term error, arg: {}", utmSource, e);
        }

        String utmContent = arg.getSubmitContent().getUtmContent();
        try {
            if (StringUtils.isNotBlank(utmContent)) {
                String newUtmContent = URLDecoder.decode(utmContent, "UTF-8");
                if (!newUtmContent.equals(utmContent)) {
                    arg.getSubmitContent().setUtmContent(newUtmContent);
                    log.info("replace utm content old: {} new : {}", utmContent, newUtmContent);
                }
            }
        } catch (Exception e) {
            log.error("decode utm content error, arg: {}", utmSource, e);
        }

    }


    private Result<CustomizeFormDataEnrollResult> doNoIdentityMemberRegister(String ea, CustomizeFormDataEnrollArg arg){
        if (Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) /*|| Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())*/){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (optionalMember.isPresent()){
            return Result.newError(SHErrorCode.PHONE_HAVE_BEEN_REGISTERED);
        }
        arg.getSubmitContent().setForceSynUtm(true);
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
        Result<String> saveMemberResult = memberManager.saveMemberByFormData(ea, arg, "other", null, marketingPromotionSourceId);
        if (!saveMemberResult.isSuccess()){
            return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg());
        }

        if(StringUtils.isNotBlank(arg.getCtaId())) {
            ThreadPoolUtils.execute(() -> ctaService.recordMemberRegister(MarketingUserActionChannelType.H5, arg, saveMemberResult.getData()), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        String marketingEvenId = null;
        String keywordId = null;
        //官网注册页
        if(arg.getSubmitContent()!=null){
            if(StringUtils.isNotBlank(arg.getSubmitContent().getUtmCampaig())){
                PaasQueryFilterArg evenQueryFilterArg = new PaasQueryFilterArg();
                PaasQueryArg evenPaasQueryArg = new PaasQueryArg(0, 1);
                evenPaasQueryArg.addFilter("event_type",  OperatorConstants.EQ, Lists.newArrayList("advertising_marketing"));
                evenPaasQueryArg.addFilter("name",  OperatorConstants.EQ,Lists.newArrayList(arg.getSubmitContent().getUtmCampaig()));
                evenQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                evenQueryFilterArg.setQuery(evenPaasQueryArg);
                InnerPage<ObjectData> dataInnerPage = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, evenQueryFilterArg,1,1);
                if (dataInnerPage != null && CollectionUtils.isNotEmpty(dataInnerPage.getDataList())) {
                    marketingEvenId = dataInnerPage.getDataList().get(0).getId();
                }
            }
            if(StringUtils.isNotBlank(arg.getSubmitContent().getUtmTerm())){
                PaasQueryFilterArg evenQueryFilterArg = new PaasQueryFilterArg();
                PaasQueryArg evenPaasQueryArg = new PaasQueryArg(0, 1);
                evenPaasQueryArg.addFilter("name",  OperatorConstants.EQ,Lists.newArrayList(arg.getSubmitContent().getUtmTerm()));
                evenQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_KEYWORD.getName());
                evenQueryFilterArg.setQuery(evenPaasQueryArg);
                InnerPage<ObjectData> dataInnerPage = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, evenQueryFilterArg,1,1);
                if (dataInnerPage != null && CollectionUtils.isNotEmpty(dataInnerPage.getDataList())) {
                    keywordId = dataInnerPage.getDataList().get(0).getId();
                }
            }
        }

        ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
            metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
        }
        String memberId = saveMemberResult.getData();

        // 会员存入线索对象
        String finalMarketingEvenId = marketingEvenId;
        String finalKeywordId = keywordId;
        ThreadPoolUtils.execute(() -> memberManager.saveMemberToLead(ea, memberId, null, finalMarketingEvenId,
                null, arg.getChannelValue(), marketingPromotionSourceId, arg.getFormId(), finalKeywordId, arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        JSONObject memberInfo = redisManager.getMemberInfo(arg.getFingerPrint());
        if (memberInfo != null) {
            ThreadPoolUtils.execute(() -> bindMemberWxAccount(ea, memberId, memberInfo), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        CustomizeFormDataEnrollResult data = doConvertMemberIdToCookieResult(ea, memberId);
        data.setPhone(arg.getSubmitContent().getPhone());
        Optional<ObjectData> member = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (member.isPresent()){
            String approvalStatus =(String) member.get().get("approval_status");
            //历史数据+导入数据审核状态为空默认放行
            if(Strings.isNullOrEmpty(approvalStatus)){
                data.setMemberApprovalStatus(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
            }else {
                data.setMemberApprovalStatus(Integer.parseInt(approvalStatus));
            }
        }
        String password = arg.getSubmitContent().getPassword();
        if (StringUtils.isNotBlank(password)) {
            memberManager.saveMemberPassword(ea, memberId, AESUtil.encode(AESUtil.randomKeyByPassword(memberId), password));
        }
        return Result.newSuccess(data);
    }

    private void bindMemberWxAccount(String ea, String memberId, JSONObject compositeObject) {
        String wxAppId = compositeObject.getString("wxAppId");
        String wxOpenId = compositeObject.getString("wxOpenId");
        if (StringUtils.isNotEmpty(wxAppId) && StringUtils.isNotEmpty(wxOpenId)) {
            wxServiceUserMemberBindDao.insertIgnore(ea, wxAppId, wxOpenId, memberId);
        }
    }

    public Result<CustomizeFormDataEnrollResult> doNoIdentityMemberUpdateInfo(String ea, CustomizeFormDataEnrollArg arg) {
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(ea, arg.getAllEnterpriseMemberCookieMap());
        if (!optionalMemberId.isPresent() || StringUtils.isBlank(optionalMemberId.get())) {
            log.warn("CustomizeFormDataServiceImpl.doNoIdentityMemberUpdateInfo error arg:{}", arg);
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }
        Result updateResult = memberManager.updateMemberByFormData(ea, optionalMemberId.get(), arg);
        return updateResult.isSuccess() ? Result.newSuccess(new CustomizeFormDataEnrollResult())
            : Result.newError(updateResult.getErrCode(), updateResult.getErrMsg(), new CustomizeFormDataEnrollResult());
    }

    private Result<CustomizeFormDataEnrollResult> doNoIdentityMemberLogin(String ea, CustomizeFormDataEnrollArg arg){
        if (Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) /*|| Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())*/){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (!optionalMember.isPresent()){
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            //开启一键注册登陆处理 注：官网为写死表单，不应影响官网登录
            if(memberConfig != null && memberConfig.isDirectLoginAndRegistration() && ObjectTypeEnum.MEMBER_LOGIN_TEMPLATE.getType() != arg.getObjectType()){
                Result<String> saveMemberResult = memberManager.saveMemberByLoginFormData(ea, arg, "other", null);
                if (!saveMemberResult.isSuccess()){
                    return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg());
                }
                if(StringUtils.isNotBlank(arg.getCtaId())) {
                    ThreadPoolUtils.execute(() -> {
                        ctaService.recordMemberRegister(MarketingUserActionChannelType.H5, arg, saveMemberResult.getData());
                        ctaService.recordMemberLogin(MarketingUserActionChannelType.H5, arg, saveMemberResult.getData());
                    }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                }
                ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
                if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
                    metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
                }
                String memberId = saveMemberResult.getData();
                arg.getSubmitContent().setForceSynUtm(true);
                String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
                // 会员存入线索对象
                ThreadPoolUtils.execute(() -> memberManager.saveMemberToLead(ea, memberId, null, null,
                        null, arg.getChannelValue(), marketingPromotionSourceId, arg.getFormId(),null, arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);

                JSONObject memberInfo = redisManager.getMemberInfo(arg.getFingerPrint());
                if (memberInfo != null) {
                    ThreadPoolUtils.execute(() -> bindMemberWxAccount(ea, memberId, memberInfo), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                }

                CustomizeFormDataEnrollResult data = doConvertMemberIdToCookieResult(ea, memberId);
                data.setPhone(arg.getSubmitContent().getPhone());
                Optional<ObjectData> member = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
                if (member.isPresent()){
                    String approvalStatus =(String) member.get().get("approval_status");
                    //历史数据+导入数据审核状态为空默认放行
                    if(Strings.isNullOrEmpty(approvalStatus)){
                        data.setMemberApprovalStatus(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
                    }else {
                        data.setMemberApprovalStatus(Integer.parseInt(approvalStatus));
                    }
                }
                return Result.newSuccess(data);
            }
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED);
        }
        String password = arg.getSubmitContent().getPassword();
        if (StringUtils.isNotBlank(password)) {
            if (!memberManager.verifyMemberPassword(ea, optionalMember.get().getId(), AESUtil.encode(AESUtil.randomKeyByPassword(optionalMember.get().getId()), password))) {
                return Result.newError(SHErrorCode.IDENTITY_INVAILED.getErrorCode(), I18nUtil.get("mark.member.password.error", "密码错误"));
            }
        }
        JSONObject memberInfo = redisManager.getMemberInfo(arg.getFingerPrint());
        if (memberInfo != null) {
            ThreadPoolUtils.execute(() -> bindMemberWxAccount(ea, optionalMember.get().getId(), memberInfo), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        if(StringUtils.isNotBlank(arg.getCtaId())) {
            ThreadPoolUtils.execute(() -> {
                ctaService.recordMemberLogin(MarketingUserActionChannelType.H5, arg, optionalMember.get().getId());
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        CustomizeFormDataEnrollResult data = doConvertMemberIdToCookieResult(ea, optionalMember.get().getId());
        data.setPhone(arg.getSubmitContent().getPhone());
        String approvalStatus =(String) optionalMember.get().get("approval_status");
        //历史数据+导入数据审核状态为空默认放行
        if(Strings.isNullOrEmpty(approvalStatus)){
            data.setMemberApprovalStatus(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
        }else {
            data.setMemberApprovalStatus(Integer.parseInt(approvalStatus));
        }
        data.setEa(ea);
        return Result.newSuccess(data);
    }

    private CustomizeFormDataEnrollResult doConvertMemberIdToCookieResult(String ea, String memberId) {
        CustomizeFormDataEnrollResult result = new CustomizeFormDataEnrollResult();
        DateTime dateTime = new DateTime();
        dateTime = dateTime.plusDays(30);
        long feCookieExpireAtSeconds = dateTime.getMillis() / 1000;
        result.setMemberCookieExpireAtMillisecond((int)feCookieExpireAtSeconds);
        dateTime = dateTime.plusHours(1);
        result.setMemberCookieKey(CookieConstant.MEMBER_COOKIE_PREFIX + ea);
        result.setMemberCookie(new MemberCookieInfo(memberId, dateTime.getMillis()).aesEncode());
        result.setMemberCookieDomain(cookieDomain);
        return result;
    }

    private Result<CustomizeFormDataEnrollResult> doWxServiceUserMemberRegister(String ea, CustomizeFormDataEnrollArg arg){
        if (Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) || /*Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode()) || */ Strings.isNullOrEmpty(arg.getWxAppId()) || Strings.isNullOrEmpty(arg.getOpenId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (optionalMember.isPresent()){
            return Result.newError(SHErrorCode.PHONE_HAVE_BEEN_REGISTERED);
        }

        Object avatar = null;
        Optional<ObjectData> wechatFanOptional = crmV2Manager.getWechatFanByOpenId(ea, arg.getWxAppId(), arg.getOpenId());
        if (wechatFanOptional.isPresent()){
            avatar = wechatFanOptional.get().get(CrmWechatFanFieldEnum.WX_HEAD_IMAGE.getFieldName());
        }
        // 会员存入线索对象
        arg.getSubmitContent().setForceSynUtm(true);
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
        Result<String> saveMemberResult = memberManager.saveMemberByFormData(ea, arg, "wechat", avatar, marketingPromotionSourceId);
        if (!saveMemberResult.isSuccess()){
            return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg());
        }

        if(StringUtils.isNotBlank(arg.getCtaId())) {
            ThreadPoolUtils.execute(() -> {
                ctaService.recordMemberRegister(MarketingUserActionChannelType.WECHAT_SERVICE, arg, saveMemberResult.getData());
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        ThreadPoolUtils.execute(() -> memberManager.saveMemberToLead(ea, saveMemberResult.getData(), null, null,
                null, arg.getChannelValue(), marketingPromotionSourceId, arg.getFormId(),null, arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
            metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
        }

        String boundMemberId = wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(ea, arg.getWxAppId(), arg.getOpenId());
        if (!Strings.isNullOrEmpty(boundMemberId)){
            wxServiceUserMemberBindDao.deleteByWxServiceUser(ea, arg.getWxAppId(), arg.getOpenId());
        }
        String memberId = saveMemberResult.getData();

        wxServiceUserMemberBindDao.insertIgnore(ea, arg.getWxAppId(), arg.getOpenId(), memberId);
        String password = arg.getSubmitContent().getPassword();
        if (StringUtils.isNotBlank(password)) {
            memberManager.saveMemberPassword(ea, memberId, AESUtil.encode(AESUtil.randomKeyByPassword(memberId), password));
        }
        return Result.newSuccess(new CustomizeFormDataEnrollResult());
    }

    private Result<CustomizeFormDataEnrollResult> doWxServiceUserMemberLogin(String ea, CustomizeFormDataEnrollArg arg){
        if (Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) /**|| Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())**/ || Strings.isNullOrEmpty(arg.getWxAppId()) || Strings.isNullOrEmpty(arg.getOpenId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (!optionalMember.isPresent()){
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            //开启一键注册登陆处理
            //官网会员登录页非物料
            boolean isFixTemplateSite = ObjectTypeEnum.MEMBER_LOGIN_TEMPLATE.getType() == arg.getObjectType();
            if(!isFixTemplateSite && memberConfig != null && memberConfig.isDirectLoginAndRegistration()){
                Result<String> saveMemberResult = memberManager.saveMemberByLoginFormData(ea, arg, "other", null);
                if (!saveMemberResult.isSuccess()){
                    return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg());
                }
                if(StringUtils.isNotBlank(arg.getCtaId())) {
                    ThreadPoolUtils.execute(() -> {
                        ctaService.recordMemberRegister(MarketingUserActionChannelType.WECHAT_SERVICE, arg, saveMemberResult.getData());
                        ctaService.recordMemberLogin(MarketingUserActionChannelType.WECHAT_SERVICE, arg, saveMemberResult.getData());
                    }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                }
                ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
                if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
                    metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
                }
                String memberId = saveMemberResult.getData();
                arg.getSubmitContent().setForceSynUtm(true);
                String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
                // 会员存入线索对象
                ThreadPoolUtils.execute(() -> memberManager.saveMemberToLead(ea, memberId, null,
                        null, null, arg.getChannelValue(), marketingPromotionSourceId, arg.getFormId(),null, arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);

                JSONObject memberInfo = redisManager.getMemberInfo(arg.getFingerPrint());
                if (memberInfo != null) {
                    ThreadPoolUtils.execute(() -> bindMemberWxAccount(ea, memberId, memberInfo), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                }

                CustomizeFormDataEnrollResult data = doConvertMemberIdToCookieResult(ea, memberId);
                data.setPhone(arg.getSubmitContent().getPhone());
                data.setEa(ea);
                return Result.newSuccess(data);
            }
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED);
        }
        String password = arg.getSubmitContent().getPassword();
        if (StringUtils.isNotBlank(password)) {
            if (!memberManager.verifyMemberPassword(ea, optionalMember.get().getId(), AESUtil.encode(AESUtil.randomKeyByPassword(optionalMember.get().getId()), password))) {
                return Result.newError(SHErrorCode.IDENTITY_INVAILED.getErrorCode(), I18nUtil.get("mark.member.password.error", "密码错误"));
            }
        }
        if(StringUtils.isNotBlank(arg.getCtaId())) {
            ThreadPoolUtils.execute(() -> {
                ctaService.recordMemberLogin(MarketingUserActionChannelType.WECHAT_SERVICE, arg, optionalMember.get().getId());
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        String boundMemberId = wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(ea, arg.getWxAppId(), arg.getOpenId());
        if (!Strings.isNullOrEmpty(boundMemberId)){
            wxServiceUserMemberBindDao.deleteByWxServiceUser(ea, arg.getWxAppId(), arg.getOpenId());
        }
        wxServiceUserMemberBindDao.insertIgnore(ea, arg.getWxAppId(), arg.getOpenId(), optionalMember.get().getId());
        //历史数据+导入数据审核状态为空默认放行
        String approvalStatus =(String) optionalMember.get().get("approval_status");
        CustomizeFormDataEnrollResult result = new CustomizeFormDataEnrollResult();
        if(Strings.isNullOrEmpty(approvalStatus)){
            result.setMemberApprovalStatus(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
        }else {
            result.setMemberApprovalStatus(Integer.parseInt(approvalStatus));
        }
        result.setEa(ea);
        return Result.newSuccess(result);
    }

    public Result<CustomizeFormDataEnrollResult> doWxServiceUserMemberUpdateInfo(String ea, CustomizeFormDataEnrollArg arg) {
        String boundMemberId = wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(ea, arg.getWxAppId(), arg.getOpenId());
        if (StringUtils.isBlank(boundMemberId)) {
            log.warn("MemberServiceImpl.doWxMiniAppUserMemberUpdateInfo boundMemberId is null arg :{}", arg);
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }
        Result updateResult = memberManager.updateMemberByFormData(ea, boundMemberId, arg);
        return updateResult.isSuccess() ? Result.newSuccess(new CustomizeFormDataEnrollResult())
            : Result.newError(updateResult.getErrCode(), updateResult.getErrMsg(), new CustomizeFormDataEnrollResult());
    }

    /**
     * 追溯查看官网行为并同步到CRM行为积分
     * @param ea
     * @param fingerPrintId
     * @param customizeFormDataUserId
     */
    private void asyncBackTraceLookupWebsiteBehavior(String ea, String fingerPrintId, String customizeFormDataUserId) {
        ThreadPoolUtils.execute(() -> {
            String userMarketingId = browserUserMarketingAccountAssociationService.associateBrowserUser(ea, fingerPrintId).getData();
            if(!Strings.isNullOrEmpty(userMarketingId)){
                long sevenDayAgoMills = DateTime.now().minusDays(7).getMillis();
                CustomizeFormDataUserEntity updatedCustomizeFormDataUser = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserId, ea);
                if(updatedCustomizeFormDataUser != null && !Strings.isNullOrEmpty(updatedCustomizeFormDataUser.getLeadId())){
                    Outer:
                    for (int pageNo = 1; pageNo < 100; pageNo++) {
                        int pageSize = 20;
                        PageArg<UserMarketingActionStatisticQueryArg> pageArg = new PageArg<>();
                        pageArg.setPageNo(pageNo);
                        pageArg.setPageSize(pageSize);
                        UserMarketingActionStatisticQueryArg queryArg = new UserMarketingActionStatisticQueryArg();
                        queryArg.setEa(ea);
                        queryArg.setUserMarketingId(userMarketingId);
                        pageArg.setQueryArgs(queryArg);
                        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> pageResultResult = userMarketingStatisticService.pageUserMarketingActionStatistic(pageArg);
                        if (pageResultResult.getData() == null || pageResultResult.getData().getData() == null){
                            break;
                        }
                        for (UserMarketingActionStatisticResult datum : pageResultResult.getData().getData()) {
                            if(datum.getCreateTime() < sevenDayAgoMills){
                                break Outer;
                            }
                            if(datum.getProperties() != null){
                                UserMarketingActionResult actionResult = BeanUtil.copyByGson(datum.getProperties(), UserMarketingActionResult.class);
                                // 只追溯到上一次提交行为
                                if(actionResult.getChannelType() == MarketingUserActionChannelType.H5.getChannelType() && actionResult.getActionType() != null && actionResult.getActionType() - 1000000 == MarketingUserActionType.SUBMIT_FORM.getActionType()){
                                    break Outer;
                                }
                                if(actionResult.getChannelType() == MarketingUserActionChannelType.H5.getChannelType() && actionResult.getActionType() != null && actionResult.getActionType() - 1000000 == MarketingUserActionType.LOOK_UP_WEBSITE.getActionType() && !Strings.isNullOrEmpty(actionResult.getObjectId())){
                                    int count = datum.getCount() == null ? 0 : datum.getCount();
                                    for (int i = 0; i < count; i++) {
                                        BehaviorSendEventData data = new BehaviorSendEventData();
                                        data.setActionApiName(ActionApiNameConstant.ACCESS);
                                        data.setCategoryApiName(CategoryApiNameConstant.WEBSITE);
                                        data.setMaterialApiName(actionResult.getObjectId());
                                        data.setObjectApiName(LeadsFieldContants.API_NAME);
                                        data.setObjectId(updatedCustomizeFormDataUser.getLeadId());
                                        data.setTenantId("" + eieaConverter.enterpriseAccountToId(ea));
                                        data.setTimestamp(actionResult.getCreateTime() == null ? System.currentTimeMillis() : actionResult.getCreateTime());
                                        behaviorSendEventSender.sendToDelayMq(data);
                                    }
                                }
                            }
                        }
                        if(pageResultResult.getData().getData().size() < pageSize){
                            break;
                        }
                    }
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    @Override
    public Result<CustomizeFormDataEnrollResult> fsCustomizeFormDataEnroll(CustomizeFormDataEnrollArg arg) {
        CustomizeFormDataEnrollResult customizeFormDataEnrollResult = new CustomizeFormDataEnrollResult();
        // 校验物料合法性
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isBlank(ea)) {
            log.warn("CustomizeFormDataServiceImpl.fsCustomizeFormDataEnroll ea is null, arg:{}", arg);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_BINDING_OBJECT_NOT_FOUND);
        }
        arg.setEa(ea);
        //文件tnpath转path
        changeTNpathFileToNpath(arg.getEa(), arg.getSubmitContent().getFileAttachmentMap());
        //如果有来源营销身份,获取是否是会员
        if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
            Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                    .getBaseInfosByIds(arg.getEa(), 1000, Lists.newArrayList(arg.getFromUserMarketingId()), InfoStateEnum.DETAIL);
            UserMarketingAccountData marketingAccountData = userMarketingAccountDataMap.get(arg.getFromUserMarketingId());
            if(marketingAccountData!=null&& CollectionUtils.isNotEmpty(marketingAccountData.getCrmMemberInfos())){
                arg.setSpreadUserIdentifyId(marketingAccountData.getCrmMemberInfos().get(0).getId());
                arg.setSpreadUserType(ChannelEnum.CRM_MEMBER.getType());
            }
        }

        //CTA组件纷享身份提交表单增加会员逻辑处理
        boolean isFsCustomizeFormDataEnrollMemberEnable = ctaService.isFsCustomizeFormDataEnrollMemberEnable(ea);
        if(isFsCustomizeFormDataEnrollMemberEnable) {
            // 处理写死的官网登录页面逻辑
            if (ObjectTypeEnum.MEMBER_LOGIN_TEMPLATE.getType() == arg.getObjectType() || ObjectTypeEnum.MEMBER_REGISTER_TEMPLATE.getType() == arg.getObjectType()){
                String fixEa = arg.getSubmitContent().getFixEa();
                if (StringUtils.isBlank(fixEa)) {
                    log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll fixea is null, arg:{}", arg);
                    return new Result<>(SHErrorCode.PARAMS_ERROR);
                }
                if (ObjectTypeEnum.MEMBER_REGISTER_TEMPLATE.getType() == arg.getObjectType()){
                    return doNoIdentityMemberRegister(fixEa, arg);
                }
                if (ObjectTypeEnum.MEMBER_LOGIN_TEMPLATE.getType() == arg.getObjectType()){
                    return doNoIdentityMemberLogin(fixEa, arg);
                }
            }
        }

        //文件tnpath转path
        changeTNpathFileToNpath(arg.getEa(), arg.getSubmitContent().getFileAttachmentMap());
        //查询是否是会议营销,并处理审核状态
        customizeFormDataManager.checkConferenceEnrollReview(ea,arg.getMarketingEventId(),customizeFormDataEnrollResult);
        // 查询表单数据
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(),arg.getEa());
        //检查表单状态
        Result checkResult = customizeFormDataManager.checkCustomizeFormDataStatus(customizeFormDataEntity, arg.getFormId(), false);
        //如果为停用或者删除状态，则直接返回
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        Result enrollFieldCheckResult = customizeFormDataManager.checkEnrollField(customizeFormDataEntity, arg.getSubmitContent(), arg.getObjectType(), arg.getObjectId(), arg.getStepFormComponentId());
        if (!enrollFieldCheckResult.isSuccess()) {
            return enrollFieldCheckResult;
        }
        //校验手机验证码是否正确
        if (!Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) && !Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())){
            boolean phoneVerified = verificationCodeManager.checkSMCode(arg.getSubmitContent().getPhone(), arg.getSubmitContent().getPhoneVerifyCode()).isSuccess();
            if (!phoneVerified){
                return Result.newError(SHErrorCode.PHONE_VERIFY_CODE_ERROR);
            }
        }

        //CTA组件纷享身份提交表单增加会员逻辑处理
        if(isFsCustomizeFormDataEnrollMemberEnable) {
            if (ObjectTypeEnum.HEXAGON_PAGE.getType() == arg.getObjectType()) {
                MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
                if (memberConfig != null) {
                    HexagonPageEntity hexagonPage = hexagonPageDAO.getById(ea, arg.getObjectId());
                    if (hexagonPage.getHexagonSiteId().equals(memberConfig.getRegistrationSiteId())) {
                        return doNoIdentityMemberRegister(ea, arg);
                    }
                    if (hexagonPage.getHexagonSiteId().equals(memberConfig.getLoginSiteId())) {
                        return doNoIdentityMemberLogin(ea, arg);
                    }
                    if (hexagonPage.getHexagonSiteId().equals(memberConfig.getUpdateInfoSiteId())) {
                        return doNoIdentityMemberUpdateInfo(ea, arg);
                    }
                }
            }
        }

        // 特殊数据校验
        if (StringUtils.isBlank(arg.getMarketingEventId())) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
            if (externalConfigEntity != null) {
                arg.setMarketingEventId(externalConfigEntity.getMarketingEventId());
            }
        }
        Result objectCheckResult = this.checkObjectLegitimacy(ea, arg.getMarketingEventId(), arg.getObjectId(), arg.getObjectType());
        if (!objectCheckResult.isSuccess()) {
            return objectCheckResult;
        }
        // 转换userId
        if (!arg.isPartner()) {
            arg.setSpreadFsUid(qywxUserManager.convertOldUserId(ea, arg.getSpreadFsUid()));
        }
        Boolean fillInOnce = customizeFormDataEntity.getFormMoreSetting().isFillInOnce();
        Boolean synchronousCRM = customizeFormDataEntity.getFormMoreSetting().isSynchronousCRM();
        Boolean enrollLimit = customizeFormDataEntity.getFormMoreSetting().isEnrollLimit();
        if (StringUtils.isBlank(arg.getEnrollId())) {
            if (fillInOnce) {
                Result<CustomizeFormDataUserEntity> enrollCheck = customizeFormDataManager
                        .checkUserIsEnrolledWithNoHandleActivity(arg.getMarketingEventId(), customizeFormDataEntity, arg.getObjectId(), arg.getObjectType(), null, null, null, arg.getEnrollUserEa(), arg.getEnrollUserFsUid(), arg.getSubmitContent().getPhone());
                if (!enrollCheck.isSuccess()) {
                    //说明用户已经报过名
                    CustomizeFormDataUserEntity customizeFormDataUserEntity = enrollCheck.getData();
                    if (customizeFormDataUserEntity != null) {
                        //返回报名id
                        customizeFormDataEnrollResult.setEnrollId(customizeFormDataUserEntity.getId());
                    }
                    return new Result<>(SHErrorCode.USERS_HAVE_REGISTERED, customizeFormDataEnrollResult);
                }
            }
            if (enrollLimit) { // 是否开启报名限制
                long countResult = customizeFormDataUserDAO.countCustomizeFormDataUserByFormId(customizeFormDataEntity.getId(), ea);
                if (countResult >= customizeFormDataEntity.getFormMoreSetting().getEnrollLimitNum()) {
                    return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_FULL.getErrorCode(), customizeFormDataEntity.getFormMoreSetting().getEnrollLimitText());
                }
            }
        }

        String crmMemberId = null;
        if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER && !customizeFormDataEntity.getFormMoreSetting().isSyncToMember()) {
            Result<String> memberResult = fsPayOrderManager.getMemberResult(arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount(), arg.getObjectType(), arg.getObjectId(),
                    arg.getOpenId(), arg.getWxAppId(),
                    arg.getFingerPrint(), arg.getAllEnterpriseMemberCookieMap(),
                    null);
            // 获取会员ID
            if (memberResult.isSuccess() && StringUtils.isNotBlank(memberResult.getData())) {
                crmMemberId = memberResult.getData();
            } else {
                return Result.newError(memberResult.getErrCode(), memberResult.getErrMsg());
            }
        }

        // 插入数据
        String customizeFormDataUserId = UUIDUtil.getUUID();
        //设置营销事件id
        buildMarketingEventIdByEnrollData(arg);
        CustomizeFormDataUserEntity customizeFormDataUserEntity = BeanUtil.copy(arg, CustomizeFormDataUserEntity.class);
        customizeFormDataUserEntity.setEa(ea);
        if (StringUtils.isBlank(arg.getEnrollId())) {
            customizeFormDataUserEntity.setId(customizeFormDataUserId);
            customizeFormDataUserEntity.setUid("");
            if(arg.isPartner()){
                customizeFormDataUserEntity.setOutUid(String.valueOf(arg.getOuterUid()));
                customizeFormDataUserEntity.setOutTenantId(arg.getOuterTenantId());
            }else{
                customizeFormDataUserEntity.setSpreadFsUid(arg.getSpreadFsUid());
            }
            //设置报名来源
            customizeFormDataUserEntity.setSourceType(customizeFormDataManager.getEnrollType(customizeFormDataUserEntity));
            //设置渠道
            customizeFormDataUserEntity.setChannelValue(customizeFormDataManager.getSystemPromotionChannelType(customizeFormDataUserEntity));
            // 创建营销推广来源对象
            arg.getSubmitContent().setForceSynUtm(true);
            String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceType(customizeFormDataManager.getMarketingSourceType(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceName(customizeFormDataManager.getMarketingSourceName(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            //判断是否有映射crm字段
            if (!synchronousCRM) {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_1908));
            }
            boolean saveResult = customizeFormDataUserDAO.insertCustomizeFormDataUser(customizeFormDataUserEntity) == 1;
            customizeFormDataEnrollResult.setEnrollId(customizeFormDataUserId);
            if (!saveResult) {
                log.warn("CustomizeFormDataServiceImpl.fsCustomizeFormDataEnroll insertCustomizeFormDataUser error customizeFormDataUserEntity:{}", customizeFormDataUserEntity);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            //神策上报埋点--提交表单
            marketingStatLogPersistorManger.sendCustomizeFormDataUserData(ea, customizeFormDataUserEntity.getId(), arg.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_H5);
        } else {
            // 更新数据
            customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getEnrollId(), ea);
            if (customizeFormDataUserEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.fsCustomizeFormDataEnroll oldData is null enrollId:{}", arg.getEnrollId());
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            BeanUtil.appendCopyCustomizeFormDataEnrollIgnoreNull(arg.getSubmitContent(), customizeFormDataUserEntity.getSubmitContent());
            // 创建营销推广来源对象
            arg.getSubmitContent().setForceSynUtm(true);
            String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            // 更新报名数据
            customizeFormDataUserDAO.updateCustomizeFormDataEnrollDataById(customizeFormDataUserEntity);
            customizeFormDataEnrollResult.setEnrollId(arg.getEnrollId());
        }
        // 特殊物料逻辑处理
        CustomizeFormDataUserEntity finalCustomizeFormDataUserEntity = customizeFormDataUserEntity;
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                customizeFormDataManager.handlerSpecialTypeEnrollData(finalCustomizeFormDataUserEntity);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        //单独处理存入其他任意对象
        if (Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(), SaveCrmObjectTypeEnum.OBJ.getType())) {
            CustomizeFormDataUserEntity finalSaveCustomizeFormDataUserEntity = customizeFormDataUserEntity;
            ThreadPoolUtils.executeWithTraceContext(()->{
                boolean updateData = !StringUtils.isEmpty(finalSaveCustomizeFormDataUserEntity.getExtraDataId());
                customizeFormDataManager.saveObjectToCrm(customizeFormDataEntity, finalSaveCustomizeFormDataUserEntity,updateData);
//                if(StringUtils.isNotBlank(arg.getCtaId())) {
//                    ctaService.recordCustomerDataEnroll(MarketingUserActionChannelType.OFFICIAL_WEB_SITE, arg);
//                }
            },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            return new Result<>(SHErrorCode.SUCCESS, customizeFormDataEnrollResult);
        }
        // 处理保利威直播无需报名可看直播
        ThreadPoolUtils.execute(() -> {
            polyvManager.handlePolyvUser(arg.getMarketingEventId(), arg.getSubmitContent(),ea);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        // 将数据同步至CRM线索
        if (synchronousCRM) {
            try {
                if (StringUtils.isBlank(customizeFormDataUserEntity.getLeadId())) {
                   boolean success = customizeFormDataManager.saveEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                   if (success){
                       //上报神策埋点-创建线索
                       String leadId = customizeFormDataUserEntity.getSubmitContent() == null ? null : customizeFormDataUserEntity.getSubmitContent().getLeadId();
                       marketingStatLogPersistorManger.sendLeadData(ea, leadId, arg.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_H5);
                   }

                    String phone = arg.getSubmitContent().getPhone();
                    if (success && customizeFormDataEntity.getFormMoreSetting().isSyncToMember() && StringUtils.isNotBlank(phone)) {
                        Optional<ObjectData> memberByEaAndPhone = memberManager.getMemberByEaAndPhone(ea, phone);
                        if (!memberByEaAndPhone.isPresent()) {
                            MemberConfigEntity memberConfig = memberManager.tryInitMemberConfig(ea);
                            CustomizeFormDataUserEntity dbData = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserId, ea);
                            Optional<String> optionalMember = memberManager.saveLeadToMemberAndSpreadData(ea, dbData, null);
                            //optionalMember.ifPresent(memberId -> wxServiceUserMemberBindDao.insertIgnore(ea, arg.getWxAppId(), arg.getOpenId(), memberId));
                            if (optionalMember.isPresent()) {
                                if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER) {
                                    Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createFsPayOrder(
                                            customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(),
                                            customizeFormDataEntity.getCreateBy(), crmMemberId, arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
                                    if (!createPayOrderResult.isSuccess()) {
                                        return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
                                    }
                                    CreateFsPayOrderResult data = createPayOrderResult.getData();
                                    BeanUtils.copyProperties(data, customizeFormDataEnrollResult);
                                    customizeFormDataUserEntity.setPayOrderId(data.getOrderNo());
                                    customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(data.getOrderNo(), customizeFormDataUserEntity.getId(), ea);
                                }
                            }
                        }
                    }

                } else {
                    // 更新crm数据
                    customizeFormDataManager.updateEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                }
            } catch (Exception e) {
                log.warn("CustomizeFormDataServiceImpl.fsCustomizeFormDataEnroll error e:{}", e);
            }
        }
        CustomizeFormDataUserEntity sendNoticeEntity = customizeFormDataUserEntity;
        ThreadPoolUtils.execute(() -> {
            // 存入线索表成功但缺失crm映射字段需要发送失败通知
            customizeFormDataManager.sendNotice(ea, sendNoticeEntity);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        handleCampaignData(customizeFormDataEntity.getEa(), customizeFormDataEnrollResult.getEnrollId(), null, null, customizeFormDataUserEntity, null, null, null, arg.getNeedSignIn(),arg.getTagId());
        if (FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER) {
            Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createFsPayOrder(
                    customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(),
                    customizeFormDataEntity.getCreateBy(), crmMemberId, arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
            if (!createPayOrderResult.isSuccess()) {
                return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
            }
            CreateFsPayOrderResult data = createPayOrderResult.getData();
            BeanUtils.copyProperties(data, customizeFormDataEnrollResult);
            customizeFormDataUserEntity.setPayOrderId(data.getOrderNo());
            customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(data.getOrderNo(), customizeFormDataUserEntity.getId(), arg.getEa());
        }
//        if(StringUtils.isNotBlank(arg.getCtaId())) {
//            ThreadPoolUtils.execute(() -> ctaService.recordCustomerDataEnroll(MarketingUserActionChannelType.OFFICIAL_WEB_SITE, arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
//        }
        return new Result<>(SHErrorCode.SUCCESS, customizeFormDataEnrollResult);
    }

    public void handleCampaignData(String ea, String customizeFormDataEnrollId, Integer qrSourceType, String qrSourceId, CustomizeFormDataUserEntity customizeFormDataUserEntity, String wxAppId, String openId, String fingerPrint, Boolean needSignIn,String tagId) {
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getMarketingEventId()) || customizeFormDataUserEntity.getObjectType().equals(ObjectTypeEnum.ACTIVITY.getType())
            || customizeFormDataUserEntity.getObjectType().equals(ObjectTypeEnum.ACTIVITY_INVITATION.getType())) {
            int corpId = eieaConverter.enterpriseAccountToId(ea);
            // 如果是直播，并且直播在直播中，这里同步写入报名数据，以为前端依赖报名数据进行跳转
            MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(corpId, customizeFormDataUserEntity.getMarketingEventId());
            if (marketingLiveEntity != null && marketingLiveEntity.getStatus() != null &&
                    marketingLiveEntity.getStatus() == LiveStatusEnum.PROCESSING.getStatus()) {
                handCampaignDataInner(ea, customizeFormDataEnrollId, qrSourceType, qrSourceId, customizeFormDataUserEntity,
                        wxAppId, openId, fingerPrint, needSignIn, tagId);
            } else {
                ThreadPoolUtils.execute(() -> {
                    handCampaignDataInner(ea, customizeFormDataEnrollId, qrSourceType, qrSourceId, customizeFormDataUserEntity,
                            wxAppId, openId, fingerPrint, needSignIn, tagId);
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
        }
    }

    public void handCampaignDataInner(String ea, String customizeFormDataEnrollId, Integer qrSourceType, String qrSourceId, CustomizeFormDataUserEntity customizeFormDataUserEntity, String wxAppId, String openId, String fingerPrint, Boolean needSignIn, String tagId) {
        String campaignMergeDataId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(ea, customizeFormDataEnrollId, true);
        customizeFormDataUserEntity.setCampaignId(campaignMergeDataId);
        if (customizeFormDataUserEntity.getObjectType().equals(ObjectTypeEnum.ACTIVITY.getType())
            || customizeFormDataUserEntity.getObjectType().equals(ObjectTypeEnum.ACTIVITY_INVITATION.getType())) {
            conferenceManager.createConferenceTicketAndAttachedInfo(ea, customizeFormDataUserEntity, qrSourceType, qrSourceId, campaignMergeDataId);
            if (dingManager.isDingAddressbook(ea)) {
                //钉钉版发
                sendConferenceDingdingReviewMessage(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(),ea);
            } else {
                //非钉钉版发
                sendActivityNotificationSms(ea, customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity);
                sendConferenceIdReviewMessage(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataEnrollId, ea);
                sendConferenceQywxReviewMessage(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), ea);
            }
            customerCustomizeFormDataManager.addEnrollObject(ea, customizeFormDataEnrollId);

            String conferenceId = activityManager.getActivityIdByObject(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), null, null);
            if (StringUtils.isNotBlank(wxAppId) && StringUtils.isNotBlank(openId)) {
                marketingFlowInstanceOuterService
                    .enrollOrCheckInConference(ea, wxAppId, openId, customizeFormDataUserEntity.getSubmitContent().getPhone(), customizeFormDataUserEntity.getSubmitContent().getEmail(), conferenceId, ConferenceEnrollOrCheckInEnum.ENROLL);
            }

            if(StringUtils.isNotBlank(fingerPrint)) {
                marketingFlowInstanceOuterService.enrollOrCheckInConferenceByH5(ea, fingerPrint, customizeFormDataUserEntity.getSubmitContent().getPhone(), customizeFormDataUserEntity.getSubmitContent().getEmail(), conferenceId,ConferenceEnrollOrCheckInEnum.ENROLL);
            }
            if (needSignIn != null && needSignIn) {
                conferenceManager.signInUserByCampaignId(ea, campaignMergeDataId, tagId);
            }
        } else {
            // 若带有市场活动且市场活动绑定会议则创建对应会议相关数据
            ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(customizeFormDataUserEntity.getMarketingEventId(), ea);
            if(activityEntity != null) {
                conferenceManager.createConferenceTicketAndAttachedInfoNoActivityObj(ea, customizeFormDataUserEntity, campaignMergeDataId);
                if (dingManager.isDingAddressbook(ea)) {
                    //钉钉版发
                    sendConferenceDingdingReviewMessage(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(),ea);
                } else {
                    sendActivityNotificationSms(ea, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), customizeFormDataUserEntity);
                    sendConferenceIdReviewMessage(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), customizeFormDataEnrollId, ea);
                    sendConferenceQywxReviewMessage(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), ea);
                }
                if (StringUtils.isNotBlank(wxAppId) && StringUtils.isNotBlank(openId)) {
                    marketingFlowInstanceOuterService
                        .enrollOrCheckInConference(ea, wxAppId, openId, customizeFormDataUserEntity.getSubmitContent().getPhone(), activityEntity.getId(), ConferenceEnrollOrCheckInEnum.ENROLL);
                }
                customerCustomizeFormDataManager.addEnrollObject(ea, customizeFormDataEnrollId);

                if(StringUtils.isNotBlank(fingerPrint)) {
                    marketingFlowInstanceOuterService.enrollOrCheckInConferenceByH5(ea, fingerPrint, customizeFormDataUserEntity.getSubmitContent().getPhone(), customizeFormDataUserEntity.getSubmitContent().getEmail(), activityEntity.getId(),
                        ConferenceEnrollOrCheckInEnum.ENROLL);
                }
                if (needSignIn != null && needSignIn) {
                    conferenceManager.signInUserByCampaignId(ea, campaignMergeDataId, tagId);
                }
            }
        }
    }

    private void sendConferenceDingdingReviewMessage(String objectId, Integer objectType,String ea) {
        ThreadPoolUtils.execute(() -> {
            String activityId = activityManager.getActivityIdByObject(objectId, objectType, null, null);
            conferenceManager.sendDingdingConferenceEnrollNoticeRealTime(activityId,ea);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    @Override
    public Result<GetCustomizeFormDataQrCodeResult> getCustomizeFormDataQrCode(String formId, String marketingEventId, String ea) {
        GetCustomizeFormDataQrCodeResult getCustomizeFormDataQrCodeResult = new GetCustomizeFormDataQrCodeResult();
        CustomizeFormQrCodeContainer customizeFormQrCodeContainer = customizeFormDataManager.createCustomizeFormDataQRCode(formId, marketingEventId, ea);
        getCustomizeFormDataQrCodeResult.setH5QrUrl(customizeFormQrCodeContainer.getH5Url());
        getCustomizeFormDataQrCodeResult.setH5QrPath(customizeFormQrCodeContainer.getH5Path());
        getCustomizeFormDataQrCodeResult.setMiniAppQrUrl(customizeFormQrCodeContainer.getMiniAppUrl());
        getCustomizeFormDataQrCodeResult.setMiniAppQrPath(customizeFormQrCodeContainer.getMiniAppPath());
        return new Result<>(SHErrorCode.SUCCESS, getCustomizeFormDataQrCodeResult);
    }

    @Override
    public Result<CreateCustomizeFormWXQrCodeResult> createCustomizeFormWXQrCode(CreateCustomizeFormWXQrCodeArg arg) {
        CreateCustomizeFormWXQrCodeResult createWxQrCodeResult = new CreateCustomizeFormWXQrCodeResult();
        // 查询报名ea
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getFormDataUserId(), arg.getEa());
        if (customizeFormDataUserEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.createCustomizeFormWXQrCode customizeFormDataUserEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId(),arg.getEa());
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.createCustomizeFormWXQrCode customizeFormDataEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        String ea = customizeFormDataEntity.getEa();
        // 查询wxAppId
        ModelResult<String> modelResult = outerServiceWechatService.transAppIdToWxAppId(arg.getAppId());
        if (!modelResult.isSuccess()) {
            log.warn("CustomizeFormDataServiceImpl.createCustomizeFormWXQrCode modelResult is not success modelResult:{}", modelResult);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        CreateParamQrCodeVO createParamQrCodeVO = new CreateParamQrCodeVO();
        createParamQrCodeVO.setEa(ea);
        createParamQrCodeVO.setWxAppId(modelResult.getResult());
        Integer fsUserId = wxTicketManager.getEnterpriseAdminInfo(ea);
        createParamQrCodeVO.setFsUserId(fsUserId);
        createParamQrCodeVO.setActionName(QrActionType.QR_LIMIT_STR_SCENE.name());
        createParamQrCodeVO.setExpireSeconds(WxTicketManager.DEFAULT_QR_CODE_EXPIRE_TIME);
        createParamQrCodeVO.setSceneType(ParamQrCodeSceneTypeConstants.CUSTOMIZE_FORM_DATA_SCENE_TYPE);
        Map<String, Object> param = Maps.newHashMap();
        param.put("formDataUserId", arg.getFormDataUserId());
        param.put("ea", ea);
        createParamQrCodeVO.setParams(param);
        ModelResult<CreateParamQrCodeResult> qrCodeResultModelResult = qrCodeService.createParamQrCode(createParamQrCodeVO);
        if (!qrCodeResultModelResult.isSuccess() || StringUtils.isBlank(qrCodeResultModelResult.getResult().getTicketUrl())) {
            log.warn("CustomizeFormDataServiceImpl.createCustomizeFormWXQrCode qrCodeResultModelResult not success createParamQrCodeVO:{}", createParamQrCodeVO);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        createWxQrCodeResult.setQrUrl(qrCodeResultModelResult.getResult().getTicketUrl());
        return new Result<>(SHErrorCode.SUCCESS, createWxQrCodeResult);
    }

    @Override
    public Result<PageResult<QueryFormUserDataResult>> queryMultipleFormUserData(QueryMultipleFormUserDataArg arg) {
        if(arg.getFormUsage() == null){
            arg.setFormUsage(FormDataUsage.COLLECT_LEADS.getUsage());
        }
        PageResult<QueryFormUserDataResult> result = queryMultipleFormUserDataManager.getUserDataBySourceType(arg);
        return Result.newSuccess(result);
    }

    @Override
    public Result<ExportEnrollsDataResult> exportMultipleFormEnrollsData(ExportMultipleFormEnrollsDataArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            if(arg.getFormUsage() == null){
                arg.setFormUsage(FormDataUsage.COLLECT_LEADS.getUsage());
            }
            ExportEnrollsDataResult result = queryMultipleFormUserDataManager.buildMultipleFormEnrollsData(arg.getSourceType(), arg.getSourceId(), arg.getFormUsage(), arg.getEa());
            String filename = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_3096) + ".xlsx";
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            ExcelUtil.fillContent(xssfSheet, result.getTitleList(), result.getEnrollInfoList());
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, filename, arg.getEa(), arg.getFsUserId());
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result reImportDataToCrm(ReImportDataToCrmArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            customizeFormDataManager.saveFromDataByEnrollids(arg.getEa(), arg.getIds());
            //发送文件助手提醒
            String text = "您于" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "提交的重新存入线索任务已经完成。您可以前往重新存入的页面查看线索存入情况。";
            pushSessionManager.pushTextToFileAssistant(text, arg.getEa(), arg.getUserId());
        },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    @FilterLog
    public Result<AreaContainerResult> getAreaData(GetAreaDataArg arg) {
        AreaContainer areaContainer = areaManager.buildAreaData();
        if (areaContainer == null) {
            log.warn("CustomizeFormDataServiceImpl.getAreaData error");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess(BeanUtil.copy(areaContainer, AreaContainerResult.class));
    }

    @Override
    public Result<AreaByKeywordResult> getAreaNameByKeyword(String ea, String keyword, Integer areaType) {
        if (StringUtils.isBlank(ea)) {
            log.error("CustomizeFormDataServiceImpl.getAreaNameByKeyword ea is null. keyword={}", keyword);
            return Result.newSuccess();
        }
        if (areaType == null) {
            areaType = InternationalAreaTypeEnum.INTERNAL_AREA.getType();
        }
        QueryAreaInfoResult queryAreaInfoResult = null;
        try {
            queryAreaInfoResult = areaManager.getAreaNameByKeyword(ea, keyword);
            if (queryAreaInfoResult == null) {
                log.warn("CustomizeFormDataServiceImpl.getAreaNameByKeyword error ea:{} keyword:{}", ea, keyword);
                return Result.newSuccess();
            }
        }catch (Exception e){
            log.error("CustomizeFormDataServiceImpl.getAreaNameByKeyword exception ea:{}, keyword:{}, exception:", ea, keyword, e);
            return Result.newSuccess();
        }

        AreaByKeywordResult result = new AreaByKeywordResult();
        List<AreaByKeywordResult.ZoneInfo> optionList = Lists.newArrayList();
        result.setOptionList(optionList);
        for (QueryAreaInfoResult.ZoneInfo zoneInfo : queryAreaInfoResult.getOptionList()) {
            AreaByKeywordResult.ZoneInfo zoneResult = new AreaByKeywordResult.ZoneInfo();
            if (areaType.equals(InternationalAreaTypeEnum.INTERNAL_AREA.getType())) {
                if (zoneInfo.getParentCodes().contains(CHINA_AREA_CODE)) {
                    zoneResult.setCode(zoneInfo.getCode());
                    zoneResult.setLabel(zoneInfo.getLabel());
                    zoneResult.setParentCodes(zoneInfo.getParentCodes());
                    zoneResult.setParentLabels(zoneInfo.getParentLabels());
                    optionList.add(zoneResult);
                }
            }else{
                zoneResult.setCode(zoneInfo.getCode());
                zoneResult.setLabel(zoneInfo.getLabel());
                zoneResult.setParentCodes(zoneInfo.getParentCodes());
                zoneResult.setParentLabels(zoneInfo.getParentLabels());
                optionList.add(zoneResult);
            }
        }

        return Result.newSuccess(result);
    }

    //根据父级id获取子级信息
    @Override
    public Result<AreaByParentResult> getZoneByParent(String ea, String parentId, Integer cascadeLevel) {
        //参数sql注入校验
        if (SqlInjectUtil.simpleContainsSqlInjection(parentId)) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        ZoneByParentResult zoneByParentResult = null;
        try {
            zoneByParentResult = areaManager.getZoneByParentId(ea, parentId, cascadeLevel);
            if (zoneByParentResult == null) {
                log.warn("CustomizeFormDataServiceImpl.getZoneByParent return null ea:{} parentId:{}", ea, parentId);
                return Result.newSuccess();
            }
        }catch (Exception e){
            log.error("CustomizeFormDataServiceImpl.getZoneByParent exception ea:{}, parentId:{}, cascadeLevel:{}, exception:", ea, parentId, cascadeLevel, e);
            return Result.newSuccess();
        }

        AreaByParentResult result = new AreaByParentResult();
        List<AreaByParentResult.ZoneInfo> zoneInfoList = new ArrayList<>();
        result.setZoneInfoList(zoneInfoList);
        for (ZoneByParentResult.ZoneInfo zoneInfo : zoneByParentResult.getOptionList()) {
            if (zoneInfo != null) {
                log.info("CustomizeFormDataServiceImpl.getZoneByParent zoneInfo:{}", zoneInfo);
            }
            AreaByParentResult.ZoneInfo zone = new AreaByParentResult.ZoneInfo();
            zone.setLabel(zoneInfo.getLabel());
            zone.setValue(zoneInfo.getValue());
            zone.setType(zoneInfo.getType());
            zone.setParentValue(zoneInfo.getParentValue());
            zoneInfoList.add(zone);
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<LocationResult> batchQueryLocationInfo(String ea, List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            log.error("CustomizeFormDataServiceImpl.batchQueryLocationInfo codes is null ea:{}", ea);
            return Result.newSuccess();
        }
        LocationResult locationResult = areaManager.batchQueryLocationInfo(ea, codes);
        return Result.newSuccess(locationResult);
    }

    private void sendActivityNotificationSms(String ea, String objectId, Integer objectType, CustomizeFormDataUserEntity customizeFormDataUserEntity){
        if (customizeFormDataUserEntity.getSubmitContent() != null && StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getPhone())) {
            String activityId = activityManager.getActivityIdByObject(objectId, objectType, null, null);
            ActivityEntity activityEntity = activityDAO.getById(activityId,customizeFormDataUserEntity.getEa());
            if (activityEntity == null) {
                return;
            }
            Map<String, ExtraSmsParamObject> extraSmsParamObjectMap = Maps.newHashMap();
            if (StringUtils.isNotBlank(customizeFormDataUserEntity.getCampaignId())) {
                CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(ea, customizeFormDataUserEntity.getCampaignId());
                if (campaignMergeDataEntity != null) {
                    conferenceManager.buildTicketParam(activityId, activityEntity.getEa(), campaignMergeDataEntity, extraSmsParamObjectMap);
                }
            }
            if (activityEntity.getEnrollReview()) {
                ThreadPoolUtils.execute(() -> {
                    conferenceManager.sendConferenceNotification(ConferenceNotificationTypeEnum.ENROLL_AUDIT.getType(), Lists.newArrayList(customizeFormDataUserEntity.getSubmitContent().getPhone()), activityEntity, Maps.newHashMap());
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            } else {
                ThreadPoolUtils.execute(() -> {
                    conferenceManager.sendConferenceNotification(ConferenceNotificationTypeEnum.ENROLL_SUCCESS.getType(), Lists.newArrayList(customizeFormDataUserEntity.getSubmitContent().getPhone()), activityEntity, extraSmsParamObjectMap);
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }

        }
    }

    private Result checkObjectLegitimacy(String ea, String marketingEventId, String objectId, Integer objectType) {
        //微页面子页面转换成微页面站点
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == objectType) {
            HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getById(ea, objectId);
            if (hexagonPageEntity != null){
                objectType = ObjectTypeEnum.HEXAGON_SITE.getType();
                objectId = hexagonPageEntity.getHexagonSiteId();
            }
        }
        log.info("checkObjectLegitimacy ea={},marketingEventId={},objectId={},objectType={}",ea,marketingEventId,objectId,objectType);
        ActivityEntity activityEntity = null;
        if (objectType == ObjectTypeEnum.ACTIVITY.getType() || objectType == ObjectTypeEnum.ACTIVITY_INVITATION.getType()) {
            String activityId = activityManager.getActivityIdByObject(objectId, objectType, null, null);
            activityEntity = activityDAO.getById(activityId,ea);
        } else if (StringUtils.isNotBlank(ea) && StringUtils.isNotBlank(marketingEventId)) {
            activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        }
        if (activityEntity != null) {
            if (activityEntity.getScale() != null && activityEntity.getEnrollCount() != null && activityEntity.getEnrollCount() >= activityEntity.getScale()) {
                return new Result<>(SHErrorCode.ACTIVITY_FULL);
            }
            // 获取当前推广内容
//            ContentMarketingEventMaterialRelationEntity contentMarketingEventMaterialRelationEntity = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdAndObjectTypeAndObjectId(ea, marketingEventId, objectType, objectId);
//            if (contentMarketingEventMaterialRelationEntity != null && contentMarketingEventMaterialRelationEntity.getIsApplyObject()) {
//                // 当设定为报名内容，才需要受到活动截止时间的限制
//                if (activityEntity.getEndTime() != null && activityEntity.getEndTime().getTime() < new Date().getTime()) {
//                    return new Result<>(SHErrorCode.ACTIVITY_END);
//                }
//            }

            if (activityEntity.getEnrollEndTime() != null && isEnrollFromData(ea,marketingEventId,objectId,objectType) && activityEntity.getEnrollEndTime().getTime() < new Date().getTime()) {
                return new Result<>(SHErrorCode.ENROLL_STOPPED);
            }
            if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DISABLED.getStatus()) {
                return new Result<>(SHErrorCode.ACTIVITY_DISABLE);
            }
            if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DELETED.getStatus()) {
                return new Result<>(SHErrorCode.ACTIVITY_DELETED);
            }
        }
        //添加直播营销,活动营销判断截止时间
        ActivityEnrollTimeConfigEntity configEntity = activityEnrollTimeConfigDAO.queryEnrollTime(ea, marketingEventId);
        if (configEntity != null) {
            if (configEntity.getStatus() != null && configEntity.getStatus() == ActivityEnrollTimeStatusEnum.ENABLED.getStatus() && isEnrollFromData(ea,marketingEventId,objectId,objectType) && configEntity.getEnrollTime().getTime() < new Date().getTime()) {
                if (StringUtils.isNotBlank(configEntity.getEnrollTip())){
                    return new Result<>(21002,configEntity.getEnrollTip());
                }
                return new Result<>(SHErrorCode.ENROLL_STOPPED);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS);
    }

    /**
     * 判断推广内容是否为报名表单
     * @param objectId
     * @param objectType
     * @param ea
     * @param marketingEventId
     */
    private boolean isEnrollFromData(String ea, String marketingEventId, String objectId, Integer objectType) {
        //如果是会议活动主页,直接进行默认为报名表单
        if (ObjectTypeEnum.ACTIVITY.getType() == objectType) {
            return true;
        }
        //只要是活动营销下的推广内容,都默认为报名表单
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (objectData != null){
            String eventForm = objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName());
            if (StringUtils.isNotBlank(eventForm) && MarketingEventFormEnum.ONLINE_MARKETING.getValue().equals(eventForm)) {
                return true;
            }
        }
        ContentMarketingEventMaterialRelationEntity contentEntity = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdAndObjectTypeAndObjectId(ea, marketingEventId, objectType, objectId);
        return contentEntity != null && contentEntity.getIsApplyObject();
    }

    /**
     * 发送会议审核通知
     * @param objectId
     * @param objectType
     */
    private void sendConferenceIdReviewMessage(String objectId, Integer objectType, String customizeFormDataUserId, String ea) {
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                String activityId = activityManager.getActivityIdByObject(objectId, objectType, null, null);
                conferenceOutService.sendReviewMessage(activityId, customizeFormDataUserId,ea);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    /**
     * 发送企业微信小程序审核通知
     * @param objectId
     * @param objectType
     */
    private void sendConferenceQywxReviewMessage(String objectId, Integer objectType,String ea) {
        ThreadPoolUtils.execute(() -> {
            String activityId = activityManager.getActivityIdByObject(objectId, objectType, null, null);
            conferenceManager.sendQywxConferenceEnrollNoticeRealTime(activityId,ea);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    private void buildMarketingEventIdByEnrollData(CustomizeFormDataEnrollArg arg) {
        if (arg.getObjectType() == ObjectTypeEnum.ACTIVITY.getType() || arg.getObjectType() == ObjectTypeEnum.ACTIVITY_INVITATION.getType()) {
            String activityId = activityManager.getActivityIdByObject(arg.getObjectId(), arg.getObjectType(), null, null);
            ActivityEntity activityEntity = activityDAO.getById(activityId,arg.getEa());
            arg.setMarketingEventId(activityEntity.getMarketingEventId());
            return;
        }
        if (StringUtils.isBlank(arg.getMarketingEventId()) && StringUtils.isNotBlank(arg.getMarketingActivityId())) {
            arg.setMarketingEventId(marketingActivityExternalConfigDao.getMarketingEventIdByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId()));
        }
    }

    @Override
    public Result<GetPayFormResult> getPayFormResult(String wxAppId, String wxOpenId, String payOrderId, String ea) {
        GetPayFormResult getPayFormResult = new GetPayFormResult();
        CustomizeFormDataUserEntity customizeFormDataUser = customizeFormDataUserDAO.getByPayOrderId(payOrderId, ea);
        Preconditions.checkState(customizeFormDataUser != null);
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUser.getFormId(),ea);
        getPayFormResult.setFieldInfos(customizeFormDataEntity.getFormBodySetting());
        QueryFormUserDataResult queryFormUserDataResult = BeanUtil.copy(customizeFormDataUser, QueryFormUserDataResult.class);
        PayOrderEntity payOrder = payOrderManager.syncAndGetPayOrderByIds(ImmutableSet.of(payOrderId),ea).get(payOrderId);
        Preconditions.checkArgument(wxAppId.equals(payOrder.getWxAppId()));
        Preconditions.checkArgument(wxOpenId.equals(payOrder.getWxOpenId()));
        queryFormUserDataResult.setPayOrderId(payOrder.getId());
        queryFormUserDataResult.setPayOrderState(payOrder.getState());
//        queryFormUserDataResult.setPayOrderTotalFee(payOrder.getTotalFee());
        queryFormUserDataResult.setPayDescription(payOrder.getDescription());
        getPayFormResult.setFormUserResult(queryFormUserDataResult);
        return Result.newSuccess(getPayFormResult);
    }

    @Override
    public Result<RepayResult> repayOrder(String wxAppId, String wxOpenId, String payOrderId,String ea) {
        RepayResult repayResult = new RepayResult();
        Result<CreateMiniAppPayOrderResult> result = payOrderManager.repay(payOrderId,ea);
        if(!result.isSuccess()){
            return Result.newError(result.getErrCode(), result.getErrMsg());
        }
        BeanUtils.copyProperties(result.getData(), repayResult);
        return Result.newSuccess(repayResult);
    }

    private Result<ExecuteEnrollCustomizeFunctionResult> executeCustomizeFunctionWithSubmit(ExecuteEnrollCustomizeFunctionArg arg) {
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getEnrollId(), arg.getEa());
        if (customizeFormDataUserEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithSubmit customizeFormDataUserEntity is null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId(),arg.getEa());
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithSubmit customizeFormDataEntity is null");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        Map<String, Object> functionValueMap = Maps.newHashMap();
        Map<String, String> picMap = customizeFormDataManager.conversionEnrollDataPic(customizeFormDataEntity.getEa(), Lists.newArrayList(customizeFormDataUserEntity));
        Map<String, String> fileAttachmentMap = customizeFormDataManager.conversionEnrollDataFileAttachment(customizeFormDataEntity.getEa(), Lists.newArrayList(customizeFormDataUserEntity));
        try {
            // 转换省市区
            log.info("getSubmitContent values:{}");
            Map<String, String> areaCodeMap = new HashMap<>();
            if (customizeFormDataUserEntity.getSubmitContent() != null && customizeFormDataUserEntity.getSubmitContent().getCountry() != null) {
                areaCodeMap.put("country", customizeFormDataUserEntity.getSubmitContent().getCountry());
            }
            if (customizeFormDataUserEntity.getSubmitContent() != null && customizeFormDataUserEntity.getSubmitContent().getProvince() != null) {
                areaCodeMap.put("province", customizeFormDataUserEntity.getSubmitContent().getProvince());
            }
            if (customizeFormDataUserEntity.getSubmitContent() != null && customizeFormDataUserEntity.getSubmitContent().getCity() != null) {
                areaCodeMap.put("city", customizeFormDataUserEntity.getSubmitContent().getCity());
            }
            if (customizeFormDataUserEntity.getSubmitContent() != null && customizeFormDataUserEntity.getSubmitContent().getDistrict() != null) {
                areaCodeMap.put("district", customizeFormDataUserEntity.getSubmitContent().getDistrict());
            }
            log.info("areaCodeMap values:{}", areaCodeMap);
            customizeFormDataManager.buildAreaInfoByEnrollData(Lists.newArrayList(customizeFormDataUserEntity));
            for (FieldInfo fieldInfo : customizeFormDataEntity.getFormBodySetting()) {
                if (StringUtils.isBlank(fieldInfo.getApiName())) {
                    continue;
                }
                Object result = customizeFormDataManager.formatEnrollDataIncludeSpecialField(fieldInfo, customizeFormDataUserEntity, picMap, true);
                if ((fieldInfo.getType().equals(FieldInfo.Type.FILE_ATTACHMENT.getValue()))){
                    List<FileAttachmentContainer> fileContainerList = (List<FileAttachmentContainer>) result;
                    List<String> fileUrls = fileContainerList.stream().map(file -> fileAttachmentMap.get(file.getPath())).collect(Collectors.toList());
                    result = fileUrls;
                }
                //设置自定义apiname
                if (customizeFormDataEntity.getCustomizeApinameMapping() != null && customizeFormDataEntity.getCustomizeApinameMapping().get(fieldInfo.getApiName()) != null){
                    functionValueMap.put(customizeFormDataEntity.getCustomizeApinameMapping().get(fieldInfo.getApiName()).toString(), result);
                }else {
                    functionValueMap.put(fieldInfo.getApiName(), result);
                }
            }

            if (areaCodeMap.get("country") != null){
                functionValueMap.put("countryCode", areaCodeMap.get("country"));
            }
            if (areaCodeMap.get("province") != null){
                functionValueMap.put("provinceCode", areaCodeMap.get("province"));
            }
            if (areaCodeMap.get("city") != null){
                functionValueMap.put("cityCode", areaCodeMap.get("city"));
            }
            if (areaCodeMap.get("district") != null){
                functionValueMap.put("districtCode", areaCodeMap.get("district"));
            }
            log.info("functionValueMap values:{}", functionValueMap);
        } catch (Exception e) {
            log.warn("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithSubmit build field error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        ExecuteCustomizeFunctionArg executeCustomizeFunctionArg = new ExecuteCustomizeFunctionArg();
        List<Parameters> parametersList = Lists.newArrayList();
        executeCustomizeFunctionArg.setApiName(arg.getApiName());
        executeCustomizeFunctionArg.setParameters(parametersList);
        Parameters parameters = new Parameters();
        parameters.setName(CustomizeFunctionConstants.ENROLL_DATA_NAME);
        parameters.setType(CustomizeFunctionConstants.PARAMETERS_TYPE.get("Map"));
        parameters.setValue(functionValueMap);
        parametersList.add(parameters);
        if (arg.getAdditionalMsg() != null){
            functionValueMap.put("functionAdditionalMsg", arg.getAdditionalMsg());
        }


        Object result = customizeFunctionManager.executeCustomizeFunction(GsonUtil.getGson().toJson(executeCustomizeFunctionArg), customizeFormDataEntity.getEa(), -10000);
        if (result == null) {
            log.warn("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithSubmit result is null arg:{}", executeCustomizeFunctionArg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        ExecuteEnrollCustomizeFunctionResult executeEnrollCustomizeFunctionResult = new ExecuteEnrollCustomizeFunctionResult();
        executeEnrollCustomizeFunctionResult.setResult(result);
        // 通过页面名获取页面id
        Map<String, Object> resultMap = GsonUtil.getGson().fromJson(GsonUtil.getGson().toJson(result), new TypeToken<Map>() {
        }.getType());
        log.info("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithSubmit resultMap:{}", resultMap);
        if (MapUtils.isEmpty(resultMap)) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        Object functionValue = resultMap.get(CustomizeFunctionConstants.FUNCTION_RESULT);
        if (functionValue == null) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        Map<String, Object> valueMap = GsonUtil.getGson().fromJson(GsonUtil.getGson().toJson(functionValue), new TypeToken<Map>() {}.getType());
        if (MapUtils.isEmpty(valueMap)) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        String jumpPage = valueMap.get(CustomizeFunctionConstants.JUMP_PAGE) + "";
        if (StringUtils.isBlank(jumpPage)) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getById(arg.getEa(), customizeFormDataUserEntity.getObjectId());
        if (hexagonPageEntity == null) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getHexagonPageBySiteIdAndPageName(arg.getEa(), hexagonPageEntity.getHexagonSiteId(), jumpPage);
        if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
            executeEnrollCustomizeFunctionResult.setHexagonPageId(hexagonPageEntityList.get(0).getId());
        }
        return Result.newSuccess(executeEnrollCustomizeFunctionResult);
    }

    private Result<ExecuteEnrollCustomizeFunctionResult> executeCustomizeFunctionWithoutSubmit(ExecuteEnrollCustomizeFunctionArg arg){
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(arg.getObjectId()) || arg.getObjectType() == null) {
            log.warn("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithoutSubmit error ea is null arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        CustomizeFormDataUserEntity customizeFormDataUserEntity = new CustomizeFormDataUserEntity();
        customizeFormDataUserEntity.setSubmitContent(new CustomizeFormDataEnroll());
        ExecuteCustomizeFunctionArg executeCustomizeFunctionArg = new ExecuteCustomizeFunctionArg();
        List<Parameters> parametersList = Lists.newArrayList();
        executeCustomizeFunctionArg.setApiName(arg.getApiName());
        executeCustomizeFunctionArg.setParameters(parametersList);
        Parameters parameters = new Parameters();
        parameters.setName(CustomizeFunctionConstants.ENROLL_DATA_NAME);
        parameters.setType(CustomizeFunctionConstants.PARAMETERS_TYPE.get("Map"));
        parameters.setValue(customizeFormDataUserEntity.getSubmitContent());
        parametersList.add(parameters);
        if (arg.getAdditionalMsg() != null) {
            customizeFormDataUserEntity.getSubmitContent().setFunctionAdditionalMsg(arg.getAdditionalMsg());
        }
        Object result = customizeFunctionManager.executeCustomizeFunction(GsonUtil.getGson().toJson(executeCustomizeFunctionArg), ea, -10000);
        if (result == null) {
            log.warn("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithoutSubmit result is null arg:{}", executeCustomizeFunctionArg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        ExecuteEnrollCustomizeFunctionResult executeEnrollCustomizeFunctionResult = new ExecuteEnrollCustomizeFunctionResult();
        executeEnrollCustomizeFunctionResult.setResult(result);
        Map<String, Object> resultMap = GsonUtil.getGson().fromJson(GsonUtil.getGson().toJson(result), new TypeToken<Map>() {
        }.getType());
        log.info("CustomizeFormDataServiceImpl.executeCustomizeFunctionWithoutSubmit resultMap:{}", resultMap);
        if (MapUtils.isEmpty(resultMap)) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }

        Object functionValue = resultMap.get(CustomizeFunctionConstants.FUNCTION_RESULT);
        if (functionValue == null) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        Map<String, Object> valueMap = GsonUtil.getGson().fromJson(GsonUtil.getGson().toJson(functionValue), new TypeToken<Map>() {}.getType());
        if (MapUtils.isEmpty(valueMap)) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        String jumpPage = valueMap.get(CustomizeFunctionConstants.JUMP_PAGE) + "";
        if (StringUtils.isBlank(jumpPage)) {
            return Result.newSuccess(executeEnrollCustomizeFunctionResult);
        }
        if (arg.getObjectType().equals(ObjectTypeEnum.HEXAGON_PAGE.getType())) {
            HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getById(ea, arg.getObjectId());
            if (hexagonPageEntity == null) {
                return Result.newSuccess(executeEnrollCustomizeFunctionResult);
            }
            List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getHexagonPageBySiteIdAndPageName(arg.getEa(), hexagonPageEntity.getHexagonSiteId(), jumpPage);
            if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
                executeEnrollCustomizeFunctionResult.setHexagonPageId(hexagonPageEntityList.get(0).getId());
            }
        }
        if (arg.getObjectType().equals(ObjectTypeEnum.HEXAGON_SITE.getType())){
            List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getHexagonPageBySiteIdAndPageName(arg.getEa(), arg.getObjectId(), jumpPage);
            if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
                executeEnrollCustomizeFunctionResult.setHexagonPageId(hexagonPageEntityList.get(0).getId());
            }
        }

        return Result.newSuccess(executeEnrollCustomizeFunctionResult);
    }

    @Override
    public Result<ExecuteEnrollCustomizeFunctionResult> executeEnrollCustomizeFunction(ExecuteEnrollCustomizeFunctionArg arg) {
        // 若没有报名id直接执行函数
        if (StringUtils.isBlank(arg.getEnrollId())) {
            return executeCustomizeFunctionWithoutSubmit(arg);
        }

        // 执行报名对应的函数
        return executeCustomizeFunctionWithSubmit(arg);
    }

    @Override
    public Result copyCustomizeForm(String formId, String newName, String ea, Integer fsUserId) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId,ea);
        if (customizeFormDataEntity == null) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        String oldName = customizeFormDataEntity.getFormHeadSetting().getName();
        AddCustomizeFormDataArg addCustomizeFormDataArg = BeanUtil.copy(customizeFormDataEntity, AddCustomizeFormDataArg.class);
        addCustomizeFormDataArg.setEa(ea);
        if (StringUtils.isNotEmpty(newName)) {
            addCustomizeFormDataArg.getFormHeadSetting().setName(newName);
        }
        addCustomizeFormDataArg.setFsUserId(fsUserId);
        addCustomizeFormDataArg.getFormHeadSetting().setHeadPhotoUrl(Lists.newArrayList());
        addCustomizeFormDataArg.setCrmFormFieldMap(BeanUtil.copy(customizeFormDataEntity.getCrmFormFieldMapV2(), FieldMappingResult.class));
        // 处理图片
        if (CollectionUtils.isNotEmpty(customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath())) {
            byte[] bytes = fileV2Manager.downloadAFile(customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath().get(0), customizeFormDataEntity.getEa());
            String newApath = fileV2Manager.uploadToApath(bytes, ImageUtil.getImageType(bytes), ea);
            if(StringUtils.isNotBlank(newApath)) {
                addCustomizeFormDataArg.getFormHeadSetting().setHeadPhotoPath(Lists.newArrayList(newApath));
            }
        }
        Result<AddCustomizeFormDataResult> result = addCustomizeFormData(addCustomizeFormDataArg, formId,false);
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(),
                oldName, OperateTypeEnum.COPY, MaterialTypeEnum.FORM.getName(), newName);
        if(result.isSuccess() && result.getData() != null) {
            ctaRelationDaoManager.copyAddCtaRelation(ea, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), formId, ea, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), result.getData().getId());
        }
        return result;
    }

    @Override
    public Result<BuildCrmObjectByEnrollDataResult> buildCrmObjectByEnrollData(BuildCrmObjectByEnrollDataArg arg) {
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getEnrollId(), arg.getEa());
        if (customizeFormDataUserEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.buildCrmObjectByEnrollData customizeFormDataUserEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId(),arg.getEa());
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.buildCrmObjectByEnrollData customizeFormDataEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        FieldMappings fieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
        Set<String> notNullCrmLeadFieldNames = crmV2Manager.getObjectFieldNameList(arg.getEa(), CrmObjectApiNameEnum.CRM_LEAD);
        boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmLeadFieldNames);
        if (!verifyResult) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_CRM_VERIFY_FIELDS_ERROR);
        }
        Map<String, Object> data = crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(customizeFormDataEntity, CrmObjectApiNameEnum.CRM_LEAD, customizeFormDataUserEntity.getSubmitContent());
        BuildCrmObjectByEnrollDataResult buildCrmObjectByEnrollDataResult = new BuildCrmObjectByEnrollDataResult();
        buildCrmObjectByEnrollDataResult.setResult(data);
        return Result.newSuccess(buildCrmObjectByEnrollDataResult);
    }

    @Override
    public Result bindEnrollDataAndCrmObject(BindEnrollDataAndCrmObjectArg arg) {
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getEnrollId(), arg.getEa());
        if (customizeFormDataUserEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.bindEnrollDataAndCrmObject customizeFormDataUserEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId(),arg.getEa());
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.bindEnrollDataAndCrmObject customizeFormDataEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        // 查询绑定的数据是否存在/作废
        try {
            ObjectData objectData = crmV2Manager.getDetail(arg.getEa(), -10000, arg.getObjectApiName(), arg.getObjectId());
            if (MapUtils.isEmpty(objectData)) {
                log.warn("CustomizeFormDataServiceImpl.bindEnrollDataAndCrmObject objectData is null:{}");
                return Result.newError(SHErrorCode.CUSTOMIZE_FORM_BIND_CRM_DATA_ERROR);
            }
        } catch (Exception e) {
            log.warn("CustomizeFormDataServiceImpl.bindEnrollDataAndCrmObject error e:{}", e);
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_BIND_CRM_DATA_ERROR);
        }
        CustomizeFormBindOtherCrmObject customizeFormBindOtherCrmObject = new CustomizeFormBindOtherCrmObject();
        if (arg.getObjectApiName().equals(CrmObjectApiNameEnum.CUSTOMER.getName()) || arg.getObjectApiName().equals(CrmObjectApiNameEnum.CONTACT.getName())) {
            customizeFormBindOtherCrmObject.setObjectId(arg.getObjectId());
            customizeFormBindOtherCrmObject.setApiName(arg.getObjectApiName());
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmObjectBind(SaveCrmStatusEnum.LINKED.getValue(), arg.getEnrollId(), customizeFormBindOtherCrmObject, arg.getEa());
        } else if (arg.getObjectApiName().equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.LINKED.getValue(), null, arg.getObjectId(), arg.getEa());
        } else {
            log.warn("CustomizeFormDataServiceImpl.bindEnrollDataAndCrmObject param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ThreadPoolUtils.execute(() -> {
            // 关联活动成员
            String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(arg.getEa(), arg.getEnrollId(), false);
            // 若为会议还需生成参会码
            customizeTicketManager.createConferenceCustomizeTicket(customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(), campaignId, arg.getEa(), customizeFormDataUserEntity.getMarketingEventId());

            // 生成会员信息
            try {
                if (arg.getObjectApiName().equals(CrmObjectApiNameEnum.CRM_LEAD.getName()) && customizeFormDataEntity.getFormMoreSetting().isSyncToMember()) {
                    String marketingPromotionSourceId = null;
                    if (customizeFormDataUserEntity.getSubmitContent() != null) {
                        marketingPromotionSourceId = customizeFormDataUserEntity.getSubmitContent().getMarketingPromotionSourceId();
                    }
                    memberManager.saveLeadToMember(arg.getEa(), arg.getObjectId(), marketingPromotionSourceId);
                }
            } catch (Exception e) {
                log.warn("CustomizeFormDataServiceImpl.bindEnrollDataAndCrmObject saveLeadToMember error e:{}", e);
            }

            String phone = null;
            String email =  customizeFormDataUserEntity.getSubmitContent().getEmail();;
            if (customizeFormDataUserEntity.getSubmitContent() != null && StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getPhone())) {
                phone = customizeFormDataUserEntity.getSubmitContent().getPhone();
            } else {
                ObjectData objectData = crmV2Manager.getDetail(arg.getEa(), -10000, arg.getObjectApiName(), arg.getObjectId());
                phone = objectData.get("tel") != null ? objectData.get("tel").toString() : null;
            }

            String triggerAction = "bindEnrollDataAndCrmObject";
            AssociationArg targetAssociationArg = new AssociationArg();
            Boolean isMiniAppChannel = false;
            String triggerSource = "";
            if (StringUtils.isNotBlank(customizeFormDataUserEntity.getWxAppId()) && StringUtils.isNotBlank(customizeFormDataUserEntity.getOpenId())) {
                targetAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
                targetAssociationArg.setEa(arg.getEa());
                targetAssociationArg.setPhone(phone);
                targetAssociationArg.setEmail(email);
                targetAssociationArg.setWxAppId(customizeFormDataUserEntity.getWxAppId());
                targetAssociationArg.setAssociationId(customizeFormDataUserEntity.getOpenId());
                triggerSource = ChannelEnum.WX_SERVICE.getDescription();
            } else if (StringUtils.isNotBlank(customizeFormDataUserEntity.getFingerPrint())) {
                targetAssociationArg.setAssociationId(customizeFormDataUserEntity.getFingerPrint());
                targetAssociationArg.setEa(arg.getEa());
                targetAssociationArg.setType(ChannelEnum.BROWSER_USER.getType());
                targetAssociationArg.setPhone(phone);
                targetAssociationArg.setEmail(email);
                triggerSource = ChannelEnum.BROWSER_USER.getDescription();
            } else if (StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())) {
                targetAssociationArg.setAssociationId(customizeFormDataUserEntity.getUid());
                targetAssociationArg.setEa(arg.getEa());
                targetAssociationArg.setType(ChannelEnum.MINIAPP.getType());
                targetAssociationArg.setEmail(email);
                targetAssociationArg.setPhone(phone);
                isMiniAppChannel = true;
                triggerSource = ChannelEnum.MINIAPP.getDescription();
            }
            targetAssociationArg.setTriggerAction(triggerAction);
            targetAssociationArg.setTriggerSource(triggerSource);

            ChannelEnum bindChannelType = ChannelEnum.getByApiName(arg.getObjectApiName());
            if (bindChannelType == null) {
                return;
            }
            // 关联对象
            AssociationArg sourceAssociationArg = new AssociationArg();
            sourceAssociationArg.setType(bindChannelType.getType());
            sourceAssociationArg.setEa(arg.getEa());
            sourceAssociationArg.setPhone(phone);
            sourceAssociationArg.setEmail(email);
            sourceAssociationArg.setAssociationId(arg.getObjectId());
            sourceAssociationArg.setTriggerAction(triggerAction);
            sourceAssociationArg.setTriggerSource(triggerSource);
            userMarketingAccountAssociationManager.bind(targetAssociationArg, sourceAssociationArg);

            String associationId = arg.getObjectId();
            ChannelEnum associationChannelType = bindChannelType;
            if (isMiniAppChannel) {
                associationChannelType = ChannelEnum.MINIAPP;
                associationId = customizeFormDataUserEntity.getUid();
            }
            // 增加标签
            objectTagManager.batchAddTagsToUserMarketings(arg.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(), customizeFormDataUserEntity.getSubmitContent().getEmail(),
                    associationChannelType.getType(), associationId, null, customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null,
                customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
            ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(customizeFormDataUserEntity.getMarketingEventId(), customizeFormDataEntity.getEa());
            if (activityEntity != null) {
                objectTagManager.batchAddTagsToUserMarketings(arg.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                        customizeFormDataUserEntity.getSubmitContent().getEmail(),
                    associationChannelType.getType(), associationId, null,
                    activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Data
    public class QueryFormUserContainer implements Serializable {

        private String objectId;

        private Integer objectType;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Result<EditObjectGroupResult> editCustomizeFormGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("CustomizeFormServiceImpl.editCustomizeFormGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("CustomizeFormServiceImpl.editCustomizeFormGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Result deleteCustomizeFormGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("CustomizeFormDataServiceImpl.deleteCustomizeFormGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Result setCustomizeFormGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("CustomizeFormDataServiceImpl.setCustomizeFormGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }
        List<CustomizeFormDataEntity> formDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(arg.getObjectIdList(),ea);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(formDataEntityList)) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        if (formDataEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.CUSTOMIZE_FORM.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        return Result.newSuccess();
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteCustomizeFormBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(arg.getIdList(),ea);
        if (CollectionUtils.isEmpty(customizeFormDataEntityList)) {
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        if (customizeFormDataEntityList.stream().anyMatch(e -> Objects.equals(CustomizeFormDataStatusEnum.NORMAL.getValue(), e.getStatus()))) {
            return new Result<>(SHErrorCode.UNBINDING_BEFORE_DELETING);
        }
        //删除的不用处理relation表吗？
        //List<CustomizeFormDataObjectEntity> customizeFormDataObjectEntityList = customizeFormDataObjectDAO.queryCustomizeFormDataObjectByFormIdList(arg.getIdList());
        List<String> idList = customizeFormDataEntityList.stream().map(CustomizeFormDataEntity::getId).collect(Collectors.toList());
        customizeFormDataDAOManager.updateCustomizeFormDataStatusBatch(ea, idList, fsUserId, CustomizeFormDataStatusEnum.DELETE.getValue());
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), arg.getIdList());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ea, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), arg.getIdList());
        ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), arg.getIdList());
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topCustomizeForm(String ea, Integer fsUserId, TopMaterialArg arg) {
        CustomizeFormDataEntity entity = customizeFormDataDAO.getCustomizeFormDataById(arg.getObjectId(),ea);
        if (entity == null) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopCustomizeForm(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.CUSTOMIZE_FORM.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), arg.getObjectId());

        return Result.newSuccess();
    }

    @Override
    public Result<Void> addCustomizeFormGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listCustomizeFormGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.values()) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类的 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(customizeFormDataDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(customizeFormDataDAO.queryAccessibleCount(ea, groupIdList, fsUserId));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(customizeFormDataDAO.queryCountCreateByMe(ea, fsUserId));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(customizeFormDataDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<PageResult<QueryFormUserDataResult>> queryFormUserDataForMarketingActivityIdAndSpreadFsUid(QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg arg) {

        if(arg==null || StringUtils.isBlank(arg.getMarketingActivityId()) || arg.getSpreadFsUid()==null || CollectionUtils.isEmpty(arg.getFromUserMarketingIds()) ||arg.getPageNum()==null || arg.getPageSize()==null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        FromUserMarketingStatisticSpreadRankingArg statisticSpreadRankingArg = BeanUtil.copy(arg, FromUserMarketingStatisticSpreadRankingArg.class);
        statisticSpreadRankingArg.setStartTime(arg.getStartDate());
        statisticSpreadRankingArg.setEndTime(arg.getEndDate());
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> pageResultResult = userMarketingStatisticService.pageFromUserMarketingActionStatisticSpreadRanking(statisticSpreadRankingArg);
        if(pageResultResult.getData()==null||pageResultResult.getData().getData()==null){
            return Result.newSuccess();
        }
        List<String> fingerPrintList = Lists.newArrayList();
        List<String> uidList = Lists.newArrayList();
        List<String> wxOpenIdList = Lists.newArrayList();
        for (UserMarketingActionStatisticResult o : pageResultResult.getData().getData()) {
            if(o.getProperties()!=null){
                if(o.getProperties().get("fingerPrint")!=null){
                    fingerPrintList.add((String) o.getProperties().get("fingerPrint"));
                }
                if(o.getProperties().get("uid")!=null){
                    uidList.add((String) o.getProperties().get("uid"));
                }
                if(o.getProperties().get("wxOpenId")!=null){
                    wxOpenIdList.add((String) o.getProperties().get("wxOpenId"));
                }
            }
        }
        PageResult<QueryFormUserDataResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        if (arg.getTime() == null) {
            arg.setTime(new Date().getTime());
        }
        pageResult.setTime(arg.getTime());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntitys = Lists.newArrayList();
        if(arg.getStartDate()!=null&&arg.getEndDate()!=null){
            customizeFormDataUserEntitys  = customizeFormDataUserDAO.pageFormUserDataForMarketingActivityIdAndSpreadFsUid(arg.getMarketingActivityId(), arg.getSpreadFsUid(),arg.getFromUserMarketingIds().get(0),arg.getKeyword(),
                    fingerPrintList,uidList,wxOpenIdList,new Date(arg.getStartDate()),new Date(arg.getEndDate()),true,page, arg.getEa());
        }else {
            customizeFormDataUserEntitys = customizeFormDataUserDAO.pageFormUserDataForMarketingActivityIdAndSpreadFsUid(arg.getMarketingActivityId(), arg.getSpreadFsUid(),arg.getFromUserMarketingIds().get(0),arg.getKeyword(),
                    fingerPrintList,uidList,wxOpenIdList,null,null,false,page, arg.getEa());
        }
        if(customizeFormDataUserEntitys.size()==0){
            return new Result<>(SHErrorCode.SUCCESS, pageResult);
        }
        List<QueryFormUserDataResult> result = getQueryFormUserDataResults(customizeFormDataUserEntitys, arg.getEa());
        pageResult.setResult(result);
        pageResult.setTotalCount(page.getTotalNum());
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public List<QueryFormUserDataResult> querySpreadFormDataStatistic(String ea, List<String> marketingActivityIds, List<Integer> userIds, Date startDate, Date endDate, Page page) {
        List<QueryFormUserDataResult> queryFormUserDataResults = Lists.newArrayList();
        List<CustomizeFormDataUserEntity> formDataUserEntities = customizeFormDataUserDAO.queryPageByMarketingActivityIdsAndUserIdRange(ea, marketingActivityIds, userIds, startDate, endDate, page);
        if (CollectionUtils.isEmpty(formDataUserEntities)) {
            return queryFormUserDataResults;
        }
        queryFormUserDataResults = getQueryFormUserDataResults(formDataUserEntities,ea);
        return queryFormUserDataResults;
    }

    @Override
    public Result<CustomizeFormDataEnrollResult> saveWxAppObjectToCrm(CustomizeFormDataEnrollArg arg) {
        com.facishare.marketing.api.result.CustomizeFormDataEnrollResult customizeFormDataEnrollResult = new CustomizeFormDataEnrollResult();
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId(),arg.getEa());
        // 校验物料合法性
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isBlank(ea)) {
            log.warn("CustomizeFormDataServiceImpl.customizeFormDataEnroll ea is null, arg:{}", arg);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_BINDING_OBJECT_NOT_FOUND);
        }
        if (StringUtils.isBlank(arg.getEa())) {
            arg.setEa(ea);
        }
        //如果有来源营销身份,获取是否是会员
        if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
            Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                    .getBaseInfosByIds(customizeFormDataEntity.getEa(), 1000, Lists.newArrayList(arg.getFromUserMarketingId()), InfoStateEnum.DETAIL);
            UserMarketingAccountData marketingAccountData = userMarketingAccountDataMap.get(arg.getFromUserMarketingId());
            if(marketingAccountData!=null&& CollectionUtils.isNotEmpty(marketingAccountData.getCrmMemberInfos())){
                arg.setSpreadUserIdentifyId(marketingAccountData.getCrmMemberInfos().get(0).getId());
                arg.setSpreadUserType(ChannelEnum.CRM_MEMBER.getType());
            }
        }
        //查询是否是会议营销,并处理审核状态
        customizeFormDataManager.checkConferenceEnrollReview(ea,arg.getMarketingEventId(),customizeFormDataEnrollResult);
        // 查询表单数据
        Result checkResult = customizeFormDataManager.checkCustomizeFormDataStatus(customizeFormDataEntity, arg.getFormId(), false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        Result enrollFieldCheckResult = customizeFormDataManager.checkEnrollField(customizeFormDataEntity, arg.getSubmitContent(), arg.getObjectType(), arg.getObjectId(), arg.getStepFormComponentId());
        if (!enrollFieldCheckResult.isSuccess()) {
            return enrollFieldCheckResult;
        }
        if (!Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) && !Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())){
            boolean phoneVerified = verificationCodeManager.checkSMCode(arg.getSubmitContent().getPhone(), arg.getSubmitContent().getPhoneVerifyCode()).isSuccess();
            if (!phoneVerified){
                return Result.newError(SHErrorCode.PHONE_VERIFY_CODE_ERROR);
            }
        }
//        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == arg.getObjectType()){
//            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
//            if (memberConfig != null) {
//                HexagonPageEntity hexagonPage = hexagonPageDAO.getById(arg.getObjectId());
//                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getRegistrationSiteId())){
//                    return doWxServiceUserMemberRegister(ea, arg);
//                }
//                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getLoginSiteId())){
//                    return doWxServiceUserMemberLogin(ea, arg);
//                }
//                if (hexagonPage.getHexagonSiteId().equals(memberConfig.getUpdateInfoSiteId())) {
//                    return doWxServiceUserMemberUpdateInfo(ea, arg);
//                }
//            }
//        }
        // 特殊数据校验
        if (StringUtils.isBlank(arg.getMarketingEventId())) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
            if (externalConfigEntity != null) {
                arg.setMarketingEventId(externalConfigEntity.getMarketingEventId());
            }
        }
        Result objectCheckResult = this.checkObjectLegitimacy(ea, arg.getMarketingEventId(), arg.getObjectId(), arg.getObjectType());
        if (!objectCheckResult.isSuccess()) {
            return objectCheckResult;
        }
        // 转换userId
        if(!arg.isPartner()){
            arg.setSpreadFsUid(qywxUserManager.convertOldUserId(ea, arg.getSpreadFsUid()));
        }
        Boolean fillInOnce = customizeFormDataEntity.getFormMoreSetting().isFillInOnce();
        Boolean synchronousCRM = customizeFormDataEntity.getFormMoreSetting().isSynchronousCRM();
        Boolean enrollLimit = customizeFormDataEntity.getFormMoreSetting().isEnrollLimit();
        if (StringUtils.isBlank(arg.getEnrollId())) {
            if(fillInOnce) {
                String enrollId = customizeFormDataManager.checkWxAppUserIsEnrolledWithNoHandleActivity(arg.getMarketingEventId(), customizeFormDataEntity, arg.getObjectId(), arg.getObjectType(), arg.getUid(), arg.getSubmitContent().getPhone());
                if (StringUtils.isNotBlank(enrollId)) {
                    customizeFormDataEnrollResult.setEnrollId(enrollId);
                    return new Result<>(SHErrorCode.USERS_HAVE_REGISTERED, customizeFormDataEnrollResult);
                }
            }
            if (enrollLimit) { // 是否开启报名限制
                long countResult = customizeFormDataUserDAO.countCustomizeFormDataUserByFormId(customizeFormDataEntity.getId(), arg.getEa());
                if (countResult >= customizeFormDataEntity.getFormMoreSetting().getEnrollLimitNum()) {
                    return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_FULL.getErrorCode(), customizeFormDataEntity.getFormMoreSetting().getEnrollLimitText());
                }
            }
        }
        String crmMemberId = null;
        if(FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER){
            Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createMiniAppFsPayOrder(
                    customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), arg.getObjectType(), arg.getObjectId(),
                    Integer.valueOf(customizeFormDataEntity.getCreateBy()), crmMemberId, arg.getOpenId(), arg.getWxAppId(), arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
            if (!createPayOrderResult.isSuccess()) {
                return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
            }
            CreateFsPayOrderResult data = createPayOrderResult.getData();
            BeanUtils.copyProperties(data, customizeFormDataEnrollResult);
            customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(customizeFormDataEnrollResult.getEnrollId(), data.getOrderNo(),arg.getEa());
        }
        // 插入数据
        String customizeFormDataUserId = UUIDUtil.getUUID();
        buildMarketingEventIdByEnrollData(arg);
        CustomizeFormDataUserEntity customizeFormDataUserEntity = BeanUtil.copy(arg, CustomizeFormDataUserEntity.class);
        // 创建营销推广来源对象
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
        customizeFormDataUserEntity.setEa(ea);
        if (StringUtils.isBlank(arg.getEnrollId())) {
            if(arg.isPartner()){
                customizeFormDataUserEntity.setOutTenantId(arg.getOuterTenantId());
                customizeFormDataUserEntity.setOutUid(String.valueOf(arg.getOuterUid()));
            }
            customizeFormDataUserEntity.setId(customizeFormDataUserId);
            customizeFormDataUserEntity.setSourceType(customizeFormDataManager.getEnrollType(customizeFormDataUserEntity));
            customizeFormDataUserEntity.setChannelValue(customizeFormDataManager.getSystemPromotionChannelType(customizeFormDataUserEntity));
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceType(customizeFormDataManager.getMarketingSourceType(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceName(customizeFormDataManager.getMarketingSourceName(customizeFormDataUserEntity));
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            //判断是否有映射crm字段
            if (!synchronousCRM) {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_1908));
            }
            boolean saveResult = customizeFormDataUserDAO.insertCustomizeFormDataUser(customizeFormDataUserEntity) == 1;
            if (!saveResult) {
                log.warn("CustomizeFormDataServiceImpl.wxCustomizeFormDataEnroll insertCustomizeFormDataUser error customizeFormDataUserEntity:{}", customizeFormDataUserEntity);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            customizeFormDataEnrollResult.setEnrollId(customizeFormDataUserId);
            //上报神策埋点-表单提交
            marketingStatLogPersistorManger.sendCustomizeFormDataUserData(ea, customizeFormDataUserEntity.getId(), arg.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_OFFICIAL_ACCOUNTS);
        } else {
            // 更新数据
            customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getEnrollId(), arg.getEa());
            if (customizeFormDataUserEntity == null) {
                log.warn("CustomizeFormDataServiceImpl.wxCustomizeFormDataEnroll oldData is null enrollId:{}", arg.getEnrollId());
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
            } else {
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                customizeFormDataEnroll.setMarketingPromotionSourceId(marketingPromotionSourceId);
                customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            }
            BeanUtil.appendCopyCustomizeFormDataEnrollIgnoreNull(arg.getSubmitContent(), customizeFormDataUserEntity.getSubmitContent());
            // 更新报名数据
            customizeFormDataUserDAO.updateCustomizeFormDataEnrollDataById(customizeFormDataUserEntity);
            customizeFormDataEnrollResult.setEnrollId(arg.getEnrollId());
        }
        // 特殊物料逻辑处理
        CustomizeFormDataUserEntity finalCustomizeFormDataUserEntity = customizeFormDataUserEntity;
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                customizeFormDataManager.handlerSpecialTypeEnrollData(finalCustomizeFormDataUserEntity);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        //任意对象的存入
        boolean updateData = !StringUtils.isEmpty(finalCustomizeFormDataUserEntity.getExtraDataId());
        customizeFormDataManager.saveObjectToCrm(customizeFormDataEntity,finalCustomizeFormDataUserEntity,updateData);
        return Result.newSuccess(customizeFormDataEnrollResult);
    }

    @Override
    public Result<Void> updateCustomizeFormDataMoreSetting(UpdateCustomizeFormDataMoreSettingVO updateCustomizeFormDataMoreSettingVO) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(updateCustomizeFormDataMoreSettingVO.getId(), updateCustomizeFormDataMoreSettingVO.getEa());
        if (customizeFormDataEntity == null) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        FormMoreSetting formMoreSetting = customizeFormDataEntity.getFormMoreSetting();
        if (formMoreSetting != null) {
            formMoreSetting.setMemberCheckType(updateCustomizeFormDataMoreSettingVO.getMemberCheckType());
            customizeFormDataDAO.updateFormMoreSetting(customizeFormDataEntity);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteEnrollData(DeleteEnrollDataArg arg) {
        List<String> enrollUserIds = arg.getEnrollUserIds();
        for (String enrollUserId : enrollUserIds) {
            customizeFormDataManager.deleteEnrollData(enrollUserId, arg.getEa(), arg.getFsUserId());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<IP2locationData> getLocationInfoByIp(String ip, String language){
        Optional<IP2locationData> opt = egressApiManger.getLocationInfoByIp(ip, language);
        if (opt.isPresent()){
            return Result.newSuccess(opt.get());
        }

        return Result.newSuccess();
    }

    private boolean changeTNpathFileToNpath(String ea,  Map<String, List<FileAttachmentContainer>> containerMap) {
        if (MapUtils.isEmpty(containerMap)) {
            return false;
        }

        //遍历map，将path进行转换
        for (Map.Entry<String, List<FileAttachmentContainer>> entry : containerMap.entrySet()) {
            String key = entry.getKey();
            List<FileAttachmentContainer> container = entry.getValue();
            for (FileAttachmentContainer fileItem : container) {
                String nPath = fileV2Manager.changeNWarehouseTempToPermanentBybusiness(ea, -10000, fileItem.getExt(), fileItem.getPath(), "fs-marketing");
                if (StringUtils.isNotEmpty(nPath)) {
                    fileItem.setPath(nPath);
                } else {
                    log.info("CustomizeFormDataServiceImpl.changeTNpathFileToNpath  failed ea:{} containerMap:{}", ea, containerMap);
                    return false;
                }
            }
        }
        return true;
    }
}