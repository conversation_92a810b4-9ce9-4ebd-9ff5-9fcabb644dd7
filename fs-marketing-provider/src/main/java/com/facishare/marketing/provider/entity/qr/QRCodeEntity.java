package com.facishare.marketing.provider.entity.qr;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class QRCodeEntity extends BaseEaEntity implements Serializable  {

    /**
     * UUID自增主键
     */
    private Integer id;

    /**
     * 二维码类型
     * {@link com.facishare.marketing.common.enums.qr.QRCodeTypeEnum}
     */
    private Integer type;

    /**
     * 二维码内容参数
     */
    private String value;

    /**
     * 验证码
     */
    private String authCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 小程序appid
     */
    private String appid;
}
