/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.usermarketingaccount;

import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.manager.rateLimiter.YxtRRateLimiter;
import com.facishare.marketing.provider.manager.rateLimiter.YxtRRateLimiterBuilder;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UserMarketingAccountChangeLogManager {
    @Autowired
    private CrmV2Manager crmV2Manager;

    @ReloadableProperty("marketing_account_change_log_gray_list")
    private String grayList;

    @ReloadableProperty("marketing_account_change_log_skip_list")
    private String skipList;

    @ReloadableProperty("UserMarketingAccountChangeLogObj.permits.perSecond")
    private Long logPermitsPerSecond;

    private static List<String> TriggerActions = Lists.newArrayList("memberEnroll", "enrollOrCheckInConference", "bindEnrollDataAndCrmObject",
            "associateWxService", "getMarketingAccount", "bindBrowserAndOpenId", "WxUserActionMessageHandler", "CrmMqNewMessageHandler",
            "saveConferenceParticipantsToCrm", "bindMemberAndCrmObj", "MankeepEventHandle", "customizeFormDataEnroll", "activitySignIn",
            "sendUnionMsg", "associateContact", "qywxExternalUserEventHandle", "associateWxServiceAndCrmWxUser", "associateExternalMarketingAccountByUnionIdAndOpenId",
            "associateCrmWxUser", "syncWechatTagsAndWechatFanTags", "associateExternalUser", "record", "enrollOrCheckInConferenceByH5", "noIdentityFormDataEnroll",
            "associateBrowserUser", "OfficialWebsiteThirdPlateformEventHandler", "bindPhoneWithBrowserUserId", "WxMiniAppUserRegisterOrLogin",
            "saveMankeepToCrm", "bindToWxWorkExternalUser", "miniappMarketingAccountAssociationService", "miniAppRecord", "associateMiniapp", "associateMiniappAndContact",
            "associateMiniappAndCustomer", "pLogin", "batchAddTagsToUserMarketings", "saveLeadToMember", "associateCrmContact", "associateCrmLead", "syncThirdLeadData");

    private synchronized YxtRRateLimiter getAddUserMarketingAccountChangeLogRRateLimiter(String ea) {
        String name = ea + ":mk:UserMarketingAccountChangeLogObj:rateLimiter";
        return YxtRRateLimiterBuilder.getYxtRRateLimiter(name, logPermitsPerSecond, 1, true, 60 * 60 * 24 * 7, TimeUnit.SECONDS);
    }

    private void asyncAddLog(String ea, Map<String,Object> objectData) {
        ThreadPoolUtils.execute(() -> addLog(ea, objectData), ThreadPoolUtils.ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    private ObjectData addLog(String ea, Map<String,Object> objectData) {
        if(!isGrayEa(ea)) {
            return null;
        }
        try {
            getAddUserMarketingAccountChangeLogRRateLimiter(ea).acquire();
            Result<ActionAddResult> result = crmV2Manager.addObjectData(ea, CrmObjectApiNameEnum.USER_MARKETING_ACCOUNT_CHANGE_LOG_OBJ.getName(), -10000, objectData);
            if (result.isSuccess() && result.getData() != null) {
                return result.getData().getObjectData();
            }
        } catch (Exception e) {
            log.info("UserMarketingAccountChangeLogManager addLog error", e.getMessage());
        }
        return null;
    }

    @FilterLog
    public void logInsertMarketingAccount(String marketingAccountId, AssociationArg associationArg) {
        if(!isGrayEa(associationArg.getEa())) {
            return;
        }
        Map<String,Object> objectData = generateObjectData(marketingAccountId, associationArg);
        objectData.put("log_type", "add");
        objectData.put("log_content", String.format("%s%s引起新增营销用户,手机号:%s", getSourceContent(associationArg), getActionContent(associationArg), StringUtils.isBlank(associationArg.getPhone()) ? "--" : associationArg.getPhone()));
        processObjectData(associationArg, objectData);
        asyncAddLog(associationArg.getEa(), objectData);
    }

    public void logRelationMarketingAccount(String marketingAccountId, AssociationArg associationArg) {
        if(!isGrayEa(associationArg.getEa())) {
            return;
        }
        Map<String,Object> objectData = generateObjectData(marketingAccountId, "", associationArg, "", Lists.newArrayList(associationArg.getAssociationId()));
        objectData.put("log_type", "associate");
        objectData.put("log_content", String.format("%s%s引起营销用户关联%s", getSourceContent(associationArg), getActionContent(associationArg), getSourceContentByType(associationArg)));
        processObjectData(associationArg, objectData);
        asyncAddLog(associationArg.getEa(), objectData);
    }

    public void logUpdateMarketingAccount(String marketingAccountId, AssociationArg associationArg) {
        if(!isGrayEa(associationArg.getEa())) {
            return;
        }
        Map<String,Object> objectData = generateObjectData(marketingAccountId, associationArg);;
        objectData.put("log_type", "add");
//        objectData.put("log_content", "在非微信环境访问营销通H5提交表单，更新营销用户手机号:" + associationArg.getPhone());
        objectData.put("log_content", String.format("%s%s引起更新营销用户手机号为:%s", getSourceContent(associationArg), getActionContent(associationArg), associationArg.getPhone()));
        processObjectData(associationArg, objectData);
        asyncAddLog(associationArg.getEa(), objectData);
    }

    public void logDeleteMarketingAccountRelation(String marketingAccountId, AssociationArg associationArg) {
        if(!isGrayEa(associationArg.getEa())) {
            return;
        }
        Map<String,Object> objectData = generateObjectData(marketingAccountId, "", associationArg, "", Lists.newArrayList(associationArg.getAssociationId()));
        objectData.put("log_type", "associate");
//        objectData.put("log_content", "在非微信环境访问营销通H5提交表单，删除未关联营销用户并关联到相同手机号的营销用户");
        objectData.put("log_content", String.format("%s%s引起营销用户删除关联%s", getSourceContent(associationArg), getActionContent(associationArg), getSourceContentByType(associationArg)));
        processObjectData(associationArg, objectData);
        asyncAddLog(associationArg.getEa(), objectData);
    }

    public void logMergeMarketingAccount(String marketingAccountId, String mergedMarketingAccountId, AssociationArg associationArg) {
        if(!isGrayEa(associationArg.getEa())) {
            return;
        }
        Map<String,Object> objectData = generateObjectData(marketingAccountId, mergedMarketingAccountId, associationArg);
        objectData.put("log_type", "merge");
//        objectData.put("log_content", "在非微信环境访问营销通H5提交表单，导致营销用户合并");
        objectData.put("log_content", String.format("%s%s引起营销用户合并", getSourceContent(associationArg), getActionContent(associationArg)));
        processObjectData(associationArg, objectData);
        asyncAddLog(associationArg.getEa(), objectData);
    }

    public void logDeleteMarketingAccount(String marketingAccountId, String mergedMarketingAccountId, AssociationArg associationArg) {
        if(!isGrayEa(associationArg.getEa())) {
            return;
        }
        Map<String,Object> objectData = generateObjectData(marketingAccountId, mergedMarketingAccountId, associationArg);
        objectData.put("log_type", "merge");
//        objectData.put("log_content", "在非微信环境访问营销通H5提交表单，导致营销用户合并,删除被合并营销用户");
        objectData.put("log_content", String.format("%s%s引起营销用户合并,删除被合并营销用户", getSourceContent(associationArg), getActionContent(associationArg)));
        processObjectData(associationArg, objectData);
        asyncAddLog(associationArg.getEa(), objectData);
    }

    public void logUpdateMarketingAccountRelation(String marketingAccountId, String mergedMarketingAccountId, AssociationArg associationArg, String affectedApiName, List<String> affectedIds) {
        if(!isGrayEa(associationArg.getEa())) {
            return;
        }
        Map<String,Object> objectData = generateObjectData(marketingAccountId, mergedMarketingAccountId, associationArg, affectedApiName, affectedIds);
        objectData.put("log_type", "merge");
//        objectData.put("log_content", "在非微信环境访问营销通H5提交表单，触发合并营销用户");
        objectData.put("log_content", String.format("%s%s引起营销用户合并,更新关联%s", getSourceContent(associationArg), getActionContent(associationArg), getSourceContent(affectedApiName)));
        processObjectData(associationArg, objectData);
        asyncAddLog(associationArg.getEa(), objectData);
    }

    private void processObjectData(AssociationArg associationArg, Map<String,Object> objectData) {
        String source = getSourceContent(associationArg);
        String action = getActionContent(associationArg);
        String traceId = "";
        TraceContext traceContext = TraceContext.get();
        if(traceContext != null && StringUtils.isNotBlank(traceContext.getTraceId())) {
            traceId = traceContext.getTraceId();
        }
        objectData.put("remark", String.format("来源:%s,触发:%s, 触发traceId:%s", source, action, traceId));
    }

    @NotNull
    private String getActionContent(AssociationArg associationArg) {
        String actionContent = "未知";
        if(StringUtils.isBlank(associationArg.getTriggerAction())) {
            return  actionContent;
        }
        switch (associationArg.getTriggerAction()) {
            case "memberEnroll":
                actionContent = "会员报名";
                break;
            case "associateCrmLead":
                actionContent = "关联线索";
                break;
            case "syncThirdLeadData":
                actionContent = "同步第三方广告数据到线索";
                break;
                case "associateCrmContact":
                actionContent = "关联联系人";
                break;
            case "saveLeadToMember":
                actionContent = "线索同步保存为会员";
                break;
            case "getMarketingAccount":
                actionContent = "获取当前身份的营销用户";
                break;
            case "WxMiniAppUserRegisterOrLogin":
            case "pLogin":
                actionContent = "微信小程序注册或登录";
                break;
            case "saveMankeepToCrm":
            case "saveConferenceParticipantsToCrm":
            case "bindEnrollDataAndCrmObject":
            case "bindMemberAndCrmObj":
                actionContent = "保存为CRM数据";
                break;
            case "record":
            case "miniAppRecord":
                actionContent = "访问营销通";
                break;
            case "enrollOrCheckInConference":
            case "enrollOrCheckInConferenceByH5":
                actionContent = "会议、表单报名或签到";
                break;
            case "associateMiniapp":
            case "associateMiniappAndContact":
            case "associateMiniappAndCustomer":
                actionContent = "关联小程序";
                break;
            case "customizeFormDataEnroll":
            case "noIdentityFormDataEnroll":
                actionContent = "提交表单";
                break;
            case "sendUnionMsg":
                actionContent = "发送互动消息";
                break;
            case "batchAddTagsToUserMarketings":
                actionContent = "营销用户打标签";
                break;
            case "associateBrowserUser":
                actionContent = "关联无身份访客";
                break;
            case "activitySignIn":
                actionContent = "活动签到";
                break;
            case "bindPhoneWithBrowserUserId":
                actionContent = "绑定无身份手机号";
                break;
            case "bindBrowserAndOpenId":
                actionContent = "绑定访客和微信用户";
                break;
            case "associateExternalUser":
                actionContent = "关联外部用户";
                break;
            case "syncWechatTagsAndWechatFanTags":
                actionContent = "同步微信粉丝数据";
                break;
            case "associateWxServiceAndCrmWxUser":
            case "associateWxService":
            case "associateCrmWxUser":
                actionContent = "关联微信用户";
                break;
            case "associateExternalMarketingAccountByUnionIdAndOpenId":
                actionContent = "通过外部的openid或unionid关联营销用户";
                break;
            case "qywxExternalUserEventHandle":
                actionContent = "绑定企业微信外部用户与无身份用户";
                break;
            case "WxUserActionMessageHandler":
                actionContent = "微信MQ消息处理";
                break;
        }
        return actionContent;
    }

    @NotNull
    private String getSourceContent(AssociationArg associationArg) {
        return getSourceContent(associationArg.getTriggerSource());
    }

    @NotNull
    private String getSourceContent(String source) {
        String sourceContent = "未知";
        if(ChannelEnum.BROWSER_USER.getDescription().equals(source)) {
            sourceContent = "无身份访客H5";
        } else if(ChannelEnum.MINIAPP.getDescription().equals(source)) {
            sourceContent = "小程序";
        } else if(ChannelEnum.WX_SERVICE.getDescription().equals(source)) {
            sourceContent = "微信公众号";
        } else if(ChannelEnum.CRM_CONTACT.getDescription().equals(source)) {
            sourceContent = "CRM联系人";
        } else if(ChannelEnum.CRM_LEAD.getDescription().equals(source)) {
            sourceContent = "CRM线索";
        } else if(ChannelEnum.CRM_WX_USER.getDescription().equals(source)) {
            sourceContent = "CRM绑定微信用户";
        } else if(ChannelEnum.CRM_ACCOUNT.getDescription().equals(source)) {
            sourceContent = "CRM客户";
        } else if(ChannelEnum.CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID.getDescription().equals(source)) {
            sourceContent = "CRM绑定微信用户带UnionId";
        } else if(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription().equals(source)) {
            sourceContent = "CRM绑定微信用户外部联系人";
        } else if(ChannelEnum.WX_WORK_EXTERNAL_USER.getDescription().equals(source)) {
            sourceContent = "微信用户外部联系人";
        } else if(ChannelEnum.WX_WORK_MINIAPP_USER.getDescription().equals(source)) {
            sourceContent = "微信小程序用户";
        } else if(ChannelEnum.CRM_MEMBER.getDescription().equals(source)) {
            sourceContent = "CRM会员";
        } else if(ChannelEnum.CUSTOMIZE_OBJECT.getDescription().equals(source)) {
            sourceContent = "CRM自定义对象";
        }
        else if("TRIGGER_INSTANCE".equals(source)) {
            sourceContent = "营销流程";
        }
        return sourceContent;
    }

    @NotNull
    private String getSourceContentByType(AssociationArg associationArg) {
        String source = "未知";
        if(ChannelEnum.BROWSER_USER.getType().equals(associationArg.getType())) {
            source = "无身份访客H5";
        } else if(ChannelEnum.MINIAPP.getType().equals(associationArg.getType())) {
            source = "小程序";
        } else if(ChannelEnum.WX_SERVICE.getType().equals(associationArg.getType())) {
            source = "微信公众号";
        } else if(ChannelEnum.CRM_CONTACT.getType().equals(associationArg.getType())) {
            source = "CRM联系人";
        } else if(ChannelEnum.CRM_LEAD.getType().equals(associationArg.getType())) {
            source = "CRM线索";
        } else if(ChannelEnum.CRM_WX_USER.getType().equals(associationArg.getType())) {
            source = "CRM绑定微信用户";
        } else if(ChannelEnum.CRM_ACCOUNT.getType().equals(associationArg.getType())) {
            source = "CRM客户";
        } else if(ChannelEnum.CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID.getType().equals(associationArg.getType())) {
            source = "CRM绑定微信用户带UnionId";
        } else if(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType().equals(associationArg.getType())) {
            source = "CRM绑定微信用户外部联系人";
        } else if(ChannelEnum.WX_WORK_EXTERNAL_USER.getType().equals(associationArg.getType())) {
            source = "微信用户外部联系人";
        } else if(ChannelEnum.WX_WORK_MINIAPP_USER.getType().equals(associationArg.getType())) {
            source = "微信小程序用户";
        } else if(ChannelEnum.CRM_MEMBER.getType().equals(associationArg.getType())) {
            source = "CRM会员";
        } else if(ChannelEnum.CUSTOMIZE_OBJECT.getType().equals(associationArg.getType())) {
            source = "CRM自定义对象";
        }
        return source;
    }

    /**
     * 是否灰度企业
     *
     * @param ea
     * @return
     */
    public boolean isGrayEa(String ea) {
        boolean isSkip = isAnyMatchEa(skipList, ea);
        if(isSkip) {
            return false;
        }
        return isAnyMatchEa(grayList, ea);
    }

    private static boolean isAnyMatchEa(String eaList, String ea) {
        List<String> grayEas = Arrays.asList(eaList.split(","));
        if(grayEas.contains("*")) {
            return true;
        }
        if (StringUtils.isNotEmpty(eaList)) {
            return grayEas.stream()
                    .anyMatch(e -> e.replace("\"", "").equals(ea));
        }
        return false;
    }

    private Map<String,Object> generateObjectData(String marketingAccountId, AssociationArg associationArg) {
        Map<String,Object> objectData = Maps.newHashMap();
        objectData.put("user_marketing_account_id", marketingAccountId);
        objectData.put("trigger_source", associationArg.getTriggerSource());
        objectData.put("trigger_action", associationArg.getTriggerAction());
        return objectData;
    }

    private Map<String,Object> generateObjectData(String marketingAccountId, String mergedMarketingAccountId, AssociationArg associationArg) {
        Map<String,Object> objectData = Maps.newHashMap();
        objectData.put("user_marketing_account_id", marketingAccountId);
        objectData.put("related_user_marketing_account_id", mergedMarketingAccountId);
        objectData.put("trigger_source", associationArg.getTriggerSource());
        objectData.put("trigger_action", associationArg.getTriggerAction());
        objectData.put("affected_relation_data_ids", associationArg.getAssociationId());
        ChannelEnum channelEnum = ChannelEnum.getByType(associationArg.getType());
        objectData.put("relation_data_api_name", channelEnum == null ? associationArg.getType() : channelEnum.getDescription());
        return objectData;
    }

    private Map<String,Object> generateObjectData(String marketingAccountId, String mergedMarketingAccountId, AssociationArg associationArg, String affectedApiName, List<String> affectedIds) {
        Map<String,Object> objectData = Maps.newHashMap();
        objectData.put("user_marketing_account_id", marketingAccountId);
        objectData.put("related_user_marketing_account_id", mergedMarketingAccountId);
        objectData.put("trigger_source", associationArg.getTriggerSource());
        objectData.put("trigger_action", associationArg.getTriggerAction());
        objectData.put("affected_relation_data_ids", String.join(",",  affectedIds));
        if(StringUtils.isBlank(affectedApiName)) {
            ChannelEnum channelEnum = ChannelEnum.getByType(associationArg.getType());
            objectData.put("relation_data_api_name", channelEnum == null ? associationArg.getType() : channelEnum.getDescription());
        } else {
            objectData.put("relation_data_api_name", affectedApiName);
        }
        return objectData;
    }

}
