package com.facishare.marketing.provider.manager.advertiser.tencent;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.result.baidu.GetDataOverviewResult;
import com.facishare.marketing.api.result.baidu.QueryAccountInfoResult;
import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO;
import com.facishare.marketing.api.vo.baidu.QueryAccountInfoVO;
import com.facishare.marketing.common.enums.advertiser.tencent.*;
import com.facishare.marketing.common.enums.baidu.AccountStatusEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.baidu.DataRefreshStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.provider.advertiser.adAccount.TencentAdResult;
import com.facishare.marketing.provider.advertiser.tencent.*;
import com.facishare.marketing.provider.baidu.keyword.AdKeywordManager;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsMappingDataDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDataDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentCampaignDAO;
import com.facishare.marketing.provider.dao.baidu.AdKeywordDAO;
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduDataStatusDAO;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsMappingDataEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupDataEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentCampaignEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.AdKeywordEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduDataStatusEntity;
import com.facishare.marketing.provider.entity.baidu.CampaignDataOverviewDTOEntity;
import com.facishare.marketing.provider.innerArg.CreateAdvertisingDetailObjArg;
import com.facishare.marketing.provider.manager.MarketingStatLogPersistorManger;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.AdMarketingManager;
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager;
import com.facishare.marketing.provider.manager.baidu.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.AdvertisingDetailsObjManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.permission.DataPermissionManager.defaultAllChannel;

@Service("tencentAdMarketingManager")
@Slf4j
public class TencentAdMarketingManager implements AdMarketingManager {
    @Autowired
    private AccountApiManager accountApiManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private AdAccountManager adAccountManager;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private CampaignApiManager campaignApiManager;
    @Autowired
    private TencentCampaignDAO tencentCampaignDAO;
    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;
    @Autowired
    private AdTokenManager adTokenManager;
    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Autowired
    private BaiduDataStatusDAO baiduDataStatusDAO;
    @Autowired
    private TencentAdGroupDataDAO tencentAdGroupDataDAO;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private AdLeadsMappingDataDAO adLeadsMappingDataDAO;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;

    @Autowired
    private AdvertisingDetailsObjManager advertisingDetailsObjManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @ReloadableProperty("tencent_campaign_list")
    private String campaignList;

    @ReloadableProperty("tencent_campaign_list_en")
    private String campaignListEN;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AdKeywordManager adKeywordManager;
    @Autowired
    private AdKeywordDAO adKeywordDAO;

    @ReloadableProperty("tencent_ad_group_refresh_day")
    private int tencentAdGroupRefreshDay;

    @Override
    public Result<List<QueryAccountInfoResult>> queryAccountInfo(QueryAccountInfoVO vo) {
        List<QueryAccountInfoResult> resultList = Lists.newArrayList();
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEaAndSource(vo.getEa(), vo.getAdAccountId(), AdSourceEnum.SOURCE_TENCETN.getSource(), true);
        if(dataPermissionManager.getNewDataPermissionSetting(vo.getEa())){
            List<String> websiteIds = dataPermissionManager.findAccessibleAdvertiseIds(vo.getEa(), vo.getFsUserId());
            if(CollectionUtils.isEmpty(websiteIds)){
                QueryAccountInfoResult result = new QueryAccountInfoResult();
                result.setEa(vo.getEa());
                result.setStatus(AccountStatusEnum.NOT_BIND_ACCOUNT.getStatus());
                resultList.add(result);
                return Result.newSuccess(resultList);
            }
            if(CollectionUtils.isNotEmpty(websiteIds) && !websiteIds.contains(defaultAllChannel)){
                //过滤可见范围
                adAccountEntityList = adAccountEntityList.stream().filter(adAccountEntity -> websiteIds.contains(adAccountEntity.getId())).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return Result.newSuccess(resultList);
        }
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            QueryAccountInfoResult result = new QueryAccountInfoResult();
            BaiduDataStatusEntity statusEntity = baiduDataStatusDAO.queryRefreshStatus(vo.getEa(), adAccountEntity.getId(), AdSourceEnum.SOURCE_TENCETN.getSource());
            boolean isStatusEntityExists = statusEntity == null || statusEntity.getRefreshStatus() == null;
            boolean isRefreshTimeExists = isStatusEntityExists || statusEntity.getRefreshTime() == null;
            result.setRefreshStatus(isStatusEntityExists ? DataRefreshStatusEnum.NONE.getStatus() : statusEntity.getRefreshStatus());
            Date refreshSuccessTime = isStatusEntityExists ? null : statusEntity.getRefreshSuccessTime();
            result.setRefreshSuccessTime(refreshSuccessTime == null ? null : refreshSuccessTime.getTime());
            result.setRefreshTime(isRefreshTimeExists ? null : statusEntity.getRefreshTime().getTime());
            QueryAccountInfoResult.AccountInfo accountInfo = new QueryAccountInfoResult.AccountInfo();
            accountInfo.setCost(adAccountEntity.getCost() != null ? Math.round(adAccountEntity.getCost()) / 100d : 0);
            accountInfo.setAccountId(adAccountEntity.getAccountId());
            accountInfo.setUsername(adAccountEntity.getUsername());
            accountInfo.setBalance(adAccountEntity.getBalance() != null ? Math.round(adAccountEntity.getBalance()) / 100d : 0);
            accountInfo.setBudget(adAccountEntity.getBudget() != null ? Math.round(adAccountEntity.getBudget()) / 100d : 0);
            accountInfo.setBudgetType(adAccountEntity.getBudgetType());
            accountInfo.setUserStat(adAccountEntity.getUserStat());
            result.setStatus(adAccountEntity.getStatus());
            result.setAccountInfo(accountInfo);
            result.setId(adAccountEntity.getId());
            result.setEa(vo.getEa());
            resultList.add(result);
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public SHErrorCode isValidAccount(AdAccountEntity adAccountEntity) {
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            return SHErrorCode.THIRD_APPLICATION_ERROR;
        }
        TencentAdResult<GetTencentAdAccountFundsInfoData> tencentAdResult = accountApiManager.getTencentAdAccountFundsInfo(adAccountEntity.getAccountId(), accessTokenOpt.get());
        if (Objects.isNull(tencentAdResult) || !tencentAdResult.isSuccess()) {
            return SHErrorCode.THIRD_APPLICATION_ERROR;
        }
        return SHErrorCode.SUCCESS;
    }

    @Override
    public Result<GetDataOverviewResult> getDataOverview(GetDataOverviewVO vo) {
        GetDataOverviewResult getDataOverviewResult = new GetDataOverviewResult();
        CampaignDataOverviewDTOEntity campaignDataOverviewDTOEntity = null;
        if (StringUtils.isNotEmpty(vo.getCampaignName())) {
            campaignDataOverviewDTOEntity = tencentAdGroupDataDAO.getTencentDataOverviewByCampaignName(vo.getEa(), vo.getAdAccountId(), vo.getCampaignName(), vo.getStartTime(), vo.getEndTime(), vo.getDataType());
        } else {
            campaignDataOverviewDTOEntity = tencentAdGroupDataDAO.getTencentDataOverview(vo.getEa(), vo.getAdAccountId(), vo.getAdGroupName(), vo.getStartTime(), vo.getEndTime(), vo.getDataType());
        }
        if (campaignDataOverviewDTOEntity == null) {
            return Result.newSuccess(getDataOverviewResult);
        }
        List<String> marketingEventIds = tencentAdGroupDAO.queryMarketingEventIds(vo.getEa(), vo.getAdAccountId(), vo.getCampaignName(), vo.getAdGroupName(), vo.getDataType());
        Long startTime = vo.getStartTime() == null ? null : vo.getStartTime().getTime();
        Long endTime = vo.getEndTime() == null ? null : vo.getEndTime().getTime();
        int clueCount = CollectionUtils.isEmpty(marketingEventIds) || marketingEventIds.get(0) == null ? 0 : campaignDataManager.getLeadsCountByMarketingEvent(vo.getEa(), marketingEventIds, startTime, endTime, null);
        getDataOverviewResult.setLeads(clueCount);
        getDataOverviewResult.setPv(campaignDataOverviewDTOEntity.getPv());
        getDataOverviewResult.setClick(campaignDataOverviewDTOEntity.getClick());
        getDataOverviewResult.setCost(campaignDataOverviewDTOEntity.getCost() == null ? 0 : Math.round(campaignDataOverviewDTOEntity.getCost()) / 100d);
        getDataOverviewResult.setClickPrice((campaignDataOverviewDTOEntity.getClick() == null || campaignDataOverviewDTOEntity.getClick() == 0) ? 0 : Math.round(campaignDataOverviewDTOEntity.getCost() / campaignDataOverviewDTOEntity.getClick()) / 100d);
        return Result.newSuccess(getDataOverviewResult);
    }

    @Override
    public boolean refreshAccountInfo(AdAccountEntity accountInfo) {
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(accountInfo);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.refreshAccountInfo getTencentAccessToken fail, accountInfo:{}", accountInfo);
            return false;
        }
        String accessToken = accessTokenOpt.get();
        TencentAdResult<GetTencentAdAccountFundsInfoData> tencentAdAccountFundsInfoResult = accountApiManager.getTencentAdAccountFundsInfo(accountInfo.getAccountId(), accessToken);
        if (!tencentAdAccountFundsInfoResult.isSuccess() || tencentAdAccountFundsInfoResult.getData() == null || CollectionUtils.isEmpty(tencentAdAccountFundsInfoResult.getData().getList())) {
            log.info("TencentAdMarketingManager.refreshAccountInfo getTencentAdAccountFundsInfo fail, accountInfo:{}", accountInfo);
            return false;
        }
        Double balance = tencentAdAccountFundsInfoResult.getData().getList().stream().filter(Objects::nonNull).mapToDouble(GetTencentAdAccountFundsInfoData.TencentAdAccountFundsInfo::getBalance).sum();
        return adAccountManager.updateBalanceAndCostById(accountInfo.getId(), balance);
    }

    @Override
    public boolean refreshCampaignInfo(AdAccountEntity accountInfo) {
        return this.refreshTencentCampaign(accountInfo.getEa(), accountInfo);
    }

    @Override
    public boolean refreshCampaignData(AdAccountEntity accountInfo, int day) {
        return true;
    }

    @Override
    public boolean refreshMarketingEventLeads(AdAccountEntity accountInfo, Integer day, List<Date> dateList) {
        return true;
    }

    @Override
    public void refreshAllData(String ea, String adAccountId, String source) {
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            log.info("TencentAdMarketingManager refreshAllData failed check no marketing_integration_app version ea:{}", ea);
            return;
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.refreshAllData getTencentAccessToken fail, adAccountEntity:{}", adAccountEntity);
            return;
        }
        TencentAdResult<GetTencentAdAccountFundsInfoData> tencentAdAccountFundsInfoResult = accountApiManager.getTencentAdAccountFundsInfo(adAccountEntity.getAccountId(), accessTokenOpt.get());
        if (!tencentAdAccountFundsInfoResult.isSuccess() || tencentAdAccountFundsInfoResult.getData() == null || CollectionUtils.isEmpty(tencentAdAccountFundsInfoResult.getData().getList())) {
            log.info("TencentAdMarketingManager.refreshAccountInfo getTencentAdAccountFundsInfo fail, accountInfo:{}", adAccountId);
            return;
        }
        try {
            adAccountManager.updateAdAccountStatusById(AccountStatusEnum.REFRESHING.getStatus(), adAccountEntity.getId());
            refreshTencentCampaign(ea, adAccountEntity);
            Date now = new Date();
            int totalCount = tencentAdGroupDAO.queryAdGroupTotalCount(ea, adAccountId);
            // 如果没有数据，可能是之前没同步，那么不过滤时间
            Date beginTime = DateUtil.minusDay(now, tencentAdGroupRefreshDay);
            refreshTencentAdGroup(ea, adAccountEntity, totalCount <= 0 ? null : DateUtil.getTimesMorning(beginTime));
            syncTencentCampaignToMarketingEventObj(ea, adAccountEntity.getAccountId(), adAccountId);
            syncTencentAdGroupToSubMarketingEventObj(ea, adAccountId);
            String startDate = DateUtil.format2(beginTime);
            String endDate = DateUtil.format2(now);
            refreshTencentAdGroupData(ea, adAccountEntity, adAccountId, startDate, endDate);
            syncTencentClueDataToCrm(ea, adAccountEntity, adAccountId, DateUtil.getSomeDay(new Date(), -1).getTime() / 1000, new Date().getTime() / 1000);
        } catch (Exception e) {
            log.error("TencentAdMarketingManager refreshAllData fail, ea: {} accountId:{} ", ea, adAccountId, e);
        } finally {
            adAccountManager.updateAdAccountStatusById(AccountStatusEnum.NORMAL.getStatus(), adAccountEntity.getId());
        }

    }

    @Override
    public void refreshKeywordData(String ea, String adAccountId, String source) {
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            log.info("TencentAdMarketingManager refreshKeywordData failed check no marketing_integration_app version ea:{}", ea);
            return;
        }
        if (!adCommonManager.isSyncAdKeyword(ea)) {
            log.info("tencent isSyncAdKeyword ea: {} is not sync keyword:", ea);
            return;
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }
        refreshKeyword(adAccountEntity, source);
        refreshDataManager.syncAdKeywordToMarketingKeywordObj(ea, adAccountEntity.getId(), source);     //同步关键词
        refreshDataManager.syncKeywordServingPlanV2(ea, adAccountEntity.getId(), source); //同步关键词投放计划
        String lastDayDate = DateUtil.parse(DateUtil.getSomeDay(new Date(), -1), DateUtil.DATE_FORMAT_DAY);
        refreshDataManager.syncMarketingTermServingLinesDataByKeyword(adAccountEntity, source, lastDayDate);
    }

    /**
     * 更新腾讯广告组数据
     *
     * @param ea              企业账号
     * @param adAccountEntity 账户信息
     * @param adAccountId     adAccount广告账户表主键id
     */
    public void refreshTencentAdGroupData(String ea, AdAccountEntity adAccountEntity, String adAccountId, String startDate, String endDate) {
        long dateMillis = System.currentTimeMillis();
        if (StringUtils.isBlank(startDate)) {
            startDate = DateUtil.dateMillis2String(DateUtil.getLastYearFromTime(dateMillis), "yyy-MM-dd");
        }
        if (StringUtils.isBlank(endDate)) {
            endDate = DateUtil.dateMillis2String(dateMillis, "yyy-MM-dd");
        }
        int pageSize = 100;
        String lastId;
        List<TencentAdGroupEntity> tencentAdGroupEntities = tencentAdGroupDAO.scanByAdAccountId(ea, adAccountId, null, pageSize);
        while(CollectionUtils.isNotEmpty(tencentAdGroupEntities)) {
            Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
            if (!accessTokenOpt.isPresent()) {
                return;
            }
            String accessToken = accessTokenOpt.get();
            List<Long> v1VersionAdGroupIdList = Lists.newArrayList();
            List<Long> v3VersionAdGroupIdList = Lists.newArrayList();
            for (TencentAdGroupEntity tencentAdGroupEntity : tencentAdGroupEntities) {
                Long campaignId = tencentAdGroupEntity.getCampaignId();
                if (campaignId == null || campaignId <= 0) {
                    v3VersionAdGroupIdList.add(tencentAdGroupEntity.getAdgroupId());
                } else {
                    v1VersionAdGroupIdList.add(tencentAdGroupEntity.getAdgroupId());
                }
            }
            List<TencentAdGroupDataResult.TencentAdGroupData> tencentAdGroupDataResultList = Lists.newArrayList();
            // 腾讯的报表数据新版旧版是互相隔离的，得单独去查
            if (CollectionUtils.isNotEmpty(v1VersionAdGroupIdList)) {
                TencentAdResult<TencentAdGroupDataResult> tencentAdGroupDataResult = campaignApiManager.getTencentAdGroupData(adAccountEntity.getAccountId(), accessToken, 1, v1VersionAdGroupIdList.size(), startDate, endDate, v1VersionAdGroupIdList, CampaignApiManager.TENCENT_V1_VERSION);
                if (tencentAdGroupDataResult != null && tencentAdGroupDataResult.isSuccess() && tencentAdGroupDataResult.getData() != null && CollectionUtils.isNotEmpty(tencentAdGroupDataResult.getData().getList())) {
                    tencentAdGroupDataResultList.addAll(tencentAdGroupDataResult.getData().getList());
                }
            }
            if (CollectionUtils.isNotEmpty(v3VersionAdGroupIdList)) {
                TencentAdResult<TencentAdGroupDataResult> tencentAdGroupDataResult = campaignApiManager.getTencentAdGroupData(adAccountEntity.getAccountId(), accessToken, 1, v3VersionAdGroupIdList.size(), startDate, endDate, v3VersionAdGroupIdList, CampaignApiManager.TENCENT_V3_VERSION);
                if (tencentAdGroupDataResult != null && tencentAdGroupDataResult.isSuccess() && tencentAdGroupDataResult.getData() != null && CollectionUtils.isNotEmpty(tencentAdGroupDataResult.getData().getList())) {
                    tencentAdGroupDataResultList.addAll(tencentAdGroupDataResult.getData().getList());
                }
            }
            // 去这里去重一下，谨防腾讯新旧接口又融合了
            List<TencentAdGroupDataResult.TencentAdGroupData> finalTencentAdGroupDataResultList = Lists.newArrayList();
            Set<Long> adGroupIdSet = Sets.newHashSet();
            for (TencentAdGroupDataResult.TencentAdGroupData tencentAdGroupData : tencentAdGroupDataResultList) {
                if (adGroupIdSet.contains(tencentAdGroupData.getAdgroup_id())) {
                    continue;
                }
                adGroupIdSet.add(tencentAdGroupData.getAdgroup_id());
                finalTencentAdGroupDataResultList.add(tencentAdGroupData);
            }
            saveTencentAdGroupData(ea, adAccountId, finalTencentAdGroupDataResultList);
            lastId = tencentAdGroupEntities.get(tencentAdGroupEntities.size() - 1).getId();
            tencentAdGroupEntities = tencentAdGroupDAO.scanByAdAccountId(ea, adAccountId, lastId, pageSize);
        }
    }

    /**
     * 保存广告组数据
     *
     * @param ea                     企业账户id
     * @param adAccountId            广告账户表主键id
     * @param tencentAdGroupDataList 广告组数据
     */
    private void saveTencentAdGroupData(String ea, String adAccountId, List<TencentAdGroupDataResult.TencentAdGroupData> tencentAdGroupDataList) {
        if (CollectionUtils.isEmpty(tencentAdGroupDataList)) {
            return;
        }
        List<Long> adGroupIds = tencentAdGroupDataList.stream().map(TencentAdGroupDataResult.TencentAdGroupData::getAdgroup_id).collect(Collectors.toList());
        List<String> reportTimes = tencentAdGroupDataList.stream().map(TencentAdGroupDataResult.TencentAdGroupData::getDate).collect(Collectors.toList());
        List<TencentAdGroupDataEntity> entities = tencentAdGroupDataDAO.queryTencentAdGroupDataList(ea, adGroupIds, adAccountId, DateUtil.parseDateList(reportTimes, "yyyy-MM-dd"));
        List<TencentAdGroupDataEntity> allEntityList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(entities)) {
            List<TencentAdGroupDataResult.TencentAdGroupData> updateTencentAdGroupDataList = new ArrayList<>();
            for (TencentAdGroupDataEntity entity : entities) {
                updateTencentAdGroupDataList.addAll(tencentAdGroupDataList.stream().filter(tencentAdGroupData -> Objects.equals(tencentAdGroupData.getAdgroup_id(), entity.getAdgroupId()) && Objects.equals(tencentAdGroupData.getDate(), DateUtil.format("yyyy-MM-dd", entity.getReportTime()))).collect(Collectors.toList()));
                tencentAdGroupDataList.removeIf(tencentAdGroupData -> Objects.equals(tencentAdGroupData.getAdgroup_id(), entity.getAdgroupId()) && Objects.equals(tencentAdGroupData.getDate(), DateUtil.format("yyyy-MM-dd", entity.getReportTime())));
            }
            List<TencentAdGroupDataEntity> updateEntityList = pageUpdateTencentAdGroupData(ea, adAccountId, updateTencentAdGroupDataList);
            allEntityList.addAll(updateEntityList);
        }
        List<TencentAdGroupDataEntity> addEntityList = pageAddTencentAdGroupData(ea, adAccountId, tencentAdGroupDataList);
        allEntityList.addAll(addEntityList);
        buildAdvertisingDetailsObj(ea, adAccountId, allEntityList);
    }

    public void reindexAdvertisingDetailsObj(String ea) {
        int totalCount = tencentAdGroupDataDAO.countByEa(ea);
        int count = 0;
        String lastId = null;
        int pageSize = 2000;
        while(count < totalCount) {
            long t1 = System.currentTimeMillis();
            List<TencentAdGroupDataEntity> list = tencentAdGroupDataDAO.scanById(ea, lastId, pageSize);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            List<TencentAdGroupDataEntity> finalList = list.stream().filter(e -> StringUtils.isNotBlank(e.getAdAccountId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(finalList)) {
                break;
            }
            count += list.size();
            lastId = list.get(list.size() - 1).getId();
            Map<String,List<TencentAdGroupDataEntity>> accountIdMap = finalList.stream().collect(Collectors.groupingBy(TencentAdGroupDataEntity::getAdAccountId));
            accountIdMap.forEach((accountId, result) -> buildAdvertisingDetailsObj(ea, accountId, result));
            log.info("tencent reindexAdvertisingDetailsObj ea: {}, totalCount: {}, count:{} 耗时: {}ms", ea, totalCount, count, System.currentTimeMillis() - t1);
        }
    }

    private void buildAdvertisingDetailsObj(String ea, String adAccountId, List<TencentAdGroupDataEntity> adGroupDataEntityList) {
        if (CollectionUtils.isEmpty(adGroupDataEntityList)) {
            return;
        }
        List<Long> adGroupIdList = adGroupDataEntityList.stream().map(TencentAdGroupDataEntity::getAdgroupId).collect(Collectors.toList());
        List<TencentAdGroupEntity> adGroupEntityList = tencentAdGroupDAO.queryTencentAdGroupList(ea, adAccountId, adGroupIdList);
        Map<Long, String> adGroupIdToMarketingEventIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(adGroupEntityList)) {
            adGroupEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getAdAccountId())).forEach(e -> adGroupIdToMarketingEventIdMap.put(e.getAdgroupId(), e.getSubMarketingEventId()));
        }
        List<CreateAdvertisingDetailObjArg> argList = Lists.newArrayList();
        for (TencentAdGroupDataEntity entity : adGroupDataEntityList) {
            CreateAdvertisingDetailObjArg arg = new CreateAdvertisingDetailObjArg();
            arg.setEa(ea);
            arg.setLaunchDate(DateUtil.format2(entity.getReportTime()));
            arg.setMarketingEventId(adGroupIdToMarketingEventIdMap.get(entity.getAdgroupId()));
            arg.setAdAccountId(entity.getAdAccountId());
            arg.setCampaignOrAdGroupId(entity.getAdgroupId());
            arg.setShow(entity.getPv() == null ? 0 : entity.getPv().longValue());
            arg.setClick(entity.getValidClickCount() == null ? 0 : entity.getValidClickCount().longValue());
            Double cost = entity.getCost();
            BigDecimal costBigDecimal = BigDecimal.ZERO;
            if (cost != null && cost > 0) {
                costBigDecimal = new BigDecimal(Double.toString(cost)).divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);
            }
            arg.setCost(costBigDecimal.doubleValue());
            argList.add(arg);
        }
        advertisingDetailsObjManager.tryUpdateOrCreateObj(argList);
    }

    public List<TencentAdGroupDataEntity> pageAddTencentAdGroupData(String ea, String adAccountId, List<TencentAdGroupDataResult.TencentAdGroupData> tencentAdGroupDataList) {
        List<TencentAdGroupDataEntity> tencentAdGroupDataEntityList = Lists.newArrayList();
        if (tencentAdGroupDataList.size() < 500) {
            PageUtil<TencentAdGroupDataResult.TencentAdGroupData> pageData = new PageUtil<>(tencentAdGroupDataList, 500);
            for (int i = 1; i <= pageData.getPageCount(); i++) {
                List<TencentAdGroupDataResult.TencentAdGroupData> pagedList = pageData.getPagedList(i);
                List<TencentAdGroupDataEntity> list = batchAddTencentAdGroupData(ea, adAccountId, pagedList);
                tencentAdGroupDataEntityList.addAll(list);
            }
        } else {
            List<TencentAdGroupDataEntity> list = batchAddTencentAdGroupData(ea, adAccountId, tencentAdGroupDataList);
            tencentAdGroupDataEntityList.addAll(list);
        }
        return tencentAdGroupDataEntityList;
    }

    public List<TencentAdGroupDataEntity> pageUpdateTencentAdGroupData(String ea, String adAccountId, List<TencentAdGroupDataResult.TencentAdGroupData> updateTencentAdGroupDataList) {
        List<TencentAdGroupDataEntity> tencentAdGroupDataEntityList = Lists.newArrayList();
        if (updateTencentAdGroupDataList.size() > 500) {
            PageUtil<TencentAdGroupDataResult.TencentAdGroupData> pageData = new PageUtil<>(updateTencentAdGroupDataList, 500);
            for (int i = 1; i <= pageData.getPageCount(); i++) {
                List<TencentAdGroupDataResult.TencentAdGroupData> pagedList = pageData.getPagedList(i);
                List<TencentAdGroupDataEntity> list = batchUpdateTencentAdGroupData(ea, adAccountId, pagedList);
                tencentAdGroupDataEntityList.addAll(list);
            }
        } else {
            List<TencentAdGroupDataEntity> list = batchUpdateTencentAdGroupData(ea, adAccountId, updateTencentAdGroupDataList);
            tencentAdGroupDataEntityList.addAll(list);
        }
        return tencentAdGroupDataEntityList;
    }


    private List<TencentAdGroupDataEntity> batchUpdateTencentAdGroupData(String ea, String adAccountId, List<TencentAdGroupDataResult.TencentAdGroupData> updateTencentAdGroupDataList) {
        if (CollectionUtils.isEmpty(updateTencentAdGroupDataList)) {
            log.info("TencentAdMarketingManager.batchUpdateTencentAdGroupData updateTencentAdGroupDataList is null, ea:{}. adAccountId:{}", ea, adAccountId);
            return Lists.newArrayList();
        }
        List<TencentAdGroupDataEntity> adGroupDataEntityList = new ArrayList<>();
        for (TencentAdGroupDataResult.TencentAdGroupData adGroupData : updateTencentAdGroupDataList) {
            TencentAdGroupDataEntity entity = new TencentAdGroupDataEntity();
            entity.setAdgroupId(adGroupData.getAdgroup_id());
            entity.setPv(adGroupData.getView_count());
            entity.setCost(adGroupData.getCost());
            entity.setCpc(adGroupData.getCpc());
            entity.setThousandDisplayPrice(adGroupData.getThousand_display_price());
            entity.setValidClickCount(adGroupData.getValid_click_count());
            entity.setReportTime(DateUtil.parse(adGroupData.getDate(), "yyyy-MM-dd"));
            entity.setUpdateTime(new Date());
            adGroupDataEntityList.add(entity);
        }
        tencentAdGroupDataDAO.batchUpdateTencentAdGroupData(ea, adAccountId, adGroupDataEntityList);
        return adGroupDataEntityList;
    }

    /**
     * 同步腾讯广告线索到线索对象
     *
     * @param ea              纷享企业账号
     * @param adAccountEntity 账户信息
     * @param adAccountId     广告账户表的主键id
     * @param startTime       起始时间
     * @param endTime         结束时间
     */
    public void syncTencentClueDataToCrm(String ea, AdAccountEntity adAccountEntity, String adAccountId, Long startTime, Long endTime) {
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            return;
        }
        TencentAdResult<TencentLeadsResult> tencentLeadsResult = campaignApiManager.getTencentLeads(adAccountEntity.getAccountId(), accessTokenOpt.get(), 1, 200, startTime, endTime);
        if (tencentLeadsResult == null || !tencentLeadsResult.isSuccess() || tencentLeadsResult.getData() == null || CollectionUtils.isEmpty(tencentLeadsResult.getData().getLeads_info()) || tencentLeadsResult.getData().getPage_info() == null) {
            return;
        }
        PageInfo pageInfo = tencentLeadsResult.getData().getPage_info();
        List<AdLeadsMappingDataEntity> adLeadsMappingDataEntities = adLeadsMappingDataDAO.queryAdLeadsMappingDataByEaAndId(ea, AdSourceEnum.SOURCE_TENCETN.getSource());
        if (CollectionUtils.isEmpty(adLeadsMappingDataEntities) || adLeadsMappingDataEntities.get(0) == null) {
            log.info("TencentAdMarketingManager.syncTencentClueDataToCrm queryAdLeadsMappingDataByEaAndId is null, ea:{}, accountId:{}", ea, adAccountEntity.getAccountId());
            return;
        }
        AdLeadsMappingDataEntity adLeadsMappingDataEntity = adLeadsMappingDataEntities.get(0);
        multiGetAndSyncTencentLeads(adAccountEntity, pageInfo.getTotal_page(), startTime, endTime, adLeadsMappingDataEntity);
    }


    /**
     * 分多次获取线索并同步到线索对象
     *
     * @param adAccountEntity          广告账户实体
     * @param totalPage                总页数
     * @param startTime                拉取的开始时间
     * @param endTime                  拉取的截止时间
     * @param adLeadsMappingDataEntity 线索映射和函数名称
     */
    public void multiGetAndSyncTencentLeads(AdAccountEntity adAccountEntity, Integer totalPage, Long startTime, Long endTime, AdLeadsMappingDataEntity adLeadsMappingDataEntity) {
        Integer page = 1;
        Integer pageSize = 200;
        String ea = adAccountEntity.getEa();
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.multiGetAndSyncTencentLeads adTokenManager.getTencentAccessToken fail, accessTokenOpt:{}, adAccountEntity:{}", accessTokenOpt, adAccountEntity);
            return;
        }
        for (; page <= totalPage; page++) {
            TencentAdResult<TencentLeadsResult> tencentLeadsResult = campaignApiManager.getTencentLeads(adAccountEntity.getAccountId(), accessTokenOpt.get(), page, pageSize, startTime, endTime);
            if (tencentLeadsResult == null || !tencentLeadsResult.isSuccess() || tencentLeadsResult.getData() == null || CollectionUtils.isEmpty(tencentLeadsResult.getData().getLeads_info())) {
                log.info("TencentAdMarketingManager.multiGetAndSyncTencentLeads campaignApiManager.getTencentLeads fail, tencentLeadsResult:{}, adAccountEntity:{}", tencentLeadsResult, adAccountEntity);
                return;
            }
            for (TencentLeadsResult.TencentLeads tencentLeads : tencentLeadsResult.getData().getLeads_info()) {
                String subMarketingEventId = getSubMarketingEventIdByAdGroupId(ea, tencentLeads.getAdgroup_id(), adAccountEntity.getId());
                Long thirdLeadsId = tencentLeads.getLeads_id();
                HashMap<String, Object> leadsMap = GsonUtil.fromJson(GsonUtil.toJson(tencentLeads), HashMap.class);
                if (Objects.nonNull(leadsMap.get("leads_create_time"))) {
                    Long leadsCreateTime = Objects.requireNonNull(DateUtil.parse(String.valueOf(leadsMap.get("leads_create_time")))).getTime();
                    leadsMap.put("leads_create_time", leadsCreateTime);
                }
                if (Objects.nonNull(leadsMap.get("leads_action_time"))) {
                    Long leadsCreateTime = Objects.requireNonNull(DateUtil.parse(String.valueOf(leadsMap.get("leads_action_time")))).getTime();
                    leadsMap.put("leads_action_time", leadsCreateTime);
                }
                Map<String, Object> objectMap = refreshDataManager.syncLeadCallFunc(ea, ObjectData.convert(leadsMap), adLeadsMappingDataEntity.getCustomFuncApiName());
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addLeadsResult = refreshDataManager.syncClueDataToCrmObj(ea, String.valueOf(thirdLeadsId), AdSourceEnum.SOURCE_TENCETN.getValue(), adLeadsMappingDataEntity, ObjectData.convert((Map<String, Object>) objectMap.get("functionResult")), subMarketingEventId);
                if (addLeadsResult == null || !addLeadsResult.isSuccess() || addLeadsResult.getData() == null || addLeadsResult.getData().getObjectData() == null) {
                    continue;
                }
                String leadId = String.valueOf(addLeadsResult.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName()));
                marketingStatLogPersistorManger.sendLeadData(ea, leadId, null, MarketingStatLogPersistorManger.CHANNEL_AD_OTHER_SYNC);
                baiduAdMarketingManager.syncCampaignMember(ea, leadId, AdSourceEnum.SOURCE_TENCETN.getSource(), tencentLeads.getAdgroup_name(), null, false, null, null, null, null);
            }
        }
    }


    /**
     * 查询腾讯广告adGroup对应的市场活动id
     *
     * @param ea               企业账号
     * @param adGroupId        腾讯广告组id
     * @param adAccountId      广告账户表主键id
     * @return 市场活动id
     */
    private String getSubMarketingEventIdByAdGroupId(String ea, Long adGroupId, String adAccountId) {
        if (adGroupId == null) {
            return null;
        }
        TencentAdGroupEntity entity = tencentAdGroupDAO.queryTencentAdGroup(ea, adGroupId, adAccountId);
        if (entity == null) {
            return null;
        }
        return entity.getSubMarketingEventId();
    }


    /**
     * 同步腾讯广告广告组到子级市场活动
     *
     * @param ea               纷享企业账号
     * @param adAccountId      广告账户表的主键id
     */
    public void syncTencentAdGroupToSubMarketingEventObj(String ea, String adAccountId) {
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }
        int pageSize = 100;
        String lastId;
        List<TencentAdGroupEntity> tencentAdGroupEntities = tencentAdGroupDAO.scanByAdAccountId(ea, adAccountId, null, pageSize);
        while(CollectionUtils.isNotEmpty(tencentAdGroupEntities)) {
            List<AdvertiserAdEntity> advertiserAdEntityList = transferAdvertiserAdEntityList(tencentAdGroupEntities);
            refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, advertiserAdEntityList, AdSourceEnum.SOURCE_TENCETN.getSource());
            lastId = tencentAdGroupEntities.get(tencentAdGroupEntities.size() - 1).getId();
            tencentAdGroupEntities = tencentAdGroupDAO.scanByAdAccountId(ea, adAccountId, lastId, pageSize);
        }
    }

    public static List<AdvertiserAdEntity> transferAdvertiserAdEntityList(List<TencentAdGroupEntity> tencentAdGroupEntities) {
        List<AdvertiserAdEntity> advertiserAdEntityList = Lists.newArrayList();
        for (TencentAdGroupEntity tencentAdGroupEntity : tencentAdGroupEntities) {
            AdvertiserAdEntity advertiserAdEntity = new AdvertiserAdEntity();
            advertiserAdEntity.setId(tencentAdGroupEntity.getId());
            advertiserAdEntity.setEa(tencentAdGroupEntity.getEa());
            advertiserAdEntity.setAdAccountId(tencentAdGroupEntity.getAdAccountId());
            advertiserAdEntity.setAdId(tencentAdGroupEntity.getAdgroupId());
            advertiserAdEntity.setAdName(tencentAdGroupEntity.getAdgroupName());
            advertiserAdEntity.setSubMarketingEventId(tencentAdGroupEntity.getSubMarketingEventId());
            advertiserAdEntity.setSource(AdSourceEnum.SOURCE_TENCETN.getSource());
            advertiserAdEntity.setCampaignId(tencentAdGroupEntity.getCampaignId());
            advertiserAdEntityList.add(advertiserAdEntity);
        }
        return advertiserAdEntityList;
    }

    /**
     * 同步腾讯广告推广计划到crm市场活动对象
     *
     * @param ea               纷享企业账号
     * @param tencentAccountId 腾讯广告账户id
     * @param adAccountId      广告账户表的主键id
     */
    public void syncTencentCampaignToMarketingEventObj(String ea, Long tencentAccountId, String adAccountId) {
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
        if (adAccountEntity == null) {
            log.info("TencentAdMarketingManager.syncTencentCampaignToMarketingEventObj queryAccountById adAccountEntity is null, ea:{}, tencentAccountId:{}, adAccountId:{}", ea, tencentAccountId, adAccountId);
            return;
        }
        int pageSize = 100;
        String lastId;
        List<TencentCampaignEntity> tencentCampaignEntityList = tencentCampaignDAO.scanByAdAccountId(ea, adAccountId, null, pageSize);
        while(CollectionUtils.isNotEmpty(tencentCampaignEntityList)) {
            List<AdCampaignEntity> adCampaignEntities = Lists.newArrayList();
            adCampaignEntities.addAll(tencentCampaignEntityList);
            refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, adCampaignEntities, AdSourceEnum.SOURCE_TENCETN.getSource());
            lastId = tencentCampaignEntityList.get(tencentCampaignEntityList.size() - 1).getId();
            tencentCampaignEntityList = tencentCampaignDAO.scanByAdAccountId(ea, adAccountId, lastId, pageSize);
        }
    }

    public void refreshTencentAdGroupPeriodically(AdAccountEntity adAccountEntity, int day) {
        try {
            Date now = new Date();
            Date beginTime = DateUtil.minusDay(now, day);
            refreshTencentAdGroup(adAccountEntity.getEa(), adAccountEntity, DateUtil.getTimesMorning(beginTime));
            syncTencentAdGroupToSubMarketingEventObj(adAccountEntity.getEa(), adAccountEntity.getId());
            String startDate = DateUtil.format2(beginTime);
            // 和百度头条一致吧，只获取昨天的数据
            refreshTencentAdGroupData(adAccountEntity.getEa(), adAccountEntity, adAccountEntity.getId(), startDate, startDate);
        } catch (Exception e) {
            log.error("refreshTencentAdGroupPeriodically error, account: {}", adAccountEntity, e);
        }
    }
    /**
     * 拉取腾讯广告广告组
     * (腾讯展示的广告即广告组)
     *
     * @param ea              纷享企业账户
     * @param adAccountEntity 账户信息
     * @return 是否同步成功
     */
    public boolean refreshTencentAdGroup(String ea, AdAccountEntity adAccountEntity, Long beginTime) {
        Integer page = 1;
        Integer pageSize = 100;
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.refreshTencentAdGroup getTencentAccessToken fail, accountInfo:{}", adAccountEntity);
            return false;
        }
        String accessToken = accessTokenOpt.get();
        List<String> apiVersions = Lists.newArrayList("v1.3", "v3.0");  //腾讯搜索广告使用v1.3接口获取；展示广告使用v3.0接口
        for (String v: apiVersions) {
            TencentAdResult<TencentAdGroupResult> tencentAdGroupResult = campaignApiManager.getTencentAdGroup(adAccountEntity.getAccountId(), accessToken, page, pageSize, beginTime, v);
            if (tencentAdGroupResult == null || tencentAdGroupResult.getData() == null
                    || CollectionUtils.isEmpty(tencentAdGroupResult.getData().getList())
                    || tencentAdGroupResult.getData().getPage_info() == null) {
               continue;
            }
            PageInfo pageInfo = tencentAdGroupResult.getData().getPage_info();
            if(pageInfo.getTotal_page() > 1) {
                multiGetAndSaveTencentAdGroups(ea, adAccountEntity, pageInfo.getTotal_page(), beginTime, v);
            } else {
                saveTencentAdGroups(ea, adAccountEntity.getId(), tencentAdGroupResult);
            }
        }
        return true;
    }


    /**
     * 拉取腾讯广告推广计划同步更新
     *
     * @param ea              纷享企业账号
     * @param adAccountEntity 腾讯广告账号id
     * @return 是否更新成功
     */
    public boolean refreshTencentCampaign(String ea, AdAccountEntity adAccountEntity) {
        Integer page = 1;
        Integer pageSize = 100;
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.refreshTencentCampaign getTencentAccessToken fail, accountInfo:{}", adAccountEntity);
            return false;
        }
        String accessToken = accessTokenOpt.get();
        TencentAdResult<TencentCampaignResult> tencentCampaignResult = campaignApiManager.getTencentCampaigns(adAccountEntity.getAccountId(), accessToken, page, pageSize);
        if (tencentCampaignResult.getData() == null || CollectionUtils.isEmpty(tencentCampaignResult.getData().getList()) || tencentCampaignResult.getData().getPage_info() == null) {
            return true;
        }
        PageInfo pageInfo = tencentCampaignResult.getData().getPage_info();
        if(pageInfo.getTotal_page() > 1) {
            multiGetAndSaveTencentCampaigns(ea, adAccountEntity, pageInfo.getTotal_page());
        } else {
            saveTencentCampaigns(ea, adAccountEntity.getId(), tencentCampaignResult);
        }
        return true;
    }

    private boolean batchUpdateTencentCampaign(String ea, String adAccountId, List<TencentCampaignResult.TencentCampaign> updateCampaignList) {
        if (CollectionUtils.isEmpty(updateCampaignList)) {
            return true;
        }
        List<TencentCampaignEntity> campaignEntityList = new ArrayList<>();
        for (TencentCampaignResult.TencentCampaign campaign : updateCampaignList) {
            TencentCampaignEntity entity = BeanUtil.copy(campaign, TencentCampaignEntity.class);
            entity.setCampaignName(campaign.getCampaign_name());
            Integer configuredStatus = TencentConfiguredStatusEnum.getStatusByName(campaign.getConfigured_status());
            entity.setConfiguredStatus(configuredStatus == null ? 0 : configuredStatus);
            Integer campaignType = TencentCampaignTypeEnum.getStatusByName(campaign.getCampaign_type());
            entity.setCampaignType(campaignType == null ? 0 : campaignType);
            Integer promotedObjectType = TencentPromotedObjectTypeEnum.getStatusByName(campaign.getPromoted_object_type());
            entity.setPromotedObjectType(promotedObjectType == null ? 0 : promotedObjectType);
            entity.setDailyBudget(campaign.getDaily_budget() == null ? 0D : campaign.getDaily_budget());
            entity.setTotalBudget(campaign.getTotal_budget() == null ? 0D : campaign.getTotal_budget());
            entity.setBudgetReachDate(campaign.getBudget_reach_date() == null ? 0L : campaign.getBudget_reach_date());
            entity.setIsDeleted(campaign.getIs_deleted() != null && campaign.getIs_deleted());
            entity.setUpdateTime(new Date());
            campaignEntityList.add(entity);
        }
        tencentCampaignDAO.batchUpdateTencentCampaign(ea, adAccountId, campaignEntityList);
        return true;
    }


    private boolean batchAddTencentCampaign(String ea, String adAccountId, List<TencentCampaignResult.TencentCampaign> campaignList) {
        if (CollectionUtils.isEmpty(campaignList)) {
            return true;
        }
        List<TencentCampaignEntity> campaignEntityList = new ArrayList<>();
        for (TencentCampaignResult.TencentCampaign campaign : campaignList) {
            TencentCampaignEntity entity = BeanUtil.copy(campaign, TencentCampaignEntity.class);
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setAdAccountId(adAccountId);
            entity.setCampaignId(campaign.getCampaign_id());
            entity.setCampaignName(campaign.getCampaign_name());
            Integer configuredStatus = TencentConfiguredStatusEnum.getStatusByName(campaign.getConfigured_status());
            entity.setConfiguredStatus(configuredStatus == null ? 0 : configuredStatus);
            Integer campaignType = TencentCampaignTypeEnum.getStatusByName(campaign.getCampaign_type());
            entity.setCampaignType(campaignType == null ? 0 : campaignType);
            Integer promotedObjectType = TencentPromotedObjectTypeEnum.getStatusByName(campaign.getPromoted_object_type());
            entity.setPromotedObjectType(promotedObjectType);
            entity.setDailyBudget(campaign.getDaily_budget() == null ? 0D : campaign.getDaily_budget());
            entity.setTotalBudget(campaign.getTotal_budget() == null ? 0D : campaign.getTotal_budget());
            entity.setBudgetReachDate(campaign.getBudget_reach_date() == null ? 0L : campaign.getBudget_reach_date());
            entity.setIsDeleted(campaign.getIs_deleted() != null && campaign.getIs_deleted());
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            campaignEntityList.add(entity);
        }
        tencentCampaignDAO.batchAddTencentCampaign(campaignEntityList);
        return true;
    }

    private boolean batchUpdateTencentAdGroup(String ea, String adAccountId, List<TencentAdGroupResult.TencentAdGroup> updateAdGroupList, List<TencentAdGroupEntity> tencentAdGroupEntities) {
        if (CollectionUtils.isEmpty(updateAdGroupList)) {
            log.info("TencentAdMarketingManager.batchUpdateTencentAdGroup updateAdGroupList is null, ea:{}. adAccountId:{}", ea, adAccountId);
            return true;
        }
        List<TencentAdGroupEntity> adGroupEntityList = new ArrayList<>();
        for (TencentAdGroupResult.TencentAdGroup adGroup : updateAdGroupList) {
            TencentAdGroupEntity entity = new TencentAdGroupEntity();
            entity.setCampaignId(Objects.nonNull(adGroup.getCampaign_id()) ? adGroup.getCampaign_id() : 0);
            entity.setAdgroupId(adGroup.getAdgroup_id());
            entity.setAdgroupName(adGroup.getAdgroup_name());
            Integer status = TencentAdStatusEnum.getStatusByName(adGroup.getStatus());
            if(status == null && StringUtils.isNotBlank(adGroup.getSystem_status())) {
                status = TencentAdStatusEnum.getStatusByName(adGroup.getSystem_status());
            }
            entity.setStatus(status == null ? 0 : status);
            entity.setBidAmount(adGroup.getBid_amount() == null ? 0D : adGroup.getBid_amount());
            entity.setTotalBudget(adGroup.getTotal_budget() == null ? 0D : adGroup.getTotal_budget());
            entity.setDailyBudget(adGroup.getDaily_budget() == null ? 0D : adGroup.getDaily_budget());
            Integer bigMode = TencentBidModEnum.getStatusByName(adGroup.getBid_mode());
            entity.setBidMode(bigMode == null ? 0 : bigMode);
            List<String> siteSet = CollectionUtils.isEmpty(adGroup.getSite_set()) ? Lists.newArrayList("") : adGroup.getSite_set();
            entity.setSiteSet(siteSet);
            entity.setIsDeleted(adGroup.getIs_deleted() != null && adGroup.getIs_deleted());
            entity.setUpdateTime(new Date());
            adGroupEntityList.add(entity);
        }
        tencentAdGroupDAO.batchUpdateTencentAdGroup(ea, adAccountId, adGroupEntityList);
        return true;
    }


    private boolean batchAddTencentAdGroup(String ea, String adAccountId, List<TencentAdGroupResult.TencentAdGroup> adGroupList) {
        if (CollectionUtils.isEmpty(adGroupList)) {
            log.info("TencentAdMarketingManager.batchAddTencentAdGroup adGroupList is null, ea:{}. adAccountId:{}", ea, adAccountId);
            return true;
        }
        List<TencentAdGroupEntity> adGroupEntityList = new ArrayList<>();
        for (TencentAdGroupResult.TencentAdGroup adGroup : adGroupList) {
            TencentAdGroupEntity entity = BeanUtil.copy(adGroup, TencentAdGroupEntity.class);
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setAdAccountId(adAccountId);
            entity.setCampaignId(Objects.nonNull(adGroup.getCampaign_id()) ? adGroup.getCampaign_id() : 0);
            entity.setAdgroupId(adGroup.getAdgroup_id());
            entity.setAdgroupName(adGroup.getAdgroup_name());
            Integer status = TencentAdStatusEnum.getStatusByName(adGroup.getStatus());
            if(status == null && StringUtils.isNotBlank(adGroup.getSystem_status())) {
                status = TencentAdStatusEnum.getStatusByName(adGroup.getSystem_status());
            }
            entity.setStatus(status == null ? 0 : status);
            entity.setBidAmount(adGroup.getBid_amount());
            entity.setTotalBudget(adGroup.getTotal_budget());
            entity.setDailyBudget(adGroup.getDaily_budget());
            entity.setBidMode(TencentBidModEnum.getStatusByName(adGroup.getBid_mode()));
            List<String> siteSet = CollectionUtils.isEmpty(adGroup.getSite_set()) ? Lists.newArrayList("") : adGroup.getSite_set();
            entity.setSiteSet(siteSet);
            entity.setIsDeleted(adGroup.getIs_deleted());
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            adGroupEntityList.add(entity);
        }
        tencentAdGroupDAO.batchAddTencentAdGroup(adGroupEntityList, AdSourceEnum.SOURCE_TENCETN.getValue());
        return true;
    }

    private List<TencentAdGroupDataEntity> batchAddTencentAdGroupData(String ea, String adAccountId, List<TencentAdGroupDataResult.TencentAdGroupData> adGroupDataList) {
        if (CollectionUtils.isEmpty(adGroupDataList)) {
            log.info("TencentAdMarketingManager.batchAddTencentAdGroupData adGroupDataList is null, ea:{}. adAccountId:{}", ea, adAccountId);
            return Lists.newArrayList();
        }
        List<TencentAdGroupDataEntity> adGroupDataEntityList = new ArrayList<>();
        for (TencentAdGroupDataResult.TencentAdGroupData adGroupData : adGroupDataList) {
            TencentAdGroupDataEntity entity = BeanUtil.copy(adGroupData, TencentAdGroupDataEntity.class);
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setAdAccountId(adAccountId);
            entity.setAdgroupId(adGroupData.getAdgroup_id());
            entity.setPv(adGroupData.getView_count());
            entity.setCost(adGroupData.getCost());
            entity.setCpc(adGroupData.getCpc());
            entity.setThousandDisplayPrice(adGroupData.getThousand_display_price());
            entity.setValidClickCount(adGroupData.getValid_click_count());
            entity.setReportTime(DateUtil.parse(adGroupData.getDate(), "yyyy-MM-dd"));
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            adGroupDataEntityList.add(entity);
        }
        tencentAdGroupDataDAO.batchAddTencentAdGroupData(adGroupDataEntityList);
        return adGroupDataEntityList;
    }


    /**
     * 分多次拉取推广计划并保存
     *
     * @param ea              纷享企业账号
     * @param adAccountEntity 广告账户信息
     * @param times           拉取次数
     * @return List<TencentCampaignResult.TencentCampaignData>
     */
    public void multiGetAndSaveTencentCampaigns(String ea, AdAccountEntity adAccountEntity, Integer times) {
        Integer page = 1;
        Integer pageSize = 100;
        Long tencentAccountId = adAccountEntity.getAccountId();
        String adAccountId = adAccountEntity.getId();
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.multiGetAndSaveTencentCampaigns getTencentAccessToken fail, adAccountEntity:{}", adAccountEntity);
            return;
        }
        for (; page <= times; page++) {
            String accessToken = accessTokenOpt.get();
            TencentAdResult<TencentCampaignResult> tencentCampaignResult = campaignApiManager.getTencentCampaigns(tencentAccountId, accessToken, page, pageSize);
            if (tencentCampaignResult.getData() == null || CollectionUtils.isEmpty(tencentCampaignResult.getData().getList())) {
                log.info("TencentAdMarketingManager.multiGetAndSaveTencentCampaigns campaignApiManager.getTencentCampaigns fail, ea:{}, adAccountId:{}", ea, adAccountId);
                return;
            }
            saveTencentCampaigns(ea, adAccountId, tencentCampaignResult);
        }
    }

    private void saveTencentCampaigns(String ea, String adAccountId, TencentAdResult<TencentCampaignResult> tencentCampaignResult) {
        List<TencentCampaignResult.TencentCampaign> campaignList = tencentCampaignResult.getData().getList();
        List<Long> campaignIds = campaignList.stream().map(TencentCampaignResult.TencentCampaign::getCampaign_id).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            log.info("TencentAdMarketingManager.multiGetAndSaveTencentCampaigns campaignIds is null, ea:{}, adAccountId:{}", ea, adAccountId);
            return;
        }
        List<TencentCampaignEntity> tencentCampaignEntities = tencentCampaignDAO.queryTencentCampaignList(ea, adAccountId, campaignIds);
        if (CollectionUtils.isEmpty(tencentCampaignEntities)) {
            batchAddTencentCampaign(ea, adAccountId, campaignList);
        } else {
            List<Long> existCampaignId = tencentCampaignEntities.stream().map(TencentCampaignEntity::getCampaignId).collect(Collectors.toList());
            List<TencentCampaignResult.TencentCampaign> updateCampaignList = campaignList.stream().filter(tencentCampaign -> existCampaignId.contains(tencentCampaign.getCampaign_id())).collect(Collectors.toList());
            campaignList.removeAll(updateCampaignList);
            batchAddTencentCampaign(ea, adAccountId, campaignList);
            batchUpdateTencentCampaign(ea, adAccountId, updateCampaignList);
        }
    }

    /**
     * 分多次拉取推广计划并保存
     *
     * @param ea              纷享企业账号
     * @param adAccountEntity 广告账户信息
     * @param totalPage       总页数
     * @return List<TencentCampaignResult.TencentCampaignData>
     */
    private void multiGetAndSaveTencentAdGroups(String ea, AdAccountEntity adAccountEntity, Integer totalPage, Long beginTime, String apiVersion) {
        Integer page = 1;
        Integer pageSize = 100;
        Long tencentAccountId = adAccountEntity.getAccountId();
        String adAccountId = adAccountEntity.getId();
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.multiGetAndSaveTencentAdGroups getTencentAccessToken fail, adAccountEntity:{}", adAccountEntity);
            return;
        }
        for (; page <= totalPage; page++) {
            String accessToken = accessTokenOpt.get();
            TencentAdResult<TencentAdGroupResult> tencentCampaignResult = campaignApiManager.getTencentAdGroup(tencentAccountId, accessToken, page, pageSize, beginTime, apiVersion);
            if (tencentCampaignResult.getData() == null || CollectionUtils.isEmpty(tencentCampaignResult.getData().getList())) {
                log.info("TencentAdMarketingManager.multiGetAndSaveTencentAdGroups getTencentAdGroup fail, ea:{}, adAccountId:{}, tencentCampaignResult:{}", ea, adAccountId, tencentCampaignResult);
                return;
            }
            saveTencentAdGroups(ea, adAccountId, tencentCampaignResult);
        }
    }

    public void saveTencentAdGroups(String ea, String adAccountId, TencentAdResult<TencentAdGroupResult> tencentCampaignResult) {
        List<TencentAdGroupResult.TencentAdGroup> adGroupList = tencentCampaignResult.getData().getList();
        List<Long> adGroupIds = adGroupList.stream().map(TencentAdGroupResult.TencentAdGroup::getAdgroup_id).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adGroupIds)) {
            log.info("TencentAdMarketingManager.multiGetAndSaveTencentAdGroups adGroupIds is null, ea:{}, adAccountId:{}", ea, adAccountId);
            return;
        }
        List<TencentAdGroupEntity> tencentAdGroupEntities = tencentAdGroupDAO.queryTencentAdGroupList(ea, adAccountId, adGroupIds);
        if (CollectionUtils.isEmpty(tencentAdGroupEntities)) {
            batchAddTencentAdGroup(ea, adAccountId, adGroupList);
        } else {
            List<Long> existAdGroupIds = tencentAdGroupEntities.stream().map(TencentAdGroupEntity::getAdgroupId).collect(Collectors.toList());
            List<TencentAdGroupResult.TencentAdGroup> updateAdGroupList = adGroupList.stream().filter(tencentAdGroup -> existAdGroupIds.contains(tencentAdGroup.getAdgroup_id())).collect(Collectors.toList());
            adGroupList.removeAll(updateAdGroupList);
            batchAddTencentAdGroup(ea, adAccountId, adGroupList);
            batchUpdateTencentAdGroup(ea, adAccountId, updateAdGroupList, tencentAdGroupEntities);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshPrototypeRoomAccountData(String ea, int cost) {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.getAdPrototypeRoomAccount(ea, AdSourceEnum.SOURCE_TENCETN.getSource());
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return;
        }
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            initCampaignAndAdGroup(adAccountEntity);
            int campaignTotalCount = tencentCampaignDAO.countByAdAccountId(ea, adAccountEntity.getId());
            int pageSize = 1000;
            String lastId = null;
            int count = 0;
            while (count < campaignTotalCount) {
                List<TencentCampaignEntity> campaignEntityList = tencentCampaignDAO.scanByAdAccountId(ea, adAccountEntity.getId(), lastId, pageSize);
                if (CollectionUtils.isEmpty(campaignEntityList)) {
                    break;
                }
                int size = campaignEntityList.size();
                count += size;
                lastId = campaignEntityList.get(size - 1).getId();
                createAdGroupData(adAccountEntity, campaignEntityList, cost);
            }

        }
    }

    private void createAdGroupData(AdAccountEntity adAccountEntity, List<TencentCampaignEntity> campaignEntityList, int totalCost) {
        List<Long> campaignIdList = campaignEntityList.stream().map(TencentCampaignEntity::getCampaignId).distinct().collect(Collectors.toList());
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        List<TencentAdGroupEntity> adGroupEntityList = tencentAdGroupDAO.queryByCampaignIdList(ea, adAccountId, campaignIdList);
        if (CollectionUtils.isEmpty(adGroupEntityList)) {
            return;
        }
        int adGroupSize = adGroupEntityList.size();
        int avgCost = totalCost / adGroupSize;
        Date now = new Date();
        Random random = new Random();
        List<TencentAdGroupDataEntity> tencentAdGroupDataEntityList = Lists.newArrayList();
        for (int i = 0; i < adGroupSize; i++) {
            TencentAdGroupEntity tencentAdGroupEntity = adGroupEntityList.get(i);
            TencentAdGroupDataEntity tencentAdGroupDataEntity = new TencentAdGroupDataEntity();
            tencentAdGroupDataEntity.setId(UUIDUtil.getUUID());
            tencentAdGroupDataEntity.setEa(ea);
            tencentAdGroupDataEntity.setAdgroupId(tencentAdGroupEntity.getAdgroupId());
            int cost;
            if (i == adGroupSize - 1) {
                cost = totalCost;
            } else {
                cost = random.nextInt(avgCost);
                totalCost -= cost;
            }
            cost = Math.max(0, cost);
            tencentAdGroupDataEntity.setCost((double) (cost * 100));
            // 每日展现数 = 每日消费数 * (1 ~ 10)
            int pv = cost * (random.nextInt(11) + 1);
            tencentAdGroupDataEntity.setPv(pv);
            // 每日点击数 = 昨日展现数 * (0.1 ~ 10%)
            double click = pv * (random.nextInt(10) + 0.1) * 0.01;
            tencentAdGroupDataEntity.setValidClickCount((int) click);
            BigDecimal thousandDisplayPrice = new BigDecimal(cost).divide(new BigDecimal(pv), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(1000));
            tencentAdGroupDataEntity.setThousandDisplayPrice(thousandDisplayPrice.doubleValue());
            BigDecimal cpc = BigDecimal.valueOf(tencentAdGroupDataEntity.getCost()).divide(new BigDecimal(click), 2, RoundingMode.HALF_UP);
            tencentAdGroupDataEntity.setCpc(cpc.doubleValue());
            Date actionDate = DateUtil.plusDay(now, -1);
            tencentAdGroupDataEntity.setReportTime(actionDate);
            tencentAdGroupDataEntity.setAdAccountId(adAccountId);
            tencentAdGroupDataEntityList.add(tencentAdGroupDataEntity);
        }
        tencentAdGroupDataDAO.batchAddTencentAdGroupData(tencentAdGroupDataEntityList);
        buildAdvertisingDetailsObj(ea, adAccountId, tencentAdGroupDataEntityList);

    }

    private void initCampaignAndAdGroup(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        List<String> existCampaignNameList = tencentCampaignDAO.getAllNameList(ea, adAccountEntity.getId());
        if (existCampaignNameList == null) {
            existCampaignNameList = Lists.newArrayList();
        }
        Set<String> existCampaignNameSet = Sets.newHashSet(existCampaignNameList);
        String finalCampaignList = I18nUtil.getSuitedLangText(campaignList, campaignListEN);
        JSONObject json = JSONObject.parseObject(finalCampaignList);
        List<TencentCampaignEntity> campaignEntityList = Lists.newArrayList();
        List<TencentAdGroupEntity> adGroupEntityList = Lists.newArrayList();

        for (Map.Entry<String, Object> entry : json.entrySet()) {
            String campaignName = entry.getKey();
            if (existCampaignNameSet.contains(campaignName)) {
                continue;
            }

            List<String> adGroupNameList = (List<String>) entry.getValue();

            TencentCampaignEntity tencentCampaignEntity = new TencentCampaignEntity();
            tencentCampaignEntity.setId(UUIDUtil.getUUID());
            tencentCampaignEntity.setEa(ea);
            tencentCampaignEntity.setAdAccountId(adAccountEntity.getId());
            tencentCampaignEntity.setCampaignId(Long.parseLong(redisManager.getPrimaryId()));
            tencentCampaignEntity.setCampaignName(campaignName);
            tencentCampaignEntity.setConfiguredStatus(TencentConfiguredStatusEnum.AD_STATUS_NORMAL.getType());
            tencentCampaignEntity.setCampaignType(TencentCampaignTypeEnum.CAMPAIGN_TYPE_SEARCH.getType());
            tencentCampaignEntity.setPromotedObjectType(TencentPromotedObjectTypeEnum.PROMOTED_OBJECT_TYPE_WECHAT_CHANNELS.getType());
            tencentCampaignEntity.setTotalBudget(0D);
            tencentCampaignEntity.setDailyBudget(0D);
            tencentCampaignEntity.setIsDeleted(false);
            campaignEntityList.add(tencentCampaignEntity);
            for (String adGroupName : adGroupNameList) {
                TencentAdGroupEntity tencentAdGroupEntity = new TencentAdGroupEntity();
                tencentAdGroupEntity.setId(UUIDUtil.getUUID());
                tencentAdGroupEntity.setEa(ea);
                tencentAdGroupEntity.setAdAccountId(adAccountEntity.getId());
                tencentAdGroupEntity.setCampaignId(tencentCampaignEntity.getCampaignId());
                tencentAdGroupEntity.setAdgroupId(Long.parseLong(redisManager.getPrimaryId()));
                tencentAdGroupEntity.setAdgroupName(adGroupName);
                tencentAdGroupEntity.setStatus(TencentAdStatusEnum.ADGROUP_STATUS_ACTIVE.getType());
                tencentAdGroupEntity.setBidAmount(200D);
                tencentAdGroupEntity.setDailyBudget(0D);
                tencentAdGroupEntity.setBidMode(TencentBidModEnum.BID_MODE_CPC.getType());
                tencentAdGroupEntity.setSiteSet(Lists.newArrayList(TencentSiteSetEnum.SITE_SET_TENCENT_NEWS.getDesc()));
                tencentAdGroupEntity.setTotalBudget(0D);
                tencentAdGroupEntity.setIsDeleted(false);
                adGroupEntityList.add(tencentAdGroupEntity);
            }
        }
        if (CollectionUtils.isEmpty(campaignEntityList)) {
            return;
        }
        tencentCampaignDAO.batchAddTencentCampaign(campaignEntityList);
        List<AdCampaignEntity> adCampaignEntityList = Lists.newArrayList(campaignEntityList);

        refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, adCampaignEntityList, AdSourceEnum.SOURCE_TENCETN.getSource());
        tencentAdGroupDAO.batchAddTencentAdGroup(adGroupEntityList, AdSourceEnum.SOURCE_TENCETN.getSource());

        List<AdvertiserAdEntity> advertiserAdEntityList = transferAdvertiserAdEntityList(adGroupEntityList);
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, advertiserAdEntityList, AdSourceEnum.SOURCE_TENCETN.getSource());
    }

    /**
     * 更新关键词信息
     *
     * @param adAccountEntity
     */
    public void refreshKeyword(AdAccountEntity adAccountEntity, String source) {
        if (adAccountEntity == null) {
            return;
        }
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.refreshTencentCampaign getTencentAccessToken fail, accountInfo:{}", adAccountEntity);
            return;
        }
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();

        int adGroupTotal = tencentAdGroupDAO.queryAdGroupTotalCount(ea, adAccountId);
        if (adGroupTotal == 0) {
            return;
        }

        int pageSize = 100;
        int totalPageCount = adGroupTotal / pageSize;
        if (adGroupTotal % pageSize != 0) {
            totalPageCount++;
        }
        for (int i = 0; i < totalPageCount; i++) {
            if (!adCommonManager.isSyncAdKeyword(ea)) {
                return;
            }
            int currentPage = i + 1;
            Page page = new Page(currentPage, pageSize);
            List<Long> adGroupIds = tencentAdGroupDAO.pageRefreshAdgroupIds(adAccountEntity.getEa(), adAccountEntity.getId(), page);
            List<TencentKeywordResult.TencentKeyword> adKeywordDataList = adKeywordManager.getTencentKeywordResultDataList(adGroupIds, adAccountEntity.getAccountId(), accessTokenOpt.get());
            syncKeywordByAdGroup(adAccountEntity.getEa(), adAccountEntity.getId(), adKeywordDataList);
        }
    }

    public AdKeywordEntity syncKeywordByKeywordId(AdAccountEntity adAccountEntity, Long keywordId) {
        if (keywordId == null) {
            return null;
        }
        Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
        if (!accessTokenOpt.isPresent()) {
            log.info("TencentAdMarketingManager.refreshTencentCampaign getTencentAccessToken fail, accountInfo:{}", adAccountEntity);
            return null;
        }
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        TencentKeywordResult.TencentKeyword tencentKeyword = adKeywordManager.getTencentKeywordById(keywordId, adAccountEntity.getAccountId(), accessTokenOpt.get());
        if(tencentKeyword == null) {
            log.info("TencentAdMarketingManager.syncKeywordByKeywordId.adKeywordManager.getTencentKeywordById return null, accountInfo:{}, keywordId:{}", adAccountEntity, keywordId);
            return null;
        }
        syncKeywordByAdGroup(adAccountEntity.getEa(), adAccountEntity.getId(), Lists.newArrayList(tencentKeyword));
        List<AdKeywordEntity> keywordEntityList = adKeywordDAO.queryAdKeywordByIds(ea, adAccountId, Lists.newArrayList(keywordId));
        refreshDataManager.batchSyncKeywordToMarketingKeywordObj(adAccountEntity.getEa(), adAccountId, keywordEntityList);
        return CollectionUtils.isEmpty(keywordEntityList) ? null : keywordEntityList.get(0);
    }

    private void syncKeywordByAdGroup(String ea, String adAccountId, List<TencentKeywordResult.TencentKeyword> adKeywordDataList) {
        if (CollectionUtils.isEmpty(adKeywordDataList)) {
            return;
        }

        List<TencentKeywordResult.TencentKeyword> addKeywordList = Lists.newArrayList();
        List<AdKeywordEntity> updateKeywordList = Lists.newArrayList();
        PageUtil pageUtil = new PageUtil(adKeywordDataList, 500);

        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<TencentKeywordResult.TencentKeyword> currentPageData = pageUtil.getPagedList(i);
            List<Long> keywordIds = currentPageData.stream().map(TencentKeywordResult.TencentKeyword::getKeywordId).collect(Collectors.toList());
            List<AdKeywordEntity> keywordEntityList = adKeywordDAO.queryAdKeywordByIds(ea, adAccountId, keywordIds);
            if (CollectionUtils.isEmpty(keywordEntityList)) {
                addKeywordList.addAll(currentPageData);
            } else {
                Map<Long, AdKeywordEntity> existKeywordMap = keywordEntityList.stream().collect(Collectors.toMap(AdKeywordEntity::getKeywordId, Function.identity(), (k1, k2) -> k1));
                currentPageData.forEach(keywordData -> {
                    if (existKeywordMap.get(keywordData.getKeywordId()) == null) {
                        addKeywordList.add(keywordData);
                    } else {
                        AdKeywordEntity entity = existKeywordMap.get(keywordData.getKeywordId());
                        entity.setKeyword(keywordData.getWord());
                        TencentKeywordResult.TencentKeyWordStatusEnum statusEnum = TencentKeywordResult.TencentKeyWordStatusEnum.getByStatus(keywordData.getStatus());
                        if(statusEnum == null) {
                            statusEnum = TencentKeywordResult.TencentKeyWordStatusEnum.OTHER;
                        }
                        entity.setStatus(statusEnum.getValue());
                        updateKeywordList.add(entity);
                    }
                });
            }
        }

        batchAddKeyword(ea, adAccountId, addKeywordList);
        batchUpdateKeyword(ea, adAccountId, updateKeywordList);
    }

    private void batchAddKeyword(String ea, String adAccountId, List<TencentKeywordResult.TencentKeyword> addKeywordList) {
        if (CollectionUtils.isEmpty(addKeywordList)) {
            return;
        }
        PageUtil pageUtil = new PageUtil(addKeywordList, 500);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<TencentKeywordResult.TencentKeyword> addData = pageUtil.getPagedList(i);
            List<AdKeywordEntity> addKeywordEntityList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(addData)) {
                addData.forEach(keywordData -> {
                    AdKeywordEntity entity = new AdKeywordEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(ea);
                    entity.setAdAccountId(adAccountId);
                    entity.setCampaignId(keywordData.getCampaignId());
                    entity.setAdgroupId(keywordData.getAdgroupId());
                    entity.setAdId(keywordData.getAdgroupId());
                    entity.setKeywordId(keywordData.getKeywordId());
                    entity.setKeyword(keywordData.getWord());
                    entity.setSource(AdSourceEnum.SOURCE_TENCETN.getSource());
                    TencentKeywordResult.TencentKeyWordStatusEnum statusEnum = TencentKeywordResult.TencentKeyWordStatusEnum.getByStatus(keywordData.getStatus());
                    if(statusEnum == null) {
                        statusEnum = TencentKeywordResult.TencentKeyWordStatusEnum.OTHER;
                    }
                    entity.setStatus(statusEnum.getValue());
                    addKeywordEntityList.add(entity);
                });
            }
            if (CollectionUtils.isNotEmpty(addKeywordEntityList)) {
                List<AdKeywordEntity> insertList = addKeywordEntityList.stream()
                        .filter(e -> (e.getAdgroupId() != null ))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(insertList)) {
                    adKeywordDAO.batchInsert(insertList);
                }
            }
        }
    }

    private void batchUpdateKeyword(String ea, String adAccountId, List<AdKeywordEntity> keywordList) {
        if (CollectionUtils.isEmpty(keywordList)) {
            return;
        }
        PageUtil pageUtil = new PageUtil(keywordList, 100);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<AdKeywordEntity> currentList = pageUtil.getPagedList(i);
            if (CollectionUtils.isNotEmpty(currentList)) {
                adKeywordDAO.batchUpdate(currentList, ea, adAccountId);
            }
        }
    }
}
