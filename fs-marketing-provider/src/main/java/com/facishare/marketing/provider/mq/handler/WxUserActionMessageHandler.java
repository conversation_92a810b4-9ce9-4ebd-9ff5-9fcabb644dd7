package com.facishare.marketing.provider.mq.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.facishare.common.rocketmq.AutoConfRocketMQProcessor;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.RecordWxUserMkActionArg;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.arg.usermarketingaccount.BatchAddOrDeleteTagNamesToCrmDataArg;
import com.facishare.marketing.api.result.WxAutoReplyRuleResult;
import com.facishare.marketing.api.service.WxUserMkActionService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.ReplaceUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.result.MaterialWxPresentMsg;
import com.facishare.marketing.outapi.service.MaterialService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.cta.CtaDAO;
import com.facishare.marketing.provider.dao.cta.CtaQrCodeRelationDAO;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowAdditionalConfigDao;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowTaskDao;
import com.facishare.marketing.provider.dao.officialWebsite.WxOfficialAccountsQrCodeDAO;
import com.facishare.marketing.provider.dao.officialWebsite.WxServiceQrCodeOfficialWebsiteRelationDAO;
import com.facishare.marketing.provider.dto.ToFinishTaskAdditionalInfo;
import com.facishare.marketing.provider.entity.QrCodeIdentifySpreadSourceRelationEntity;
import com.facishare.marketing.provider.entity.WxOfficialAccountsQrCodeChannelEntity;
import com.facishare.marketing.provider.entity.WxServiceQrCodePartnerRelationEntity;
import com.facishare.marketing.provider.entity.WxServiceQrCodeUserMarketingRelationEntity;
import com.facishare.marketing.provider.entity.cta.CtaEntity;
import com.facishare.marketing.provider.entity.cta.CtaQrCodeRelationEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowAdditionalConfigEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowTaskEntity;
import com.facishare.marketing.provider.entity.officialWebsite.WxOfficialAccountsQrCodeEntity;
import com.facishare.marketing.provider.entity.officialWebsite.WxServiceQrCodeOfficialWebsiteRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerData.BehaviorSendEventData;
import com.facishare.marketing.provider.innerData.ResponseMsgList;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.marketingactivity.WeChatServiceManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.manager.wxOfficialAccountsProxy.WxOfficialAccountsProxyManager;
import com.facishare.marketing.provider.manager.wxOfficialAccountsProxy.WxOfficialAccountsProxyManagerFactory;
import com.facishare.marketing.provider.mq.sender.BehaviorSendEventSender;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.util.NumberUtil;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.wechat.dubborestouterapi.arg.QueryStoreQrCodeArg;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import com.facishare.wechat.dubborestouterapi.service.proxy.WechatMessageRestService;
import com.facishare.wechat.dubborestouterapi.service.union.WechatQrCodeRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.service.QrCodeService;
import com.facishare.wechat.proxy.service.WxCustomerServiceMsgService;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.otherrestapi.integral.constant.ActionApiNameConstant;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WxUserActionMessageHandler extends AbstractMessageHandler<WxUserActionMessageHandler.WxUserActionEvent> {

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingFlowAdditionalConfigDao marketingFlowAdditionalConfigDao;
    @Autowired
    private MarketingFlowInstanceManager marketingFlowInstanceManager;
    @Autowired
    private WxOfficialAccountsProxyManagerFactory wxOfficialAccountsProxyManagerFactory;
    @Autowired
    private MarketingFlowTaskDao marketingFlowTaskDao;
    @Autowired
    private WxUserMkActionService wxUserMkActionService;
    @Autowired
    private WechatQrCodeRestService wechatQrCodeRestService;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private BehaviorSendEventSender behaviorSendEventSender;
    @Autowired
    private WeChatServiceManager weChatServiceManager;
    @Autowired
    private WxCustomerServiceMsgService wxCustomerServiceMsgService;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private TriggerInstanceManager triggerInstanceManager;
    @Autowired
    private WechatMessageRestService wechatMessageRestService;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;
    @Autowired
    private WxServiceQrCodeOfficialWebsiteRelationDAO wxServiceQrCodeOfficialWebsiteRelationDAO;
    @Autowired
    private WxOfficialAccountsQrCodeDAO wxOfficialAccountsQrCodeDAO;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;

    @Autowired
    MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    private AutoConfRocketMQProcessor processor;

    @Autowired
    private QrCodeService qrCodeService;

    @Autowired
    private DelayQueueSender delayQueueSender;
    @Autowired
    private WxServiceQrCodeUserMarketingRelationDAO wxServiceQrCodeUserMarketingRelationDAO;

    @Autowired
    private WxOfficialAccountsQrCodeChannelDAO wxOfficialAccountsQrCodeChannelDAO;
    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;
    @Autowired
    private WxServiceQrCodePartnerRelationDAO wxServiceQrCodePartnerRelationDAO;
    @Autowired
    private CtaQrCodeRelationDAO ctaQrCodeRelationDAO;
    @Autowired
    private CtaDAO ctaDAO;

    @Override
    protected WxUserActionMessageHandler.WxUserActionEvent getMsgObj(MessageExt msg) {
        return JSONObject.parseObject(msg.getBody(), WxUserActionMessageHandler.WxUserActionEvent.class, Feature.IgnoreNotMatch);
    }

    @Override
    protected String getEa(WxUserActionMessageHandler.WxUserActionEvent event) {
        return event.getEa();
    }

    @Override
    public void directHandle(WxUserActionMessageHandler.WxUserActionEvent event) {
        String sceneId = null;
        try {
            log.info("WxUserActionConsumer handleMessage mq-receive-wx-user-action:{}", event);
            //过滤掉非营销通的公众号消息
            if (enterpriseInfoManager.isEnterpriseStopAndVersionExpiredFilterLog(event.getEa())) {
                return;
            }
            //微信公众号二维码id
            sceneId = event.getSceneId();
            if (StringUtils.isNotEmpty(sceneId)) {
                sceneId = sceneId.replace(WxUserActionMessageHandler.WxUserActionEvent.scenePrefix, "");
                if (sceneId.length() < 32 && Integer.parseInt(sceneId) > 100000000) {
                    //大于1亿为服务通的消息，直接跳过
                    return;
                }
            }

            doRecordToUnionMarketingUserMessage(event);
            log.info("mq-receive-wx-user-action:{}", event);
            ThreadPoolUtils.execute(() -> doWxAutoReplyRules(event), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            if (event.isValidFollowEvent()) {
                if(sceneId.startsWith(WxUserActionMessageHandler.WxUserActionEvent.ctaPrefix)) {
                    processCtaWxQrCode(event, sceneId);
                }
                //观众公众号
                WxServiceQrCodeOfficialWebsiteRelationEntity relationEntity = wxServiceQrCodeOfficialWebsiteRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
                if (relationEntity != null) {
                    ThreadPoolUtils.execute(() -> this.cacheWxOpenId(relationEntity.getBrowserUserId(), event.getWxOpenId()), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                    //关注公众号时，只会推送关注消息，不会推送扫描消息，所以扫码次数也要加1
                    wxOfficialAccountsQrCodeDAO.increaseSubscribCount(event.getEa(), relationEntity.getMainSceneId());
                    wxOfficialAccountsQrCodeDAO.increaseScanCount(event.getEa(), relationEntity.getMainSceneId());
                    doWxTempQrcodeAutoReplyRules(relationEntity.getEa(), relationEntity.getMainSceneId(), event.getWxOpenId());
                }
                String userMarketingAccountId = doGetUserMarketingAccountId(event);
                //绑定微信营销用户身份和访客身份
                if (StringUtils.isNotBlank(sceneId)) {
                    bindBrowseUserAndWxUser(event, sceneId);
                }
                //绑定微信营销用户身份和访客身份
                if (StringUtils.isNotBlank(sceneId)) {
                    //微页面内嵌公总哈二维码
                    WxServiceQrCodeUserMarketingRelationEntity userMarketingRelationEntity = wxServiceQrCodeUserMarketingRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
                    if (userMarketingRelationEntity != null) {
                        wxOfficialAccountsQrCodeDAO.increaseScanCount(event.getEa(), userMarketingRelationEntity.getMainSceneId());
                        //自动回复规则
                        doHexagonWxTempQrcodeAutoReplyRules(userMarketingRelationEntity.getEa(), userMarketingRelationEntity.getSceneId(), event.getWxOpenId(), event.getAppId());
                        //处理标签
                        doHexagonWxTempQrcodeTag(event, userMarketingRelationEntity);
                        //绑定营销用户身份和公众号身份
                        bindUserMarketingAndWxUser(event, sceneId);
                    }
                }
                if (StringUtils.isNotBlank(sceneId)) {
                    //伙伴推广海报公众号二维码
                    WxServiceQrCodePartnerRelationEntity partnerRelationEntity = wxServiceQrCodePartnerRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
                    if (partnerRelationEntity != null) {
                        wxOfficialAccountsQrCodeDAO.increaseScanCount(event.getEa(), partnerRelationEntity.getMainSceneId());
                        //自动回复规则
                        doPartnerWxTempQrcodeAutoReplyRules(partnerRelationEntity.getEa(), partnerRelationEntity.getSceneId(), event.getWxOpenId(), event.getAppId());
                        //处理标签
                        doPartnerWxTempQrcodeTag(event, partnerRelationEntity);
                    }
                }
                String wxAppId = event.getWxAppId();
                String ea = event.getEa();
                Integer ei = eieaConverter.enterpriseAccountToId(ea);
                ThreadPoolUtils.execute(() -> {
                    this.doProcessIntegral(ea, ei, userMarketingAccountId, wxAppId, ActionApiNameConstant.SUBSCRIBE, CategoryApiNameConstant.OFFICIAL_ACCOUNT);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
                List<MarketingFlowAdditionalConfigEntity> marketingFlowAdditionalConfigs = marketingFlowAdditionalConfigDao.listFollowWxServiceAccountFlows(ei, event.getAppId());
                if (!marketingFlowAdditionalConfigs.isEmpty()  && StringUtils.isNotBlank(userMarketingAccountId)) {
                    Set<String> marketingFlowIds = marketingFlowAdditionalConfigs.stream().map(MarketingFlowAdditionalConfigEntity::getBpmFlowId).collect(Collectors.toSet());
                    marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), marketingFlowIds);
                }
                // 创建营销推广来源对象并且关联微信用户对象
                handleMarketingPromotionObj(ea, sceneId, event);
            }
            if (event.isValidUnFollowEvent()) {
                //取消关注
                String userMarketingAccountId = doGetUserMarketingAccountId(event);
                String wxAppId = event.getWxAppId();
                String ea = event.getEa();
                Integer ei = eieaConverter.enterpriseAccountToId(ea);
                ThreadPoolUtils.execute(() -> {
                    this.doProcessIntegral(ea, ei, userMarketingAccountId, wxAppId, ActionApiNameConstant.UN_SUBSCRIBE, CategoryApiNameConstant.OFFICIAL_ACCOUNT);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
            if (event.isValidSendMessageEvent()) {
                String userMarketingAccountId = doGetUserMarketingAccountId(event);
                //绑定微信营销用户身份和访客身份
                if (StringUtils.isNotBlank(sceneId)) {
                    bindBrowseUserAndWxUser(event, sceneId);
                }

                Integer ei = eieaConverter.enterpriseAccountToId(event.getEa());
                List<MarketingFlowTaskEntity> marketingFlowTaskEntities = marketingFlowTaskDao.listMatchedResponseWxMessageTasks(ei, userMarketingAccountId, event.getAppId());
                Set<String> taskIds = marketingFlowTaskEntities.stream().map(MarketingFlowTaskEntity::getTaskId).collect(Collectors.toSet());
                if (!taskIds.isEmpty()) {
                    ToFinishTaskAdditionalInfo additionalInfo = new ToFinishTaskAdditionalInfo();
                    additionalInfo.setWxMessageContent(event.getTitle());
                    marketingFlowInstanceManager.finishTasks(ei, taskIds, additionalInfo);
                }
            }

            if (event.isValidScanQrCodeEvent() && StringUtils.isNotBlank(sceneId)) {
                //扫码消息
                WxServiceQrCodeOfficialWebsiteRelationEntity relationEntity = wxServiceQrCodeOfficialWebsiteRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
                if (relationEntity != null) {
                    ThreadPoolUtils.execute(() -> this.cacheWxOpenId(relationEntity.getBrowserUserId(), event.getWxOpenId()), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                    wxOfficialAccountsQrCodeDAO.increaseScanCount(event.getEa(), relationEntity.getMainSceneId());
                    doWxTempQrcodeAutoReplyRules(relationEntity.getEa(), relationEntity.getMainSceneId(), event.getWxOpenId());
                }
                //微页面内嵌公总哈二维码
                WxServiceQrCodeUserMarketingRelationEntity userMarketingRelationEntity = wxServiceQrCodeUserMarketingRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
                if (userMarketingRelationEntity != null) {
                    wxOfficialAccountsQrCodeDAO.increaseScanCount(event.getEa(), userMarketingRelationEntity.getMainSceneId());
                    //自动回复规则
                    doHexagonWxTempQrcodeAutoReplyRules(userMarketingRelationEntity.getEa(), userMarketingRelationEntity.getSceneId(), event.getWxOpenId(), event.getAppId());
                    //处理标签
                    doHexagonWxTempQrcodeTag(event, userMarketingRelationEntity);
                    //绑定营销用户身份和公众号身份
                    bindUserMarketingAndWxUser(event, sceneId);
                }
                //伙伴推广海报公众号二维码
                WxServiceQrCodePartnerRelationEntity partnerRelationEntity = wxServiceQrCodePartnerRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
                if (partnerRelationEntity != null) {
                    wxOfficialAccountsQrCodeDAO.increaseScanCount(event.getEa(), partnerRelationEntity.getMainSceneId());
                    //自动回复规则
                    doPartnerWxTempQrcodeAutoReplyRules(partnerRelationEntity.getEa(), partnerRelationEntity.getSceneId(), event.getWxOpenId(), event.getAppId());
                    //处理标签
                    doPartnerWxTempQrcodeTag(event, partnerRelationEntity);
                }

                if(sceneId.startsWith(WxUserActionMessageHandler.WxUserActionEvent.ctaPrefix)) {
                    processCtaScanWxQrCode(event, sceneId);
                }
            }
        } catch (Exception e) {
            log.warn("Error at consume e:", e);
        }
        try {
            if (event.isValidFollowEvent() || event.isValidUnFollowEvent() || event.isValidScanQrCodeEvent()) {
                String marketingUserId = doGetUserMarketingAccountId(event);
                if (!Strings.isNullOrEmpty(marketingUserId)) {
                    Map<String, Object> paramMap = Maps.newHashMap();
                    paramMap.put("objectId", event.getWxAppId());
                    paramMap.put("objectType", ObjectTypeEnum.WX_SERVICE_ACCOUNT.getType());
                    String triggerActionType = null;
                    if (event.isValidFollowEvent()) {
                        paramMap.put("actionType", MarketingUserActionType.FOLLOW_WX_SERVICE.getActionType());
                        triggerActionType = TriggerActionTypeEnum.WX_SERVICE_SUBSCRIBE.getTriggerActionType();
                    }
                    if (event.isValidUnFollowEvent()) {
                        paramMap.put("actionType", MarketingUserActionType.UN_FOLLOW_WX_SERVICE.getActionType());
                        triggerActionType = TriggerActionTypeEnum.WX_SERVICE_UNSUBSCRIBE.getTriggerActionType();
                    }
                    if (event.isValidScanQrCodeEvent()) {
                        paramMap.put("actionType", MarketingUserActionType.SCAN_WX_SERVICE_QR_CODE.getActionType());
                        triggerActionType = TriggerActionTypeEnum.SCAN_WX_SERVICE_QR_CODE.getTriggerActionType();
                    }
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneMsgAndTriggerAction(event.getEa(), marketingUserId, TriggerSceneEnum.WX_SERVICE_ACCOUNT.getTriggerScene(), event.getWxAppId(), triggerActionType, paramMap);
                }
            }
            if (event.isValidClickMenuEvent()) {
                String marketingUserId = doGetUserMarketingAccountId(event);
                if (!Strings.isNullOrEmpty(marketingUserId)) {
                    Map<String, Object> paramMap = Maps.newHashMap();
                    paramMap.put("objectId", event.getTitle());
                    paramMap.put("objectType", ObjectTypeEnum.WX_SERVICE_MENU.getType());
                    paramMap.put("actionType", MarketingUserActionType.CLICK_WX_SERVICE_MENU.getActionType());
                    paramMap.put("wxAppId", event.getWxAppId());
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.WX_SERVICE_ACCOUNT.getTriggerScene(), event.getWxAppId(), TriggerActionTypeEnum.WX_SERVICE_CLICK_MENU.getTriggerActionType(), paramMap);
                }
            }
            if (event.isValidClickLinkActionEvent()) {
                String marketingUserId = doGetUserMarketingAccountId(event);
                if (!Strings.isNullOrEmpty(marketingUserId)) {
                    Map<String, Object> paramMap = Maps.newHashMap();
                    paramMap.put("objectId", event.getWxAppId());
                    paramMap.put("objectType", ObjectTypeEnum.WX_SERVICE_ACCOUNT.getType());
                    paramMap.put("actionType", MarketingUserActionType.CLICK_WX_SERVICE_LINK.getActionType());
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.WX_SERVICE_ACCOUNT.getTriggerScene(), event.getWxAppId(), TriggerActionTypeEnum.WX_SERVICE_CLICK_MENU.getTriggerActionType(), paramMap);
                }
            }
        } catch (Exception e) {
            log.warn("Exception", e);
        }

        try {
            WxUserActionMessageHandler.WxOfficialAccountsProxyMessage wxOfficialAccountsProxyMessage = JSONObject.parseObject(JSONObject.toJSONString(event), WxUserActionMessageHandler.WxOfficialAccountsProxyMessage.class, Feature.IgnoreNotMatch);
            WxOfficialAccountsProxyManager wxOfficialAccountsProxyManager = wxOfficialAccountsProxyManagerFactory.getByType(wxOfficialAccountsProxyMessage.getSceneType());
            if (wxOfficialAccountsProxyManager != null) {
                wxOfficialAccountsProxyManager.handleMessage(wxOfficialAccountsProxyMessage);
            }
        } catch (Exception e) {
            log.warn("Error at consume e:", e);
        }

    }

    private void processCtaWxQrCode(WxUserActionEvent event, String sceneId) {
        CtaQrCodeRelationEntity ctaQrCodeRelationEntity = ctaQrCodeRelationDAO.getByState(event.getEa(), sceneId);
        if(ctaQrCodeRelationEntity == null) {
            return;
        }
        AssociationArg wxAssociationArg = new AssociationArg();
        wxAssociationArg.setEa(event.getEa());
        wxAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxAssociationArg.setWxAppId(event.getWxAppId());
        wxAssociationArg.setAssociationId(event.getWxOpenId());
        wxAssociationArg.setTriggerAction("WxUserActionMessageHandler");
        wxAssociationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        String wxUserMarketingAccountId = userMarketingAccountRelationManager.getByEaAndKeyProperties(wxAssociationArg).getUserMarketingAccountId();
        log.info("processCtaWxQrCode wxUserMarketingAccountId by wxOpenId result: {} ", wxUserMarketingAccountId);
        ctaQrCodeRelationEntity.setWxOpenId(event.getWxOpenId());
        ctaQrCodeRelationEntity.setWxAppId(event.getWxAppId());
        ctaQrCodeRelationEntity.setAppId(event.getAppId());
        ctaQrCodeRelationEntity.setStatus(1);
        if(StringUtils.isNotBlank(ctaQrCodeRelationEntity.getBrowserUserId())) {
            /**
             * 访客身份已经关联了营销用户，且该营销用户关联了公众号身份，就不再做绑定，防止多个用户扫一个码，身份关联到一起了
             * **/
            AssociationArg browserUserAssociateArg = new AssociationArg();
            browserUserAssociateArg.setAssociationId(ctaQrCodeRelationEntity.getBrowserUserId());
            browserUserAssociateArg.setEa(event.getEa());
            browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
            String browseUserMarketingAccountId = userMarketingAccountRelationManager.getByEaAndKeyProperties(browserUserAssociateArg).getUserMarketingAccountId();
            if (browseUserMarketingAccountId != null) {
                ctaQrCodeRelationEntity.setUserMarketingId(browseUserMarketingAccountId);
                ctaQrCodeRelationDAO.updateCtaQrCodeRelation(ctaQrCodeRelationEntity);
                List<UserMarketingWxServiceAccountRelationEntity> wxMarketingUserListAssociateBrowseUser = userMarketingWxServiceAccountRelationDao.getByUserMarketingId(browseUserMarketingAccountId,event.getEa());
                if (CollectionUtils.isNotEmpty(wxMarketingUserListAssociateBrowseUser)) {
                    log.info("WxUserActionConsumer.processCtaWxQrCode exist wxMarketingUser associate by browseuser  event:{} browseId:{}  browseUserMarketingId:{}",
                            event, ctaQrCodeRelationEntity.getBrowserUserId(), browseUserMarketingAccountId);
                    return;
                }

                if (StringUtils.isBlank(wxUserMarketingAccountId)) {
                    userMarketingAccountAssociationManager.doAssociateAccount(wxAssociationArg, browseUserMarketingAccountId);
                    return;
                }
                userMarketingAccountRelationManager.bindWxUserAndBrowserUser(event.getEa(), event.getWxAppId(), event.getWxOpenId(), ctaQrCodeRelationEntity.getBrowserUserId(), null, "WxUserActionMessageHandler");
            } else {
                ctaQrCodeRelationEntity.setUserMarketingId(wxUserMarketingAccountId);
                ctaQrCodeRelationDAO.updateCtaQrCodeRelation(ctaQrCodeRelationEntity);
            }
        } else if (StringUtils.isBlank(wxUserMarketingAccountId)) {
            wxUserMarketingAccountId = userMarketingAccountAssociationManager.associate(wxAssociationArg).getUserMarketingAccountId();
            ctaQrCodeRelationEntity.setUserMarketingId(wxUserMarketingAccountId);
            ctaQrCodeRelationDAO.updateCtaQrCodeRelation(ctaQrCodeRelationEntity);
        } else {
            ctaQrCodeRelationEntity.setUserMarketingId(wxUserMarketingAccountId);
            ctaQrCodeRelationDAO.updateCtaQrCodeRelation(ctaQrCodeRelationEntity);
        }
    }

    private void processCtaScanWxQrCode(WxUserActionEvent event, String sceneId) {
        CtaQrCodeRelationEntity ctaQrCodeRelationEntity = ctaQrCodeRelationDAO.getByState(event.getEa(), sceneId);
        if(ctaQrCodeRelationEntity == null) {
            return;
        }
        AssociationArg wxAssociationArg = new AssociationArg();
        wxAssociationArg.setEa(event.getEa());
        wxAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxAssociationArg.setWxAppId(event.getWxAppId());
        wxAssociationArg.setAssociationId(event.getWxOpenId());
        wxAssociationArg.setTriggerAction("WxUserActionMessageHandler");
        wxAssociationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        String wxUserMarketingAccountId = userMarketingAccountRelationManager.getByEaAndKeyProperties(wxAssociationArg).getUserMarketingAccountId();
        log.info("processCtaScanWxQrCode wxUserMarketingAccountId by wxOpenId result: {} ", wxUserMarketingAccountId);
        ctaQrCodeRelationEntity.setWxOpenId(event.getWxOpenId());
        ctaQrCodeRelationEntity.setWxAppId(event.getWxAppId());
        ctaQrCodeRelationEntity.setAppId(event.getAppId());
        ctaQrCodeRelationEntity.setStatus(1);
        if (StringUtils.isNotBlank(wxUserMarketingAccountId)) {
            ctaQrCodeRelationEntity.setUserMarketingId(wxUserMarketingAccountId);
            ctaQrCodeRelationDAO.updateCtaQrCodeRelation(ctaQrCodeRelationEntity);
        }
    }

    private void doPartnerWxTempQrcodeTag(WxUserActionEvent event, WxServiceQrCodePartnerRelationEntity relationEntity) {
        ThreadPoolUtils.execute(() -> {
            Optional<ObjectData> optionalObjectData = crmV2Manager.getWechatFanByOpenId(event.getEa(), event.getWxAppId(), event.getWxOpenId());
            if (!optionalObjectData.isPresent() || optionalObjectData.get() == null) {
                log.warn("WxUserActionConsumer.doPartnerWxTempQrcodeTag getWechatFanByOpenId result is null event:{}", event);
            } else {
                QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
                queryStoreQrCodeArg.setAppId(event.getAppId());
                queryStoreQrCodeArg.setSceneId(Long.valueOf(relationEntity.getMainSceneId()));
                queryStoreQrCodeArg.setEnterpriseAccount(event.getEa());
                ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
                log.info("WxUserActionConsumer.doPartnerWxTempQrcodeTag qrCodeService.queryQrCodeByIds  pagerModelResult:{} ", pagerModelResult);
                if (pagerModelResult.isSuccess() && pagerModelResult.getResult() != null) {
                    BatchAddOrDeleteTagNamesToCrmDataArg batchAddOrDeleteTagNamesToCrmDataArg = new BatchAddOrDeleteTagNamesToCrmDataArg();
                    batchAddOrDeleteTagNamesToCrmDataArg.setCrmObjectDescribeApiName(CrmWechatFanFieldEnum.API_NAME.getFieldName());
                    Object wechatId = optionalObjectData.get().get(CrmWechatFanFieldEnum.ID.getFieldName());
                    batchAddOrDeleteTagNamesToCrmDataArg.setCrmObjectIds(ImmutableList.of(wechatId.toString()));
                    batchAddOrDeleteTagNamesToCrmDataArg.setTagNames(BeanUtil.copy(pagerModelResult.getResult().getData().get(0).getTagNames(), TagName.class));
                    Result<Void> batchAddTagNamesToCrmDataResult = userMarketingAccountService.batchAddTagNamesToCrmData(event.getEa(), -10000, batchAddOrDeleteTagNamesToCrmDataArg);
                    if (!batchAddTagNamesToCrmDataResult.isSuccess()) {
                        log.warn("WxUserActionConsumer.doPartnerWxTempQrcodeTag batchAddTagNamesToCrmData fail errorCode:{} errorMsg:{} arg:{}", batchAddTagNamesToCrmDataResult.getErrCode(), batchAddTagNamesToCrmDataResult.getErrMsg(), batchAddOrDeleteTagNamesToCrmDataArg);
                    }
                }
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    private void doHexagonWxTempQrcodeTag(WxUserActionEvent event, WxServiceQrCodeUserMarketingRelationEntity relationEntity) {
        ThreadPoolUtils.execute(() -> {
            Optional<ObjectData> optionalObjectData = crmV2Manager.getWechatFanByOpenId(event.getEa(), event.getWxAppId(), event.getWxOpenId());
            if (!optionalObjectData.isPresent() || optionalObjectData.get() == null) {
                log.warn("WxUserActionConsumer.bindUserMarketingAndWxUser getWechatFanByOpenId result is null event:{}", event);
            } else {
                QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
                queryStoreQrCodeArg.setAppId(event.getAppId());
                queryStoreQrCodeArg.setSceneId(Long.valueOf(relationEntity.getMainSceneId()));
                queryStoreQrCodeArg.setEnterpriseAccount(event.getEa());
                ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
                log.info("WxUserActionConsumer.bindUserMarketingAndWxUser qrCodeService.queryQrCodeByIds  pagerModelResult:{} ", pagerModelResult);
                if (pagerModelResult.isSuccess() && pagerModelResult.getResult() != null) {
                    BatchAddOrDeleteTagNamesToCrmDataArg batchAddOrDeleteTagNamesToCrmDataArg = new BatchAddOrDeleteTagNamesToCrmDataArg();
                    batchAddOrDeleteTagNamesToCrmDataArg.setCrmObjectDescribeApiName(CrmWechatFanFieldEnum.API_NAME.getFieldName());
                    Object wechatId = optionalObjectData.get().get(CrmWechatFanFieldEnum.ID.getFieldName());
                    batchAddOrDeleteTagNamesToCrmDataArg.setCrmObjectIds(ImmutableList.of(wechatId.toString()));
                    batchAddOrDeleteTagNamesToCrmDataArg.setTagNames(BeanUtil.copy(pagerModelResult.getResult().getData().get(0).getTagNames(), TagName.class));
                    Result<Void> batchAddTagNamesToCrmDataResult = userMarketingAccountService.batchAddTagNamesToCrmData(event.getEa(), -10000, batchAddOrDeleteTagNamesToCrmDataArg);
                    if (!batchAddTagNamesToCrmDataResult.isSuccess()) {
                        log.warn("WxUserActionConsumer.bindUserMarketingAndWxUser batchAddTagNamesToCrmData fail errorCode:{} errorMsg:{} arg:{}", batchAddTagNamesToCrmDataResult.getErrCode(), batchAddTagNamesToCrmDataResult.getErrMsg(), batchAddOrDeleteTagNamesToCrmDataArg);
                    }
                }
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    public void handleMarketingPromotionObj(String ea, String qrCodeId, WxUserActionEvent event) {
        try {

            ObjectData wechatFanObj = crmV2Manager.getWechatFanObjByOpenId(ea, event.getWxAppId(), event.getWxOpenId());
            if (wechatFanObj == null) {
                return;
            }
            String marketingPromotionSourceId = null;
            QrCodeIdentifySpreadSourceRelationEntity qrCodeIdentifySpreadSourceRelationEntity = marketingPromotionSourceArgObjectRelationManager.getByQrCodeIdentifyId(ea, qrCodeId);
            if (qrCodeIdentifySpreadSourceRelationEntity != null && StringUtils.isNotBlank(qrCodeIdentifySpreadSourceRelationEntity.getPromotionSourceParam())) {
                // 官网集客二维码过来的
                String param = qrCodeIdentifySpreadSourceRelationEntity.getPromotionSourceParam();
                MarketingPromotionSourceArg marketingPromotionSourceArg = JSONObject.parseObject(param, MarketingPromotionSourceArg.class);
                marketingPromotionSourceArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.WECHAT.getLeadModule());
                marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.OFFICIAL_ACCOUNT.getDataSource());
                marketingPromotionSourceArg.setHandleUtm(true);
                marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObj(marketingPromotionSourceArg);
            } else {
                if (StringUtils.isBlank(qrCodeId) || !NumberUtil.isNumeric(qrCodeId)) {
                    return;
                }
                QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
                queryStoreQrCodeArg.setAppId(event.getAppId());
                queryStoreQrCodeArg.setSceneId(Long.valueOf(qrCodeId));
                queryStoreQrCodeArg.setEnterpriseAccount(ea);
                ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
                if (pagerModelResult.getResult() == null || !pagerModelResult.isSuccess() || CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
                    log.info("WxOfficialAccountsServiceImpl handleMarketingPromotionObj wx code can't be found ea:{} qrCoedId:{}", ea, qrCodeId);
                    return;
                }
                QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
                MarketingPromotionSourceArg marketingPromotionSourceArg = new MarketingPromotionSourceArg();
                marketingPromotionSourceArg.setEa(ea);
                marketingPromotionSourceArg.setMarketingEventId(qrCodeResult.getMarketingEventId());
                marketingPromotionSourceArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.WECHAT.getLeadModule());
                marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.OFFICIAL_ACCOUNT.getDataSource());
                // 先用二维码id去找渠道，找不到再用二维码名称去找渠道，因为历史数据没有二维码id
                WxOfficialAccountsQrCodeChannelEntity wxOfficialAccountsQrCodeChannel = wxOfficialAccountsQrCodeChannelDAO.getByQrCodeId(ea, event.getWxAppId(), qrCodeId);
                if (wxOfficialAccountsQrCodeChannel == null) {
                    wxOfficialAccountsQrCodeChannel = wxOfficialAccountsQrCodeChannelDAO.getWxOfficialAccountsQrCodeChannel(ea, event.getWxAppId(), qrCodeResult.getQrCodeName());
                }
                if (wxOfficialAccountsQrCodeChannel != null) {
                    marketingPromotionSourceArg.setChannelValue(wxOfficialAccountsQrCodeChannel.getChannel());
                }
                marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObj(marketingPromotionSourceArg);
            }
            if (marketingPromotionSourceId == null) {
                return;
            }
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put(CrmWechatFanFieldEnum.ID.getFieldName(), wechatFanObj.getId());
            dataMap.put(CrmWechatFanFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getFieldName(), marketingPromotionSourceId);
            crmV2Manager.editWechatFanObj(ea, dataMap);
        } catch (Exception e) {
            log.error("WxUserActionConsumer->handleMarketingPromotionObj error,even:[{}]", event, e);
        }
    }

    private void cacheWxOpenId(String browserUserId, String wxOpenId) {
        JSONObject memberInfoObj = redisManager.getMemberInfo(browserUserId);
        if (memberInfoObj != null) {
            memberInfoObj.put("wxOpenId", wxOpenId);
            redisManager.setMemberInfo(browserUserId, memberInfoObj);
        }
    }

    //绑定微信营销用户身份和访客身份
    private void bindBrowseUserAndWxUser(WxUserActionEvent event, String sceneId) {
        WxServiceQrCodeOfficialWebsiteRelationEntity relationEntity = wxServiceQrCodeOfficialWebsiteRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
        if (relationEntity == null || StringUtils.isBlank(relationEntity.getBrowserUserId())) {
            return;
        }

        /**
         * 访客身份已经关联了营销用户，且该营销用户关联了公众号身份，就不再做绑定，防止多个用户扫一个码，身份关联到一起了
         * **/
        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(relationEntity.getBrowserUserId());
        browserUserAssociateArg.setEa(relationEntity.getEa());
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        String browseUserMarketingAccountId = userMarketingAccountRelationManager.getByEaAndKeyProperties(browserUserAssociateArg).getUserMarketingAccountId();
        if (browseUserMarketingAccountId != null) {
            List<UserMarketingWxServiceAccountRelationEntity> wxMarketingUserListAssociateBrowseUser = userMarketingWxServiceAccountRelationDao.getByUserMarketingId(browseUserMarketingAccountId,event.getEa());
            if (CollectionUtils.isNotEmpty(wxMarketingUserListAssociateBrowseUser)) {
                log.info("WxUserActionConsumer.bindBrowseUserAndWxUser exist wxMarketingUser associate by browseuser  event:{} sceneId:{} browseId:{}  browseUserMarketingId:{}",
                        event, sceneId, relationEntity.getBrowserUserId(), browseUserMarketingAccountId);
                return;
            }
        }

        AssociationArg wxAssociationArg = new AssociationArg();
        wxAssociationArg.setEa(event.getEa());
        wxAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxAssociationArg.setWxAppId(event.getWxAppId());
        wxAssociationArg.setAssociationId(event.getWxOpenId());
        String wxUserMarketingAccountId = userMarketingAccountRelationManager.getByEaAndKeyProperties(wxAssociationArg).getUserMarketingAccountId();
        if (wxUserMarketingAccountId == null) {
            return;
        }

        userMarketingAccountRelationManager.bindWxUserAndBrowserUser(event.getEa(), event.getWxAppId(), event.getWxOpenId(), relationEntity.getBrowserUserId(), null, "WxUserActionMessageHandler");
    }

    public void doWxTempQrcodeAutoReplyRules(String ea, String wxQrCoedId, String wxOpenId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(wxQrCoedId) || StringUtils.isBlank(wxOpenId)) {
            return;
        }

        WxOfficialAccountsQrCodeEntity qrCodeEntity = wxOfficialAccountsQrCodeDAO.queryQrCodeById(wxQrCoedId,ea);
        if (qrCodeEntity == null) {
            return;
        }

        QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
        queryStoreQrCodeArg.setAppId(qrCodeEntity.getPlatformAppId());
        queryStoreQrCodeArg.setSceneId(qrCodeEntity.getSceneId());
        queryStoreQrCodeArg.setEnterpriseAccount(ea);
        ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
        if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
            log.info("WxOfficialAccountsServiceImpl doWxTempQrcodeAutoReplyRules wx code can't be found ea:{} wxQrCoedId:{}", ea, wxQrCoedId);
            return;
        }
        QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
        if (qrCodeResult.getResponseMsg() == null) {
            return;
        }
        List<com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg> sendCustomerServiceMsgArgList = jsonToSendCustomerServiceMsgArgList(ea, qrCodeResult.getResponseMsg());
        if (CollectionUtils.isEmpty(sendCustomerServiceMsgArgList)) {
            log.warn("WxUserActionConsumer.doWxTempQrcodeAutoReplyRules jsonToSendCustomerServiceMsgArgList result is empty");
            return;
        }

        for (int i = 0; i < sendCustomerServiceMsgArgList.size(); i++) {
            com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg sendCustomerServiceMsgArg = sendCustomerServiceMsgArgList.get(i);
            String url = sendCustomerServiceMsgArg.getUrl();
            url = ReplaceUtil.replaceWxAppId(url, qrCodeEntity.getWxAppId());
            url = ReplaceUtil.replaceMarketingActivityId(url, "");
            url = ReplaceUtil.replaceMarketingEventId(url, qrCodeEntity.getMarketingEventId() == null ? "" : qrCodeEntity.getMarketingEventId());
            url = ReplaceUtil.replaceSpreadFsUserId(url, "");
            url = ReplaceUtil.replaceQrcodeId(url, "");
            sendCustomerServiceMsgArg.setUrl(url);
            sendCustomerServiceMsgArg.setEa(ea);
            sendCustomerServiceMsgArg.setFsUserId(-10000);
            sendCustomerServiceMsgArg.setAppId(qrCodeEntity.getPlatformAppId());
            sendCustomerServiceMsgArg.setWxOpenId(wxOpenId);
            sendCustomerServiceMsgArg.setMsgId(UUIDUtil.getUUID());
            ModelResult<String> sendMessageResult = wechatMessageRestService.sendCustomerServiceMessage(sendCustomerServiceMsgArg);
            log.info("WxUserActionConsumer.doWxTempQrcodeAutoReplyRules sendMessageResult:{}  sendCustomerServiceMsgArg:{}", sendMessageResult, sendCustomerServiceMsgArg);
            if (!sendMessageResult.isSuccess()) {
                log.warn("WxUserActionConsumer.doWxTempQrcodeAutoReplyRules sendMessage fail errorCode:{} errorMsg:{}", sendMessageResult.getErrorCode(), sendMessageResult.getErrorMessage());
            }
        }
    }

    public void doPartnerWxTempQrcodeAutoReplyRules(String ea, String qrCoedId, String wxOpenId, String appId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(qrCoedId) || StringUtils.isBlank(wxOpenId) || StringUtils.isBlank(appId)) {
            return;
        }

        WxServiceQrCodePartnerRelationEntity qrCodeEntity = wxServiceQrCodePartnerRelationDAO.queryRelationByEaAndSceneId(ea, qrCoedId);
        if (qrCodeEntity == null) {
            return;
        }

        QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
        queryStoreQrCodeArg.setAppId(appId);
        queryStoreQrCodeArg.setSceneId(Long.valueOf(qrCodeEntity.getMainSceneId()));
        queryStoreQrCodeArg.setEnterpriseAccount(ea);
        ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
        if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
            log.info("WxOfficialAccountsServiceImpl doPartnerWxTempQrcodeAutoReplyRules wx code can't be found ea:{} qrCoedId:{}", ea, qrCoedId);
            return;
        }
        QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
        if (qrCodeResult.getResponseMsg() == null) {
            return;
        }
        List<com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg> sendCustomerServiceMsgArgList = jsonToSendCustomerServiceMsgArgList(ea, qrCodeResult.getResponseMsg());
        if (CollectionUtils.isEmpty(sendCustomerServiceMsgArgList)) {
            log.warn("WxUserActionConsumer.doHexagonWxTempQrcodeAutoReplyRules jsonToSendCustomerServiceMsgArgList result is empty");
            return;
        }

        for (int i = 0; i < sendCustomerServiceMsgArgList.size(); i++) {
            log.info("WxUserActionConsumer.doPartnerWxTempQrcodeAutoReplyRules sendCustomerServiceMsgArgList url:{}", sendCustomerServiceMsgArgList.get(i).getUrl());
            com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg sendCustomerServiceMsgArg = sendCustomerServiceMsgArgList.get(i);
            String url = sendCustomerServiceMsgArg.getUrl();
            url = ReplaceUtil.replaceWxAppId(url, qrCodeResult.getAppId());
            url = ReplaceUtil.replaceMarketingActivityId(url, qrCodeEntity.getMarketingActivityId() == null ? "" : qrCodeEntity.getMarketingActivityId());
            url = ReplaceUtil.replaceMarketingEventId(url, qrCodeEntity.getMarketingEventId() == null ? "" : qrCodeEntity.getMarketingEventId());
            url = ReplaceUtil.replaceSpreadFsUserId(url, "");
            url = ReplaceUtil.replaceQrcodeId(url, "");
            url = ReplaceUtil.replaceUpstreamEa(url, qrCodeEntity.getEa());
            url = ReplaceUtil.replaceOuterTenantId(url, qrCodeEntity.getOuterTenantId());
            url = ReplaceUtil.replaceOuterUid(url, qrCodeEntity.getOuterUid());
            url = ReplaceUtil.replacePartner(url, "true");
            sendCustomerServiceMsgArg.setUrl(url);
            sendCustomerServiceMsgArg.setEa(ea);
            sendCustomerServiceMsgArg.setFsUserId(-10000);
            sendCustomerServiceMsgArg.setAppId(appId);
            sendCustomerServiceMsgArg.setWxOpenId(wxOpenId);
            sendCustomerServiceMsgArg.setMsgId(UUIDUtil.getUUID());
            ModelResult<String> sendMessageResult = wechatMessageRestService.sendCustomerServiceMessage(sendCustomerServiceMsgArg);
            log.info("WxUserActionConsumer.doPartnerWxTempQrcodeAutoReplyRules sendMessageResult:{}  sendCustomerServiceMsgArg:{}", sendMessageResult, sendCustomerServiceMsgArg);
            if (!sendMessageResult.isSuccess()) {
                log.warn("WxUserActionConsumer.doPartnerWxTempQrcodeAutoReplyRules sendMessage fail errorCode:{} errorMsg:{}", sendMessageResult.getErrorCode(), sendMessageResult.getErrorMessage());
            }
        }
    }

    public void doHexagonWxTempQrcodeAutoReplyRules(String ea, String qrCoedId, String wxOpenId, String appId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(qrCoedId) || StringUtils.isBlank(wxOpenId) || StringUtils.isBlank(appId)) {
            return;
        }

        WxServiceQrCodeUserMarketingRelationEntity qrCodeEntity = wxServiceQrCodeUserMarketingRelationDAO.queryRelationByEaAndSceneId(ea, qrCoedId);
        if (qrCodeEntity == null) {
            return;
        }

        QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
        queryStoreQrCodeArg.setAppId(appId);
        queryStoreQrCodeArg.setSceneId(Long.valueOf(qrCodeEntity.getMainSceneId()));
        queryStoreQrCodeArg.setEnterpriseAccount(ea);
        ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
        if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
            log.info("WxOfficialAccountsServiceImpl doHexagonWxTempQrcodeAutoReplyRules wx code can't be found ea:{} qrCoedId:{}", ea, qrCoedId);
            return;
        }
        QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
        if (qrCodeResult.getResponseMsg() == null) {
            return;
        }
        List<com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg> sendCustomerServiceMsgArgList = jsonToSendCustomerServiceMsgArgList(ea, qrCodeResult.getResponseMsg());
        if (CollectionUtils.isEmpty(sendCustomerServiceMsgArgList)) {
            log.warn("WxUserActionConsumer.doHexagonWxTempQrcodeAutoReplyRules jsonToSendCustomerServiceMsgArgList result is empty");
            return;
        }

        for (int i = 0; i < sendCustomerServiceMsgArgList.size(); i++) {
            com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg sendCustomerServiceMsgArg = sendCustomerServiceMsgArgList.get(i);
            String url = sendCustomerServiceMsgArg.getUrl();
            url = ReplaceUtil.replaceWxAppId(url, qrCodeResult.getAppId());
            url = ReplaceUtil.replaceMarketingActivityId(url, "");
            url = ReplaceUtil.replaceMarketingEventId(url, qrCodeResult.getMarketingEventId() == null ? "" : qrCodeResult.getMarketingEventId());
            url = ReplaceUtil.replaceSpreadFsUserId(url, "");
            url = ReplaceUtil.replaceQrcodeId(url, "");
            sendCustomerServiceMsgArg.setUrl(url);
            sendCustomerServiceMsgArg.setEa(ea);
            sendCustomerServiceMsgArg.setFsUserId(-10000);
            sendCustomerServiceMsgArg.setAppId(appId);
            sendCustomerServiceMsgArg.setWxOpenId(wxOpenId);
            sendCustomerServiceMsgArg.setMsgId(UUIDUtil.getUUID());
            ModelResult<String> sendMessageResult = wechatMessageRestService.sendCustomerServiceMessage(sendCustomerServiceMsgArg);
            log.info("WxUserActionConsumer.doHexagonWxTempQrcodeAutoReplyRules sendMessageResult:{}  sendCustomerServiceMsgArg:{}", sendMessageResult, sendCustomerServiceMsgArg);
            if (!sendMessageResult.isSuccess()) {
                log.warn("WxUserActionConsumer.doHexagonWxTempQrcodeAutoReplyRules sendMessage fail errorCode:{} errorMsg:{}", sendMessageResult.getErrorCode(), sendMessageResult.getErrorMessage());
            }
        }
    }


    public boolean doWxAutoReplyRules(WxUserActionEvent event) {
        if (event.isNeedAutoReplyMessageEvent()) {
            Optional<List<WxAutoReplyRuleResult>> optionalWxAutoReplyRuleResults = weChatServiceManager.listValidWxAutoReplyRuleResult(event.getEa(), event.getWxAppId(), event.getActionType());
            if (!optionalWxAutoReplyRuleResults.isPresent() || CollectionUtils.isEmpty(optionalWxAutoReplyRuleResults.get())) {
                log.info("WxUserActionConsumer.doWxAutoReply without follow rule event:{}", event);
                return false;
            }
            List<WxAutoReplyRuleResult> wxAutoReplyRuleResults = optionalWxAutoReplyRuleResults.get();
            wxAutoReplyRuleResults.forEach(wxAutoReplyRuleResult -> {
                // 校验LIKE和EQ规则
                if (wxAutoReplyRuleResult.getActionType() != null && WxUserActionEvent.SEND_MESSAGE_ACTION_TYPE == wxAutoReplyRuleResult.getActionType()) {
                    boolean matchRule = wxAutoReplyRuleResult.isContentMatchRule(event.getTitle());
                    if (!matchRule) {
                        return;
                    }
                }
                // 添加标签
                if (CollectionUtils.isNotEmpty(wxAutoReplyRuleResult.getTagNameList())) {
                    Optional<ObjectData> optionalObjectData = crmV2Manager.getWechatFanByOpenId(event.getEa(), event.getWxAppId(), event.getWxOpenId());
                    if (!optionalObjectData.isPresent() || optionalObjectData.get() == null) {
                        log.warn("WxUserActionConsumer.doWxAutoReply getWechatFanByOpenId result is null event:{}", event);
                    } else {
                        BatchAddOrDeleteTagNamesToCrmDataArg batchAddOrDeleteTagNamesToCrmDataArg = new BatchAddOrDeleteTagNamesToCrmDataArg();
                        batchAddOrDeleteTagNamesToCrmDataArg.setCrmObjectDescribeApiName(CrmWechatFanFieldEnum.API_NAME.getFieldName());
                        Object wechatId = optionalObjectData.get().get(CrmWechatFanFieldEnum.ID.getFieldName());
                        batchAddOrDeleteTagNamesToCrmDataArg.setCrmObjectIds(ImmutableList.of(wechatId.toString()));
                        batchAddOrDeleteTagNamesToCrmDataArg.setTagNames(wxAutoReplyRuleResult.getTagNameList());
                        Result<Void> batchAddTagNamesToCrmDataResult = userMarketingAccountService.batchAddTagNamesToCrmData(event.getEa(), -10000, batchAddOrDeleteTagNamesToCrmDataArg);
                        if (!batchAddTagNamesToCrmDataResult.isSuccess()) {
                            log.warn("WxUserActionConsumer.doWxAutoReply batchAddTagNamesToCrmData fail errorCode:{} errorMsg:{} arg:{}", batchAddTagNamesToCrmDataResult.getErrCode(), batchAddTagNamesToCrmDataResult.getErrMsg(), batchAddOrDeleteTagNamesToCrmDataArg);
                        }
                    }
                }
                // 发送客服消息
                List<com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg> sendCustomerServiceMsgArgList = jsonToSendCustomerServiceMsgArgList(event.getEa(), wxAutoReplyRuleResult.getResponseMsg());
                if (CollectionUtils.isEmpty(sendCustomerServiceMsgArgList)) {
                    log.warn("WxUserActionConsumer.doWxAutoReply jsonToSendCustomerServiceMsgArgList result is empty");
                    return;
                }
                sendCustomerServiceMsgArgList.forEach(sendCustomerServiceMsgArg -> {
                    sendCustomerServiceMsgArg.setEa(event.getEa());
                    sendCustomerServiceMsgArg.setFsUserId(-10000);
                    sendCustomerServiceMsgArg.setAppId(event.getAppId());
                    sendCustomerServiceMsgArg.setWxOpenId(event.getWxOpenId());
                    sendCustomerServiceMsgArg.setMsgId(UUIDUtil.getUUID());
                    //ModelResult<Void> sendMessageResult = wxCustomerServiceMsgService.sendMessage(sendCustomerServiceMsgArg);
                    ModelResult<String> sendMessageResult = wechatMessageRestService.sendCustomerServiceMessage(sendCustomerServiceMsgArg);
                    log.info("WxUserActionConsumer.doWxAutoReply sendMessageResult:{} event:{} sendCustomerServiceMsgArg:{}", sendMessageResult, event, sendCustomerServiceMsgArg);
                    if (!sendMessageResult.isSuccess()) {
                        log.warn("WxUserActionConsumer.doWxAutoReply sendMessage fail errorCode:{} errorMsg:{}", sendMessageResult.getErrorCode(), sendMessageResult.getErrorMessage());
                    }
                });
            });
        } else {
            log.info("WxUserActionConsumer.doWxAutoReply don't need auto reply event:{}", event);
            return false;
        }
        return true;
    }

    private List<com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg> jsonToSendCustomerServiceMsgArgList(String ea, String responseMsgJson) {
        if (StringUtils.isBlank(responseMsgJson)) {
            log.warn("WxUserActionConsumer.jsonToSendCustomerServiceMsgArgFiled arg is error");
            return null;
        }
        ResponseMsgList responseMsgList = JsonUtil.fromJson(responseMsgJson, ResponseMsgList.class);
        if (CollectionUtils.isEmpty(responseMsgList)) {
            return null;
        }
        List<com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg> result = new LinkedList<>();
        responseMsgList.stream().filter(Objects::nonNull).forEach(responseMsg -> {
            com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg sendCustomerServiceMsgArg = new com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg();
            sendCustomerServiceMsgArg.setType(ResponseMsgTypeEnum.toWxCustomerServiceMsgType(responseMsg.getType()));
            if (ResponseMsgTypeEnum.TEXT.getType().equals(responseMsg.getType())) {
                sendCustomerServiceMsgArg.setContent(responseMsg.getText());
            } else if (ResponseMsgTypeEnum.IMAGE.getType().equals(responseMsg.getType())) {
                sendCustomerServiceMsgArg.setTNOrNPath(responseMsg.getNPath());
            } else if (ResponseMsgTypeEnum.MATERIAL.getType().equals(responseMsg.getType())) {
                sendCustomerServiceMsgArg.setUrl(responseMsg.getLink());
                Result<MaterialWxPresentMsg> materialWxPresentMsgResult = materialService.getMaterialWxPresentMsg(ea, responseMsg.getMaterialType(), responseMsg.getMaterialId());
                if (materialWxPresentMsgResult == null || !materialWxPresentMsgResult.isSuccess() || materialWxPresentMsgResult.getData() == null) {
                    log.warn("WxUserActionConsumer.jsonToSendCustomerServiceMsgArgFiled materialWxPresentMsgResult is not successful responseMsgJson:{} responseMsg:{} sendCustomerServiceMsgArg:{}", responseMsgJson, responseMsg, sendCustomerServiceMsgArg);
                    return;
                }
                sendCustomerServiceMsgArg.setContent(materialWxPresentMsgResult.getData().getTitle());
                sendCustomerServiceMsgArg.setDescription(materialWxPresentMsgResult.getData().getDescription());
                sendCustomerServiceMsgArg.setSharePicUrl(materialWxPresentMsgResult.getData().getSharePicUrl());
            } else if (ResponseMsgTypeEnum.MP_NEWS.getType().equals(responseMsg.getType())) {
                sendCustomerServiceMsgArg.setMediaId(responseMsg.getText());
            }
            result.add(sendCustomerServiceMsgArg);
        });
        return result;
    }

    private void doProcessIntegral(String ea, Integer ei, String userMarketingAccountId, String wxAppId, String actionApiName, String categoryApiName) {
        if (StringUtils.isEmpty(userMarketingAccountId)){
            return;
        }

        List<String> leadIds = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, ChannelEnum.CRM_LEAD.getType(), Lists.newArrayList(userMarketingAccountId));
        for (String leadId : leadIds) {
            BehaviorSendEventData data = new BehaviorSendEventData();
            data.setActionApiName(actionApiName);
            data.setCategoryApiName(categoryApiName);
            data.setMaterialApiName(wxAppId);
            data.setObjectApiName(LeadsFieldContants.API_NAME);
            data.setObjectId(leadId);
            data.setTenantId("" + ei);
            data.setTimestamp(System.currentTimeMillis());
            behaviorSendEventSender.sendToDelayMq(data);
        }
    }

    private String doGetUserMarketingAccountId(WxUserActionEvent event) {
        AssociationArg associationArg = new AssociationArg();
        associationArg.setType(ChannelEnum.WX_SERVICE.getType());
        associationArg.setWxAppId(event.getWxAppId());
        associationArg.setAssociationId(event.getWxOpenId());
        associationArg.setEa(event.getEa());
        associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        associationArg.setTriggerAction("WxUserActionMessageHandler");
        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
        if (associationResult == null || associationResult.getUserMarketingAccountId() == null) {
            log.warn("WxUserActionConsumer.doGetUserMarketingAccountId associationResult is not successful event:{}", event);
            return null;
        }
        return associationResult.getUserMarketingAccountId();
    }

    @Data
    public static class WxUserActionEvent implements Serializable {
        public static final int FOLLOW_ACTION_TYPE = 1;
        public static final int UN_FOLLOW_ACTION_TYPE = 2;
        public static final int CLICK_MENU_ACTION_TYPE = 3;
        public static final int CLICK_LINK_ACTION_TYPE = 4;
        public static final int SEND_MESSAGE_ACTION_TYPE = 5;
        public static final int SCAN_QR_CODE_ACTION_TYPE = 6;

        public static final String scenePrefix = "qrscene_";
        public static final String ctaPrefix = "CTA_";


        private String ea;
        private Long actionTime;
        private String wxAppId;
        private String wxOpenId;
        private int actionType;
        private String appId;
        private String serviceName;
        private String title;
        private String link;
        private Integer subscribeChannel;
        /**
         * 微信公众号二维码id 如果为空则是营销通的，如果不为空有两种格式：100000001和qrscene_100000001,其中的数字小于1亿是营销通的消息，大于1亿是服务通的消息
         */
        private String sceneId;
        private String sceneStr;
        private String spreadFsUserId;
        private String sceneType;
        private Map<String, Object> param;

        private boolean validBase() {
            return !Strings.isNullOrEmpty(ea) && !Strings.isNullOrEmpty(appId) && !Strings.isNullOrEmpty(wxAppId) && !Strings.isNullOrEmpty(wxOpenId);
        }

        public boolean isValidFollowEvent() {
            return validBase() && actionType == FOLLOW_ACTION_TYPE;
        }

        public boolean isValidUnFollowEvent() {
            return validBase() && actionType == UN_FOLLOW_ACTION_TYPE;
        }

        public boolean isValidSendMessageEvent() {
            return validBase() && actionType == SEND_MESSAGE_ACTION_TYPE;
        }

        public boolean isValidScanQrCodeEvent() {
            return validBase() && actionType == SCAN_QR_CODE_ACTION_TYPE;
        }

        public boolean isNeedAutoReplyMessageEvent() {
            return isValidFollowEvent() || isValidSendMessageEvent();
        }

        public boolean isValidClickMenuEvent() {
            return validBase() && actionType == CLICK_MENU_ACTION_TYPE && !Strings.isNullOrEmpty(title);
        }

        public boolean isValidClickLinkActionEvent() {
            return validBase() && actionType == CLICK_LINK_ACTION_TYPE;
        }
    }

    private void doRecordToUnionMarketingUserMessage(WxUserActionEvent event) {
        RecordWxUserMkActionArg recordWxUserMkActionArg = new RecordWxUserMkActionArg();
        recordWxUserMkActionArg.setEa(event.getEa());
        recordWxUserMkActionArg.setWxAppId(event.getWxAppId());
        recordWxUserMkActionArg.setWxOpenId(event.getWxOpenId());
        String sceneId = event.getSceneId();
        if (StringUtils.isNotEmpty(sceneId)) {
            sceneId = sceneId.replace(WxUserActionMessageHandler.WxUserActionEvent.scenePrefix, "");
        }
        if(StringUtils.isNotBlank(sceneId) && sceneId.startsWith(WxUserActionEvent.ctaPrefix)) {
            CtaQrCodeRelationEntity ctaQrCodeRelationEntity = ctaQrCodeRelationDAO.getByState(event.getEa(), sceneId);
            if(ctaQrCodeRelationEntity != null) {
                CtaEntity ctaEntity = ctaDAO.queryCtaDetail(event.getEa(), ctaQrCodeRelationEntity.getCtaId());
                if(ctaEntity != null) {
                    Map<String, Object> extensionParams = Maps.newHashMap();
                    extensionParams.put("ctaId", ctaEntity.getId());
                    extensionParams.put("ctaName", ctaEntity.getName());
                    extensionParams.put("ctaObjectType", ctaQrCodeRelationEntity.getObjectType());
                    extensionParams.put("ctaObjectId", ctaQrCodeRelationEntity.getObjectId());
                    recordWxUserMkActionArg.setExtensionParams(extensionParams);
                }
                recordWxUserMkActionArg.setObjectId(ctaQrCodeRelationEntity.getObjectId());
                recordWxUserMkActionArg.setObjectType(ctaQrCodeRelationEntity.getObjectType());
            }
        }

        if (event.getActionType() == WxUserActionEvent.FOLLOW_ACTION_TYPE && Integer.valueOf(2).equals(event.getSubscribeChannel())) {
            recordWxUserMkActionArg.setActionType(MarketingUserActionType.SCAN_WX_SERVICE_QR_CODE.getActionType());
            recordWxUserMkActionArg.setObjectId(event.getTitle());
            wxUserMkActionService.record(recordWxUserMkActionArg);
        }
        if (event.getActionType() == WxUserActionEvent.FOLLOW_ACTION_TYPE) {
            recordWxUserMkActionArg.setActionType(MarketingUserActionType.FOLLOW_WX_SERVICE.getActionType());
        }
        if (event.getActionType() == WxUserActionEvent.UN_FOLLOW_ACTION_TYPE) {
            recordWxUserMkActionArg.setActionType(MarketingUserActionType.UN_FOLLOW_WX_SERVICE.getActionType());
        }
        if (event.getActionType() == WxUserActionEvent.CLICK_MENU_ACTION_TYPE) {
            recordWxUserMkActionArg.setActionType(MarketingUserActionType.CLICK_WX_SERVICE_MENU.getActionType());
            recordWxUserMkActionArg.setObjectId(event.getTitle());
        }
        if (event.getActionType() == WxUserActionEvent.CLICK_LINK_ACTION_TYPE) {
            recordWxUserMkActionArg.setActionType(MarketingUserActionType.CLICK_WX_SERVICE_LINK.getActionType());
            recordWxUserMkActionArg.setObjectId(event.getLink());
        }
        if (event.getActionType() == WxUserActionEvent.SEND_MESSAGE_ACTION_TYPE) {
            return;
        }
        if (event.getActionType() == WxUserActionEvent.SCAN_QR_CODE_ACTION_TYPE) {
            recordWxUserMkActionArg.setActionType(MarketingUserActionType.SCAN_WX_SERVICE_QR_CODE.getActionType());
            recordWxUserMkActionArg.setObjectId(event.getTitle());
        }
        wxUserMkActionService.record(recordWxUserMkActionArg);
    }

    //绑定微信营销用户身份和访客身份
    private void bindUserMarketingAndWxUser(WxUserActionEvent event, String sceneId) {
        WxServiceQrCodeUserMarketingRelationEntity relationEntity = wxServiceQrCodeUserMarketingRelationDAO.queryRelationByEaAndSceneId(event.getEa(), sceneId);
        if (relationEntity == null || StringUtils.isBlank(relationEntity.getUserMarketingId())) {
            return;
        }

        /**
         * 访客身份已经关联了营销用户，且该营销用户关联了公众号身份，就不再做绑定，防止多个用户扫一个码，身份关联到一起了
         * **/
        List<UserMarketingWxServiceAccountRelationEntity> wxMarketingUserListAssociateBrowseUser = userMarketingWxServiceAccountRelationDao.getByUserMarketingId(relationEntity.getUserMarketingId(),event.getEa());
        if (CollectionUtils.isNotEmpty(wxMarketingUserListAssociateBrowseUser)) {
            return;
        }

        AssociationArg wxAssociationArg = new AssociationArg();
        wxAssociationArg.setEa(event.getEa());
        wxAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxAssociationArg.setWxAppId(event.getWxAppId());
        wxAssociationArg.setAssociationId(event.getWxOpenId());
//        //企微客户有营销用户就关联,没有就新建一个营销用户,已经有relation关联就merge,没有关联就新建一条relation
//        AssociationResult targetAssociationResult = userMarketingAccountAssociationManager.associate(wxAssociationArg);
        AssociationResult targetAssociationResult = userMarketingAccountRelationManager.getByEaAndKeyProperties(wxAssociationArg);
        if (targetAssociationResult.getUserMarketingAccountId() == null) {
            return;
        }
        //关联企业微信用户和营销用户
        userMarketingAccountAssociationManager.merge(event.getEa(), targetAssociationResult.getUserMarketingAccountId(), relationEntity.getUserMarketingId(), wxAssociationArg);
    }


    @Data
    public static class WxOfficialAccountsProxyMessage implements Serializable {

        private static final long serialVersionUID = 1L;

        private String ea;

        private String wxAppId;

        private String wxOpenId;

        private Integer actionType;

        private String appId;

        private String serviceName;

        private String sceneType;

        private Map<String, Object> param;

        private Integer subscribeChannel;

    }
}
