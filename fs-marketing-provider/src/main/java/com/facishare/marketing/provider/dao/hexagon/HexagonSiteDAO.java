package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.dao.param.hexagon.HexagonSiteQueryParam;
import com.facishare.marketing.provider.dto.ContentCenterDataDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

public interface HexagonSiteDAO {
    @FilterLog
    @Select("select * from hexagon_site where id = #{id} and status != 4")
    HexagonSiteEntity getById(@Param("id") String id, @Param("ea") String ea);

    @Select("select * from hexagon_site where id = #{id}")
    HexagonSiteEntity getByIdIngnoreStatus(@Param("id") String id, @Param("ea") String ea);

    @Select("SELECT COUNT(*) FROM hexagon_site WHERE ea=#{ea} AND name=#{name} AND status != 4 AND status != 5 AND is_system_site != true")
    int queryHexagonSiteCountByName(@Param("ea")String ea, @Param("name")String name);

    @Select("select COUNT(*) from hexagon_site where ea=#{ea} AND name=#{name} AND status != 4 AND status != 5")
    int queryCountByName(@Param("ea")String ea, @Param("name")String name);

    @Select("<script>"
            + "select * from hexagon_site where status != 4 AND id in"
            + " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + "</script>")
    List<HexagonSiteEntity> getByIds(@Param("ids")List<String> ids, @Param("ea") String ea);

    @Select("<script>"
            + "select * from hexagon_site where id in"
            + " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + "</script>")
    List<HexagonSiteEntity> getAllByIds(@Param("ids")List<String> ids, @Param("ea") String ea);

    @Select("select * from hexagon_site where id = #{id}")
    HexagonSiteEntity getInclueDeletedById(@Param("id") String id, @Param("ea") String ea);

    @Select("<script>"
            + "select * from hexagon_site where ea = #{ea} and status != 4 and status != 5"
            + "         <if test=\"statusFitter != null\">\n"
            + "             AND status = #{statusFitter}\n"
            + "          </if>\n"
            + "         <if test=\"searchFitter != null\">\n"
            + "             AND \"name\" ~* #{searchFitter}\n"
            + "          </if>\n"
            + "         <if test=\"excludeSystemSite\">\n"
            + "             AND (is_system_site = false OR is_system_site is null) "
            + "          </if>\n"
            + " order by update_time desc"
            + "</script>")
    List<HexagonSiteEntity> getByEa(@Param("ea") String ea, @Param("page") Page page, @Param("searchFitter") String searchFitter, @Param("statusFitter") Integer statusFitter, @Param("excludeSystemSite") boolean excludeSystemSite);

    @Select("<script>"
            + "SELECT * FROM hexagon_site h LEFT JOIN content_marketing_event_material_relation c ON h.id = c.object_id\n"
            + "WHERE h.ea=#{ea} AND c.event_type != '3' AND c.event_type != 'live_marketing' AND h.status != 4 and h.status != 5\n"
            + "         <if test=\"statusFitter != null\">\n"
            + "             AND h.status = #{statusFitter}\n"
            + "          </if>\n"
            + "         <if test=\"searchFitter != null\">\n"
            + "             AND \"h.name\" ~* #{searchFitter}\n"
            + "          </if>\n"
            + "         <if test=\"excludeSystemSite\">\n"
            + "             AND (h.is_system_site = false OR h.is_system_site is null) "
            + "          </if>\n"
            + " ORDER BY h.update_time DESC"
            + "</script>")
    List<HexagonSiteEntity> getMarketingContentSiteByPage1(@Param("ea") String ea, @Param("page") Page page, @Param("searchFitter") String searchFitter, @Param("statusFitter") Integer statusFitter, @Param("excludeSystemSite") boolean excludeSystemSite);


    @Select("<script>"
            + "SELECT distinct h.* FROM hexagon_site h LEFT JOIN content_marketing_event_material_relation c ON h.id = c.object_id\n"
            + "WHERE h.ea=#{ea} AND h.status != 4 and h.status != 5 AND c.marketing_event_id NOT IN(\n"
            + "SELECT marketing_event_id FROM activity a WHERE a.ea=#{ea} AND a.marketing_event_id is not null and a.status = 1\n"
            + "UNION ALL\n"
            +" SELECT marketing_event_id FROM marketing_live m WHERE m.corp_id=#{corpId} AND m.marketing_event_id is not null and m.status != 6)\n"
            + "         <if test=\"statusFitter != null\">\n"
            + "             AND h.status = #{statusFitter}\n"
            + "          </if>\n"
            + "         <if test=\"searchFitter != null\">\n"
            + "             AND h.name ~* #{searchFitter}\n"
            + "          </if>\n"
            + "         <if test=\"excludeSystemSite\">\n"
            + "             AND (h.is_system_site = false OR h.is_system_site is null) "
            + "          </if>\n"
            + " ORDER BY h.update_time DESC"
            + "</script>")
    List<HexagonSiteEntity> getMarketingContentSiteByPage(@Param("ea") String ea, @Param("corpId")int corpId, @Param("page") Page page, @Param("searchFitter") String searchFitter, @Param("statusFitter") Integer statusFitter, @Param("excludeSystemSite") boolean excludeSystemSite);


    @Select("<script>"
            + "select * from hexagon_site where status != 4 AND status != 5 "
            + " AND id IN <foreach collection=\"ids\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{ids[${idx}]}</foreach> "
            + "</script>")
    List<HexagonSiteEntity> listByIds( @Param("ids") Collection<String> ids, @Param("ea") String ea);


    @Select("<script>"
        + "select id,name from hexagon_site where "
        + " id IN <foreach collection=\"ids\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{ids[${idx}]}</foreach> "
        + "</script>")
    List<HexagonSiteEntity> listNameByIds( @Param("ids") Collection<String> ids, @Param("ea") String ea);

    @Insert("INSERT INTO hexagon_site"
            + "(id, ea, name, lead_count, status, create_by, update_by, is_system_site, create_time, update_time)"
            + " VALUES"
            + " (#{obj.id}, #{obj.ea}, #{obj.name}, 0, #{obj.status}, #{obj.createBy}, #{obj.updateBy}, #{obj.isSystemSite}, now(), now())")
    int insert(@Param("obj") HexagonSiteEntity hexagonSiteEntity);

    @Update("<script>"
            + "UPDATE hexagon_site"
            + "    <set>"
            + "         <if test=\"name != null\">\n"
            + "             name = #{name},\n"
            + "          </if>\n"
            + "         <if test=\"status != null\">\n"
            + "             status = #{status},\n"
            + "          </if>\n"
            + "         <if test=\"updateBy != null\">\n"
            + "             update_by = #{updateBy},\n"
            + "          </if>\n"
            + "         update_time = now()\n"
            + "    </set>"
            + "    WHERE id = #{id}"
            + "</script>")
    int update(HexagonSiteEntity hexagonSiteEntity);

    @Update("update hexagon_site set status = 4 where id = #{id}")
    int deleteById(@Param("id") String id, @Param("ea") String ea);

    @Update("update hexagon_site set status = #{status} where id = #{id}")
    int updateStatus(@Param("status") Integer status, @Param("id") String id, @Param("ea") String ea);

    @Select("<script>"
            + "SELECT * FROM\n"
            + "(\n"
            + "SELECT C.hexagon_site_id,C.share_pic_h5_apath, C.share_title, c.out_display_name, c.share_desc, row_number () over (PARTITION BY C.hexagon_site_id ORDER BY C.is_homepage ASC NULLS LAST, C.create_time DESC, C.share_pic_h5_apath DESC NULLS LAST) AS ROW FROM\n"
            + "(\n"
            + "select B.*,A.out_display_name from hexagon_site AS A JOIN hexagon_page AS B ON A.id = B.hexagon_site_id where A.id IN\n"
            + " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + "AND B.share_pic_h5_apath is not null\n"
            + ") AS C \n"
            + ") AS D WHERE D.row = 1"
            + "</script>")
    List<HexagonSiteListDTO> getCoverBySiteIds(@Param("ids") List<String> ids, @Param("ea") String ea);

    @Select("<script>"
            + "SELECT * FROM\n"
            + "(\n"
            + "SELECT C.hexagon_site_id,C.form_id,C.id as hexagon_page_id, row_number () over (PARTITION BY C.hexagon_site_id ORDER BY C.is_homepage ASC NULLS LAST, C.create_time DESC, C.form_id DESC NULLS LAST) AS ROW FROM\n"
            + "(\n"
            + "select B.* from hexagon_site AS A JOIN hexagon_page AS B ON A.id = B.hexagon_site_id where A.id IN\n"
            + " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + "AND B.form_id is not null AND B.form_id != '' AND B.status = 1\n"
            + ") AS C \n"
            + ") AS D WHERE D.row = 1"
            + "</script>")
    List<HexagonSiteListDTO> getFormBySiteIds(@Param("ids") List<String> ids, @Param("ea") String ea);


    @Select("<script>"
        + "SELECT * FROM\n"
        + "(\n"
        + "SELECT C.conference_id, C.hexagon_site_id,C.form_id,C.id as hexagon_page_id, row_number () over (PARTITION BY C.hexagon_site_id ORDER BY C.is_homepage ASC NULLS LAST, C.create_time DESC, C.form_id DESC NULLS LAST) AS ROW FROM\n"
        + "(\n"
        + "SELECT E.id AS conference_id, B.* from hexagon_site AS A JOIN hexagon_page AS B ON A.id = B.hexagon_site_id JOIN activity AS E ON A.id = E.activity_detail_site_id WHERE A.id IN\n"
        + " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
        + "     #{item}"
        + " </foreach>"
        + "AND B.form_id is not null AND B.status = 1\n"
        + ") AS C \n"
        + ") AS D WHERE D.row = 1"
        + "</script>")
    List<HexagonSiteListDTO> getConferenceSiteFormBySiteIds(@Param("ids") List<String> ids, @Param("ea") String ea);

    @Update("update hexagon_site set is_system_site=true where id = #{id}")
    int markAsSystemSite(@Param("id") String id, @Param("ea") String ea);

    @Select("<script>"
            + "SELECT COUNT(*) FROM hexagon_site WHERE ea = #{ea} AND create_by=#{userId} AND (is_system_site is null OR is_system_site = false) AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "</script>")
    int queryAllHexagonSiteCountCreateByMe(@Param("ea")String ea, @Param("userId")Integer userId, @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT COUNT(*) FROM hexagon_site WHERE ea=#{ea} AND (is_system_site = false OR is_system_site is null) AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "</script>")
    int queryAllHexagonSiteCountCreateByAll(@Param("ea")String ea, @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT COUNT(*) FROM ("
            + "SELECT hexagon_site.id FROM hexagon_site JOIN object_group_relation on  hexagon_site.ea = object_group_relation.ea AND hexagon_site.id = object_group_relation.object_id"
            + " WHERE hexagon_site.ea = #{ea} AND (hexagon_site.is_system_site = false OR hexagon_site.is_system_site is null) AND hexagon_site.status NOT IN(4,5)\n"
            + " AND object_group_relation.group_id IN\n"
            + "<foreach collection = 'groupIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "<if test=\"keyword != null\"> AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\"> AND status=#{status}\n</if>"
            + "UNION "
            + " select hexagon_site.id from hexagon_site  where ea = #{ea} and status NOT IN(4,5) AND (is_system_site = false OR is_system_site is null)"
            + "<if test=\"keyword != null\"> AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + " and id not in (select object_id from object_group_relation where ea = #{ea} and object_type = 26)"
            + "UNION "
            + "SELECT hexagon_site.id FROM hexagon_site WHERE ea = #{ea} AND create_by = #{userId}\n"
            + "<if test=\"keyword != null\"> AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + " AND (is_system_site is null OR is_system_site = false) AND status NOT IN(4,5)"
            + " ) hex"
            + "</script>")
    int queryAccessibleHexagonSiteCount(@Param("ea")String ea, @Param("status")Integer status, @Param("userId")Integer userId,
                                        @Param("keyword")String keyword, @Param("groupIdList")List<String> groupIdList);

    @Select("<script>"
            + "SELECT COUNT(*) FROM("
            + "SELECT * FROM hexagon_site WHERE ea=#{ea} AND create_by=#{userId} AND (is_system_site = false OR is_system_site is null) AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "UNION\n"
            + "SELECT h.* FROM object_access_authorize a INNER JOIN hexagon_site h ON a.object_id = h.id AND a.ea=#{ea}\n"
            + "AND h.ea=#{ea} WHERE a.ea=#{ea} AND (h.is_system_site is null OR h.is_system_site = false)\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
            + "AND (a.access_authorize ->>'isPublic'='true' OR\n"
            + "array(select jsonb_array_elements_text(a.access_authorize -> 'userIds')) @> array[#{userId}::text])\n"
            + ")"
            + "</script>")
    int queryHexagonCountByAllForNotAdmin(@Param("ea")String ea, @Param("userId")Integer userId, @Param("status")Integer status, @Param("keyword")String keyword);


    @Select("<script>"
            + "SELECT COUNT(*) FROM object_access_authorize a INNER JOIN hexagon_site h ON a.object_id = h.id AND a.ea=#{ea} AND status NOT IN(4,5)\n"
            + "AND h.ea=#{ea} WHERE h.ea=#{ea} AND (h.is_system_site is null OR h.is_system_site = false)\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
            + "AND array(select jsonb_array_elements_text(a.access_authorize -> 'userIds')) @> array[#{userId}::text]\n"
            + "</script>")
    int queryHexagonCountByShareToMe(@Param("ea")String ea,  @Param("userId")Integer userId, @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT COUNT(*) FROM object_access_authorize a INNER JOIN hexagon_site h ON a.object_id = h.id AND a.ea=#{ea}\n"
            + "AND h.ea=#{ea} WHERE a.ea=#{ea} AND (is_system_site is null OR is_system_site = false)\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
            + " AND a.access_authorize ->>'isPublic'='true'\n"
            + "</script>")
    int queryHexagonCountByPublic(@Param("ea")String ea,  @Param("userId")Integer userId, @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT COUNT(*) FROM ("
            + "SELECT id FROM hexagon_site WHERE ea=#{ea} AND (is_system_site is null OR is_system_site = false) AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea=#{ea} AND object_type=#{objectType}))t"
            + "</script>")
    int queryHexagonCountByUnGrouped(@Param("ea")String ea,  @Param("objectType")Integer objectType,  @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "select count(*) from ("
            + "SELECT hexagon_site.id FROM hexagon_site WHERE ea = #{ea} AND (is_system_site is null OR is_system_site = false) AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 26)"
            + "UNION "
            + "SELECT hexagon_site.id FROM hexagon_site WHERE ea = #{ea} AND create_by = #{userId}\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + " AND (is_system_site is null OR is_system_site = false) AND status NOT IN(4,5)"
            + " ) ct"
            + "</script>")
    int queryUnGroupAndCreateByMeCount(@Param("ea")String ea, @Param("userId") int userId, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT * FROM hexagon_site" +
            " left join object_top on hexagon_site.id = object_top.object_id and object_top.ea = hexagon_site.ea\n"+
            " WHERE hexagon_site.ea=#{ea} AND (is_system_site = false OR is_system_site is null) AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
//            + " ORDER BY create_time DESC\n"
            + " order by object_top.create_time desc nulls last, hexagon_site.create_time desc"
            + "</script>")
    List<HexagonSiteEntity> pageQueryHexagonEntityByAllForAdmin(@Param("ea")String ea, @Param("status")Integer status, @Param("keyword")String keyword, @Param("page")Page page);

    @Select("<script>"
            + "SELECT * FROM("
            + "SELECT * FROM hexagon_site WHERE ea=#{ea} AND create_by=#{userId} AND (is_system_site = false OR is_system_site is null) AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "UNION\n"
            + "SELECT h.* FROM object_access_authorize a INNER JOIN hexagon_site h ON a.object_id = h.id AND a.ea=#{ea}\n"
            + "AND h.ea=#{ea} WHERE a.ea=#{ea} AND (h.is_system_site is null OR h.is_system_site = false)\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
            + "AND (a.access_authorize ->>'isPublic'='true' OR\n"
            + "array(select jsonb_array_elements_text(a.access_authorize -> 'userIds')) @> array[#{userId}::text])\n"
            + ")t ORDER BY t.create_time DESC"
            + "</script>")
    List<HexagonSiteEntity> pageQueryHexagonEntityByAllForNotAdmin(@Param("ea")String ea, @Param("userId")Integer userId, @Param("status")Integer status, @Param("keyword")String keyword, @Param("page")Page page);

    @Select("<script>"
            + "SELECT * FROM hexagon_site left join object_top on hexagon_site.id = object_top.object_id and object_top.ea = hexagon_site.ea\n" +
            "WHERE hexagon_site.ea=#{ea} AND (is_system_site is null OR is_system_site = false) AND create_by=#{userId} AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "<if test = 'materialTagFilter != null and materialTagFilter.materialTagIds != null and materialTagFilter.materialTagIds.size() > 0'>" +
                "<if test = 'materialTagFilter.type == 1'>" +
                " and exists (" +
                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                " and mtr.tag_id in " +
                " <foreach open='(' close=')' separator=',' item='item' index='index' collection='materialTagFilter.materialTagIds'> " +
                " #{materialTagFilter.materialTagIds[${index}]} " +
                " </foreach>" +
                ")" +
                "</if>" +
                "<if test = 'materialTagFilter.type == 2'>" +
                " and exists (" +
                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                " group by object_id having ARRAY_AGG(tag_id) <![CDATA[ @> ]]> " +
                " ARRAY<foreach open='[' close=']' separator=',' item='item' index='index' collection='materialTagFilter.materialTagIds'> #{materialTagFilter.materialTagIds[${index}]} </foreach>::varchar[] " +
                ")" +
                "</if>"
            + "</if>"

            + "<if test=\"hasFormId == true\">  AND EXISTS (SELECT hexagon_site_id FROM hexagon_page LEFT JOIN cta_relation ON cta_relation.ea=hexagon_page.ea AND cta_relation.object_id=hexagon_page.id AND cta_relation.object_type=27 WHERE hexagon_site.ea = hexagon_page.ea AND hexagon_site.id = hexagon_page.hexagon_site_id AND hexagon_page.form_id IS NOT NULL AND hexagon_page.form_id != '' AND cta_relation.id IS NULL) \n</if>"

            + "order by object_top.create_time desc nulls last, hexagon_site.create_time desc\n"
            + "</script>")
    List<HexagonSiteEntity> pageQueryHexagonEntityByMe(@Param("ea")String ea, @Param("userId")Integer userId, @Param("status")Integer status, @Param("keyword")String keyword,
                                                       @Param("materialTagFilter") MaterialTagFilterArg materialTagFilter, @Param("hasFormId")boolean hasFormId, @Param("page")Page page);

    @Select("<script>"
            + "SELECT h.* FROM object_access_authorize a INNER JOIN hexagon_site h ON a.object_id = h.id AND a.ea=#{ea} AND status NOT IN(4,5)\n"
            + "AND h.ea=#{ea} WHERE h.ea=#{ea} AND (h.is_system_site is null OR h.is_system_site = false)\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
            + "AND array(select jsonb_array_elements_text(a.access_authorize -> 'userIds')) @> array[#{userId}::text]\n"
            + "ORDER BY h.create_time DESC\n"
            + "</script>")
    List<HexagonSiteEntity> pageQueryHexagonEntityBySharedToMe(@Param("ea")String ea,  @Param("userId")Integer userId, @Param("status")Integer status, @Param("keyword")String keyword, @Param("page")Page page);

    @Select("<script>"
            + "SELECT h.* FROM object_access_authorize a INNER JOIN hexagon_site h ON a.object_id = h.id AND a.ea=#{ea} AND status NOT IN(4,5)\n"
            + "AND h.ea=#{ea} WHERE h.ea=#{ea} AND (h.is_system_site is null OR h.is_system_site = false)\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
            + "AND a.access_authorize ->>'isPublic'='true'\n"
            + "ORDER BY h.create_time DESC\n"
            + "</script>")
    List<HexagonSiteEntity> pageQueryHexagonEntityByPublic(@Param("ea")String ea, @Param("status")Integer status, @Param("keyword")String keyword, @Param("page")Page page);

    @Select("<script>"
            + "SELECT * FROM hexagon_site left join object_top on hexagon_site.id = object_top.object_id and object_top.ea = hexagon_site.ea\n" +
            "WHERE hexagon_site.ea=#{ea} AND status NOT IN(4,5) AND (is_system_site is null OR is_system_site = false)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "AND hexagon_site.id NOT IN(SELECT object_id FROM object_group_relation WHERE ea=#{ea} AND object_type=#{objectType})"
            + "<if test = 'materialTagFilter != null and materialTagFilter.materialTagIds != null and materialTagFilter.materialTagIds.size() > 0'>" +
                "<if test = 'materialTagFilter.type == 1'>" +
                " and exists (" +
                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                " and mtr.tag_id in " +
                " <foreach open='(' close=')' separator=',' item='item' index='index' collection='materialTagFilter.materialTagIds'> " +
                " #{materialTagFilter.materialTagIds[${index}]} " +
                " </foreach>" +
                ")" +
                "</if>" +
                "<if test = 'materialTagFilter.type == 2'>" +
                " and exists (" +
                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                " group by object_id having ARRAY_AGG(tag_id) <![CDATA[ @> ]]> " +
                " ARRAY<foreach open='[' close=']' separator=',' item='item' index='index' collection='materialTagFilter.materialTagIds'> #{materialTagFilter.materialTagIds[${index}]} </foreach>::varchar[] " +
                ")" +
                "</if>"
            + "</if>"

            + "<if test=\"hasFormId == true\">  AND EXISTS (SELECT hexagon_site_id FROM hexagon_page LEFT JOIN cta_relation ON cta_relation.ea=hexagon_page.ea AND cta_relation.object_id=hexagon_page.id AND cta_relation.object_type=27 WHERE hexagon_site.ea = hexagon_page.ea AND hexagon_site.id = hexagon_page.hexagon_site_id AND hexagon_page.form_id IS NOT NULL AND hexagon_page.form_id != '' AND cta_relation.id IS NULL) \n</if>"

            + "order by object_top.create_time desc nulls last, hexagon_site.create_time desc\n"
            + "</script>")
    List<HexagonSiteEntity> pageQueryHexagonEntityByUngrouped(@Param("ea")String ea, @Param("status")Integer status,
                                                              @Param("keyword")String keyword, @Param("objectType")Integer objectType,
                                                              @Param("materialTagFilter") MaterialTagFilterArg materialTagFilter, @Param("hasFormId")boolean hasFormId, @Param("page")Page page);

    @Select("<script>"
            + "SELECT h.* FROM hexagon_site h INNER JOIN object_group_relation l ON h.id=l.object_id AND h.ea=#{ea} AND l.ea=#{ea}\n"+
            " left join object_top on h.id = object_top.object_id and object_top.ea = h.ea\n"
            + "WHERE h.ea=#{ea} AND (h.is_system_site is null OR h.is_system_site = false) AND h.status NOT IN(4,5) AND l.group_id=#{groupId}\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
       //     + "ORDER BY h.create_time DESC\n"
            + "order by object_top.create_time desc nulls last, h.create_time desc\n"
            + "</script>")
    List<HexagonSiteEntity> pageQueryHexagonEntityByGroupId(@Param("ea")String ea, @Param("groupId")String groupId, @Param("status")Integer status,
                                                              @Param("keyword")String keyword, @Param("page")Page page);

    @Update("UPDATE hexagon_site SET is_system_site=#{status}, update_time=now() WHERE ea=#{ea} AND id=#{objectId}")
    int updateHexagonSiteSystemStatus(@Param("ea")String ea, @Param("objectId")String objectId, @Param("status")boolean status);

    @Select("<script>"
            + "SELECT hexagon_site.id AS objectId, 26 AS objectType, hexagon_site.name AS title, hexagon_site.create_time, hexagon_site.out_display_name AS outDisplayName FROM hexagon_site" +
            " left join object_top on hexagon_site.id = object_top.object_id and object_top.ea = hexagon_site.ea\n"+
            " WHERE hexagon_site.ea = #{ea} AND hexagon_site.status=1 \n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"ids != null\">"
            + "  AND hexagon_site.id IN\n"
            +   "<foreach collection = 'ids' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{ids[${num}]}"
            +   "</foreach>"
            +"</if>"
            + "<if test = 'materialTagFilter != null and materialTagFilter.materialTagIds != null and materialTagFilter.materialTagIds.size() > 0'>" +
                "<if test = 'materialTagFilter.type == 1'>" +
                " and exists (" +
                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                " and mtr.tag_id in " +
                " <foreach open='(' close=')' separator=',' item='item' index='index' collection='materialTagFilter.materialTagIds'> " +
                " #{materialTagFilter.materialTagIds[${index}]} " +
                " </foreach>" +
                ")" +
                "</if>" +
                "<if test = 'materialTagFilter.type == 2'>" +
                " and exists (" +
                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                " group by object_id having ARRAY_AGG(tag_id) <![CDATA[ @> ]]> " +
                " ARRAY<foreach open='[' close=']' separator=',' item='item' index='index' collection='materialTagFilter.materialTagIds'> #{materialTagFilter.materialTagIds[${index}]} </foreach>::varchar[] " +
                ")" +
                "</if>"
            + "</if>"
//            + "ORDER BY create_time DESC"
            + " order by object_top.create_time desc nulls last, hexagon_site.create_time desc\n"
            + "</script>")
    List<ContentCenterDataDTO> getListByEaAndIds(@Param("ea")String ea, @Param("ids") List<String> ids,@Param("keyword")String keyword, @Param("page")Page page,
                                                 @Param("materialTagFilter") MaterialTagFilterArg materialTagFilter);

    @Update("<script>"
            + "update hexagon_site set status = 4 where id IN\n"
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "</script>")
    int deleteByIdList(@Param("idList") List<String> idList, @Param("ea") String ea);

    @Select(
            "<script>"
                    + "select hexagon_site.* from hexagon_site\n"
                    + "left join object_group_relation on hexagon_site.id = object_group_relation.object_id and hexagon_site.ea = object_group_relation.ea\n"
                    + "left join object_top on hexagon_site.id = object_top.object_id and object_top.ea = hexagon_site.ea\n"
                    + "where "
                    // 下面两个 choose 必须放在where最前面 否则 druid 会拦截，除非配置去掉
                    + " <choose>"
                        + "<when test=\"param.strictCheckGroup == false\">"
                            + "  ( hexagon_site.create_by = #{param.userId} \n"
                            + " or object_group_relation.group_id is null "
                        + "</when>"
                        + "<otherwise>"
                            + " ( "
                        + "</otherwise>"
                    + "</choose>"

                    + " <choose>"
                        + "<when test=\"param.permissionGroupIdList != null and param.permissionGroupIdList.size != 0\">"
                            + "<if test=\"param.strictCheckGroup == false\">"
                            + " or "
                            + "</if>"
                            + " object_group_relation.group_id in "
                            + "<foreach collection = 'param.permissionGroupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                            + "#{param.permissionGroupIdList[${index}]}"
                            + "</foreach> ) "
                        + "</when>"
                        + "<otherwise>"
                            + " <choose>"
                                + "<when test=\"param.strictCheckGroup == false\">"
                                    + "  ) "
                                + "</when>"
                                + "<otherwise>"
                                    + " 1 = 1 ) "
                                + "</otherwise>"
                            + "</choose>"
                        + "</otherwise>"
                    + "</choose>"

                    + " and hexagon_site.ea = #{param.ea} and (is_system_site = false OR is_system_site is null) and hexagon_site.status NOT IN(4,5)\n"
                    + "<if test=\"param.keyword != null\">AND name LIKE CONCAT('%', #{param.keyword}, '%')\n</if>"
                    + "<if test=\"param.status != null\">AND status=#{param.status}\n</if>"
                    + "<if test=\"param.groupIdList != null and param.groupIdList.size != 0\">"
                    + " and object_group_relation.group_id in "
                    + "<foreach collection = 'param.groupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    + "#{param.groupIdList[${index}]}"
                    + "</foreach>"
                    + "</if>"

                    + "<if test = 'param.materialTagFilter != null'>" +
                        "<if test = 'param.materialTagFilter.materialTagIds != null and param.materialTagFilter.materialTagIds.size() > 0'>" +
                            "<if test = 'param.materialTagFilter.type == 1'>" +
                                " and exists (" +
                                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                                " and mtr.tag_id in " +
                                " <foreach open='(' close=')' separator=',' item='item' index='index' collection='param.materialTagFilter.materialTagIds'> " +
                                " #{param.materialTagFilter.materialTagIds[${index}]} " +
                                " </foreach>" +
                                ")" +
                            "</if>" +
                            "<if test = 'param.materialTagFilter.type == 2'>" +
                                " and exists (" +
                                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                                " group by object_id having ARRAY_AGG(tag_id) <![CDATA[ @> ]]> " +
                                " ARRAY<foreach open='[' close=']' separator=',' item='item' index='index' collection='param.materialTagFilter.materialTagIds'> #{param.materialTagFilter.materialTagIds[${index}]} </foreach>::varchar[] " +
                                ")" +
                            "</if>" +
                        "</if>" +

                        "<if test = 'param.materialTagFilter.menuMaterialTagIds != null and param.materialTagFilter.menuMaterialTagIds.size() > 0'>" +
                            "<if test = 'param.materialTagFilter.menuType == 1'>" +
                                " and exists (" +
                                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                                " and mtr.tag_id in " +
                                " <foreach open='(' close=')' separator=',' item='item' index='index' collection='param.materialTagFilter.menuMaterialTagIds'> " +
                                " #{param.materialTagFilter.menuMaterialTagIds[${index}]} " +
                                " </foreach>" +
                                ")" +
                            "</if>" +
                            "<if test = 'param.materialTagFilter.menuType == 2'>" +
                                " and exists (" +
                                " select 1 from material_tag_relation mtr where mtr.object_id = hexagon_site.id and mtr.object_type = 26 and mtr.ea = hexagon_site.ea " +
                                " group by object_id having ARRAY_AGG(tag_id) <![CDATA[ @> ]]> " +
                                " ARRAY<foreach open='[' close=']' separator=',' item='item' index='index' collection='param.materialTagFilter.menuMaterialTagIds'> #{param.materialTagFilter.menuMaterialTagIds[${index}]} </foreach>::varchar[] " +
                                ")" +
                            "</if>" +
                        "</if>"

                    + "</if>"

                    + "<if test=\"param.hasFormId == true\">  AND EXISTS (SELECT hexagon_site_id FROM hexagon_page LEFT JOIN cta_relation ON cta_relation.ea=hexagon_page.ea AND cta_relation.object_id=hexagon_page.id AND cta_relation.object_type=27 WHERE hexagon_site.ea = hexagon_page.ea AND hexagon_site.id = hexagon_page.hexagon_site_id AND hexagon_page.form_id IS NOT NULL AND hexagon_page.form_id != '' AND cta_relation.id IS NULL) \n</if>"

                    + "order by object_top.create_time desc nulls last, hexagon_site.create_time desc"
                    + "</script>"
    )
    List<HexagonSiteEntity> getAccessiblePage(@Param("param") HexagonSiteQueryParam param, @Param("page") Page page, @Param("ea") String ea);

    @Update("UPDATE hexagon_site SET is_system_site=#{status},create_by=#{creator}, update_time=now() WHERE ea=#{ea} AND id=#{objectId}")
    int updateHexagonSiteSystemStatusAndCreator(@Param("ea")String ea, @Param("objectId")String objectId, @Param("status")boolean status, @Param("creator")Integer creator);
    @Select("<script>"
            + "SELECT id AS objectId, out_display_name AS outDisplayName, 26 AS objectType, name AS title, create_time FROM hexagon_site WHERE ea=#{ea} AND status=1 \n"
            + "<if test=\"keyword != null\">" +
            " and name like CONCAT('%', #{keyword}, '%') " +
            " </if>"
            + "<if test=\"ids != null and ids.size()>0\">"
            + "  AND id IN\n"
            +   "<foreach collection = 'ids' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{ids[${num}]}"
            +   "</foreach>"
            + " ORDER BY array_positions(ARRAY"
            +   "<foreach collection = 'ids' item = 'item' index='num' open = '[' separator = ',' close = ']'>"
            +       "#{ids[${num}]}"
            +   "</foreach>"
            + " :: varchar[], id )"
            + "</if>"
            + "</script>")
    List<ContentCenterDataDTO> getListOrderByEaAndIds(@Param("ea")String ea, @Param("ids") List<String> ids, @Param("keyword")String keyword,@Param("page")Page page);

    @Delete("delete from hexagon_site WHERE id=#{id}")
    int deleteHexagonSiteById(@Param("id") String id, @Param("ea") String ea);

    @Update("UPDATE hexagon_site SET out_display_name=#{outDisplayName}, update_time=now() WHERE id=#{id} AND ea=#{ea}")
    int updateOutDisplayName(@Param("id") String id, @Param("outDisplayName") String outDisplayName, @Param("ea") String ea);
}
