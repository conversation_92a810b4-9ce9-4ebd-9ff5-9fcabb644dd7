package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.provider.entity.mail.MailAddressListEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * Created by z<PERSON>gh on 2020/7/2.
 */
public interface MailAddressListDAO {
    @Insert("INSERT INTO mail_address_list(id, ea, address, name, description, member_count, create_time, update_time) VALUES(#{entity.id},\n"
            + " #{entity.ea}, #{entity.address}, #{entity.name}, #{entity.description}, #{entity.memberCount}, #{entity.createTime}, #{entity.updateTime})")
    int insert(@Param("entity")MailAddressListEntity entity);

    @Update(" UPDATE mail_address_list SET member_count = #{memberCount} WHERE id = #{id} ")
    int updateMemberCount(@Param("memberCount") Integer memberCount, @Param("id") String id,@Param("ea")String ea);
}
