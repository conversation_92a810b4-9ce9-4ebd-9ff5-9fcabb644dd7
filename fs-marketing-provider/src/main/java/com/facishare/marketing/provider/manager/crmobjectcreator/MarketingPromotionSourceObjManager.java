/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.data.SpreadChannelOptionData;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.advertiser.headlines.HeadlinesLocalLeadModuleEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.HttpUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.advertiser.headlines.ad.GetHeadlinesLocalClueResult;
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDAO;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.QrCodeIdentifySpreadSourceRelationEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.innerData.KfCustomerEventContentData;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerResult.UtmMarketingEventResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.baidu.UtmDataManger;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.mq.consumer.handlers.OfficialWebsiteThirdPlateformEventHandler;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.util.ObjDescribeUtil;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.CreateObjectResult;
import com.fxiaoke.crmrestapi.result.FindLayoutResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.crmrestapi.service.ObjectLayoutService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.contstant.OperatorConstants.EQ;
import static com.facishare.marketing.common.contstant.OperatorConstants.IS;

@Component("marketingPromotionSourceObjManager")
@Slf4j
public class MarketingPromotionSourceObjManager {
    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectServiceManager objectServiceManager;

    @Autowired
    private ObjectLayoutService objectLayoutService;

    @Autowired
    private CrmDataAuthManager crmDataAuthManager;

    @Autowired
    private UtmDataManger utmDataManger;

    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;

    @Autowired
    private OfficialWebsiteDAO officialWebsiteDAO;

    @Autowired
    private OfficialWebsiteManager officialWebsiteManager;

    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;

    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @ReloadableProperty("host")
    private String host;

    private static final Set<String> CONSULT_SET = Sets.newHashSet("consult", "1", "LEADS_TYPE_ONLINE_CONSULT", "LEADS_TYPE_INTELLIGENT_TOOL");
    private static final Set<String> TEL_SET = Sets.newHashSet("tel", "2", "LEADS_TYPE_MAKE_PHONE_CALL", "LEADS_TYPE_PHONE");
    private static final Set<String> FORM_SET = Sets.newHashSet("form", "0", "LEADS_TYPE_FORM");
    private static final Set<String> LOTTERY_SET = Sets.newHashSet("lottery", "5", "LEADS_TYPE_LOTTERY");
    private static final Set<String> COUPON_SET = Sets.newHashSet("coupon", "4", "LEADS_TYPE_PROMOTION_COUPON");
    private static final Set<String> CALLBACK_SET = Sets.newHashSet("callback", "3");
    private static final Set<String> WECHAT_SET = Sets.newHashSet("wechat", "follow", "LEADS_TYPE_ONE_CLICK_AUTHORIZE", "LEADS_TYPE_PAGE_SCAN_CODE", "LEADS_TYPE_PROMOTION_FOLLOW", "LEADS_TYPE_APPLY_WECHAT_AUTH");


    public String getApiName() {
        return CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName();
    }

    public String getJsonData(int ei) {
        //return ObjDescribeUtil.getObjectDescribe("/object_describe/marketing_promotion_source_obj.json");
        String json = ObjDescribeUtil.getObjectDescribe("/object_describe/marketing_promotion_source_obj.json");
        JSONObject jsonObject = JSON.parseObject(json);
        JSONObject fieldJsonObject = jsonObject.getJSONObject("fields");
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if (getDescribeResultResult == null || !getDescribeResultResult.isSuccess() || getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
            fieldJsonObject.remove("member_id");
        }

        getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_KEYWORD.getName());
        if (getDescribeResultResult == null || !getDescribeResultResult.isSuccess() || getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
            fieldJsonObject.remove("marketing_keyword_id");
        }
        return jsonObject.toJSONString();
    }

    public String getJsonLayout(int ei) {
        boolean existMember = true;
        boolean existKeyword = true;
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if (getDescribeResultResult == null || !getDescribeResultResult.isSuccess() || getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
            existMember = false;
        }
        getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_KEYWORD.getName());
        if (getDescribeResultResult == null || !getDescribeResultResult.isSuccess() || getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
            existKeyword = false;
        }

        String layoutJson = ObjDescribeUtil.getObjectDescribe("/object_describe/marketing_promotion_source_obj_layout.json");
        JSONObject layoutJsonObject = JSON.parseObject(layoutJson);
        JSONArray components = layoutJsonObject.getJSONArray("components");
        boolean finalExistMember = existMember;
        boolean finalExistKeyword = existKeyword;
        for (Object component : components) {
            JSONObject componentJsonObject = (JSONObject) component;
            String apiName = componentJsonObject.getString("api_name");
            if ("form_component".equals(apiName)) {
                JSONArray fieldSectionArr = componentJsonObject.getJSONArray("field_section");
                for (Object fieldSectionObj : fieldSectionArr) {
                    JSONObject fieldSectionJSONObject = (JSONObject) fieldSectionObj;
                    if ("base_field_section__c".equals(fieldSectionJSONObject.getString("api_name"))) {
                        JSONArray formFields = fieldSectionJSONObject.getJSONArray("form_fields");
                        formFields.removeIf(e -> {
                            JSONObject fieldJsonObject = (JSONObject) e;
                            String fieldName = fieldJsonObject.getString("field_name");
                            return (fieldName.equals("marketing_keyword_id") && !finalExistMember) || (fieldName.equals("member_id") && !finalExistKeyword);
                        });

                    }
                }
            }
        }
        return layoutJsonObject.toJSONString();
        //return ObjDescribeUtil.getObjectDescribe("/object_describe/marketing_promotion_source_obj_layout.json");
    }

    public String getJsonListLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/marketing_promotion_source_obj_list_layout.json");
    }

    public void createObjDescAndAddObjField(String ea) {
        getOrCreateObjDescribe(ea);
        addMarketingPromotionSourceFiledToOtherObj(ea);
        updateEntityOpenness(ea);
    }

    private void updateEntityOpenness(String ea) {
        String apiName = CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName();
        crmDataAuthManager.updateEntityOpenness(ea, SuperUserConstants.USER_ID, apiName, "1", "0");
    }

    public ObjectDescribe getOrCreateObjDescribe(String ea) {
        // 这里正常的创建营销推广来源的描述
        int ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), this.getApiName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && !getDescribeResultResult.isSuccess()) {
            throw new BizException(getDescribeResultResult.getCode(), getDescribeResultResult.getMessage());
        } else if (getDescribeResultResult.isSuccess()) {
            return getDescribeResultResult.getData().getDescribe();
        }
        CreateObjectArg crmCreateObjectVO = new CreateObjectArg();
        crmCreateObjectVO.setActive(true);
        crmCreateObjectVO.setIncludeLayout(true);
        crmCreateObjectVO.setLayoutType("detail");
        // 这里的getJsonData有一些特殊逻辑，如果没有会员对象和营销关键词对象，会将会员字段和营销关键词去掉去掉
        crmCreateObjectVO.setJsonData(this.getJsonData(ei));
        // 这里的getJsonLayout有一些特殊逻辑，如果没有会员对象和营销关键词对象，会将会员字段和营销关键词去掉去掉
        crmCreateObjectVO.setJsonLayout(this.getJsonLayout(ei));
        crmCreateObjectVO.setJsonListLayout(this.getJsonListLayout());
        InnerResult<CreateObjectResult> createObjectResultInnerResult = objectServiceManager.createObject(ei, SuperUserConstants.USER_ID, crmCreateObjectVO);
        if (!createObjectResultInnerResult.isSuccess()) {
            throw new OuterServiceRuntimeException(createObjectResultInnerResult.getErrCode(), createObjectResultInnerResult.getErrMessage());
        }
        ObjectDescribe objectDescribe = createObjectResultInnerResult.getResult().getObjectDescribe();
        // 这里将线索的渠道字段也刷到营销推广来源的描述
        spreadChannelManager.addSpreadChannelToObj(ea, this.getApiName(), "spread_channel");
        return objectDescribe;
    }

    /**
     * 将营销推广来源字段加到企微客户、企微加好友记录对象
     */
    public void addMarketingPromotionSourceFieldToQywxObj(String ea) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            HeaderObj systemHeader = new HeaderObj(ei, -10000);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            String fieldName = "marketing_promotion_source_id";
            if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
                ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
                if (!objectDescribe.getFields().containsKey(fieldName)) {
                    AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                    arg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                    arg.setFieldDescribe("{\"describe_api_name\":\"WechatWorkExternalUserObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"营销推广来源\",\"target_api_name\":\"MarketingPromotionSourceObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"企微客户\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"marketing_promotion_source_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                    arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"WechatWorkExternalUserObj_default_layout__c\",\"label\":\"默认布局\",\"is_default\":true}]");
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                    log.info("addWechatWorkExternalUserObjFiled ea:{} result: {}", ea, result);
                }
            }

            objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
            if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
                ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
                if (!objectDescribe.getFields().containsKey(fieldName)) {
                    AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                    arg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                    arg.setFieldDescribe("{\"describe_api_name\":\"WechatFriendsRecordObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"营销推广来源\",\"target_api_name\":\"MarketingPromotionSourceObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"企微加好友记录\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"marketing_promotion_source_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                    //arg.setLayoutList("[{\"api_name\":\"WechatFriendsRecordObj_layout_generate_by_UDObjectServer__c\",\"label\":\"默认布局\",\"is_default\":true,\"render_type\":\"object_reference\",\"is_show\":true,\"is_required\":false,\"is_readonly\":true}]");
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                    log.info("addWechatFriendsRecordObjFiled ea:{} result: {}", ea, result);
                    // 企微好友记录的布局有点特殊，需要单独更新
                    updateWechatFriendRecordLayout(ea);
                }
            }
        } catch (Exception e) {
            log.error("addMarketingPromotionSourceFieldToQywxObj err, ea:[{}]", ea, e);
        }
    }

    public void addMemberField(String ea) {
        try {
            getOrCreateObjDescribe(ea);
            HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            String fieldName = "member_id";
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
                ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
                if (!objectDescribe.getFields().containsKey(fieldName)) {
                    AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                    arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
                    arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"会员\",\"target_api_name\":\"MemberObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"营销推广来源\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"member_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                    arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                    log.info("addMemberField result: {}", result);
                }
            }
        } catch (Exception e) {
            log.error("addMemberField error,ea:[{}]", ea, e);
        }
    }

    public void addMarketingKeywordField(String ea) {
        try {
            getOrCreateObjDescribe(ea);
            HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            String fieldName = "marketing_keyword_id";
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
                ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
                if (!objectDescribe.getFields().containsKey(fieldName)) {
                    AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                    arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
                    arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"会员\",\"target_api_name\":\"MarketingKeywordObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"营销推广来源\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"marketing_keyword_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                    arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                    log.info("addMarketingKeywordField result: {}", result);
                }
            }
        } catch (Exception e) {
            log.error("addMarketingKeywordField error,ea:[{}]", ea, e);
        }
    }

    /**
     * 将营销推广来源字段添加到 线索、微信用户、企微客户、活动成员、会员、企微加好友记录
     * 支持重复执行
     */
    public void addMarketingPromotionSourceFiledToOtherObj(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        String fieldName = "marketing_promotion_source_id";
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            if (!objectDescribe.getFields().containsKey(fieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"LeadsObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"营销推广来源\",\"target_api_name\":\"MarketingPromotionSourceObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"线索\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"marketing_promotion_source_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"LeadsObj_layout_generate_by_UDObjectServer__c\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("addLeadsObjFiled ea:{} result: {}", ea, result);
            }
        }

        objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT.getName());
        if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            if (!objectDescribe.getFields().containsKey(fieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"WechatFanObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"营销推广来源\",\"target_api_name\":\"MarketingPromotionSourceObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"微信粉丝\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"marketing_promotion_source_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"WechatFanObj_default_layout__c\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("addWechatFanObjFiled ea:{} result: {}", ea, result);
            }
        }


        objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            if (!objectDescribe.getFields().containsKey(fieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"CampaignMembersObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"营销推广来源\",\"target_api_name\":\"MarketingPromotionSourceObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"活动成员\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"marketing_promotion_source_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("addCampaignMembersObjFiled ea:{} result: {}", ea, result);
            }
        }

        objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            if (!objectDescribe.getFields().containsKey(fieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"MemberObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"营销推广来源\",\"target_api_name\":\"MarketingPromotionSourceObj\",\"target_related_list_name\":\"target_related_list_kTwKd__c\",\"target_related_list_label\":\"会员\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"marketing_promotion_source_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("addMemberObjFiled ea:{} result: {}", ea, result);
            }
        }
        // 处理企微好友、企微加好友记录
        addMarketingPromotionSourceFieldToQywxObj(ea);
    }

    public void updateWechatFriendRecordLayout(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        FindLayoutArg findLayoutArg = new FindLayoutArg();
        findLayoutArg.setApiName("layout_hfk1M");
        findLayoutArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
        InnerResult<FindLayoutResult> layoutResult = objectLayoutService.findLayout(systemHeader, findLayoutArg);
        if (layoutResult.isSuccess()) {
            LayoutDescribe layout = layoutResult.getResult().getLayout();
            JSONObject layoutObj = JSON.parseObject(JSON.toJSONString(layout));
            //layoutObj.getJSONArray("components").getJSONObject(0).getJSONArray("button_info").set(0, JSON.parseObject("{\"hidden\":[\"Add_button_default\",\"IntelligentForm_button_default\"],\"page_type\":\"list\",\"render_type\":\"list_normal\",\"order\":[\"Import_button_default\",\"Export_button_default\",\"ExportFile_button_default\"],\"exposed_button\":1}"));

            JSONArray components = layoutObj.getJSONArray("components");
            for (Object component : components) {
                JSONObject componentObj = (JSONObject) component;
                if (componentObj.getString("api_name").equals("form_component")) {
                    JSONArray fieldSectionArr = componentObj.getJSONArray("field_section");
                    for (Object fieldSection : fieldSectionArr) {
                        JSONObject fieldSectionObj = (JSONObject) fieldSection;
                        if (fieldSectionObj.getString("api_name").equals("base_field_section")) {
                            JSONArray formFields = fieldSectionObj.getJSONArray("form_fields");
                            boolean existPromotionSourceField = false;
                            for (Object formField : formFields) {
                                JSONObject formFieldObj = (JSONObject) formField;
                                if (formFieldObj.getString("field_name").equals("marketing_promotion_source_id")) {
                                    existPromotionSourceField = true;
                                    break;
                                }
                            }
                            if (!existPromotionSourceField) {
                                JSONObject addJSONObject = new JSONObject();
                                addJSONObject.put("is_readonly", false);
                                addJSONObject.put("is_required", false);
                                addJSONObject.put("render_type", "object_reference");
                                addJSONObject.put("field_name", "marketing_promotion_source_id");
                                formFields.add(addJSONObject);
                            }
                        }
                    }
                }
            }

            UpdateLayoutArg updateLayoutArg = new UpdateLayoutArg();
            updateLayoutArg.setLayout_data(JSON.toJSONString(layoutObj, (ValueFilter) (o, k, v) -> {
                if (v instanceof BigDecimal) {
                    return ((BigDecimal) v).intValue();
                } else {
                    return v;
                }
            }));
            updateLayoutArg.setSkip_validate(true);
            InnerResult<Map<String, Object>> result = objectLayoutService.updateLayout(systemHeader, updateLayoutArg);
            log.info("updateLayout WechatFriendsRecordObj layout result : {}", result);
        }
    }

    public String tryGetOrCreateObjByFormEnroll(CustomizeFormDataEnrollArg arg) {
        String id = tryCreateByQrCodeId(arg);
        if (StringUtils.isNotBlank(id)) {
            return id;
        }
        CustomizeFormDataUserEntity customizeFormDataUserEntity = BeanUtil.copy(arg, CustomizeFormDataUserEntity.class);
        customizeFormDataUserEntity.setSourceType(customizeFormDataManager.getEnrollType(customizeFormDataUserEntity));
        String channelValue = customizeFormDataManager.getSystemPromotionChannelType(customizeFormDataUserEntity);
        arg.setChannelValue(channelValue);

        MarketingPromotionSourceArg marketingPromotionSourceArg = BeanUtil.copy(arg, MarketingPromotionSourceArg.class);
        if(arg.getSubmitContent()!=null&&StringUtils.isNotBlank(arg.getSubmitContent().getUtmCampaig())){
            marketingPromotionSourceArg.setSyncUtmCampaign(true);
        }else {
            marketingPromotionSourceArg.setSyncUtmCampaign(false);
        }
        CustomizeFormDataEnroll customizeFormDataEnroll = arg.getSubmitContent();
        if (customizeFormDataEnroll != null) {
            marketingPromotionSourceArg.setUtmTerm(customizeFormDataEnroll.getUtmTerm());
            marketingPromotionSourceArg.setUtmContent(customizeFormDataEnroll.getUtmContent());
            marketingPromotionSourceArg.setUtmCampaign(customizeFormDataEnroll.getUtmCampaig());
            marketingPromotionSourceArg.setUtmSource(customizeFormDataEnroll.getUtmSource());
            marketingPromotionSourceArg.setUtmMedium(customizeFormDataEnroll.getUtmMedium());
            marketingPromotionSourceArg.setUnitId(customizeFormDataEnroll.getUnitId());
            marketingPromotionSourceArg.setKeywordId(customizeFormDataEnroll.getKeywordId());
            marketingPromotionSourceArg.setAccountId(customizeFormDataEnroll.getAccountId());
            marketingPromotionSourceArg.setHandleUtm(customizeFormDataEnroll.isForceSynUtm());
            if (StringUtils.isBlank(marketingPromotionSourceArg.getSourceUrl()) && StringUtils.isNotBlank(customizeFormDataEnroll.getMarketingSourceSite())) {
                marketingPromotionSourceArg.setSourceUrl(customizeFormDataEnroll.getMarketingSourceSite());
            }
        }
        marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
        marketingPromotionSourceArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule());
        return tryGetOrCreateObj(marketingPromotionSourceArg);
    }

    public String tryCreateByQrCodeId(CustomizeFormDataEnrollArg arg) {
        if (StringUtils.isBlank(arg.getQrCodeId()) || StringUtils.isBlank(arg.getQrCodeCategory())) {
           return null;
        }
        if (arg.getQrCodeCategory().equals(QrCodeCategoryEnum.QYWX.getType())) {
            QywxAddFanQrCodeEntity qywxAddFanQrCodeEntity = qywxAddFanQrCodeDAO.getById(arg.getQrCodeId());
            if (qywxAddFanQrCodeEntity == null || StringUtils.isBlank(qywxAddFanQrCodeEntity.getState())) {
                return null;
            }
            QrCodeIdentifySpreadSourceRelationEntity qrCodeIdentifySpreadSourceRelationEntity = marketingPromotionSourceArgObjectRelationManager.getByQrCodeIdentifyId(arg.getEa(), qywxAddFanQrCodeEntity.getState());
            if (qrCodeIdentifySpreadSourceRelationEntity == null || StringUtils.isBlank(qrCodeIdentifySpreadSourceRelationEntity.getPromotionSourceParam())) {
                return null;
            }
            if (arg.allFieldNull()) {
                return null;
            }
            MarketingPromotionSourceArg marketingPromotionSourceArg = JSONObject.parseObject(qrCodeIdentifySpreadSourceRelationEntity.getPromotionSourceParam(), MarketingPromotionSourceArg.class);
            marketingPromotionSourceArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.WECHAT.getLeadModule());
            marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.QYWX.getDataSource());
            CustomizeFormDataEnroll customizeFormDataEnroll = arg.getSubmitContent();
            // 把utm相关参数填充到表单中
            if (StringUtils.isBlank(customizeFormDataEnroll.getUnitId()) && StringUtils.isNotBlank(marketingPromotionSourceArg.getUnitId())) {
                customizeFormDataEnroll.setUnitId(marketingPromotionSourceArg.getUnitId());
            }
            if (StringUtils.isBlank(customizeFormDataEnroll.getAccountId()) && StringUtils.isBlank(marketingPromotionSourceArg.getAccountId())) {
                customizeFormDataEnroll.setAccountId(marketingPromotionSourceArg.getAccountId());
            }
            if (StringUtils.isBlank(customizeFormDataEnroll.getKeywordId()) && StringUtils.isBlank(marketingPromotionSourceArg.getKeywordId())) {
                customizeFormDataEnroll.setKeywordId(marketingPromotionSourceArg.getKeywordId());
            }
            if (StringUtils.isBlank(customizeFormDataEnroll.getUtmMedium()) && StringUtils.isNotBlank(marketingPromotionSourceArg.getUtmMedium())) {
                customizeFormDataEnroll.setUtmMedium(String.valueOf(marketingPromotionSourceArg.getUtmMedium()));
            }
            if (StringUtils.isBlank(customizeFormDataEnroll.getUtmSource()) && StringUtils.isNotBlank(marketingPromotionSourceArg.getUtmSource())) {
                customizeFormDataEnroll.setUtmSource(marketingPromotionSourceArg.getUtmSource());
            }
            if (StringUtils.isBlank(customizeFormDataEnroll.getUtmCampaig()) && StringUtils.isNotBlank(marketingPromotionSourceArg.getUtmCampaign())) {
                customizeFormDataEnroll.setUtmCampaig(marketingPromotionSourceArg.getUtmCampaign());
            }
            if (StringUtils.isBlank(customizeFormDataEnroll.getUtmContent()) && StringUtils.isNotBlank(marketingPromotionSourceArg.getUtmContent())) {
                customizeFormDataEnroll.setUtmContent(marketingPromotionSourceArg.getUtmContent());
            }
            if (StringUtils.isBlank(customizeFormDataEnroll.getUtmTerm()) && StringUtils.isNotBlank(marketingPromotionSourceArg.getUtmTerm())) {
                customizeFormDataEnroll.setUtmTerm(marketingPromotionSourceArg.getUtmTerm());
            }
            return tryGetOrCreateObj(marketingPromotionSourceArg);
        }
        // 还有公众号的需要处理
        return null;
    }

    public String tryGetOrCreateObjByKfCustomerEventContentData(KfCustomerEventContentData arg) {
        MarketingPromotionSourceArg marketingPromotionSourceArg = new MarketingPromotionSourceArg();
        marketingPromotionSourceArg.setEa(arg.getEa());
        marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.KF53.getDataSource());
        marketingPromotionSourceArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.KF53.getLeadModule());
        marketingPromotionSourceArg.setLandingPageUrl(arg.getLand_page());
        marketingPromotionSourceArg.setChannelValue(SystemPromotionChannelEnum.KF.getValue());
        marketingPromotionSourceArg.setThirdPlatformName(MarketingPromotionSourceLeadModuleEnum.KF53.getLeadModule());
        marketingPromotionSourceArg.setThirdPlatformDataId(arg.getGuest_id());
        marketingPromotionSourceArg.setSourceUrl(arg.getFrom_page());
        marketingPromotionSourceArg.setSourceUrlType(arg.getSourceName());
        marketingPromotionSourceArg.setConvertPageUrl(arg.getTalk_page());
        marketingPromotionSourceArg.setUtmTerm(arg.getUtmDataMap().get("utm_term"));
        marketingPromotionSourceArg.setUtmSource(arg.getUtmDataMap().get("utm_source"));
        marketingPromotionSourceArg.setUtmCampaign(arg.getUtmDataMap().get("utm_campaign"));
        marketingPromotionSourceArg.setUtmContent(arg.getUtmDataMap().get("utm_content"));
        if(arg.getUtmDataMap().containsKey("unitId") && StringUtils.isNumeric(arg.getUtmDataMap().get("unitId"))) {
            marketingPromotionSourceArg.setUnitId(arg.getUtmDataMap().get("unitId"));
        }
        if(arg.getUtmDataMap().containsKey("accountId") && StringUtils.isNumeric(arg.getUtmDataMap().get("accountId"))) {
            marketingPromotionSourceArg.setAccountId(arg.getUtmDataMap().get("accountId"));
        }
        if(arg.getUtmDataMap().containsKey("e_keywordid") && StringUtils.isNumeric(arg.getUtmDataMap().get("e_keywordid"))) {
            marketingPromotionSourceArg.setKeywordId(arg.getUtmDataMap().get("e_keywordid"));
        }
        return tryGetOrCreateObj(marketingPromotionSourceArg);
    }

    public String tryGetOrCreateObjByLocalLeadData(GetHeadlinesLocalClueResult.HeadlinesLocalClueResult arg, AdAccountEntity adAccountEntity, String marketingEventId) {
        MarketingPromotionSourceArg marketingPromotionSourceArg = new MarketingPromotionSourceArg();
        marketingPromotionSourceArg.setEa(adAccountEntity.getEa());
        marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
        marketingPromotionSourceArg.setLeadModule(getHeadlinesLocalLeadModule(arg.getClueType()));
        marketingPromotionSourceArg.setLandingPagePlatform(I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_285)); // 落地页平台
        marketingPromotionSourceArg.setThirdPlatformName(I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_284)); // 外部平台名称
        marketingPromotionSourceArg.setThirdPlatformDataId(arg.getClueId()); // 外部平台ID
        marketingPromotionSourceArg.setChannelValue(SystemPromotionChannelEnum.AD.getValue()); // 推广渠道
        marketingPromotionSourceArg.setMarketingEventId(marketingEventId); // 市场活动
        return tryGetOrCreateObj(marketingPromotionSourceArg);
    }

    private String getHeadlinesLocalLeadModule(String clueType) {
        if (StringUtils.isBlank(clueType)) {
            return null;
        }
        if (HeadlinesLocalLeadModuleEnum.FORM.getLeadModule().equals(clueType)) {
            return MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule();
        } else if (HeadlinesLocalLeadModuleEnum.CONSULT.getLeadModule().equals(clueType)){
            return MarketingPromotionSourceLeadModuleEnum.CONSULT.getLeadModule();
        } else if (HeadlinesLocalLeadModuleEnum.SMARTPHONE.getLeadModule().equals(clueType)) {
            return MarketingPromotionSourceLeadModuleEnum.TEL.getLeadModule();
        } else if (HeadlinesLocalLeadModuleEnum.GROUP_BUYING.getLeadModule().equals(clueType)) {
            return MarketingPromotionSourceLeadModuleEnum.OTHER.getLeadModule();
        }
        return null;
    }

    public String tryGetOrCreateObj(MarketingPromotionSourceArg arg) {
        try {
            if (arg.allFieldNull()) {
                return null;
            }

            Map<String, Object> crmObjectData = new HashMap<>();

            String ea = arg.getEa();

            if (arg.getSpreadFsUid() != null) {
                UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, arg.getSpreadFsUid());
                if (StringUtils.isNotBlank(userRelationEntity.getMemberId())) {
                    arg.setSpreadMemberId(userRelationEntity.getMemberId());
                }
            }

            if (StringUtils.isNotBlank(arg.getSpreadUserIdentifyId()) && arg.getSpreadUserType() != null) {
                String userMarketingId = userMarketingAccountRelationManager.getUserMarketingByOuterUserIdentifyId(arg.getEa(), arg.getSpreadUserIdentifyId(), arg.getSpreadUserType());
                if (userMarketingId != null) {
                    arg.setUserMarketingId(userMarketingId);
                }
                if (ChannelEnum.CRM_MEMBER.getType().equals(arg.getSpreadUserType()) && StringUtils.isBlank(arg.getSpreadMemberId())) {
                    arg.setSpreadMemberId(arg.getSpreadUserIdentifyId());
                }
            }
            // utm相关的东西再表单提交之后更新utm之后才处理，减少表单提交的同步逻辑
            if (arg.isHandleUtm() && (StringUtils.isNotBlank(arg.getUnitId()) || StringUtils.isNotBlank(arg.getUtmCampaign()))) {
                boolean syncUtmCampaign = utmDataManger.isSyncUtmCampaign(ea);
                AdObjectFieldMappingEntity mappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                Long accountId = StringUtils.isBlank(arg.getAccountId()) ? null : Long.parseLong(arg.getAccountId());
                Long unitId = StringUtils.isBlank(arg.getUnitId()) ? null : Long.parseLong(arg.getUnitId());
                AdSourceEnum adSourceEnum = AdSourceEnum.getBySource(arg.getUtmSource());
                String source = adSourceEnum == null ? null : adSourceEnum.getSource();
                Optional<UtmMarketingEventResult> marketingEventIdOptional = utmDataManger.getOrCreateMarketingEventObj(ea, accountId, unitId, arg.getUtmCampaign(), source, mappingEntity, syncUtmCampaign);
                if (marketingEventIdOptional.isPresent()) {
                    UtmMarketingEventResult utmMarketingEventResult = marketingEventIdOptional.get();
                    String marketingEventId = utmMarketingEventResult.getMarketingEventId();
                    String adName = utmMarketingEventResult.getAdName();
                    crmObjectData.put("marketing_event_id", marketingEventId);
                    arg.setMarketingEventId(marketingEventId);
                    if (StringUtils.isNotBlank(adName)) {
                        crmObjectData.put("utm_campaign", adName);
                    }
                }
            }
            if (arg.isHandleUtm()) {
                // 营销关键词ID
                ObjectData marketingKeywordObjectData = getMarketingKeywordId(arg);
                if (marketingKeywordObjectData != null) {
                    crmObjectData.put("utm_term", marketingKeywordObjectData.getName());
                    crmObjectData.put("marketing_keyword_id", marketingKeywordObjectData.getId());
                }
            }
// 不在查询了，产品说没次进来都是新的
//            String id = querySameMarketingPromotionSourceObjId(arg);
//            if (StringUtils.isNotBlank(id)) {
//                return id;
//            }
            // 落地页类型createOfficialWebsiteWxQrCode
            String landingPageValue = getLandingPageValue(arg);
            // 推广渠道
            String spreadChannel = getSpreadChannel(arg);

            // 推广伙伴客户ID(即互联企业的关联客户)
            String outAccountId = getOutAccountId(arg);
            // 伙伴的tenantId
            Integer outTenantId = getOutTenantId(arg);

            if (StringUtils.isNotBlank(arg.getObjectId())) {
                crmObjectData.put("landing_page_id", arg.getObjectId());
            }

            if (StringUtils.isNotBlank(landingPageValue)) {
                crmObjectData.put("landing_page_type", landingPageValue);
            }

            if (StringUtils.isNotBlank(arg.getMarketingActivityId())) {
                crmObjectData.put("marketing_activity_id", arg.getMarketingActivityId());
            }

            if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
                crmObjectData.put("marketing_event_id", arg.getMarketingEventId());
            }

            if (arg.getSpreadFsUid() != null && !QywxUserConstants.isVirtualUserId(arg.getSpreadFsUid())) {
                crmObjectData.put("spread_fs_uid", Lists.newArrayList(String.valueOf(arg.getSpreadFsUid())));
            }

            if (StringUtils.isNotBlank(outAccountId)) {
                crmObjectData.put("out_account_id", outAccountId);
            }

            if (outTenantId != null) {
                crmObjectData.put("out_tid", String.valueOf(outTenantId));
            }

            if (arg.getOuterUid() != null) {
                crmObjectData.put("out_uid", Lists.newArrayList(String.valueOf(arg.getOuterUid())));
            }

            if (StringUtils.isNotBlank(spreadChannel)) {
                if (spreadChannel.contains(":")) {
                    String[] split = spreadChannel.split(":");
                    if (split.length == 2) {
                        crmObjectData.put("spread_channel", split[0]);
                        crmObjectData.put("spread_channel__o", split[1]);
                    }
                } else {
                    crmObjectData.put("spread_channel", spreadChannel);
                }
            }

            if (StringUtils.isNotBlank(arg.getLandingPageUrl()) && HttpUtil.isUrl(arg.getLandingPageUrl())) {
                crmObjectData.put("landing_url", arg.getLandingPageUrl());
                String landingPagePlatform = getLandingPagePlatform(ea, arg.getLandingPageUrl());
                crmObjectData.put("landing_page_platform", landingPagePlatform);
            }

            if(MarketingPromotionDataFromEnum.KF53.getDataSource().equals(arg.getDataFrom())) {
                crmObjectData.put("landing_page_platform", MarketingPromotionSourceLandingpagePlatformEnum.OFFICIAL_WEBSITE.getPlatformValue());
            }

            if (StringUtils.isNotBlank(arg.getUtmMedium())) {
                crmObjectData.put("utm_medium", arg.getUtmMedium());
            }

            if (StringUtils.isNotBlank(arg.getUtmSource())) {
                crmObjectData.put("utm_source", arg.getUtmSource());
            }

            if (StringUtils.isNotBlank(arg.getUtmContent())) {
                crmObjectData.put("utm_content", arg.getUtmContent());
            }

            if (StringUtils.isNotBlank(arg.getUserMarketingId())) {
                crmObjectData.put("user_marketing_id", arg.getUserMarketingId());
            }

            if (StringUtils.isNotBlank(arg.getSourceUrl())) {
                crmObjectData.put("source_url", arg.getSourceUrl());
            }

            String sourceUrlType = getSourceUrlType(ea, arg.getSourceUrl());
            if (StringUtils.isNotBlank(sourceUrlType)) {
                crmObjectData.put("source_url_type", sourceUrlType);
            }

            if (StringUtils.isNotBlank(arg.getLandingPageName())) {
                crmObjectData.put("landing_page_name", arg.getLandingPageName());
            }

            if (StringUtils.isNotBlank(arg.getConvertPageName())) {
                crmObjectData.put("convert_page_name", arg.getConvertPageName());
            }

            if (StringUtils.isNotBlank(arg.getConvertPageUrl())) {
                crmObjectData.put("convert_page_url", arg.getConvertPageUrl());
            }

            if (StringUtils.isNotBlank(arg.getSpreadMemberId())) {
                crmObjectData.put("member_id", arg.getSpreadMemberId());
            }

            String qywxUserId = null;
            if (arg.getSpreadFsUid() != null) {
                QywxVirtualFsUserEntity entity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(arg.getEa(), arg.getSpreadFsUid());
                if (entity != null && StringUtils.isNotBlank(entity.getQyUserId())) {
                    qywxUserId = entity.getQyUserId();
                }
            }
            qywxUserId = StringUtils.isNotBlank(arg.getSpreadQywxUserId()) ? arg.getSpreadQywxUserId() : qywxUserId;
            if (StringUtils.isNotBlank(qywxUserId)) {
                crmObjectData.put("out_platform_uid", qywxUserId);
                String qywxUserName = qywxManager.getStaffNameByUserId(arg.getEa(), qywxUserId);
                if (StringUtils.isNotBlank(qywxUserName)) {
                    crmObjectData.put("out_platform_user_name", qywxUserName);
                }
            }

            if (StringUtils.isNotBlank(arg.getThirdPlatformName())) {
                crmObjectData.put("third_platform_name", arg.getThirdPlatformName());
            }

            if (StringUtils.isNotBlank(arg.getThirdPlatformDataId())) {
                crmObjectData.put("third_platform_data_id", arg.getThirdPlatformDataId());
            }

            if (StringUtils.isNotBlank(arg.getFsCTA())) {
                crmObjectData.put("cta", arg.getFsCTA());
            }
            String client = transferClient(arg.getClient());
            if (StringUtils.isNotBlank(client)) {
                crmObjectData.put("client", client);
            }

            if (StringUtils.isNotBlank(arg.getOperateSystem())) {
                crmObjectData.put("operate_system", arg.getOperateSystem());
            }

            if (StringUtils.isNotBlank(arg.getBrowser())) {
                crmObjectData.put("browser", arg.getBrowser());
            }

            if (StringUtils.isNotBlank(arg.getIpAddr())) {
                crmObjectData.put("ip_address", arg.getIpAddr());
            }
            
            if (StringUtils.isNotBlank(arg.getLeadModule())) {
                crmObjectData.put("lead_module", getLeadModule(arg.getLeadModule()));
            }

            if (StringUtils.isNotBlank(arg.getDataFrom())) {
                crmObjectData.put("data_from", arg.getDataFrom());
            }

            List<String> owner = Lists.newArrayList();
            if (arg.getSpreadFsUid() != null && !QywxUserConstants.isVirtualUserId(arg.getSpreadFsUid())) {
                owner.add(String.valueOf(arg.getSpreadFsUid()));
            } else if (arg.getFsUserId() != null) {
                owner.add(String.valueOf(arg.getFsUserId()));
            } else {
                owner.add(String.valueOf(SuperUserConstants.USER_ID));
            }
            crmObjectData.put("owner", owner);
            crmObjectData.put("created_by", owner);
            log.info("创建营销推广来源,ea:[{}], param:[{}]", ea, crmObjectData);
            Map<String, Object> result = crmMetadataManager.addMetadata(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), crmObjectData);
            return result.get("_id").toString();
        } catch (Exception e) {
            log.info("创建营销推广来源对象失败,arg:[{}]", arg, e);
        }
        return null;
    }

    private String getLeadModule(String leadModule) {
        if (StringUtils.isBlank(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.OTHER.getLeadModule();
        }
        if (CONSULT_SET.contains(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.CONSULT.getLeadModule();
        } else if (TEL_SET.contains(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.TEL.getLeadModule();
        } else if (FORM_SET.contains(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule();
        } else if (LOTTERY_SET.contains(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.LOTTERY.getLeadModule();
        } else if (COUPON_SET.contains(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.COUPON.getLeadModule();
        } else if (CALLBACK_SET.contains(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.CALLBACK.getLeadModule();
        } else if (WECHAT_SET.contains(leadModule)) {
            return MarketingPromotionSourceLeadModuleEnum.WECHAT.getLeadModule();
        } else {
            return MarketingPromotionSourceLeadModuleEnum.OTHER.getLeadModule();
        }
    }

    private String getLandingPagePlatform(String ea, String landingPageUrl) {
        if (landingPageUrl.contains("aisite.wejianzhan.com")) {
            return MarketingPromotionSourceLandingpagePlatformEnum.JI_MU_YU.getPlatformValue();
        } else if (landingPageUrl.contains("chengzijianzhan.com")) {
            return MarketingPromotionSourceLandingpagePlatformEnum.CHENG_ZI_JIAN_ZHAN.getPlatformValue();
        } else if (landingPageUrl.contains("qq.com")) {
            return MarketingPromotionSourceLandingpagePlatformEnum.TENCENT.getPlatformValue();
        } else if (officialWebsiteManager.isOfficialWebsite(ea, landingPageUrl)) {
            return MarketingPromotionSourceLandingpagePlatformEnum.OFFICIAL_WEBSITE.getPlatformValue();
        }
        try {
            landingPageUrl = landingPageUrl.replace("www.", "");
            URI uri = new URI(landingPageUrl);
            String landingPageHost = uri.getHost();
            if (host.contains(landingPageHost)) {
                return MarketingPromotionSourceLandingpagePlatformEnum.YXT.getPlatformValue();
            }
        } catch (URISyntaxException e) {
            log.warn("营销推广来源 getLandingPagePlatform error, ea: {} landingPageUrl: {}", ea, landingPageUrl, e);
        }
        return MarketingPromotionSourceLandingpagePlatformEnum.OTHER.getPlatformValue();
    }

    private String getSourceUrlType(String ea, String sourceUrl) {
        if (StringUtils.isBlank(sourceUrl)) {
            return null;
        }
        if (sourceUrl.contains("zhihu.com")) {
            return "知乎";
        } else if (sourceUrl.contains("weibo.cn")) {
            return "微博";
        } else if (sourceUrl.contains("weixin.qq.com")) {
            return "微信";
        } else if (sourceUrl.contains("news.163.com")) {
            return "网易新闻";
        } else if (sourceUrl.contains("ifeng.com")) {
            return "凤凰网";
        } else if (sourceUrl.contains("sohu.com")) {
            return "搜狐新闻";
        } else if (sourceUrl.contains("news.qq.com")) {
            return "腾讯新闻";
        } else if (sourceUrl.contains("toutiao.com")) {
            return "今日头条";
        } else if (sourceUrl.contains("sm.cn")) {
            return "神马";
        } else if (sourceUrl.contains("bing.com")) {
            return "必应";
        } else if (sourceUrl.contains("so.com")) {
            return "360搜索";
        } else if (sourceUrl.contains("sogou.com")) {
            return "搜狗";
        } else if (sourceUrl.contains("baidu.com") || sourceUrl.contains("weijianzhan")) {
            return "百度";
        }
        try {
            OfficialWebsiteEntity officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(ea);
            if (officialWebsiteEntity != null && StringUtils.isNotBlank(officialWebsiteEntity.getWebsiteUrl())) {
                String url = officialWebsiteEntity.getWebsiteUrl();
                url = OfficialWebsiteThirdPlateformEventHandler.getDomainHost(url);
                String pageDomain = OfficialWebsiteThirdPlateformEventHandler.getDomainHost(sourceUrl);
                if (url != null && url.equals(pageDomain)) {
                    return "官网";
                }
            }
        } catch (Exception e) {
            log.warn("营销推广来源 getSourceUrlType error, sourceUrl:[{}]", sourceUrl, e);
        }
        return null;
    }

    public String transferClient(Integer client) {
        if (client == null) {
            return "other";
        }
        switch (client) {
            case 0:
                return "miniApp";
            case 1:
                return "h5";
            case 2:
                return "web";
            case 3:
                return "app";
            default:
                return "other";
        }
    }

    public String querySameMarketingPromotionSourceObjId(MarketingPromotionSourceArg arg) {
        if (arg.allFieldNull()) {
            return null;
        }

        // 落地页类型
        String landingPageValue = getLandingPageValue(arg);
        // 推广渠道
        String spreadChannel = getSpreadChannel(arg);
        // 推广伙伴客户ID(即互联企业的关联客户)
        String outAccountId = getOutAccountId(arg);
        // 伙伴的tenantId
        Integer outTenantId = getOutTenantId(arg);

        List<PaasQueryArg.Condition> conditionList = Lists.newArrayList();

        if (StringUtils.isNotBlank(arg.getObjectId())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_page_id", Lists.newArrayList(arg.getObjectId()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_page_id", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(landingPageValue)) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_page_type", Lists.newArrayList(landingPageValue), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_page_type", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getMarketingActivityId())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("marketing_activity_id", Lists.newArrayList(arg.getMarketingActivityId()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("marketing_activity_id", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("marketing_event_id", Lists.newArrayList(arg.getMarketingEventId()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("marketing_event_id", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (arg.getSpreadFsUid() != null) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("spread_fs_uid", Lists.newArrayList(String.valueOf(arg.getSpreadFsUid())), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("spread_fs_uid", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(outAccountId)) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_account_id", Lists.newArrayList(outAccountId), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_account_id", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (outTenantId != null) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_tid", Lists.newArrayList(String.valueOf(outTenantId)), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_tid", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (arg.getOuterUid() != null) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_uid", Lists.newArrayList(String.valueOf(arg.getOuterUid())), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_uid", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(spreadChannel)) {
            if (spreadChannel.contains("other")) {
                spreadChannel = "other";
            }
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("spread_channel", Lists.newArrayList(spreadChannel), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("spread_channel", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getLandingPageUrl())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_url", Lists.newArrayList(arg.getLandingPageUrl()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_url", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getUtmMedium())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_medium", Lists.newArrayList(arg.getUtmMedium()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_medium", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getUtmSource())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_source", Lists.newArrayList(arg.getUtmSource()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_source", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getUtmCampaign())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_campaign", Lists.newArrayList(arg.getUtmCampaign()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_campaign", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getUtmContent())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_content", Lists.newArrayList(arg.getUtmContent()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_content", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getUtmTerm())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_term", Lists.newArrayList(arg.getUtmTerm()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("utm_term", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        String qywxUserId = null;
        if (arg.getSpreadFsUid() != null && QywxUserConstants.isVirtualUserId(arg.getSpreadFsUid())) {
            QywxVirtualFsUserEntity entity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(arg.getEa(), arg.getSpreadFsUid());
            if (entity != null && StringUtils.isNotBlank(entity.getQyUserId())) {
                qywxUserId = entity.getQyUserId();
            }
        }
        qywxUserId = StringUtils.isNotBlank(arg.getSpreadQywxUserId()) ? arg.getSpreadQywxUserId() : qywxUserId;
        if (StringUtils.isNotBlank(qywxUserId)) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_platform_uid", Lists.newArrayList(qywxUserId), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("out_platform_uid", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getSpreadMemberId())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("member_id", Lists.newArrayList(arg.getSpreadMemberId()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("member_id", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getUserMarketingId())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("user_marketing_id", Lists.newArrayList(arg.getUserMarketingId()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("user_marketing_id", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }
        // 只用查sourceUrl就好，sourceUrlType不用查
        if (StringUtils.isNotBlank(arg.getSourceUrl())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("source_url", Lists.newArrayList(arg.getSourceUrl()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("source_url", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getLandingPageName())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_page_name", Lists.newArrayList(arg.getLandingPageName()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("landing_page_name", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getConvertPageName())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("convert_page_name", Lists.newArrayList(arg.getConvertPageName()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("convert_page_name", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getConvertPageUrl())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("convert_page_url", Lists.newArrayList(arg.getConvertPageUrl()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("convert_page_url", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getThirdPlatformName())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("third_platform_name", Lists.newArrayList(arg.getThirdPlatformName()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("third_platform_name", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getThirdPlatformDataId())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("third_platform_data_id", Lists.newArrayList(arg.getThirdPlatformDataId()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("third_platform_data_id", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getFsCTA())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("cta", Lists.newArrayList(arg.getFsCTA()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("cta", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }
        String client = transferClient(arg.getClient());
        if (StringUtils.isNotBlank(client)) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("client", Lists.newArrayList(client), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("client", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getOperateSystem())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("operate_system", Lists.newArrayList(arg.getOperateSystem()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("operate_system", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getBrowser())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("browser", Lists.newArrayList(arg.getBrowser()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("browser", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getIpAddr())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("ip_address", Lists.newArrayList(arg.getIpAddr()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("ip_address", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getLeadModule())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("lead_module", Lists.newArrayList(arg.getLeadModule()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("lead_module", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        if (StringUtils.isNotBlank(arg.getDataFrom())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("data_from", Lists.newArrayList(arg.getDataFrom()), EQ);
            conditionList.add(condition);
        } else {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("data_from", Lists.newArrayList(""), IS);
            conditionList.add(condition);
        }

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.setFilters(conditionList);
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
        List<String> selectFields = Lists.newArrayList("_id");
        findByQueryV3Arg.setSelectFields(selectFields);
        try {
            InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(arg.getEa(), SuperUserConstants.USER_ID, findByQueryV3Arg);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                return objectDataInnerPage.getDataList().get(0).getId();
            }
        } catch (Exception e) {
            log.error("querySameMarketingPromotionSourceObjId error, arg[{}]", arg, e);
        }
        return null;
    }

    private String getLandingPageValue(MarketingPromotionSourceArg arg) {
        String landingPageValue = null;
        if (arg.getObjectType() != null) {
            int objectType = arg.getObjectType();
            if (objectType == ObjectTypeEnum.HEXAGON_PAGE.getType()) {
                landingPageValue = "hexagon";
            } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                landingPageValue = "article";
            } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                landingPageValue = "product";
            } else if (objectType == ObjectTypeEnum.OFFICIAL_WEBSITE.getType()) {
                landingPageValue = "official_web";
            } else if (objectType == ObjectTypeEnum.MAIL_TASK.getType()) {
                landingPageValue = "email";
            } else {
                landingPageValue = "other";
            }
        }
        return landingPageValue;
    }

    private String getSpreadChannel(MarketingPromotionSourceArg arg) {
        String channelValue = arg.getChannelValue();
        if (StringUtils.isBlank(channelValue)) {
            return null;
        }
        if (channelValue.contains("other")) {
            return channelValue;
        }
        if (SpreadChannelManager.promotionChannelMap.containsValue(channelValue)) {
            return channelValue;
        } else {
            Result<List<SpreadChannelOptionData>> result = spreadChannelManager.querySpreadChannelList(arg.getEa(), SuperUserConstants.USER_ID);
            if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                return "other";
            }
            List<SpreadChannelOptionData> spreadChannelOptionDataList = result.getData();
            return spreadChannelOptionDataList.stream().anyMatch(e -> e.getValue().equals(channelValue)) ? channelValue : "other";
        }
    }

    public String getOutAccountId(MarketingPromotionSourceArg arg) {
        if (StringUtils.isBlank(arg.getOuterTenantId())) {
            return null;
        }
        return customizeFormDataManager.getCustomerByEnterpriserelationId(arg.getEa(), arg.getOuterTenantId());
    }

    public Integer getOutTenantId(MarketingPromotionSourceArg arg) {
        if (StringUtils.isBlank(arg.getOuterTenantId())) {
            return null;
        }
        try {
            List<PaasQueryArg.Condition> conditionList = Lists.newArrayList();
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("_id", Lists.newArrayList(arg.getOuterTenantId()), EQ);
            conditionList.add(condition);
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.setFilters(conditionList);
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.ENTERPRISE_RELATION_OBJ.getName());
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            List<String> selectFields = Lists.newArrayList("enterprise_account");
            findByQueryV3Arg.setSelectFields(selectFields);
            InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(arg.getEa(), SuperUserConstants.USER_ID, findByQueryV3Arg);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                ObjectData objectData = objectDataInnerPage.getDataList().get(0);
                String ea = objectData.getString("enterprise_account");
                return eieaConverter.enterpriseAccountToId(ea);
            }
        } catch (Exception e) {
            log.info("营销推广详情获取outTenantId失败，arg:[{}]", arg, e);
        }
        return null;
    }

    public ObjectData getMarketingKeywordId(MarketingPromotionSourceArg arg) {
        if (StringUtils.isBlank(arg.getKeywordId()) && StringUtils.isBlank(arg.getUtmTerm())) {
            return null;
        }
        Long keywordId = StringUtils.isBlank(arg.getKeywordId()) ? null : Long.parseLong(arg.getKeywordId());
        Long accountId = StringUtils.isBlank(arg.getAccountId()) ? null : Long.parseLong(arg.getAccountId());

        return utmDataManger.getOrCreateKeywordObj(arg.getEa(), keywordId, arg.getUtmTerm(), arg.getMarketingEventId(), accountId);
    }

    public void updateUserMarketingIdByMergeEvent(String ea, String oldUserMarketingAccountId, String newUserMarketingAccountId) {
        int pageSize = 1;
        int maxTry = 10;
        int totalUpdateCount = 0;
        long beginTime = System.currentTimeMillis();
        int time = 0;
        while (true) {
            List<PaasQueryArg.Condition> conditionList = Lists.newArrayList();
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("user_marketing_id", Lists.newArrayList(oldUserMarketingAccountId), EQ);
            conditionList.add(condition);
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, pageSize);
            paasQueryArg.setFilters(conditionList);
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            List<String> selectFields = Lists.newArrayList("_id");
            findByQueryV3Arg.setSelectFields(selectFields);
            InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(ea, SuperUserConstants.USER_ID, findByQueryV3Arg);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList()) && time <= maxTry) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("user_marketing_id", newUserMarketingAccountId);
                for (ObjectData objectData : objectDataInnerPage.getDataList()) {
                    dataMap.put("_id", objectData.getId());
                    crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), dataMap);
                    totalUpdateCount++;
                }
            } else {
                break;
            }
            time++;
        }
        log.info("合并营销用户,oldId:[{}],newId:[{}],updateCount:[{}],耗时:[{}]", oldUserMarketingAccountId,
                newUserMarketingAccountId, totalUpdateCount, System.currentTimeMillis() - beginTime);
    }

    public void deleteSpreadChannel(String ea) {
        String channelValue = "chat_online";
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> oldDescribeResult =
                objectDescribeService.getDescribe(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());

        if (!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null) {
            log.error("delChannel failed get MarketingPromotionSourceObj describe failed");
            return;
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("spread_channel");
        if (fieldDescribe == null) {
            log.error("delChannel failed get MarketingPromotionSourceObj fieldDescribe null");
            return;
        }
        List<Map<String, Object>> options = (List<Map<String, Object>>) (fieldDescribe.get("options"));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(options)) {
            log.error("delChannel failed channel option null ea:{} channelList:{}");
            return;
        }
        Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(o -> o.toString()).collect(Collectors.toSet());
        if (!optionValues.contains(channelValue)) {
            log.info("MarketingPromotionSourceObj.delChannel failed channel not exist ea:{} channelValue:{}", ea, channelValue);
            return;
        }
        List<Map<String, Object>> newOptions = Lists.newArrayList();
        for (Map<String, Object> map : options) {
            if (!map.get("value").equals(channelValue)) {
                newOptions.add(map);
            }
        }
        fieldDescribe.put("options", newOptions);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                objectDescribeService.updateField(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), "spread_channel", fieldDescribe);
        if (!describeResult.isSuccess()) {
            log.info("delChannel delChannel failed ea:{} describeResult:{}", ea, describeResult);
            return;
        }
    }

    // 营销推广来源字段新增【外部平台ID名称】、【外部平台ID】
    public void addThirdPlatformField(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
        if (objectDescribeResult != null && objectDescribeResult.isSuccess()) {
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            String fieldName = "third_platform_name";
            if (!objectDescribe.getFields().containsKey(fieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"外部平台名称\",\"api_name\":\"third_platform_name\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"default_value\":\"\",\"is_show_mask\":false,\"pattern\":\"\",\"max_length\":100}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("add third_platform_name Filed ea:{} result: {}", ea, result);
            }
            fieldName = "third_platform_data_id";
            if (!objectDescribe.getFields().containsKey(fieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"外部平台ID\",\"api_name\":\"third_platform_data_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"default_value\":\"\",\"is_show_mask\":false,\"pattern\":\"\",\"max_length\":100}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("add third_platform_data_id Filed ea:{} result: {}", ea, result);
            }
        }
    }

    public void addCTAFieldAndUpdateFieldLabel(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            return;
        }
        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        if (!objectDescribe.getFields().containsKey("cta")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"CTA\",\"api_name\":\"cta\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"default_value\":\"\",\"is_show_mask\":false,\"pattern\":\"\",\"max_length\":100}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add CTA Filed ea:{} result: {}", ea, result);
        }
        FieldDescribe fieldDescribe = objectDescribe.getFields().get("landing_page_type");
        if (fieldDescribe != null) {
            String label = fieldDescribe.getLabel();
            if (!"内容类型".equals(label)) {
                fieldDescribe.put("label", "内容类型");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), this.getApiName(), "landing_page_type", fieldDescribe);
                log.info("更新 landing_page_type 的label, ea: {} result: {}", ea, describeResult);
            }
        }
        fieldDescribe = objectDescribe.getFields().get("landing_page_id");
        if (fieldDescribe != null) {
            String label = fieldDescribe.getLabel();
            if (!"内容ID".equals(label)) {
                fieldDescribe.put("label", "内容ID");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), this.getApiName(), "landing_page_id", fieldDescribe);
                log.info("更新 landing_page_id 的label, ea: {} result: {}", ea, describeResult);
            }
        }
    }

    public void addClientAndOtherField(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            return;
        }
        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        if (!objectDescribe.getFields().containsKey("client")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"客户端\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"client\",\"options\":[{\"label\":\"微信小程序\",\"value\":\"miniApp\"},{\"label\":\"h5\",\"value\":\"h5\"},{\"label\":\"web\",\"value\":\"web\"},{\"label\":\"app\",\"value\":\"app\"},{\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add client Filed ea:{} result: {}", ea, result);
        }

        if (!objectDescribe.getFields().containsKey("operate_system")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"操作系统\",\"api_name\":\"operate_system\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"default_value\":\"\",\"is_show_mask\":false,\"pattern\":\"\",\"max_length\":2000}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add operate system Filed ea:{} result: {}", ea, result);
        }

        if (!objectDescribe.getFields().containsKey("browser")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"浏览器\",\"api_name\":\"browser\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"default_value\":\"\",\"is_show_mask\":false,\"pattern\":\"\",\"max_length\":2000}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add browser Filed ea:{} result: {}", ea, result);
        }

        if (!objectDescribe.getFields().containsKey("ip_address")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"ip地址\",\"api_name\":\"ip_address\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"default_value\":\"\",\"is_show_mask\":false,\"pattern\":\"\",\"max_length\":2000}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add ip address Filed ea:{} result: {}", ea, result);
        }

        if (!objectDescribe.getFields().containsKey("source_url_type")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"来源网址类型\",\"api_name\":\"source_url_type\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"default_value\":\"\",\"is_show_mask\":false,\"pattern\":\"\",\"max_length\":2000}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add source url type Filed ea:{} result: {}", ea, result);
        }

    }

    public void addLandingPageAndOtherField(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
        if (objectDescribeResult == null ||  !objectDescribeResult.isSuccess()) {
            log.error("addLandingPageAndOtherField failed get MarketingPromotionSourceObj describe failed, ea: {}", ea);
            return;
        }
        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        String fieldName = "landing_page_platform";
        if (!objectDescribe.getFields().containsKey(fieldName)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"落地页平台\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"landing_page_platform\",\"options\":[{\"label\":\"基木鱼\",\"value\":\"jimuyu\"},{\"label\":\"橙子建站\",\"value\":\"chengzijianzhan\"},{\"label\":\"腾讯\",\"value\":\"tencent\"},{\"label\":\"营销通\",\"value\":\"yxt\"},{\"label\":\"官网\",\"value\":\"official_website\"},{\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add landing_page_platform Filed ea:{} result: {}", ea, result);
        }
        fieldName = "lead_module";
        if (!objectDescribe.getFields().containsKey(fieldName)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"留咨组件\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"lead_module\",\"options\":[{\"label\":\"咨询\",\"value\":\"consult\"},{\"label\":\"表单\",\"value\":\"form\"},{\"label\":\"电话\",\"value\":\"tel\"},{\"label\":\"抽奖\",\"value\":\"lottery\"},{\"label\":\"卡券\",\"value\":\"coupon\"},{\"label\":\"回呼\",\"value\":\"callback\"},{\"label\":\"微信\",\"value\":\"wechat\"},{\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add lead_module Filed ea:{} result: {}", ea, result);
        }

        if (!objectDescribe.getFields().containsKey("spread_type")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"quote_field_type\":\"select_one\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"label\":\"推广方式\",\"type\":\"quote\",\"quote_field\":\"marketing_activity_id__r.spread_type\",\"is_required\":false,\"api_name\":\"spread_type\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"quote\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add source url type Filed ea:{} result: {}", ea, result);
        }
        fieldName = "data_from";
        if (!objectDescribe.getFields().containsKey(fieldName)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingPromotionSourceObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"数据来源\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"data_from\",\"options\":[{\"label\":\"营销通\",\"value\":\"yxt\"},{\"label\":\"企业微信\",\"value\":\"qywx\"},{\"label\":\"公众号\",\"value\":\"official_account\"},{\"label\":\"纷享客服\",\"value\":\"fx_online_service\"},{\"label\":\"百度广告\",\"value\":\"baidu_ad\"},{\"label\":\"头条广告\",\"value\":\"headline_ad\"},{\"label\":\"腾讯广告\",\"value\":\"tencent_ad\"},{\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"MarketingPromotionSourceObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add data_from Filed ea:{} result: {}", ea, result);
        }
    }

    public ObjectData getById(String ea, String id) {
        return crmMetadataManager.getById(ea, SuperUserConstants.USER_ID, this.getApiName(), id);
    }

    public void updateMarketingPromotionSource(String ea, String leadId, String marketingEventId, String marketingKeywordId) {
        if (StringUtils.isBlank(leadId)) {
            return;
        }
        if (StringUtils.isBlank(marketingKeywordId) && StringUtils.isBlank(marketingEventId)) {
            return;
        }
        ObjectData leadObjectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CRM_LEAD.getName(), leadId);
        if (leadObjectData == null) {
            log.warn("updateMarketingPromotionSource get leadObjectData is null ea:{} leadId:{}", ea, leadId);
            return;
        }
        String marketingPromotionSourceId = leadObjectData.getString("marketing_promotion_source_id");
        if (StringUtils.isBlank(marketingPromotionSourceId)) {
            log.warn("updateMarketingPromotionSource lead marketing promotion source is null ea:{} leadId:{}", ea, leadId);
            return;
        }
        ObjectData marketingPromotionSourceObjectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), marketingPromotionSourceId);
        if (marketingPromotionSourceObjectData == null) {
            log.warn("updateMarketingPromotionSource get marketingPromotionSourceObjectData is null ea:{} leadId:{} marketingPromotionSourceId:{}", ea, leadId, marketingPromotionSourceId);
            return;
        }
        String existMarketingEventId = marketingPromotionSourceObjectData.getString("marketing_event_id");
        Map<String, Object> updateMap = new HashMap<>();

        if (StringUtils.isNotBlank(marketingEventId) && !marketingEventId.equals(existMarketingEventId)) {
            ObjectData marketingEventObjectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            if (marketingEventObjectData != null) {
                updateMap.put("marketing_event_id", marketingEventId);
                updateMap.put("utm_campaign", marketingEventObjectData.getName());
            }
        }
        String existMarketingKeywordId = marketingPromotionSourceObjectData.getString("marketing_keyword_id");
        if (StringUtils.isNotBlank(marketingKeywordId) && !marketingKeywordId.equals(existMarketingKeywordId)) {
            ObjectData marketingKeywordObjectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_KEYWORD.getName(), marketingKeywordId);
            if (marketingKeywordObjectData != null) {
                updateMap.put("marketing_keyword_id", marketingKeywordId);
                updateMap.put("utm_term", marketingKeywordObjectData.getName());
            }
        }
        if (!updateMap.isEmpty()) {
            updateMap.put("_id", marketingPromotionSourceId);
            com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> editResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), updateMap);
            log.info("updateMarketingPromotionSource ea: {} arg: {}, result: {}", ea, updateMap, editResult);
        }
    }

    public void updateLeadsModuleAndDatFromOptions(String ea) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            return;
        }
        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        FieldDescribe fieldDescribe = objectDescribe.getFields().get("lead_module");
        if (fieldDescribe != null) {
            List<Map<String, Object>> options = fieldDescribe.getOptions();
            if(options.stream().noneMatch(x -> x.containsValue(MarketingPromotionSourceLeadModuleEnum.KF53.getLeadModule()))) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", MarketingPromotionSourceLeadModuleEnum.KF53.getLeadModuleName());
                map.put("value", MarketingPromotionSourceLeadModuleEnum.KF53.getLeadModule());
                options.add(map);
                fieldDescribe.put("options", options);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), "lead_module", fieldDescribe);
                if (describeResult == null || !describeResult.isSuccess()) {
                    log.info("MarketingPromotionSourceObjManager.updateLeadsModuleAndDatFromOptions lead_module failed ea:{} describeResult:{}", ea, describeResult);
                }
            }
        }
        fieldDescribe = objectDescribe.getFields().get("data_from");
        if (fieldDescribe != null) {
            List<Map<String, Object>> options = fieldDescribe.getOptions();
            if(options.stream().noneMatch(x -> x.containsValue(MarketingPromotionDataFromEnum.KF53.getDataSource()))) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", MarketingPromotionDataFromEnum.KF53.getDataSourceName());
                map.put("value", MarketingPromotionDataFromEnum.KF53.getDataSource());
                options.add(map);
                fieldDescribe.put("options", options);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), "data_from", fieldDescribe);
                if (describeResult == null || !describeResult.isSuccess()) {
                    log.info("MarketingPromotionSourceObjManager.updateLeadsModuleAndDatFromOptions data_from failed ea:{} describeResult:{}", ea, describeResult);
                }
            }
        }
    }

}
