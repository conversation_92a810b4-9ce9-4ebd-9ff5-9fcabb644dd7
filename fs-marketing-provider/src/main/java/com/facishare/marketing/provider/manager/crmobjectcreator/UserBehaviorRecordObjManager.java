/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.crm.CrmFuncCodeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.marketing.provider.manager.SpreadChannelManager;
import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.util.ObjDescribeUtil;
import com.facishare.privilege.api.module.function.BatchAddFunctionVo;
import com.facishare.privilege.define.FuncType;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("userBehaviorRecordObjManager")
public class UserBehaviorRecordObjManager extends AbstractObjManager {

    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private MemberManager memberManager;

    @Override
    public String getApiName() {
        return CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName();
    }

    @Override
    public String getJsonData() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/user_behavior_record_data.json");
    }

    @Override
    public String getJsonLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/user_behavior_record_layout.json");
    }

    @Override
    public String getJsonListLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/user_behavior_record_list_layout.json");
    }

    @Override
    @FilterLog
    public ObjectDescribe getOrCreateObjDescribe(String ea) {
        ObjectDescribe objectDescribe = super.getOrCreateObjDescribe(ea);
        //将自定义渠道添加到互动渠道字段是行
        spreadChannelManager.addSpreadChannelToObj(ea, this.getApiName(), "spread_channel");
        // 吐血了，突然创建预设对象没有查看全部和编辑全部权限，这里手动加上去
        addViewAllAndEditAllFuncCode(ea);
        addFunctionPrivilegeToUserRole(ea);
        return objectDescribe;
    }

    public void addFunctionPrivilegeToUserRole(String ea) {
        // 市场管理员拥有全部权限
        List<String> resultList = Lists.newArrayList();
        resultList.add(this.getApiName());
        for (CrmFuncCodeEnum funcCodeEnum : CrmFuncCodeEnum.values()) {
            resultList.add(this.getApiName() + "||" + funcCodeEnum.getCode());
        }
        userRoleManager.configRoleObjectFunctionPrivilege(ea, SuperUserConstants.USER_ID, UserRoleManager.MARKETING_MANAGER_ROLE_CODE, resultList, null);

        resultList = Lists.newArrayList();
        resultList.add(this.getApiName());
        for (CrmFuncCodeEnum funcCodeEnum : CrmFuncCodeEnum.values()) {
            if (funcCodeEnum == CrmFuncCodeEnum.VIEW_ALL || funcCodeEnum == CrmFuncCodeEnum.EDIT_ALL) {
                continue;
            }
            resultList.add(this.getApiName() + "||" + funcCodeEnum.getCode());
        }
        userRoleManager.configRoleObjectFunctionPrivilege(ea, SuperUserConstants.USER_ID, UserRoleManager.MARKETING_USER_ROLE_CODE, resultList, null);
        userRoleManager.configRoleObjectFunctionPrivilege(ea, SuperUserConstants.USER_ID, UserRoleManager.SALE_USER_ROLE_CODE, resultList, null);

    }


    public void addViewAllAndEditAllFuncCode(String ea) {
        List<BatchAddFunctionVo.FunctionVo> functionList = Lists.newArrayList();

        BatchAddFunctionVo.FunctionVo viewAll = new BatchAddFunctionVo.FunctionVo();
        viewAll.setFuncCode(this.getApiName() + "||" + CrmFuncCodeEnum.VIEW_ALL.getCode());
        viewAll.setFuncName(CrmFuncCodeEnum.VIEW_ALL.getName());
        viewAll.setFuncType(FuncType.SYSTEM_DEFINED_FUNCTION_CODE_TYPE);
        functionList.add(viewAll);

        BatchAddFunctionVo.FunctionVo editAll = new BatchAddFunctionVo.FunctionVo();
        editAll.setFuncCode(this.getApiName() + "||" + CrmFuncCodeEnum.EDIT_ALL.getCode());
        editAll.setFuncName(CrmFuncCodeEnum.EDIT_ALL.getName());
        editAll.setFuncType(FuncType.SYSTEM_DEFINED_FUNCTION_CODE_TYPE);
        functionList.add(editAll);

        userRoleManager.batchAddSystemFuncCode(ea, SuperUserConstants.USER_ID, functionList);
    }

    public void tryAddSpreadMemberId(String ea) {
        try {
            if (!memberManager.isOpenMember(ea)) {
                log.info("tryAddSpreadMemberId, 该企业尚未开通会员模块, ea: {}", ea);
                return;
            }
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            HeaderObj systemHeader = new HeaderObj(ei, -10000);

            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
            if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
                log.warn("没有会员对象描述, ea: {}", ea);
                return ;
            }
            String fieldName = "spread_member_id";
            objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
            if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
                log.warn("没有用户行为记录对象描述, ea: {}", ea);
                return ;
            }
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            if (!objectDescribe.getFields().containsKey(fieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"UserBehaviorRecordsObj\",\"is_extend\":false,\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"推广会员\",\"target_api_name\":\"MemberObj\",\"target_related_list_name\":\"target_related_list_user_behavior__c\",\"target_related_list_label\":\"用户行为记录\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"spread_member_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"UserBehaviorRecordsObj_layout_default\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("addMemberField result: {}", result);
            }
        } catch (Exception e) {
            log.error("addMemberField error,ea:[{}]", ea, e);
        }
    }

    public void updateDescribeForCta(String ea) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            return;
        }
        HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        FieldDescribe fieldDescribe = objectDescribe.getFields().get("action_type");
        if (fieldDescribe != null) {
            List<Map<String, Object>> options = fieldDescribe.getOptions();
            if(options.stream().noneMatch(x -> x.containsValue("register_member"))) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "注册成为会员");
                map.put("value", "register_member");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "登录会员账号");
                map.put("value", "login_member_account");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "授权微信手机号");
                map.put("value", "authorize_wechat_phone");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "授权微信头像昵称");
                map.put("value", "authorize_wechat_nickname");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "触发CTA组件，展示引导留咨页");
                map.put("value", "trigger_cta_display_lead_generation_page");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "发送文件到邮箱");
                map.put("value", "send_file_mail");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "跳出网页");
                map.put("value", "close_website_page");
                options.add(map);
                fieldDescribe.put("options", options);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(systemHeader, CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName(), "action_type", fieldDescribe);
                if (describeResult == null || !describeResult.isSuccess()) {
                    log.error("UserBehaviorRecordObjManager.updateActionTypeOptions action_type failed ea:{} describeResult:{}", ea, describeResult);
                }
            }
        }
        if (!objectDescribe.getFields().containsKey("cta_id")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"UserBehaviorRecordsObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_extend\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"CTA组件ID\",\"api_name\":\"cta_id\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":false,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"UserBehaviorRecordsObj_layout_default\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addCtaIdField result: {}", result);
        }
        if (!objectDescribe.getFields().containsKey("cta_name")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"UserBehaviorRecordsObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_extend\":false,\"max_length\":500,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"CTA组件名称\",\"api_name\":\"cta_name\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":false,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"UserBehaviorRecordsObj_layout_default\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addCtaNameField result: {}", result);
        }
        if (!objectDescribe.getFields().containsKey("cta_object_type")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"UserBehaviorRecordsObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_extend\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"CTA关联对象类型\",\"api_name\":\"cta_object_type\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":false,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"UserBehaviorRecordsObj_layout_default\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addCtaObjectTypeField result: {}", result);
        }
        if (!objectDescribe.getFields().containsKey("cta_object_id")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"UserBehaviorRecordsObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_extend\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"CTA关联对象ID\",\"api_name\":\"cta_object_id\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":false,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"UserBehaviorRecordsObj_layout_default\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addCtaObjectIdField result: {}", result);
        }
        if (!objectDescribe.getFields().containsKey("cta_object_name")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"UserBehaviorRecordsObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_extend\":false,\"max_length\":500,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"CTA关联对象名称\",\"api_name\":\"cta_object_name\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":false,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"UserBehaviorRecordsObj_layout_default\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addCtaObjectNameField result: {}", result);
        }
        if (!objectDescribe.getFields().containsKey("website_url")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"UserBehaviorRecordsObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"^(((http[s]?|ftp):\\\\/\\\\/|www\\\\.)[a-z0-9\\\\.\\\\-]+\\\\.([a-z]{2,4})|((http[s]?|ftp):\\\\/\\\\/)?(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))(\\\\.(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))){3})(:\\\\d+)?(\\\\/[a-z0-9\\\\$\\\\^\\\\*\\\\+\\\\?\\\\(\\\\)\\\\{\\\\}\\\\.\\\\-_~!@#%&:;\\\\/=<>]*)?\",\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"访问网址\",\"type\":\"url\",\"is_required\":false,\"api_name\":\"website_url\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"is_extend\":false,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"url\",\"api_name\":\"UserBehaviorRecordsObj_layout_default\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addMemberField result: {}", result);
        }
        FieldDescribe objDescribe = objectDescribe.getFields().get("object_type");
        if (objDescribe != null) {
            List<Map<String, Object>> options = objDescribe.getOptions();
            if(options.stream().noneMatch(x -> x.containsValue("website_track"))) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "官网追踪页面");
                map.put("value", "website_track");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "微信用户");
                map.put("value", "wechat_fan");
                options.add(map);
                map = new HashMap<>();
                map.put("label", "会员");
                map.put("value", "member");
                options.add(map);
                objDescribe.put("options", options);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(systemHeader, CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName(), "object_type", objDescribe);
                if (describeResult == null || !describeResult.isSuccess()) {
                    log.error("UserBehaviorRecordObjManager.updateActionTypeOptions object_type failed ea:{} describeResult:{}", ea, describeResult);
                }
            }
        }
    }
}
