/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.conference;

import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.typehandlers.value.FileAttachmentContainer;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Sets;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.conference.SendEnrollMessageNoticeArg;
import com.facishare.mankeep.api.outService.service.OutConferenceService;
import com.facishare.marketing.api.UpdateSignInSuccessSettingArg;
import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.api.arg.sms.GroupSenderArg;
import com.facishare.marketing.api.result.CrmFieldResult;
import com.facishare.marketing.api.result.EnumDetailResult;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.conference.GetConferenceStatisticDataResult;
import com.facishare.marketing.api.result.conference.GetSignInSettingResult;
import com.facishare.marketing.api.result.conference.GetSimpleConferenceDetail;
import com.facishare.marketing.api.result.conference.QueryEnrollReviewResult;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.vo.conference.*;
import com.facishare.marketing.common.contstant.ConferenceReplaceConstants;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.campaign.CampaignConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataInviteStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.conference.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonTemplateTypeEnum;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.MwSendTaskTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SaveOrSendTypeEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.model.SmsParamObject;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.ActivityNotificationSettings;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.typehandlers.value.FieldInfo;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.manager.ConferenceDAOManager;
import com.facishare.marketing.provider.dao.manager.CustomizeFormDataDAOManager;
import com.facishare.marketing.provider.dao.manager.HexagonSiteDAOManager;
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.ticket.CustomizeTicketDAO;
import com.facishare.marketing.provider.dao.ticket.WxTicketReceiveDAO;
import com.facishare.marketing.provider.dto.campaignEnroll.BaseCampaignEnrollData;
import com.facishare.marketing.provider.dto.campaignMergeData.ActivityCustomizeFormDataUserDTO;
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignStatisticDTO;
import com.facishare.marketing.provider.dto.campaignMergeData.PageCampaignParticipantsDTO;
import com.facishare.marketing.provider.dto.conference.ConferenceEnrollBaseInfoDTO;
import com.facishare.marketing.provider.dto.conference.ConferenceInvitationUserDTO;
import com.facishare.marketing.provider.dto.conference.EnrollNoticeTaskDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.sms.ExtraSmsParamObject;
import com.facishare.marketing.provider.entity.ticket.CustomizeTicketReceiveEntity;
import com.facishare.marketing.provider.entity.ticket.WxTicketReceiveEntity;
import com.facishare.marketing.provider.innerResult.BatchShortUrlResult;
import com.facishare.marketing.provider.innerResult.UserRelationPartnerInfo;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.cusomerDev.sbt.SbtFormDataObject;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.image.ImageCreator;
import com.facishare.marketing.provider.manager.image.ImageDrawer;
import com.facishare.marketing.provider.manager.image.ImageDrawerTypeEnum;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.qywx.QywxMiniAppMessageManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.qywx.VirtualUserManager;
import com.facishare.marketing.provider.manager.sms.mw.SmsParamManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.mq.handler.dto.CrmEventDTO;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingEventRemoteManager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.BatchCreateShortUrlsArg;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.marketing.provider.util.NumberUtil;
import com.facishare.marketing.statistic.outapi.service.MarketingEventStatisticService;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.organization.adapter.api.business.service.OrganizationService;
import com.facishare.wechat.dubborestouterapi.arg.QueryStoreQrCodeArg;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import com.facishare.wechat.dubborestouterapi.service.union.WechatQrCodeRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.BulkDeleteArg;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.*;
import com.facishare.organization.adapter.api.business.util.FetchRangeMode;
import com.facishare.organization.adapter.api.business.util.FetchStatusMode;
import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.wechat.dubborestouterapi.arg.QueryStoreQrCodeArg;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import com.facishare.wechat.dubborestouterapi.service.union.WechatQrCodeRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.BulkDeleteArg;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.BulkDeleteResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import java.io.File;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Created by ranluch on 2019/7/25.
 */
@Component
@Slf4j
public class ConferenceManager {

    @Autowired
    private FileManager fileManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private FsMessageManager fsMessageManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private ConferenceUserGroupDAO conferenceUserGroupDAO;

    @Autowired
    private CustomizeTicketManager customizeTicketManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private QywxMiniAppMessageManager qywxMiniAppMessageManager;

    @Autowired
    private CustomizeTicketDAO customizeTicketDAO;

    @Autowired
    private WxTicketReceiveDAO wxTicketReceiveDAO;

    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private ImageCreator imageCreator;
    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private ConferenceReviewEmployeeDAO conferenceReviewEmployeeDAO;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private ConferenceInvitationUserDAO conferenceInvitationUserDAO;

    @Autowired
    private MarketingEventRemoteManager marketingEventRemoteManager;

    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @ReloadableProperty("default_conference_cover")
    private String defaultConferenceCover;

    @Autowired
    private MemberAccessibleCampaignDAO memberAccessibleCampaignDAO;

    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MemberManager memberManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private VirtualUserManager virtualUserManager;
    @Autowired
    private MarketingEventStatisticService marketingEventStatisticService;

    @Autowired
    private OrganizationService organizationService;


    // 提交报名短信通知模板
//    public static String ENROLL_SUCCESS_TEMPLATE_NAME_DEFAULT = "提交报名短信通知模板";
//
//    // 未开启审核、用户提交报名将发送此短信通知
//    public static String ENROLL_SUCCESS_TEMPLATE_TEXT_DEFAULT = "欢迎参加{title},您已成功报名本活动,会议时间:{startTime},会议地点:{location},参会码：{ticket.code}，点击查看电子票：{ticket.url}";
//
//    // 报名提交审核通知
//    public static String ENROLL_AUDIT_TEMPLATE_NAME_DEFAULT = "报名提交审核通知模板";
//    // 报名提交审核通知
//    public static String ENROLL_AUDIT_TEMPLATE_TEXT_DEFAULT = "感谢您报名参加{title},我们已收到您的报名信息,审核结果将以短信的形式进行通知,请耐心等候审核结果";
//
//    // 审核通过提醒
//    public static String ENROLL_AUDIT_SUCCESS_TEMPLATE_NAME_DEFAULT = "审核通过通知模板";
//
//    // 审核通过提醒
//    public static String ENROLL_AUDIT_SUCCESS_TEMPLATE_TEXT_DEFAULT = "恭喜您在{title}提交的报名资料已成功通过审核！会议时间:{startTime},会议地点:{location},参会码：{ticket.code}，点击查看电子票：{ticket.url}";
//
//    //审核未通过提醒
//    public static String ENROLL_AUDIT_FAIL_TEMPLATE_NAME_DEFAULT = "审核未通过通知模板";
//    //审核未通过提醒
//    public static String ENROLL_AUDIT_FAIL_TEMPLATE_TEXT_DEFAULT = "很抱歉,您提交的{title}报名未通过审核,感谢您报名参加本次会议";


    public static String ENROLL_SUCCESS_TEMPLATE_NAME_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLSUCCESSTEMPLATENAMEDEFAULT.getKey();
    public static String ENROLL_SUCCESS_TEMPLATE_TEXT_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLSUCCESSTEMPLATETEXTDEFAULT.getKey();
    public static String ENROLL_AUDIT_TEMPLATE_NAME_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLAUDITTEMPLATENAMEDEFAULT.getKey();
    public static String ENROLL_AUDIT_TEMPLATE_TEXT_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLAUDITTEMPLATETEXTDEFAULT.getKey();
    public static String ENROLL_AUDIT_SUCCESS_TEMPLATE_NAME_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLAUDITSUCCESSTEMPLATENAMEDEFAULT.getKey();
    public static String ENROLL_AUDIT_SUCCESS_TEMPLATE_TEXT_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLAUDITSUCCESSTEMPLATETEXTDEFAULT.getKey();
    public static String ENROLL_AUDIT_FAIL_TEMPLATE_NAME_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLAUDITFAILTEMPLATENAMEDEFAULT.getKey();
    public static String ENROLL_AUDIT_FAIL_TEMPLATE_TEXT_DEFAULT = I18nKeyStaticEnum.MARK_STATIC_ENROLLAUDITFAILTEMPLATETEXTDEFAULT.getKey();

    @Autowired
    private SmsParamManager smsParamManager;

    @Autowired
    private SendService sendService;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private ConferenceNotificationSettingDAO notificationSettingDAO;

    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private OutConferenceService outConferenceService;

    @Autowired
    private NoticeDAO noticeDAO;

    @Autowired
    private ConferenceDAOManager conferenceDAOManager;
    @Autowired
    private MarketingActivityExternalConfigDao externalConfigDao;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private ConferenceInviteParticipantDAO inviteParticipantDAO;

    @Autowired
    private NoticeManager noticeManager;

    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private ObjectDataService objectDataService;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;

    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;

    @Autowired
    private HexagonSiteManager hexagonSiteManager;

    @Autowired
    private MemberConfigDao memberConfigDao;

    @Autowired
    private CustomizeFormDataDAOManager customizeFormDataDAOManager;

    @Autowired
    private HexagonSiteDAOManager hexagonSiteDAOManager;
    @Autowired
    private ConferenceSignInJumpSettingDAO conferenceSignInJumpSettingDAO;
    @Autowired
    private ConferenceSignInSuccessSettingDAO conferenceSignInSuccessSettingDAO;
    @ReloadableProperty("conference.sms.signatureId")
    private String conferenceSmsSignatureId;

    @Value("${host}")
    private String host;

    @ReloadableProperty("conferenceTemplateSiteId")
    private String conferenceTemplateSiteId;

    @ReloadableProperty("conferenceTemplateIndexPageId")
    private String conferenceTemplateIndexPageId;

    @ReloadableProperty("conferenceTemplateFormId")
    private String conferenceTemplateFormId;

    @ReloadableProperty("conferenceTemplateFormPageId")
    private String conferenceTemplateFormPageId;

    @ReloadableProperty("conferenceTemplateSuccessPageId")
    private String conferenceTemplateSuccessPageId;

    @ReloadableProperty("conferenceSuccessIconPath")
    private String conferenceSuccessIconPath;

    private Gson gs = new Gson();

    @Autowired
    private SafetyManagementManager safetyManagementManager;

    @Autowired
    private ShortUrlManager shortUrlManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private WechatQrCodeRestService wechatQrCodeRestService;

    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private HexagonManager hexagonManager;

    @Autowired
    private UserRelationManager userRelationManager;

    public ActivityEntity createConference(CreateOrUpdateConferenceVO vo, String marketingEventId) {
        //会议基本信息
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, vo.getEa());
        if (conferenceEntity != null) {
            conferenceEntity.setStatus(ActivityStatusEnum.UNPUBLISHED.getStatus());
            conferenceEntity.setLocation(vo.getLocation());
            conferenceEntity.setCreateBy(vo.getFsUserId());
            conferenceEntity.setUpdateBy(vo.getFsUserId());
            conferenceEntity.setCreateTime(DateUtil.now());
            conferenceEntity.setUpdateTime(DateUtil.now());
            conferenceEntity.setType(ActivityTypeEnum.OFF_LING.type);
            conferenceEntity.setConferenceDetails(vo.getConferenceDetails());
            conferenceEntity.setMapAddress(vo.getMapAddress());
            conferenceEntity.setMapLocation(vo.getMapLocation());
            return conferenceEntity;
        }

        conferenceEntity = new ActivityEntity();
        conferenceEntity.setId(UUIDUtil.getUUID());
        conferenceEntity.setEa(vo.getEa());
        conferenceEntity.setMarketingEventId(marketingEventId);
        conferenceEntity.setTitle(vo.getTitle());
        conferenceEntity.setStartTime(new Date(vo.getStartTime()));
        conferenceEntity.setEndTime(new Date(vo.getEndTime()));
        conferenceEntity.setEnrollEndTime(new Date(vo.getEndTime()));
        conferenceEntity.setStatus(ActivityStatusEnum.UNPUBLISHED.getStatus());
        conferenceEntity.setLocation(vo.getLocation());
        conferenceEntity.setCreateBy(vo.getFsUserId());
        conferenceEntity.setUpdateBy(vo.getFsUserId());
        conferenceEntity.setCreateTime(DateUtil.now());
        conferenceEntity.setUpdateTime(DateUtil.now());
        conferenceEntity.setType(ActivityTypeEnum.OFF_LING.type);
        conferenceEntity.setConferenceDetails(vo.getConferenceDetails());
        conferenceEntity.setShowAcitivityList(vo.isShowActivityList());
        conferenceEntity.setMapAddress(vo.getMapAddress());
        conferenceEntity.setMapLocation(vo.getMapLocation());
        conferenceDAOManager.addConference(conferenceEntity);

        return conferenceEntity;
    }

    public Result<Void> addConferencePhoto(String conferenceId, String path, String ea, Integer fsUserId) {
        //处理图片
        String apath = null;
        String thumbnailApath = null;
        if (path.startsWith("TA_")) {
            try {
                FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(path, ea, fsUserId);
                if (null != fileManagerPicResult) {
                    apath = fileManagerPicResult.getUrlAPath();
                    thumbnailApath = fileManagerPicResult.getThumbUrlApath();
                }
            } catch (Exception e) {
                log.info("ConferenceServiceImpl updateConferenceDetail conferenceEntity UpdateCoverImage failed, conferenceId:{} path:{} ea:{} fsUserId:{}", conferenceId, path, ea, fsUserId);
                return Result.newError(SHErrorCode.CONFERENCE_UPDATE_IMAGE_FAIL);
            }
        } else if (path.startsWith("A_")) {
            // 默认图
            apath = fileV2Manager.getApathByApath(path, null, ea);
            thumbnailApath = apath;
        } else if (path.startsWith("C_")) {
            apath = path;
            thumbnailApath = apath;
        }
        if (StringUtils.isNotEmpty(apath) && StringUtils.isNotEmpty(thumbnailApath)) {
            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.ACTIVITY_COVER, conferenceId, apath, thumbnailApath);
        }

        if (StringUtils.isNotBlank(apath)) {
            //异步线程创建小程序封面
            asnyCreateMiniappCover(conferenceId, apath,ea);
        }

        return Result.newSuccess();
    }

    //异步线程创建小程序封面
    private void asnyCreateMiniappCover(String conferenceId, String apath, String ea){
        new Thread(
                () -> {
                    Map<String, Object> params = new HashMap<>();
                    params.put("coverApath", apath);
                    params.put("ea", ea);
                    ImageDrawer imageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
                    String cardPhotoApath = imageDrawer.draw(params);
                    if (StringUtils.isNotBlank(cardPhotoApath)) {
                        photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.MINI_COVER_ACTIVITY_NORMAL, conferenceId, cardPhotoApath, cardPhotoApath);
                    }
                }
        ).start();
    }

    /**
     * 同步CRM市场活动中会议数据到营销通会议
     *
     * @param ea
     * @param fsUserId
     * @param marketingEventId
     * @param objectData
     */

    public synchronized void syncConferenceDataFromCrm(String ea, Integer fsUserId, String marketingEventId, ObjectData objectData, String op, CrmEventDTO.Body body) {
        if (StringUtils.equals("i", op)) {
            addConferenceFromCrm(ea, fsUserId, marketingEventId, objectData);
        } else if (StringUtils.equals("u", op)) {
            // 处理活动类型转成会议的情况
            // 需求变动，暂时不用处理 handleMarketingEventTransformToConference(ea, marketingEventId, objectData, body);
            updateConfenrenceFromCrm(ea, fsUserId, marketingEventId, objectData);
        } else if (StringUtils.equals("d", op) || StringUtils.equals("invalid", op)) {
            deleteConfenrenceFromCrm(ea, fsUserId, marketingEventId);
        } else {
            log.info("syncConferenceDataFromCrm ignore op ea:{} marketingEventId:{} op:{}", ea, marketingEventId, op);
        }
    }

    public void handleMarketingEventTransformToConference(String ea, String marketingEventId, ObjectData objectData, CrmEventDTO.Body body) {
        // 需求变动，不在处理旧数据，代码暂时保留
        //        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
//        if (activityEntity == null) {
//            List<MarketingEventData> marketingEventDataList = MarketingEventData.wrap(Lists.newArrayList(objectData));
//            List<MarketingEventsBriefResult> marketingEventsBriefResultList = marketingEventManager.convert2MarketingEventsBriefResults(marketingEventDataList);
//            marketingEventManager.fillInOtherData(marketingEventsBriefResultList, Lists.newArrayList(objectData));
//            conferenceManager.initCrmOldConference(ea, marketingEventsBriefResultList);
//        }
//        activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
//        if (activityEntity == null) {
//            log.warn("handleConferenceEnrollData create activity error, ea: {} marketingEventId:{}", ea, marketingEventId);
//            return;
//        }
//        // 如果之前从非会议转成会议，需要处理报名数据、生成参会码
//        String beforeEventForm = body.getBeforeTriggerData() == null ? null : body.getBeforeTriggerData().getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName());
//        String afterEventForm = body.getAfterTriggerData() == null ? null : body.getAfterTriggerData().getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName());
//        if (StringUtils.isBlank(beforeEventForm) || StringUtils.isBlank(afterEventForm)) {
//            log.info("handleMarketingEventTransformToConference beforeEventForm or afterEventForm is null, ea:{} marketingEventId:{} beforeEventForm: {} afterEventForm: {}", ea, marketingEventId, beforeEventForm, afterEventForm);
//            return;
//        }
//        if (MarketingEventFormEnum.CONFERENCE_MARKETING.getValue().equals(afterEventForm) && !afterEventForm.equals(beforeEventForm)) {
//            int pageSize = 50;
//            List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.scanByMarketingEventId(ea, marketingEventId, null, pageSize);
//            while(CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
//                List<String> campaignIdList = campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
//                List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getByMarketingEventIdAndCampaignIdList(ea, marketingEventId, campaignIdList);
//                Map<String, CustomizeFormDataUserEntity> campaginIdToCustomizeFormDataMap = Maps.newHashMap();
//                if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
//                    customizeFormDataUserEntityList.stream().filter(e -> e.getSpreadFsUid() != null).forEach(e -> campaginIdToCustomizeFormDataMap.put(e.getCampaignId(), e));
//                }
//                for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
//                    log.info("handleMarketingEventTransformToConference create activity enroll, ea: {} campaignMergeDataEntity: {}", ea, campaignMergeDataEntity);
//                    CustomizeFormDataUserEntity customizeFormDataUserEntity = campaginIdToCustomizeFormDataMap.get(campaignMergeDataEntity.getId());
//                    Integer spreadFsUid =  customizeFormDataUserEntity == null ? null : customizeFormDataUserEntity.getSpreadFsUid();
//                    createConferenceTicketAndAttachedInfo(ea, ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getId(), spreadFsUid, ConferenceEnrollSourceTypeEnum.OTHER.getType(), campaignMergeDataEntity.getId(), marketingEventId);
//                }
//                String lastId = campaignMergeDataEntityList.get(campaignMergeDataEntityList.size() - 1).getId();
//                campaignMergeDataEntityList = campaignMergeDataDAO.scanByMarketingEventId(ea, marketingEventId, lastId, pageSize);
//            }
//        }
    }

    private void deleteConfenrenceFromCrm(String ea, Integer fsUserId, String marketingEventId) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity == null) {
            log.info("deleteConfenrenceFromCrm marketingEvent is not exist ea:{} marketingEventId:{}", ea, marketingEventId);
            return;
        }
        conferenceDAOManager.updateConferenceStatus(ea, activityEntity.getId(), ActivityStatusEnum.CRM_INVALID.getStatus(), fsUserId);
        log.info("conference is invalid from marketingEvent ea:{} conferenceId:{} marketingEventId:{}", ea, activityEntity.getId(), marketingEventId);
        //   customizeFormDataManager.unBindCustomizeFormDataObject(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), ea);
        /*
        if (isCanDeleted(activityEntity.getId())){
            if (activityEntity.getStatus() != ActivityStatusEnum.DELETED.getStatus()) {
                conferenceDAOManager.updateConferenceStatus(ea, activityEntity.getId(), ActivityStatusEnum.DELETED.getStatus(), fsUserId);
                customizeFormDataManager.unBindCustomizeFormDataObject(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), ea);
            }
        }
        */
    }

    private void updateConfenrenceFromCrm(String ea, Integer fsUserId, String marketingEventId, ObjectData objectData) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity == null) {
            log.info("updateConfenrenceFromCrm marketingEvent is not exist ea:{} marketingEventId:{}", ea, marketingEventId);
            return;
        }
        ActivityEntity updateActivityEntity = new ActivityEntity();
        updateActivityEntity.setId(activityEntity.getId());
        //同步参会地点
        if (ea.equals("sbtjt888")) {
            if (!StringUtils.equals(objectData.getString(SbtFormDataObject.MARKETING_EVEN_LOCATION), activityEntity.getLocation())) {
                updateActivityEntity.setLocation(objectData.getString(SbtFormDataObject.MARKETING_EVEN_LOCATION));
            }
        } else {
            if (!StringUtils.equals(objectData.getString(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName()), activityEntity.getLocation())) {
                updateActivityEntity.setLocation(objectData.getString(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName()));
            }
        }

        //同步标题
        if (!StringUtils.equals(objectData.getName(), activityEntity.getTitle())) {
            updateActivityEntity.setTitle(objectData.getName());
        }

        //同步参会时间
        //同步开始时间和结束时间，由于市场活动中开始时间和结束时间是日期字段，营销通中的开始日期和结束时间是日期时间字段，所以需要判断是否在同一天。防止同一天的更改无法同步到营销通。
        Long startTime = objectData.getLong(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName());
        Long endTime = objectData.getLong(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName());
        if (!marketingEventManager.isSameDay(activityEntity.getStartTime(), new Date(startTime))) {
            updateActivityEntity.setStartTime(new Date(startTime));
        }
        if (!marketingEventManager.isSameDay(activityEntity.getEndTime(), new Date(endTime))) {
            updateActivityEntity.setEndTime(new Date(endTime));
        }

        updateActivityEntity.setScale(activityEntity.getScale());
        conferenceDAOManager.updateConference(updateActivityEntity);
        conferenceManager.resetConferenceIndexPage(updateActivityEntity.getId(), false);
    }


    private void addConferenceFromCrm(String ea, Integer fsUserId, String marketingEventId, ObjectData objectData) {
        //让从web端创建会议的表单预设完成
        int retryTimes = 10;
        while (retryTimes-- > 0){
            if (conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea) != null) {
                log.info("addConferenceFromCrm marketingEvent is exist ea:{} marketingEventId:{}", ea, marketingEventId);
                return;
            }else {
                try {
                    Thread.sleep(300);
                } catch (InterruptedException e) {
                    log.info("addConferenceFromCrm sleep ea:{} fsUserId:{} marketingEventId:{} exception e:", ea, fsUserId, marketingEventId);
                }
            }
        }

        log.info("ConferenceServiceImpl addConference objectData={}", objectData);
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MARKETING_EVENT);
        ActivityEntity conferenceEntity = new ActivityEntity();
        conferenceEntity.setId(UUIDUtil.getUUID());
        conferenceEntity.setEa(ea);
        conferenceEntity.setMarketingEventId(marketingEventId);
        conferenceEntity.setTitle(objectData.getName());
        Long beginTime = objectData.getLong(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName());
        conferenceEntity.setStartTime(beginTime == null ? DateUtil.now() : DateUtil.fromTimestamp(beginTime));
        Long endTime = objectData.getLong(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName());
        conferenceEntity.setEndTime(endTime == null ? DateUtil.now() : DateUtil.fromTimestamp(endTime));
        conferenceEntity.setEnrollEndTime(endTime == null ? DateUtil.now() : DateUtil.fromTimestamp(endTime));
        conferenceEntity.setStatus(ActivityStatusEnum.ENABLED.getStatus());
        conferenceEntity.setLocation(objectData.getString(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName()));
        if (ea.equals("sbtjt888")) {
            conferenceEntity.setLocation(objectData.getString(SbtFormDataObject.MARKETING_EVEN_LOCATION));
        }
        conferenceEntity.setCreateBy(fsUserId);
        conferenceEntity.setUpdateBy(fsUserId);
        conferenceEntity.setCreateTime(DateUtil.now());
        conferenceEntity.setUpdateTime(DateUtil.now());
        conferenceEntity.setType(ActivityTypeEnum.OFF_LING.type);
        conferenceEntity.setShowAcitivityList(false);

        List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
        Optional<CrmFieldResult> crmFieldResultOptional = crmFieldVOS.stream().filter(data -> data.getFieldName().equals(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName())).findFirst();
        conferenceEntity.setMarketingEventType(getMarketingEventTypeDesc(crmFieldResultOptional, objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName())));
        String conferenceId = conferenceDAOManager.addConference(conferenceEntity);
        if (conferenceId == null) {
            log.info("ConferenceServiceImpl conferenceDAO.create conferenceEntity failed, conferenceEntity:{}", conferenceEntity);
            return;
        }

        // 创建默认会议站点
        conferenceManager.createConferenceSiteV2(conferenceEntity.getId(),null,marketingEventId,conferenceEntity.getTitle());
        ActivityEntity activityEntity = activityDAO.getById(conferenceId);
        if(activityEntity!=null&&StringUtils.isNotEmpty(activityEntity.getActivityDetailSiteId())){
            String eventLandingPage = host + "/proj/page/marketing-page?" + "marketingEventId=" + marketingEventId + "&ea=" + ea + "&objectType=26&id=" + activityEntity.getActivityDetailSiteId()
                    +"&targetObjectType=13&targetObjectId=" + conferenceId + "&type=1";
            crmV2Manager.updateMarketingEvenObjLandingPage(ea,  marketingEventId, fsUserId,eventLandingPage);
        }

        // 将会议绑定在市场活动下
        ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(conferenceEntity.getEa());
        entity.setObjectId(conferenceEntity.getId());
        entity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
        entity.setMarketingEventId(conferenceEntity.getMarketingEventId());
        contentMarketingEventMaterialRelationDAO.save(entity);

        // 创建二维码
        ThreadPoolUtils.executeWithTraceContext(() -> {
            activityManager.createActivityQrCode(conferenceId, conferenceEntity.getEa(), null, null);
            activityManager.createActivitySignInQrCode(conferenceId, conferenceEntity.getEa(), null);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    /**
     * 营销通会议数据同步到CRM市场活动会议
     *
     * @param ea
     * @param marketingEventId
     * @param vo
     */
    public com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> updateConferenceToCrmMarketingEvent(String ea, Integer fsUserId, String marketingEventId, CreateOrUpdateConferenceVO vo) {
        ActionEditArg actionEditArg = new ActionEditArg();
        Map<String, Object> objectMap = new HashMap<>();
        if(Objects.nonNull(vo.getCreateObjectDataModel())){
            objectMap = ObjectData.convert(vo.getCreateObjectDataModel().getObjectData());
        }
        objectMap.put("object_describe_api_name", CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        objectMap.put("object_describe_id", CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        objectMap.put(CrmV2MarketingEventFieldEnum.ID.getFieldName(), marketingEventId);
        objectMap.put(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), vo.getTitle());
        objectMap.put(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName(), vo.getEventType());
        objectMap.put(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName(), MarketingEventFormEnum.CONFERENCE_MARKETING.getValue());
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        objectMap.put("tenant_id", tenantId);
        if (ea.equals("sbtjt888")) {
            objectMap.put(SbtFormDataObject.MARKETING_EVEN_LOCATION, vo.getLocation());
        } else {
            objectMap.put(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName(), vo.getLocation());
        }
        objectMap.put(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName(), vo.getStartTime());
        objectMap.put(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName(), vo.getEndTime());
        if(!Strings.isNullOrEmpty(vo.getCoverImagePath())){
            try {
                List<Map<String, Object>> headImage = new ArrayList<>(1);
                String npath = fileV2Manager.getNpathByApath(vo.getCoverImagePath(), vo.getEa());
                if (StringUtils.isNotBlank(npath)) {
                    Map<String, Object> fileMap = new HashMap<>();
                    fileMap.put("ext", "jpg");
                    fileMap.put("path", npath);
                    headImage.add(fileMap);
                    objectMap.put("cover", headImage);
                }
            } catch (Exception e) {
                log.warn("upload head exception ,ea={},apath={}",vo.getEa(),vo.getCoverImagePath(),e);
            }
        }

        actionEditArg.setObjectData(ObjectData.convert(objectMap));
        HeaderObj systemHeader = new HeaderObj(tenantId, fsUserId);
        return metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), true, true, actionEditArg);
    }

    private String getMarketingEventTypeDesc(Optional<CrmFieldResult> crmFieldResultOptional, String eventType) {
        if (crmFieldResultOptional.isPresent()) {
            // 从描述中获取枚举对应的枚举值
            CrmFieldResult crmFieldResult = crmFieldResultOptional.get();
            List<EnumDetailResult> enumDetailResults = crmFieldResult.getEnumDetails();
            for (EnumDetailResult enumDetailResult : enumDetailResults) {
                if (enumDetailResult.getItemCode().equals(eventType)) {
                    return enumDetailResult.getItemName();
                }
            }
        }
        return null;
    }

    public void sendConferenceNotification(int notificationType, List<String> phoneList, ActivityEntity activityEntity, Map<String, ExtraSmsParamObject> extraSmsParamObjectMap) {
        if (CollectionUtils.isEmpty(phoneList) || activityEntity == null) {
            log.info("ConferenceManager sendConferenceNotification failed, notificationType={}, phoneList={}, activityEntity={}", notificationType, phoneList, activityEntity);
            return;
        }
        ConferenceNotificationSettingEntity notificationSettingEntity = notificationSettingDAO.getNotificationSettingByEa(activityEntity.getEa());
        if (notificationSettingEntity == null || notificationSettingEntity.getActivityNotificationSettings() == null) {
            log.info("ConferenceManager sendConferenceNotification failed notificationSettingEntity is null, notificationType={}, phoneList={}, activityEntity={}", notificationType, phoneList, activityEntity);
            return;
        }
        ActivityNotificationSettings notificationSettings = notificationSettingEntity.getActivityNotificationSettings();
        if (notificationSettings.getSmsType() == null) {
            notificationSettings.setSmsType(ConferenceSmsType.FS.getType());
        }
        if (extraSmsParamObjectMap == null) {
            extraSmsParamObjectMap = Maps.newHashMap();
        }
        String templateId = null;
        switch (ConferenceNotificationTypeEnum.fromType(notificationType)) {
            case ENROLL_SUCCESS:
                templateId = notificationSettings.getEnrollSuccess() != null && notificationSettings.getEnrollSuccess() ? notificationSettings.getEnrollSuccessTemplateId() : null;
                break;
            case ENROLL_AUDIT:
                templateId = notificationSettings.getEnrollAudit() != null && notificationSettings.getEnrollAudit() ? notificationSettings.getEnrollAuditTemplateId() : null;
                break;
            case ENROLL_AUDIT_SUCCESS:
                templateId = notificationSettings.getEnrollAuditSuccess() != null && notificationSettings.getEnrollAuditSuccess() ? notificationSettings.getEnrollAuditSuccessTemplateId() : null;
                break;
            case ENROLL_AUDIT_FAIL:
                templateId = notificationSettings.getEnrollAuditFail() != null && notificationSettings.getEnrollAuditFail() ? notificationSettings.getEnrollAuditFailTemplateId() : null;
                break;
            default:
        }

        if (StringUtils.isBlank(templateId)) {
            return;
        }

        String signatureId = null;
        if (notificationSettings.getSmsType().equals(ConferenceSmsType.FS.getType())) {
            signatureId = conferenceSmsSignatureId;
        }

        Map<String, List<SmsParamObject>> paramObjectMap = Maps.newHashMap();
        for (String phone : phoneList) {
            ExtraSmsParamObject extraSmsParamObject = extraSmsParamObjectMap.get(phone);
            if (extraSmsParamObject != null) {
                paramObjectMap.put(phone, Lists.newArrayList(activityEntity, extraSmsParamObject));
            } else {
                paramObjectMap.put(phone, Lists.newArrayList(activityEntity));
            }
        }

        List<PhoneContentResult> phoneContentResults = smsParamManager.buildParamValueMap(paramObjectMap);

        GroupSenderArg groupSenderArg = new GroupSenderArg();
        groupSenderArg.setChannelType(ChannelTypeEnum.CONFERENCE_NOTIFICATION.getType());
        groupSenderArg.setEventType(SaveOrSendTypeEnum.SEND.getType());
        groupSenderArg.setType(MwSendTaskTypeEnum.IMMEDIATELY_SEND.getType());
        groupSenderArg.setEa(activityEntity.getEa());
        groupSenderArg.setUserId(-5000);
        groupSenderArg.setTemplateId(templateId);
        groupSenderArg.setPhones(phoneContentResults);
        groupSenderArg.setSignatureId(signatureId);
        groupSenderArg.setBusinessType(ChannelTypeEnum.CONFERENCE_NOTIFICATION.getName());
        sendService.sendGroupSms(groupSenderArg);
    }

    public void sendNotificationByEnrollIds(String activityId, List<String> conferenceEnrollIds, Integer reviewStatus, String reviewFailedMsg) {
        if (StringUtils.isBlank(activityId) || CollectionUtils.isEmpty(conferenceEnrollIds) || reviewStatus == null) {
            log.info("ConferenceManager sendNotificationByEnrollIds failed, activityId={}, conferenceEnrollIds={}, reviewStatus={}", activityId, conferenceEnrollIds, reviewStatus);
            return;
        }
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(activityId);
        if (activityEntity == null) {
            log.info("ConferenceManager sendNotificationByEnrollIds activityEntity is null, activityId={}, conferenceEnrollIds={}, reviewStatus={}", activityId, conferenceEnrollIds, reviewStatus);
            return;
        }
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByConferenceEnrollIdsAndReviewStatus(conferenceEnrollIds, reviewStatus);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            log.warn("ConferenceManager.sendNotificationByEnrollIds campaignMergeDataEntityList is null activityId={}, conferenceEnrollIds={}, reviewStatus={}", activityId, conferenceEnrollIds, reviewStatus);
            return;
        }
        int notificationType;
        SendEnrollMessageNoticeArg noticeArg = new SendEnrollMessageNoticeArg();
        Map<String, ExtraSmsParamObject> extraSmsParamObjectMap = Maps.newHashMap();
        if (reviewStatus.equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus())) {
            notificationType = ConferenceNotificationTypeEnum.ENROLL_AUDIT_SUCCESS.getType();
            noticeArg.setReviewResult(true);
            List<String> longUrls = new ArrayList<>();
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                if (StringUtils.isNotBlank(campaignMergeDataEntity.getPhone())) {
                    String ticketUrl =host + ConferenceParamEnum.buildConferenceTicketUrl(host, activityEntity.getEa(), campaignMergeDataEntity.getId());
                    longUrls.add(ticketUrl);
                }
            }
            Map<String, String> shortUrlMapping = new HashMap<>();
            List<List<String>> partition = Lists.partition(new ArrayList<>(longUrls), 2000);
            for (List<String> item : partition) {
                BatchCreateShortUrlsArg arg = new BatchCreateShortUrlsArg();
                arg.setUrls(item);
                BatchShortUrlResult batchCreateShortUrl = shortUrlManager.batchCreateShortUrl(arg);
                if (batchCreateShortUrl != null && batchCreateShortUrl.getShortUrlMapping() != null) {
                    Map<String, String> LongUrl2ShortUrl = batchCreateShortUrl.getShortUrlMapping();
                    shortUrlMapping.putAll(LongUrl2ShortUrl);
                }
            }
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
//                buildTicketParam(activityId, activityEntity.getEa(), campaignMergeDataEntity, extraSmsParamObjectMap);
                if (StringUtils.isNotBlank(campaignMergeDataEntity.getPhone())) {
                    Long ticketCode = customizeTicketManager
                            .createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityId, campaignMergeDataEntity.getId(), activityEntity.getEa(), null);
                    ExtraSmsParamObject extraSmsParam = new ExtraSmsParamObject();
                    extraSmsParam.addParamValue("ticket.code", ticketCode == null ? "" : ticketCode + "");
                    if (ticketCode != null) {
                        String ticketUrl = ConferenceParamEnum.buildConferenceTicketUrl(host, campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getId());
                        extraSmsParam.addParamValue("ticket.url", shortUrlMapping.get(ticketUrl));
                    } else {
                        extraSmsParam.addParamValue("ticket.url", "");
                    }
                    extraSmsParamObjectMap.put(campaignMergeDataEntity.getPhone(), extraSmsParam);
                }
            }
        } else {
            notificationType = ConferenceNotificationTypeEnum.ENROLL_AUDIT_FAIL.getType();
            noticeArg.setReviewResult(false);
            noticeArg.setRemark(reviewFailedMsg);
        }

        List<String> phoneList = campaignMergeDataEntityList.stream().filter(data -> StringUtils.isNotBlank(data.getPhone())).map(CampaignMergeDataEntity::getPhone).collect(Collectors.toList());

       /* List<String> uids = formDataUserEntities.stream().filter(dataUserEntity -> StringUtils.isNotBlank(dataUserEntity.getUid()))
                .map(CustomizeFormDataUserEntity::getUid).collect(Collectors.toList());*/

        List<ActivityCustomizeFormDataUserDTO> activityCustomizeFormDataUserDTOList = campaignMergeDataDAO.queryLatestFormDataUserByActivityEnrollId(conferenceEnrollIds);
        List<String> formUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(activityCustomizeFormDataUserDTOList)) {
            formUserIds = activityCustomizeFormDataUserDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getUid())).map(CustomizeFormDataUserEntity::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(phoneList)) {
            sendConferenceNotification(notificationType, phoneList, activityEntity, extraSmsParamObjectMap);
        }
        if (CollectionUtils.isNotEmpty(formUserIds)) {
            noticeArg.setActivityId(activityId);
            //noticeArg.setUids(uids);
            noticeArg.setFormUserId(formUserIds);
            outConferenceService.sendEnrollMessageNotice(noticeArg);
        }
    }

    public void buildTicketParam(String activityId, String ea, CampaignMergeDataEntity campaignMergeDataEntity, Map<String, ExtraSmsParamObject> extraSmsParamObjectMap) {
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getPhone())) {
            Long ticketCode = customizeTicketManager
                    .createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityId, campaignMergeDataEntity.getId(), ea, null);
            ExtraSmsParamObject extraSmsParam = new ExtraSmsParamObject();
            extraSmsParam.addParamValue("ticket.code", ticketCode == null ? "" : ticketCode + "");
            if (ticketCode != null) {
                String ticketUrl = ConferenceParamEnum.buildConferenceTicketUrl(host, ea, campaignMergeDataEntity.getId());
                extraSmsParam.addParamValue("ticket.url", smsParamManager.getShortUrl(ticketUrl));
            } else {
                extraSmsParam.addParamValue("ticket.url", "");
            }
            extraSmsParamObjectMap.put(campaignMergeDataEntity.getPhone(), extraSmsParam);
        }
    }

    public Optional<Map<String, Map<String, String>>> getEnrollParamToWechatServiceTemplate(String ea, List<String> campaignIds, String marketingEventId) {
        if (CollectionUtils.isEmpty(campaignIds) || StringUtils.isEmpty(ea)) {
            return Optional.empty();
        }
        ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(campaignIds.get(0));
        if (activityEnrollDataEntity == null) {
            log.warn("ConferenceManager.getEnrollParamToWechatServiceTemplate activityEnrollDataEntity is NULL ea:{}, conferenceEnrollIds:{}", ea, campaignIds);
            return Optional.empty();
        }
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            log.warn("ConferenceManager.getEnrollParamToWechatServiceTemplate campaignMergeDataEntityList is empty ea:{} conferenceEnrollIds:{}", ea, campaignIds);
            return Optional.empty();
        }
        ActivityEntity activityEntity = null;
        ConferenceEnrollBaseInfoDTO conferenceEnrollBaseInfoDTO = null;
        if (StringUtils.isNotBlank(marketingEventId)) {
            activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
            if (activityEntity != null) {
                conferenceEnrollBaseInfoDTO = new ConferenceEnrollBaseInfoDTO();
                conferenceEnrollBaseInfoDTO.conversionByActivity(activityEntity);
                conferenceEnrollBaseInfoDTO.setActivityUrl(
                        host + "/ec/cml-marketing/release/web/cml-marketing.html?conferenceId=" + conferenceEnrollBaseInfoDTO.getActivityId() + "&marketingEventId=" + conferenceEnrollBaseInfoDTO
                                .getMarketingEventId() + "&byshare=1&_hash=/cml/h5/conference_detail");
            }
        }
        Map<String, Map<String, String>> campaignId2ParamMap = new HashMap<>();
        for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
            Map<String, String> paraMap = new HashMap<>();
            Long ticketCode = customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEnrollDataEntity.getActivityId(), campaignMergeDataEntity.getId(), ea, null);
            paraMap.put("{参会码}", ticketCode == null ? "" : ticketCode + "");
            String ticketUrl = ConferenceParamEnum.buildConferenceTicketUrl(host, ea, campaignMergeDataEntity.getId());
            paraMap.put("{门票URL}", ticketUrl);
            paraMap.put("{电话}", campaignMergeDataEntity.getPhone());
            paraMap.put("{姓名}", campaignMergeDataEntity.getName());
            if (conferenceEnrollBaseInfoDTO != null) {
                paraMap.putAll(ConferenceParamEnum.conversionBaseParamByObject(conferenceEnrollBaseInfoDTO, true));
            }
            campaignId2ParamMap.put(campaignMergeDataEntity.getId(), paraMap);
        }
        return Optional.of(campaignId2ParamMap);
    }

    public List<PhoneContentResult> buildPhoneContentEnrollList(List<String> conferenceEnrollIds, boolean isExistsVars) {
        List<PhoneContentResult> phoneContentResults = Lists.newArrayList();
        if (CollectionUtils.isEmpty(conferenceEnrollIds)) {
            return phoneContentResults;
        }
        ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataDAO.getEntityById(conferenceEnrollIds.get(0));
        if (activityEnrollDataEntity == null) {
            return phoneContentResults;
        }

        ActivityEntity activityEntity = conferenceDAO.getConferenceById(activityEnrollDataEntity.getActivityId());
        if (activityEntity == null) {
            return phoneContentResults;
        }

        List<String> campaignMergeDataIds = campaignMergeDataManager.activityEnrollIdToCampaignId(conferenceEnrollIds);

        if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
            return phoneContentResults;
        }

        List<CrmUserDefineFieldVo> allObjectFieldDescribesList = crmV2Manager.getAllObjectFieldDescribesList(activityEntity.getEa(), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignMergeDataIds);
        List<String> campaingIds = campaignMergeDataEntityList.stream().filter(campaignMergeDataEntity -> StringUtils.isNotBlank(campaignMergeDataEntity.getPhone()) && StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())).map(CampaignMergeDataEntity::getCampaignMembersObjId).collect(Collectors.toList());;
        Map<String, ObjectData> campaignMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(campaingIds)) {
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(200);
            searchQuery.setOffset(0);
            searchQuery.addFilter(CrmV2LeadFieldEnum.ID.getNewFieldName(), campaingIds, OperatorConstants.IN);
            Page<ObjectData>  pageDataLists = crmV2Manager.getList(activityEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), searchQuery);
            if (pageDataLists != null && CollectionUtils.isNotEmpty(pageDataLists.getDataList())){
                campaignMap = pageDataLists.getDataList().stream().collect(Collectors.toMap(ObjectData::getId, Function.identity(), (v1, v2)->v1));
            }
        }
        List<String> longUrls = new ArrayList<>();
        for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
            if (StringUtils.isBlank(campaignMergeDataEntity.getPhone())) {
                continue;
            }
            String ticketUrl = ConferenceParamEnum.buildConferenceTicketUrl(host, campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getId());
            longUrls.add(ticketUrl);
            String activityUrl = host + "/ec/cml-marketing/release/web/cml-marketing.html?conferenceId=" + activityEntity.getId() + "&marketingEventId=" + activityEntity.getMarketingEventId() + "&byshare=1&_hash=/cml/h5/conference_detail";
            longUrls.add(activityUrl);
        }
        Map<String, String> shortUrlMapping = new HashMap<>();
        List<List<String>> partition = Lists.partition(new ArrayList<>(longUrls), 2000);
        for (List<String> item : partition) {
            BatchCreateShortUrlsArg arg = new BatchCreateShortUrlsArg();
            arg.setUrls(item);
            BatchShortUrlResult batchCreateShortUrl = shortUrlManager.batchCreateShortUrl(arg);
            if (batchCreateShortUrl != null && batchCreateShortUrl.getShortUrlMapping() != null) {
                Map<String, String> LongUrl2ShortUrl = batchCreateShortUrl.getShortUrlMapping();
                shortUrlMapping.putAll(LongUrl2ShortUrl);
            }
        }
        for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
            Map<String, List<SmsParamObject>> paramObjectMap = Maps.newHashMap();
            if (StringUtils.isBlank(campaignMergeDataEntity.getPhone())) {
                continue;
            }
            if (isExistsVars) {
                Long ticketCode = customizeTicketManager
                        .createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEnrollDataEntity.getActivityId(), campaignMergeDataEntity.getId(), activityEntity.getEa(), null);
                ExtraSmsParamObject extraSmsParam = new ExtraSmsParamObject();
                extraSmsParam.addParamValue("ticket.code", ticketCode == null ? "" : ticketCode + "");
                if (ticketCode != null) {
                    String ticketUrl = ConferenceParamEnum.buildConferenceTicketUrl(host, campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getId());
                    extraSmsParam.addParamValue("ticket.url", shortUrlMapping.get(ticketUrl));
                } else {
                    extraSmsParam.addParamValue("ticket.url", "");
                }
                String activityUrl = host + "/ec/cml-marketing/release/web/cml-marketing.html?conferenceId=" + activityEntity.getId() + "&marketingEventId=" + activityEntity.getMarketingEventId() + "&byshare=1&_hash=/cml/h5/conference_detail";
                extraSmsParam.addParamValue("activity.url", shortUrlMapping.get(activityUrl));
                paramObjectMap.put(campaignMergeDataEntity.getPhone(), Lists.newArrayList(activityEntity, campaignMergeDataEntity, extraSmsParam));
                List<PhoneContentResult> tmpPhoneContentResults = smsParamManager.buildParamValueMap(paramObjectMap);

                // 添加活动成员参数
                Map<String, String> detail = null;
                String campaignMembersObjId = campaignMergeDataEntity.getCampaignMembersObjId();
                if (campaignMembersObjId != null){
                    ObjectData objectData = campaignMap.get(campaignMembersObjId);
                    detail = crmV2Manager.objectDataTransaction(activityEntity.getEa(), -10000, allObjectFieldDescribesList, objectData, false).get("en");
                }
                if (detail != null) {
                    HashMap<String, String> stringVHashMap = new HashMap<>();
                    detail.forEach((k,v)->{
                        if (v != null) {
                            stringVHashMap.put(k, String.valueOf(v));
                        } else {
                            stringVHashMap.put(k, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                        }
                    });
                    tmpPhoneContentResults.get(0).getParamMap().putAll(stringVHashMap);
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Map<String, String> specialParams = new HashMap<>();
                specialParams.put("activity.name", activityEntity.getTitle());
                if (activityEntity.getStartTime() != null) {
                    specialParams.put("activity.startTime", sdf.format(activityEntity.getStartTime()));
                }
                if (activityEntity.getEndTime() != null) {
                    specialParams.put("activity.endTime", sdf.format(activityEntity.getEndTime()));
                }
                tmpPhoneContentResults.get(0).getParamMap().putAll(specialParams);

                phoneContentResults.addAll(tmpPhoneContentResults);
            } else {
                // 不存在变量，只组装手机号即可
                phoneContentResults.add(new PhoneContentResult(campaignMergeDataEntity.getPhone(), Maps.newHashMap()));
            }
        }

        return phoneContentResults;
    }


    /**
     * 获取会议详情内容path
     *
     * @param content
     * @return
     */
    public String getConferencePath(String content) {
        String fileName = System.currentTimeMillis() + "_" + UUIDUtil.getUUID() + WebCrawlerUtil.EXT;
        File file = fileManager.savaFile(fileName, content, WebCrawlerUtil.FORMAT);
        byte[] bytes = fileManager.file2Bytes(file);
        file.delete();
        return fileManager.uploadFileToAWarehouse(WebCrawlerUtil.EXT, bytes);
    }

    /**
     * 根据会议详情path获取会议详情
     *
     * @param path
     * @return
     */
    public String getConferenceDetailsByPath(String path) {
        byte[] bytes = fileV2Manager.downloadAFile(path, null);
        return bytes == null ? "" : new String(bytes);
    }

    public ExportEnrollsDataResult buildExportParticipantsData(QueryConferenceParticipantsVO vo) {
        ExportEnrollsDataResult result = new ExportEnrollsDataResult();
        try {
            ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
            if (activityEntity == null) {
                return result;
            }
            String groupUserId = null;
            if (CollectionUtils.isNotEmpty(vo.getGroupUserId())) {
                groupUserId = vo.getGroupUserId().stream().collect(Collectors.joining(","));
            }
            // 查询物料对应表单
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(vo.getEa(), vo.getConferenceId(), ObjectTypeEnum.ACTIVITY.getType());
            result.setFileName(objectManager.getObjectName(vo.getConferenceId(), ObjectTypeEnum.ACTIVITY.getType()));
            result.setTitleList(generateExcelTitleList(customizeFormDataEntity));
            List<PageCampaignParticipantsDTO> campaignParticipantsDTOList = campaignMergeDataDAO.queryCampaignParticipantsData(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getInviteStatus(), vo.getSignStatus(), vo.getKeyword(), vo.getFilterPhoneUser(), vo.getSaveCrmStatus(), vo.getReviewStatus(), vo.getChannelValue(), groupUserId);
            if (CollectionUtils.isNotEmpty(campaignParticipantsDTOList)) {
                result.setEnrollInfoList(generateExcelParticipantsList(vo.getConferenceId(), activityEntity.getMarketingEventId(), activityEntity.getEa(), campaignParticipantsDTOList, customizeFormDataEntity));
            } else {
                result.setEnrollInfoList(Lists.newArrayList());
            }
        } catch (Exception e) {
            log.warn("ConferenceManager.buildExportParticipantsData error e: {}", e);
        }
        return result;
    }

    private List<String> generateExcelTitleList(CustomizeFormDataEntity customizeFormDataEntity) {
        List<String> titleList = new ArrayList<>();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1071));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1072));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1073));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1074));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1075));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1076));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1077));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1078));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1079));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1080));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1081));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1082));
        if (customizeFormDataEntity != null) {
            for (FieldInfo fieldInfo : customizeFormDataEntity.getFormBodySetting()) {
                titleList.add(fieldInfo.getLabel());
            }
        }
        return titleList;
    }

    private List<List<Object>> generateExcelParticipantsList(String conferenceId, String marketingEventId, String ea, List<PageCampaignParticipantsDTO> campaignParticipantsDTOList, CustomizeFormDataEntity customizeFormDataEntity) {
        List<List<Object>> participantsList = Lists.newArrayList();
        List<String> campaignMembersObjIds = campaignParticipantsDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).map(
                PageCampaignParticipantsDTO::getCampaignMembersObjId).collect(
                Collectors.toList());

        // 查询姓名（兼容表单无名称，对象有名称场景）
        List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(ea, marketingEventId, campaignMembersObjIds);
        Map<String, String> ownerNameMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(ea, objectDataList);
        Map<String, ObjectData> objectDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectDataList)){
            objectDataMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, data -> data, (v1, v2) -> v1));
        }
        List<String> campaignMergeDataIds = campaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getId).collect(Collectors.toList());
        // 查询邀约人
        List<ConferenceInvitationUserDTO> conferenceInvitationUserDTOList = conferenceInvitationUserDAO
                .queryLatestConferenceInvitationUserByCampaignMergeDataId(ea, campaignMergeDataIds, conferenceId);
        Map<Integer, FSEmployeeMsg> allFSEmployeeMsgMap = Maps.newHashMap();
        List<Integer> fsUserIdContainVirtualUser = Lists.newArrayList();
        Map<String, Integer> campaignMergeDataUserMap = conferenceInvitationUserDTOList.stream()
                .collect(Collectors.toMap(ConferenceInvitationUserDTO::getCampaignMergeDataId, ConferenceInvitationUserDTO::getUserId, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(conferenceInvitationUserDTOList)) {
            List<Integer> invitationUserIds = conferenceInvitationUserDTOList.stream().map(ConferenceInvitationUserDTO::getUserId).collect(Collectors.toList());
            List<Integer> fsUserList = invitationUserIds.stream().filter(data -> !QywxUserConstants.isVirtualUserId(data)).filter(Objects::nonNull).collect(Collectors.toList());
            fsUserIdContainVirtualUser.addAll(invitationUserIds.stream().filter(QywxUserConstants::isVirtualUserId).collect(Collectors.toList()));
            allFSEmployeeMsgMap.putAll(fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, fsUserList, true));
        }
        // 查询推广人
        fsUserIdContainVirtualUser.addAll(campaignParticipantsDTOList.stream().filter(data -> data.getSpreadFsUserId() != null).map(PageCampaignParticipantsDTO::getSpreadFsUserId).collect(Collectors.toList()));
        allFSEmployeeMsgMap.putAll(fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIdContainVirtualUser, true));
        // spreadUid和伙伴的信息map
        Map<Integer, UserRelationPartnerInfo> fsUserIdToPartnerInfoMap = userRelationManager.getPartnerInfoByFsUserIdList(ea, fsUserIdContainVirtualUser);
        // 拼装物料数据
        Map<String, String> objectNameMap = Maps.newHashMap();
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : campaignParticipantsDTOList) {
            String objectKey = pageCampaignParticipantsDTO.getEnrollSourceObjectId() + "#" + pageCampaignParticipantsDTO.getEnrollSourceObjectType();
            if (StringUtils.isBlank(objectNameMap.get(objectKey))) {
                String objectName = objectManager.getObjectName(pageCampaignParticipantsDTO.getEnrollSourceObjectId(), pageCampaignParticipantsDTO.getEnrollSourceObjectType());
                if (StringUtils.isNotBlank(objectName)) {
                    objectNameMap.put(objectKey, objectName);
                }
            }
        }
        // 查询会议报名内容
        List<String> campaignIds = campaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getId).collect(Collectors.toList());
        Map<String, CustomizeFormDataUserEntity> enrollMap = campaignMergeDataManager.getLatestActivityEnrollDataByCampaignId(ea, marketingEventId, conferenceId, campaignIds);
        /**
        if (enrollMap == null){
            enrollMap = campaignMergeDataManager.getParentMarketingEventEnrollDataByCampaignId(ea, marketingEventId, campaignIds);
        }
         */
        Map<String, String> picMap = customizeFormDataManager.conversionEnrollDataPic(ea, enrollMap.values());
        Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(ea);
        // 是否开启手机脱敏
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : campaignParticipantsDTOList) {
            List<Object> enrollInfos = Lists.newArrayList();
            // 名称
            String userName = pageCampaignParticipantsDTO.getName();
            if (StringUtils.isBlank(userName) && StringUtils.isNotBlank(pageCampaignParticipantsDTO.getCampaignMembersObjId())) {
                ObjectData userObjData = objectDataMap.get(pageCampaignParticipantsDTO.getCampaignMembersObjId());
                userName = userObjData != null ? userObjData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName()) : null;
            }
            enrollInfos.add(UnicodeFormatter.decodeUnicodeString(userName));
            String memberType = null;
            if (pageCampaignParticipantsDTO.getBindCrmObjectType() != null) {
                CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(pageCampaignParticipantsDTO.getBindCrmObjectType());
                memberType = campaignMergeDataObjectTypeEnum.getDesc();
            }
            enrollInfos.add(memberType);
            // 邀约人
            Integer invitationUserId = campaignMergeDataUserMap.get(pageCampaignParticipantsDTO.getId());
            if (invitationUserId != null) {
                FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(invitationUserId);
                enrollInfos.add(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
            } else {
                enrollInfos.add(null);
            }
            // 推广人
            if (pageCampaignParticipantsDTO.getSpreadFsUserId() != null) {
                if (QywxUserConstants.isPartnerVirtualUserId(pageCampaignParticipantsDTO.getSpreadFsUserId())) {
                    UserRelationPartnerInfo userRelationPartnerInfo = fsUserIdToPartnerInfoMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId());
                    String spreadUserName = "";
                    if (userRelationPartnerInfo != null) {
                        String outTenantName = userRelationPartnerInfo.getOuterTenantName() == null ? "" : userRelationPartnerInfo.getOuterTenantName();
                        String outUserName = userRelationPartnerInfo.getOuterUserName() == null ? "" : userRelationPartnerInfo.getOuterUserName();
                        spreadUserName = outTenantName + "-" + outUserName;
                    }
                    enrollInfos.add(spreadUserName);
                } else {
                    FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId());
                    enrollInfos.add(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
                }
            } else {
                enrollInfos.add(null);
            }
//            if (pageCampaignParticipantsDTO.getSpreadFsUserId() != null) {
//                FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId());
//                if (null != fsEmployeeMsg) {
//                    enrollInfos.add(fsEmployeeMsg.getName());
//                } else if (null != outUserNameMap && outUserNameMap.containsKey(pageCampaignParticipantsDTO.getSpreadFsUserId()) && StringUtils.isNotBlank(outUserNameMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId()))) {
//                    enrollInfos.add(outUserNameMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId()));
//                } else {
//                    enrollInfos.add(null);
//                }
//            } else {
//                enrollInfos.add(null);
//            }
            // 负责人
            if (StringUtils.isNotBlank(pageCampaignParticipantsDTO.getCampaignMembersObjId())) {
                enrollInfos.add(ownerNameMap.get(pageCampaignParticipantsDTO.getCampaignMembersObjId()));
            } else {
                enrollInfos.add(null);
            }
            // 来源推广内容
            enrollInfos.add(objectNameMap.get(pageCampaignParticipantsDTO.getEnrollSourceObjectId() + "#" + pageCampaignParticipantsDTO.getEnrollSourceObjectType()));
            // 来源渠道
            /*String channelLabel = channelValueMap.get(pageCampaignParticipantsDTO.getChannelValue());
            enrollInfos.add(StringUtils.isNotBlank(channelLabel) ? channelLabel : null);*/
            enrollInfos.add(spreadChannelManager.getChannelLabelByChannelValue(ea, channelValueMap, pageCampaignParticipantsDTO.getChannelValue()));
            enrollInfos.add(CampaignMergeDataInviteStatusEnum.getDescByType(pageCampaignParticipantsDTO.getInviteStatus()));
            enrollInfos.add(ConferenceEnrollReviewStatusEnum.getStatusDesc(pageCampaignParticipantsDTO.getReviewStatus()));
            enrollInfos.add(pageCampaignParticipantsDTO.getCode() != null ? pageCampaignParticipantsDTO.getCode().toString() : null);
            enrollInfos.add(ActivitySignOrEnrollEnum.getTypeDesc(pageCampaignParticipantsDTO.getSignIn()));
            enrollInfos.add(pageCampaignParticipantsDTO.getSignInTime() != null ? DateUtil.format(pageCampaignParticipantsDTO.getSignInTime()) : null);
            List<String> groupNames = getGroupNameByIdsStr(pageCampaignParticipantsDTO.getGroupUserId());
            if (CollectionUtils.isNotEmpty(groupNames)) {
                enrollInfos.add(groupNames.toString());
            } else {
                enrollInfos.add(null);
            }
            // 查询会议报名数据
            CustomizeFormDataUserEntity customizeFormDataUserEntity = enrollMap.get(pageCampaignParticipantsDTO.getId());
            if (customizeFormDataUserEntity != null && customizeFormDataEntity != null && StringUtils.isNotBlank(pageCampaignParticipantsDTO.getId())) {
                if (turnOnPhoneNumberSensitive) {
                    CustomizeFormDataEnroll submitContent = customizeFormDataUserEntity.getSubmitContent();
                    if (Objects.nonNull(submitContent) && StringUtils.isEmpty(submitContent.getPhone())) {
                        // 报名表单的手机号为空时，将活动成员数据的手机号填充到报名表单
                        submitContent.setPhone(pageCampaignParticipantsDTO.getPhone());
                    }
                    safetyManagementManager.phoneNumberSensitive(customizeFormDataUserEntity);
                }
                customizeFormDataManager.buildAreaInfoByEnrollData(Lists.newArrayList(customizeFormDataUserEntity));
                Map<String, String> fileAttachmentMap = customizeFormDataManager.conversionEnrollDataFileAttachment(ea, enrollMap.values());
                for (FieldInfo fieldInfo : customizeFormDataEntity.getFormBodySetting()) {
                    Object result = customizeFormDataManager.formatEnrollDataIncludeSpecialField(fieldInfo, customizeFormDataUserEntity, picMap, false);
                    if (fieldInfo.getType().equals(FieldInfo.Type.FILE_ATTACHMENT.getValue())){
                        if (result == null){
                            result = "";
                        }else {
                            List<FileAttachmentContainer> fileContainerList = (List<FileAttachmentContainer>) result;
                            List<String> linkUrl = fileContainerList.stream().map(file -> fileAttachmentMap.get(file.getPath())).collect(Collectors.toList());
                            result = linkUrl;
                        }
                    }
                    log.info("add enroll data result:{}", result);
                    enrollInfos.add(result);
                }
            }
            participantsList.add(enrollInfos);
        }
        enrollMap.clear();
        log.info("participantsList:{}", participantsList);
        return participantsList;
    }

    /**
     * 发送报名审核企信通知
     *
     * @param conferenceId
     * @return
     */
    public boolean sendReviewMessage(String conferenceId) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(conferenceId);
        if (activityEntity == null) {
            log.warn("conference is not exist conferenceId:{}", conferenceId);
            return false;
        }

        if (!activityEntity.getEnrollReview()) {
            return true;
        }
        if (activityEntity.getEnrollCheckEmployee() == null) {
            return true;
        }

        String noticeId = UUIDUtil.getUUID();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String submitTime = sdf.format(new Date());
        ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataDAO.getLatestEnrollByConferenceId(conferenceId);
        if (activityEnrollDataEntity != null) {
            submitTime = sdf.format(activityEnrollDataEntity.getCreateTime());
        }

        StringBuilder sb = new StringBuilder()
                .append(host)
                .append("/ec/kemai/release/notice.html?_hash=notice&title=报名审核")
                .append("&noticeId=")
                .append(noticeId)
                .append("&useNoticeCenterPage=true&contentType=")
                .append(NoticeContentTypeEnum.CONFERENCE_ENROLL_REVIEW.getType());
        String url = sb.toString();
        List<Double> checkEmployee = gs.fromJson(activityEntity.getEnrollCheckEmployee(), ArrayList.class);
        List<Integer> toUserList = checkEmployee.stream().map(user -> user.intValue()).collect(Collectors.toList());
        List<Double> checkDepartment = gs.fromJson(activityEntity.getEnrollCheckDepartment(), ArrayList.class);
        List<Integer> toDepartmentList = checkDepartment.stream().map(department -> department.intValue()).collect(Collectors.toList());
        Set<Integer> allUserIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(toUserList)) {
            allUserIds.addAll(toUserList);
        }
        if (CollectionUtils.isNotEmpty(toDepartmentList)) {
            if (toDepartmentList.contains(Constant.WHOLE_COMPANY_ID)) {
                List<Integer> employeeIdList = fsAddressBookManager.getEmployeeIdsByEa(activityEntity.getEa());
                allUserIds.addAll(employeeIdList);
            } else {
                List<Integer> userIdsByDepartmentId = fsAddressBookManager.getEmployeeIdsByCircleIds(activityEntity.getEa(), toDepartmentList);
                allUserIds.addAll(userIdsByDepartmentId);
            }
        }

        NoticeEntity noticeEntity = new NoticeEntity();
        noticeEntity.setId(noticeId);
        noticeEntity.setTitle(activityEntity.getTitle());
        noticeEntity.setContentType(NoticeContentTypeEnum.CONFERENCE_ENROLL_REVIEW.getType());
        noticeEntity.setContent(conferenceId);
        noticeEntity.setSendType(NoticeSendTypeEnum.NORMAL.getType());
        noticeEntity.setCreateTime(new Date());
        noticeEntity.setUpdateTime(new Date());
        noticeEntity.setStatus(NoticeStatusEnum.SUCCESS.getStatus());
        noticeEntity.setFsEa(activityEntity.getEa());
        noticeEntity.setFsUserId(activityEntity.getCreateBy());
        FsAddressBookManager.FSEmployeeMsg employeeMsg = fsAddressBookManager.getEmployeeInfo(activityEntity.getEa(), activityEntity.getCreateBy());
        if (employeeMsg != null) {
            noticeEntity.setUsername(employeeMsg.getFullName());
        } else {
            noticeEntity.setUsername("");
        }
        NoticeSendArg.NoticeVisibilityVO vo = new NoticeSendArg.NoticeVisibilityVO();
        vo.setUserIds(toUserList);
        noticeEntity.setSendScope(gs.toJson(vo));
        noticeDAO.addNotice(noticeEntity);

        String customParamJson = noticeManager.urlDispatcher(activityEntity.getEa(), noticeEntity.getContentType(), noticeEntity.getContent(), noticeEntity.getId(), null);

        fsMessageManager.doSendFXMessage(activityEntity.getEa(), activityEntity.getCreateBy(), toUserList, activityEntity.getTitle(), url, submitTime, customParamJson);
        return true;
    }

    /**
     * 定时发送审核通知
     */
    public void sendConferenceEnrollNoticeTask() {
        long twoHourCheckTime = 3600 * 2 * 1000;          //2小时发一次提醒
        long twentyFourHourCheckTime = 3600 * 24 * 1000;  //24小时发一次提醒

        Date checkpoint = null;
        List<EnrollNoticeTaskDTO> enrollNoticeTaskDTOList = conferenceDAO.queryCheckEnrollNotice(new Date(System.currentTimeMillis() - twoHourCheckTime));
        if (CollectionUtils.isEmpty(enrollNoticeTaskDTOList)) {
            return;
        }
        List<String> noticeConferenceIds = Lists.newArrayList();
        for (EnrollNoticeTaskDTO taskDTO : enrollNoticeTaskDTOList) {
            List<PageCampaignParticipantsDTO> pageCampaignParticipantsDTOList = campaignMergeDataDAO.queryCampaignParticipantsDataByMarketingEventIds(taskDTO.getEa(), Lists.newArrayList(taskDTO.getMarketingEventId()), ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus(), null);
            if (CollectionUtils.isNotEmpty(pageCampaignParticipantsDTOList)) {
                noticeConferenceIds.add(taskDTO.getConferenceId());
            }
        }
        if (CollectionUtils.isEmpty(noticeConferenceIds)) {
            log.warn("sendConferenceEnrollNoticeTask pageCampaignParticipantsDTOList is null marketingEventIds:{}", noticeConferenceIds);
            return;
        }

        List<QueryEnrollReviewResult> enrollReviewResults = activityEnrollDataDAO.queryConferenceEnrollReviewInfo(noticeConferenceIds);
        if (CollectionUtils.isEmpty(enrollReviewResults)) {
            return;
        }
        Map<String, EnrollNoticeTaskDTO> conferenceMap = new HashMap<>();
        for (EnrollNoticeTaskDTO dto : enrollNoticeTaskDTOList) {
            conferenceMap.put(dto.getConferenceId(), dto);
        }

        List<String> realNeedEnrollConferenceIdLists = enrollReviewResults.stream().map(enroll -> enroll.getConferenceId()).collect(Collectors.toList());
        Set<String> enrollConferenceSet = new HashSet<>(realNeedEnrollConferenceIdLists);
        List<String> conferenceIds = Lists.newArrayList();
        for (String conferenceId : enrollConferenceSet) {
            EnrollNoticeTaskDTO dto = conferenceMap.get(conferenceId);
            if (dto.getEnrollCheckEmployee() == null) {
                continue;
            }

            if (dto.getEnrollNoticePoint() == ConferenceEnrollCheckType.REAL_TIME.getType()) {
                continue;
            }

            if (dto.getEnrollNoticePoint() == ConferenceEnrollCheckType.TWO_HOURS.getType()) {
                //2小时发送一次
                checkpoint = new Date(System.currentTimeMillis() - twoHourCheckTime);
            } else {
                //24小时发送一次
                checkpoint = new Date(System.currentTimeMillis() - twentyFourHourCheckTime);
            }
            int ret = conferenceDAO.updateSendCheckEnrollNoticeFlag(dto.getConferenceId(), checkpoint, dto.getEnrollNoticePoint());
            if (ret < 1) {
                continue;
            }
            conferenceIds.add(conferenceId);
        }

        if (CollectionUtils.isNotEmpty(conferenceIds)) {
            for (String conferenceId : conferenceIds) {
                sendReviewMessage(conferenceId);
                sendQywxConferenceEnrollNoticeRealTime(conferenceId);
            }
        }
    }

    /**
     * 报名后实时发送审核通知
     *
     * @param conferenceId
     */
    public void sendConferenceEnrollNoticeRealTime(String conferenceId) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(conferenceId);
        if (activityEntity == null) {
            return;
        }
        if (!activityEntity.getEnrollReview()) {
            return;
        }
        if (activityEntity.getEnrollNoticePoint() != ConferenceEnrollCheckType.REAL_TIME.getType()) {
            log.info("sendReviewMessage not real notice conferenceId:{}", conferenceId);
            return;
        }

        sendReviewMessage(conferenceId);
    }

    /**
     * 开始邀约后，更新邀约状态
     *
     * @param id
     */
    public void updateInviteStatusAfterInviting(String id) {
        List<ConferenceInviteParticipantEntity> inviteParticipantEntities = inviteParticipantDAO.findByIds(Lists.newArrayList(id));
        if (CollectionUtils.isNotEmpty(inviteParticipantEntities)) {
            ConferenceInviteParticipantEntity entity = inviteParticipantEntities.get(0);
            if (entity.getStatus().intValue() == ConferenceInviteStatusEnum.UN_INVITE.getStatus()) {
                inviteParticipantDAO.updateInviteSendStatus(Lists.newArrayList(id), ConferenceInviteStatusEnum.INVITED_UN_SIGN_UP.getStatus());
            }
        }
    }

    /**
     * 根据手机号修改邀约状态
     *
     * @param phone
     */
    public void updateInviteStatusAfterSignIn(String conferenceId, String phone) {
        inviteParticipantDAO.updateInviteStatusByPhone(conferenceId, phone, ConferenceInviteStatusEnum.SIGN_IN.getStatus());
    }

    /**
     * 根据手机号批量修改邀约状态
     *
     * @param conferenceId
     * @param phoneList
     */
    public void updateInviteStatusAfterSignIn(String conferenceId, List<String> phoneList) {
        if (StringUtils.isBlank(conferenceId) || CollectionUtils.isEmpty(phoneList)) {
            return;
        }
        inviteParticipantDAO.updateInviteStatusByPhoneList(conferenceId, phoneList, ConferenceInviteStatusEnum.SIGN_IN.getStatus());
    }


    public Integer createGroupUserInfo(String ea, Integer fsUserId, String conferenceId, String groupName) {
        ConferenceUserGroupEntity conferenceUserGroupEntity = conferenceUserGroupDAO.getConferenceUserGroupByEaConferenceIdAndName(ea, conferenceId, groupName);
        Integer groupUserId = null;
        // 若没有分组名则插入更新
        if (conferenceUserGroupEntity == null) {
            conferenceUserGroupEntity = new ConferenceUserGroupEntity();
            conferenceUserGroupEntity.setEa(ea);
            conferenceUserGroupEntity.setConferenceId(conferenceId);
            conferenceUserGroupEntity.setGroupName(groupName);
            conferenceUserGroupEntity.setStatus(ConferenceUserGroupStatusEnum.NORMAL.getValue());
            conferenceUserGroupEntity.setCreateBy(fsUserId);
            conferenceUserGroupEntity.setUpdateBy(fsUserId);
            conferenceUserGroupDAO.insertConferenceUserGroup(conferenceUserGroupEntity);
            groupUserId = conferenceUserGroupEntity.getId();
        } else {
            groupUserId = conferenceUserGroupEntity.getId();
        }
        return groupUserId;
    }

    public List<String> getGroupNameByIdsStr(String ids) {
        if (StringUtils.isBlank(ids)) {
            return null;
        }
        String str[] = ids.split(",");
        List<String> name = Lists.newArrayList();
        for (String id : str) {
            Integer idInt = Integer.valueOf(id);
            ConferenceUserGroupEntity conferenceUserGroupEntity = conferenceUserGroupDAO.getConferenceUserGroupById(idInt);
            if (conferenceUserGroupEntity != null) {
                name.add(conferenceUserGroupEntity.getGroupName());
            }
        }
        return name;
    }

    public void sendQywxConferenceEnrollNoticeRealTime(String conferenceId) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(conferenceId);
        if (activityEntity == null) {
            log.warn("ConferenceManager.sendQywxConferenceEnrollNoticeRealTime activityEntity is null conferenceId:{}", conferenceId);
            return;
        }
        if (!activityEntity.getEnrollReview()) {
            return;
        }
        // 校验是否开通企业微信
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(activityEntity.getEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(activityEntity.getEa()));
        if (qywxMiniappConfigEntity == null) {
            return;
        }
        List<Double> checkEmployee = gs.fromJson(activityEntity.getEnrollCheckEmployee(), ArrayList.class);
        List<Integer> toUserList = checkEmployee.stream().map(user -> user.intValue()).collect(Collectors.toList());
        List<Double> checkDepartment = gs.fromJson(activityEntity.getEnrollCheckDepartment(), ArrayList.class);
        List<Integer> toDepartmentList = checkDepartment.stream().map(department -> department.intValue()).collect(Collectors.toList());
        Set<Integer> allUserIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(toUserList)) {
            allUserIds.addAll(toUserList);
        }
        if (CollectionUtils.isNotEmpty(toDepartmentList)) {
            if (toDepartmentList.contains(Constant.WHOLE_COMPANY_ID)) {
                List<Integer> employeeIdList = fsAddressBookManager.getEmployeeIdsByEa(activityEntity.getEa());
                allUserIds.addAll(employeeIdList);
            } else {
                List<Integer> userIdsByDepartmentId = fsAddressBookManager.getEmployeeIdsByCircleIds(activityEntity.getEa(), toDepartmentList);
                allUserIds.addAll(userIdsByDepartmentId);
            }
        }
        if (CollectionUtils.isEmpty(allUserIds)) {
            return;
        }
        Map<Integer, String> userMap = qywxUserManager.getQyUserIdByFsUserInfo(activityEntity.getEa(), Lists.newArrayList(allUserIds));
        if (MapUtils.isEmpty(userMap)) {
            return;
        }
        String submitTime = DateUtil.format3(new Date());
        ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataDAO.getLatestEnrollByConferenceId(conferenceId);
        if (activityEnrollDataEntity != null) {
            submitTime = DateUtil.format3(activityEnrollDataEntity.getCreateTime());
        }
        qywxMiniAppMessageManager.sendConferenceCheckEnroll(activityEntity.getEa(), Lists.newArrayList(userMap.values()), activityEntity.getTitle(), submitTime, conferenceId);
    }

    public void sendDingdingConferenceEnrollNoticeRealTime(String conferenceId) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(conferenceId);
        if (activityEntity == null) {
            log.warn("ConferenceManager.sendDingdingConferenceEnrollNoticeRealTime activityEntity is null conferenceId:{}", conferenceId);
            return;
        }
        if (!activityEntity.getEnrollReview()) {
            return;
        }

        ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataDAO.getLatestEnrollByConferenceId(conferenceId);
        dingManager.sendConferenceCheckEnroll(activityEntity,conferenceId,activityEnrollDataEntity);
    }

    public Result<Void> deleteParticipants(DeleteParticipantVO vo) {
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(vo.getCampaignId());
        if (campaignMergeDataEntity == null) {
            log.warn("ConferenceManager.deleteParticipants campaignMergeDataEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.CONFERENCE_PARTICIPATN_NOT_FOUND);
        }

        if (StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId()) && vo.isVerifyBindingObject()) {
            return Result.newError(SHErrorCode.CONFERENCE_PARTICIPATN_SAVED_LEAD);
        }

        // 删除参会人员数据
        campaignMergeDataDAO.deleteCampaignMergeData(vo.getCampaignId());

        // 删除会议关联数据
        List<String> conferenceEnrollId = campaignMergeDataManager.campaignIdToActivityEnrollId(Lists.newArrayList(vo.getCampaignId()));
        if (CollectionUtils.isNotEmpty(conferenceEnrollId)) {
            ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataDAO.
                    getActivityEnrollDataById(conferenceEnrollId.get(0));
            CustomizeTicketReceiveEntity customizeTicketReceiveEntity = customizeTicketDAO.
                    getTicketByAssociationAndDataUserId(activityEnrollDataEntity.getActivityId(), vo.getCampaignId());
            WxTicketReceiveEntity wxTicketReceiveEntity = wxTicketReceiveDAO.
                    getWxTicketReceiveByFormDataUserId(vo.getCampaignId());
            activityDAO.decEnrollCount(activityEnrollDataEntity.getActivityId());
            activityEnrollDataDAO.deleteById(activityEnrollDataEntity.getId());
            if (customizeTicketReceiveEntity != null) {
                customizeTicketDAO.deleteCustomizeTicketReceiveById(customizeTicketReceiveEntity.getId());
            }
            if (wxTicketReceiveEntity != null) {
                wxTicketReceiveDAO.deleteById(wxTicketReceiveEntity.getId());
            }
        }

        // 去除会员报名数据
        memberAccessibleCampaignDAO.deleteMemberAccessibleCampaignDataByCampaignId(vo.getCampaignId());

        // 去除表单关联
        customizeFormDataUserDAO.unBindCampaignData(vo.getCampaignId());

        return Result.newSuccess();
    }

    public void createConferenceTicketAndAttachedInfo(String ea, CustomizeFormDataUserEntity customizeFormDataUserEntity, Integer qrSourceType, String qrSourceId, String campaignMergeDataId) {
        boolean signIn = false;
        if (StringUtils.isBlank(campaignMergeDataId)) {
            return;
        }
        createConferenceEnrollAttachedInfo(campaignMergeDataId, customizeFormDataUserEntity, qrSourceType, qrSourceId, signIn);
        // 创建卡券
        String activityId = activityManager.getActivityIdByObject(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), ea, customizeFormDataUserEntity.getMarketingEventId());
        customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityId, campaignMergeDataId, ea, null);
    }

    public void createConferenceTicketAndAttachedInfo(String ea, Integer objectType, String objectId, Integer spreadFsUserId, Integer enrollSourceType, String campaignMergeDataId, String marketingEventId) {
        ActivityEntity activityEntity = null;
        if (ObjectTypeEnum.ACTIVITY.getType() == objectType) {
            activityEntity = activityDAO.getById(objectId);
            if (activityEntity != null) {
                ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
                activityEnrollDataEntity.setId(UUIDUtil.getUUID());
                activityEnrollDataEntity.setEnrollSourceType(enrollSourceType);
                activityEnrollDataEntity.setUid("");
                activityEnrollDataEntity.setActivityId(activityEntity.getId());
                activityEnrollDataEntity.setFormDataUserId(campaignMergeDataId);
                activityEnrollDataEntity.setSpreadFsUserId(spreadFsUserId);
                activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
                activityEnrollDataEntity.setSignInTime(null);
                activityEnrollDataEntity.setCreateTime(new Date());
                activityEnrollDataEntity.setEnrollSourceId(null);
                activityEnrollDataEntity
                        .setReviewStatus(activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
                activityEnrollDataEntity.setReviewUser(null);
                activityEnrollDataEntity.setReviewTime(null);
                activityEnrollDataEntity.setObjectId(activityEntity.getId());
                activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                addActivityEnrollData(activityEnrollDataEntity);
            }
        } else if (ObjectTypeEnum.ACTIVITY_INVITATION.getType() == objectType) {
            String activityId = activityManager.getActivityIdByObject(objectId, objectType, ea, marketingEventId);
            activityEntity = activityDAO.getById(activityId);
            if (activityEntity != null) {
                ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
                activityEnrollDataEntity.setId(UUIDUtil.getUUID());
                activityEnrollDataEntity.setEnrollSourceType(enrollSourceType);
                activityEnrollDataEntity.setUid("");
                activityEnrollDataEntity.setActivityId(activityEntity.getId());
                activityEnrollDataEntity.setFormDataUserId(campaignMergeDataId);
                activityEnrollDataEntity.setSpreadFsUserId(spreadFsUserId);
                activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
                activityEnrollDataEntity.setSignInTime(null);
                activityEnrollDataEntity.setCreateTime(new Date());
                activityEnrollDataEntity.setEnrollSourceId(null);
                activityEnrollDataEntity
                        .setReviewStatus(activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
                activityEnrollDataEntity.setReviewUser(null);
                activityEnrollDataEntity.setReviewTime(null);
                activityEnrollDataEntity.setObjectId(activityEntity.getId());
                activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY_INVITATION.getType());
                addActivityEnrollData(activityEnrollDataEntity);
            }
        } else if (StringUtils.isNotBlank(marketingEventId)) {
            activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
            // 若市场活动非会议则直接返回
            if (activityEntity == null) {
                return;
            }
            ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
            activityEnrollDataEntity.setId(UUIDUtil.getUUID());
            activityEnrollDataEntity.setEnrollSourceType(enrollSourceType);
            activityEnrollDataEntity.setUid("");
            activityEnrollDataEntity.setActivityId(activityEntity.getId());
            activityEnrollDataEntity.setFormDataUserId(campaignMergeDataId);
            activityEnrollDataEntity.setSpreadFsUserId(spreadFsUserId);
            activityEnrollDataEntity.setSignInTime(null);
            activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
            activityEnrollDataEntity.setCreateTime(new Date());
            activityEnrollDataEntity.setEnrollSourceId(null);
            activityEnrollDataEntity
                    .setReviewStatus(activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
            activityEnrollDataEntity.setReviewUser(null);
            activityEnrollDataEntity.setReviewTime(null);
            activityEnrollDataEntity.setObjectId(activityEntity.getId());
            activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
            addActivityEnrollData(activityEnrollDataEntity);
        }
        // 创建卡券
        if (activityEntity != null) {
            customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getId(), campaignMergeDataId, ea, marketingEventId);
        }
    }

    /**
     * 为非会议报名数据创建会议相关数据
     */
    public void createConferenceTicketAndAttachedInfoNoActivityObj(String ea, CustomizeFormDataUserEntity customizeFormDataUserEntity, String campaignMergeDataId) {
        String marketingEventId = customizeFormDataUserEntity.getMarketingEventId();
        if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(campaignMergeDataId)) {
            return;
        }
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity == null) {
            return;
        }
        createConferenceEnrollAttachedInfo(campaignMergeDataId, customizeFormDataUserEntity, activityEntity);

        // 创建会议卡券
        customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getId(), campaignMergeDataId, ea, null);
    }

    private void createConferenceEnrollAttachedInfo(String campaignMergeDataId, CustomizeFormDataUserEntity customizeFormDataUserEntity, ActivityEntity activityEntity) {
        ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
        activityEnrollDataEntity.setId(UUIDUtil.getUUID());
        activityEnrollDataEntity.setEnrollSourceType(activityManager.buildActivityEnrollDataSourceType(customizeFormDataUserEntity, null, null));
        activityEnrollDataEntity.setUid("");
        activityEnrollDataEntity.setActivityId(activityEntity.getId());
        activityEnrollDataEntity.setFormDataUserId(campaignMergeDataId);
        activityEnrollDataEntity.setSpreadFsUserId(customizeFormDataUserEntity.getSpreadFsUid());
        activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
        activityEnrollDataEntity.setSignInTime(null);
        activityEnrollDataEntity.setCreateTime(new Date());
        activityEnrollDataEntity.setEnrollSourceId(null);
        activityEnrollDataEntity
                .setReviewStatus(activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
        activityEnrollDataEntity.setReviewUser(null);
        activityEnrollDataEntity.setReviewTime(null);
        activityEnrollDataEntity.setObjectId(activityEntity.getId());
        activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
        addActivityEnrollData(activityEnrollDataEntity);
    }

    private void createConferenceEnrollAttachedInfo(String campaignMergeDataId, CustomizeFormDataUserEntity customizeFormDataUserEntity, Integer qrSourceType, String qrSourceId, boolean signIn) {
        Integer objectType = customizeFormDataUserEntity.getObjectType();
        String objectId = customizeFormDataUserEntity.getObjectId();
        if (ObjectTypeEnum.ACTIVITY.getType() == objectType) {
            ActivityEntity activityEntity = activityDAO.getById(objectId);
            if (activityEntity != null) {
                ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
                activityEnrollDataEntity.setId(UUIDUtil.getUUID());
                activityEnrollDataEntity.setEnrollSourceType(activityManager.buildActivityEnrollDataSourceType(customizeFormDataUserEntity, qrSourceType, qrSourceId));
                activityEnrollDataEntity.setUid("");
                activityEnrollDataEntity.setActivityId(activityEntity.getId());
                activityEnrollDataEntity.setFormDataUserId(campaignMergeDataId);
                activityEnrollDataEntity.setSpreadFsUserId(customizeFormDataUserEntity.getSpreadFsUid());
                if (signIn && !activityEntity.getEnrollReview()) {
                    activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.SIGN_IN.getType());
                } else {
                    activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
                }
                activityEnrollDataEntity.setSignInTime(null);
                activityEnrollDataEntity.setCreateTime(new Date());
                activityEnrollDataEntity.setEnrollSourceId(qrSourceId);
                activityEnrollDataEntity
                        .setReviewStatus(activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
                activityEnrollDataEntity.setReviewUser(null);
                activityEnrollDataEntity.setReviewTime(null);
                activityEnrollDataEntity.setObjectId(objectId);
                activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                addActivityEnrollData(activityEnrollDataEntity);
            }
        } else if (ObjectTypeEnum.ACTIVITY_INVITATION.getType() == objectType) {
            String activityId = activityManager.getActivityIdByObject(objectId, objectType, null, null);
            ActivityEntity activityEntity = activityDAO.getById(activityId);
            if (activityEntity != null) {
                ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
                activityEnrollDataEntity.setId(UUIDUtil.getUUID());
                activityEnrollDataEntity.setEnrollSourceType(activityManager.buildActivityEnrollDataSourceType(customizeFormDataUserEntity, qrSourceType, qrSourceId));
                activityEnrollDataEntity.setUid("");
                activityEnrollDataEntity.setActivityId(activityEntity.getId());
                activityEnrollDataEntity.setFormDataUserId(campaignMergeDataId);
                activityEnrollDataEntity.setSpreadFsUserId(customizeFormDataUserEntity.getSpreadFsUid());
                activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
                activityEnrollDataEntity.setSignInTime(null);
                activityEnrollDataEntity.setCreateTime(new Date());
                activityEnrollDataEntity.setEnrollSourceId(qrSourceId);
                activityEnrollDataEntity
                        .setReviewStatus(activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
                activityEnrollDataEntity.setReviewUser(null);
                activityEnrollDataEntity.setReviewTime(null);
                activityEnrollDataEntity.setObjectId(objectId);
                activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY_INVITATION.getType());
                addActivityEnrollData(activityEnrollDataEntity);
            }
        }
    }

    public void createConferenceEnrollAttachedInfoByCampaignObj(String marketingEventId, String campaignMergeDataId, String memberType, String ea) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity == null) {
            return;
        }
        ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
        activityEnrollDataEntity.setId(UUIDUtil.getUUID());
        activityEnrollDataEntity.setEnrollSourceType(ConferenceEnrollSourceTypeEnum.SYNC_FROM_CRM_CAMPAIGN_OBJ.getType());
        activityEnrollDataEntity.setUid("");
        activityEnrollDataEntity.setActivityId(activityEntity.getId());
        activityEnrollDataEntity.setFormDataUserId(campaignMergeDataId);
        Integer inviteStatus = null;
        Integer signInStatus = ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType();
        Date signInTime = null;
        Integer reviewStatus = activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus();
        if (CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue().equals(memberType)) {
            inviteStatus = CampaignMergeDataInviteStatusEnum.NOT_INVITED.getType();
        } else {
            inviteStatus = CampaignMergeDataInviteStatusEnum.INVITED.getType();
        }
        if (CampaignMembersObjMemberStatusEnum.REVIEW.getValue().equals(memberType)) {
            reviewStatus = ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus();
        } else if (CampaignMembersObjMemberStatusEnum.PARTICIPATE.getValue().equals(memberType)) {
            signInStatus = ActivitySignOrEnrollEnum.SIGN_IN.getType();
            reviewStatus = ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus();
        }
        activityEnrollDataEntity.setSignIn(signInStatus);
        if (signInStatus.equals(ActivitySignOrEnrollEnum.SIGN_IN.getType())) {
            signInTime = new Date();
            activityEnrollDataEntity.setSignInTime(signInTime);
        } else {
            activityEnrollDataEntity.setSignInTime(null);
        }
        activityEnrollDataEntity.setCreateTime(new Date());
        activityEnrollDataEntity.setEnrollSourceId(null);
        activityEnrollDataEntity.setReviewStatus(reviewStatus);
        activityEnrollDataEntity.setReviewUser(null);
        activityEnrollDataEntity.setReviewTime(null);
        activityEnrollDataEntity.setObjectId(activityEntity.getId());
        activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
        addActivityEnrollData(activityEnrollDataEntity);

        // 修改邀约状态
        if (inviteStatus != null) {
            campaignMergeDataManager.updateCampaignMergeDataInviteStatus(inviteStatus, campaignMergeDataId, false);
        }

        // 修改签到/审核状态
        Map<String, Object> editMap = Maps.newHashMap();
        ActivitySignOrEnrollEnum activitySignOrEnrollEnum = ActivitySignOrEnrollEnum.getByType(signInStatus);
        if (activitySignOrEnrollEnum != null && StringUtils.isNotBlank(activitySignOrEnrollEnum.getCampaignMergeDataApiName())) {
            editMap.put(CampaignConstants.SIGN_IN_STATUS_API_NAME, activitySignOrEnrollEnum.getCampaignMergeDataApiName());
            if (signInTime != null) {
                editMap.put(CampaignConstants.SIGNED_TIME_API_NAME, signInTime.getTime());
            }
        }
        ConferenceEnrollReviewStatusEnum conferenceEnrollReviewStatusEnum = ConferenceEnrollReviewStatusEnum.getByType(reviewStatus);
        if (conferenceEnrollReviewStatusEnum != null) {
            editMap.put(CampaignConstants.APPROVAL_STATUS_API_NAME, conferenceEnrollReviewStatusEnum.getCampaignMembersObjOptions());
        }
        CampaignMergeDataInviteStatusEnum campaignMergeDataInviteStatusEnum = CampaignMergeDataInviteStatusEnum.getByType(inviteStatus);
        if (campaignMergeDataInviteStatusEnum != null) {
            editMap.put(CampaignConstants.INVITATION_STATUS_API_NAME, campaignMergeDataInviteStatusEnum.getCrmOptions());
        }
        campaignMergeDataManager.updateCampaignMembersObjByCampaignMergeId(campaignMergeDataId, editMap);

    }


    public Result updateConferenceBaseInfo(UpdateConferenceDetailVO vo, ActivityEntity conferenceEntity) {
        if (StringUtils.isNotBlank(vo.getTitle())) {
            conferenceEntity.setTitle(vo.getTitle());
        }
        if (vo.getStartTime() != null) {
            conferenceEntity.setStartTime(DateUtil.fromTimestamp(vo.getStartTime()));
        }
        if (vo.getEndTime() != null) {
            conferenceEntity.setEndTime(DateUtil.fromTimestamp(vo.getEndTime()));
        }
        if (vo.getType() != null) {
            conferenceEntity.setType(vo.getType());
        }
        conferenceEntity.setScale(vo.getScale());

        conferenceEntity.setLocation(vo.getLocation());
        if (vo.isUpdateConferenceDetails() && StringUtils.isNotBlank(vo.getConferenceDetails())) {
            // 设置会议详情
            conferenceEntity.setConferenceDetails(getConferencePath(vo.getConferenceDetails()));
        }
        conferenceEntity.setUpdateBy(vo.getFsUserId());
        conferenceDAOManager.updateConference(conferenceEntity);
        if (vo.isUpdateCoverImage() && vo.getCoverImageTAPath().startsWith("TA_")) {
            try {
                FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(vo.getCoverImageTAPath(), vo.getEa(), vo.getFsUserId());
                if (null != fileManagerPicResult) {
                    photoManager.addOrUpdatePhotoByPhotoTargetType(vo.getEa(),PhotoTargetTypeEnum.ACTIVITY_COVER, vo.getId(), fileManagerPicResult.getUrlAPath(), fileManagerPicResult.getThumbUrlApath());
                }
                Map<String, Object> params = new HashMap<>();
                params.put("coverApath", fileManagerPicResult.getUrlAPath());
                ImageDrawer imageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
                String cardPhotoApath = imageDrawer.draw(params);
                if (StringUtils.isNotBlank(cardPhotoApath)) {
                    photoManager.addOrUpdatePhotoByPhotoTargetType(vo.getEa(),PhotoTargetTypeEnum.MINI_COVER_ACTIVITY_NORMAL, vo.getId(), cardPhotoApath, cardPhotoApath);
                }
            } catch (Exception e) {
                log.info("ConferenceServiceImpl updateConferenceDetail conferenceEntity UpdateCoverImage failed, vo:{}", vo);
                return Result.newError(SHErrorCode.CONFERENCE_UPDATE_IMAGE_FAIL);
            }
        }
        conferenceManager.resetConferenceIndexPage(conferenceEntity.getId(), false);
        return Result.newSuccess();
    }

    public Result updateConferenceEnrollSetting(ConferenceEnrollSettingVO vo, ActivityEntity conferenceEntity) {
        // 若当前表单没有设置手机则不能绑定
       /* CustomizeFormDataEntity checkFormData = customizeFormDataDAO.getCustomizeFormDataById(vo.getFormId());
        if (checkFormData != null) {
            if (checkFormData.getFormBodySetting().stream().noneMatch(data -> data.getApiName().equals("phone"))) {
                return Result.newError(SHErrorCode.CUSTOMIZE_FORM_NOT_PHONE_SETTING);
            }
        }*/
        conferenceEntity.setEnrollReview(vo.getEnrollReview());
        conferenceEntity.setEnrollEndTime(DateUtil.fromTimestamp(vo.getEnrollEndTime()));
        if (StringUtils.isNotBlank(vo.getEnrollPendingReviewTip())) {
            conferenceEntity.setEnrollPendingReviewTip(vo.getEnrollPendingReviewTip());
        }
        if (StringUtils.isNotBlank(vo.getEnrollReviewFailureTip())) {
            conferenceEntity.setEnrollReviewFailureTip(vo.getEnrollReviewFailureTip());
        }
        //conferenceEntity.setEnrollButton(vo.getEnrollButton());
        /*CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(conferenceEntity.getEa(), conferenceEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity == null || !customizeFormDataEntity.getId().equals(vo.getFormId())) {
            customizeFormDataManager.bindCustomizeFormDataObject(vo.getFormId(),conferenceEntity.getId(),ObjectTypeEnum.ACTIVITY.getType(), conferenceEntity.getEa(), vo.getFsUserId(), null, null, null);
            // 表单绑定市场活动
            // 若之前绑定过更换表单则更新绑定关系
            if (customizeFormDataEntity != null && !customizeFormDataEntity.getId().equals(vo.getFormId())) {
                ContentMarketingEventMaterialRelationEntity contentMarketingEventMaterialRelationEntity = contentMarketingEventMaterialRelationDAO
                    .getByMarketingEventIdAndObjectTypeAndObjectId(conferenceEntity.getEa(), conferenceEntity.getMarketingEventId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(),
                        customizeFormDataEntity.getId());
                if (contentMarketingEventMaterialRelationEntity != null) {
                    contentMarketingEventMaterialRelationDAO.deleteContentMarketingEventMaterialRelationById(contentMarketingEventMaterialRelationEntity.getId());
                }
            }
            if (StringUtils.isNotBlank(vo.getFormId()) && StringUtils.isNotBlank(conferenceEntity.getMarketingEventId())) {
                ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(conferenceEntity.getEa());
                entity.setObjectId(vo.getFormId());
                entity.setObjectType(ObjectTypeEnum.CUSTOMIZE_FORM.getType());
                entity.setMarketingEventId(conferenceEntity.getMarketingEventId());
                contentMarketingEventMaterialRelationDAO.save(entity);
            }
        }*/

        Gson gs = new Gson();
        String checkEmployee = null;

        if (null != vo.getAddressBookType() && 2 == vo.getAddressBookType()) {
            if (!CollectionUtils.isEmpty(vo.getOutEnrollCheckEmployee())) {
                vo.setEnrollCheckEmployee(Lists.newArrayList());
            }
            checkEmployee = gs.toJson(vo.getOutEnrollCheckEmployee(), ArrayList.class);
        }else{
            if (CollectionUtils.isEmpty(vo.getEnrollCheckEmployee())) {
                vo.setEnrollCheckEmployee(Lists.newArrayList());
            }
            checkEmployee = gs.toJson(vo.getEnrollCheckEmployee(), ArrayList.class);
        }
        if (checkEmployee != null) {
            conferenceEntity.setEnrollCheckEmployee(checkEmployee);
        }
        String departmentIds = null;
        if (CollectionUtils.isEmpty(vo.getEnrollCheckDepartment())) {
            vo.setEnrollCheckDepartment(Lists.newArrayList());
        }
        departmentIds = gs.toJson(vo.getEnrollCheckDepartment(), ArrayList.class);
        if (departmentIds != null) {
            conferenceEntity.setEnrollCheckDepartment(departmentIds);
        }
        conferenceEntity.setEnrollNoticePoint(vo.getEnrollNoticePoint());

        conferenceEntity.setDefaultContentMobileDisplay(vo.getDefaultContentMobileDisplay());
        conferenceEntity.setDefaultPosterMobileDisplay(vo.getDefaultPosterMobileDisplay());

        conferenceDAO.updateConferenceEnrollSetting(conferenceEntity);

        //更新会议和审核人员对应关系
        if (null != vo.getAddressBookType() && 2 == vo.getAddressBookType()) {
            updateConferenceReviewEmployee(vo);
        }else{
            updateConferenceReviewEmployee(vo.getEa(), vo.getId(), vo.getEnrollCheckEmployee(), vo.getEnrollCheckDepartment());
        }
        objectTagManager.addOrUpdateObjectTag(vo.getEa(), vo.getFsUserId(), vo.getId(), ObjectTypeEnum.ACTIVITY.getType(), vo.getTagNameList());
        return Result.newSuccess();
    }

    public void updateConferenceReviewEmployee(ConferenceEnrollSettingVO vo) {
        List<String> dingUserIds = dingManager.getDingUserIdsByDepartmentList(vo.getOutEnrollCheckEmployee(), vo.getEnrollCheckDepartment(), vo.getEa());
        List<QywxVirtualFsUserEntity> virtualUserByEaAndQyIds = virtualUserManager.getVirtualUserByEaAndQyIds(vo.getEa(), dingUserIds);
        Map<String,Integer> outIdToFsUserIdMap = new HashMap<>();
        for (QywxVirtualFsUserEntity virtualUserByEaAndQyId : virtualUserByEaAndQyIds) {
            outIdToFsUserIdMap.put(virtualUserByEaAndQyId.getQyUserId(),virtualUserByEaAndQyId.getUserId());
        }
        List<ConferenceReviewEmployeeEntity> entities = Lists.newArrayList();
        for (String dingUserId : dingUserIds) {
            ConferenceReviewEmployeeEntity conferenceReviewEmployeeEntity = new ConferenceReviewEmployeeEntity();
            conferenceReviewEmployeeEntity.setId(UUIDUtil.getUUID());
            conferenceReviewEmployeeEntity.setEa(vo.getEa());
            conferenceReviewEmployeeEntity.setUserId(outIdToFsUserIdMap.get(dingUserId));
            conferenceReviewEmployeeEntity.setConferenceId(vo.getId());
            conferenceReviewEmployeeEntity.setOutUserId(dingUserId);
            entities.add(conferenceReviewEmployeeEntity);
        }
        conferenceReviewEmployeeDAO.deleteByConferenceId(vo.getId());
        if (CollectionUtils.isNotEmpty(entities)) {
            conferenceReviewEmployeeDAO.addConferenceReviewEmployees(entities);
        }
    }

    /**
     * 更新会议审核人
     * conferenceId
     *
     * @param ea
     * @param conferenceId
     * @param userIds
     * @param department
     */
    public void updateConferenceReviewEmployee(String ea, String conferenceId, List<Integer> userIds, List<Integer> department) {
        Set<Integer> allUserIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            allUserIds.addAll(userIds);
        }
        if (CollectionUtils.isNotEmpty(department)) {
            if (department.contains(Constant.WHOLE_COMPANY_ID)) {
                List<Integer> employeeIdList = fsAddressBookManager.getEmployeeIdsByEa(ea);
                allUserIds.addAll(employeeIdList);
            } else {
                List<Integer> userIdsByDepartmentId = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, department);
                allUserIds.addAll(userIdsByDepartmentId);
            }
        }
        List<ConferenceReviewEmployeeEntity> entities = Lists.newArrayList();
        List<Integer> employees = Lists.newArrayList(allUserIds);
        for (int i = 0; i < employees.size(); i++) {
            ConferenceReviewEmployeeEntity conferenceReviewEmployeeEntity = new ConferenceReviewEmployeeEntity();
            conferenceReviewEmployeeEntity.setId(UUIDUtil.getUUID());
            conferenceReviewEmployeeEntity.setEa(ea);
            conferenceReviewEmployeeEntity.setUserId(employees.get(i));
            conferenceReviewEmployeeEntity.setConferenceId(conferenceId);
            entities.add(conferenceReviewEmployeeEntity);
        }
        conferenceReviewEmployeeDAO.deleteByConferenceId(conferenceId);
        if (CollectionUtils.isNotEmpty(entities)) {
            conferenceReviewEmployeeDAO.addConferenceReviewEmployees(entities);
        }
    }

    public Result updateConferenceStatus(String id, Integer status, String ea, Integer fsUserId) {
        if (!ActivityStatusEnum.isValidType(status)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceById(id);
        if (conferenceEntity == null) {
            log.info("ConferenceServiceImpl updateConferenceStatus conferenceEntity failed, ea:{} id:{}", ea, id);
            return Result.newError(SHErrorCode.CONFERENCE_UPDATE_NOT_FOUND);
        }
        /*

        if (status.equals(ActivityStatusEnum.DISABLED.getStatus()) && !conferenceEntity.getStatus().equals(ActivityStatusEnum.ENABLED.getStatus())) {
            return Result.newError(SHErrorCode.CONFERENCE_STATUS_TO_DISABLED_FAIL);
        }

        if (status.equals(ActivityStatusEnum.ENABLED.getStatus()) && !conferenceEntity.getStatus().equals(ActivityStatusEnum.DISABLED.getStatus()) && !conferenceEntity.getStatus().equals(ActivityStatusEnum.UNPUBLISHED.getStatus())) {
            return Result.newError(SHErrorCode.CONFERENCE_STATUS_TO_ENABLED_FAIL);
        }

        if (status.equals(ActivityStatusEnum.DELETED.getStatus()) && !conferenceEntity.getStatus().equals(ActivityStatusEnum.UNPUBLISHED.getStatus()) &&
                !isCanDeleted(id)) {
            return Result.newError(SHErrorCode.CONFERENCE_STATUS_TO_DELETE_FAIL);
        }
        */

        //删除市场活动数据
        if (status.equals(ActivityStatusEnum.DELETED.getStatus())) {
            try {
                crmV2Manager.bulkInvalid(ea, fsUserId == null ? -10000 : fsUserId, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList(conferenceEntity.getMarketingEventId()));
            } catch (Exception e) {
                log.info("updateConferenceStatus delete conference marketingEvent failed conferenceId:{} marketingEventId:{} error:{}",
                        conferenceEntity.getId(), conferenceEntity.getMarketingEventId(), e.getMessage());
                return Result.newError(SHErrorCode.CONFERENCE_DELETE_MARKETINGEVENT_FAILED);
            }
            customizeFormDataManager.unBindCustomizeFormDataObject(id, ObjectTypeEnum.ACTIVITY.getType(), ea);
        }

        if (status.equals(ActivityStatusEnum.ENABLED.getStatus())) {
            ThreadPoolUtils.execute(new Runnable() {
                @Override
                public void run() {
                    // 关联市场活动与物料
                    ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(conferenceEntity.getEa());
                    entity.setObjectId(conferenceEntity.getId());
                    entity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                    entity.setMarketingEventId(conferenceEntity.getMarketingEventId());
                    contentMarketingEventMaterialRelationDAO.save(entity);
                }
            }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        }
        conferenceDAOManager.updateConferenceStatus(ea, id, status, fsUserId);

        return Result.newSuccess();
    }


    public void addActivityEnrollData(ActivityEnrollDataEntity activityEnrollDataEntity) {
        if (activityEnrollDataEntity == null) {
            return;
        }
        String lockKey = "marketing:addActivityEnrollData:" + activityEnrollDataEntity.getFormDataUserId();
        try {
            boolean lockRequired = redisManager.lock(lockKey, 10);
            if (!lockRequired) {
                log.warn("addActivityEnrollData retryGetLock fail formDataUserId:{}",activityEnrollDataEntity.getFormDataUserId());
                return;
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(activityEnrollDataEntity.getFormDataUserId()));
            if (CollectionUtils.isNotEmpty(activityEnrollDataEntityList)) {
                return;
            }

            if (StringUtils.isNotBlank(activityEnrollDataEntity.getActivityId())) {
                ActivityEntity activityEntity = activityDAO.getById(activityEnrollDataEntity.getActivityId());
                if (activityEntity != null) {
                    if(StringUtils.isEmpty(activityEnrollDataEntity.getMarketingEventId())){
                        activityEnrollDataEntity.setMarketingEventId(activityEntity.getMarketingEventId());
                    }
                    if(StringUtils.isEmpty(activityEnrollDataEntity.getEa())){
                        activityEnrollDataEntity.setEa(activityEntity.getEa());
                    }
                }
            }
            activityEnrollDataDAO.insertActivityEnrollData(activityEnrollDataEntity);
            // 增加报名人数
            activityDAO.incrementEnrollCount(activityEnrollDataEntity.getActivityId(), 1);
        } catch (Exception e) {
            log.error("ConferenceManager.addActivityEnrollData formDataUserId:{} error e:", activityEnrollDataEntity.getFormDataUserId(), e);
        } finally {
            redisManager.unLock(lockKey);
        }
    }

    public Map<String, String> queryCampaignMembersObjOwnerMap(String ea, String marketingEventId, List<String> campaignMembersObjIds) {
        Map<String, String> nameMap = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(campaignMembersObjIds)) {
            return nameMap;
        }
        Filter objectIdFilter = new Filter();
        objectIdFilter.setFieldName(CampaignMembersObjApiNameEnum.ID.getApiName());
        objectIdFilter.setOperator(OperatorContants.IN);
        objectIdFilter.setFieldValues(campaignMembersObjIds);
        List<ObjectData> objectDataList = crmV2Manager.getCampaignMembersObjByFilter(ea, -10000, Lists.newArrayList(objectIdFilter), marketingEventId, true);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return nameMap;
        }
        List<Integer> ownerList = objectDataList.stream().filter(data -> data.getOwner() != null).map(ObjectData::getOwner).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ownerList)) {
            return nameMap;
        }
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, ownerList, true);
        for (ObjectData objectData : objectDataList) {
            if (objectData.getOwner() == null) {
                continue;
            }
            if (objectData.getOwner() == -10000) {
                nameMap.put(objectData.getId(), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
            }
            FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(objectData.getOwner());
            if (fsEmployeeMsg == null) {
                continue;
            }
            String userName = fsEmployeeMsg.getName();
            if (StringUtils.isBlank(userName)) {
                continue;
            }
            nameMap.put(objectData.getId(), userName);
        }
        return nameMap;
    }

    public Map<String, String> queryCampaignMembersObjBusinessOwnerMap(String ea, List<ObjectData> objectDataList) {
        Map<String, String> nameMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(objectDataList)){
            return nameMap;
        }
        for (ObjectData objectData : objectDataList) {
            String userName = getCampaignMembersObjBusinessOwnerByObj(objectData);
            if (StringUtils.isBlank(userName)) {
                continue;
            }
            nameMap.put(objectData.getId(), userName);
        }
        if (MapUtils.isEmpty(nameMap)) {
            return nameMap;
        }

        Set<String> userIdSet = Sets.newHashSet();
        nameMap.values().stream().filter(e -> e.startsWith("[")).map(e -> JSONArray.parseArray(e, String.class)).forEach(userIdSet::addAll);

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("user_id", OperatorConstants.IN, Lists.newArrayList(userIdSet));
        List<String> selectFieldList = Lists.newArrayList("_id", "user_id", "name");
        List<ObjectData> personObjDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.PERSONNEL_OBJ.getName(), selectFieldList, paasQueryArg);
        Map<String, String> employeeIdToNameMap = personObjDataList.stream().collect(Collectors.toMap(e -> e.getString("user_id"), e -> e.getString("name"), (v1, v2) -> v1));
        nameMap.forEach((objectId, userIds) -> {
            if (userIds.startsWith("[")) {
                List<String> userIdList = JSONArray.parseArray(userIds, String.class);
                if (CollectionUtils.isNotEmpty(userIdList)) {
                    String userId = userIdList.get(0);
                    String userName = employeeIdToNameMap.getOrDefault(userId, "");
                    nameMap.put(objectId, userName);
                }
            }
        });
        return nameMap;
    }

    public String getCampaignMembersObjBusinessOwnerByObj(ObjectData objectData) {
        if (objectData == null) {
            return null;
        }
        String bindApiName = objectData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName());
        if (StringUtils.isBlank(bindApiName)) {
            return null;
        }
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByApiName(bindApiName);
        if (campaignMergeDataObjectTypeEnum == null) {
            return null;
        }
        return objectData.getString(campaignMergeDataObjectTypeEnum.getBusinessOwner());
    }

    public Map<String, ObjectData> queryCampaignMembersObjMapByField(String ea, String marketingEventId, List<String> campaignMembersObjIds) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(campaignMembersObjIds) || StringUtils.isBlank(marketingEventId)) {
            return Maps.newHashMap();
        }
        List<ObjectData> objectDataList = queryCampaignMembersObjByFilter(ea, marketingEventId, campaignMembersObjIds);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Maps.newHashMap();
        }
        return objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, data -> data, (v1, v2) -> v1));
    }

    public List<ObjectData> queryCampaignMembersObjByFilter(String ea, String marketingEventId, List<String> campaignMembersObjIds) {
        if (CollectionUtils.isEmpty(campaignMembersObjIds)){
            return new ArrayList<>();
        }

        int pageSize = 1000;
        PageUtil pageUtil = new PageUtil(campaignMembersObjIds, pageSize);
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, pageSize);
        paasQueryArg.addFilter(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), FilterOperatorEnum.EQ.getValue(), Lists.newArrayList(marketingEventId));
        paasQueryArg.addFilter(CampaignMembersObjApiNameEnum.ID.getApiName(), FilterOperatorEnum.IN.getValue(), pageUtil.getPagedList(1));
        queryFilterArg.setQuery(paasQueryArg);

        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, -10000, queryFilterArg, null, pageSize);
        if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())){
            return new ArrayList<>();
        }

        List<ObjectData> objectDataList = Lists.newArrayList();
        objectDataList.addAll(objectDataInnerPage.getDataList());
        int totalCount = objectDataInnerPage.getTotalCount();
        int count = objectDataInnerPage.getDataList().size();
        String lastId = objectDataInnerPage.getDataList().get(count - 1).getId();
        while (count < totalCount){
            objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, -10000, queryFilterArg, lastId, pageSize);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())){
                break;
            }
            count += objectDataInnerPage.getDataList().size();
            lastId = objectDataInnerPage.getDataList().get(count - 1).getId();
            objectDataList.addAll(objectDataInnerPage.getDataList());
        }

        return objectDataList;
    }

    public com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingEventObj(CreateOrUpdateConferenceVO vo) {
//        Map<String, Object> params = new HashMap<>();
        List<String> createByList = Lists.newArrayList();
        createByList.add(String.valueOf(vo.getFsUserId()));
        int ei = eieaConverter.enterpriseAccountToId(vo.getEa());
/*        params.put(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), vo.getTitle());
        params.put(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName(), vo.getStartTime());
        params.put(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName(), vo.getEndTime());
        params.put("created_by", createByList);
        params.put(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName(),MarketingEventEnum.MEETING_SALES.getEventType());
        if (vo.getEa().equals("sbtjt888")){
            params.put(SbtFormDataObject.MARKETING_EVEN_LOCATION, vo.getLocation());
        }else {
            params.put(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName(), vo.getLocation());
        }
        params.put("status", 1);*/

        ActionAddArg arg = new ActionAddArg();
        ObjectData data = ObjectData.convert(vo.getCreateObjectDataModel().getObjectData());
        //为了防止params中少了别的参数，还是使用data中的数据
        data.computeIfAbsent(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), k -> vo.getTitle());
        data.computeIfAbsent(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName(), k -> vo.getStartTime());
        data.computeIfAbsent(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName(), k -> vo.getEndTime());
        data.computeIfAbsent("created_by", k -> createByList);
        data.computeIfAbsent(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName(), k -> vo.getEventType());
        data.put(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName(), vo.getParentId());
        data.put(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName(), MarketingEventFormEnum.CONFERENCE_MARKETING.getValue());
        if(!Strings.isNullOrEmpty(vo.getCoverImagePath())){
            try {
                List<Map<String, Object>> headImage = new ArrayList<>(1);
                String npath = fileV2Manager.getNpathByApath(vo.getCoverImagePath(), vo.getEa());
                if (StringUtils.isNotBlank(npath)) {
                    Map<String, Object> fileMap = new HashMap<>();
                    fileMap.put("ext", "jpg");
                    fileMap.put("path", npath);
                    headImage.add(fileMap);
                    data.put("cover", headImage);
                }
            } catch (Exception e) {
                log.warn("upload head exception ,ea={},apath={}",vo.getEa(),vo.getCoverImagePath(),e);
            }
        }
        if (vo.getEa().equals("sbtjt888")) {
            data.put(SbtFormDataObject.MARKETING_EVEN_LOCATION, vo.getLocation());
        } else {
            data.put(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName(), vo.getLocation());
        }
        arg.setObjectData(data);
        try {
            return metadataActionService.add(new HeaderObj(ei, vo.getFsUserId()), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), false, arg);
        } catch (Exception e) {
            log.warn("CrmManager.createData Exception apiName:{}, e:{}", CrmObjectApiNameEnum.MARKETING_EVENT.getName(), e);
            return null;
        }

    }

    public ActivityEntity update(CreateOrUpdateConferenceVO vo, ActivityEntity conferenceEntity) {
        if (StringUtils.isNotBlank(vo.getTitle())) {
            conferenceEntity.setTitle(vo.getTitle());
        }
        if (vo.getStartTime() != null) {
            conferenceEntity.setStartTime(DateUtil.fromTimestamp(vo.getStartTime()));
        }
        if (vo.getEndTime() != null) {
            conferenceEntity.setEndTime(DateUtil.fromTimestamp(vo.getEndTime()));
        }

        conferenceEntity.setLocation(vo.getLocation());
        if (vo.isUpdateConferenceDetails() && StringUtils.isNotBlank(vo.getConferenceDetails())) {
            // 设置会议详情
            conferenceEntity.setConferenceDetails(getConferencePath(vo.getConferenceDetails()));
        }
        conferenceEntity.setUpdateBy(vo.getFsUserId());
        conferenceEntity.setShowAcitivityList(vo.isShowActivityList());
        conferenceEntity.setMapAddress(vo.getMapAddress());
        conferenceEntity.setMapLocation(vo.getMapLocation());
        return conferenceEntity;
    }

    public int queryMarketingEventCountByTitle(String ea, String title) {
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(MarketingEventFieldContants.NAME, Lists.newArrayList(title), FilterOperatorEnum.EQ);
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        params.setIncludeLayout(false);
        params.setIncludeDescribe(false);
        params.setIncludeButtonInfo(false);
        Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), MarketingEventFieldContants.API_NAME, params);
        return total == null ? 0 : total;
    }

    public void syncCrmInvalidConference(String ea) {
        List<String> marketingEventIds = conferenceDAO.getNormalMarketingEventByEa(ea);
        if (CollectionUtils.isEmpty(marketingEventIds)) {
            return;
        }

        marketingEventIds.forEach(marketingEventId -> {
            ControllerDetailArg arg = new ControllerDetailArg();
            arg.setObjectDataId(marketingEventId);
            arg.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
            try {
                ObjectData objectData = metadataControllerServiceManager.detail(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), MarketingEventFieldContants.API_NAME, arg);
            } catch (Exception e) {
                if (e instanceof OuterServiceRuntimeException) {
                    if (e.getMessage().equals("数据已作废或已删除")) {
                        conferenceDAO.updateConferenceStatusByMarketingEvent(ea, marketingEventId, ActivityStatusEnum.CRM_INVALID.getStatus());
                    }
                }
            }
        });
    }

    public com.fxiaoke.crmrestapi.common.result.Result<BulkDeleteResult> bulkDelete(String ea, Integer fsUserId, List<String> ids) {
        BulkDeleteArg arg = new BulkDeleteArg();
        arg.setDataIds(ids);
        return objectDataService.bulkDelete(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), MarketingEventFieldContants.API_NAME, arg, true);
    }

    public Result signInUserByPhone(String ea, String marketingEventId, String phone) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(phone)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        try {
            // 查询会议
            ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
            if (activityEntity == null) {
                return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
            }
            // 查询总表中手机号数据
            List<CampaignMergeDataEntity> campaignMergeDataByPhone = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, marketingEventId, phone, false);
            if (CollectionUtils.isEmpty(campaignMergeDataByPhone)) {
                log.warn("ConferenceManager.signInUserByPhone campaignMergeDataByPhone is null marketingEventId:{}, phone:{}", marketingEventId, phone);
                return Result.newError(SHErrorCode.NO_DATA);
            }
            List<String> needSetCampaignId = campaignMergeDataByPhone.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
            }
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                return Result.newSuccess();
            }
            campaignMergeDataManager
                    .updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataByPhone) {
                List<BaseCampaignEnrollData> baseCampaignEnrollDataList = campaignMergeDataDAO.queryCampaignEnrollDataIdentityInfo(ea, marketingEventId, campaignMergeDataEntity.getId());
                if (CollectionUtils.isNotEmpty(baseCampaignEnrollDataList)) {
                    BaseCampaignEnrollData baseCampaignEnrollData = baseCampaignEnrollDataList.get(0);
                    activityManager
                            .handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, campaignMergeDataEntity.getPhone(), baseCampaignEnrollData.getOpenId(),
                                    baseCampaignEnrollData.getWxAppId(),
                                    baseCampaignEnrollData.getFingerPrint(), baseCampaignEnrollData.getEnrollUserEa(), baseCampaignEnrollData.getEnrollUserFsUid());
                }
            }
        } catch (Exception e) {
            log.warn("ConferenceManager.signInUserByPhone error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    public Result signInUserByCampaignId(String campaignId,String tagId) {
        if (StringUtils.isBlank(campaignId)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        try {
            CampaignMergeDataEntity campaignMergeData = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
            if (campaignMergeData == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(campaignMergeData.getMarketingEventId(), campaignMergeData.getEa());
            if (activityEntity == null) {
                return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(campaignId));
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
            }
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                return Result.newSuccess();
            }
            campaignMergeDataManager
                    .updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            List<BaseCampaignEnrollData> baseCampaignEnrollDataList = campaignMergeDataDAO
                    .queryCampaignEnrollDataIdentityInfo(campaignMergeData.getEa(), campaignMergeData.getMarketingEventId(), campaignMergeData.getId());
            if (CollectionUtils.isNotEmpty(baseCampaignEnrollDataList)) {
                BaseCampaignEnrollData baseCampaignEnrollData = baseCampaignEnrollDataList.get(0);
                activityManager
                        .handleActivitySignInOtherProcess(Lists.newArrayList(campaignMergeData.getId()), activityEntity, campaignMergeData.getPhone(), baseCampaignEnrollData.getOpenId(),
                                baseCampaignEnrollData.getWxAppId(),
                                baseCampaignEnrollData.getFingerPrint(), baseCampaignEnrollData.getEnrollUserEa(), baseCampaignEnrollData.getEnrollUserFsUid());
            }
            //处理分会场签到
            if (StringUtils.isNotBlank(tagId)) {
                activityManager.handleConferenceTag(Lists.newArrayList(campaignId),activityEntity.getEa(),tagId);
            }
        } catch (Exception e) {
            log.warn("ConferenceManager.signInUserByCampaignId error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    public void createDefaultConferenceSite(String conferenceId) {
        ActivityEntity activityEntity = activityDAO.getById(conferenceId);
        if (activityEntity == null || StringUtils.isNotBlank(activityEntity.getActivityDetailSiteId())) {
            return;
        }
        // id 集合
        DefaultConferenceSiteIdContainer defaultConferenceSiteIdContainer = new DefaultConferenceSiteIdContainer();
        String newFormId = defaultConferenceSiteIdContainer.getNewFormId();
        List<String> hexagonPageIds = new ArrayList<>();
        try {
            // 处理站点数据
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(conferenceTemplateSiteId);
            if (hexagonSiteEntity == null) {
                return;
            }
            HexagonSiteEntity saveSiteData = new HexagonSiteEntity();
            saveSiteData.setId(defaultConferenceSiteIdContainer.getNewSiteId());
            saveSiteData.setEa(activityEntity.getEa());
            saveSiteData.setName(activityEntity.getTitle());
            saveSiteData.setStatus(HexagonStatusEnum.NORMAL.getType());
            saveSiteData.setCreateBy(activityEntity.getCreateBy());
            saveSiteData.setUpdateBy(activityEntity.getCreateBy());
            saveSiteData.setCreateTime(activityEntity.getCreateTime());
            saveSiteData.setUpdateTime(activityEntity.getCreateTime());
            saveSiteData.setSystemSite(true);

            // 处理首页
            HexagonPageEntity indexPageEntity = handleIndexPage(defaultConferenceSiteIdContainer, activityEntity);
            // 【市场活动设置】中设置的模板，若有则按设置处理，无则默认模板
            HexagonTemplateSiteEntity hexagonTemplateSite = hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(activityEntity.getEa(), HexagonTemplateTypeEnum.CONFERENCE.getType());
            if (hexagonTemplateSite != null) {
                List<HexagonTemplatePageEntity> templatePages = hexagonTemplatePageDAO.getBySiteId(hexagonTemplateSite.getId());
                HexagonSiteListDTO formInfo = hexagonTemplateSiteDAO.getFormByTemplateSiteId(hexagonTemplateSite.getId());
                Map<String, String> buttonInsideAction = new HashMap<>();
                for (int i = 0; i < templatePages.size(); i++) {
                    HexagonTemplatePageEntity templatePage = templatePages.get(i);
                    Map<String, Object> copyResult = hexagonSiteManager.copyPageFromTemplate(activityEntity.getEa(), templatePage, defaultConferenceSiteIdContainer.getNewSiteId(), formInfo, newFormId, buttonInsideAction);
                    String newPageId = (String) copyResult.get("newPageId");
                    hexagonPageIds.add(newPageId);
                    if (i == 0) {
                        String indexContent = indexPageEntity.getContent();
                        indexContent = indexContent.replace(defaultConferenceSiteIdContainer.getNewFormPageId(), newPageId);
                        indexPageEntity.setContent(indexContent);
                    }
                    newFormId = (String) copyResult.get("newFormId");
                }
            } else {
                // 处理表单报名页
                HexagonPageEntity formPageEntity = handleFormPage(defaultConferenceSiteIdContainer, activityEntity);
                // 报名成功页
                HexagonPageEntity successPageEntity = handleSuccessPage(defaultConferenceSiteIdContainer, activityEntity);
                hexagonPageDAO.insert(formPageEntity);
                hexagonPageIds.add(formPageEntity.getId());
                hexagonPageDAO.insert(successPageEntity);
                hexagonPageIds.add(successPageEntity.getId());
            }

            // 数据保存与绑定
            hexagonSiteDAOManager.insert(saveSiteData);
            hexagonPageDAO.insert(indexPageEntity);
            conferenceDAO.updateConferenceDetailSiteId(activityEntity.getId(), defaultConferenceSiteIdContainer.getNewSiteId());
            ContentMarketingEventMaterialRelationEntity contentMarketingEventMaterialRelationEntity = new ContentMarketingEventMaterialRelationEntity();
            contentMarketingEventMaterialRelationEntity.setId(defaultConferenceSiteIdContainer.getContentMarketingEventMaterialRelationId());
            contentMarketingEventMaterialRelationEntity.setEa(activityEntity.getEa());
            contentMarketingEventMaterialRelationEntity.setObjectId(defaultConferenceSiteIdContainer.getNewSiteId());
            contentMarketingEventMaterialRelationEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
            contentMarketingEventMaterialRelationEntity.setMarketingEventId(activityEntity.getMarketingEventId());
            contentMarketingEventMaterialRelationEntity.setIsApplyObject(true);
            contentMarketingEventMaterialRelationDAO.save(contentMarketingEventMaterialRelationEntity);
            contentMarketingEventMaterialRelationDAO.updateIsApplyObject(defaultConferenceSiteIdContainer.getContentMarketingEventMaterialRelationId(), true);
            List<HexagonPageEntity> pageByFormIdAndSiteId = hexagonPageDAO.getPageByFormIdAndSiteId(saveSiteData.getId(), newFormId);
            // 将表单绑定微页面下
            customizeFormDataManager.bindCustomizeFormDataObject(newFormId, pageByFormIdAndSiteId.get(0).getId(), ObjectTypeEnum.HEXAGON_PAGE.getType(), activityEntity.getEa(), activityEntity.getCreateBy(), null, null, null);
            // 将表单绑定至会议下
            customizeFormDataManager.bindCustomizeFormDataObject(newFormId, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa(), activityEntity.getCreateBy(), null, null, null);
        } catch (Exception e) {
            log.info("ConferenceManager.createDefaultConferenceSite fail, e:{}", e);
            // 删除全部数据(微页面与表单)
            hexagonSiteDAOManager.deleteById(activityEntity.getEa(), defaultConferenceSiteIdContainer.getNewSiteId());
            hexagonPageDAO.deleteById(defaultConferenceSiteIdContainer.getNewIndexPageId());
            if (!hexagonPageIds.isEmpty()) {
                hexagonPageDAO.deleteByIds(hexagonPageIds);
            }
            customizeFormDataDAOManager.deleteCustomizeFormData(activityEntity.getEa(), newFormId);
            contentMarketingEventMaterialRelationDAO.deleteContentMarketingEventMaterialRelationById(defaultConferenceSiteIdContainer.getContentMarketingEventMaterialRelationId());
            conferenceDAO.updateConferenceDetailSiteId(activityEntity.getId(), null);
            customizeFormDataManager.unBindCustomizeFormDataObject(defaultConferenceSiteIdContainer.getNewFormPageId(), ObjectTypeEnum.HEXAGON_PAGE.getType(), activityEntity.getEa());
            customizeFormDataManager.unBindCustomizeFormDataObject(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa());
        }
    }

    public void createConferenceSite(String conferenceId,String marketingTemplateId) {
        ActivityEntity activityEntity = activityDAO.getById(conferenceId);
        if (activityEntity == null || StringUtils.isNotBlank(activityEntity.getActivityDetailSiteId())) {
            return;
        }
        // id 集合
        DefaultConferenceSiteIdContainer defaultConferenceSiteIdContainer = new DefaultConferenceSiteIdContainer();
        String newFormId = defaultConferenceSiteIdContainer.getNewFormId();
        List<String> hexagonPageIds = new ArrayList<>();
        try {
            // 处理站点数据
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(conferenceTemplateSiteId);
            if (hexagonSiteEntity == null) {
                return;
            }
            HexagonSiteEntity saveSiteData = new HexagonSiteEntity();
            saveSiteData.setId(defaultConferenceSiteIdContainer.getNewSiteId());
            saveSiteData.setEa(activityEntity.getEa());
            saveSiteData.setName(activityEntity.getTitle());
            saveSiteData.setStatus(HexagonStatusEnum.NORMAL.getType());
            saveSiteData.setCreateBy(activityEntity.getCreateBy());
            saveSiteData.setUpdateBy(activityEntity.getCreateBy());
            saveSiteData.setCreateTime(activityEntity.getCreateTime());
            saveSiteData.setUpdateTime(activityEntity.getCreateTime());
            saveSiteData.setSystemSite(true);

            // 处理首页
            HexagonPageEntity indexPageEntity = handleIndexPage(defaultConferenceSiteIdContainer, activityEntity);
            // 【市场活动设置】中设置的模板，若有则按设置处理，无则默认模板
            HexagonTemplateSiteEntity hexagonTemplateSite = null;
            if (Strings.isNullOrEmpty(marketingTemplateId)) {
                hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(activityEntity.getEa(), HexagonTemplateTypeEnum.CONFERENCE.getType());
            }else{
                hexagonTemplateSite = hexagonTemplateSiteDAO.getById(marketingTemplateId);
            }
            if (hexagonTemplateSite != null) {
                List<HexagonTemplatePageEntity> templatePages = hexagonTemplatePageDAO.getBySiteId(hexagonTemplateSite.getId());
                HexagonSiteListDTO formInfo = hexagonTemplateSiteDAO.getFormByTemplateSiteId(hexagonTemplateSite.getId());
                Map<String, String> buttonInsideAction = new HashMap<>();
                for (int i = 0; i < templatePages.size(); i++) {
                    HexagonTemplatePageEntity templatePage = templatePages.get(i);
                    Map<String, Object> copyResult = hexagonSiteManager.copyPageFromTemplate(activityEntity.getEa(), templatePage, defaultConferenceSiteIdContainer.getNewSiteId(), formInfo, newFormId, buttonInsideAction);
                    String newPageId = (String) copyResult.get("newPageId");
                    hexagonPageIds.add(newPageId);
                    if (i == 0) {
                        String indexContent = indexPageEntity.getContent();
                        indexContent = indexContent.replace(defaultConferenceSiteIdContainer.getNewFormPageId(), newPageId);
                        indexPageEntity.setContent(indexContent);
                    }
                    newFormId = (String) copyResult.get("newFormId");
                }
            } else {
                // 处理表单报名页
                HexagonPageEntity formPageEntity = handleFormPage(defaultConferenceSiteIdContainer, activityEntity);
                // 报名成功页
                HexagonPageEntity successPageEntity = handleSuccessPage(defaultConferenceSiteIdContainer, activityEntity);
                hexagonPageDAO.insert(formPageEntity);
                hexagonPageIds.add(formPageEntity.getId());
                hexagonPageDAO.insert(successPageEntity);
                hexagonPageIds.add(successPageEntity.getId());
            }

            // 数据保存与绑定
            hexagonSiteDAOManager.insert(saveSiteData);
            hexagonPageDAO.insert(indexPageEntity);
            conferenceDAO.updateConferenceDetailSiteId(activityEntity.getId(), defaultConferenceSiteIdContainer.getNewSiteId());
            ContentMarketingEventMaterialRelationEntity contentMarketingEventMaterialRelationEntity = new ContentMarketingEventMaterialRelationEntity();
            contentMarketingEventMaterialRelationEntity.setId(defaultConferenceSiteIdContainer.getContentMarketingEventMaterialRelationId());
            contentMarketingEventMaterialRelationEntity.setEa(activityEntity.getEa());
            contentMarketingEventMaterialRelationEntity.setObjectId(defaultConferenceSiteIdContainer.getNewSiteId());
            contentMarketingEventMaterialRelationEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
            contentMarketingEventMaterialRelationEntity.setMarketingEventId(activityEntity.getMarketingEventId());
            contentMarketingEventMaterialRelationEntity.setIsApplyObject(true);
            contentMarketingEventMaterialRelationEntity.setIsMobileDisplay(true);
            contentMarketingEventMaterialRelationDAO.save(contentMarketingEventMaterialRelationEntity);
            contentMarketingEventMaterialRelationDAO.updateIsApplyObject(defaultConferenceSiteIdContainer.getContentMarketingEventMaterialRelationId(), true);
            List<HexagonPageEntity> pageByFormIdAndSiteId = hexagonPageDAO.getPageByFormIdAndSiteId(saveSiteData.getId(), newFormId);
            // 将表单绑定微页面下
            customizeFormDataManager.bindCustomizeFormDataObject(newFormId, pageByFormIdAndSiteId.get(0).getId(), ObjectTypeEnum.HEXAGON_PAGE.getType(), activityEntity.getEa(), activityEntity.getCreateBy(), null, null, null);
            // 将表单绑定至会议下
            customizeFormDataManager.bindCustomizeFormDataObject(newFormId, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa(), activityEntity.getCreateBy(), null, null, null);
        } catch (Exception e) {
            log.info("ConferenceManager.createDefaultConferenceSite fail, e:{}", e);
            // 删除全部数据(微页面与表单)
            hexagonSiteDAOManager.deleteById(activityEntity.getEa(), defaultConferenceSiteIdContainer.getNewSiteId());
            hexagonPageDAO.deleteById(defaultConferenceSiteIdContainer.getNewIndexPageId());
            if (!hexagonPageIds.isEmpty()) {
                hexagonPageDAO.deleteByIds(hexagonPageIds);
            }
            customizeFormDataDAOManager.deleteCustomizeFormData(activityEntity.getEa(), newFormId);
            contentMarketingEventMaterialRelationDAO.deleteContentMarketingEventMaterialRelationById(defaultConferenceSiteIdContainer.getContentMarketingEventMaterialRelationId());
            conferenceDAO.updateConferenceDetailSiteId(activityEntity.getId(), null);
            customizeFormDataManager.unBindCustomizeFormDataObject(defaultConferenceSiteIdContainer.getNewFormPageId(), ObjectTypeEnum.HEXAGON_PAGE.getType(), activityEntity.getEa());
            customizeFormDataManager.unBindCustomizeFormDataObject(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa());
        }
    }

    private HexagonPageEntity handleIndexPage(DefaultConferenceSiteIdContainer defaultConferenceSiteIdContainer, ActivityEntity activityEntity) throws Exception {
        String headPic = "";
        PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId(),activityEntity.getEa());
        if (photoEntity != null) {
            headPic = fileV2Manager.getSpliceUrl(activityEntity.getEa(), photoEntity.getPath());
        } else {
            headPic = fileV2Manager.getSpliceUrl(null, defaultConferenceCover);
        }
        HexagonPageEntity templatePageEntity = hexagonPageDAO.getById(conferenceTemplateIndexPageId);
        if (templatePageEntity == null) {
            throw new Exception(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2682));
        }
        HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
        hexagonPageEntity.setId(defaultConferenceSiteIdContainer.getNewIndexPageId());
        hexagonPageEntity.setEa(activityEntity.getEa());
        hexagonPageEntity.setHexagonSiteId(defaultConferenceSiteIdContainer.getNewSiteId());
        hexagonPageEntity.setName(activityEntity.getTitle());
        hexagonPageEntity.setShareTitle(activityEntity.getTitle());
        hexagonPageEntity.setShareDesc(templatePageEntity.getShareDesc().replace(ConferenceReplaceConstants.conferenceStartTime, DateUtil.format3(activityEntity.getStartTime()))
                .replace(ConferenceReplaceConstants.conferenceLocation, StringUtils.isNotBlank(activityEntity.getLocation()) ? activityEntity.getLocation() : ""));
        if (photoEntity != null) {
            hexagonPageEntity.setSharePicMpApath(photoEntity.getPath());
            hexagonPageEntity.setSharePicH5Apath(photoEntity.getPath());
        }
        String newContent = replaceContent(defaultConferenceSiteIdContainer, activityEntity, templatePageEntity.getContent(), headPic);
        hexagonPageEntity.setContent(newContent);
        hexagonPageEntity.setIsHomepage(templatePageEntity.getIsHomepage());
        hexagonPageEntity.setFormId(null);
        hexagonPageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
        hexagonPageEntity.setCreateBy(activityEntity.getCreateBy());
        hexagonPageEntity.setUpdateBy(activityEntity.getCreateBy());
        hexagonPageEntity.setCreateTime(activityEntity.getCreateTime());
        hexagonPageEntity.setUpdateTime(activityEntity.getUpdateTime());
        return hexagonPageEntity;
    }

    private HexagonPageEntity handleFormPage(DefaultConferenceSiteIdContainer defaultConferenceSiteIdContainer, ActivityEntity activityEntity) throws Exception {
        HexagonPageEntity templatePageEntity = hexagonPageDAO.getById(conferenceTemplateFormPageId);
        if (templatePageEntity == null) {
            throw new Exception(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2711));
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(conferenceTemplateFormId);
        if (customizeFormDataEntity == null) {
            throw new Exception(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2715));
        }
        // 复制表单
        customizeFormDataEntity.getFormHeadSetting().setName(activityEntity.getTitle() + I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2718));
        customizeFormDataEntity.getFormHeadSetting().setTitle(activityEntity.getTitle() + I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2718));
        CustomizeFormDataEntity saveData = new CustomizeFormDataEntity();
        saveData.setId(defaultConferenceSiteIdContainer.getNewFormId());
        saveData.setEa(activityEntity.getEa());
        saveData.setType(CustomizeFormDataTypeEnum.HEXAGON.getValue());
        saveData.setFormUsage(FormDataUsage.COLLECT_LEADS.getUsage());
        saveData.setFormHeadSetting(customizeFormDataEntity.getFormHeadSetting());
        saveData.setFormBodySetting(customizeFormDataEntity.getFormBodySetting());
        saveData.setFormFootSetting(customizeFormDataEntity.getFormFootSetting());
        saveData.setFormMoreSetting(customizeFormDataEntity.getFormMoreSetting());
        saveData.setFormSuccessSetting(customizeFormDataEntity.getFormSuccessSetting());
        saveData.setCrmApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
        saveData.setStatus(CustomizeFormDataStatusEnum.NORMAL.getValue());
        saveData.setCreateBy(activityEntity.getCreateBy());
        saveData.setUpdateBy(activityEntity.getCreateBy());
        saveData.setCreateTime(activityEntity.getCreateTime());
        saveData.setUpdateTime(activityEntity.getUpdateTime());
        customizeFormDataDAOManager.insertCustomizeFormData(saveData);

        // 复制微页面
        HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
        hexagonPageEntity.setId(defaultConferenceSiteIdContainer.getNewFormPageId());
        hexagonPageEntity.setEa(activityEntity.getEa());
        hexagonPageEntity.setHexagonSiteId(defaultConferenceSiteIdContainer.getNewSiteId());
        hexagonPageEntity.setName(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2718));
        String newContent = replaceContent(defaultConferenceSiteIdContainer, activityEntity, templatePageEntity.getContent(), null);
        hexagonPageEntity.setContent(newContent);
        hexagonPageEntity.setIsHomepage(templatePageEntity.getIsHomepage());
        hexagonPageEntity.setFormId(defaultConferenceSiteIdContainer.getNewFormId());
        hexagonPageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
        hexagonPageEntity.setCreateBy(activityEntity.getCreateBy());
        hexagonPageEntity.setUpdateBy(activityEntity.getCreateBy());
        hexagonPageEntity.setCreateTime(activityEntity.getCreateTime());
        hexagonPageEntity.setUpdateTime(activityEntity.getUpdateTime());
        return hexagonPageEntity;
    }

    private HexagonPageEntity handleSuccessPage(DefaultConferenceSiteIdContainer defaultConferenceSiteIdContainer, ActivityEntity activityEntity) throws Exception {
        HexagonPageEntity templatePageEntity = hexagonPageDAO.getById(conferenceTemplateSuccessPageId);
        if (templatePageEntity == null) {
            throw new Exception(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2759));
        }
        HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
        hexagonPageEntity.setId(defaultConferenceSiteIdContainer.getNewSuccessPageId());
        hexagonPageEntity.setEa(activityEntity.getEa());
        hexagonPageEntity.setHexagonSiteId(defaultConferenceSiteIdContainer.getNewSiteId());
        hexagonPageEntity.setName(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2765));
        String newContent = replaceContent(defaultConferenceSiteIdContainer, activityEntity, templatePageEntity.getContent(), null);
        hexagonPageEntity.setContent(newContent);
        hexagonPageEntity.setIsHomepage(templatePageEntity.getIsHomepage());
        hexagonPageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
        hexagonPageEntity.setCreateBy(activityEntity.getCreateBy());
        hexagonPageEntity.setUpdateBy(activityEntity.getCreateBy());
        hexagonPageEntity.setCreateTime(activityEntity.getCreateTime());
        hexagonPageEntity.setUpdateTime(activityEntity.getUpdateTime());
        return hexagonPageEntity;
    }

    private String replaceContent(DefaultConferenceSiteIdContainer defaultConferenceSiteIdContainer, ActivityEntity activityEntity, String content, String headPic) {
        // 该企业是否开启会员
        String quotesRegex = "^\"|\"$";
        boolean openMember = memberManager.isOpenMember(activityEntity.getEa());
        String successIconUrl = fileV2Manager.getSpliceUrl(null, conferenceSuccessIconPath);
        MemberConfigEntity memberConfigEntity = null;
        if (openMember) {
            memberConfigEntity = memberConfigDao.getByEa(activityEntity.getEa());
        }
        if (defaultConferenceSiteIdContainer != null) {
            content = content.replace(ConferenceReplaceConstants.conferenceIndexPageId, defaultConferenceSiteIdContainer.getNewIndexPageId())
                    .replace(ConferenceReplaceConstants.conferenceFormPageId, defaultConferenceSiteIdContainer.getNewFormPageId())
                    .replace(ConferenceReplaceConstants.conferenceSuccessPageId, defaultConferenceSiteIdContainer.getNewSuccessPageId());
        }
        content = content
                .replace(ConferenceReplaceConstants.conferenceName, activityEntity.getTitle())
                .replace(ConferenceReplaceConstants.conferenceStartTime, DateUtil.format3(activityEntity.getStartTime()))
                .replace(ConferenceReplaceConstants.conferenceEndTime, DateUtil.format3(activityEntity.getEndTime()))
                .replace(ConferenceReplaceConstants.conferenceLocation, StringUtils.isNotBlank(activityEntity.getLocation()) ? activityEntity.getLocation() : "")
                .replace(ConferenceReplaceConstants.conferenceHeadPic, StringUtils.isNotBlank(headPic) ? headPic : "")
                .replace(ConferenceReplaceConstants.conferenceId, activityEntity.getId())
                .replace(ConferenceReplaceConstants.conferenceMarketingEventId, activityEntity.getMarketingEventId())
                .replace(ConferenceReplaceConstants.memberHasConfig, (openMember && memberConfigEntity != null) ? "true" : "false")
                .replace(ConferenceReplaceConstants.memberLoginId, (openMember && memberConfigEntity != null) ? memberConfigEntity.getLoginSiteId() : "")
                .replace(ConferenceReplaceConstants.memberRegisteId, (openMember && memberConfigEntity != null) ? memberConfigEntity.getRegistrationSiteId() : "")
                .replace(ConferenceReplaceConstants.memberPageType, (openMember && memberConfigEntity != null) ? "memberPage" : "")
                .replace(ConferenceReplaceConstants.memberLoginName, "会员登录页")
                .replace(ConferenceReplaceConstants.mapAddress, activityEntity.getMapAddress() == null ? "" : activityEntity.getMapAddress())
                .replace(ConferenceReplaceConstants.lat, (activityEntity.getMapLocation() != null && activityEntity.getMapLocation().getLat() != null) ? String.valueOf(activityEntity.getMapLocation().getLat()) : "")
                .replace(ConferenceReplaceConstants.lng, (activityEntity.getMapLocation() != null && activityEntity.getMapLocation().getLng() != null) ? String.valueOf(activityEntity.getMapLocation().getLng()) : "")
                .replace(ConferenceReplaceConstants.memberRegisteName, "会员注册页")
                .replace(ConferenceReplaceConstants.conferenceSuccessIcon, successIconUrl)
                .replace(ConferenceReplaceConstants.conferenceDetail, StringUtils.isNotBlank(activityEntity.getConferenceDetails()) ? GsonUtil.getGson().toJson(conferenceManager.getConferenceDetailsByPath(activityEntity.getConferenceDetails())).replaceAll(quotesRegex, "") : "")
                .replaceAll("\n", "");
        return content;
    }

    public void resetConferenceIndexPage(String conferenceId, boolean isCreateConference) {
        try {
            ActivityEntity activityEntity = activityDAO.getById(conferenceId);
            if (activityEntity == null || StringUtils.isBlank(activityEntity.getActivityDetailSiteId())) {
                return;
            }
            // 获取该微页面首页
            HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activityEntity.getActivityDetailSiteId());
            if (homePage == null) {
                return;
            }
            String content = homePage.getContent();
            String headPic = "";
            PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId(),activityEntity.getEa());
            String picPath = null;
            if (photoEntity != null) {
                picPath = photoEntity.getPath();
                headPic = fileV2Manager.getSpliceUrl(activityEntity.getEa(), photoEntity.getPath());
            } else {
                picPath = defaultConferenceCover;
                headPic = fileV2Manager.getSpliceUrl(null, defaultConferenceCover);
            }
            // 将原微页面信息修改
            for (ConferenceReplaceConstants.BlockDataEnum blockDataEnum : ConferenceReplaceConstants.BlockDataEnum.values()) {
                String value = blockDataEnum.getValue();
                value = replaceContent(null, activityEntity, value, headPic);
                value = value.replace("\"", "\\\"");
                if (isCreateConference || (!skipConfenrecHexagonContentItem(blockDataEnum))) {
                    content = ConferenceReplaceConstants.BlockDataEnum.conversionToRegular(blockDataEnum, content, value);
                }
            }
            if (isCreateConference) {
                homePage.setShareTitle(activityEntity.getTitle());
                homePage.setShareDesc(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2847) + DateUtil.format3(activityEntity.getStartTime()) + "\n" + I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_2847_1) + activityEntity.getLocation());
            }
            if (StringUtils.isBlank(homePage.getShareTitle())) {
                homePage.setShareTitle(activityEntity.getTitle());
            }
            homePage.setSharePicH5Apath(picPath);
            homePage.setSharePicMpApath(picPath);
            hexagonPageDAO.updateContent(homePage.getId(), content);
            hexagonPageDAO.updateShareInfo(homePage.getId(), homePage.getShareTitle(), homePage.getShareDesc(), picPath);
            redisManager.deleteHexgonPage(homePage.getId());
            redisManager.deleteHexgonSite(activityEntity.getActivityDetailSiteId());
            redisManager.deleteHexagonHomePage(activityEntity.getActivityDetailSiteId());
        } catch (Exception e) {
            log.warn("ConferenceManager.resetConferenceIndexPage error e:", e);
        }
    }

    public boolean skipConfenrecHexagonContentItem(ConferenceReplaceConstants.BlockDataEnum blockDataEnum){
        if (ConferenceReplaceConstants.BlockDataEnum.MEMBER_AUTO_SIGN_UP == blockDataEnum
        || ConferenceReplaceConstants.BlockDataEnum.CONFERENCE_SHAREOPTS_CONTAINER == blockDataEnum
        || ConferenceReplaceConstants.BlockDataEnum.CONFERENCE_NAME_CONTAINER == blockDataEnum){
            return true;
        }

        return false;
    }

    /**
     * 根据页面重置会议formId
     *
     * @param pageId             页面id
     * @param lastTimeHomeFormId 上一次的主页面id
     */
    public void resetConferenceFormByPage(String pageId, String lastTimeHomeFormId) {
        ThreadPoolUtils.execute(() -> {
            HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getById(pageId);
            if (hexagonPageEntity == null) {
                return;
            }
            ActivityEntity activityEntity = conferenceDAO.getActivityByDetailSiteId(hexagonPageEntity.getHexagonSiteId());
            if (activityEntity == null) {
                return;
            }
            List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(hexagonPageEntity.getHexagonSiteId()));
            CustomizeFormDataEntity conferenceBindForm = customizeFormDataManager.getBindFormDataByObject(activityEntity.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
            if (CollectionUtils.isEmpty(hexagonSiteListDTOList)) {
                // 若会议且绑定是上一个表单则解绑否则不解绑（兼容旧会议表单）
                if (conferenceBindForm != null && conferenceBindForm.getId().equals(lastTimeHomeFormId)) {
                    customizeFormDataManager.unBindCustomizeFormDataObject(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa());
                }
            } else {
                // 若微页面有表单则直接解绑会议表单再绑定
                customizeFormDataManager.bindCustomizeFormDataObject(hexagonSiteListDTOList.get(0).getFormId(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa(),
                        activityEntity.getCreateBy(), null, null, null);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public String getEnrollNameByObjectType(String ea,Integer bindCrmObjectType, String bindCrmObjectId) {
        String apiName = CampaignMergeDataObjectTypeEnum.getApiNameByType(bindCrmObjectType);
        ObjectData objectData = crmV2Manager.getObjectData(ea, -10000, apiName, bindCrmObjectId);
        if (objectData != null) {
            return objectData.getName();
        }
        return null;
    }

    public Map<String, String> getEnrollNameMapByObjects(String ea,Integer bindCrmObjectType, List<String> bindCrmObjectIds){
        if (CollectionUtils.isEmpty(bindCrmObjectIds)){
            return null;
        }

        String apiName = CampaignMergeDataObjectTypeEnum.getApiNameByType(bindCrmObjectType);
        List<List<String>> partitionList = Lists.partition(bindCrmObjectIds, 2000);
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(apiName);
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", "name"));
        int pageSize = 2000;
        List<ObjectData> objectDataList = Lists.newArrayList();
        for (List<String> partition : partitionList) {
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), partition);
            queryFilterArg.setQuery(paasQueryArg);
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            if (totalCount <= 0) {
                continue;
            }
            int count = 0;
            String lastId = null;
            while (count < totalCount) {
                InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
                if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
                    break;
                }
                int size = innerPage.getDataList().size();
                count += size;
                lastId = innerPage.getDataList().get(size - 1).getId();
                objectDataList.addAll(innerPage.getDataList());
            }
        }
        if (CollectionUtils.isEmpty(objectDataList)){
            return null;
        }
        return objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2)->v2));
    }
    public Map<String, String> getEnrollNameByObjectType(String ea,Map<Integer, List<String>> bingCrmObjectTypeToIdMap) {
        Map<String, String> resultMap = Maps.newHashMap();
        if (MapUtils.isEmpty(bingCrmObjectTypeToIdMap)) {
            return resultMap;
        }
        bingCrmObjectTypeToIdMap.forEach((crmObjectType, objectIdList) -> {
            String apiName = CampaignMergeDataObjectTypeEnum.getApiNameByType(crmObjectType);
            if (StringUtils.isNotBlank(apiName)) {
                List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, apiName, Lists.newArrayList("_id", "name"),objectIdList);
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    objectDataList.forEach(e -> resultMap.put(e.getId(), e.getName()));
                }
            }
        });
        return resultMap;
    }

    public Map<String,String> getExtraDataNameByObjectIds(String ea, List<String> extraDataIds, String crmApiName) {
        Map<String, String> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(extraDataIds)) {
            return resultMap;
        }
        List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, crmApiName, Lists.newArrayList("_id", "name"),extraDataIds);
        objectDataList.forEach(e -> resultMap.put(e.getId(), e.getName()));
        return resultMap;
    }


    @Data
    private static class DefaultConferenceSiteIdContainer implements Serializable {
        private String newSiteId = UUIDUtil.getUUID();
        private String newIndexPageId = UUIDUtil.getUUID();
        private String newFormPageId = UUIDUtil.getUUID();
        private String newSuccessPageId = UUIDUtil.getUUID();
        private String newFormId = UUIDUtil.getUUID();
        private String contentMarketingEventMaterialRelationId = UUIDUtil.getUUID();
    }

    /**
     * 会议统计数据
     * @param ea
     * @param marketingEventId
     * @return
     */
    public Result<GetConferenceStatisticDataResult> getConferenceStatistic(String ea, String marketingEventId) {
        ActivityEntity activityEntity = activityDAO.getActivityEntityByMarketingEventId(ea, marketingEventId);
        if (activityEntity == null) {
            log.warn("ConferenceManager -> getConferenceStatistic error activityEntity is null ea:{}, marketingEventId:{}", ea, marketingEventId);
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        GetConferenceStatisticDataResult result = new GetConferenceStatisticDataResult();
        CampaignStatisticDTO campaignStatisticDTO = campaignMergeDataDAO.getCampaignStatisticData(ea, marketingEventId);
        if (campaignStatisticDTO == null) {
            result.setEnrollCount(0);
            result.setSignInCount(0);
            result.setReviewCount(0);
            result.setSignInRatio("0%");
            result.setNewLeadCount(0);
            result.setInviteCount(0);
            result.setNotInviteCount(0);
            result.setErrorDataCount(0);
            result.setUv(0);
            return Result.newSuccess(result);
        }
        result.setEnrollCount(campaignStatisticDTO.getCampaignCount());
        result.setInviteCount(campaignStatisticDTO.getInvitedCount());
        result.setNotInviteCount(campaignStatisticDTO.getNotInvitedCount());
        result.setReviewCount(campaignStatisticDTO.getPendingReviewCount());
        result.setNewLeadCount(campaignStatisticDTO.getBindCampaignObjCount());
        result.setSignInCount(campaignStatisticDTO.getSignInCount());
        result.setErrorDataCount(campaignStatisticDTO.getErrorDataCount());
        result.setSignInRatio(NumberUtil.getPercentage(campaignStatisticDTO.getSignInCount(), campaignStatisticDTO.getCampaignCount(), 0));
        // 设置渠道数据
        Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(ea);
        if (MapUtils.isEmpty(channelValueMap)) {
            return Result.newSuccess(result);
        }
        Map<String, Integer> channelResult = Maps.newHashMap();
        List<CampaignStatisticDTO> campaignStatisticDTOList = campaignMergeDataDAO.getChannelValueStatisticData(ea, marketingEventId);
        if (CollectionUtils.isNotEmpty(campaignStatisticDTOList)) {
            for (CampaignStatisticDTO data : campaignStatisticDTOList) {
                String name = spreadChannelManager.getChannelLabelByChannelValue(ea, channelValueMap, data.getChannelValue());
                if (StringUtils.isBlank(name)) {
                    name = "--";
                }
                channelResult.put(name, data.getChannelValueCount());
            }
        }
        if (MapUtils.isNotEmpty(channelResult)) {
            result.setChannelStatistic(channelResult);
        }
        channelValueMap.clear();
        campaignStatisticDTOList.clear();
        com.facishare.marketing.statistic.common.result.Result<Map<String, Integer>> uVMaps = marketingEventStatisticService.getUVs(ea, com.google.common.collect.Sets.newHashSet(marketingEventId));
        if (uVMaps.getCode() == 0 && MapUtils.isNotEmpty(uVMaps.getData())) {
            result.setUv(uVMaps.getData().get(marketingEventId));
        }
        return Result.newSuccess(result);
    }

    public Result<GetSignInSettingResult> getSignInSetting(String confecenceId){
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(confecenceId);
        if (activityEntity == null) {
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        GetSignInSettingResult getSignInSettingResult = new GetSignInSettingResult();
        ConferenceSignInJumpSettingEntity jumpSettingInfo = conferenceSignInJumpSettingDAO.getJumpSettingInfo(activityEntity.getEa(), activityEntity.getId());
        if (jumpSettingInfo == null) {
            // 默认跳转会议
            getSignInSettingResult.setJumpObjectId(activityEntity.getId());
            getSignInSettingResult.setJumpObjectType(ObjectTypeEnum.ACTIVITY.getType());
        } else {
            getSignInSettingResult.setJumpObjectId(jumpSettingInfo.getJumpObjectId());
            getSignInSettingResult.setJumpObjectType(jumpSettingInfo.getJumpObjectType());
            getSignInSettingResult.setJumpUrl(jumpSettingInfo.getJumpUrl());
        }
        return Result.newSuccess(getSignInSettingResult);
    }

    public Result<UpdateSignInSuccessSettingArg> getSignInSuccessSetting(String confecenceId) {
        ConferenceSignInSuccessSetting entity = new ConferenceSignInSuccessSetting();
        entity.setConferenceId(confecenceId);
        UpdateSignInSuccessSettingArg result = new UpdateSignInSuccessSettingArg();
        List<ConferenceSignInSuccessSetting> byEntity = conferenceSignInSuccessSettingDAO.findByEntity(entity);
        if (null != byEntity && !byEntity.isEmpty()) {
            BeanUtils.copyProperties(byEntity.get(0), result);
            result.setPhoneSign(result.getPhoneSign() == null || result.getPhoneSign());
            result.setMiniAppComponent(result.getMiniAppComponent() != null && result.getMiniAppComponent());
            result.setEmailSign(result.getEmailSign() != null && result.getEmailSign());
        }
        //处理跳转推广内容
        Result<GetSignInSettingResult> signInSettingResult = this.getSignInSetting(confecenceId);
        if (signInSettingResult.isSuccess() && signInSettingResult.getData() != null) {
            result.setJumpObjectId(signInSettingResult.getData().getJumpObjectId());
            result.setJumpObjectType(signInSettingResult.getData().getJumpObjectType());
            result.setJumpUrl(signInSettingResult.getData().getJumpUrl());
        }
        //处理引流二维码
        if (StringUtils.isNotBlank(result.getSignSuccessQrcodeId())) {
            QrCodeSignResult qrCodeSignResult = handleConferenceQrCodeName(result.getSignSuccessQrcodeId(), result.getSignSuccessQrcodeType(), result.getSignSuccessWxAppId(), result.getEa());
            if (qrCodeSignResult != null) {
                result.setSignSuccessQrcodeName(qrCodeSignResult.getQrCodeName());
                result.setSignSuccessQrcodeUrl(qrCodeSignResult.getQrCodeUrl());
            }
        }
        if (StringUtils.isNotBlank(result.getAuditingQrcodeId())) {
            QrCodeSignResult qrCodeSignResult = handleConferenceQrCodeName(result.getAuditingQrcodeId(), result.getAuditingQrcodeType(), result.getAuditingWxAppId(), result.getEa());
            if (qrCodeSignResult != null) {
                result.setAuditingQrcodeName(qrCodeSignResult.getQrCodeName());
                result.setAuditingQrcodeUrl(qrCodeSignResult.getQrCodeUrl());
            }
        }
        if (StringUtils.isNotBlank(result.getAuditFailQrcodeId())) {
            QrCodeSignResult qrCodeSignResult = handleConferenceQrCodeName(result.getAuditFailQrcodeId(), result.getAuditFailQrcodeType(), result.getAuditFailWxAppId(), result.getEa());
            if (qrCodeSignResult != null) {
                result.setAuditFailQrcodeName(qrCodeSignResult.getQrCodeName());
                result.setAuditFailQrcodeUrl(qrCodeSignResult.getQrCodeUrl());
            }
        }
        return Result.newSuccess(result);
    }

    @Data
    public static class QrCodeSignResult implements Serializable {
        @ApiModelProperty("二维码名称")
        private String qrCodeName;
        @ApiModelProperty("二维码地址")
        private String qrCodeUrl;
    }

    private QrCodeSignResult handleConferenceQrCodeName(String qrCodeId, Integer qrCodeType,String wxAppId,String ea) {
        QrCodeSignResult qrCodeSignResult = new QrCodeSignResult();
        if (qrCodeType == 1) {
            QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
            queryStoreQrCodeArg.setAppId(wxAppId);
            queryStoreQrCodeArg.setSceneId(Long.valueOf(qrCodeId));
            queryStoreQrCodeArg.setEnterpriseAccount(ea);
            ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
            if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
                log.info("ConferenceManager handleConferenceQrCodeName wechatQrCodeRestService.queryStoreQrCode fail, pagerModelResult:{}", pagerModelResult);
                return null;
            }
            QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
            if (qrCodeResult != null) {
                qrCodeSignResult.setQrCodeName(qrCodeResult.getQrCodeName());
                qrCodeSignResult.setQrCodeUrl(qrCodeResult.getShowUrl());
            }
            return qrCodeSignResult;
        } else if (qrCodeType == 2) {
            QywxAddFanQrCodeEntity  qywxAddFanQrCodeEntity = qywxAddFanQrCodeDAO.getById(qrCodeId);
            if (qywxAddFanQrCodeEntity != null) {
                qrCodeSignResult.setQrCodeName(qywxAddFanQrCodeEntity.getQrCodeName());
                qrCodeSignResult.setQrCodeUrl(qywxAddFanQrCodeEntity.getQrCodeUrl());
            }
            return qrCodeSignResult;
        }
        return null;
    }

    public Result<GetSimpleConferenceDetail> getSimpleDetail(String conferenceId){
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(conferenceId);
        if (activityEntity == null) {
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        GetSimpleConferenceDetail result = new GetSimpleConferenceDetail();
        result.setStartTime(activityEntity.getStartTime().getTime());
        result.setEndTime(activityEntity.getEndTime().getTime());

        if (null != activityEntity.getEnrollEndTime()) {
            result.setEnrollEndTime(activityEntity.getEnrollEndTime().getTime());
        }
        result.setState(getConferenceStatuByStartTime(activityEntity.getStartTime(), activityEntity.getEndTime()));
        PhotoEntity photoEntity = photoManager.querySinglePhotoByEa(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId(), null);
        if (photoEntity != null) {
            result.setCoverImageUrl(photoEntity.getUrl());
            result.setCoverImageSmallUrl(org.apache.commons.lang3.StringUtils.isBlank(photoEntity.getThumbnailUrl()) ? photoEntity.getUrl() : photoEntity.getThumbnailUrl());
        }else {
            String defaultCoverPath = defaultConferenceCover;
            String defaultCoverUrl = fileV2Manager.getUrlByPath(defaultCoverPath, activityEntity.getEa(), false);
            result.setCoverImageUrl(defaultCoverUrl);
            result.setCoverImageSmallUrl(defaultCoverUrl);
        }

        String activitySiteId = activityEntity.getActivityDetailSiteId();
        if (StringUtils.isNotEmpty(activitySiteId)) {
            HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activitySiteId);
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), homePage.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), homePage.getId());
            if (coverCutH5PhotoEntity != null) {
                result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), homePage.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }

        result.setFlowStatus(getConferenceTimeFlowStatus(activityEntity));
        result.setTitle(activityEntity.getTitle());
        result.setLocation(activityEntity.getLocation());
        result.setScale(activityEntity.getScale());
        result.setEnrollCount(activityEntity.getEnrollCount());
        result.setMarketingEventId(activityEntity.getMarketingEventId());

        return Result.newSuccess(result);
    }

    public int getConferenceTimeFlowStatus(ActivityEntity activityEntity) {
        if (activityEntity.getStatus().equals(ActivityStatusEnum.DELETED.getStatus())) {
            return ConferenceTimeFlowStatusEnum.DELETED.getStatus();
        }
        if (activityEntity.getStatus().equals(ActivityStatusEnum.DISABLED.getStatus())) {
            return ConferenceTimeFlowStatusEnum.DISABLED.getStatus();
        }
        if (activityEntity.getStatus().equals(ActivityStatusEnum.UNPUBLISHED.getStatus())) {
            if (canPublicConference(activityEntity)) {
                return ConferenceTimeFlowStatusEnum.UNPUBLISHED.getStatus();
            } else {
                return ConferenceTimeFlowStatusEnum.UNPUBLISHED_NO.getStatus();
            }
        }
        long current = System.currentTimeMillis();
        if (current < activityEntity.getStartTime().getTime()) {
            return ConferenceTimeFlowStatusEnum.NOT_STARTED.getStatus();
        } else if (activityEntity.getStartTime().getTime() <= current && current < activityEntity.getEndTime().getTime()) {
            return ConferenceTimeFlowStatusEnum.PROCESSING.getStatus();
        } else {
            return ConferenceTimeFlowStatusEnum.END.getStatus();
        }
    }

    /**
     * 是否能够发布会议
     */
    public boolean canPublicConference(ActivityEntity activityEntity) {
        if (activityEntity == null) {
            return false;
        }
        // 尚未设置会议详情
        if (org.apache.commons.lang3.StringUtils.isBlank(activityEntity.getConferenceDetails())) {
            return false;
        }
        // 尚未绑定表单
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(activityEntity.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        return customizeFormDataEntity != null;
    }

    private Integer getConferenceStatuByStartTime(Date startTime, Date endTime) {
        Date now = new Date();
        if (startTime != null && now.getTime() < startTime.getTime()) {
            return 2;
        } else if (startTime != null && endTime != null && now.getTime() >= startTime.getTime() && now.getTime() <= endTime.getTime()) {
            return 3;
        } else if (endTime != null && now.getTime() > endTime.getTime()) {
            return 4;
        } else {
            return 0;
        }
    }

    public String createConferenceSiteV2(String conferenceId, String marketingTemplateId,String marketingEventId,String title) {
        try {
            ActivityEntity activityEntity = activityDAO.getById(conferenceId);
            if (activityEntity == null || StringUtils.isNotBlank(activityEntity.getActivityDetailSiteId())) {
                return null;
            }

            // 1. 获取模板信息
            HexagonTemplateSiteEntity hexagonTemplateSite;
            if (StringUtils.isBlank(marketingTemplateId)) {
                hexagonTemplateSite = hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(
                    activityEntity.getEa(),
                    HexagonTemplateTypeEnum.CONFERENCE.getType()
                );
            } else {
                hexagonTemplateSite = hexagonTemplateSiteDAO.getById(marketingTemplateId);
            }

            if (hexagonTemplateSite == null) {
                log.error("模板不存在, conferenceId={}, marketingTemplateId={}", conferenceId, marketingTemplateId);
                return null;
            }

            // 2. 复制微页面
            HexagonCopyArg arg = new HexagonCopyArg();
            arg.setId(hexagonTemplateSite.getId());
            arg.setName(activityEntity.getTitle());

            Result<CreateSiteResult> siteResult = hexagonManager.hexagonCopySite(
                activityEntity.getEa(),
                activityEntity.getCreateBy(),
                arg,
                HexagonManager.COPY_FROM_TEMPLATE
            );
            if (siteResult.getData() == null || !siteResult.isSuccess()) {
                log.error("复制微页面失败, conferenceId={}, templateId={}, result={}",
                    conferenceId, hexagonTemplateSite.getId(), siteResult);
                return null;
            }
            hexagonSiteDAO.updateHexagonSiteSystemStatus(activityEntity.getEa(), siteResult.getData().getId(), true);
            String newSiteId = siteResult.getData().getId();
            String newFormId = siteResult.getData().getFormId();
            String relationEntityId = null;

            try {
                // 3. 更新会议关联的微页面ID
                conferenceDAO.updateConferenceDetailSiteId(activityEntity.getId(), newSiteId);

                HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getHomePage(newSiteId);
                String content = hexagonPageEntity.getContent();
                String replacedContent = content.replace("!!marketingEventId!!", marketingEventId).replace("!!marketingEventTitle!!", title);
                HexagonPageEntity pageEntity = new HexagonPageEntity();
                pageEntity.setId(hexagonPageEntity.getId());
                pageEntity.setContent(replacedContent);
                pageEntity.setName(title);
                pageEntity.setEa(activityEntity.getEa());
                hexagonPageDAO.update(pageEntity);

                // 4. 创建市场活动物料关联
                ContentMarketingEventMaterialRelationEntity relationEntity = new ContentMarketingEventMaterialRelationEntity();
                relationEntity.setId(UUIDUtil.getUUID());
                relationEntity.setEa(activityEntity.getEa());
                relationEntity.setObjectId(newSiteId);
                relationEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                relationEntity.setMarketingEventId(activityEntity.getMarketingEventId());
                relationEntity.setIsApplyObject(true);
                relationEntity.setIsMobileDisplay(true);
                contentMarketingEventMaterialRelationDAO.insert(relationEntity);
                relationEntityId=relationEntity.getId();
                // 5. 将表单绑定至会议下
                if (StringUtils.isNotBlank(newFormId)) {
                    customizeFormDataManager.bindCustomizeFormDataObject(
                        newFormId,
                        activityEntity.getId(),
                        ObjectTypeEnum.ACTIVITY.getType(),
                        activityEntity.getEa(),
                        activityEntity.getCreateBy(),
                        null, null, null
                    );
                }

                log.info("创建会议微页面成功, conferenceId={}, templateId={}, siteId={}",
                    conferenceId, hexagonTemplateSite.getId(), newSiteId);
                return newSiteId;
            } catch (Exception e) {
                // 6. 异常回滚
                log.error("创建会议微页面关联失败,开始清理数据, conferenceId={}, siteId={}", conferenceId, newSiteId, e);
                hexagonSiteDAOManager.deleteById(activityEntity.getEa(), newSiteId);
                if (StringUtils.isNotBlank(newFormId)) {
                    customizeFormDataManager.unBindCustomizeFormDataObject(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa());
                }
                if(StringUtils.isNotBlank(relationEntityId)){
                    contentMarketingEventMaterialRelationDAO.deleteContentMarketingEventMaterialRelationById(relationEntityId);
                }
                conferenceDAO.updateConferenceDetailSiteId(activityEntity.getId(), null);
            }
        } catch (Exception e) {
            log.error("创建会议微页面异常, conferenceId={}, marketingTemplateId={}", conferenceId, marketingTemplateId, e);
        }
        return null;
    }

    public void initCrmOldConference(String ea, List<MarketingEventsBriefResult> marketingEventsBriefResults){
        if (CollectionUtils.isEmpty(marketingEventsBriefResults)){
            return;
        }
        List<ActivityEntity> conferenceList = Lists.newArrayList();
        for (MarketingEventsBriefResult  marketingEventsBriefResult : marketingEventsBriefResults){
            ActivityEntity conferenceEntity = new ActivityEntity();
            conferenceEntity.setId(UUIDUtil.getUUID());
            conferenceEntity.setEa(ea);
            conferenceEntity.setMarketingEventId(marketingEventsBriefResult.getId());
            conferenceEntity.setTitle(marketingEventsBriefResult.getName());
            Long beginTime = marketingEventsBriefResult.getBeginTime();
            conferenceEntity.setStartTime(beginTime == null ? DateUtil.now() : DateUtil.fromTimestamp(beginTime));
            Long endTime = marketingEventsBriefResult.getEndTime();
            conferenceEntity.setEndTime(endTime == null ? DateUtil.now() : DateUtil.fromTimestamp(endTime));
            conferenceEntity.setStatus(ActivityStatusEnum.ENABLED.getStatus());
            conferenceEntity.setLocation(marketingEventsBriefResult.getLocation());
            conferenceEntity.setCreateBy(marketingEventsBriefResult.getCreateBy());
            conferenceEntity.setUpdateBy(marketingEventsBriefResult.getCreateBy());
            Long createTime = marketingEventsBriefResult.getCreateTime();
            conferenceEntity.setCreateTime(createTime == null ? DateUtil.now() : DateUtil.fromTimestamp(createTime));
            conferenceEntity.setUpdateTime(conferenceEntity.getCreateTime());
            conferenceEntity.setType(ActivityTypeEnum.OFF_LING.type);
            conferenceEntity.setMarketingEventType(marketingEventsBriefResult.getEventType());
            conferenceList.add(conferenceEntity);
        }

        if (CollectionUtils.isNotEmpty(conferenceList)){
            log.info("sync old conference from crm count:{} data:{}", conferenceList.size(), conferenceList);
            conferenceDAOManager.batchAddConference(conferenceList);
            // 创建二维码
            ThreadPoolUtils.execute(new Runnable() {
                @Override
                public void run() {
                    for (ActivityEntity conferenceEntity : conferenceList) {
                        activityManager.createActivityQrCode(conferenceEntity.getId(), conferenceEntity.getEa(), null, null);
                        activityManager.createActivitySignInQrCode(conferenceEntity.getId(), conferenceEntity.getEa(), null);
                        conferenceManager.createConferenceSite(conferenceEntity.getId(),null);
                    }
                }
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
    }
}