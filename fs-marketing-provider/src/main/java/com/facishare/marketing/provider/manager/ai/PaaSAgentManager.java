package com.facishare.marketing.provider.manager.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.drew.lang.annotations.Nullable;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.vo.ai.AgentInvokeRequest;
import com.facishare.marketing.api.vo.ai.AgentReportRequest;
import com.facishare.marketing.api.vo.ai.AgentWelcomeRequest;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.api.result.ai.AgentExecuteResponse;
import com.facishare.marketing.api.result.ai.AgentResponse;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.sse.RedisMQ;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.sharegpt.utils.ObjectDataUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.gson.reflect.TypeToken;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.internal.sse.RealEventSource;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;

@Slf4j
@Component
public class PaaSAgentManager {

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private EIEAConverter converter;
    @Autowired
    private MergeJedisCmd jedisCmd;
    @Getter
    @ReloadableProperty("ai_helpe_paas_agent_base_url")
    private String baseUrl;

    public AgentResponse<AgentResponse.AgentInvokeResult> agentInvoke(String ea, Integer fsUserId, String businessName, AgentInvokeRequest req) {
        Map<String, String> headers = getHeaders(ea, fsUserId, businessName);
        String jsonString = JSON.toJSONString(req);
        log.info("agentInvoke:{}", jsonString);
        RequestBody requestBody = RequestBody.create(null, jsonString);
        String url = baseUrl + "/v1/agent/invoke";
        return httpManager.executePostHttpWithRequestBodyAndHeader(requestBody, url, new TypeToken<AgentResponse<AgentResponse.AgentInvokeResult>>() {
        }, headers);
    }

    public AgentExecuteResponse<AgentExecuteResponse.AgentExecuteResult> agentExecute(String ea, Integer fsUserId, String businessName, AgentInvokeRequest req) {
        Map<String, String> headers = getHeaders(ea, fsUserId, businessName);
        String jsonString = JSON.toJSONString(req);
        log.info("agentExecute:{}", jsonString);
        RequestBody requestBody = RequestBody.create(null, jsonString);
        String url = baseUrl + "/v1/agent/execute";
        return httpManager.executePostHttpWithRequestBodyAndHeader(requestBody, url, new TypeToken<AgentExecuteResponse<AgentExecuteResponse.AgentExecuteResult>>() {
        }, headers);
    }

    public AgentExecuteResponse<AgentExecuteResponse.AgentExecuteResult> treamingAgentExecute(String ea, Integer fsUserId, String businessName, String channel, AgentInvokeRequest req) {
        AgentExecuteResponse.AgentExecuteResult agentResponse = null;
        Map<String, String> headers = getHeaders(ea, fsUserId, businessName);
        String jsonString = JSON.toJSONString(req);
        log.info("treamingAgentInvoke:{}", jsonString);
        RequestBody requestBody = RequestBody.create(MediaType.parse(APPLICATION_JSON_UTF8_VALUE), jsonString);
        Request.Builder requestBuilder = new Request.Builder()
                .url(baseUrl + "/v1/sse/inner/agent/execute")
                .post(requestBody);
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        CompletableFuture<AgentExecuteResponse.AgentExecuteResult> futureResponse = new CompletableFuture<>();
        TraceContext context = TraceContext.get().copyNecessaries();
        EventSourceListener eventSourceListener = new EventSourceListener() {
            AgentExecuteResponse.AgentExecuteResult agentExecuteResult = new AgentExecuteResponse.AgentExecuteResult();

            @Override
            public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type, String data) {
                if (StringUtils.isBlank(data)) {
                    return;
                }
                if (context != null) {
                    TraceContext._set(context);
                }
                try {
                    agentExecuteResult = GsonUtil.fromJson(data, AgentExecuteResponse.AgentExecuteResult.class);
                    RedisMQ.publish(jedisCmd, channel, JSONObject.toJSONString(ObjectDataUtil.objectDataResultMapping(agentExecuteResult)));
                } finally {
                    if (context != null) {
                        TraceContext.remove();
                    }
                }
            }

            @Override
            public void onClosed(@NotNull EventSource eventSource) {
                if (context != null) {
                    TraceContext._set(context);
                }
                try {
                    log.info("{} finalResultResponse:{}", req.getApiName(), agentExecuteResult);
                    futureResponse.complete(agentExecuteResult);
                } finally {
                    if (context != null ) {
                        TraceContext.remove();
                    }
                }
            }

            @Override
            public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
                if (context != null) {
                    TraceContext._set(context);
                }
                try {
                    log.info("{} treamingAgentExecute fail:{}", req.getApiName(), agentExecuteResult);
                    futureResponse.complete(null);
                } finally {
                    if (context != null ) {
                        TraceContext.remove();
                    }
                }
            }
        };
        RealEventSource realEventSource = new RealEventSource(requestBuilder.build(), eventSourceListener);
        OkHttpClient client = httpManager.getOkHttpClient();
        realEventSource.connect(client);
        try {
            agentResponse = futureResponse.get(120L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("treamingAgentInvoke timeout");
        }
        if (agentResponse == null) {
            return new AgentExecuteResponse<>(SHErrorCode.SERVER_BUSY.getErrorCode(), SHErrorCode.SERVER_BUSY.getErrorMessage());
        } else {
            return new AgentExecuteResponse<>(0, agentResponse);
        }
    }

    public AgentResponse<AgentResponse.AgentReportResult> agentReport(String ea, Integer fsUserId, String businessName, AgentReportRequest req) {
        Map<String, String> headers = getHeaders(ea, fsUserId, businessName);
        String jsonString = JSON.toJSONString(req);
        log.info("agentReport:{}", jsonString);
        RequestBody requestBody = RequestBody.create(null, jsonString);
        String url = baseUrl + "/v1/agent/report";
        return httpManager.executePostHttpWithRequestBodyAndHeader(requestBody, url, new TypeToken<AgentResponse.AgentReportResult>() {
        }, headers);
    }

    public AgentResponse<AgentResponse.AgentWelcomeResult> agentWelcome(String ea, Integer fsUserId, String businessName, AgentWelcomeRequest req) {
        Map<String, String> headers = getHeaders(ea, fsUserId, businessName);
        String jsonString = JSON.toJSONString(req);
        log.info("agentWelcome:{}", jsonString);
        RequestBody requestBody = RequestBody.create(null, jsonString);
        String url = baseUrl + "/v1/agent/welcome";
        return httpManager.executePostHttpWithRequestBodyAndHeader(requestBody, url, new TypeToken<AgentResponse<AgentResponse.AgentWelcomeResult>>() {
        }, headers);
    }

    private Map<String, String> getHeaders(String ea, Integer fsUserId, String businessName) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", String.valueOf(converter.enterpriseAccountToId(ea)));
        if (fsUserId != null && QywxUserConstants.isFsUserId(fsUserId)) {
            headers.put("x-fs-userInfo", String.valueOf(fsUserId));
        } else {
            headers.put("x-fs-userInfo", "-10000");
        }
        if (StringUtils.isNotEmpty(businessName)) {
            headers.put("x-fs-business-name", businessName);
        }
        headers.put("x-fs-locale", I18nUtil.getLanguage());
        headers.put("x-fs-trace-id", TraceContext.get().getTraceId());
        return headers;
    }

}
