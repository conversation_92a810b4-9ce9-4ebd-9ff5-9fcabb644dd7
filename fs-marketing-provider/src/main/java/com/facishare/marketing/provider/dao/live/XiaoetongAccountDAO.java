package com.facishare.marketing.provider.dao.live;

import com.facishare.marketing.provider.entity.live.XiaoetongAccountEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface XiaoetongAccountDAO {
    @Insert("INSERT INTO xiaoetong_account(id, ea, app_id, secret_key, client_id, create_time, update_time) VALUES(#{entity.id}, #{entity.ea}, #{entity.appId}, #{entity.secretKey}, #{entity.clientId}, now(), now())")
    int insert(@Param("entity")XiaoetongAccountEntity entity);

    @Select("SELECT * FROM xiaoetong_account WHERE ea=#{ea}")
    XiaoetongAccountEntity getByEa(@Param("ea")String ea);


    @Select("SELECT * FROM xiaoetong_account WHERE app_id=#{appId}")
    List<XiaoetongAccountEntity> getByAppId(@Param("appId")String appId, @Param("ea")String ea);

    @Update("update xiaoetong_account set app_id = #{entity.appId}, secret_key = #{entity.secretKey}, client_id = #{entity.clientId}, update_time = now() where ea = #{entity.ea}")
    int update(@Param("entity") XiaoetongAccountEntity entity);
}
