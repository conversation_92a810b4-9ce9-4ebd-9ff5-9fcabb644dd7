package com.facishare.marketing.provider.mq.sender;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.provider.mq.handler.AbstractMessageHandler;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.message.MessageQueue;

import java.util.List;
import java.util.Map;
import java.util.Random;

@Slf4j
public class MarketingMessageSender extends AutoConfMQProducer {

    private final Random random = new Random();

    public MarketingMessageSender(String configName, String sectionNames) {
        super(configName, sectionNames);
    }

    /**
     * 发送消息
     *
     * @param msgObj
     */
    public void send(String messageHandler, Object msgObj) {
        send(messageHandler, msgObj, null, null);
    }

    /**
     * 发送消息(带标签)
     *
     * @param msgObj
     * @param tag
     */
    public void send(String messageHandler, Object msgObj, String tag, String hashKey) {
        try {
            Message message = handle(messageHandler, msgObj);
            if (StringUtils.isNotBlank(tag)) {
                message.setTags(tag);
            }
            SendResult sendResult = super.send(message,
                    (mqs, msg, arg) -> mqs.get(arg == null ?
                            random.nextInt(mqs.size()) : Math.abs(arg.hashCode()) % mqs.size()), hashKey);
            //暂时不要打印  log.info("sendResult:{}", sendResult);
        } catch (Exception e) {
            // 不要打印body
            log.error("MarketingMessageSender.send error", e);
        }
    }

    /**
     * 消息装配
     *
     * @param msgObj
     * @return
     */
    protected Message handle(String messageHandler, Object msgObj) {
        Message message = new Message();
        message.putUserProperty(AbstractMessageHandler.MESSAGE_HANDLER, messageHandler);
        message.putUserProperty(AbstractMessageHandler.MESSAGE_TYPE, msgObj.getClass().getName());
        message.setTopic(getDefaultTopic());
        if (msgObj instanceof MessageExt) {
            // 如果是MessageExt,使用场景为直接原消息转发,此时keys和tags应不改变,其他如DelayTime之类参数暂不支持
            MessageExt messageExt = (MessageExt) msgObj;
            message.setKeys(messageExt.getKeys());
            message.setTags(messageExt.getTags());
            message.setBody(messageExt.getBody());
            // 不能直接使用原消息的所有properties,系统默认的key重复会报错：The Property<CONSUME_START_TIME> is used by system, input another please
            String isMkRebalanced = messageExt.getProperty("is_mk_rebalanced");
            if (StringUtils.isNotBlank(isMkRebalanced)) {
                message.putUserProperty("is_mk_rebalanced", isMkRebalanced);
            }
        } else {
            message.setBody(JSON.toJSONBytes(msgObj));
        }
        return message;
    }
}
