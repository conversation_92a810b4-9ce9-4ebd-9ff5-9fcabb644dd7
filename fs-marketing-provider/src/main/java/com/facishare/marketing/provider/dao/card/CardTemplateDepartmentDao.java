package com.facishare.marketing.provider.dao.card;

import com.facishare.marketing.provider.entity.CardTemplateDepartmentEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/25
 **/
public interface CardTemplateDepartmentDao {

    @Insert("INSERT INTO card_template_department " +
            "(id, ea, card_template_id, department_id, create_time, update_time) " +
            "values " +
            "(#{obj.id}, #{obj.ea}, #{obj.cardTemplateId}, #{obj.departmentId}, now(), now()) ")
    int insert(@Param("obj") CardTemplateDepartmentEntity entity);

    @Insert("<script>"
            + "INSERT INTO card_template_department(id, ea, card_template_id, department_id, create_time, update_time) "
            + " VALUES "
            + "<foreach collection=\"entities\" item=\"obj\" index=\"index\" separator=\",\"> "
            + " (#{obj.id}, #{obj.ea}, #{obj.cardTemplateId}, #{obj.departmentId}, now(), now()) "
            + "</foreach>"
            + "</script>")
    int batchInsert(@Param("entities") List<CardTemplateDepartmentEntity> entities);

    @Delete("delete FROM card_template_department WHERE card_template_id = #{cardTemplateId}")
    int deleteByCardTplId(@Param("cardTemplateId") String cardTemplateId, @Param("ea") String ea);

    @Select("SELECT * FROM card_template_department WHERE card_template_id = #{cardTemplateId}")
    List<CardTemplateDepartmentEntity> getByCardTplId(@Param("cardTemplateId") String cardTemplateId, @Param("ea") String ea);

    @Select("<script>" +
                "SELECT * FROM card_template_department WHERE card_template_id IN " +
                "<foreach collection = 'cardTemplateIds' item = 'item' open = '(' separator = ',' close = ')'>" +
                    "#{item}" +
                "</foreach>" +
            "</script>")
    List<CardTemplateDepartmentEntity> batchGetByCardTplIds(@Param("cardTemplateIds") List<String> cardTemplateIds, @Param("ea") String ea);

    @Select("<script>" +
                "SELECT * FROM card_template_department WHERE ea = #{ea} and department_id IN " +
                "<foreach collection = 'departmentIds' item = 'item' open = '(' separator = ',' close = ')'>" +
                    "#{item}" +
                "</foreach>" +
                " order by create_time desc " +
            "</script>")
    List<CardTemplateDepartmentEntity> batchGetByDeptIds(@Param("ea") String ea, @Param("departmentIds") List<Integer> departmentIds);

    @Select("<script>" +
                "SELECT * FROM card_template_department WHERE ea = #{ea} and department_id = #{departmentId} order by create_time desc " +
            "</script>")
    List<CardTemplateDepartmentEntity> getByDeptId(@Param("ea") String ea, @Param("departmentId") Integer departmentId);


}
