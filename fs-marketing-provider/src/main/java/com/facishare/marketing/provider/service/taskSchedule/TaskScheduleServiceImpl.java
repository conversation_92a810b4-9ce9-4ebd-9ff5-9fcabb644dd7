package com.facishare.marketing.provider.service.taskSchedule;

import com.facishare.marketing.api.service.taskSchedule.TaskScheduleService;
import com.facishare.marketing.common.enums.TaskScheduleTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.entity.taskSchedule.TaskScheduleEntity;
import com.facishare.marketing.provider.manager.taskSchedule.TaskScheduleManager;
import com.facishare.marketing.provider.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("taskScheduleService")
public class TaskScheduleServiceImpl implements TaskScheduleService {

    @Autowired
    private TaskScheduleManager taskScheduleManager;

    @Override
    public Result<Void> scheduleTask(String ea, String scheduleId) {
        ContextUtil.buildNewTraceContext();
        TaskScheduleEntity taskScheduleEntity = taskScheduleManager.getTaskScheduleById(ea, scheduleId);
        if (taskScheduleEntity == null) {
            log.info("task schedule is not exist, ea: {} scheduleId: {}", ea, scheduleId);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        // 开始执行任务
        TaskScheduleTypeEnum taskScheduleTypeEnum = TaskScheduleTypeEnum.getByType(taskScheduleEntity.getTaskType());
        if (taskScheduleTypeEnum == null) {
            log.info("task schedule type enum not exist, ea: {} scheduleId: {}", ea, scheduleId);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (taskScheduleTypeEnum.getThreadPoolTypeEnums() == null) {
            ThreadPoolUtils.executeWithNewThread("task_schedule_" + ea + "_" + scheduleId, () ->  taskScheduleManager.scheduleTask(taskScheduleEntity));
        } else {
            ThreadPoolUtils.execute(() ->  taskScheduleManager.scheduleTask(taskScheduleEntity), taskScheduleTypeEnum.getThreadPoolTypeEnums());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> checkDeadTask() {
        ContextUtil.buildNewTraceContext();
        ThreadPoolUtils.executeWithNewThread("task_schedule_check_dead_task", () -> taskScheduleManager.checkDeadTask());
        return Result.newSuccess();
    }
}
