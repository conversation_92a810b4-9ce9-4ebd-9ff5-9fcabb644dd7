package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.provider.entity.mail.MailSendExtraDataEntity;
import org.apache.ibatis.annotations.*;

/**
 * Created  By zhoux 2020/08/18
 **/
public interface MailSendExtraDataDAO {

    @Insert("INSERT INTO mail_send_extra_data(ea, task_id, data_type, extra_data, create_time) VALUES(\n"
        + " #{entity.ea}, #{entity.taskId}, #{entity.dataType}, #{entity.extraData,typeHandler=com.facishare.marketing.common.typehandlers.MailSendExtraDataTypeHandler}, now())")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    void insert(@Param("entity") MailSendExtraDataEntity entity);


    @Select(" SELECT * FROM mail_send_extra_data WHERE task_id = #{taskId}")
    MailSendExtraDataEntity getExtraDataByTaskId(@Param("taskId") String taskId, @Param("ea")String ea);

    @Delete("DELETE FROM mail_send_extra_data WHERE task_id=#{taskId} AND ea=#{ea}")
    int deleteByTaskId(@Param("taskId")String taskId, @Param("ea")String ea);
}
