package com.facishare.marketing.provider.entity.ticket;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.WxTicketContent;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2019/10/28
 **/
@Data
public class WxTicketEntity extends BaseEaEntity implements Serializable  {

    private String id;

    

    private Integer ticketType;

    private String associationId;

    private WxTicketContent ticketContent;

    private String cardId;

    private String wxAppId;

    private Integer status;

    private Integer createBy;

    private Date createTime;

    private Integer updateBy;

    private Date updateTime;

}
