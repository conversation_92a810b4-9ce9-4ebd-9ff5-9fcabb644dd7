package com.facishare.marketing.provider.manager.qywx;

import com.facishare.marketing.api.result.wxcoupon.UploadV3FileResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.QYWXApiConstants;
import com.facishare.marketing.common.contstant.mail.MailConstant;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.ReflectionUtil;
import com.facishare.marketing.provider.advertiser.headlines.HeadlinesRequestResult;
import com.facishare.marketing.provider.advertiser.headlines.ad.GetHeadlinesLocalClueResult;
import com.facishare.marketing.provider.baidu.IResultData;
import com.facishare.marketing.provider.innerResult.qywx.UploadMediaResult;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.mail.result.MailBaseResp;
import com.facishare.open.emailproxy.common.thread.ThreadUtil;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.FormBody.Builder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by zhengh on 2020/1/2.
 */
@Component
@Slf4j
public class HttpManager {

    @Resource(name = "httpSupport")
    private OkHttpSupport httpSupport;

    @Resource(name = "httpSupport4Qywx")
    private OkHttpSupport httpSupport4Qywx;

    @Autowired
    private AppVersionManager appVersionManager;

    @ReloadableProperty("headlines_retry_count")
    private Integer headlineRetryCount;

    @ReloadableProperty("headlines_sleep_time")
    private Long headlineSleepTime;

    @ReloadableProperty("send_cloud_retry_count")
    private Integer sendCloudRetryCount;

    @ReloadableProperty("send_cloud_sleep_time")
    private Long sendCloudSleepTime;

    @Getter
    private static final OkHttpClient okHttpClient = new OkHttpClient.Builder().readTimeout(120L, TimeUnit.SECONDS).build();

    private Gson gson = new Gson();

    public <T> T executePostHttp(Object body, String url, TypeToken typeToken) {
        Object responseBody;
        Request request = null;
        try {
            RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(body));
            request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
            if (isChangeQywxProxy(url)) {
                responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("execute http failed plainBody:{} url:{} return code:{}", body, url, response.code());
                        return null;
                    }
                });
            } else {
                responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("execute http failed plainBody:{} url:{} return code:{}", body, url, response.code());
                        return null;
                    }
                });
            }

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    @FilterLog
    public <T> T executePostHttpWithRequestBody(RequestBody requestBody, String url, TypeToken typeToken) {
        Object responseBody;
        Request request = null;
        try {
            request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }
                    log.warn("execute http failed url:{} return code:{}",  url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T> T executePostHttpWithRetry(Object body, String url, TypeToken typeToken) {
        Object responseBody = null;
        RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(body));
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
        int retryCount = 3;
        for (int i = 0; i < retryCount; i++) {
            boolean isException = false;
            try {
                if (isChangeQywxProxy(url)) {
                    responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                        @Override
                        public Object response(Response response) throws Exception {
                            if (response.code() == HttpStatus.SC_OK) {
                                return response.body().string();
                            }
                            log.warn("execute http failed plainBody:{} url:{} return code:{}", body, url, response.code());
                            return null;
                        }
                    });
                } else {
                    responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                        @Override
                        public Object response(Response response) throws Exception {
                            if (response.code() == HttpStatus.SC_OK) {
                                return response.body().string();
                            }
                            log.warn("execute http failed plainBody:{} url:{} return code:{}", body, url, response.code());
                            return null;
                        }
                    });
                }
            } catch (Exception e) {
                isException = true;
            }
            if (!isException) {
                break;
            }
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T> T executePostHttpWithRequestBodyAndHeader(RequestBody requestBody, String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json").post(requestBody);
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK || response.code() == HttpStatus.SC_CREATED) {
                        return response.body().string();
                    }

                    log.warn("execute http failed url:{} return code:{}",  url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    /**
     * 目睹专用，主要是目睹返回错误信息时http请求的状态码不是200
     * @param requestBody
     * @param url
     * @param typeToken
     * @param header
     * @param <T>
     * @return
     */
    public <T> T executePostHttpWithRequestBodyAndHeaderV2(RequestBody requestBody, String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json").post(requestBody);
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("execute http failed url:{} return result:{}",  url, response.body().string());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T extends IResultData> HeadlinesRequestResult<T> executeHeadlinesRequest(HeadlinesRequestCallback<T> callback) {
        HeadlinesRequestResult<T> result = null;
        for (int i = 0; i < headlineRetryCount; i++) {
            result = callback.execute();
            // 如果触发限流了 试着休眠一下 开发者频控超限报错错误码为40110  接口总频控超限报错错误码为40100  一般都是 40100,这是头条的总限制，没有任何办法处理， 只能休眠
            if (result != null && (result.getCode() == 40100 || result.getCode() == 40110) && i < headlineRetryCount - 1) {
                log.info("rate limit , retry count: {} result: {}", i, result);
                ThreadUtil.sleepIngore(headlineSleepTime);
            } else {
                return result;
            }
        }
        return result;
    }

    public interface HeadlinesRequestCallback<T extends IResultData> {
        HeadlinesRequestResult<T> execute();
    }

    public MailBaseResp executeSendCloudRequest(SendCloudRequestCallback<MailBaseResp> callback) {
        MailBaseResp result = null;
        for (int i = 0; i < sendCloudRetryCount; i++) {
            result = callback.execute();
            // 如果触发限流了 试着休眠一下  接口频率受限(每个api user,每个接口、每分钟调用4000次，目前只限制投递回应)
            if (result != null && result.getStatusCode() != null && result.getStatusCode() == MailConstant.SEND_CLOUD_RATE_LIMIT_CODE && i < sendCloudRetryCount - 1) {
                log.warn("send cloud rate limit , retry count: {} result: {}", i, result);
                ThreadUtil.sleepIngore(sendCloudSleepTime);
            } else {
                return result;
            }
        }
        return result;
    }

    public interface SendCloudRequestCallback<MailBaseResp> {
        MailBaseResp execute();
    }

    /**
     * post请求，支持完全自定义的requestBody和header
     * @param requestBody
     * @param url
     * @param typeToken
     * @param header
     * @return
     * @param <T>
     */
    public <T> T executePostHttpWithRequestBodyAndHeaderV3(RequestBody requestBody, String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).post(requestBody);
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("executePostHttpWithRequestBodyAndHeaderV3 failed, url:{}, code:{}, result:{}", url, response.code(), response.body().string());
                    return response.body().string();
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T> T executePostHttpByRequestBody(RequestBody requestBody, String url, TypeToken typeToken) {
        Object responseBody;
        Request request = null;
        try {
            request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("execute http failed  url:{} return code:{}", url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }


    public <T> T executePostHttpByRequestBody(RequestBody requestBody, String url, TypeToken typeToken, Map<String, String> headerMap) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder();
            builder.addHeader("Content-Type", "application/json");
            if (MapUtils.isNotEmpty(headerMap)) {
                for (Map.Entry<String, String> map : headerMap.entrySet()) {
                    if (StringUtils.isNotBlank(map.getValue())) {
                        builder.addHeader(map.getKey(), map.getValue());
                    }
                }
            }
            request = builder.url(url).post(requestBody).build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }
                    log.warn("execute http failed  url:{} return code:{}", url, response.code());
                    return null;
                }
            });
        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public String  executePostHttpReturnString(Object plainBody, String url) {
        Object responseBody;
        Request request = null;
        try {
            RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(plainBody));
            request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .post(requestBody)
                    .build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("execute http failed plainBody:{} url:{} return code:{}", plainBody, url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return (String)responseBody;
    }

    public Pair<Integer, String> executePostHttpReturnStringAndHttpCode(String url, String body, Map<String, String> headers) {
        try {
            RequestBody requestBody = RequestBody.create(null, body);
            Request.Builder rb = new Request.Builder();
            if (headers != null){
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    rb.addHeader(header.getKey(), header.getValue());
                }
            }
            Request request = rb.url(url).post(requestBody).build();
            return (Pair<Integer, String>) httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.body() != null){
                        return Pair.build(response.code(), response.body().string());
                    }
                    return Pair.build(response.code(), null);
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e.getCause());
        }
    }

    public <T> Pair<Integer, T> executeGetHttpAndGetHttpCode(String url, TypeToken typeToken){
        Response responseBody;
        Request request = null;
        try {
            request = new Request.Builder().url(url).build();
            responseBody = (Response) httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    return response;
                }
            });
        } catch (Exception e) {
            log.warn("HttpManager executeGetHttp exception, request={}, exception:", request, e);
            return null;
        }
        if (responseBody.code() != HttpStatus.SC_OK){
            return Pair.build(responseBody.code(), null);
        }
        try{
            return Pair.build(responseBody.code(), gson.fromJson(responseBody.body().string(), typeToken.getType()));
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    public String  executePostByPlainBodyHttpReturnString(String body, String url) {
        Object responseBody;
        Request request = null;
        try {
            RequestBody requestBody = RequestBody.create(null, body);
            request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("execute http failed plainBody:{} url:{} return code:{}", body, url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return (String)responseBody;
    }

    public <T> T executeGetHttp(String url, TypeToken typeToken){
        Object responseBody;
        Request request = null;
        try {
            request = new Request.Builder().url(url).build();
            if (isChangeQywxProxy(url)) {
                responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }
                        log.warn("HttpManager executeGetHttp failed  url:{} return code:{}", url, response.code());
                        return null;
                    }
                });
            } else {
                responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }
                        log.warn("HttpManager executeGetHttp failed  url:{} return code:{}", url, response.code());
                        return null;
                    }
                });
            }

        } catch (Exception e) {
            log.warn("HttpManager executeGetHttp exception, request={}, exception:", request, e);
            return null;
        }
        log.info("HttpManager executeGetHttp responseBody={}", responseBody);
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T> T executeGetHttpWithRetry(String url, TypeToken typeToken){
        int retryCount = 3;
        Object responseBody = null;
        for (int i = 0; i < retryCount; i++){
            boolean isException = false;
            try {
                Request request = new Request.Builder().url(url).build();
                if (isChangeQywxProxy(url)) {
                    responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                        @Override
                        public Object response(Response response) throws Exception {
                            if (response.code() == HttpStatus.SC_OK) {
                                return response.body().string();
                            }else {
                                log.warn("HttpManager executeGetHttp failed  url:{} return code:{}", url, response.code());
                                return null;
                            }
                        }
                    });
                } else {
                    responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                        @Override
                        public Object response(Response response) throws Exception {
                            if (response.code() == HttpStatus.SC_OK) {
                                return response.body().string();
                            }else {
                                log.warn("HttpManager executeGetHttp failed  url:{} return code:{}", url, response.code());
                                return null;
                            }
                        }
                    });
                }
            }catch (Exception e){
                isException = true;
            }

            if (!isException) {
                break;
            }
        }

        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public String executeGetHttpReturnString(String url) {
        Object responseBody;
        Request request = null;
        try {
            request = new Request.Builder().url(url).build();
            if (isChangeQywxProxy(url)) {
                responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager executeGetHttp failed  url:{} return code:{}", url, response.code());
                        return null;
                    }
                });
            } else {
                responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager executeGetHttp failed  url:{} return code:{}", url, response.code());
                        return null;
                    }
                });
            }

        } catch (Exception e) {
            log.warn("HttpManager executeGetHttp exception, request={}, exception:", request, e);
            return null;
        }
        log.info("HttpManager executeGetHttp responseBody={}", responseBody);
        return (String) responseBody;
    }

    //seo抓取数据使用,这里是使用syncNonblockingExecute,不会阻塞线程,超时直接返回
    @FilterLog
    public String executeGetHttpReturnStringWithOutTime(String url,Long outTime) {
        Object responseBody;
        Request request = null;
        try {
            request = new Request.Builder().url(url).addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36").build();
            responseBody = httpSupport.syncNonblockingExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("HttpManager executeGetHttpReturnStringWithOutTime failed  url:{} return code:{}", url, response.code());
                    return null;
                }
            },outTime);

        } catch (Exception e) {
            log.warn("HttpManager executeGetHttpReturnStringWithOutTime exception, request={}, exception:", request, e);
            return null;
        }
        log.info("HttpManager executeGetHttpReturnStringWithOutTime responseBody={}", responseBody);
        return (String) responseBody;
    }

    //获取链接标题
    @FilterLog
    public String executeGetHttpTitleWithOutTime(String url,Long outTime) {
        Object responseBody;
        Request request = null;
        try {
            request = new Request.Builder().url(url).addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36").build();
            responseBody = httpSupport.syncNonblockingExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        String responseBody = response.body().string();
                        String title = null;
                        if(StringUtils.isNotBlank(responseBody)){
                            Document doc = Jsoup.parse(responseBody);
                            title = doc.title();
                            if(StringUtils.isBlank(title)){
                                title = doc.selectFirst("h1") == null ? null : doc.selectFirst("h1").text();
                            }
                        }
                        return title;
                    }
                    log.warn("HttpManager executeGetHttpTitleWithOutTime failed  url:{} return code:{}", url, response.code());
                    return null;
                }
            },outTime);

        } catch (Exception e) {
            log.warn("HttpManager executeGetHttpTitleWithOutTime exception, request={}, exception:", request, e);
            return null;
        }
        log.info("HttpManager executeGetHttpTitleWithOutTime responseBody={}", responseBody);
        return (String) responseBody;
    }

    public <T> T executeGetHttpWithHeader(String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json");
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("execute http failed url:{} return code:{}",  url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    /**
     * 目睹专用，主要是目睹返回错误信息时http请求的状态码不是200
     * @param url
     * @param typeToken
     * @param header
     * @param <T>
     * @return
     */
    public <T> T executeGetHttpWithHeaderV2(String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json");
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("execute http failed url:{} return result:{}", url, response.body().string());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }


    /**
     * get请求，支持完全自定义的header
     * @param url
     * @param typeToken
     * @param header
     * @return
     * @param <T>
     */
    public <T> T executeGetHttpWithHeaderV3(String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url);
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("executeGetHttpWithHeaderV3 failed, url:{}, code:{}, result:{}", url, response.code(), response.body().string());
                    return response.body().string();
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }
    public String executePostHttpReturnRawString(Object body, String url) {
        Object responseBody;
        Request request = null;
        try {
            RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(body));
            request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .post(requestBody)
                    .build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    log.warn("execute http failed plainBody:{} url:{} return code:{}", body, url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return (String)responseBody;
    }

    public byte[] getBytesByUrl(String url){
        try {
            return httpSupport.getBytes(url);
        }catch (Exception e){
            log.warn("HttpManager.getBytesByUrl ex url:{}, exception:", url, e);
            return null;
        }
    }

    public byte[] getImageBytesByUrl(String url) {
        Object responseBody;
        Request request = null;
        try {
            request = new Request.Builder().url(url).get().build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        if (StringUtils.contains(response.header("Content-Type"), "image")) {
                            return response.body().bytes();
                        }
                    }
                    log.warn("getImageBytesByUrl failed url:{} return code:{}", url, response.code());
                    return null;
                }
            });
            return (byte[]) responseBody;
        } catch (Exception e) {
            log.warn("getImageBytesByUrl, request={}, exception:", request, e);
            return null;
        }
    }

    public byte[] postBytesByUrl(String url, Map<String, Object> params){
        Object responseBody;
        Request request = null;
        try {
            RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(params));
            request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .post(requestBody)
                    .build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().bytes();
                    }
                    log.warn("execute http failed plainBody:{} url:{} return code:{}", params, url, response.code());
                    return null;
                }
            });
            return (byte[]) responseBody;
        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
    }

    @FilterLog
    public String transformUrlParams(Map<String, String> params) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        Map<String, String> paramsEncode = Maps.newHashMap();
        params.forEach((key, value) -> {
            if (StringUtils.isNotBlank(key)) {
                try {
                    if (StringUtils.isNotBlank(value)) {
                        paramsEncode.put(key, URLEncoder.encode(value, "utf-8"));
                    }
                } catch (UnsupportedEncodingException e) {
                    log.warn("exception:",  e);
                }
            }
        });
        return Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(paramsEncode);
    }

    public static String transformUrlParamsV2(String url, Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        Map<String, String> paramsEncode = Maps.newHashMap();
        params.forEach((key, value) -> {
            if (StringUtils.isNotBlank(key)) {
                try {
                    if (Objects.nonNull(value)) {
                        paramsEncode.put(key, URLEncoder.encode(Objects.toString(value), "utf-8"));
                    }
                } catch (UnsupportedEncodingException e) {
                    log.warn("exception:",  e);
                }
            }
        });
        String join = Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(paramsEncode);
        return url + "?" + join;
    }

    public Builder buildFormBodyByMap(Map<String, String> data) {
        Builder builder = new FormBody.Builder();
        for (Map.Entry<String, String> map : data.entrySet()) {
            if (StringUtils.isNotBlank(map.getValue())) {
                builder.add(map.getKey(), map.getValue());
            }
        }
        return builder;
    }

    public RequestBody buildFormBodyByMap(Object source, Class clazz) {
        List<String> fieldNameList = ReflectionUtil.listFieldName(clazz);
        FormBody.Builder builder = new FormBody.Builder();
        try {
            if (CollectionUtils.isEmpty(fieldNameList)) {
                return builder.build();
            }
            for (String fieldName : fieldNameList) {
                Object object = ReflectionUtil.getFieldValue(fieldName, source);
                if (object != null) {
                    builder.add(fieldName, object.toString());
                }
            }
        } catch (Exception e) {
            log.warn("HttpManager.buildFormBodyByMap error e:{}", e);
        }
        return builder.build();
    }


    // fileType媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件（file）
    public UploadMediaResult uploadFile(String accessToken, byte[] data, String fileName, String fileType){
        String url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + accessToken + "&type=" + fileType;
        Object responseBody = null;
        Request request = null;
        try {
            //设置边界
            String boundary = "----------" + System.currentTimeMillis();
            Map<String, String> headsMap = new HashMap<>();
            headsMap.put("Content-Disposition", "form-data; name=\"media\";filename=" + fileName + ";filelength="+data.length);
            RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), data);
            MultipartBody requestBody = new MultipartBody.Builder(boundary)
                .setType(MultipartBody.FORM)
//                .addPart(Headers.of(headsMap), fileBody)
                .addFormDataPart("Content-Disposition","form-data; name=\"media\";filename=" + fileName + ";filelength="+data.length)
                .addFormDataPart(fileType, fileName, fileBody)
                .build();

            request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "multipart/form-data; boundary=" + boundary)
                .addHeader("Content-Length", String.valueOf(data.length))
                .post(requestBody)
                .build();
            if (appVersionManager.isAwsCloud()) {
                responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager uploadFile execute http failed requestBody:{} url:{} return code:{}", requestBody, url, response.code());
                        return null;
                    }
                });
            } else {
                responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager uploadFile execute http failed requestBody:{} url:{} return code:{}", requestBody, url, response.code());
                        return null;
                    }
                });
            }
        } catch (Exception e) {
            log.warn("HttpManager uploadFile failed fileName:{}, fileType:{} e:", fileName, fileType, e);
        }
        return responseBody == null ? null : new Gson().fromJson((String) responseBody, UploadMediaResult.class);
    }

    public <T> T executePatchHttpWithRequestBodyAndHeader(RequestBody requestBody, String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json").patch(requestBody);
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }

                    if (response.code() == HttpStatus.SC_NO_CONTENT) {
                        return gson.fromJson("OK",typeToken.getType());
                    }

                    log.warn("execute http failed url:{} return code:{}",  url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    //提供个给微信营销专用 V3 图片上传接口
    public UploadV3FileResult uploadV3File(String url, File data, String fileName, String shaFile,String auth){
        Object responseBody = null;
        Request request = null;
        try {
            //设置边界
            String boundary = "--" + System.currentTimeMillis();

            RequestBody fileBody = RequestBody.create(MediaType.parse("image/jpg"), data);
            MultipartBody requestFileBody = new MultipartBody.Builder(boundary)
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("meta",shaFile)
                    .addFormDataPart("file", fileName, fileBody)
                    .build();

            request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "multipart/form-data; boundary=" + boundary)
                    .addHeader("Accept","application/json")
                    .addHeader("Authorization", auth)
                    .post(requestFileBody)
                    .build();
            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        UploadV3FileResult uploadV3FileResult = new UploadV3FileResult();
                        uploadV3FileResult.setRescontent(new String(response.body().bytes()));
                        uploadV3FileResult.setWtimestamp(response.header("Wechatpay-Timestamp"));
                        uploadV3FileResult.setWnonce(response.header("Wechatpay-Nonce"));
                        uploadV3FileResult.setWsign(response.header("Wechatpay-Signature"));
                        uploadV3FileResult.setWserial(response.header("Wechatpay-Serial"));
                        return uploadV3FileResult;
                    }
                    log.warn("HttpManager uploadFile execute http failed requestBody:{} url:{} return code:{}", fileBody, url, response.code());
                    return null;
                }
            });
        } catch (Exception e) {
            log.warn("HttpManager uploadFile failed fileName:{}, fileType:{} e:", fileName, shaFile, e);
        }
        return (UploadV3FileResult) responseBody;
    }

    public <T> T executeGetHttpAddHeader(String url, String token, TypeToken typeToken){
        Object responseBody;
        Request request = null;
        try {
            Request.Builder reqBuilder = new Request.Builder().addHeader("x-acs-dingtalk-access-token", token);
            request = reqBuilder.url(url).build();

            responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response.code() == HttpStatus.SC_OK) {
                        return response.body().string();
                    }
                    log.warn("HttpManager executeGetHttp failed  url:{} return code:{}", url, response.code());
                    return null;
                }
            });

        } catch (Exception e) {
            log.warn("HttpManager executeGetHttp exception, request={}, exception:", request, e);
            return null;
        }
        log.info("HttpManager executeGetHttp responseBody={}", responseBody);
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }


    // fileType媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件（file）
    public UploadMediaResult uploadFileToQywx(String accessToken, byte[] data, String fileName, String fileType){
        String url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + accessToken + "&type=" + fileType;
        Object responseBody = null;
        Request request = null;
        try {
            //设置边界
            String boundary = "----------" + System.currentTimeMillis();
            Map<String, String> headsMap = new HashMap<>();
            headsMap.put("Content-Disposition", "form-data; name=\"media\";filename=" + fileName + ";filelength="+data.length);
            RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), data);
            MultipartBody requestBody = new MultipartBody.Builder(boundary)
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("Content-Disposition","form-data; name=\"media\";filename=" + fileName + ";filelength="+data.length)
                    .addFormDataPart(fileType, fileName, fileBody)
                    .build();

            request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "multipart/form-data; boundary=" + boundary)
                    .addHeader("Content-Length", String.valueOf(data.length))
                    .post(requestBody)
                    .build();
            if (appVersionManager.isAwsCloud()) {
                responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager uploadFile execute http failed requestBody:{} url:{} return code:{}", requestBody, url, response.code());
                        return null;
                    }
                });
            } else {
                responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager uploadFile execute http failed requestBody:{} url:{} return code:{}", requestBody, url, response.code());
                        return null;
                    }
                });
            }
        } catch (Exception e) {
            log.warn("HttpManager uploadFileToQywx failed fileName:{}, fileType:{} e:", fileName, fileType, e);
        }
        return responseBody == null ? null : new Gson().fromJson((String) responseBody, UploadMediaResult.class);
    }


    //  企业微信上传附件 fileType媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件（file）
    public UploadMediaResult uploadAttachmentToQywx(String accessToken, byte[] data, String fileName, String fileType){
        String url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload_attachment?access_token=" + accessToken + "&media_type=" + fileType+"&attachment_type=1";
        Object responseBody = null;
        Request request = null;
        try {
            //设置边界
            String boundary = "----------" + System.currentTimeMillis();
            Map<String, String> headsMap = new HashMap<>();
            headsMap.put("Content-Disposition", "form-data; name=\"media\";filename=" + fileName + ";filelength="+data.length);
            RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), data);
            MultipartBody requestBody = new MultipartBody.Builder(boundary)
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("Content-Disposition","form-data; name=\"media\";filename=" + fileName + ";filelength="+data.length)
                    .addFormDataPart(fileType, fileName, fileBody)
                    .build();

            request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "multipart/form-data; boundary=" + boundary)
                    .addHeader("Content-Length", String.valueOf(data.length))
                    .post(requestBody)
                    .build();
            if (appVersionManager.isFxCloud()) {
                responseBody = httpSupport4Qywx.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager uploadAttachmentToQywx execute http failed requestBody:{} url:{} return code:{}", requestBody, url, response.code());
                        return null;
                    }
                });
            } else {
                responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.code() == HttpStatus.SC_OK) {
                            return response.body().string();
                        }

                        log.warn("HttpManager uploadAttachmentToQywx execute http failed requestBody:{} url:{} return code:{}", requestBody, url, response.code());
                        return null;
                    }
                });
            }
        } catch (Exception e) {
            log.warn("HttpManager uploadAttachmentToQywx failed fileName:{}, fileType:{} e:", fileName, fileType, e);
        }
        return responseBody == null ? null : new Gson().fromJson((String) responseBody, UploadMediaResult.class);
    }

    /*************************************以下方法直接使用okHttpClient请求*********************************************/

    public <T> T executeGetByOkHttpClient(String url, TypeToken typeToken) {
        Object responseBody = null;
        Request request = new Request.Builder().url(url).build();
        try {
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == 200 && response.body() != null) {
                responseBody = response.body().string();
            }
        } catch (Exception e) {
            log.warn("HttpManager executeGetByOkHttpClient exception, request={}, exception:", request, e);
        }
        log.info("HttpManager executeGetByOkHttpClient responseBody={}", responseBody);
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T> T executePostByOkHttpClient(Object body, String url, TypeToken typeToken) {
        Object responseBody = null;
        RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(body));
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
        try {
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == 200 && response.body() != null) {
                responseBody = response.body().string();
            }
        } catch (Exception e) {
            log.warn("Http executePostByOkHttpClient, request={}, exception:", request, e);
        }
        log.info("HttpManager executePostByOkHttpClient responseBody={}", responseBody);
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T> T executePostByOkHttpClientWithRequestBody(RequestBody requestBody, String url, TypeToken typeToken) {
        Object responseBody = null;
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
        try {
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == 200 && response.body() != null) {
                responseBody = response.body().string();
            }
        } catch (Exception e) {
            log.warn("Http executePostByOkHttpWithRequestBody, request={}, exception:", request, e);
        }
        log.info("HttpManager executePostByOkHttpWithRequestBody responseBody={}", responseBody);
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    public <T> T executePostByOkHttpClientWithRequestBodyAndHeader(RequestBody requestBody, String url, TypeToken typeToken, Map<String, String> header) {
        Object responseBody = null;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json").post(requestBody);
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == HttpStatus.SC_OK && response.body() != null) {
                responseBody = response.body().string();
            }
        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        log.warn("execute http failed url:{} responseBody:{}", url, responseBody);
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }


    public <T> T executePostByOkHttpClientWithRequestBodyAndHeaderV2(RequestBody requestBody, String url, TypeToken typeToken, Map<String, String> header,Gson gson) {
        Object responseBody = null;
        Request request = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json").post(requestBody);
            if (header != null && !header.isEmpty()) {
                for (Entry<String, String> entry : header.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            request = builder.build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == HttpStatus.SC_OK && response.body() != null) {
                responseBody = response.body().string();
            }
        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        log.warn("execute http failed url:{} responseBody:{}", url, responseBody);
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    //文件服务请求不区分状态码,仅图片转码使用
    public <T> T executePostHttpIgnoreStatusCode(Object body, String url, TypeToken typeToken) {
        Object responseBody;
        Request request = null;
        try {
            RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(body));
            request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .post(requestBody)
                    .build();
                responseBody = httpSupport.syncExecute(request, new SyncCallback() {
                    @Override
                    public Object response(Response response) throws Exception {
                        if (response.body() != null) {
                            return response.body().string();
                        }
                        log.warn("execute http failed plainBody:{} url:{} return code:{}", body, url, response.code());
                        return null;
                    }
                });

        } catch (Exception e) {
            log.warn("Http executeHttp, request={}, exception:", request, e);
            return null;
        }
        return responseBody == null ? null : gson.fromJson((String) responseBody, typeToken.getType());
    }

    /**
     * 亚马逊云企微相关接口需要回源到纷享云，使用新的代理
     * @param url
     * @return
     */
    private boolean isChangeQywxProxy(String url){
        if (appVersionManager.isAwsCloud() && isQywxUrl(url)) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否是企微的url
     * @param url
     * @return
     */
    private boolean isQywxUrl(String url) {
        return url.contains(QYWXApiConstants.QYWX_RESET_API_HOST);
    }
}
