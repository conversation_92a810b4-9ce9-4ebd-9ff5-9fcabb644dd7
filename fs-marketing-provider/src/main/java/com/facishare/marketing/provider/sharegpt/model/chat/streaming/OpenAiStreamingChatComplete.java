package com.facishare.marketing.provider.sharegpt.model.chat.streaming;

import com.facishare.ai.api.model.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class OpenAiStreamingChatComplete {

    @Data
    static class Arg {
        private List<Message> messages;
        private Integer maxTokens;
        private Double temperature;
        private String model;
        private List<Tool> tools;
        private String tool_choice;
        private List<String> imageStrings;
        private boolean supportImage;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Result {
        private boolean finish;
        private String content;
        private List<ToolCall> tool_calls;
    }

}
