package com.facishare.marketing.provider.manager.cardtemplate;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.result.GetCardCommonSettingResult;
import com.facishare.marketing.api.result.minAppCardNavbar.QueryMiniAppCardNavbarListResult;
import com.facishare.marketing.api.vo.UpdateCardCommonSettingVo;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.card.BgCardTypeEnum;
import com.facishare.marketing.common.enums.card.CardDefaultEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CardCommonSetting;
import com.facishare.marketing.common.typehandlers.value.CardCommonSettingList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.card.CardTemplateDao;
import com.facishare.marketing.provider.dao.card.CardTemplateDepartmentDao;
import com.facishare.marketing.provider.dao.card.EnterpriseDefaultCardDao;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonOfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.manager.CardManager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.ResourceManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CardTemplateManager {

    @Autowired
    private ResourceManager resourceManager;

    @Autowired
    private EnterprseInfoDao enterprseInfoDao;

    @Autowired
    private CustomizeMiniappCardSettingDAO customizeMiniappCardSettingDAO;

    @Autowired
    private CustomizeMiniappCardNavbarDAO customizeMiniappCardNavbarDAO;

    @Autowired
    private EnterpriseDefaultCardDao enterpriseDefaultCardDao;

    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Autowired
    private CardTemplateDao cardTemplateDao;

    @Autowired
    private HexagonOfficialWebsiteDAO hexagonOfficialWebsiteDAO;

    @Autowired
    private CardTemplateDepartmentDao cardTemplateDepartmentDao;

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private CardManager cardManager;

    public String getCardLogoAPath(CustomizeMiniappCardSettingEntity entity){
        String cardLogo = entity.getCardLogo();
        Integer cardLogoDefault = entity.getCardLogoDefault();
        if (StringUtils.isNotEmpty(cardLogo)) {
            if (cardLogoDefault == null) {
                return cardLogo;
            }
            if (CardDefaultEnum.CUSTOMIZE.getType().equals(cardLogoDefault)) {
                return cardLogo;
            }
        }
        if (CardDefaultEnum.DEFAULT.getType().equals(cardLogoDefault)) {
            return resourceManager.getResouceApath(ResourceApathEnum.ACROSS_LOGO);
        }
        EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(entity.getEa());
        if (enterpriseInfoEntity != null && StringUtils.isNotEmpty(enterpriseInfoEntity.getIconAcrossPath())) {
            return enterpriseInfoEntity.getIconAcrossPath();
        }
        return resourceManager.getResouceApath(ResourceApathEnum.ACROSS_LOGO);
    }

    public String getCardAvatarAPath(CustomizeMiniappCardSettingEntity entity) {
        String cardAvatarPath = entity.getCardAvatarPath();
        Integer cardAvatarDefault = entity.getCardAvatarDefault();
        if (StringUtils.isNotEmpty(cardAvatarPath)) {
            if (cardAvatarDefault == null) {
                return cardAvatarPath;
            }
            if (CardDefaultEnum.CUSTOMIZE.getType().equals(cardAvatarDefault)) {
                return cardAvatarPath;
            }
        }
        return resourceManager.getResouceApath(ResourceApathEnum.AVATAR_LOGO);
    }

    public String getCardImageAPath(CustomizeMiniappCardSettingEntity entity) {
        String cardImagePath = entity.getCardImagePath();
        Integer cardImageDefault = entity.getCardImageDefault();
        if (cardImageDefault == null || CardDefaultEnum.DEFAULT.getType().equals(cardImageDefault)) {
            Integer cardType = entity.getCardType();
            if (cardType != null) {
                if (CardTypeEnum.STYLE2.getType().equals(cardType)) {
                    return resourceManager.getResouceApath(ResourceApathEnum.SIMPLE_CARD_TEMPLATE);
                }
                if (CardTypeEnum.STYLE3.getType().equals(cardType)) {
                    return resourceManager.getResouceApath(ResourceApathEnum.BLACK_GOLD_CARD_TEMPLATE);
                }
                if (CardTypeEnum.STYLE4.getType().equals(cardType)) {
                    return resourceManager.getResouceApath(ResourceApathEnum.BUSINESS_CARD_TEMPLATE);
                }
                if (CardTypeEnum.STYLE5.getType().equals(cardType)) {
                    if (entity.getCardImageDefaultIndex() != null && entity.getCardImageDefaultIndex() == 0) {
                        return resourceManager.getResouceApath(ResourceApathEnum.FRAMELESS_CARD_DEFAULT_TEMPLATE_0);
                    }
                    if (entity.getCardImageDefaultIndex() != null && entity.getCardImageDefaultIndex() == 1) {
                        return resourceManager.getResouceApath(ResourceApathEnum.FRAMELESS_CARD_DEFAULT_TEMPLATE_1);
                    }
                    if (entity.getCardImageDefaultIndex() != null && entity.getCardImageDefaultIndex() == 2) {
                        return resourceManager.getResouceApath(ResourceApathEnum.FRAMELESS_CARD_DEFAULT_TEMPLATE_2);
                    }
                }
                if (CardTypeEnum.STYLE6.getType().equals(cardType)) {
                    if (entity.getCardImageDefaultIndex() != null && entity.getCardImageDefaultIndex() == 0) {
                        return resourceManager.getResouceApath(ResourceApathEnum.TRADITION_CARD);
                    }
                    if (entity.getCardImageDefaultIndex() != null && entity.getCardImageDefaultIndex() == 1) {
                        return resourceManager.getResouceApath(ResourceApathEnum.FRAMELESS_CARD_DEFAULT_TEMPLATE_0);
                    }
                    if (entity.getCardImageDefaultIndex() != null && entity.getCardImageDefaultIndex() == 2) {
                        return resourceManager.getResouceApath(ResourceApathEnum.FRAMELESS_CARD_DEFAULT_TEMPLATE_1);
                    }
                    if (entity.getCardImageDefaultIndex() != null && entity.getCardImageDefaultIndex() == 3) {
                        return resourceManager.getResouceApath(ResourceApathEnum.FRAMELESS_CARD_DEFAULT_TEMPLATE_2);
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(cardImagePath) && entity.getBgSettingType() != null && BgCardTypeEnum.IMAGE.getType().equals(entity.getBgSettingType()) && CardDefaultEnum.CUSTOMIZE.getType().equals(cardImageDefault)) {
            return cardImagePath;
        }
        return null;
    }


    /**
     * 刷名片默认模板
     * @param ea
     */
    @Transactional
    public void refreshDefaultTemplate(String ea){
        try {
            // 检查是否已经刷过
            CardTemplateEntity defaultCardTemplateEntity = cardTemplateDao.getDefault(ea);
            if (defaultCardTemplateEntity != null) {
                log.info("名片模板刷过了，ea:{}", ea);
                return;
            }

            // 生成模板
            String cardTemplateId = UUIDUtil.getUUID();
            CardTemplateEntity cardTemplateEntity = new CardTemplateEntity();
            cardTemplateEntity.setId(cardTemplateId);
            cardTemplateEntity.setEa(ea);
            cardTemplateEntity.setIsDefault(1);
            cardTemplateEntity.setName(I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_175));
            // 1 刷取联系我设置
            EnterpriseMetaConfigEntity enterpriseMetaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
            if (enterpriseMetaConfigEntity != null) {
                cardTemplateEntity.setCardCommonSetting(enterpriseMetaConfigEntity.getCardCommonSetting());
            }
            int result = cardTemplateDao.insert(cardTemplateEntity);
            if (result <= 0) {
                return;
            }
            // 2 刷取名片风格设置
            refreshCustomizeMiniAppCardSetting(ea, cardTemplateId);
            // 3 刷取名片底部菜单设置
            refreshCustomizeMiniAppCardNavbar(ea, cardTemplateId);
            // 4 刷取名片企业默认设置
            enterpriseDefaultCardDao.updateCardTplIdByEa(ea, cardTemplateId);
            // 5 刷新员工名片
            List<CardEntity> cardEntities = fsBindManager.getAllCardByEa(ea);
            if (CollectionUtils.isNotEmpty(cardEntities)) {
                List<String> uids = cardEntities.stream().map(CardEntity::getUid).collect(Collectors.toList());
                cardDAO.batchUpdateCardTemplateByUids(uids, cardTemplateId);
            }
            log.info("名片模板刷成功了，ea:{}", ea);
        } catch (Exception e) {
            log.info("名片模板刷失败了，ea:{}", ea, e);
        }
    }


    /**
     * 刷数据-刷取名片风格设置
     * @param ea
     * @param cardTemplateId
     */
    private void refreshCustomizeMiniAppCardSetting(String ea, String cardTemplateId){
        // 判断企业之前有无设置过，没有则初始化一份
        CustomizeMiniappCardSettingEntity customizeMiniappCardSettingEntity = customizeMiniappCardSettingDAO.queryByEa(ea);
        if (customizeMiniappCardSettingEntity == null) {
            // 之前没有设置数据，则进行初始化
            initCustomizeMiniAppCardSetting(ea, cardTemplateId);
        } else {
            // 之前已经有设置数据，则更新模板id
            customizeMiniappCardSettingDAO.updateCardTplIdByEa(ea, cardTemplateId);
        }
    }

    /**
     * 初始名片风格设置
     * @param ea
     * @param cardTemplateId
     */
    public void initCustomizeMiniAppCardSetting(String ea, String cardTemplateId){
        CustomizeMiniappCardSettingEntity customizeMiniappCardSettingEntity = customizeMiniappCardSettingDAO.queryByEaAndTemplateId(ea, cardTemplateId);
        if (customizeMiniappCardSettingEntity != null) {
            return ;
        } else {
            customizeMiniappCardSettingEntity = new CustomizeMiniappCardSettingEntity();
            customizeMiniappCardSettingEntity.setId(UUIDUtil.getUUID());
            customizeMiniappCardSettingEntity.setEa(ea);
            customizeMiniappCardSettingEntity.setNavbarButtonColor("#2B9CFA");
            customizeMiniappCardSettingEntity.setCardTemplateId(cardTemplateId);
            customizeMiniappCardSettingDAO.insertCustomizeMiniappCardSetting(customizeMiniappCardSettingEntity);
        }
    }

    /**
     * 刷数据-刷取名片菜单设置
     * @param ea
     * @param cardTemplateId
     */
    private void refreshCustomizeMiniAppCardNavbar(String ea, String cardTemplateId){
        // 判断企业之前有无设置过，没有则初始化一份
        List<CustomizeMiniappCardNavbarEntity> customizeMiniappCardNavbarEntities = customizeMiniappCardNavbarDAO.queryByEa(ea);
        if (CollectionUtils.isEmpty(customizeMiniappCardNavbarEntities)) {
            // 之前没有设置数据，则进行初始化
            initCustomizeMiniAppCardNavbar(ea, cardTemplateId);
        } else {
            // 之前已经有设置数据，则更新模板id
            customizeMiniappCardNavbarDAO.updateCardTplIdByEa(ea, cardTemplateId);
        }
    }

    /**
     * 初始化名片菜单设置
     * @param ea
     * @param cardTemplateId
     */
    public void initCustomizeMiniAppCardNavbar(String ea, String cardTemplateId){
        // 若是客脉则跳过
//        String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
//        if (WxAppInfoEnum.isMankeep(wxAppId) || WxAppInfoEnum.isMankeepPro(wxAppId)) {
//            return;
//        }

        List<CustomizeMiniappCardNavbarEntity> navbarEntities = customizeMiniappCardNavbarDAO.queryCustomizeMiniappCardNavbar(ea, cardTemplateId);
        if (CollectionUtils.isNotEmpty(navbarEntities)) {
            return;
        } else {
            // 之前没有设置数据，则进行初始化
            // 设置初始化信息
            List<HexagonOfficialWebsiteEntity> hexagonOfficialWebsiteEntityList = hexagonOfficialWebsiteDAO.getWebsiteHexagonSiteInfoByEa(ea);
            Integer seq = 1;
            for (WechatAccountChannelEnum wechatAccountChannelEnum : WechatAccountChannelEnum.values()) {
                if (wechatAccountChannelEnum == WechatAccountChannelEnum.OFFICIAL && CollectionUtils.isEmpty(hexagonOfficialWebsiteEntityList)) {
                    continue;
                }
                // 构建保存数据
                String id = UUIDUtil.getUUID();
                CustomizeMiniappCardNavbarEntity customizeMiniappCardNavbarEntity = new CustomizeMiniappCardNavbarEntity();
                customizeMiniappCardNavbarEntity.setId(id);
                customizeMiniappCardNavbarEntity.setEa(ea);
                customizeMiniappCardNavbarEntity.setTitle(wechatAccountChannelEnum.getShowName());
                customizeMiniappCardNavbarEntity.setIconTitle(wechatAccountChannelEnum.getIconTitle());
                customizeMiniappCardNavbarEntity.setForwardType(wechatAccountChannelEnum.getForwardType());
                customizeMiniappCardNavbarEntity.setForwardName(wechatAccountChannelEnum.getShowName());
                if (wechatAccountChannelEnum == WechatAccountChannelEnum.OFFICIAL) {
                    customizeMiniappCardNavbarEntity.setForwardId(hexagonOfficialWebsiteEntityList.get(0).getHexagonSiteId());
                }
                customizeMiniappCardNavbarEntity.setStatus(CustomizeMiniAppCardNavbarStatusEnum.ENABLE.getStatus());
                customizeMiniappCardNavbarEntity.setSeq(seq);
                customizeMiniappCardNavbarEntity.setCardTemplateId(cardTemplateId);
                customizeMiniappCardNavbarDAO.insertCustomizeMiniappCardNavbar(customizeMiniappCardNavbarEntity);
                seq = seq + 1;
            }
        }
    }

    /**
     * 获取模板联系我设置
     * @param cardTemplateId
     * @return
     */
    public Result<List<GetCardCommonSettingResult>> getCardCommonSetting(String ea, String cardTemplateId){
        CardCommonSettingList cardCommonSettingList;
        if (StringUtils.isBlank(cardTemplateId)) {
            // 兼容历史逻辑
            EnterpriseMetaConfigEntity enterpriseMetaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
            if (enterpriseMetaConfigEntity == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            cardCommonSettingList = enterpriseMetaConfigEntity.getCardCommonSetting();
        } else {
            CardTemplateEntity entity = cardTemplateDao.getById(cardTemplateId);
            if (entity == null) {
                return Result.newSuccess(Lists.newArrayList());
            }
            cardCommonSettingList = entity.getCardCommonSetting();
        }

        CardCommonSettingList cardCommonSettings = handleCardCommonSetting(ea, cardCommonSettingList);
        if (CollectionUtils.isEmpty(cardCommonSettings)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        List<GetCardCommonSettingResult> getCardCommonSettingResults = BeanUtil.copy(cardCommonSettings, GetCardCommonSettingResult.class);
        return Result.newSuccess(getCardCommonSettingResults);
    }

    /**
     * 更新联系我设置
     * @param vo
     * @return
     */
    public Result updateCardCommonSetting(UpdateCardCommonSettingVo vo) {
        String ea = vo.getEa();
        if (StringUtils.isBlank(vo.getCardTemplateId())) {
            // 兼容历史逻辑
            if (CollectionUtils.isEmpty(vo.getSetting())) {
                return Result.newSuccess();
            }
            EnterpriseMetaConfigEntity enterpriseMetaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
            if (enterpriseMetaConfigEntity == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            Result checkResult = updateCardCommonSettingCheck(ea, vo.getSetting());
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            // 查询已有设置只更新输入设置
            CardCommonSettingList cardCommonSettings = enterpriseMetaConfigEntity.getCardCommonSetting();
            Map<Integer, CardCommonSetting> updateCardCommonSettingMap = vo.getSetting().stream().collect(Collectors.toMap(CardCommonSetting::getType, data -> data));
            Map<Integer, CardCommonSetting> existCardCommonSettingMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(cardCommonSettings)) {
                existCardCommonSettingMap = cardCommonSettings.stream().collect(Collectors.toMap(CardCommonSetting::getType, data -> data));
            }
            CardCommonSettingList saveSetting = new CardCommonSettingList();
            for (CardCommonSettingTypeEnum cardCommonSettingTypeEnum : CardCommonSettingTypeEnum.values()) {
                if (updateCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()) != null) {
                    saveSetting.add(updateCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()));
                } else if (existCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()) != null) {
                    saveSetting.add(existCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()));
                }
            }
            enterpriseMetaConfigDao.updateCardCommonSetting(ea, saveSetting);
            return Result.newSuccess();
        } else {
            String cardTemplateId = vo.getCardTemplateId();
            List<CardCommonSetting> setting = vo.getSetting();
            if (CollectionUtils.isEmpty(setting)) {
                return Result.newSuccess();
            }
            CardTemplateEntity entity = cardTemplateDao.getById(cardTemplateId);
            if (entity == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }

            Result checkResult = updateCardCommonSettingCheck(ea, vo.getSetting());
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            // 查询已有设置只更新输入设置
            CardCommonSettingList cardCommonSettings = entity.getCardCommonSetting();
            Map<Integer, CardCommonSetting> updateCardCommonSettingMap = vo.getSetting().stream().collect(Collectors.toMap(CardCommonSetting::getType, data -> data));
            Map<Integer, CardCommonSetting> existCardCommonSettingMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(cardCommonSettings)) {
                existCardCommonSettingMap = cardCommonSettings.stream().collect(Collectors.toMap(CardCommonSetting::getType, data -> data));
            }
            CardCommonSettingList saveSetting = new CardCommonSettingList();
            for (CardCommonSettingTypeEnum cardCommonSettingTypeEnum : CardCommonSettingTypeEnum.values()) {
                if (updateCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()) != null) {
                    saveSetting.add(updateCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()));
                } else if (existCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()) != null) {
                    saveSetting.add(existCardCommonSettingMap.get(cardCommonSettingTypeEnum.getType()));
                }
            }
            entity.setCardCommonSetting(saveSetting);
            cardTemplateDao.update(entity);
            return Result.newSuccess();
        }
    }

    public CardCommonSettingList handleCardCommonSetting(String ea, CardCommonSettingList cardCommonSettings) {
        if (StringUtils.isBlank(ea)) {
            return new CardCommonSettingList();
        }
        if (CollectionUtils.isEmpty(cardCommonSettings)) {
            // 设置客户联系
            // 判断是否开通企业微信
            QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
            cardCommonSettings = new CardCommonSettingList();
            if (qywxCorpAgentConfigEntity == null) {
                cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType(), CardCommonSettingShowTypeEnum.PERSONAL_COMMUNICATION.getType(), I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_415), true));
            } else {
                cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType(), CardCommonSettingShowTypeEnum.CONTACT_ME.getType(), I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_415), true));
            }
            cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.PRODUCT_STYLE.getType(), CardCommonSettingShowTypeEnum.PRODUCT_DEFAULT_STYLE.getType(), "", true));
           /* if (qywxCorpAgentConfigEntity == null) {
                cardCommonSettings = new CardCommonSettingList();
                cardCommonSettings.buildAllDisableData();
            } else {
                cardCommonSettings = new CardCommonSettingList();
                // 纷享与木里木外特殊处理先将"联系我"隐藏
                Boolean enableStatus = true;
                if (fsAddressBookManager.mustUseFxiaokeAddressBook(ea)) {
                    enableStatus = false;
                }
                cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType(), CardCommonSettingShowTypeEnum.CONTACT_ME.getType(), I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_415), enableStatus));
            }*/
        }
        return cardCommonSettings;
    }

    public Result updateCardCommonSettingCheck(String ea, List<CardCommonSetting> setting) {
        if (CollectionUtils.isEmpty(setting)) {
            return Result.newSuccess();
        }
        // 客户联系设置
        for (CardCommonSetting cardCommonSetting : setting) {
            if (cardCommonSetting.getType().equals(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType())) {
                QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
                if (qywxCorpAgentConfigEntity == null && cardCommonSetting.getShowType().equals(CardCommonSettingShowTypeEnum.CONTACT_ME.getType())) {
                    return Result.newError(SHErrorCode.ENTERPRISE_NEED_BIND_QYWX);
                }
            }
        }
        return Result.newSuccess();
    }

    /**
     * 删除模板
     * @param id
     * @return
     */
    @Transactional
    public Result<Void> deleteCardTemplate(String id){
        CardTemplateEntity cardTemplateEntity = cardTemplateDao.getById(id);
        if (cardTemplateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        // 默认模板不能删除
        if (cardTemplateEntity.getIsDefault() == 1) {
            return Result.newError(SHErrorCode.CARD_TEMPLATE_DELETE_FAIL.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_466));
        }

        cardTemplateDao.deleteById(id);

        // 删除适用范围
        cardTemplateDepartmentDao.deleteByCardTplId(id);
        // 删除模板设置
        customizeMiniappCardSettingDAO.deleteByCardTplId(id);
        // 删除名片底部菜单设置
        customizeMiniappCardNavbarDAO.deleteByCardTplId(id);
        // 删除模板默认设置
        enterpriseDefaultCardDao.deleteByCardTplId(id);

        // 触发全部名片更新
        cardManager.updateAllCard(cardTemplateEntity.getEa(), 1);
        return Result.newSuccess();
    }

}