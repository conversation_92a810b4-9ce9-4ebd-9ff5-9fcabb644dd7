package com.facishare.marketing.provider.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jetty.util.ConcurrentHashSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class EnterpriseInfoManager {

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @ReloadableProperty("marketing_license_expire_white_list")
    private String marketingLicenseExpireWhite;

    private static final Set<String> whiteCache = new ConcurrentHashSet<>();

    static {
        ConfigFactory.getConfig("fs-marketing-provider", conf -> {
            String configValue = conf.get("marketing_license_expire_white_list", null);
            if (StringUtils.isBlank(configValue)) {
                log.info("EnterpriseInfoManager , marketing_license_expire_white_list is blank");
                return;
            }
            log.info("EnterpriseInfoManager , marketing_license_expire_white_list: {}", configValue);
            List<String> marketingLicenseExpireWhiteList = GsonUtil.getGson().fromJson(configValue, ArrayList.class);
            whiteCache.addAll(marketingLicenseExpireWhiteList);
        });
    }

    // 手动刷新缓存
    public void manualUpdateCache(String data) {
        String[] arr = data.split("-");
        String ea = arr[0];
        String statusStr = arr[1];
        boolean status = Boolean.parseBoolean(statusStr);
        redisManager.setEnterpriseStatus(ea, status ? StringPool.ONE : StringPool.ZERO);
    }

    public void updateCache(String ea, boolean status) {
        if (whiteCache.contains(ea)) {
            return;
        }
        redisManager.setEnterpriseStatus(ea, status ? StringPool.ONE : StringPool.ZERO);
    }


    /**
     * 判断企业是否停用、营销通配额是否过期
     * 注意：这里是从本地表查询的，注意企业和云环境是否一致 不一致会出问题
     * @param ea
     * @return true: 停用  false: 启用
     */
    public boolean isEnterpriseStopAndVersionExpired(String ea) {
        return !getStatusFromCache(ea);
    }

    @FilterLog
    public boolean isEnterpriseStopAndVersionExpiredFilterLog(String ea) {
        return !getStatusFromCache(ea);
    }

    private boolean getStatusFromCache(String ea) {
        try {
            if (StringUtils.isBlank(ea)) {
                return true;
            }
            if (whiteCache.contains(ea)) {
                return true;
            }
            String redisCache = redisManager.getEnterpriseStatus(ea);
            if (StringUtils.isNotBlank(redisCache)) {
                return StringPool.ONE.equals(redisCache);
            }
            boolean isStop = marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null;
            boolean status = !isStop;
            redisManager.setEnterpriseStatus(ea, status ? StringPool.ONE : StringPool.ZERO);
            return status;
        } catch (Exception e) {
            log.error("get enterprise status from cache error, ea:{}", ea, e);
        }
        return true;
    }
}
