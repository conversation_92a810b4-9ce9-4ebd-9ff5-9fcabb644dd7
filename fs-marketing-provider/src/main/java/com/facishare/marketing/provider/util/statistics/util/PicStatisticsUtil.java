package com.facishare.marketing.provider.util.statistics.util;

import com.alibaba.dubbo.common.logger.Logger;
import com.alibaba.dubbo.common.logger.LoggerFactory;
import com.facishare.marketing.common.enums.TimeTypeEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.util.statistics.service.PicStatisticsService;
import groovy.util.logging.Slf4j;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by houym on 2018/8/22.
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class PicStatisticsUtil {
    private static final Logger writeLog = LoggerFactory.getLogger("picStatisticLog");
    @Autowired
    private PicStatisticsService instance;

    //图片下载次数
    public void picDownloadNum() {
        synchronized (instance) {
            instance.setPicDownloadNum(instance.getPicDownloadNum() + 1);
            writeFile();
        }
    }

    //图片下载失败次数
    public void picAddFailureNum() {
        synchronized (instance) {
            instance.setPicAddFailureNum(instance.getPicAddFailureNum() + 1);
            writeFile();
        }
    }

    //写日志文件
    private void writeFile() {
        synchronized (instance) {
            Date lastTime = instance.getLastTime(); //获取对象中的上次时间
            Date currentTime = new Date(); //获取当前时间
            //判断上次时间是否为空值，为空赋值为当前时间
            if (null == lastTime) {
                instance.setLastTime(currentTime);
                lastTime = currentTime;
            }
            final Long TimeDifference = DateUtil.getTimeDifference(lastTime, currentTime, TimeTypeEnum.MILLIS.getType());
            //比较时间差是否为一个小时
            if (TimeDifference >= 3600000L) {
                //刷新上次时间
                instance.setLastTime(currentTime);
                writeLog.info("图片下载数:" + instance.getPicDownloadNum() + "  图片失败数:" + instance.getPicAddFailureNum());
            }
        }
    }
}
