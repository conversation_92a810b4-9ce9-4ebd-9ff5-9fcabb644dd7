package com.facishare.marketing.provider.entity.pay;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.util.DateUtil;
import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PayOrderEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String merchantId;
    private String wxAppId;
    private String wxOpenId;
    private String type;
    /** @see com.facishare.marketing.common.contstant.pay.PayOrderState */
    private String state;
    /** @see com.facishare.marketing.common.contstant.pay.FeeType */
    private String feeType;
    private Integer totalFee;
    private String wxTransactionId;
    private String description;
    private String startTime;
    private String expireTime;
    private String prepayId;
    private String nonceStr;
    private String spBillCreateIp;
    private String deviceInfo;
    private Date createTime;
    private Date updateTime;

    public String getDisplayTotalFee(){
        return BigDecimal.valueOf(totalFee).divide(BigDecimal.valueOf(100)).setScale(2).toPlainString();
    }

    public boolean isOrderExpired(){
        return !Strings.isNullOrEmpty(expireTime) && DateUtil.getDateTimeByWxPayTimeFormat(expireTime).getMillis() < System.currentTimeMillis();
    }
}
