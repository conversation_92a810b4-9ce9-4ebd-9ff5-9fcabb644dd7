/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.sharegpt.agent.tool.impl;

import com.facishare.marketing.provider.sharegpt.agent.tool.CommandType;
import com.facishare.marketing.provider.sharegpt.agent.tool.ToolParam;
import com.facishare.marketing.provider.sharegpt.agent.tool.AbstractCommand;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.data.message.ChatMessageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.util.Map;

@Component
@Slf4j
public class KeywordIndexCommand extends AbstractCommand {

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @ReloadableProperty("aizhan_private_key")
    private String aizhanPrivateKey;

    public static final String url = "https://apistore.aizhan.com/baidu/index/";
    
    public static final String name = "查询关键词指数";

    @Override
    public String name() {
        return name;
    }


    @Tool("查询关键词指数,返回json: word 关键词, zhishu 总指数, zhishu_pc PC指数, zhishu_wise 移动指数")
    public String keywordIndex(
            @ToolParam("ea") String ea,
            @ToolParam(name = "words", type = ChatMessageType.AI, desc = "关键词,若多个用|分隔") String words
    ) {
        String result = "";
        try {
            Long keywordIndexCommandCount = redisManager.getKeywordIndexCommandCount(ea);
            if (keywordIndexCommandCount >= 2000) {
                return result;
            }
            if (StringUtils.isBlank(words)) {
                return result;
            }
            Map<String, String> requestMap = Maps.newHashMap();
            requestMap.put("words", words);
            String finalUrl = url + aizhanPrivateKey + "?" + httpManager.transformUrlParams(requestMap);
            String resultJson = httpManager.executeGetHttpReturnString(finalUrl);
            String parserJson = parserJson(resultJson);
            log.warn("finalUrl:{} resultJson:{}", finalUrl, parserJson);
            if (StringUtils.isNotBlank(parserJson)) {
                result = URLDecoder.decode(parserJson, "UTF-8");
            }
        } catch (Exception e) {
            log.warn("KeywordIndexCommand execute error", e);
        }
        return result;
    }

    public static String parserJson(String json) {
        String resultJson = null;
        if (StringUtils.isEmpty(json)) {
            return resultJson;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
            Object obj = objectMapper.readValue(json, Object.class);
            resultJson = objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.warn("KeywordIndexCommand parser resultJson error", e);
        }
        return resultJson;
    }

}
