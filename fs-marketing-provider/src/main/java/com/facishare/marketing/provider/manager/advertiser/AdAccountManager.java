package com.facishare.marketing.provider.manager.advertiser;

import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component("adAccountManager")
public class AdAccountManager {

    @Autowired
    private BaiduAccountDAO accountDAO;

    @ReloadableProperty("ad_prototype_room_account")
    private String adPrototypeRoomAccount;
    @ReloadableProperty("ad_prototype_room_account_en")
    private String adPrototypeRoomAccountEN;

    public boolean addAccount(AdAccountEntity adAccountEntity) {
        return accountDAO.addAccount(adAccountEntity);
    }

    public boolean updateAccount(AdAccountEntity adAccountEntity) {
        return accountDAO.updateAccount(adAccountEntity);
    }

    public boolean updateBalanceAndCostById(String id, Double balance) {
        return accountDAO.updateBalanceAndCostById(id, balance);
    }

    public boolean updateRefreshTokenByAccountId(String refreshToken, String accessToken, Long accountId) {
        return accountDAO.updateRefreshTokenByAccountId(refreshToken, accessToken, accountId);
    }

    public boolean updateTokenByAuthAccountId(String ea, Long authAccountId, String refreshToken, String accessToken) {
        return accountDAO.updateTokenByAuthAccountId(ea, authAccountId, refreshToken, accessToken);
    }

    public boolean updateRefreshTokenById(String refreshToken, String accessToken, String id) {
        return accountDAO.updateRefreshTokenById(refreshToken, accessToken, id);
    }

    public boolean updateAccountRefreshData(AdAccountEntity adAccountEntity) {
        return accountDAO.updateAccountRefreshData(adAccountEntity);
    }

    public AdAccountEntity queryAccountByEaAndAccountId(String ea, Long accountId, String source) {
        return accountDAO.queryAccountByEaAndAccountId(ea, accountId, source);
    }

    public AdAccountEntity queryAccountWithoutStatus(String ea, Long accountId, String source) {
        return accountDAO.queryAccountWithoutStatus(ea, accountId, source);
    }

    public AdAccountEntity queryEnableAccountById(String adAccountId) {
        return accountDAO.queryEnableAccountById(adAccountId);
    }

    public List<AdAccountEntity> queryEnableAccountByEa(String ea) {
        return accountDAO.queryEnableAccountByEa(ea);
    }


    public List<AdAccountEntity> queryAccountByEaAndSource(String ea, String adAccountId, String source, boolean containPrototypeAccount) {
        List<AdAccountEntity> adAccountEntityList = accountDAO.queryAccountByEaAndSource(ea, adAccountId, source);
        if (containPrototypeAccount) {
            List<AdAccountEntity> prototypeAccountList = getAdPrototypeRoomAccount(ea, source);
            prototypeAccountList.stream().filter(e -> adAccountId == null || e.getId().equals(adAccountId)).forEach(adAccountEntityList::add);
        }
        return adAccountEntityList;
    }

    @FilterLog
    public List<AdAccountEntity> queryAccountByEa(String ea, boolean containPrototypeAccount) {
        List<AdAccountEntity> adAccountEntityList = Lists.newArrayList();
        List<AdAccountEntity> list = accountDAO.queryAccountByEa(ea);
        if (CollectionUtils.isNotEmpty(list)) {
            adAccountEntityList.addAll(list);
        }
        if (containPrototypeAccount) {
            adAccountEntityList.addAll(getAdPrototypeRoomAccountByEa(ea));
        }
        return adAccountEntityList;
    }

    public AdAccountEntity queryAccountById(String id) {
        return accountDAO.queryAccountById(id);
    }

    public List<AdAccountEntity> queryAccountByIds(List<String> ids) {
        return accountDAO.queryAccountByIds(ids);
    }

    @FilterLog
    public List<AdAccountEntity> getAllAdAccount(boolean containPrototypeAccount) {
        List<AdAccountEntity> adAccountEntityList = accountDAO.getAllAdAccount();
        if (containPrototypeAccount) {
            adAccountEntityList.addAll(getAllAdPrototypeRoomAccount());
        }
        return adAccountEntityList;
    }

    public List<String> getBaiduAdAccountWithOutStatus() {
        return accountDAO.getBaiduAdAccountWithOutStatus();
    }

    public List<String> getHeadlinesAdAccountWithOutStatus() {
        return accountDAO.getHeadlinesAdAccountWithOutStatus();
    }

    public int updateAdAccountStatus(AdAccountEntity adAccountEntity) {
        return accountDAO.updateAdAccountStatus(adAccountEntity);
    }

    public int updateAdAccountStatusById(Integer status, String id) {
        return accountDAO.updateAdAccountStatusById(status, id);
    }

    public List<String> findAllEa(boolean containPrototypeAccount) {
        List<String> list = accountDAO.findAllEa();
        if (containPrototypeAccount) {
            getAllAdPrototypeRoomAccount().stream().map(AdAccountEntity::getEa).distinct().forEach(list::add);
        }
        return list;
    }

    public List<AdAccountEntity> getAdPrototypeRoomAccount(String ea, String source) {
        List<AdAccountEntity> adAccountEntityList = getAllAdPrototypeRoomAccount();
        return adAccountEntityList.stream().filter(e -> e.getEa().equals(ea) && (source == null || e.getSource().equals(source))).collect(Collectors.toList());
    }

    public AdAccountEntity getAdPrototypeRoomAccountById(String ea, String id) {
        List<AdAccountEntity> adAccountEntityList = getAllAdPrototypeRoomAccount();
        return adAccountEntityList.stream().filter(e -> e.getEa().equals(ea) && e.getId().equals(id)).findFirst().orElse(null);
    }

    public List<AdAccountEntity> getAdPrototypeRoomAccountBySource(String source) {
        List<AdAccountEntity> adAccountEntityList = getAllAdPrototypeRoomAccount();
        return adAccountEntityList.stream().filter(e -> e.getSource().equals(source)).collect(Collectors.toList());
    }

    public List<AdAccountEntity> getAdPrototypeRoomAccountByEa(String ea) {
        List<AdAccountEntity> adAccountEntityList = getAllAdPrototypeRoomAccount();
        return adAccountEntityList.stream().filter(e -> e.getEa().equals(ea)).collect(Collectors.toList());
    }

    public List<AdAccountEntity> getAllAdPrototypeRoomAccount() {
        String finalAdPrototypeRoomAccount = I18nUtil.getSuitedLangText(adPrototypeRoomAccount, adPrototypeRoomAccountEN);
        return JSONArray.parseArray(finalAdPrototypeRoomAccount, AdAccountEntity.class);
    }

    public boolean isPrototypeRoomAccount(String ea, String id) {
        return getAdPrototypeRoomAccountById(ea, id) != null;
    }

    public List<AdAccountEntity> getByUserName(String ea, String adUsername) {
        return accountDAO.queryAccountByUsername(ea, adUsername);
    }

    public List<AdAccountEntity> getAllByEa(String ea) {
        return accountDAO.getAllByEa(ea);
    }

    public List<AdAccountEntity> queryByName(String ea, String userName, String source) {
        return accountDAO.queryByName(ea, userName, source);
    }

    public List<AdAccountEntity> queryAccountByAccountIdList(String ea, List<Long> accountIdList) {
        return accountDAO.queryAccountByAccountIdList(ea, accountIdList);
    }

    public List<AdAccountEntity> queryByAuthAccountId(String ea, long authAccountId) {
        return accountDAO.queryByAuthAccountId(ea, authAccountId);
    }

    public AdAccountEntity queryAccountByAccountId(String ea, Long accountId) {
        return accountDAO.queryAccountByAccountId(ea, accountId);
    }

    public AdAccountEntity queryAccountBySourceAndAccountId(String ea, Long accountId, String source) {
        return accountDAO.queryAccountBySourceAndAccountId(ea, accountId, source);
    }

    public AdAccountEntity queryAllStatusAccountByAccountId(String ea, Long accountId) {
        return accountDAO.queryAllStatusAccountByAccountId(ea, accountId);
    }



    public List<AdAccountEntity> queryAllLocalAccount(String source, Integer type) {
        return accountDAO.queryAllLocalAdAccount(source, type);
    }

    public List<AdAccountEntity> queryALlLocalAccountByEa(String source, Integer type, String ea) {
        return accountDAO.queryAllLocalAdAccountByEa(source, type, ea);
    }
}
