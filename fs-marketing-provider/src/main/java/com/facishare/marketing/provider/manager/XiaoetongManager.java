/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.dbroute.TenantService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.result.live.XiaoetongAccessTokenResult;
import com.facishare.marketing.api.result.live.XiaoetongAccountResult;
import com.facishare.marketing.api.result.live.XiaoetongApiBaseResult;
import com.facishare.marketing.api.result.live.XiaoetongLiveListResult;
import com.facishare.marketing.api.vo.live.BindXiaoetongAccountVO;
import com.facishare.marketing.api.vo.live.ListVO;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.contstant.live.XiaoetongApiConstant;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataSourceTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2FieldTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.live.LiveUserActionTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.live.*;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignWxDTO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.live.*;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.innerArg.CreateOrUpdateMarketingLeadSyncRecordObjArg;
import com.facishare.marketing.provider.innerArg.live.*;
import com.facishare.marketing.provider.innerData.live.*;
import com.facishare.marketing.provider.innerData.live.xiaoetong.XiaoetongApiGetLiveDetailResult;
import com.facishare.marketing.provider.innerData.live.xiaoetong.XiaoetongUserData;
import com.facishare.marketing.provider.innerData.live.xiaoetong.XiaoetongGetLiveOverviewData;
import com.facishare.marketing.provider.innerData.live.xiaoetong.arg.XiaoetongApiGetLiveDetailArg;
import com.facishare.marketing.provider.innerData.live.xiaoetong.arg.XiaoetongApiGetUserArg;
import com.facishare.marketing.provider.innerData.live.xiaoetong.result.XiaoetongApiGetUserResult;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("xiaoetongManager")
@Slf4j
public class XiaoetongManager {

    // 小鹅通直播拉取时间节点（只拉取该时间点之后的数据）
    public static final String END_SYNC_CREATETIME = "2022-06-30 16:00:00";

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private XiaoetongAccountDAO xiaoetongAccountDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;
    @Autowired
    private LiveUserAccountRelationDAO liveUserAccountRelationDAO;
    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;
    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;
    @Autowired
    private ActionManager actionManager;
    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;
    @Autowired
    private TenantService tenantService;

    public Result<Void> bindXiaoketongAccount(BindXiaoetongAccountVO vo){
        XiaoetongAccountEntity xiaoetongAccountEntity =xiaoetongAccountDAO.getByEa(vo.getEa());
        if (xiaoetongAccountEntity != null){
            xiaoetongAccountEntity.setAppId(vo.getAppId());
            xiaoetongAccountEntity.setSecretKey(vo.getSecretKey());
            xiaoetongAccountEntity.setClientId(vo.getClientId());
            xiaoetongAccountDAO.update(xiaoetongAccountEntity);
        } else {
            xiaoetongAccountEntity = new XiaoetongAccountEntity();
            xiaoetongAccountEntity.setId(UUIDUtil.getUUID());
            xiaoetongAccountEntity.setEa(vo.getEa());
            xiaoetongAccountEntity.setAppId(vo.getAppId());
            xiaoetongAccountEntity.setClientId(vo.getClientId());
            xiaoetongAccountEntity.setSecretKey(vo.getSecretKey());
            xiaoetongAccountDAO.insert(xiaoetongAccountEntity);
        }
        return Result.newSuccess();
    }

    /**
     * 获取小鹅通账号信息
     * @param ea
     * @return
     */
    public Result<XiaoetongAccountResult> getXiaoetongAccount(String ea){
        XiaoetongAccountEntity entity = xiaoetongAccountDAO.getByEa(ea);
        XiaoetongAccountResult result = new XiaoetongAccountResult();
        if (Objects.nonNull(entity)) {
            result.setIsBind(true);
            result.setAppId(entity.getAppId());
            result.setSecretKey(entity.getSecretKey());
            result.setClientId(entity.getClientId());
        } else {
            result.setIsBind(false);
        }
        return Result.newSuccess(result);
    }

    public Optional<MarketingLiveEntity> getMarketingLiveByXiaoetongId(String ea, String xiaoetongId){
        MarketingLiveEntity entity = marketingLiveDAO.getMarketingLiveByXiaoetongId(ea, xiaoetongId);
        if (entity == null){
            return Optional.empty();
        }
        return Optional.of(entity);
    }

    /**
     * 测试接口是否联通
     * @param ea
     * @return
     */
    public Result testApi(String ea){
        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
        if (xiaoetongAccountEntity == null){
            return Result.newError(SHErrorCode.UN_BIND_XIAOETONG_ACCOUNT);
        }

        String appId = xiaoetongAccountEntity.getAppId();
        String clientId = xiaoetongAccountEntity.getClientId();
        String secretKey = xiaoetongAccountEntity.getSecretKey();

        String url = "https://api.xiaoe-tech.com/token?" + "app_id=" + appId + "&client_id=" + clientId + "&secret_key=" + secretKey + "&grant_type=client_credential";
        XiaoetongApiBaseResult<XiaoetongAccessTokenResult> result = httpManager.executeGetHttp(url, new TypeToken<XiaoetongApiBaseResult<XiaoetongAccessTokenResult>>(){});
        if (!result.isSuccess() || result.getData() == null || StringUtils.isEmpty(result.getData().getAccessToken())){
            return Result.newError(SHErrorCode.XIAOETONG_NOT_BIND_ACCOUNT);
        }
        String accessToken = result.getData().getAccessToken();
        redisManager.setXiaoetongAccessToken(ea, appId, accessToken, result.getData().getExpiresIn());
        return Result.newSuccess();
    }

    public Optional<String> getAccessToken(String ea, String appId, String appSecret, String clientId) {
        if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(appId) || Strings.isNullOrEmpty(appSecret)) {
            return null;
        }

        String accessToken = redisManager.getXiaoetongAccessToken(ea, appId);
        if (StringUtils.isNotEmpty(accessToken)){
            return Optional.of(accessToken);
        }

        // 加锁，防止并发获取Token
        String lockKey = "marketing_xiaoetongTokenLock_" + ea + "_" + appId;
        try {
            if (!redisManager.lock(lockKey, 5)) { // 5秒内不允许重复获取
                return Optional.empty();
            }
            String url = "https://api.xiaoe-tech.com/token?" + "app_id=" + appId + "&client_id=" + clientId + "&secret_key=" + appSecret + "&grant_type=client_credential";
            XiaoetongApiBaseResult<XiaoetongAccessTokenResult> result = httpManager.executeGetHttp(url, new TypeToken<XiaoetongApiBaseResult<XiaoetongAccessTokenResult>>(){});
            if (!result.isSuccess() || result.getData() == null || StringUtils.isEmpty(result.getData().getAccessToken())){
                log.info("xiaoetongApi.getAccessToken failed ea:{} result:{}", ea, result);
                return Optional.empty();
            }
            accessToken = result.getData().getAccessToken();
            redisManager.setXiaoetongAccessToken(ea, appId, accessToken, result.getData().getExpiresIn());
            return Optional.of(accessToken);
        } catch (Exception e) {
            log.warn("XiaoetongManager -> getAccessToken, ea:{} exception:", ea, e);
            return Optional.empty();
        } finally {
            redisManager.unLock(lockKey);
        }
    }

    /**
     * 根据EA获取小鹅通Token
     * @param ea
     * @return
     */
    public Optional<String> getAccessTokenByEa(String ea) {
        if (Strings.isNullOrEmpty(ea)) {
            return null;
        }

        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
        if (xiaoetongAccountEntity == null){
            log.info("XiaoetongManager -> getAccessTokenByEa not bind xiaoetongAccount 未绑定小鹅通账号 ea:{}", ea);
            return null;
        }

        return getAccessToken(ea, xiaoetongAccountEntity.getAppId(), xiaoetongAccountEntity.getSecretKey(), xiaoetongAccountEntity.getClientId());
    }

    public Result<PageResult<com.facishare.marketing.api.result.live.XiaoetongLiveListResult>> getLiveList(ListVO vo){
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(0);
        List<com.facishare.marketing.api.result.live.XiaoetongLiveListResult> data = Lists.newArrayList();
        pageResult.setResult(data);
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setPageNum(vo.getPageNum());

        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(vo.getEa());
        if (xiaoetongAccountEntity == null){
            return Result.newSuccess(pageResult);
        }
        Optional<String> accessOpt = getAccessToken(vo.getEa(), xiaoetongAccountEntity.getAppId(), xiaoetongAccountEntity.getSecretKey(), xiaoetongAccountEntity.getClientId());
        if (!accessOpt.isPresent()) {
            return Result.newError(SHErrorCode.GET_XIAOETONG_ACCESS_TOKEN_FAILED);
        }

        String url = "https://api.xiaoe-tech.com/xe.alive.list.get/1.0.0";
        XiaoetongLiveListArg arg = new XiaoetongLiveListArg();
        arg.setAccessToken(accessOpt.get());
        arg.setPage(vo.getPageNum());
        arg.setPageSize(vo.getPageSize());
        arg.setSearchContent(vo.getKeyword());
        XiaoetongApiBaseResult<XiaoetongLiveListInnerData> listResult = httpManager.executePostHttp(arg, url, new TypeToken<XiaoetongApiBaseResult<XiaoetongLiveListInnerData>>(){});
        if (!listResult.isSuccess()){
            log.warn("xiaoetongApi.getLiveList failed vo:{} result:{}", vo, listResult);
            return Result.newError(SHErrorCode.LIST_XIAOKETONG_LIVE_FAILED);
        }
        if (CollectionUtils.isEmpty(listResult.getData().getList())){
            return Result.newSuccess(pageResult);
        }

        for (XiaoetongLiveListInnerData.liveListEntity liveListEntity : listResult.getData().getList()){
            com.facishare.marketing.api.result.live.XiaoetongLiveListResult liveListResult = BeanUtil.copy(liveListEntity, com.facishare.marketing.api.result.live.XiaoetongLiveListResult.class);
            liveListResult.setPageUrl(liveListResult.getPageUrl());
            data.add(liveListResult);
        }
        pageResult.setTotalCount(listResult.getData().getTotal());
        pageResult.setPageNum(listResult.getData().getPage());
        pageResult.setPageSize(listResult.getData().getPageCount());

        return Result.newSuccess(pageResult);
    }

    /**
     * 获取小鹅通直播详情
     * @param ea
     * @param thirdPlatformId
     * @return
     */
    public XiaoetongApiGetLiveDetailResult getLiveDetail(String ea, String thirdPlatformId){
        if (StringUtils.isBlank(thirdPlatformId)) {
            return null;
        }
        Optional<String> accessOpt = getAccessTokenByEa(ea);
        if (!accessOpt.isPresent()) {
            log.info("XiaoetongManager -> getLiveDetail getAccessTokenByEa failed ea:{}", ea);
            return null;
        }

        XiaoetongApiGetLiveDetailArg arg = new XiaoetongApiGetLiveDetailArg();
        arg.setId(thirdPlatformId);
        arg.setAccessToken(accessOpt.get());
        XiaoetongApiBaseResult<XiaoetongApiGetLiveDetailResult> apiResult = httpManager.executePostHttp(arg, XiaoetongApiConstant.GET_LIVE_DETAIL_URL, new TypeToken<XiaoetongApiBaseResult<XiaoetongApiGetLiveDetailResult>>() {
        });
        if (!apiResult.isSuccess()) {
            log.info("XiaoetongManager -> getLiveDetail failed ea:{}, result:{}", ea, apiResult);
            return null;
        }
        return apiResult.getData();
    }

    public Optional<String> loginXiaoetongUser(String ea, String phone, String name){
        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
        if (xiaoetongAccountEntity == null){
            return Optional.empty();
        }

        Optional<String> accessOpt = getAccessToken(ea, xiaoetongAccountEntity.getAppId(), xiaoetongAccountEntity.getSecretKey(), xiaoetongAccountEntity.getClientId());
        if (!accessOpt.isPresent()) {
            return Optional.empty();
        }

        XiaoetongLoginArg arg = new XiaoetongLoginArg();
        arg.setAccessToken(accessOpt.get());
        XiaoetongLoginArg.LoginData loginData = new XiaoetongLoginArg.LoginData();
        loginData.setPhone(phone);
        if (name != null) {
            loginData.setNickName(name);
        }
        arg.setData(loginData);

        String url = "https://api.xiaoe-tech.com/xe.user.register/1.0.0";
        XiaoetongApiBaseResult<XiaoetongLoginInnerData> loginResult = httpManager.executePostHttp(arg, url, new TypeToken<XiaoetongApiBaseResult<XiaoetongLoginInnerData>>(){});
        if (!loginResult.isSuccess()){
            log.warn("xiaoetongApi.loginXiaoetongUser failed ea:{} result:{}", ea, loginResult);
            return Optional.empty();
        }

        return Optional.of(loginResult.getData().getUserId());
    }

    public Optional<String> getLoginedUserInfo(String ea, String phone) {
        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
        if (xiaoetongAccountEntity == null) {
            return Optional.empty();
        }

        Optional<String> accessOpt = getAccessToken(ea, xiaoetongAccountEntity.getAppId(), xiaoetongAccountEntity.getSecretKey(), xiaoetongAccountEntity.getClientId());
        if (!accessOpt.isPresent()) {
            return Optional.empty();
        }

        QueryLoginedUserByPhoneArg arg = new QueryLoginedUserByPhoneArg();
        arg.setAccessToken(accessOpt.get());
        QueryLoginedUserByPhoneArg.UserDataArg userDataArg = new QueryLoginedUserByPhoneArg.UserDataArg();
        userDataArg.setPhone(phone);
        List<String> fieldList = Lists.newArrayList();
        fieldList.add("nickname");
        fieldList.add("name");
        arg.setData(userDataArg);
        userDataArg.setFieldList(fieldList);

        String url = "https://api.xiaoe-tech.com/xe.user.info.get/1.0.0";
        XiaoetongApiBaseResult<LoginedUserInfoInnerData> userInfoResult = httpManager.executePostHttp(arg, url, new TypeToken<XiaoetongApiBaseResult<LoginedUserInfoInnerData>>() {
        });
        if (userInfoResult.isSuccess()) {
            return Optional.of(userInfoResult.getData().getUserId());
        }
        return Optional.empty();
    }

    public Optional<String> queryUserLoginRedirect(String ea, String userId, String redirectUrl){
        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
        if (xiaoetongAccountEntity == null){
            log.info("XiaoetongManager.queryUserLoginRedirect ea not bind xiaoetong ea:{}", ea);
            return Optional.empty();
        }

        Optional<String> accessOpt = getAccessToken(ea, xiaoetongAccountEntity.getAppId(), xiaoetongAccountEntity.getSecretKey(), xiaoetongAccountEntity.getClientId());
        if (!accessOpt.isPresent()) {
            log.info("XiaoetongManager.queryUserLoginRedirect get access token failed ea:{}", ea);
            return Optional.empty();
        }
        XiaoetongLoginedUserRedirectArg arg = new XiaoetongLoginedUserRedirectArg();
        arg.setAccessToken(accessOpt.get());
        arg.setUserId(userId);
        XiaoetongLoginedUserRedirectArg.UserRedirectArg userRedirectArg = new XiaoetongLoginedUserRedirectArg.UserRedirectArg();
        userRedirectArg.setLoginType(2);
        userRedirectArg.setRedirectUrl(redirectUrl);
        arg.setData(userRedirectArg);
        String url = "https://api.xiaoe-tech.com/xe.login.url/1.0.0";
        XiaoetongApiBaseResult<XiaoetongLoginedUserRedirectResult> loginUserRedirectResult = httpManager.executePostHttp(arg, url, new TypeToken<XiaoetongApiBaseResult<XiaoetongLoginedUserRedirectResult>>(){});
        if (!loginUserRedirectResult.isSuccess()){
            log.info("xiaoetongApi.queryUserLoginRedirect execute post xe.login.url failed ea:{} arg:{} loginUserRedirectResult:{}", ea, arg, loginUserRedirectResult);
            return Optional.empty();
        }

        return Optional.of(loginUserRedirectResult.getData().getLoginUrl());
    }

    /**
     * 拉取小鹅通直播数据
     * @param ea
     * @param liveId
     * @return
     */
    public XiaoetongLiveRecordByLiveData getLiveRecordDataByLiveNew(String ea, String liveId){
        // 获取Token
        Optional<String> accessOpt = getAccessTokenByEa(ea);
        if (!accessOpt.isPresent()) {
            return null;
        }

        XiaoetongLiveRecordByLiveData resultData = new XiaoetongLiveRecordByLiveData();

        // 先查询一次，取出total
        LiveRecordDataByLiveArg arg = new LiveRecordDataByLiveArg();
        LiveRecordDataByLiveArg.LiveRecordDataInnerDataArg innerDataArg = new LiveRecordDataByLiveArg.LiveRecordDataInnerDataArg();
        innerDataArg.setMaxRecord(true);
        innerDataArg.setSearch(false);
        innerDataArg.setUserIds(Lists.newArrayList());
        innerDataArg.setSearch_max_learn_progress(0);
        arg.setAccessToken(accessOpt.get());
        arg.setResourceId(liveId);
        arg.setPage(1);
        arg.setPageSize(1);
        arg.setLiveRecordDataInnerDataArg(innerDataArg);
        XiaoetongLiveRecordByLiveData firstData = getLearnRecordFromApi(arg);
        if (Objects.nonNull(firstData) && firstData.getTotal() > 0) {
            ArrayList<XiaoetongLiveRecordByLiveData.RecordByLiveInnerData> objects = Lists.newArrayList();
            // 根据total计算页数，分多次查询
            int total = firstData.getTotal();
            int pageSize = 100; // 每次100条
            int totalPage = total / pageSize + 1;
            for (int i = 0; i < totalPage; i++) {
                arg.setPage(i+1);
                arg.setPageSize(pageSize);
                XiaoetongLiveRecordByLiveData recordByLiveData = getLearnRecordFromApi(arg);
                if (Objects.nonNull(recordByLiveData) && CollectionUtils.isNotEmpty(recordByLiveData.getList())) {
                    objects.addAll(recordByLiveData.getList());
                }
            }
            log.info("getLiveRecordDataByLiveNew 小鹅通直播数据总数 ea:{}, xiaoetongLiveId:{}, total:{}", ea, liveId, total);
            log.info("getLiveRecordDataByLiveNew 小鹅通直播数据 ea:{}, xiaoetongLiveId:{}, objects:{}", ea, liveId, objects);
            resultData.setList(objects);
            resultData.setTotal(total);
        }
        return resultData;
    }

    private XiaoetongLiveRecordByLiveData getLearnRecordFromApi(LiveRecordDataByLiveArg arg){
        try {
            XiaoetongApiBaseResult<XiaoetongLiveRecordByLiveData> recordDataResult = httpManager.executePostHttp(arg, XiaoetongApiConstant.LEANING_RECORD_BY_RESOURCE_URL, new TypeToken<XiaoetongApiBaseResult<XiaoetongLiveRecordByLiveData>>() {});
            if (!recordDataResult.isSuccess()) {
                log.info("XiaoetongManager -> getLearnRecordFromApi failed 获取小鹅通直播数据失败 arg:{} result:{}", arg, recordDataResult);
                return null;
            }
            return recordDataResult.getData();
        }catch (Exception e){
            log.error("XiaoetongManager -> getLearnRecordFromApi error:{}", e);
            return null;
        }
    }

    /**
     * 根据小鹅通直播用户信息（单个用户信息接口获取）
     * @param ea
     * @param userIds
     * @param xiaoetongLiveId
     * @return
     */
    public List<XiaoetongUserData> getUsersByApiV2(String ea, List<String> userIds, String xiaoetongLiveId){
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }

        // 取出Token
        Optional<String> accessOpt = getAccessTokenByEa(ea);
        if (!accessOpt.isPresent()) {
            return null;
        }

        // 批量并行拉取数据
        List<XiaoetongUserData> resultList = Lists.newCopyOnWriteArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(userIds.size());
        for (String userId : userIds) {
            ThreadPoolUtils.execute(() -> {
                try {
                    // 查询用户详情
                    XiaoetongApiGetUserResult getUserResult = getUser(ea, userId);
                    if (getUserResult != null) {
                        XiaoetongUserData xiaoetongUserData = BeanUtil.copy(getUserResult, XiaoetongUserData.class);
                        resultList.add(xiaoetongUserData);
                    }
                } catch (Exception e) {
                    log.warn("async getUser error e:", e);
                } finally {
                    countDownLatch.countDown();
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.QYWX_ADDRESS_BOOK);
        }
        try {
            countDownLatch.await(60L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("XiaoetongManager -> getUsersByApiV2 error e:{}", e);
        }
        log.info("XiaoetongManager -> getUsersByApiV2 success 小鹅通用户数据 xiaoetongLiveId:{}, size:{}, result:{}", xiaoetongLiveId, resultList.size(), resultList);
        return resultList;
    }

    /**
     * 拉取小鹅通直播概览数据
     * @param ea
     * @param liveId 资源id
     * @param type 直播分类（1：全部；2：直播中；3:回放; 4:在线）
     * @return
     */
    public XiaoetongApiBaseResult<XiaoetongGetLiveOverviewData> getLiveOverviewByApi(String ea, String liveId, String type){
        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
        if (xiaoetongAccountEntity == null){
            log.info("XiaoetongManager -> getLiveOverviewByApi not bind xiaoetongAccount 未绑定小鹅通账号 ea:{}", ea);
            return null;
        }

        Optional<String> accessOpt = getAccessToken(ea, xiaoetongAccountEntity.getAppId(), xiaoetongAccountEntity.getSecretKey(), xiaoetongAccountEntity.getClientId());
        if (!accessOpt.isPresent()) {
            return null;
        }

        Map<String, String> arg = Maps.newHashMap();
        arg.put("access_token", accessOpt.get());
        arg.put("resource_id", liveId);
        arg.put("type", type);

        try {
            XiaoetongApiBaseResult<XiaoetongGetLiveOverviewData> recordDataResult = httpManager.executePostHttp(arg, XiaoetongApiConstant.LIVE_OVERVIEW_URL, new TypeToken<XiaoetongApiBaseResult<XiaoetongGetLiveOverviewData>>() {});
            if (recordDataResult == null) {
                log.info("XiaoetongManager -> getLiveOverviewByApi failed null, url:{} arg:{}", arg, XiaoetongApiConstant.LIVE_OVERVIEW_URL);
                return null;
            }
            if (!recordDataResult.isSuccess()) {
                log.info("XiaoetongManager -> getLiveOverviewByApi failed 获取小鹅通直播概览数据失败 ea:{} arg:{} result:{}", ea, arg, recordDataResult);
            } else {
                log.info("XiaoetongManager -> getLiveOverviewByApi success 获取小鹅通直播概览数据成功 ea:{} arg:{} result:{}", ea, arg, recordDataResult);
            }
            return recordDataResult;
        }catch (Exception e){
            log.info("XiaoetongManager -> getLiveOverviewByApi error ea:{} arg:{}", ea, arg, e);
            return null;
        }
    }

    /**
     * 获取小鹅通单个用户信息
     * @param ea
     * @param userId
     * @return
     */
    private XiaoetongApiGetUserResult getUser(String ea, String userId){
        try {
            // 获取Token
            Optional<String> accessOpt = getAccessTokenByEa(ea);
            if (!accessOpt.isPresent()) {
                return null;
            }

            XiaoetongApiGetUserArg arg = new XiaoetongApiGetUserArg();
            arg.setAccessToken(accessOpt.get());
            arg.setUserId(userId);
            XiaoetongApiGetUserArg.ParamData paramData = new XiaoetongApiGetUserArg.ParamData();
            ArrayList<String> fieldList = Lists.newArrayList("nickname", "name", "phone", "phone_collect", "gender", "city", "province", "country",
                    "birth", "address", "company", "job", "wx_account", "created_at", "wx_email", "industry");
            paramData.setFiledList(fieldList);
            arg.setData(paramData);
            XiaoetongApiBaseResult<XiaoetongApiGetUserResult> result = httpManager.executePostHttp(arg, XiaoetongApiConstant.GET_USER_URL, new TypeToken<XiaoetongApiBaseResult<XiaoetongApiGetUserResult>>() {
            });
            if (!result.isSuccess()) {
                log.info("XiaoetongManager -> getUser failed 获取小鹅通用户数据失败 ea:{} arg:{} result:{}", ea, arg, result);
                return null;
            }
            log.info("XiaoetongManager -> getUser success 获取小鹅通用户数据 ea:{} arg:{} result:{}", ea, arg, result);
            return result.getData();
        } catch (Exception e) {
            log.error("XiaoetongManager -> getUser error:{}", e);
            return null;
        }
    }

    public void syncXiaoetongLiveStatusByEa(String ea, String thirdPlatformId){
        XiaoetongApiGetLiveDetailResult liveDetail = getLiveDetail(ea, thirdPlatformId);
        if (liveDetail == null) {
            return;
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);

        Integer aliveState = liveDetail.getAliveState();
        int marketingLiveStatus = LiveStatusEnum.NOT_START.getStatus();
        if (aliveState == 0) {
            marketingLiveStatus = LiveStatusEnum.NOT_START.getStatus();
        } else if (aliveState == 1) {
            marketingLiveStatus = LiveStatusEnum.PROCESSING.getStatus();
        } else if (aliveState == 2) {
            marketingLiveStatus = LiveStatusEnum.FINISH.getStatus();
        }

        marketingLiveDAO.updateXiaoetongLiveStatus(ei, thirdPlatformId, marketingLiveStatus);
        marketingLiveStatisticsDAO.updateXiaoetongLiveStatus(ei, thirdPlatformId, marketingLiveStatus);
    }

    private void updateXiaoetongLiveStatus(String ea, List<XiaoetongLiveListResult> liveListResults){
        if (CollectionUtils.isEmpty(liveListResults)){
            return;
        }
        for (XiaoetongLiveListResult result : liveListResults) {
            String statusString = result.getAliveState();
            String xiaoetongLiveId = result.getId();
            if (StringUtils.isEmpty(statusString)){
                return;
            }
            int statusValue = Integer.parseInt(statusString);
            int marketingLiveStatus = LiveStatusEnum.NOT_START.getStatus();
            if (statusValue == 0) {
                marketingLiveStatus = LiveStatusEnum.NOT_START.getStatus();
            } else if (statusValue == 1) {
                marketingLiveStatus = LiveStatusEnum.PROCESSING.getStatus();
            } else if (statusValue == 2) {
                marketingLiveStatus = LiveStatusEnum.FINISH.getStatus();
            }

            marketingLiveDAO.updateXiaoetongLiveStatus(eieaConverter.enterpriseAccountToId(ea), xiaoetongLiveId, marketingLiveStatus);
            marketingLiveStatisticsDAO.updateXiaoetongLiveStatus(eieaConverter.enterpriseAccountToId(ea), xiaoetongLiveId, marketingLiveStatus);
        }
    }

    /**
     * 拉取小鹅通直播数据，并进行后续处理
     * @param ea
     * @param marketingEventId
     * @param xiaoetongLiveId
     */
    public void syncXiaoetongLiveDataByIdNew(String ea, String marketingEventId, String xiaoetongLiveId){
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
        if (marketingLiveEntity == null){
            return;
        }
        Date createTime = marketingLiveEntity.getCreateTime();
        // 2022-06-30 发版之前的数据不做处理
        Date endCreateTime = DateUtil.parse(END_SYNC_CREATETIME);
        if (createTime.compareTo(endCreateTime) < 0) {
            return;
        }

        // 拉取小鹅通直播数据
        XiaoetongLiveRecordByLiveData xiaoetongLiveResult = getLiveRecordDataByLiveNew(ea, xiaoetongLiveId);
        if (Objects.isNull(xiaoetongLiveResult) || CollectionUtils.isEmpty(xiaoetongLiveResult.getList())) {
            log.info("XiaoetongManager.syncXiaoetongLiveDataByIdNew no live data 未获取到小鹅通直播数据");
            return;
        }

        // 从直播数据中取出userId,并去重
        List<XiaoetongLiveRecordByLiveData.RecordByLiveInnerData> liveDataList = xiaoetongLiveResult.getList();
        List<String> userIds = liveDataList.stream().map(recordByLiveInnerData -> recordByLiveInnerData.getUserId()).distinct().collect(Collectors.toList());

        // 直播数据按用户分组
        List<XiaoetongLiveRecordByLiveData.RecordByLiveInnerData> xiaoetongLiveDataList = xiaoetongLiveResult.getList();
        Map<String, List<XiaoetongLiveRecordByLiveData.RecordByLiveInnerData>> xiaoetongLiveDataMap =
                xiaoetongLiveDataList.stream().collect(Collectors.groupingBy(XiaoetongLiveRecordByLiveData.RecordByLiveInnerData::getUserId));

        // 查询用户数据
        List<XiaoetongUserData> xiaoetongUserDataList = getUsersByApiV2(ea, userIds, xiaoetongLiveId);
        if (CollectionUtils.isEmpty(xiaoetongUserDataList)) {
            log.info("XiaoetongManager.syncXiaoetongLiveDataByIdNew no user data 未获取到小鹅通直播用户数据");
            return;
        }

        // 过滤出无手机号但是存在openid的用户，用于后面批量查
        List<String> wxOpenIds = xiaoetongUserDataList.stream()
                .filter(data -> StringUtils.isBlank(data.getBindPhone()) && StringUtils.isBlank(data.getCollectPhone()) && StringUtils.isNotBlank(data.getWxOpenId()))
                .map(XiaoetongUserData::getWxOpenId)
                .collect(Collectors.toList());

        // 根据openid批量查报名信息手机号
        Map<String, String> phoneMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(wxOpenIds)) {
            List<CampaignWxDTO> campaignWxDTOS = campaignMergeDataDAO.getPhoneByOpenIds(ea, marketingEventId, wxOpenIds);
            if (CollectionUtils.isNotEmpty(campaignWxDTOS)) {
                phoneMap = campaignWxDTOS.stream().collect(Collectors.toMap(CampaignWxDTO::getOpenId, CampaignWxDTO::getPhone, (v1, v2) -> v1));
            }
        }

        // 填充手机号
        for (XiaoetongUserData userData : xiaoetongUserDataList) {
            if (StringUtils.isNotBlank(userData.getPhone())) {
                continue;
            }
            if (StringUtils.isNotBlank(userData.getCollectPhone())) {
                userData.setPhone(userData.getCollectPhone());
            } else if (StringUtils.isNotBlank(userData.getBindPhone())) {
                userData.setPhone(userData.getBindPhone());
            } else {
                String wxOpenId = userData.getWxOpenId();
                if (StringUtils.isBlank(wxOpenId)){
                    continue;
                }
                String phone = phoneMap.get(wxOpenId);
                if (StringUtils.isBlank(phone)) {
                    continue;
                }
                userData.setPhone(phone);
                userData.setCollectPhone(phone);
                userData.setBindPhone(phone);
            }
        }

        // 过滤掉没有手机号的数据（上面填充手机号后如果还是没有手机号，就无法匹配到报名人了）
        List<XiaoetongUserData> includePhoneUserDatas = xiaoetongUserDataList.stream().filter(xiaoetongUserData -> StringUtils.isNotBlank(xiaoetongUserData.getPhone())).collect(Collectors.toList());
        log.info("includePhoneUserDatas xiaoetongLiveId:{}, size:{}, result:{}", xiaoetongLiveId, includePhoneUserDatas.size(), includePhoneUserDatas);

        // 查询已有直播数据
        log.info("syncXiaoetongLiveDataByIdNew step1");
        List<LiveUserStatusEntity> oldLiveUserStatusList = liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveId(xiaoetongLiveId,ea);
        Map<String, LiveUserStatusEntity> oldLiveUserStatusMap = oldLiveUserStatusList.stream().filter(liveUserStatusEntity -> StringUtils.isNotBlank(liveUserStatusEntity.getPhone()))
                .collect(Collectors.toMap(LiveUserStatusEntity::getPhone, Function.identity(), (v1, v2) -> v1));

        int totalViewTimes = includePhoneUserDatas.size();
        int totalViewUser = includePhoneUserDatas.size();
        List<LiveUserStatusEntity> insertViewUserList = Lists.newArrayList();
        List<LiveUserStatusEntity> updateViewUserList = Lists.newArrayList();

        log.info("syncXiaoetongLiveDataByIdNew step2");
        for (XiaoetongUserData xiaoetongUserData : includePhoneUserDatas) {
            String xiaoetongUserId = xiaoetongUserData.getUserId();
            String phone = xiaoetongUserData.getPhone();
            List<XiaoetongLiveRecordByLiveData.RecordByLiveInnerData> innerDataList = xiaoetongLiveDataMap.get(xiaoetongUserId);
            if (CollectionUtils.isEmpty(innerDataList)){
                continue;
            }

            int viewTime = 0; //观看时间
            for (XiaoetongLiveRecordByLiveData.RecordByLiveInnerData recordByLiveInnerData : innerDataList){
                if (StringUtils.isNotEmpty(recordByLiveInnerData.getStayTime())){
                    viewTime += Integer.parseInt(recordByLiveInnerData.getStayTime());
                }
            }
            viewTime = viewTime / 60 == 0 ? 1 : viewTime / 60;

            // 更新观看数据live_user_status
            LiveUserStatusEntity liveUserStatusEntity;
            if (oldLiveUserStatusMap.containsKey(phone)) {
                // 更新
                liveUserStatusEntity = oldLiveUserStatusMap.get(phone);
                if (liveUserStatusEntity.getViewTime() != null && viewTime == liveUserStatusEntity.getViewTime()) {
                    continue;
                }
                // 判断小鹅通userid是否一致，防止手机号重复时，更新错乱
                if (!Objects.equals(liveUserStatusEntity.getOuterUserId(), xiaoetongUserId)) {
                    continue;
                }
                liveUserStatusEntity.setViewTime(viewTime);
                updateViewUserList.add(liveUserStatusEntity);
            } else {
                // 新增
                liveUserStatusEntity = new LiveUserStatusEntity();
                liveUserStatusEntity.setId(UUIDUtil.getUUID());
                liveUserStatusEntity.setEa(ea);
                liveUserStatusEntity.initStatus(Lists.newArrayList(LiveUserActionTypeEnum.VIEW));
                liveUserStatusEntity.setXiaoetongLiveId(xiaoetongLiveId);
                liveUserStatusEntity.setType(3);
                liveUserStatusEntity.setPhone(phone);
                liveUserStatusEntity.setViewTime(viewTime);
                liveUserStatusEntity.setLastViewTime(DateUtil.parse(innerDataList.get(0).getLastLearnTime()));
                liveUserStatusEntity.setOuterUserId(xiaoetongUserId);
                insertViewUserList.add(liveUserStatusEntity);
                // 生成crm线索
                ThreadPoolUtils.execute(() -> {
                    saveLeadsToCrm(xiaoetongUserData, ea, marketingEventId);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
            // 发送直播行为数据
            ThreadPoolUtils.execute(() -> {
                Optional<String> browserUserFinger = browserUserRelationManager.getOrCreateBrowserUserIdByPhone(ea, phone);
                RecordActionArg recordActionArg = new RecordActionArg();
                recordActionArg.setEa(ea);
                recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
                recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                recordActionArg.setObjectId(marketingLiveEntity.getId());
                recordActionArg.setMarketingEventId(marketingLiveEntity.getMarketingEventId());
                recordActionArg.setFingerPrint(browserUserFinger.get());
                if (liveUserStatusEntity.getViewTime() > 0) {
                    recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_LIVE.getActionType());
                    recordActionArg.setActionTime(System.currentTimeMillis());
                    actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        log.info("syncXiaoetongLiveDataByIdNew step3, insertViewUserList size:{}, updateViewUserList size:{}", insertViewUserList.size(), updateViewUserList.size());
        if (CollectionUtils.isNotEmpty(insertViewUserList)){
            PageUtil page = new PageUtil(insertViewUserList, 200);
            for (int i = 0; i < page.getPageCount(); i++){
                List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                liveUserStatusDAO.batchInsert(currentPage);
            }
        }
        if (CollectionUtils.isNotEmpty(updateViewUserList)){
            PageUtil page = new PageUtil(updateViewUserList, 200);
            for (int i = 0; i < page.getPageCount(); i++){
                List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                liveUserStatusDAO.batchUpdate(currentPage);
            }
        }

        List<MarketingLiveStatistics> marketingLiveStatisticsList = marketingLiveStatisticsDAO.getByXiaoetongLiveId(eieaConverter.enterpriseAccountToId(ea), xiaoetongLiveId);
        if (CollectionUtils.isEmpty(marketingLiveStatisticsList)){
            return;
        }
        log.info("syncXiaoetongLiveDataByIdNew step4");
        for (MarketingLiveStatistics marketingLiveStatistics : marketingLiveStatisticsList){
            if (marketingLiveStatistics.getTotalViewUsers() > totalViewUser){
                totalViewUser = marketingLiveStatistics.getTotalViewUsers();
            }
            if (marketingLiveStatistics.getViewTimes() > totalViewTimes){
                totalViewTimes = marketingLiveStatistics.getViewTimes();
            }
            // 回放次数更新
            int recordTimes = marketingLiveStatistics.getRecordTimes();
            XiaoetongApiBaseResult<XiaoetongGetLiveOverviewData> liveOverviewByApi = getLiveOverviewByApi(ea, xiaoetongLiveId, "3");
            if (Objects.nonNull(liveOverviewByApi)) {
                if (liveOverviewByApi.isSuccess()) {
                    XiaoetongGetLiveOverviewData.Stats stats = liveOverviewByApi.getData().getStats();
                    XiaoetongGetLiveOverviewData.StatsInnerData hisLearnedTimes = stats.getHisLearnedTimes();
                    //recordTimes = hisLearnedTimes.getValue();
                }
            }
            marketingLiveStatisticsDAO.updateViewTimesAndViewUsers(marketingLiveStatistics.getId(), totalViewUser, totalViewTimes, recordTimes,marketingLiveStatistics.getCorpId());
        }
    }

    // 构建活动成员对象
    private CampaignMergeDataEntity buildCampaignMemberObjData(String ea, Integer bindCrmObjectType, String bindCrmObjectId, String marketingEventId, String outerUserId, Boolean addCampaignMember) {
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
        if (campaignMergeDataObjectTypeEnum == null) {
            return null;
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        } catch (Exception e) {
            log.warn("BaiduCampaignService.buildCampaignMemberObjData error e:{}", e);
        }
        if (objectData == null) {
            return null;
        }

        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        String phone = campaignMergeDataManager.getPhoneByObject(objectData);
        String name = objectData.getName();
        String campaignId = UUIDUtil.getUUID();
        campaignMergeDataEntity.setId(campaignId);
        campaignMergeDataEntity.setEa(ea);
        campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        campaignMergeDataEntity.setBindCrmObjectId(bindCrmObjectId);
        campaignMergeDataEntity.setBindCrmObjectType(bindCrmObjectType);
        campaignMergeDataEntity.setName(name);
        campaignMergeDataEntity.setPhone(phone);
        campaignMergeDataEntity.setCreateTime(new Date());
        campaignMergeDataEntity.setUpdateTime(new Date());
        campaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType());
        campaignMergeDataEntity.setOuterUserId(outerUserId);
        campaignMergeDataEntity.setAddCampaignMember(addCampaignMember);

        return campaignMergeDataEntity;
    }

    public Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity campaignMergeDataEntity) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());

        return dataMap;
    }

    public String syncCampaignMember(String ea, String leadId, String marketingEventId, String outerUserId, Boolean addCampaignMember, String phone) {
        // 判断当前手机号有无报名数据
        List<CampaignMergeDataEntity> campaignMergeDataEntites = campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(ea, marketingEventId, phone);
        if (CollectionUtils.isNotEmpty(campaignMergeDataEntites)) {
            // 已经报名
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntites.get(0);
            campaignMergeDataDAO.updateCampaignMergeDataOuterUserId(ea, outerUserId, campaignMergeDataEntity.getId());
            return campaignMergeDataEntity.getId();
        } else {
            // 未报名，生成活动成员
            CampaignMergeDataEntity campaignMergeDataEntity = buildCampaignMemberObjData(ea, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId, outerUserId, addCampaignMember);
            if (campaignMergeDataEntity == null) {
                return null;
            }

            Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity);
            String campaignMemberObjId = crmV2Manager.addCampaignMembersObjByLock(ea, dataMap, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId);
            if (campaignMemberObjId != null) {
                campaignMergeDataEntity.setCampaignMembersObjId(campaignMemberObjId);
                campaignMergeDataManager.addCampaignDataOnlyUnLock(campaignMergeDataEntity);
                log.info("XiaoetongManager syncCampaignMember success, data:{}", campaignMergeDataEntity);
            }

            String campaignMemberId = campaignMergeDataEntity.getId();
            if (StringUtils.isNotBlank(campaignMemberId)) {
                campaignMergeDataDAO.updateCampaignMergeDataOuterUserId(ea, outerUserId, campaignMemberId);
            }
            return campaignMemberId;
        }
    }

    private Map<String, Object> buildLeadsFieldData(XiaoetongUserData data, String ea) {
        // 查询小鹅通设置的线索映射关系
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPluginFieldMap(ea, MarketingPluginTypeEnum.XIAOETONG_LIVE.getType(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (marketingPluginConfigEntity == null || marketingPluginConfigEntity.getCrmFormFieldMap() == null) {
            log.info("XiaoetongManager.buildLeadsFieldData error 未设置线索映射 ea:{}", ea);
            return null;
        }
        FieldMappings crmFormFieldMap = marketingPluginConfigEntity.getCrmFormFieldMap();
        Map<String, Object> param = createObjectDataToCrmLeadFieldDataMap(ea, data, crmFormFieldMap, marketingPluginConfigEntity.getCrmPoolId(), marketingPluginConfigEntity.getCrmRecordType());
        return param;
    }

    private Map<String, Object> createObjectDataToCrmLeadFieldDataMap(String ea, XiaoetongUserData data, FieldMappings crmFormFieldMap, String crmPoolId, String crmRecordType) {
        Map<String, Object> crmData = new HashMap<>();
        // 获取线索描述
        Map<String, CrmUserDefineFieldVo> fieldTypeMap = Maps.newHashMap();
        try {
            List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD);
            if (CollectionUtils.isNotEmpty(crmUserDefineFieldVoList)) {
                fieldTypeMap = crmUserDefineFieldVoList.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, Function.identity(), (v1, v2) -> v1));
            }
        } catch (Exception e) {
            log.warn("XiaoetongManager.createObjectDataToCrmLeadFieldDataMap error e:{}", e);
            return null;
        }

        for (FieldMappings.FieldMapping fieldMapping : crmFormFieldMap){
            if (StringUtils.isBlank(fieldMapping.getCrmFieldName())) {
                continue;
            }
            String mankeepFieldName = fieldMapping.getMankeepFieldName();
            Object mankeepFieldValue = data.getFieldValueByName(mankeepFieldName);
            String crmFieldName = fieldMapping.getCrmFieldName();
            String fieldType = fieldTypeMap.get(fieldMapping.getCrmFieldName()) == null ? null : fieldTypeMap.get(fieldMapping.getCrmFieldName()).getFieldTypeName();
            Object crmFieldValue = mankeepFieldValue;
            if (Objects.equals(fieldType, CrmV2FieldTypeEnum.SelectOne.getName())) {
                // 单选
                if (fieldMapping.getValuesOptions() != null) {
                    crmFieldValue = fieldMapping.getValuesOptions().get(mankeepFieldValue + "");
                }
            }

            // 若为空，取设置的默认值
            if (crmFieldValue == null) {
                if (StringUtils.isNotBlank(fieldType) && fieldType.equals(CrmV2FieldTypeEnum.SelectMany.getName())) {
                    crmFieldValue = Lists.newArrayList(fieldMapping.getDefaultValue());
                } else {
                    crmFieldValue = fieldMapping.getDefaultValue();
                }
            }

            log.info("this fieldMapping:{},  fieldValue:{}", fieldMapping, crmFieldValue);
            crmData.put(crmFieldName, crmFieldValue);
        }
        if (StringUtils.isNotBlank(crmPoolId)) {
            crmData.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), crmPoolId);
        }
        crmData.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), crmPoolId);
        crmData.put(CrmV2LeadFieldEnum.RecordType.getNewFieldName(), crmRecordType);
        log.info("xiaoetong createObjectDataToCrmLeadFieldDataMap crmData:{}", crmData);
        return crmData;
    }

    protected HeaderObj createHeaderObj(String ea, Integer userId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);

        if (null == userId) {
            userId = -10000;
        }

        return new HeaderObj(tenantId, userId);
    }

    public void saveLeadsToCrm(XiaoetongUserData data, String ea, String marketingEventId) {
        log.info("XiaoetongManager.saveLeadsToCrm original data 原始数据:{}", data);
        String outerUserId = data.getUserId();
        String phone = data.getPhone();
        String leadId = null;
        Integer createBy = null;
        try {
            data.setEa(ea);
            data.setMarketingEventId(marketingEventId);
            // 组装crm线索对象
            Map<String, Object> params = buildLeadsFieldData(data, ea);
            if (params == null) {
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_707));
                return;
            }
            //设置线索来源渠道promotion_channel
            params.put("promotion_channel", SpreadChannelManager.promotionChannelMap.get("其他"));
            params.put("from_marketing", true);
            // 设置市场活动id
            params.put(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), marketingEventId);

            ObjectData objectData = new ObjectData();
            objectData.putAll(params);
            objectData.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectData.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());

            //重复线索不进入CRM
            Result<CrmV2Manager.LeadDuplicateSearchResult> leadDuplicateSearchResultResult = crmV2Manager.leadDuplicateSearchByObjectV2(ea, objectData);
            log.info("XiaoetongManager -> saveLeadsToCrm 查重返回结果，result:{}", leadDuplicateSearchResultResult);
            if (!leadDuplicateSearchResultResult.isSuccess()) {
                // 查询失败
                log.info("XiaoetongManager -> saveLeadsToCrm 查重校验失败，result:{}", leadDuplicateSearchResultResult);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_727));
                return;
            }
            CrmV2Manager.LeadDuplicateSearchResult duplicateSearchResult = leadDuplicateSearchResultResult.getData();
            if (!duplicateSearchResult.isDuplicate() && StringUtils.isNotBlank(duplicateSearchResult.getLeadId())) {
                log.info("XiaoetongManager.saveLeadsToCrm clue is exist 小鹅通数据转线索重复, data:{}", data);
                leadId = duplicateSearchResult.getLeadId();
                if (StringUtils.isBlank(phone)) {
                    return;
                }
                syncCampaignMember(ea, leadId, marketingEventId, outerUserId, true, phone);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_738));
                return;
            }

            // 获取线索创建人
            createBy = clueDefaultSettingService.getClueCreator(null, ea, ClueDefaultSettingTypeEnum.ONLINE_SERVICE.getType());
            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectData);

            CreateLeadResult createLeadResult = new CreateLeadResult();
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = null;
            log.info("XiaoetongManager saveLeadsToCrm param:{}", actionAddArg);
            result = metadataActionService.add(createHeaderObj(ea, createBy), LeadsFieldContants.API_NAME, false, actionAddArg);
            if (result != null && result.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
                leadId = (String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName());
                log.info("XiaoetongManager.saveLeadsToCrm success data:{} leadId:{}", data, leadId);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, leadId, MarketingLeadSyncRecordObjManager.SUCCESS_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_750));
            } else {
                String message = I18nUtil.getStaticByKey(I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
                if (result != null && StringUtils.isNotBlank(result.getMessage())) {
                    message = result.getMessage();
                }
                createLeadResult.setMessage(message);
                log.info("XiaoetongManager.saveLeadsToCrm failed data:{} message:{}", data, message);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
                return;
            }

            if (org.apache.commons.lang.StringUtils.isNotEmpty(leadId)) {
                //上报神策系统
                marketingStatLogPersistorManger.sendLeadData(ea, leadId, null, MarketingStatLogPersistorManger.CHANNEL_AD_BAIDU_SYNC);
                //生成活动成员
                syncCampaignMember(ea, leadId, marketingEventId, outerUserId, false, phone);
            }
        } catch (Exception e) {
            log.warn("XiaoetongManager saveLeadsToCrm error data:{}, e:{}", data, e);
            String message = StringUtils.isBlank(e.getMessage()) ? I18nUtil.getStaticByKey(I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR)) : e.getMessage();
            tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
            return;
        }
    }

    private void tryCreateOrUpdateMarketingLeadSyncRecordObj(XiaoetongUserData arg, String leadId, String syncStatus, String remark) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg leadSyncRecordObjArg = buildCreateMarketingLeadSyncRecordObjArg(arg, leadId);
        leadSyncRecordObjArg.setSyncStatus(syncStatus);
        leadSyncRecordObjArg.setRemark(remark);
        leadSyncRecordObjArg.setLeadId(leadId);
        leadSyncRecordObjArg.setMarketingLeadSyncRecordObjId(arg.getMarketingLeadSyncRecordObjId());
        marketingLeadSyncRecordObjManager.tryCreateOrUpdateMarketingLeadSyncRecordObj(leadSyncRecordObjArg);
    }

    private CreateOrUpdateMarketingLeadSyncRecordObjArg buildCreateMarketingLeadSyncRecordObjArg(XiaoetongUserData arg, String leadId) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg marketingLeadSyncRecordObjArg = new CreateOrUpdateMarketingLeadSyncRecordObjArg();
        marketingLeadSyncRecordObjArg.setEa(arg.getEa());
        marketingLeadSyncRecordObjArg.setSyncData(JsonUtil.toJson(arg));
        // 不知道咋配置的 还有配置成other的
        marketingLeadSyncRecordObjArg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_XIAOETONGMANAGER_1101));
        marketingLeadSyncRecordObjArg.setOutPlatformType(MarketingLeadSyncRecordObjManager.XIAO_E_TONG_PLATFORM);
        // 这里有的没有配置clueId 反而在data里配置了id
        if (StringUtils.isNotBlank(arg.getUserId())) {
            marketingLeadSyncRecordObjArg.setOutPlatformDataId(arg.getUserId());
        }
        String mobile = arg.getPhone();
        String name = arg.getNickName();
        if (StringUtils.isNotBlank(mobile) && !PhoneNumberCheck.isPhoneLegal(mobile)) {
            mobile = null;
        }
        marketingLeadSyncRecordObjArg.setMobile(mobile);
        marketingLeadSyncRecordObjArg.setLeadName(name);
        marketingLeadSyncRecordObjArg.setLeadId(leadId);
        return marketingLeadSyncRecordObjArg;
    }

    public void doHandleUserMergedMessage(String appId, String sourceUserId, String targetUserId){
        tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
            List<XiaoetongAccountEntity> xiaoetongAccountEntityList = xiaoetongAccountDAO.getByAppId(appId, dbRouteEa);
            if (CollectionUtils.isEmpty(xiaoetongAccountEntityList)){
                return;
            }

            liveUserAccountRelationDAO.updateOuterUserId(xiaoetongAccountEntityList.get(0).getEa(), sourceUserId, targetUserId);
        });
    }

    /**
     * 判断是否是新改版后的小鹅通直播数据
     * @param ei
     * @param marketingEventId
     * @return
     */
    public boolean isXiaoetongUpgradedLive(Integer ei, String marketingEventId){
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
        if (marketingLiveEntity == null) {
            return false;
        }

        // 小鹅通直播，并且是2022.06.30之后的数据才是新改版后的小鹅通直播数据
        Date createTime = marketingLiveEntity.getCreateTime();
        Integer platform = marketingLiveEntity.getPlatform();
        Date endCreateTime = DateUtil.parse(END_SYNC_CREATETIME);
        if (createTime.compareTo(endCreateTime) > 0 && Objects.equals(platform, LivePlatformEnum.XIAOETONG.getType())) {
            return true;
        }
        return false;
    }
}