package com.facishare.marketing.provider.service.distribution;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.marketing.api.result.distribution.DistributorRightsResult;
import com.facishare.marketing.api.result.distribution.ListDistributePlanResult;
import com.facishare.marketing.api.result.distribution.PageDistributePlanResult;
import com.facishare.marketing.api.result.distribution.QueryPlanGradesByPlanIdResult;
import com.facishare.marketing.api.service.distribution.DistributePlanGradeService;
import com.facishare.marketing.api.vo.AddOrUpdateDistributePlanGradeVO;
import com.facishare.marketing.api.vo.DeleteDistributePlanGradeVO;
import com.facishare.marketing.api.vo.ListDistributePlanVO;
import com.facishare.marketing.api.vo.PageDistributePlanVO;
import com.facishare.marketing.api.vo.QueryPlanGradesByPlanIdVO;
import com.facishare.marketing.api.vo.UpdateDistributePlanStatusVO;
import com.facishare.marketing.common.enums.distribution.DistributePlanStatusEnum;
import com.facishare.marketing.common.enums.distribution.DistributorGradeRuleTypeEnum;
import com.facishare.marketing.common.enums.distribution.DistributorGradeTypeEnum;
import com.facishare.marketing.common.enums.distribution.DistributorRightsTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.DistributePlanDao;
import com.facishare.marketing.provider.dao.OperatorDao;
import com.facishare.marketing.provider.dao.distribution.ClueDAO;
import com.facishare.marketing.provider.dao.distribution.DistributePlanGradeDao;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.dao.distribution.DistributorRightsDao;
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity;
import com.facishare.marketing.provider.entity.distribution.ClueCountEntity;
import com.facishare.marketing.provider.entity.distribution.DistributePlanGradeEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorCountEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorRightsEntity;
import com.facishare.marketing.provider.entity.distribution.OperatorCountEntity;
import com.facishare.marketing.provider.manager.AuthManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.distribution.DistributePlanGradeManager;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by ranluch on 2019/4/4.
 */
@Slf4j
@Service("distributePlanGradeService")
public class DistributePlanGradeServiceImpl implements DistributePlanGradeService {
    @Autowired
    private DistributePlanGradeManager planGradeManager;

    @Autowired
    private DistributePlanGradeDao planGradeDao;

    @Autowired
    private DistributePlanDao distributePlanDao;

    @Autowired
    private AuthManager authManager;

    @Autowired
    private OperatorDao operatorDao;

    @Autowired
    private DistributorDao distributorDao;

    @Autowired
    private ClueDAO clueDAO;

    @Autowired
    private DistributorRightsDao distributorRightsDao;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Override
    public Result<Void> addOrUpdateDistributePlanGrade(AddOrUpdateDistributePlanGradeVO vo) {
        if (vo == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        vo.setCondition1(vo.getCondition1() == null ? 0 : vo.getCondition1());
        vo.setCondition2(vo.getCondition2() == null ? 0 : vo.getCondition2());
        vo.setValue1(vo.getValue1() == null ? 0f : vo.getValue1());
        vo.setValue2(vo.getValue2() == null ? 0f : vo.getValue2());

        if (StringUtils.isEmpty(vo.getId())) { // 新建
            int currentMaxGrade;
            DistributePlanGradeEntity planCurrentMaxGrade = planGradeManager.getPlanCurrentMaxGrade(vo.getEa(), vo.getPlanId(), vo.getType());
            if (planCurrentMaxGrade == null) {
                currentMaxGrade = 0;
            } else {
                currentMaxGrade = planCurrentMaxGrade.getGrade();
            }
            if (vo.getGrade() != currentMaxGrade + 1) {
                return Result.newError(SHErrorCode.PLAN_GRADE_IS_ERROR);
            }
            if (currentMaxGrade > 0 && !planCurrentMaxGrade.getRuleType().equals(vo.getRuleType())) {
                return Result.newError(SHErrorCode.PLAN_GRADE_RULE_TYPE_ERROR);
            }

            DistributePlanGradeEntity planGradeEntity = new DistributePlanGradeEntity();
            planGradeEntity.setId(UUIDUtil.getUUID());
            planGradeEntity.setPlanId(vo.getPlanId());
            planGradeEntity.setGrade(vo.getGrade());
            planGradeEntity.setName(vo.getName());
            planGradeEntity.setType(vo.getType());
            planGradeEntity.setRuleType(vo.getRuleType());
            planGradeEntity.setCondition1(vo.getCondition1());
            planGradeEntity.setCondition2(vo.getCondition2());
            if(vo.getGrade() == 1) {
                planGradeEntity.setCondition1(0);
                planGradeEntity.setCondition2(0);
            } else {
                if (vo.getCondition1() <= planCurrentMaxGrade.getCondition1()) { //高等级的必须大于低等级的
                    if (vo.getRuleType().equals(DistributorGradeRuleTypeEnum.ORDER_AMOUNT.getType())) {
                        return Result.newError(SHErrorCode.PLAN_GRADE_MONEY_ERROR);
                    } else if (vo.getRuleType().equals(DistributorGradeRuleTypeEnum.CLUE_AMOUNT.getType())) {
                        return Result.newError(SHErrorCode.PLAN_GRADE_CLUE_NUM_ERROR);
                    } else {
                        return Result.newError(SHErrorCode.PLAN_GRADE_DISTRIBUTOR_NUM_ERROR);
                    }
                }

                if (vo.getCondition2() < planCurrentMaxGrade.getCondition2()) { //高等级的必须不小于低等级的
                    return Result.newError(SHErrorCode.PLAN_GRADE_CLUE_WIN_NUM_ERROR);
                }
            }
            DistributorRightsEntity rightsEntity = new DistributorRightsEntity();
            rightsEntity.setId(UUIDUtil.getUUID());
            rightsEntity.setType(vo.getRightsType());
            rightsEntity.setValue1(vo.getValue1());
            rightsEntity.setValue2(vo.getValue2());
            rightsEntity.setRemark(vo.getRemark());
            rightsEntity.setGrade(planGradeEntity.getGrade());
            rightsEntity.setEa(vo.getEa());

            planGradeEntity.setRightsId(rightsEntity.getId());
            planGradeEntity.setFsUserId(vo.getFsUserId());
            try {
                distributorRightsDao.addDistributorRights(rightsEntity);
                planGradeDao.insertDistributePlanGrade(planGradeEntity);
            } catch (Exception e) {
                log.info("DistributePlanGradeServiceImpl addOrUpdateDistributePlanGrade addDistributorRights or insertDistributePlanGrade exception, vo:{}, exception:{}", vo, e.fillInStackTrace());
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } else { // 编辑
            DistributePlanGradeEntity gradeEntity = planGradeDao.getPlanGradeById(vo.getEa(), vo.getId());
            if (gradeEntity == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            if (gradeEntity.getGrade() > 1 && !gradeEntity.getRuleType().equals(vo.getRuleType())) {
                return Result.newError(SHErrorCode.PLAN_GRADE_RULE_TYPE_ERROR);
            }

            if (gradeEntity.getGrade() == 1 && (vo.getCondition1() != 0 || vo.getCondition2() != 0)) {
                return Result.newError(SHErrorCode.PLAN_GRADE_MONEY_FIRST_ERROR);
            }

            DistributePlanGradeEntity previous = planGradeDao.getPlanGradeByPlanIdAndGrade(vo.getEa(), gradeEntity.getPlanId(), vo.getType(), gradeEntity.getGrade() - 1);
            if (previous != null && (vo.getCondition1() <= previous.getCondition1() || vo.getCondition2() < previous.getCondition2())) {
                return Result.newError(SHErrorCode.PLAN_GRADE_MONEY_PREVIOUS_ERROR);
            }
            DistributePlanGradeEntity next = planGradeDao.getPlanGradeByPlanIdAndGrade(vo.getEa(), gradeEntity.getPlanId(), vo.getType(), gradeEntity.getGrade() + 1);
            if (next != null && (vo.getCondition1() >= next.getCondition1() || vo.getCondition2() > next.getCondition2())) {
                return Result.newError(SHErrorCode.PLAN_GRADE_MONEY_NEXT_ERROR);
            }

            DistributorRightsEntity rightsEntity = new DistributorRightsEntity();
            rightsEntity.setId(UUIDUtil.getUUID());
            rightsEntity.setType(vo.getRightsType());
            rightsEntity.setValue1(vo.getValue1());
            rightsEntity.setValue2(vo.getValue2());
            rightsEntity.setRemark(vo.getRemark());
            rightsEntity.setGrade(gradeEntity.getGrade());
            rightsEntity.setEa(vo.getEa());

            gradeEntity.setRightsId(rightsEntity.getId());

            try {
                distributorRightsDao.addDistributorRights(rightsEntity);
                planGradeDao.updatetDistributePlanGrade(vo.getEa(), gradeEntity.getId(), vo.getName(), vo.getRuleType(), vo.getCondition1(), vo.getCondition2(), gradeEntity.getRightsId());
            } catch (Exception e) {
                log.info("DistributePlanGradeServiceImpl addOrUpdateDistributePlanGrade updatetDistributePlanGrade exception, vo:{}, exception:{}", vo, e.fillInStackTrace());
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QueryPlanGradesByPlanIdResult>> queryPlanGradesByPlanId(QueryPlanGradesByPlanIdVO vo) {
        if (vo == null || StringUtils.isEmpty(vo.getPlanId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (vo.getTime() == null) {
            vo.setTime(System.currentTimeMillis());
        }
        if (vo.getType() == null) {
            vo.setType(DistributorGradeTypeEnum.DISTRIBUTOR_GRADE.getType());
        }
        PageResult<QueryPlanGradesByPlanIdResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTime(vo.getTime());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<DistributePlanGradeEntity> planGradeEntities = planGradeDao.pagePlanGradesByPlanId(vo.getEa(), vo.getPlanId(), vo.getType(), page);
        List<QueryPlanGradesByPlanIdResult> planGradeResults = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(planGradeEntities)) {
            Map<String, DistributorRightsEntity> rightsEntityMap = Maps.newHashMap();
            List<String> rightsIds = planGradeEntities.stream().filter(distributePlanGradeEntity -> StringUtils.isNotEmpty(distributePlanGradeEntity.getRightsId())).map(DistributePlanGradeEntity :: getRightsId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(rightsIds)) {
                List<DistributorRightsEntity> rightsEntities = distributorRightsDao.queryDistributorRightsByIds(vo.getEa(), rightsIds);
                if (CollectionUtils.isNotEmpty(rightsEntities)) {
                    rightsEntityMap = rightsEntities.stream().collect(Collectors.toMap(DistributorRightsEntity :: getId, v -> v, (k1, k2) -> k1));
                }
            }
            for (DistributePlanGradeEntity planGradeEntity : planGradeEntities) {
                QueryPlanGradesByPlanIdResult planGradeResult = BeanUtil.copy(planGradeEntity, QueryPlanGradesByPlanIdResult.class);
                DistributorRightsResult rightsResult;
                if (StringUtils.isNotEmpty(planGradeEntity.getRightsId())) {
                    DistributorRightsEntity rightsEntity = rightsEntityMap.get(planGradeEntity.getRightsId());
                    rightsResult = BeanUtil.copy(rightsEntity, DistributorRightsResult.class);
                } else {
                    rightsResult = new DistributorRightsResult();
                    rightsResult.setType(DistributorRightsTypeEnum.ORDER_AMOUNT_PERCENT.getType());
                    if (vo.getType() == DistributorGradeTypeEnum.DISTRIBUTOR_GRADE.getType()){
                        rightsResult.setValue1(planGradeEntity.getRewardRatio() / 10f);
                    } else {
                        rightsResult.setValue1(planGradeEntity.getDistributionRewardRatio() / 10f);
                    }
                }
                planGradeResult.setRightsResult(rightsResult);
                planGradeResults.add(planGradeResult);
            }
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(planGradeResults);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> deleteDistributePlanGrade(DeleteDistributePlanGradeVO vo) {
        if (vo == null || StringUtils.isEmpty(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return planGradeManager.deleteDistributePlanGrade(vo.getEa(), vo.getId());
    }

    @Override
    public Result<ListDistributePlanResult> listDistributePlan(ListDistributePlanVO vo) {
        if (vo == null || StringUtils.isEmpty(vo.getEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ListDistributePlanResult result = new ListDistributePlanResult();
        List<ListDistributePlanResult.DistributePlan> distributePlanList = Lists.newArrayList();
        result.setDistributePlanList(distributePlanList);

        List<DistributePlanEntity> planEntities = null;
        if (vo.isAppAdmin()) {
            planEntities = distributePlanDao.getDistributePlansByFsEa(vo.getEa(), vo.getTitle());
        } else {
            planEntities = distributePlanDao.getDistributePlansByFsUserId(vo.getEa(), vo.getUserId(), vo.getTitle());
        }

        if (CollectionUtils.isNotEmpty(planEntities)) {
            List<String> planIds = planEntities.stream().map(DistributePlanEntity :: getId).collect(Collectors.toList());
            List<DistributorCountEntity> distributorCountList = distributorDao.getDistributorCount(vo.getEa(), planIds);
            Map<String, DistributorCountEntity> distributorCountMap = Maps.newHashMap();

            if (CollectionUtils.isNotEmpty(distributorCountList)) {
                distributorCountMap = distributorCountList.stream().collect(Collectors.toMap(DistributorCountEntity::getPlanId, a -> a,(k1,k2)->k1));
            }

            List<ClueCountEntity> clueCountList = clueDAO.getClueCount(vo.getEa(), planIds);
            Map<String, ClueCountEntity> clueCountMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(clueCountList)) {
                clueCountMap = clueCountList.stream().collect(Collectors.toMap(ClueCountEntity::getPlanId, a -> a,(k1,k2)->k1));
            }
            for (DistributePlanEntity distributePlanEntity: planEntities) {
                ListDistributePlanResult.DistributePlan distributePlan = new ListDistributePlanResult.DistributePlan();
                distributePlan.setPlanId(distributePlanEntity.getId());
                distributePlan.setPlanTitle(distributePlanEntity.getPlanTitle());
                DistributorCountEntity distributorCountEntity = distributorCountMap.get(distributePlanEntity.getId());
                distributePlan.setDistributorNum(distributorCountEntity != null ? distributorCountEntity.getDistributorCount() : 0);
                ClueCountEntity clueCountEntity = clueCountMap.get(distributePlanEntity.getId());
                distributePlan.setClueNum(clueCountEntity != null ? clueCountEntity.getClueCount() : 0);
                distributePlan.setCreateTime(distributePlanEntity.getCreateTime().getTime());
                distributePlan.setClueRewardStatus(distributePlanEntity.getClueRewardStatus());
                distributePlan.setValidClueReward(distributePlanEntity.getValidClueReward());
                distributePlanList.add(distributePlan);
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<PageDistributePlanResult>> pageDistributePlan(PageDistributePlanVO vo) {
        if (vo.getTime() == null) {
            vo.setTime(System.currentTimeMillis());
        }
        if (StringUtils.isEmpty(vo.getSearchKey())) {
            vo.setSearchKey(null);
        }

        if (vo.getStatus() != null && !vo.getStatus().equals(DistributePlanStatusEnum.OPEN.getStatus()) && !vo.getStatus().equals(DistributePlanStatusEnum.CLOSE.getStatus())) {
            vo.setStatus(null);
        }
        PageResult<PageDistributePlanResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTime(vo.getTime());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);

        List<DistributePlanEntity> planEntities = null;
        if (vo.isAppAdmin()) {
            planEntities = distributePlanDao.pageDistributePlansByFsEa(vo.getEa(), vo.getSearchKey(), vo.getStatus(), page);
        } else {
            planEntities = distributePlanDao.pageDistributePlansByFsUserId(vo.getEa(), vo.getUserId(), vo.getSearchKey(), vo.getStatus(), page);
        }
        if (CollectionUtils.isEmpty(planEntities)) {
            return Result.newSuccess(pageResult);
        }
        List<String> planIds = planEntities.stream().map(DistributePlanEntity :: getId).collect(Collectors.toList());
        List<OperatorCountEntity> operatorCountList = operatorDao.getOperatorCount(planIds,vo.getEa());
        Map<String, OperatorCountEntity> operatorCountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(operatorCountList)) {
            operatorCountMap = operatorCountList.stream().collect(Collectors.toMap(OperatorCountEntity::getPlanId, a -> a,(k1,k2)->k1));
        }

        List<DistributorCountEntity> distributorCountList = distributorDao.getDistributorCount(vo.getEa(), planIds);
        Map<String, DistributorCountEntity> distributorCountMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(distributorCountList)) {
            distributorCountMap = distributorCountList.stream().collect(Collectors.toMap(DistributorCountEntity::getPlanId, a -> a,(k1,k2)->k1));
        }

        List<ClueCountEntity> clueCountList = clueDAO.getClueCount(vo.getEa(), planIds);
        Map<String, ClueCountEntity> clueCountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(clueCountList)) {
            clueCountMap = clueCountList.stream().collect(Collectors.toMap(ClueCountEntity::getPlanId, a -> a,(k1,k2)->k1));
        }

        List<PageDistributePlanResult> dataList = Lists.newArrayList();
        DecimalFormat decimalFormat = new DecimalFormat("#.00");
        for (DistributePlanEntity distributePlanEntity : planEntities) {
            PageDistributePlanResult data = new PageDistributePlanResult();
            data.setPlanId(distributePlanEntity.getId());
            data.setPlanTitle(distributePlanEntity.getPlanTitle());
            String userName = fsAddressBookManager.getEmployeeInfo(distributePlanEntity.getFsEa(), distributePlanEntity.getFsUserId()) == null ? "" : fsAddressBookManager.getEmployeeInfo(distributePlanEntity.getFsEa(), distributePlanEntity.getFsUserId()).getFullName();
            data.setCreator(userName);
            data.setCreateTime(distributePlanEntity.getCreateTime().getTime());
            data.setPlanDesc(distributePlanEntity.getPlanDesc());
            data.setStatus(distributePlanEntity.getStatus());
            data.setCluePoolName(distributePlanEntity.getCrmLeadPoolName());

            OperatorCountEntity operatorCountEntity = operatorCountMap.get(distributePlanEntity.getId());
            if (operatorCountEntity != null) {
                data.setOperatorCount(operatorCountEntity.getOperatorCount());
            }

            DistributorCountEntity distributorCountEntity = distributorCountMap.get(distributePlanEntity.getId());
            if (distributorCountEntity != null) {
                data.setDistributorCount(distributorCountEntity.getDistributorCount());
                if (distributorCountEntity.getTotalReward() >= 0.01d) {
                    data.setTotalReward(decimalFormat.format(distributorCountEntity.getTotalReward()));
                } else {
                    data.setTotalReward("0.00");
                }
            }

            ClueCountEntity clueCountEntity = clueCountMap.get(distributePlanEntity.getId());
            if (clueCountEntity != null) {
                data.setClueCount(clueCountEntity.getClueCount());
                data.setWinClueCount(clueCountEntity.getWinClueCount());
            }

            dataList.add(data);
        }

        pageResult.setResult(dataList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> updateDistributePlanStatus(UpdateDistributePlanStatusVO vo) {
        DistributePlanEntity distributePlanEntity = distributePlanDao.getDistributePlanByPlanId(vo.getEa(), vo.getPlanId());
        if (distributePlanEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        distributePlanDao.updateDistributePlanStatus(vo.getEa(), vo.getPlanId(), vo.getStatus());
        return Result.newSuccess();
    }
}
