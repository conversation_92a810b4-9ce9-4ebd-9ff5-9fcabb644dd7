package com.facishare.marketing.provider.dao;

import com.facishare.marketing.common.typehandlers.value.FlexibleJson;
import com.facishare.marketing.provider.dto.TriggerTaskSnapshotDTO;
import com.facishare.marketing.provider.entity.TriggerTaskInstanceEntity;
import com.facishare.marketing.provider.entity.TriggerTaskSnapshotEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2021-02-25.
 */
public interface TriggerTaskSnapshotDao {

	@Insert("INSERT INTO trigger_task_snapshot(id, ea, trigger_id, trigger_snapshot_id, name, serial_number, task_type, wx_app_id, wx_message_content, wx_template_msg, tag_name_list, sms_template_id, email, execute_type, execute_delay_minutes, board_card_arg, web_hook_id, send_union_message_arg,sop_task_type," +
			"task_offset_day,task_offset_minute,message_content ,create_time, update_time,sop_task_name, notice_type, send_union_message_extend_arg, send_sop_setting, pre_serial_number, property, sms_vars) " +
			" VALUES(#{id}, #{ea}, #{triggerId}, #{triggerSnapshotId}, #{name}, #{serialNumber}, #{taskType}, #{wxAppId}, #{wxMessageContent}, #{wxTemplateMsg}, #{tagNameList}, #{smsTemplateId}, #{email}, #{executeType}, #{executeDelayMinutes}, #{boardCardArg}, #{webHookId}, #{sendUnionMessageArg}," +
			" #{sopTaskType}, #{taskOffsetDay}, #{taskOffsetMinute},#{messageContent}, NOW(), NOW(),#{sopTaskName}, #{noticeType}, #{sendUnionMessageExtendArg}, #{sendSopSetting}, #{preSerialNumber}, #{property}, #{smsVars})")
	int insert(TriggerTaskSnapshotEntity task);
	
	@Select("<script>" +
		"SELECT * FROM trigger_task_snapshot WHERE ea=#{ea} AND trigger_snapshot_id IN " +
		"<foreach collection='triggerSnapshotIds' item='triggerSnapshotId' open='(' close=')' separator=','>#{triggerSnapshotId}</foreach>" +
		"</script>")
	List<TriggerTaskSnapshotEntity> listByTriggerSnapshotIds(@Param("ea") String ea, @Param("triggerSnapshotIds") Collection<String> triggerSnapshotIds);

	@Select("SELECT * FROM trigger_task_snapshot WHERE ea=#{ea} AND trigger_snapshot_id = #{triggerSnapshotId} ORDER BY serial_number")
	List<TriggerTaskSnapshotEntity> listByTriggerSnapshotId(@Param("ea") String ea, @Param("triggerSnapshotId") String triggerSnapshotId);

	@Select("SELECT * FROM trigger_task_snapshot WHERE ea=#{ea} AND trigger_snapshot_id = #{triggerSnapshotId} and serial_number = 0")
	TriggerTaskSnapshotEntity getByTriggerSnapshotId(@Param("ea") String ea, @Param("triggerSnapshotId") String triggerSnapshotId);
	
	@Select("SELECT * FROM trigger_task_snapshot WHERE id=#{id}")
	TriggerTaskSnapshotEntity getById(@Param("ea") String ea, @Param("id") String id);

	@Select("SELECT * FROM trigger_task_snapshot WHERE ea = #{ea} AND trigger_id = #{triggerId} AND task_type = #{taskType} ORDER BY update_time DESC LIMIT 1")
	TriggerTaskSnapshotEntity getByTriggerIdAndTaskType(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("taskType") String taskType);

	@Select("<script>" +
		"SELECT DISTINCT id FROM trigger_task_snapshot WHERE ea = #{ea} AND trigger_id = #{triggerId} AND task_type IN " +
		"<foreach collection='taskTypes' item='taskType' open='(' close=')' separator=','>#{taskType}</foreach>" +
		"</script>")
	List<String> getIdsByTriggerIdAndTaskTypes(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("taskTypes") Collection<String> taskTypes);

	@Insert("INSERT INTO trigger_task_snapshot (\"id\", ea, trigger_id, trigger_snapshot_id, \"name\", serial_number, task_type, wx_app_id, wx_message_content, wx_template_msg, sms_template_id, email, "
		  + "tag_name_list, execute_type, execute_delay_minutes, create_time, update_time, board_card_arg, web_hook_id, send_union_message_arg,sop_task_type,task_offset_day,task_offset_minute,message_content, " +
			"notice_type, send_union_message_extend_arg, send_sop_setting, pre_serial_number, property) "
		  + "SELECT #{newId}, ea, #{newTriggerId}, #{newTriggerSnapshotId}, \"name\", serial_number, task_type, wx_app_id, wx_message_content, wx_template_msg, sms_template_id, email, tag_name_list, "
		  + "execute_type, execute_delay_minutes, NOW(), NOW(), board_card_arg, web_hook_id, send_union_message_arg ,sop_task_type,task_offset_day,task_offset_minute,message_content, " +
			"notice_type, send_union_message_extend_arg, send_sop_setting, pre_serial_number, property "
		  + "FROM trigger_task_snapshot WHERE \"id\" = #{oldId}")
	void copy(@Param("ea") String ea, @Param("oldId") String oldId, @Param("newId") String newId, @Param("newTriggerId") String newTriggerId, @Param("newTriggerSnapshotId") String newTriggerSnapshotId);

	@Select("<script>"
		+	"SELECT trigger_id, string_agg(DISTINCT task_type,',') AS task_types FROM trigger_task_snapshot WHERE ea = #{ea} AND trigger_snapshot_id IN "
		+   "<foreach collection='triggerSnapshotIds' item='triggerSnapshotId' open='(' close=')' separator=','> #{triggerSnapshotId}</foreach>"
		+   " GROUP BY trigger_id"
		+	"</script>")
	List<TriggerTaskSnapshotDTO> getTaskTypes(@Param("ea") String ea, @Param("triggerSnapshotIds") Collection<String> triggerSnapshotIds);


	@Select("<script>" +
			"SELECT * FROM trigger_task_snapshot WHERE ea = #{ea} AND trigger_id = #{triggerId} AND id IN " +
			"<foreach collection='ids' item='id' open='(' close=')' separator=','>#{id}</foreach>" +
			"</script>")
	List<TriggerTaskSnapshotDTO> getByIds(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("ids") Collection<String> idList);

	@Select("<script>" +
			"SELECT * FROM trigger_task_snapshot WHERE ea = #{ea} AND id IN " +
			"<foreach collection='ids' item='id' open='(' close=')' separator=','>#{id}</foreach>" +
			"</script>")
	List<TriggerTaskSnapshotEntity> getBySnapshotIds(@Param("ea") String ea, @Param("ids") Collection<String> idList);

	@Select("<script>" +
			"select * \n" +
			"from trigger_task_snapshot \n" +
			"where \n" +
			"ea = #{ea} \n" +
			"and email ->> 'title' = #{title} \n" +
			"and trigger_id = #{triggerId} \n" +
			"order by create_time desc \n" +
			"limit 1" +
			"</script>")
	TriggerTaskSnapshotEntity getByMailTitleAndTriggerId(@Param("ea") String ea, @Param("title") String title, @Param("triggerId") String triggerId);

	@Select("<script>" +
			"select a.* \n" +
			"from trigger_task_snapshot a\n" +
			"left join scene_trigger b on a.trigger_id = b.trigger_id and a.ea = b.ea \n" +
			"where\n" +
			"a.ea = #{ea}\n" +
			"and a.email ->> 'title' = #{title}\n" +
			"and b.scene_target_id = #{parentMarketingEventId}\n" +
			"order by create_time desc limit 1" +
			"</script>")
	TriggerTaskSnapshotEntity getByMailTitleAndParentMarketingEventId(@Param("ea") String ea, @Param("title") String title, @Param("parentMarketingEventId") String parentMarketingEventId);

	// 该查询仅适用于非判断分支节点
	@Select("select\n" +
			"\t*\n" +
			"from\n" +
			"\ttrigger_task_snapshot\n" +
			"where\n" +
			"\tea = #{ea}\n" +
			"\tand trigger_id = #{triggerId}\n" +
			"\tand trigger_snapshot_id = #{triggerSnapshotId}\n" +
			"\tand case when pre_serial_number is null then serial_number = #{serialNumber}+1 else pre_serial_number = #{serialNumber} end")
	TriggerTaskSnapshotEntity findNextNode(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerSnapshotId") String triggerSnapshotId, @Param("serialNumber") Integer serialNumber);

	@Select("select * from trigger_task_snapshot where ea = #{ea} and trigger_id = #{triggerId} and trigger_snapshot_id = #{triggerSnapshotId} and serial_number = #{serialNumber}")
    TriggerTaskSnapshotEntity getNextTriggerTaskSnapshot(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerSnapshotId") String triggerSnapshotId, @Param("serialNumber") Integer serialNumber);
}
