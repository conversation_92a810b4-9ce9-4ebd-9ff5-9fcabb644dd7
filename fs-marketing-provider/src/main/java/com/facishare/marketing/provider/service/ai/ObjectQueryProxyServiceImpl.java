package com.facishare.marketing.provider.service.ai;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.ai.ObjAbstractLayoutResult;
import com.facishare.marketing.api.service.ai.ObjectQueryProxyService;
import com.facishare.marketing.api.vo.ai.QueryObjDataVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.FieldDescribeService;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.result.CrmListPage;
import com.facishare.privilege.api.UserPrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.user.BatchGetMajorRoleCodeByUserIdsVo;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.FindAssignedLayoutByLayoutTypeArg;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.arg.FindLayoutArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.result.AssignedLayoutData;
import com.fxiaoke.crmrestapi.result.FindLayoutResult;
import com.fxiaoke.crmrestapi.service.ObjectLayoutService;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.contstant.OperatorConstants.EQ;

@Slf4j
@Service("objectQueryProxyService")
public class ObjectQueryProxyServiceImpl implements ObjectQueryProxyService {

    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private FieldDescribeService fieldDescribeService;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private EIEAConverter converter;
    @Autowired
    private ObjectLayoutService objectLayoutService;
    @Autowired
    private UserPrivilegeRestService userPrivilegeRestService;

    @Override
    public Result<PageResult> pageQueryObjDatas(String ea, Integer fsUserId, QueryObjDataVO vo) {
        if (StringUtils.isEmpty(vo.getObjectAPIName()) || StringUtils.isBlank(vo.getQuery())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult pageResult = new PageResult();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        PaasQueryArg arg = null;
        try {
            arg = GsonUtil.fromJson(vo.getQuery(), PaasQueryArg.class);
        } catch (Exception e) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setOffset((vo.getPageNum() - 1) * vo.getPageSize());
        arg.setLimit(vo.getPageSize());
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setSearchQueryInfoJson(GsonUtil.toJson(arg));
        findByQueryV3Arg.setSelectFields(vo.getSelectFields());
        findByQueryV3Arg.setDescribeApiName(vo.getObjectAPIName());
        InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
        if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            pageResult.setResult(objectDataInnerPage.getDataList());
            pageResult.setTotalCount(objectDataInnerPage.getTotalCount());
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result queryObjDataById(String ea, Integer fsUserId, QueryObjDataVO vo) {
        if (StringUtils.isEmpty(vo.getObjectAPIName()) || StringUtils.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ObjectData obj = crmMetadataManager.findByIdV3(ea, fsUserId, vo.getObjectAPIName(), vo.getSelectFields(), vo.getId());
        if (obj == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess(obj);
    }

    @Override
    public Result queryObjFiledInfo(String ea, Integer fsUserId, QueryObjDataVO vo) {
        if (StringUtils.isEmpty(vo.getObjectAPIName()) || StringUtils.isEmpty(vo.getObjectFiledAPIName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<CrmUserDefineFieldVo> allObjectFieldDescribesList = crmV2Manager.getAllObjectFieldDescribesList(ea, vo.getObjectAPIName());
        Map<String, CrmUserDefineFieldVo> allObjectFieldDescribesMap = allObjectFieldDescribesList.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, Function.identity()));
        return Result.newSuccess(allObjectFieldDescribesMap.get(vo.getObjectFiledAPIName()));
    }

    @Override
    @FilterLog
    public Result queryObjAbstractLayoutAndData(String ea, Integer fsUserId, QueryObjDataVO vo) {
        ObjAbstractLayoutResult result = new ObjAbstractLayoutResult();
        String objectAPIName = vo.getObjectAPIName();
        String id = vo.getId();
        if (StringUtils.isBlank(objectAPIName) || StringUtils.isBlank(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (QywxUserConstants.isVirtualUserId(fsUserId)) {
            fsUserId = SuperUserConstants.USER_ID;
        }
        int tenantId = converter.enterpriseAccountToId(ea);
        HeaderObj systemHeader = new HeaderObj(tenantId, fsUserId);
        systemHeader.put("x-fs-locale", I18nUtil.getLanguage());
        // 查询 roleCode
        BatchGetMajorRoleCodeByUserIdsVo.Argument queryMajarRoleArgument = new BatchGetMajorRoleCodeByUserIdsVo.Argument();
        queryMajarRoleArgument.setUserIds(Lists.newArrayList(String.valueOf(fsUserId)));
        PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();
        Map<String, String> majorRoleMap = userPrivilegeRestService.batchGetMajorRoleCodeByUserIds(privilegeContext, queryMajarRoleArgument);
        String userRoleCode = majorRoleMap != null ? majorRoleMap.get(String.valueOf(fsUserId)) : null;
        String layoutApiName = null;
        // 查询 layout_api_names
        // 移动端摘要布局
        layoutApiName = findAssignedLayoutByLayoutType(objectAPIName, systemHeader, userRoleCode, "list");
        // 移动端列表布局
        if (StringUtils.isNotBlank(layoutApiName)) {
            // 查询布局详情
            if (StringUtils.isNotEmpty(layoutApiName)) {
                FindLayoutArg findLayoutArg = new FindLayoutArg();
                findLayoutArg.setObjectDescribeApiName(objectAPIName);
                findLayoutArg.setApiName(layoutApiName);
                InnerResult<FindLayoutResult> serviceLayout = objectLayoutService.findLayout(systemHeader, findLayoutArg);
                if (serviceLayout != null && serviceLayout.getResult() != null) {
                    result.setLayout(serviceLayout.getResult().getLayout());
                    result.setObjectDescribe(serviceLayout.getResult().getObjectDescribe());
                }
            }
            // 查询对象数据
            result.setObjectData(crmV2Manager.getObjectData(ea, fsUserId, objectAPIName, id));
        } else {
            ControllerListArg arg = new ControllerListArg();
            List<PaasQueryArg.Condition> conditionList = Lists.newArrayList();
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("_id", Lists.newArrayList(id), EQ);
            conditionList.add(condition);
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.setFilters(conditionList);
            arg.setSearchQueryInfo(GsonUtil.toJson(paasQueryArg));
            arg.setIncludeLayout(true);
            com.fxiaoke.crmrestapi.common.result.Result<CrmListPage<ObjectData>> crmListPageResult = fieldDescribeService.list(systemHeader, objectAPIName, arg);
            if (crmListPageResult != null && crmListPageResult.isSuccess() && crmListPageResult.getData() != null) {
                result.setLayout(crmListPageResult.getData().getLayout());
                result.setObjectDescribe(crmListPageResult.getData().getObjectDescribe());
                if (CollectionUtils.isNotEmpty(crmListPageResult.getData().getDataList())) {
                    result.setObjectData(crmListPageResult.getData().getDataList().get(0));
                }
            }
        }
        return Result.newSuccess(result);
    }

    private String findAssignedLayoutByLayoutType(String objectAPIName, HeaderObj systemHeader, String userRoleCode, String layoutType) {
        FindAssignedLayoutByLayoutTypeArg findAssignedLayoutByLayoutTypeArg = new FindAssignedLayoutByLayoutTypeArg();
        findAssignedLayoutByLayoutTypeArg.setLayoutType(layoutType);
        findAssignedLayoutByLayoutTypeArg.setDescribeApiName(objectAPIName);
        com.fxiaoke.crmrestapi.common.result.Result<AssignedLayoutData> assignedLayoutDataResult = objectLayoutService.findAssignedLayoutByLayoutType(systemHeader, findAssignedLayoutByLayoutTypeArg);
        if (assignedLayoutDataResult != null && assignedLayoutDataResult.isSuccess() && assignedLayoutDataResult.getData() != null
                && CollectionUtils.isNotEmpty(assignedLayoutDataResult.getData().getRole_list())) {
            for (AssignedLayoutData.RoleList roleList : assignedLayoutDataResult.getData().getRole_list()) {
                if (StringUtils.isNotBlank(userRoleCode)) {
                    if (StringUtils.equals(userRoleCode, roleList.getRoleCode())) {
                        List<AssignedLayoutData.RoleList.RecordLayout> recordLayout = roleList.getRecord_layout();
                        if (CollectionUtils.isNotEmpty(recordLayout)) {
                            return recordLayout.get(0).getLayout_api_name();
                        }
                    }
                } else {
                    for (AssignedLayoutData.LayoutList layoutList : assignedLayoutDataResult.getData().getLayout_list()) {
                        if (layoutList.getIs_default()) {
                            return layoutList.getApi_name();
                        }
                    }
                }
            }
        }
        return null;
    }

}
