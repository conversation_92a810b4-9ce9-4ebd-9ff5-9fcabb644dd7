package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class TaggingEntity extends BaseEaEntity implements Serializable  {
    private String id;
    private String tagId;
    private int targetType;
    private String targetId;
    private String createBy;
    private Date createTime;
}
