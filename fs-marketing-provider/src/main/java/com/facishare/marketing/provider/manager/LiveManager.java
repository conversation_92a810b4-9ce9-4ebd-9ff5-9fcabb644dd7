package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.CreateObjectDataModel;
import com.facishare.marketing.api.result.live.CreateMarketingLiveResult;
import com.facishare.marketing.api.result.live.LiveBriefStatisticsResult;
import com.facishare.marketing.api.result.live.QueryLiveEnrollListResult;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.vo.live.CreateLiveVO;
import com.facishare.marketing.api.vo.live.QueryLiveEnrollListVO;
import com.facishare.marketing.common.contstant.CampaignMembersConstants;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.live.LiveUserActionTypeEnum;
import com.facishare.marketing.common.model.SmsParamObject;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService;
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.*;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.ChannelsAccountEntity;
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics;
import com.facishare.marketing.provider.entity.live.MarketingLiveViewLoginEntity;
import com.facishare.marketing.provider.innerData.live.mudu.MuduApiGetRoomListResult;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.sms.mw.SmsParamManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.util.ContextUtil;
import com.facishare.training.common.result.Result;
import com.facishare.training.outer.api.arg.LiveDefaultRecordArg;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.facishare.training.outer.api.service.live.LiveCommonService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.DbRouteFillEaDataManager.defaultEa;

/**
 * Created by zhengh on 2020/4/14.
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class LiveManager {
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private SmsParamManager smsParamManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private LiveCommonService liveCommonService;

    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;

    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private ActionManager actionManager;

    @Autowired
    private RedisManager redisManager;
    
    @Autowired
    private SceneTriggerManager sceneTriggerManager;

    @Autowired
    private XiaoetongManager xiaoetongManager;

    @Autowired
    private MuduManager muduManager;

    @Autowired
    private VHallManager vHallManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    @Autowired
    private LiveUserAccountRelationDAO liveUserAccountRelationDAO;

    @Autowired
    private CampaignMergeDataResetManager campaignMergeDataResetManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MarketingLiveViewLoginDAO marketingLiveViewLoginDAO;

    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @Autowired
    private CrmLeadMarketingAccountAssociationService crmLeadMarketingAccountAssociationService;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private PolyvManager polyvManager;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private EIEAConverter converter;

    @Autowired
    private LiveService liveService;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    private String addLiveLockPre = "marketingLive_";
    private final String liveUpdateKey = "marketing_vhall_liveId_";
    private final String muduLiveUpdateKey = "marketing_mudu_liveId_";
    private int PAGE_SIZE = 100;
    private int HISOTRY_LIST_MAX_COUNT = 1000;   //查询历史数据，每次查询最大数量
    private int LIVE_VIEW = 1;    //观看直播
    private int RECORDE_VIEW = 2; //观看回放
    private Gson gson = new Gson();
    @Autowired
    private ChannelsAccountDAO channelsAccountDAO;

    public Optional<MarketingLiveEntity> getMarketingLiveEntityByMarketingEventId(String ea, String marketingEventId) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(marketingEventId)) {
            log.warn("LiveManager.getMarketingLiveEntityByMarketingEventId ea:{} marketingEventId:{}", ea, marketingEventId);
            return Optional.empty();
        }
        int corpId = eieaConverter.enterpriseAccountToId(ea);
        return Optional.ofNullable(marketingLiveDAO.getByCorpIdAndMarketingEventId(corpId, marketingEventId));
    }

    public List<PhoneContentResult> buildLivePhoneContentList(List<String> campaignMergeDataIds, boolean isExistsVars){
        List<PhoneContentResult> phoneContentResults = Lists.newArrayList();
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignMergeDataIds);

        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)){
            return phoneContentResults;
        }

        String marketingEventId = campaignMergeDataEntityList.get(0).getMarketingEventId();
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(campaignMergeDataEntityList.get(0).getEa()), marketingEventId);
        for (CampaignMergeDataEntity entity : campaignMergeDataEntityList) {
            if (StringUtils.isBlank(entity.getPhone())) {
                continue;
            }
            if (isExistsVars) {
                Map<String, List<SmsParamObject>> paramObjectMap = Maps.newHashMap();
                paramObjectMap.put(entity.getPhone(), Lists.newArrayList(marketingLiveEntity, entity));
                List<PhoneContentResult> tmpPhoneContentResults = smsParamManager.buildParamValueMap(paramObjectMap);
                // 添加活动成员参数
                String campaignMembersObjId = entity.getCampaignMembersObjId();
                Map<String, String> detail = crmV2Manager.getObjectDataEnTextVal(entity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMembersObjId);

                if (detail != null) {
                    HashMap<String, String> stringVHashMap = new HashMap<>();
                    detail.forEach((k,v)->{
                        if (v != null) {
                            stringVHashMap.put(k, String.valueOf(v));
                        } else {
                            stringVHashMap.put(k, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                        }
                    });
                    tmpPhoneContentResults.get(0).getParamMap().putAll(stringVHashMap);
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Map<String, String> specialParams = new HashMap<>();
                specialParams.put("activity.name", marketingLiveEntity.getTitle());
                specialParams.put("activity.startTime", sdf.format(marketingLiveEntity.getStartTime()));
                specialParams.put("activity.endTime", sdf.format(marketingLiveEntity.getEndTime()));
                tmpPhoneContentResults.get(0).getParamMap().putAll(specialParams);

                phoneContentResults.addAll(tmpPhoneContentResults);
            } else {
                // 不存在变量，只组装手机号即可
                phoneContentResults.add(new PhoneContentResult(entity.getPhone(), Maps.newHashMap()));
            }
        }

        return phoneContentResults;
    }

    public void syncVhallLiveStatisticsData(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);   //只更新一个月内的直播状态
        Date limitDate = c.getTime();
        int totalCount = marketingLiveStatisticsDAO.getSyncVhallLiveIdCount(limitDate);
        if (totalCount == 0){
            return;
        }

        int totalPageCount = totalCount / PAGE_SIZE;
        if (totalCount % PAGE_SIZE != 0) {
            totalPageCount++;
        }
        log.info("syncLiveStatisticsData total:{}  page:{}", totalCount, totalPageCount);
        for (int i = 0; i < totalPageCount; i++){
            Page page = new Page(i + 1, PAGE_SIZE);
            List<Integer> liveStatisticses =  marketingLiveStatisticsDAO.getPageSyncVhallLiveIds(limitDate, page);
            if (CollectionUtils.isEmpty(liveStatisticses)) {
                continue;
            }

            for (Integer liveId : liveStatisticses) {
                syncVhallLiveTryLock(liveId);
            }
        }
    }

    //设置回放
    public void scheduleSetDefaultRecord(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.HOUR, -6);
        Date limitDate = c.getTime();
        List<MarketingLiveEntity> marketingLiveEntityList = marketingLiveDAO.getForDefaultRecordLiveIds(limitDate);
        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return;
        }
        log.info("marketingLiveEntityList:{}", marketingLiveEntityList);

        marketingLiveEntityList.forEach(marketingLiveEntity ->{
            try {
                //过滤掉停用企业、营销通配额过期企业
                String ea = eieaConverter.enterpriseIdToAccount(marketingLiveEntity.getCorpId());
                if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
                    log.info("LiveManager.scheduleSetDefaultRecord failed enterprise stop or license expire ea:{}", ea);
                    return;
                }

                if (marketingLiveEntity.getAutoRecord() == 1 && marketingLiveEntity.getStatus() == LiveStatusEnum.FINISH.getStatus() && marketingLiveEntity.getRecordId() == null) {
                    LiveDefaultRecordArg liveDefaultRecordArg = new LiveDefaultRecordArg();
                    liveDefaultRecordArg.setLiveId(marketingLiveEntity.getLiveId());
                    liveDefaultRecordArg.setTitle(marketingLiveEntity.getTitle());
                    com.facishare.training.common.result.Result<Integer> recordResult = liveCommonService.setDefaultRecord(liveDefaultRecordArg);
                    if (recordResult == null || !recordResult.isSuccess() || recordResult.getData() == null) {
                        log.info("scheduleSetDefaultRecord set live record failed result:{} arg:{}", recordResult, liveDefaultRecordArg);
                    } else {
                        marketingLiveDAO.updateLiveRecord(marketingLiveEntity.getLiveId(), recordResult.getData());
                        Result<Integer> liveStatusResult = liveCommonService.getLiveStatus(eieaConverter.enterpriseIdToAccount(marketingLiveEntity.getCorpId()), marketingLiveEntity.getLiveId());
                        if (liveStatusResult.isSuccess() && liveStatusResult.getData() != null) {
                            marketingLiveStatisticsDAO.updateStatus(marketingLiveEntity.getLiveId(), liveStatusResult.getData());
                            marketingLiveDAO.updateStatusByLiveId(marketingLiveEntity.getLiveId(), liveStatusResult.getData());
                        }
                    }
                }
            }catch(Exception e){
                log.error("scheduleSetDefaultRecord exception e:", e);
            }
        });
    }

    public void syncVhallLiveTryLock(Integer liveId){
        String key = liveUpdateKey + String.valueOf(liveId);
        try {
            boolean redisLock = redisManager.lock(key, 60);
            if (!redisLock) {
                return;
            }
            //syncLiveById(liveId);
        } catch (Exception e) {
            log.warn("LiveManager.sncyLiveTryLock sncyLiveTryLock statistics:{} error e:", liveId, e);
            return;
        } finally {
            redisManager.unLock(key);
        }
    }

    public void syncXiaoetongLiveTryLock(String ea, String marketingEventId, String xiaoetongLiveId){
        String key = liveUpdateKey + String.valueOf(xiaoetongLiveId);
        try {
            boolean redisLock = redisManager.lock(key, 60);
            if (!redisLock) {
                return;
            }
            syncXiaoetongLiveDataById(ea, marketingEventId, xiaoetongLiveId);
        } catch (Exception e) {
            log.warn("LiveManager.sncyXiaoetongLiveTryLock sncyLiveTryLock statistics:{} error e:", xiaoetongLiveId, e);
            return;
        } finally {
            redisManager.unLock(key);
        }
    }

    public void syncPolyvLiveTryLock(String ea, String marketingEventId, String channelId){
        String key = liveUpdateKey + channelId;
        try {
            boolean redisLock = redisManager.lock(key, 60);
            if (!redisLock) {
                return;
            }
            polyvManager.syncLiveDataById(ea, marketingEventId, channelId);
        } catch (Exception e) {
            log.warn("LiveManager.sncyPolyvLiveTryLock sncyLiveTryLock statistics:{} error e:", channelId, e);
            return;
        } finally {
            redisManager.unLock(key);
        }
    }

    public void syncXiaoetongLiveStatisticsData(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);   //只更新一个月内的直播状态
        Date limitDate = c.getTime();
        List<MarketingLiveEntity> marketingLiveEntityList = marketingLiveDAO.getSyncXiaoetongLive(limitDate);
        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return;
        }

        //拉取小鹅通直播数据
        for (MarketingLiveEntity entity : marketingLiveEntityList){
            String ea = eieaConverter.enterpriseIdToAccount(entity.getCorpId());
            if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
                log.info("LiveManager.syncXiaoetongLiveStatisticsData failed enterprise stop or license expire ea:{}", ea);
                continue;
            }
            syncXiaoetongLiveTryLock(ea, entity.getMarketingEventId(), entity.getXiaoetongLiveId());
            xiaoetongManager.syncXiaoetongLiveStatusByEa(ea, entity.getXiaoetongLiveId());
            this.syncLiveDataToCrm(entity);
        }
    }

    /**
     * 同步小鹅通直播数据
     * @param xiaoetongLiveId
     */
    public void syncXiaoetongLiveDataById(String ea, String marketingEventId, String xiaoetongLiveId){
       xiaoetongManager.syncXiaoetongLiveDataByIdNew(ea, marketingEventId, xiaoetongLiveId);
    }

    public void upsertUserStatus(Integer liveId, List<String> phones, LiveUserActionTypeEnum actionTypeEnum) {
        if (liveId == null || CollectionUtils.isEmpty(phones) || actionTypeEnum == null) {
            return;
        }
        List<LiveUserStatusEntity> existingData = liveUserStatusDAO.queryLiveUserStatusByLiveIdAndPhone(liveId, phones);
        List<String> existingUser = Lists.newArrayList();
        List<String> notExistingData;
        if (CollectionUtils.isNotEmpty(existingData)) {
            existingUser = existingData.stream().map(LiveUserStatusEntity::getPhone).collect(Collectors.toList());
            List<String> finalExistingUser = existingUser;
            notExistingData = phones.stream().filter(data -> !finalExistingUser.contains(data)).collect(Collectors.toList());
        } else {
            notExistingData = phones;
        }
        // 新增
        if (CollectionUtils.isNotEmpty(notExistingData)) {
            for (String phone : notExistingData) {
                try {
                    LiveUserStatusEntity liveUserStatusEntity = new LiveUserStatusEntity();
                    liveUserStatusEntity.setId(UUIDUtil.getUUID());
                    liveUserStatusEntity.setLiveId(liveId);
                    liveUserStatusEntity.setPhone(phone);
                    liveUserStatusEntity.initStatus(Lists.newArrayList(actionTypeEnum));
                    liveUserStatusDAO.insertLiveUserStatus(liveUserStatusEntity);
                } catch (Exception e) {
                    log.warn("LiveManager.upsertUserStatus error e:{}", e);
                }
            }
        }
        // 更新
        if (CollectionUtils.isNotEmpty(existingUser)) {
            // 分批更新防止sql过长
            if (existingUser.size() > 50) {
                PageUtil<String> phonePage = new PageUtil<>(existingUser, 50);
                for (int i = 1; i <= phonePage.getPageCount(); i++) {
                    liveUserStatusDAO.updateLiveUserStatusByLiveIdAndPhone(liveId, phonePage.getPagedList(i), actionTypeEnum.getType());
                }
            } else {
                liveUserStatusDAO.updateLiveUserStatusByLiveIdAndPhone(liveId, existingUser, actionTypeEnum.getType());
            }
        }
    }

    /**
     * 根据直播id与动作返回直播数据
     * @param liveId
     * @param actionTypeEnum
     * @return
     */
    public List<String> queryLiveUserByStatus(Integer liveId, LiveUserActionTypeEnum actionTypeEnum) {
        List<String> phones = Lists.newArrayList();
        if (liveId == null || actionTypeEnum == null) {
            return phones;
        }
        List<LiveUserStatusEntity> liveUserStatusEntityList = liveUserStatusDAO.queryLiveUserPerformedStatusByType(liveId, actionTypeEnum.getType());
        if (CollectionUtils.isNotEmpty(liveUserStatusEntityList)) {
            phones = liveUserStatusEntityList.stream().map(LiveUserStatusEntity::getPhone).collect(Collectors.toList());
        }
        return phones;
    }

    public void syncLiveFromCrmMarketingEvent(String ea, Integer fsUserId, String marketingEventId, ObjectData objectData, String op){
        if (StringUtils.equals("i", op)){
            addLiveFromCrm(ea, fsUserId, marketingEventId, objectData);
        }else if (StringUtils.equals("u", op)){
            updateLiveFromCrm(ea, fsUserId, marketingEventId, objectData);
        }else if (StringUtils.equals("d", op) || StringUtils.equals("invalid", op)){
            invalidLiveFromCrm(ea, fsUserId, marketingEventId);
        } else if (StringUtils.equals("recover", op)){
            reoverLiveFromCrm(ea, fsUserId, marketingEventId);
        } else {
            log.info("syncConferenceDataFromCrm ignore op ea:{} marketingEventId:{} op:{}", ea, marketingEventId, op);
        }

    }

    private void reoverLiveFromCrm(String ea, Integer fsUserId, String marketingEventId){
        Integer corpId = eieaConverter.enterpriseAccountToId(ea);
        MarketingLiveEntity entity = marketingLiveDAO.getByCorpIdAndMarketingEventId(corpId, marketingEventId);
        if (entity == null){
            log.info("reoverLiveFromCrm  failed liveEntity is not exist ea:{} fsUserId:{} marketingEventId:{}", ea, fsUserId, marketingEventId);
            return;
        }

        if (entity.getStatus() == LiveStatusEnum.DELETE.getStatus()) {
            int status = LiveStatusEnum.FINISH.getStatus();
            if (entity.getPlatform() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
                try {
                    Result<Integer> liveStatusResult = liveCommonService.getLiveStatus(ea, entity.getLiveId());
                    status = liveStatusResult.getData();
                }catch (Exception e){
                    log.info("reoverLiveFromCrm sync vhall live failed ea:{} liveId:{}");
                }
            } else {
                if (entity.getStartTime().getTime() > System.currentTimeMillis()){
                    status = LiveStatusEnum.NOT_START.getStatus();
                }else if (entity.getEndTime().getTime() < System.currentTimeMillis()){
                    status = LiveStatusEnum.FINISH.getStatus();
                }else {
                    status = LiveStatusEnum.PROCESSING.getStatus();
                }
            }
            marketingLiveDAO.updateStatusById(entity.getId(), status);
            integralServiceManager.asyncRegisterMaterial(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), CategoryApiNameConstant.LIVE, entity.getId(), entity.getTitle());
        }
    }

    private void addLiveFromCrm(String ea, Integer fsUserId, String marketingEventId, ObjectData objectData){
        Integer corpId = eieaConverter.enterpriseAccountToId(ea);
        if (marketingLiveDAO.getByCorpIdAndMarketingEventId(corpId, marketingEventId) != null){
            log.info("addLiveFromCrm failed liveEntity is exist ea:{} fsUserId:{} marketingEventId:{} objectData:{}", ea, fsUserId, marketingEventId, objectData);
            return;
        }

        MarketingLiveEntity entity = new MarketingLiveEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setCorpId(eieaConverter.enterpriseAccountToId(ea));
        entity.setMarketingEventId(marketingEventId);
        entity.setTitle(objectData.getName());
        entity.setCreateUserId(fsUserId);
        entity.setStatus(LiveStatusEnum.NOT_START.getStatus());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        Long beginTime = objectData.getLong(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName());
        entity.setStartTime(beginTime == null ? DateUtil.now() : DateUtil.fromTimestamp(beginTime));
        Long endTime = objectData.getLong(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName());
        entity.setEndTime(endTime == null ? DateUtil.now() : DateUtil.fromTimestamp(endTime));
        entity.setShowAcitivityList(false);
        addLiveByLock(entity);
    }

    public String addLiveByLock(MarketingLiveEntity entity){
        String key = addLiveLockPre + entity.getMarketingEventId();
        try {
            // 尝试获取分布式锁 过期时间为100s
            int retry = 0;
            while(retry++ < 30) {
                boolean redisLock = redisManager.lock(key, 10);
                if (!redisLock) {
                    // 未获取到锁执行等待
                    Thread.sleep(100);
                } else {
                    MarketingLiveEntity existEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(entity.getCorpId(), entity.getMarketingEventId());
                    if (existEntity == null){
                        int result = marketingLiveDAO.addMarketingLive(entity);
                        if (result > 0) {
                            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), entity.getCreateUserId(), ObjectTypeEnum.LIVE.getType(), null, entity.getId()));
                            return entity.getId();
                        }
                        return null;
                    }
                    return existEntity.getId();
                }
            }
        } catch (Exception e) {
            log.warn("LiveManager.addLiveByLock error e:{}", e);
            return null;
        } finally {
            redisManager.unLock(key);
        }

        return null;
    }

    public void updateLiveFromCrm(String ea, Integer fsUserId, String marketingEventId, ObjectData objectData){
        String title = objectData.getName();
        Long startTime = objectData.getLong(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName());
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
        if (marketingLiveEntity == null){
            log.info("updateLiveFromCrm marketingLiveEntity is not exist ea:{} marketingEventId:{}", ea, marketingEventId);
            addLiveFromCrm(ea, fsUserId, marketingEventId, objectData);
            return;
        }

        if (marketingLiveEntity.getPlatform() == null || marketingLiveEntity.getPlatform() == LivePlatformEnum.OTHER_PLATFORM.getType()){
            if (!StringUtils.equals(title, marketingLiveEntity.getTitle())){
                marketingLiveDAO.updateTitleById(marketingLiveEntity.getId(), eieaConverter.enterpriseAccountToId(ea), title);
            }
            //同步开始时间和结束时间，由于市场活动中开始时间和结束时间是日期字段，营销通中的开始日期和结束时间是日期时间字段，所以需要判断是否在同一天。防止同一天的更改无法同步到营销通。
            boolean sameDay = marketingEventManager.isSameDay(marketingLiveEntity.getStartTime(), new Date(startTime));
            if (startTime != null && !sameDay){
                marketingLiveDAO.updateStartTimeById(marketingLiveEntity.getId(), marketingLiveEntity.getCorpId(), new Date(startTime));
                sceneTriggerManager.handleLiveStartTimeChange(ea, marketingLiveEntity.getId(), marketingLiveEntity.getStartTime(), new Date(startTime));
            }
        }
    }

    private void invalidLiveFromCrm(String ea, Integer fsUserId, String marketingEventId){
        Integer corpId = eieaConverter.enterpriseAccountToId(ea);
        MarketingLiveEntity entity = marketingLiveDAO.getByCorpIdAndMarketingEventId(corpId, marketingEventId);
        if (entity == null){
            log.info("invalidLiveFromCrm failed liveEntity is not exist ea:{} fsUserId:{} marketingEventId:{}", ea, fsUserId, marketingEventId);
            return;
        }

        marketingLiveDAO.updateStatusById(entity.getId(), LiveStatusEnum.DELETE.getStatus());
        integralServiceManager.asyncRemoveMaterial(ea, CategoryApiNameConstant.LIVE,entity.getId());
    }

    public void syncCrmInvalidMarketingEventLive(String ea){
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<String> marketingEventIds = marketingLiveDAO.getValidLiveByEa(ei);
        if (CollectionUtils.isEmpty(marketingEventIds)){
            return;
        }

        if (campaignMergeDataResetManager.enterpriseStop(ea)) {
            log.info("initOldMarketingRoleToCrmRole skip stop enterprise ea:{}", ea);
            return;
        }

        for (String marketingEventId : marketingEventIds) {
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = crmV2Manager.getDetailWithMessage(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            if (result != null && (result.getCode() == ********* || result.getMessage().equals("数据已作废或已删除"))) {
                // 作废数据清除关联
                invalidLiveFromCrm(ea, -10000, marketingEventId);
                log.info("invalid live ea:{} marketingEventId:{}", ea, marketingEventId);
            }
        }
    }

    public void syncPolyvLiveStatisticsData() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);   //只更新一个月内的直播状态
        Date limitDate = c.getTime();
        List<MarketingLiveEntity> marketingLiveEntityList = marketingLiveDAO.getSyncPolyvLive(limitDate);
        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return;
        }

        //拉取小鹅通直播数据
        for (MarketingLiveEntity entity : marketingLiveEntityList){
            String ea = eieaConverter.enterpriseIdToAccount(entity.getCorpId());
            if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
                log.info("LiveManager.syncXiaoetongLiveStatisticsData failed enterprise stop or license expire ea:{}", ea);
                continue;
            }

            syncPolyvLiveTryLock(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), entity.getMarketingEventId(), entity.getXiaoetongLiveId());
            polyvManager.syncLiveStatusByEaAndChannelId(eieaConverter.enterpriseIdToAccount(entity.getCorpId()),entity.getXiaoetongLiveId());
            this.syncLiveDataToCrm(entity);
        }
    }

    /**
     * 保存目睹直播子活动
     * @param ea
     * @param muduEventId
     */
    public void saveMuduSubEvent(String ea, String muduEventId){
        log.info("saveMuduSubEvent, ea:{}, muduEventId:{}", ea, muduEventId);
        // 1查询目睹场所列表
        String accessToken = muduManager.getAccessToken(ea);
        MuduApiGetRoomListResult muduApiGetRoomListResult = muduManager.getRoomList(accessToken, muduEventId);
        if (Objects.isNull(muduApiGetRoomListResult) || org.apache.commons.collections.CollectionUtils.isEmpty(muduApiGetRoomListResult.getItems())) {
            return;
        }

        List<MuduApiGetRoomListResult.RoomInfo> items = muduApiGetRoomListResult.getItems();
        // 2查询主活动数据
        MarketingLiveEntity parentEntity = marketingLiveDAO.getMarketingLiveByXiaoetongId(muduEventId);
        if (parentEntity == null) {
            return;
        }
        for (MuduApiGetRoomListResult.RoomInfo item : items) {
            // 检查有无关联过
            String roomId = item.getRoomId();
            MarketingLiveEntity entity = marketingLiveDAO.getMarketingLiveByXiaoetongId(roomId);
            if (entity == null) {
                // 不存在，新建
                CreateLiveVO createLiveVO = new CreateLiveVO();
                createLiveVO.setLivePlatform(LivePlatformEnum.MUDU.getType());
                createLiveVO.setEa(ea);
                createLiveVO.setCorpId(parentEntity.getCorpId());
                createLiveVO.setFsUserId(parentEntity.getCreateUserId());
                createLiveVO.setTitle(item.getRoomName());
                createLiveVO.setXiaoetongLiveUrl(item.getRoomWatchUrl());
                createLiveVO.setDesc("");
                createLiveVO.setCoverTaPath(parentEntity.getCover());
                createLiveVO.setExt("");
                if (parentEntity.getStartTime() != null) {
                    createLiveVO.setStartTime(parentEntity.getStartTime().getTime());
                }
                if (parentEntity.getEndTime() != null) {
                    createLiveVO.setEndTime(parentEntity.getEndTime().getTime());
                }
                createLiveVO.setLectureUserName(parentEntity.getLectureName());
                createLiveVO.setLecturePassword(parentEntity.getLecturePassword());
                createLiveVO.setChatOn(parentEntity.getChatOn());
                createLiveVO.setMaxLiveCount(parentEntity.getMaxLiveCount());
                createLiveVO.setAutoRecord(parentEntity.getAutoRecord());
                if (StringUtils.isNotBlank(parentEntity.getTags())){
                    createLiveVO.setTagNames(gson.fromJson(parentEntity.getTags(), TagNameList.class));
                }
                createLiveVO.setShowActivityList(parentEntity.getShowAcitivityList());
                CreateObjectDataModel.Arg arg = new CreateObjectDataModel.Arg();
                arg.setObjectData(Maps.newHashMap());
                createLiveVO.setCreateObjectDataModel(arg);
                createLiveVO.setXiaoetongLiveId(roomId);
                createLiveVO.setParentId(parentEntity.getMarketingEventId());
                createLiveVO.setMarketingTemplateId(parentEntity.getMarketingTemplateId());
                createLiveVO.setSubEvent(1);
                createLiveVO.setMuduParentId(muduEventId);
                createLiveVO.setParentHexagonFromId(parentEntity.getFormHexagonId());
                log.info("create sub mudu live, arg:{}", createLiveVO);
                com.facishare.marketing.common.result.Result<CreateMarketingLiveResult> result = liveService.createLive(createLiveVO);
                log.info("create sub mudu live, result:{}", result);
            }
        }
    }

    /**
     * 同步目睹直播数据
     */
    public void syncMuduLiveData4Job(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);   //只更新一个月内的直播状态
        Date limitDate = c.getTime();
        List<MarketingLiveEntity> marketingLiveEntityList = marketingLiveDAO.getSyncMuduLive(limitDate);
        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return;
        }

        //拉取目睹直播数据
        for (MarketingLiveEntity entity : marketingLiveEntityList){
            String ea = eieaConverter.enterpriseIdToAccount(entity.getCorpId());
            if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
                log.info("LiveManager.syncMuduLiveData failed enterprise stop or license expire ea:{}", ea);
                continue;
            }
            syncMuduLiveTryLock(ea, entity.getMarketingEventId(), entity.getXiaoetongLiveId());
            // 同步直播状态
            muduManager.syncMuduLiveStatus(ea, entity.getXiaoetongLiveId());
        }
    }

    public void syncVHallLiveData4Job(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);   //只更新一个月内的直播状态
        Date limitDate = c.getTime();
        List<MarketingLiveEntity> marketingLiveEntityList = marketingLiveDAO.getSyncVHallLive(limitDate);
        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return;
        }

        for (MarketingLiveEntity entity : marketingLiveEntityList){
            String ea = eieaConverter.enterpriseIdToAccount(entity.getCorpId());
            if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
                log.info("LiveManager.syncMuduLiveData failed enterprise stop or license expire ea:{}", ea);
                continue;
            }
            vHallManager.syncDataTryLock(ea, entity.getMarketingEventId(), entity.getXiaoetongLiveId());
        }
    }

    public void syncMuduLiveTryLock(String ea, String marketingEventId, String liveId){
        String key = muduLiveUpdateKey + liveId;
        try {
            boolean redisLock = redisManager.lock(key, 60);
            if (!redisLock) {
                return;
            }

            // 检查是否有新的子活动产生
            saveMuduSubEvent(ea, liveId);
            // 主活动数据同步
            syncSingleMuduLiveData(ea, marketingEventId);
            // 查询子活动并同步子活动数据
            List<MarketingLiveEntity> muduSubEvents = marketingLiveDAO.getMuduSubEvent(liveId, eieaConverter.enterpriseAccountToId(ea));
            if (CollectionUtils.isNotEmpty(muduSubEvents)) {
                for (MarketingLiveEntity muduSubEvent : muduSubEvents) {
                    syncSingleMuduLiveData(ea, muduSubEvent.getMarketingEventId());
                }
            }
        } catch (Exception e) {
            log.warn("LiveManager.syncMuduLiveTryLock error, liveId:{}", liveId, e);
            return;
        } finally {
            redisManager.unLock(key);
        }
    }

    /**
     * 单个活动数据同步
     * @param ea
     * @param marketingEventId
     */
    public void syncSingleMuduLiveData(String ea, String marketingEventId){
        // 同步直播数据
        muduManager.syncMuduLiveStatistic(ea, marketingEventId);
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
        this.syncLiveDataToCrm(marketingLiveEntity);
    }

    public void syncLiveDataToCrm(MarketingLiveEntity marketingLiveEntity) {
        ThreadPoolUtils.execute(() -> {
            QueryLiveEnrollListVO vo = new QueryLiveEnrollListVO();
            String ea = converter.enterpriseIdToAccount(marketingLiveEntity.getCorpId());
            vo.setEa(ea);
            vo.setFsUserId(1000);
            vo.setMarketingEventId(marketingLiveEntity.getMarketingEventId());
            vo.setPageNum(1);
            vo.setPageSize(1);
            com.facishare.marketing.common.result.Result<PageResult<QueryLiveEnrollListResult>> pageResultResult = liveService.queryLiveEnrollListForSync(vo);
            PageResult<QueryLiveEnrollListResult> data = pageResultResult.getData();
            if (data != null) {
                Integer totalCount = data.getTotalCount();
                for (int pageNum = 0; pageNum * 10 < totalCount; pageNum++) {
                    vo.setPageNum(pageNum + 1);
                    vo.setPageSize(10);
                    com.facishare.marketing.common.result.Result<PageResult<QueryLiveEnrollListResult>> tmp = liveService.queryLiveEnrollListForSync(vo);
                    PageResult<QueryLiveEnrollListResult> tmpData = tmp.getData();
                    List<QueryLiveEnrollListResult> result = tmpData.getResult();
                    result.forEach(e -> {
                        Map<String, Object> dataMap = new HashMap<>();
                        dataMap.put(CampaignMembersConstants.MARKETING_WATCH_STAUTS, e.getViewTime() != null && e.getViewTime() > 0 ? "1" : "0");
                        dataMap.put(CampaignMembersConstants.MARKETING_WATCH_DURATION, e.getViewTime());
                        dataMap.put(CampaignMembersConstants.MARKETING_PLAYBACK_STATUS, e.getReplayTime() != null && e.getReplayTime() > 0 ? "1" : "0");
                        dataMap.put(CampaignMembersConstants.MARKETING_PLAYBACK_DURATION, e.getReplayTime());
                        dataMap.put(CampaignMembersConstants.MARKETING_INTERACTION_STATUS, e.getInteractiveCount() != null && e.getInteractiveCount() > 0 ? "1" : "0");
                        dataMap.put(CampaignMembersConstants.MARKETING_INTERACTION_FREQUENCY, e.getInteractiveCount());
                        campaignMergeDataManager.updateCampaignMembersObj(ea, e.getCampaignMembersObjId(), dataMap);
                    });
                }
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    /**
     * 直播统计数据
     * @param ea
     * @param marketingEventId
     * @return
     */
    public com.facishare.marketing.common.result.Result<LiveBriefStatisticsResult> getLiveStatistics(String ea, String marketingEventId) {
        LiveBriefStatisticsResult result = new LiveBriefStatisticsResult();
        MarketingLiveEntity entity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
        if (entity == null){
            log.error("LiveManager -> getLiveStatistics failed, live is not exist marketingEventId:{}", marketingEventId);
            return com.facishare.marketing.common.result.Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        if (entity.getPlatform() != null && entity.getPlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
            MarketingLiveStatistics marketingLiveStatistics = marketingLiveStatisticsDAO.getByLiveId(entity.getLiveId());
            if (marketingLiveStatistics == null) {
                log.error("LiveManager -> getLiveStatistics failed liveStatistics is not exist marketingEventId:{}", marketingEventId);
                return com.facishare.marketing.common.result.Result.newError(SHErrorCode.MARKETING_LIVE_STATISTICS_NOT_EXIST);
            }
            result.setViewCount(marketingLiveStatistics.getTotalViewUsers());
            result.setViewTimes(marketingLiveStatistics.getViewTimes());
            result.setViewRecordUserCount(marketingLiveStatistics.getTotalRecordUsers());
            result.setViewRecordTimes(marketingLiveStatistics.getRecordTimes());
            result.setChatUserCount(marketingLiveStatistics.getTotalChatUser());
            result.setChatTimes(marketingLiveStatistics.getChatTimes());
        }
        if (entity.getPlatform() != null && (entity.getPlatform().intValue() == LivePlatformEnum.XIAOETONG.getType()||entity.getPlatform().intValue() == LivePlatformEnum.POLYV.getType()
                ||entity.getPlatform().intValue() == LivePlatformEnum.MUDU.getType() || entity.getPlatform().intValue() == LivePlatformEnum.VHALL.getType())){
            List<MarketingLiveStatistics> marketingLiveStatisticList = marketingLiveStatisticsDAO.getByXiaoetongLiveId(eieaConverter.enterpriseAccountToId(ea), entity.getXiaoetongLiveId());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(marketingLiveStatisticList)) {
                log.error("LiveManager -> getLiveStatistics failed liveStatistics is not exist marketingEventId:{}", marketingEventId);
                return com.facishare.marketing.common.result.Result.newError(SHErrorCode.MARKETING_LIVE_STATISTICS_NOT_EXIST);
            }
            MarketingLiveStatistics statistics = marketingLiveStatisticList.get(0);
            result.setViewCount(statistics.getTotalViewUsers());
            result.setViewTimes(statistics.getViewTimes());
            result.setViewRecordUserCount(statistics.getTotalRecordUsers());
            result.setViewRecordTimes(statistics.getRecordTimes());
            result.setChatUserCount(statistics.getTotalChatUser());
            result.setChatTimes(statistics.getChatTimes());
            // 目睹驻留相关的几个字段沿用观看相关的字段
            result.setStayCount(statistics.getTotalViewUsers());
            result.setStayTimes(statistics.getViewTimes());
            result.setStayDuration(statistics.getViewDuration());
            if (statistics.getPerViewDuration() != null) {
                result.setPerViewDuration(Float.valueOf(statistics.getPerViewDuration()));
            }
            result.setPerRecordDuration(statistics.getPerRecordDuration());
            if (entity.getPlatform().intValue() == LivePlatformEnum.VHALL.getType()) {
                // 微吼(注意理解一下，观看人数和回放人数加起来不是总人数，因为有重复的，有些人可能既看了直播，也看了回放，所以必须用观众人数)
                int totalAttendeeUsers = statistics.getTotalAttendeeUsers();
                if (totalAttendeeUsers != 0) {
                    result.setPerViewDuration(Float.valueOf(statistics.getViewDuration()) / totalAttendeeUsers);
                    result.setPerViewTimes(Float.valueOf(statistics.getViewTimes()) / totalAttendeeUsers);
                }
            }

            result.setViewDuration(statistics.getViewDuration());
        }
        if (entity.getPlatform() != null && (entity.getPlatform().intValue() == LivePlatformEnum.OTHER_PLATFORM.getType())) {
            List<ObjectData> campaignMembersObjs = campaignMergeDataManager.getCampaignMembersObjByMaketingEventIds(ea, Collections.singletonList(marketingEventId));
            int viewCount = 0;
            int viewRecordUserCount = 0;
            if (campaignMembersObjs != null) {
                for (ObjectData item : campaignMembersObjs) {
                    if ("1".equals(item.get("marketing_watch_stauts"))) {
                        viewCount++;
                    }
                    if ("1".equals(item.get("marketing_playback_status"))) {
                        viewRecordUserCount++;
                    }
                }
            }
            result.setViewCount(viewCount);
            result.setViewRecordUserCount(viewRecordUserCount);
        }

        result.setUv(marketingEventManager.calMarketingEventUVStatistic(ea, marketingEventId));
        result.setEnrollCount(campaignMergeDataDAO.countCampaignMergeDataByMarketingEventId(ea, marketingEventId));
        result.setSpreadCount(marketingEventManager.calMarketingEventEnterpriseSpreadNum(ea, marketingEventId));

        return com.facishare.marketing.common.result.Result.newSuccess(result);
    }

    // 刷一下历史数据 只跑一次
    public void registerMarketingLiveOldMaterial() {
        Long totalCount = marketingLiveDAO.countAll();
        if (totalCount == null || totalCount <= 0) {
            return ;
        }
        String lastId = null;
        long count = 0;
        while(count < totalCount) {
            List<MarketingLiveEntity> marketingLiveEntities = marketingLiveDAO.scanAllByLastId(lastId);
            if (CollectionUtils.isEmpty(marketingLiveEntities)) {
                break;
            }
            count += marketingLiveEntities.size();
            lastId = marketingLiveEntities.get(marketingLiveEntities.size() - 1).getId();
            Map<Integer, List<MarketingLiveEntity>> map = marketingLiveEntities.stream().collect(Collectors.groupingBy(MarketingLiveEntity::getCorpId));
            for (Map.Entry<Integer, List<MarketingLiveEntity>> integerListEntry : map.entrySet()) {
                int ei = integerListEntry.getKey();
                List<MarketingLiveEntity> entities = integerListEntry.getValue();
                String ea = eieaConverter.enterpriseIdToAccount(ei);
                List<String> marketingEventIdList = entities.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId())).map(MarketingLiveEntity::getMarketingEventId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(marketingEventIdList)) {
                    continue;
                }
                List<ContentMarketingEventMaterialRelationEntity> relationEntities = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdList(ea, marketingEventIdList);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                Map<String, ObjectData> objectDataMap = crmV2Manager.getObjectDataMapByIds(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Sets.newHashSet(marketingEventIdList), Lists.newArrayList("name"));
                List<String> objectIdList = relationEntities.stream().filter(e -> e.getObjectType() == ObjectTypeEnum.HEXAGON_SITE.getType() && StringUtils.isNotBlank(e.getObjectId())).map(ContentMarketingEventMaterialRelationEntity::getObjectId).collect(Collectors.toList());
                List<HexagonSiteEntity> hexagonSiteEntities = hexagonSiteDAO.listByIds(objectIdList);
                if (CollectionUtils.isEmpty(hexagonSiteEntities)) {
                    continue;
                }
                Map<String, HexagonSiteEntity> objectMap = hexagonSiteEntities.stream().filter(e -> "报名预约".equals(e.getName()) || "获取讲师PPT".equals(e.getName())).collect(Collectors.toMap(HexagonSiteEntity::getId, e -> e, (v1, v2) -> v1));
                for (ContentMarketingEventMaterialRelationEntity relationEntity : relationEntities) {
                    try {
                        HexagonSiteEntity hexagonSiteEntity = objectMap.get(relationEntity.getObjectId());
                        if (hexagonSiteEntity == null) {
                            continue;
                        }
                        ObjectData marketingEventData = objectDataMap.get(relationEntity.getMarketingEventId());
                        if (marketingEventData == null) {
                            continue;
                        }
                        String name = marketingEventData.getName() + "-" + hexagonSiteEntity.getName();
                        integralServiceManager.syncRegisterMaterial(ea, CategoryApiNameConstant.MICRO_PAGE, relationEntity.getObjectId(), name);
                        log.info("registerMarketingLiveOldMaterial ea: {}, id: {}, name: {}", ea, relationEntity.getObjectId(), name);
                    } catch (Exception e) {
                        log.error("registerMarketingLiveOldMaterial error:", e);
                    }
                }
            }
        }

    }

    public void initMarketingLiveChannelAccount() {
        List<MarketingLiveEntity> channelLive = marketingLiveDAO.getChannelLive();
        if (CollectionUtils.isEmpty(channelLive)) {
            return;
        }
        Map<Integer, List<MarketingLiveEntity>> map = channelLive.stream().collect(Collectors.groupingBy(MarketingLiveEntity::getCorpId));
        for (Map.Entry<Integer, List<MarketingLiveEntity>> entry : map.entrySet()) {
            String ea = eieaConverter.enterpriseIdToAccount(entry.getKey());
            List<ChannelsAccountEntity> accountEntities = channelsAccountDAO.getAllByEa(ea);
            if (CollectionUtils.isEmpty(accountEntities)) {
                continue;
            }
            int i = marketingLiveDAO.updateChannelAccountByIds(entry.getKey(), accountEntities.get(0).getId());
        }
        return;
    }

    /**
     * 同步第三方平台直播基础信息
     */
    public void syncLiveBaseInfo() {
        // 只同步一个月的直播
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        Date limitDate = c.getTime();
        List<MarketingLiveEntity> marketingLiveEntityList = marketingLiveDAO.getNeedSyncLive(limitDate);
        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return;
        }

        Map<Integer, List<MarketingLiveEntity>> collect = marketingLiveEntityList.stream().collect(Collectors.groupingBy(MarketingLiveEntity::getPlatform));
        for (Map.Entry<Integer, List<MarketingLiveEntity>> entry : collect.entrySet()) {
            Integer platform = entry.getKey();
            List<MarketingLiveEntity> list = entry.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            for (MarketingLiveEntity marketingLiveEntity : list) {
                Integer corpId = marketingLiveEntity.getCorpId();
                String ea = eieaConverter.enterpriseIdToAccount(corpId);
                String thirdPlatformId = marketingLiveEntity.getXiaoetongLiveId();
                if (StringUtils.isBlank(thirdPlatformId)) {
                    continue;
                }
                try {
                    // 每个直播都生成新的traceId
                    ContextUtil.buildNewTraceContext();
                    if (Objects.equals(platform, LivePlatformEnum.XIAOETONG.getType())) {
                        // 小鹅通直播
                        xiaoetongManager.syncXiaoetongLiveStatusByEa(ea, thirdPlatformId);
                    } else if (Objects.equals(platform, LivePlatformEnum.POLYV.getType())) {
                        // 保利威直播
                        polyvManager.syncLiveStatusByEaAndChannelId(ea, thirdPlatformId);
                    } else if (Objects.equals(platform, LivePlatformEnum.MUDU.getType())) {
                        // 目睹直播
                        if (marketingLiveEntity.getSubEvent() != 1) {
                            muduManager.syncMuduLiveStatus(ea, thirdPlatformId);
                        }
                    } else if (Objects.equals(platform, LivePlatformEnum.VHALL.getType())) {
                        // 微吼直播
                        vHallManager.syncLiveStatus(ea, thirdPlatformId);
                    }
                } catch (Exception e) {
                    log.info("syncLiveBaseInfo error, ea: {}, platform:{}, thirdPlatformId: {}, e:", ea, platform, thirdPlatformId, e);
                }
            }
        }
    }

    /**刷marketing_live_view_login表里的ea */
    public void refreshMarketingLiveViewLoginEa(){
        int totalCount = 0;
        log.info("start to refreshEa for user table");
        List<MarketingLiveViewLoginEntity> marketingLiveViewLoginEntities = marketingLiveViewLoginDAO.getAllByEaIsNull();
        while(CollectionUtils.isNotEmpty(marketingLiveViewLoginEntities)){
            totalCount += marketingLiveViewLoginEntities.size();
            setMarketingLiveViewLoginEa(marketingLiveViewLoginEntities);
            marketingLiveViewLoginEntities = marketingLiveViewLoginDAO.getAllByEaIsNull();
            log.info("refreshEa for account marketing_live_view_login count:{}", totalCount);
        }
        log.info("stop to refreshEa for user table");
    }

    private void setMarketingLiveViewLoginEa(List<MarketingLiveViewLoginEntity> marketingLiveViewLoginEntities){
       List<String> marketingEventIdList = marketingLiveViewLoginEntities.stream().map(MarketingLiveViewLoginEntity::getMarketingEventId).distinct().collect(Collectors.toList());
       List<MarketingLiveEntity> marketingLiveEntities = marketingLiveDAO.getByMarketingEventIds(marketingEventIdList);
       Map<String, Integer> marketingEventIdEaMap = marketingLiveEntities.stream().collect(Collectors.toMap(MarketingLiveEntity::getMarketingEventId, MarketingLiveEntity::getCorpId, (v1,v2) ->v1));

       List<MarketingLiveViewLoginEntity> updateList = new ArrayList<>();
       for (MarketingLiveViewLoginEntity marketingLiveViewLoginEntity : marketingLiveViewLoginEntities) {
           if (StringUtils.isNotBlank(marketingLiveViewLoginEntity.getEa())){
               continue;
           }
           if (marketingEventIdEaMap.get(marketingLiveViewLoginEntity.getMarketingEventId()) != null){
               marketingLiveViewLoginEntity.setEa(eieaConverter.enterpriseIdToAccount(marketingEventIdEaMap.get(marketingLiveViewLoginEntity.getMarketingEventId())));
           }else {
               marketingLiveViewLoginEntity.setEa(defaultEa);
           }
           updateList.add(marketingLiveViewLoginEntity);
       }
       if (CollectionUtils.isNotEmpty(updateList)){
           marketingLiveViewLoginDAO.batchUpdateEa(updateList);
       }
    }
}