package com.facishare.marketing.provider.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;

/**
 * 企业物料统计
 *
 * <AUTHOR>
 */
@Data
@Entity
public class EnterpriseObjectAmountStatisticEntity extends ChannelDataEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    
    private Integer objectType;
    private String objectId;
    private Integer activeCount;
    private Integer forwardCount;
    private Integer lookUpCount;
    private Integer employeeForwardCount;
    private Integer forwardEmployeeCount;
    private Integer forwardOtherCount;
    private Date createTime;
    private Date lastActiveTime;
}
