package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.miniAppSetting.BasePageTimingArg;
import com.facishare.marketing.api.arg.miniAppSetting.BaseTimingArg;
import com.facishare.marketing.api.arg.miniAppSetting.SetMiniappActivityListConfigArg;
import com.facishare.marketing.api.result.miniAppSetting.BaseInfoResult;
import com.facishare.marketing.api.result.miniAppSetting.ContentRankingResult;
import com.facishare.marketing.api.result.miniAppSetting.DataBriefingResult;
import com.facishare.marketing.api.result.miniAppSetting.StaffLeadRankingResult;
import com.facishare.marketing.api.result.statistics.BaseStatisticsResult;
import com.facishare.marketing.api.result.statistics.BaseStatisticsResult.StatisticsDetails;
import com.facishare.marketing.api.service.MiniAppSettingService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.miniAppSetting.TimeRangeTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.ActivityShowDataConfigDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.MarketingObjectStatisticWithSourceDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.dto.MarketingObjectStatisticWithSourceDTO;
import com.facishare.marketing.provider.entity.ActivityShowDataConfigEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.innerData.MiniAppVisitData;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.miniAppSetting.MiniAppSettingManager;
import com.facishare.marketing.provider.manager.miniAppSetting.result.BaseVisitTrendResult;
import com.facishare.marketing.provider.manager.miniAppSetting.result.DailySummaryResult;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.util.MapUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2021/02/22
 **/
@Slf4j
@Service("miniAppSettingService")
public class MiniAppSettingServiceImpl implements MiniAppSettingService {

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private MiniAppSettingManager miniAppSettingManager;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private MarketingObjectStatisticWithSourceDAO marketingObjectStatisticWithSourceDAO;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private ActivityShowDataConfigDAO activityShowDataConfigDAO;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;

    @Autowired
    private RedisManager redisManager;

    @Override
    public Result<DataBriefingResult> dataBriefing(BaseTimingArg arg) {
        // 校验当前小程序是否为托管小程序
        Optional<String> appIdOptional = wechatAccountManager.getWxAppIdByEa(arg.getEa(), MKThirdPlatformConstants.PLATFORM_ID);
        if (!appIdOptional.isPresent() || StringUtils.isBlank(appIdOptional.get()) || WxAppInfoEnum.isSystemApp(appIdOptional.get())) {
            log.warn("MiniAppSettingServiceImpl.dataBriefing error  arg:{}", arg);
            return Result.newError(SHErrorCode.NOT_HOSTING_MINIAPP);
        }
        DataBriefingResult dataBriefingResult = new DataBriefingResult();


        // 判断查询时间（日，周，月）
        Long timeDifference = arg.getEndTime() - arg.getStartTime();
        BaseVisitTrendResult baseVisitTrendResult = null;
        String beginTime = DateUtil.format(new Date(arg.getStartTime()), "yyyyMMdd");
        String endTime = DateUtil.format(new Date(arg.getEndTime()), "yyyyMMdd");
        Long formatStartTime = arg.getStartTime();
        Long formatEndTime = arg.getEndTime();
        if(arg.getFilterType()!=null&&arg.getFilterType()==1){
            Result<BaseVisitTrendResult> uvAndPv = getVisitUvAndPv(arg, appIdOptional.get());
            baseVisitTrendResult = uvAndPv.getData();
        }else {
            if (timeDifference == 0) {
                // 天
                final MiniAppVisitData miniAppStatData = redisManager.getMiniappVisitData(appIdOptional.get(), "DAY");
                if (miniAppStatData != null){
                    baseVisitTrendResult = new BaseVisitTrendResult();
                    baseVisitTrendResult.setVisitPv(miniAppStatData.getVisitPv());
                    baseVisitTrendResult.setVisitUv(miniAppStatData.getVisitUv());
                    //更新redis中的数据
                    ThreadPoolUtils.submit(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                BaseVisitTrendResult visitTrend = miniAppSettingManager.getVisitTrend(arg.getEa(),appIdOptional.get(), beginTime, endTime, TimeRangeTypeEnum.DAY);
                                if (visitTrend != null) {
                                    miniAppStatData.setVisitPv(visitTrend.getVisitPv());
                                    miniAppStatData.setVisitUv(visitTrend.getVisitUv());
                                    redisManager.setMiniappVisitData(appIdOptional.get(), "DAY", miniAppStatData);
                                }
                            }catch (Exception e){
                                log.info("MiniAppSettingServiceImpl.getVisitTrend as day exception arg:{} e:", arg, e);
                            }
                        }
                    }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
                    //如果开始时间等于结束时间,查完微信数据将时间拨直当天自然日时间
                    formatStartTime = DateUtil.getStartTimeCurrentDay(new Date(arg.getStartTime()));
                    formatEndTime = DateUtil.getEndTimeCurrentDay(new Date(arg.getEndTime()));
                }else {
                    baseVisitTrendResult = miniAppSettingManager.getVisitTrend(arg.getEa(), appIdOptional.get(), beginTime, endTime, TimeRangeTypeEnum.DAY);
                    formatStartTime = DateUtil.getDayStartTime(new Date(formatStartTime));
                    formatEndTime = DateUtil.getDayEndTime(new Date(formatEndTime));
                    if (baseVisitTrendResult != null){
                        MiniAppVisitData wxMiniAppStatData = new MiniAppVisitData(baseVisitTrendResult.getVisitPv(), baseVisitTrendResult.getVisitUv());
                        redisManager.setMiniappVisitData(appIdOptional.get(), "DAY", wxMiniAppStatData);
                    }
                }
            } else if (timeDifference == 1000L * 3600 * 24 * 6) {
                // 周
                final MiniAppVisitData miniAppStatData = redisManager.getMiniappVisitData(appIdOptional.get(), "WEEK");
                if (miniAppStatData != null){
                    baseVisitTrendResult = new BaseVisitTrendResult();
                    baseVisitTrendResult.setVisitPv(miniAppStatData.getVisitPv());
                    baseVisitTrendResult.setVisitUv(miniAppStatData.getVisitUv());
                    //更新redis中的数据
                    ThreadPoolUtils.submit(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                BaseVisitTrendResult visitTrend = miniAppSettingManager.getVisitTrend(arg.getEa(), appIdOptional.get(), beginTime, endTime, TimeRangeTypeEnum.WEEK);
                                if (visitTrend != null) {
                                    miniAppStatData.setVisitPv(visitTrend.getVisitPv());
                                    miniAppStatData.setVisitUv(visitTrend.getVisitUv());
                                    redisManager.setMiniappVisitData(appIdOptional.get(), "WEEK", miniAppStatData);
                                }
                            }catch (Exception e){
                                log.info("MiniAppSettingServiceImpl.getVisitTrend as week exception arg:{} e:", arg, e);
                            }
                        }
                    }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
                }else {
                    baseVisitTrendResult = miniAppSettingManager.getVisitTrend(arg.getEa(), appIdOptional.get(), beginTime, endTime, TimeRangeTypeEnum.WEEK);
                    if (baseVisitTrendResult != null){
                        MiniAppVisitData wxMiniAppStatData = new MiniAppVisitData(baseVisitTrendResult.getVisitPv(), baseVisitTrendResult.getVisitUv());
                        redisManager.setMiniappVisitData(appIdOptional.get(), "WEEK", wxMiniAppStatData);
                    }
                }
            } else if (timeDifference >= 1000L * 3600 * 24 * 27 && timeDifference <= 1000L * 3600 * 24 * 30) {
                // 月
                final MiniAppVisitData miniAppStatData = redisManager.getMiniappVisitData(appIdOptional.get(), "MONTH");
                if (miniAppStatData != null){
                    baseVisitTrendResult = new BaseVisitTrendResult();
                    baseVisitTrendResult.setVisitPv(miniAppStatData.getVisitPv());
                    baseVisitTrendResult.setVisitUv(miniAppStatData.getVisitUv());
                    //更新redis中的数据
                    ThreadPoolUtils.submit(new Runnable() {
                        public void run() {
                            try {
                                BaseVisitTrendResult visitTrend = miniAppSettingManager.getVisitTrend(arg.getEa(), appIdOptional.get(), beginTime, endTime, TimeRangeTypeEnum.MONTH);
                                miniAppStatData.setVisitPv(visitTrend.getVisitPv());
                                miniAppStatData.setVisitUv(visitTrend.getVisitUv());
                                redisManager.setMiniappVisitData(appIdOptional.get(), "MONTH", miniAppStatData);
                            }catch (Exception e){
                                log.info("MiniAppSettingServiceImpl.getVisitTrend as MONTH exception arg:{} e:", arg, e);
                            }
                        }
                    }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
                }else {
                    baseVisitTrendResult = miniAppSettingManager.getVisitTrend(arg.getEa(), appIdOptional.get(), beginTime, endTime, TimeRangeTypeEnum.MONTH);
                    if (baseVisitTrendResult != null){
                        MiniAppVisitData wxMiniAppStatData = new MiniAppVisitData(baseVisitTrendResult.getVisitPv(), baseVisitTrendResult.getVisitUv());
                        redisManager.setMiniappVisitData(appIdOptional.get(), "MONTH", wxMiniAppStatData);
                    }
                }
            } else {
                return Result.newError(SHErrorCode.TIME_RANEG_ERROR);
            }
        }
        // 查询线索
        int leadCount = customizeFormDataUserDAO.countMinAppEnrollCountByEa(arg.getEa(), new Date(formatStartTime), new Date(formatEndTime), true);
        if (baseVisitTrendResult == null) {
            dataBriefingResult.setVisitPv(0L);
            dataBriefingResult.setVisitUv(0L);
        } else {
            dataBriefingResult.setVisitPv(baseVisitTrendResult.getVisitPv());
            dataBriefingResult.setVisitUv(baseVisitTrendResult.getVisitUv());
        }
        dataBriefingResult.setLeadCount(leadCount);
        return Result.newSuccess(dataBriefingResult);
    }

    private Result<BaseVisitTrendResult> getVisitUvAndPv(BaseTimingArg arg,String appId) {
        List<DateUtil.DateArg>  dateArgList;
        Map<String, BaseVisitTrendResult> dateNumMap = Maps.newConcurrentMap();
        try {
            dateArgList = DateUtil.getMonAndWeekAndDate(arg.getStartTime(), arg.getEndTime(), "yyyyMMdd");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        log.info("MiniAppSettingServiceImpl.getVisitUvAndPv dateArgList:{} arg:{} ", dateArgList);
        if(CollectionUtils.isNotEmpty(dateArgList)){
            CountDownLatch countDownLatch = new CountDownLatch(dateArgList.size());
            for (DateUtil.DateArg data : dateArgList) {
                ThreadPoolUtils.execute(() -> {
                    BaseVisitTrendResult baseVisitTrendResult = null;
                    try {
                        baseVisitTrendResult = miniAppSettingManager.getVisitTrend(arg.getEa(), appId, data.getStartDate(), data.getEndDate(), data.getTimeRangeTypeEnum());
                        if (baseVisitTrendResult != null) {
                            dateNumMap.put(data.getStartDate()+"-"+data.getEndDate(),baseVisitTrendResult);
                        }
                    } catch (Exception e) {
                        log.warn("MiniAppSettingServiceImpl.visitTrend error e:{}", e);
                    } finally {
                        baseVisitTrendResult = null;
                        countDownLatch.countDown();
                    }
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
            try {
                countDownLatch.await(30L, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("MiniAppSettingServiceImpl.getVisitUvAndPv error e:{}", e);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
        }
        log.info("MiniAppSettingServiceImpl.getVisitUvAndPv dateNumMap:{} arg:{} ", dateNumMap);
        Long countPv=0L;
        Long countUv=0L;
        for (Map.Entry<String, BaseVisitTrendResult> entry : dateNumMap.entrySet()) {
            countUv+=entry.getValue().getVisitUv();
            countPv+=entry.getValue().getVisitPv();
        }
        BaseVisitTrendResult result = new BaseVisitTrendResult();
        result.setVisitPv(countPv);
        result.setVisitUv(countUv);
        return Result.newSuccess(result);
    }


    @Override
    public Result<BaseInfoResult> baseInfo(BaseTimingArg arg) {
        // 校验当前小程序是否为托管小程序
        Optional<String> appIdOptional = wechatAccountManager.getWxAppIdByEa(arg.getEa(), MKThirdPlatformConstants.PLATFORM_ID);
        if (!appIdOptional.isPresent() || StringUtils.isBlank(appIdOptional.get()) || WxAppInfoEnum.isSystemApp(appIdOptional.get())) {
            log.warn("MiniAppSettingServiceImpl.baseInfo error arg:{}", arg);
            return Result.newError(SHErrorCode.NOT_HOSTING_MINIAPP);
        }

        DailySummaryResult dailySummaryResult = miniAppSettingManager.getDailySummary(arg.getEa(), appIdOptional.get());
        BaseInfoResult baseInfoResult = new BaseInfoResult();
        if (dailySummaryResult == null) {
            log.warn("MiniAppSettingServiceImpl.dataBriefing dailySummaryResult is null");
            baseInfoResult.setVisitTotal(0L);
            return Result.newSuccess(baseInfoResult);
        }
        baseInfoResult.setVisitTotal(dailySummaryResult.getVisitTotal());
        return Result.newSuccess(baseInfoResult);
    }

    @Override
    public Result<BaseStatisticsResult> visitTrend(BaseTimingArg arg) {
        // 校验当前小程序是否为托管小程序
        BaseStatisticsResult baseStatisticsResult = new BaseStatisticsResult();
        Optional<String> appIdOptional = wechatAccountManager.getWxAppIdByEa(arg.getEa(), MKThirdPlatformConstants.PLATFORM_ID);
        if (!appIdOptional.isPresent() || StringUtils.isBlank(appIdOptional.get()) || WxAppInfoEnum.isSystemApp(appIdOptional.get())) {
            log.warn("MiniAppSettingServiceImpl.visitTrend error arg:{}", arg);
            return Result.newError(SHErrorCode.NOT_HOSTING_MINIAPP);
        }
        List<String> timeDifferenceList = DateUtil.findDates(new Date(arg.getStartTime()), new Date(arg.getEndTime()), "yyyyMMdd");
        List<StatisticsDetails> statisticsDetailsList = Lists.newArrayList();
        Map<String, Integer> dateNumMap = Maps.newConcurrentMap();
        CountDownLatch countDownLatch = new CountDownLatch(timeDifferenceList.size());
        for (String time : timeDifferenceList) {
            ThreadPoolUtils.execute(() -> {
                BaseVisitTrendResult baseVisitTrendResult = null;
                try {
                    baseVisitTrendResult = miniAppSettingManager.getVisitTrend(arg.getEa(), appIdOptional.get(), time, time, TimeRangeTypeEnum.DAY);
                    if (baseVisitTrendResult != null) {
                        dateNumMap.put(time, baseVisitTrendResult.getVisitUv().intValue());
                    }
                } catch (Exception e) {
                    log.warn("MiniAppSettingServiceImpl.visitTrend error e:{}", e);
                } finally {
                    baseVisitTrendResult = null;
                    countDownLatch.countDown();
                }
            }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        }
        try {
            countDownLatch.await(30L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("MailManager.queryAllOpenAndClickUserNum error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        Map<String, Integer> resultMap = MapUtil.sortByKeyAsc(dateNumMap);
        for (Map.Entry<String, Integer> entry : resultMap.entrySet()) {
            StatisticsDetails statisticsDetails = new StatisticsDetails();
            statisticsDetails.setSimpleTime(entry.getKey());
            statisticsDetails.setNumResult(entry.getValue());
            statisticsDetailsList.add(statisticsDetails);
        }
        baseStatisticsResult.setLinearGraphData(statisticsDetailsList);
        return Result.newSuccess(baseStatisticsResult);
    }

    @Override
    public Result<PageResult<StaffLeadRankingResult>> staffLeadRanking(BasePageTimingArg arg) {
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<StaffLeadRankingResult> staffLeadRankingResultList = Lists.newArrayList();
        PageResult<StaffLeadRankingResult> pageResult = new PageResult();
        pageResult.setPageNum(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(staffLeadRankingResultList);
        List<CustomizeFormClueNumDTO> customizeFormClueNumDTOList = Lists.newArrayList();
        int totalCount = 0;
        if (arg.getStartTime() == null || arg.getEndTime() == null) {
            customizeFormClueNumDTOList = customizeFormDataUserDAO.pageStaffMinAppEnrollLead(arg.getEa(), null, null, false, page);
            totalCount = customizeFormDataUserDAO.countStaffMinAppEnrollLead(arg.getEa(), null, null, false);
        } else {
            customizeFormClueNumDTOList = customizeFormDataUserDAO.pageStaffMinAppEnrollLead(arg.getEa(), new Date(arg.getStartTime()), new Date(arg.getEndTime()), true, page);
            totalCount = customizeFormDataUserDAO.countStaffMinAppEnrollLead(arg.getEa(), new Date(arg.getStartTime()), new Date(arg.getEndTime()), true);
        }
        if (CollectionUtils.isEmpty(customizeFormClueNumDTOList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(totalCount);
        List<Integer> spreadFsUserIds = customizeFormClueNumDTOList.stream().map(CustomizeFormClueNumDTO::getSpreadFsUid).collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(arg.getEa(), spreadFsUserIds, true);
        for (CustomizeFormClueNumDTO customizeFormClueNumDTO : customizeFormClueNumDTOList) {
            FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(customizeFormClueNumDTO.getSpreadFsUid());
            StaffLeadRankingResult staffLeadRankingResult = new StaffLeadRankingResult();
            staffLeadRankingResult.setName(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
            staffLeadRankingResult.setClueNum(customizeFormClueNumDTO.getCount());
            staffLeadRankingResultList.add(staffLeadRankingResult);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<ContentRankingResult>> contentRanking(BasePageTimingArg arg) {
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<ContentRankingResult> contentRankingResultList = Lists.newArrayList();
        PageResult<ContentRankingResult> pageResult = new PageResult();
        pageResult.setPageNum(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(contentRankingResultList);
        List<MarketingObjectStatisticWithSourceDTO> marketingObjectStatisticWithSourceDTOList = Lists.newArrayList();
        int totalCount = 0;
        if (arg.getStartTime() == null || arg.getEndTime() == null) {
            marketingObjectStatisticWithSourceDTOList = marketingObjectStatisticWithSourceDAO.pageObjectLookUpCount(arg.getEa(), null, null, false, page);
            totalCount = marketingObjectStatisticWithSourceDAO.countObjectLookUpCount(arg.getEa(), null, null, false);
        } else {
            //String beginTime = DateUtil.format(new Date(arg.getStartTime()), "yyyy-MM-dd");
            //String endTime = DateUtil.format(new Date(arg.getEndTime()), "yyyy-MM-dd");
            marketingObjectStatisticWithSourceDTOList = marketingObjectStatisticWithSourceDAO.pageObjectLookUpCount(arg.getEa(), new Date(arg.getStartTime()), new Date(arg.getEndTime()), true, page);
            totalCount = marketingObjectStatisticWithSourceDAO.countObjectLookUpCount(arg.getEa(), new Date(arg.getStartTime()), new Date(arg.getEndTime()), true);
        }
        if (CollectionUtils.isEmpty(marketingObjectStatisticWithSourceDTOList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(totalCount);
        Map<Integer, List<String>> idMap = Maps.newHashMap();
        for (MarketingObjectStatisticWithSourceDTO marketingObjectStatisticWithSourceDTO : marketingObjectStatisticWithSourceDTOList) {
            idMap.computeIfAbsent(marketingObjectStatisticWithSourceDTO.getObjectType(), data -> Lists.newArrayList()).add(marketingObjectStatisticWithSourceDTO.getObjectId());
        }
        Map<String, String> objectMap = objectManager.getObjectName(null, idMap);
        for (MarketingObjectStatisticWithSourceDTO dto : marketingObjectStatisticWithSourceDTOList) {
            ContentRankingResult contentRankingResult = new ContentRankingResult();
            contentRankingResult.setContentName(objectMap.get(dto.getObjectId()));
            contentRankingResult.setViewCount(dto.getCount());
            contentRankingResultList.add(contentRankingResult);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result setMiniappActivityListConfig(String ea, Integer fsUserId, SetMiniappActivityListConfigArg arg) {
        ActivityShowDataConfigEntity entity = activityShowDataConfigDAO.queryByEa(ea);
        if (entity == null){
            entity = new ActivityShowDataConfigEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setUserId(fsUserId);
            entity.setActivityTypeConfig(arg.getActivityShowConfigTypeData());
            activityShowDataConfigDAO.insert(entity);
            return Result.newSuccess(entity.getId());
        }

        entity.setActivityTypeConfig(arg.getActivityShowConfigTypeData());
        activityShowDataConfigDAO.updateByEa(ea, entity);
        return Result.newSuccess();
    }

    @Override
    public Result<String> getMiniappAccessTokenByAppId(String ea, Integer fsUserId, String platformId, String wxAppId) {
        EaWechatAccountBindEntity entity = eaWechatAccountBindDao.getCustomizedByPlatformIdAndWxAppId(ea, platformId, wxAppId);
        if (entity == null || !StringUtils.equals(ea, entity.getEa())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String accessToken = null;
        WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(ea, wxAppId);
        if (wechatAccountConfig != null){
            accessToken = wechatAccountManager.getAccessTokenByWxAppId(ea, wxAppId);
        }

        return Result.newSuccess(accessToken);
    }
}
