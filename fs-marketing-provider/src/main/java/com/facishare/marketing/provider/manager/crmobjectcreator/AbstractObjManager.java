/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.provider.innerArg.CreateAdvertisingDetailObjArg;
import com.facishare.marketing.provider.manager.ObjectServiceManager;
import com.facishare.marketing.provider.remote.FieldDescribeService;
import com.facishare.marketing.provider.remote.rest.arg.FindLayoutListArg;
import com.facishare.marketing.provider.remote.rest.arg.UpdateLayoutListArg;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.arg.CreateObjectArg;
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.CreateObjectResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public abstract class AbstractObjManager {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ObjectServiceManager objectServiceManager;

    @Autowired
    private FieldDescribeService fieldDescribeService;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @FilterLog
    public ObjectDescribe getOrCreateObjDescribe(String ea) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), this.getApiName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && !getDescribeResultResult.isSuccess()) {
            throw new BizException(getDescribeResultResult.getCode(), getDescribeResultResult.getMessage());
        } else if (getDescribeResultResult.isSuccess()) {
            return getDescribeResultResult.getData().getDescribe();
        }
        CreateObjectArg crmCreateObjectVO = new CreateObjectArg();
        crmCreateObjectVO.setActive(true);
        crmCreateObjectVO.setIncludeLayout(true);
        crmCreateObjectVO.setLayoutType("detail");
        crmCreateObjectVO.setJsonData(this.getJsonData());
        crmCreateObjectVO.setJsonLayout(this.getJsonLayout());
        crmCreateObjectVO.setJsonListLayout(this.getJsonListLayout());
        InnerResult<CreateObjectResult> result = objectServiceManager.createObject(ei, SuperUserConstants.USER_ID, crmCreateObjectVO);
        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getErrCode(), result.getErrMessage());
        }
        return result.getResult().getObjectDescribe();
    }

    public abstract String getApiName();

    public abstract String getJsonData();

    public abstract String getJsonLayout();

    public abstract String getJsonListLayout();

    /**
     * 隐藏对象列表的新建、导入、智能表单三个按钮
     * 隐藏新建按钮，注意，目前需要配置才能实现，配置文件：fs-paas-appframework-config  key:list_layout_gray_object 在该key后面追加对应的对象
     */
    public void tryHideAddImportFormButton(String ea, String objApiName) {
        try {
            HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
            FindLayoutListArg arg = new FindLayoutListArg();
            arg.setLayoutType("list_layout");
            arg.setObjectDescribeApiName(objApiName);
            com.fxiaoke.crmrestapi.common.result.Result result = fieldDescribeService.findListLayout(systemHeader, arg);
            if (!result.isSuccess() || result.getData() == null) {
                return;
            }
            log.info("findLayoutList ea: {} objApiName: {} 结果: {}", ea, objApiName, result);
            JSONObject layoutObj = JSON.parseObject(JsonUtil.toJson(result.getData()));
            JSONArray jsonArray = layoutObj.getJSONArray("layouts");
            if (CollectionUtils.isEmpty(jsonArray)) {
                return;
            }
            JSONObject defaultListLayout = null;
            for (Object o : jsonArray) {
                JSONObject jsonObject = (JSONObject) o;
                if (jsonObject.get("api_name") != null && jsonObject.get("api_name").toString().equals("default_list_layout")) {
                    defaultListLayout = jsonObject;
                    break;
                }
            }
            if (defaultListLayout == null) {
                return;
            }
            String defaultListLayoutData = "{\"field_section\":[],\"buttons\":[],\"related_list_name\":\"\",\"button_info\":[{\"exposed_button\":1,\"hidden\":[\"Add_button_default\",\"IntelligentForm_button_default\",\"Import_button_default\"],\"page_type\":\"list\",\"render_type\":\"list_normal\",\"order\":[\"Export_button_default\",\"ExportFile_button_default\"]},{\"hidden\":[],\"page_type\":\"list\",\"render_type\":\"list_batch\",\"order\":[\"ChangeOwner_button_default\",\"Abolish_button_default\",\"AddTeamMember_button_default\",\"DeleteTeamMember_button_default\",\"Lock_button_default\",\"Unlock_button_default\",\"Export_button_default\",\"ExportFile_button_default\",\"ChangePartnerOwner_button_default\",\"Print_button_default\"]},{\"exposed_button\":0,\"hidden\":[],\"page_type\":\"list\",\"render_type\":\"list_single\",\"order\":[]}],\"nameI18nKey\":\"paas.udobj.list_page\",\"filters_info\":[{\"page_type\":\"list\",\"fields\":[]}],\"type\":\"list\",\"all_page_summary_info\":[],\"view_info\":[{\"name\":\"list_view\",\"is_default\":true,\"is_show\":true},{\"name\":\"split_view\",\"is_default\":false,\"is_show\":true}],\"enable_selected_layout\":false,\"define_view_info\":[\"list_view\",\"split_view\"],\"api_name\":\"list_component\",\"header\":\"列表页\",\"filter_info\":[],\"grayLimit\":1,\"summary_info\":[],\"scene_info\":[{\"hidden\":[],\"page_type\":\"list\",\"render_type\":\"drop_down\",\"order\":[\"All\",\"Participate\",\"InCharge\",\"SubInCharge\",\"InChargeDept\",\"Shared\",\"SubParticipate\"]}]}";
            JSONObject defaultListLayoutDataJsonObject = JSONObject.parseObject(defaultListLayoutData);
            for (Object o : defaultListLayout.getJSONArray("components")) {
                JSONObject jsonObject = (JSONObject) o;
                if (jsonObject.get("api_name") != null && jsonObject.get("api_name").toString().equals("list_component")) {
                    jsonObject.putAll(defaultListLayoutDataJsonObject);
                    break;
                }
            }
            UpdateLayoutListArg updateLayoutListArg = new UpdateLayoutListArg();
            updateLayoutListArg.setLayoutData(defaultListLayout);
            com.fxiaoke.crmrestapi.common.result.Result updateResult = fieldDescribeService.updateListLayout(systemHeader, updateLayoutListArg);
            log.info("更新结果： ea: {} objApiName: {} result: {}", ea, objApiName, updateResult);
        } catch (Exception e) {
            log.error("隐藏新增按钮出错, ea:{} objApiName: {}", ea, objApiName, e);
        }

    }

    /**
     * 主要 rentokil 刷库
     *
     * @param ea
     */
    public void tryAddExtendObjDataId(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        ObjectDescribe objectDescribe = this.getOrCreateObjDescribe(ea);
        if (objectDescribe == null) {
            log.warn("Ea:{} have not EmployeePromoteDetailObj", ea);
            return;
        }
        if (!objectDescribe.getFields().containsKey("extend_obj_data_id")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"" + this.getApiName() + "\",\"default_is_expression\":false,\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"extend_obj_data_id\",\"type\":\"text\",\"default_to_zero\":false,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"extend_obj_data_id\",\"define_type\":\"package\",\"is_extend\":false,\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"max_length\":100,\"status\":\"released\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }

}
