package com.facishare.marketing.provider.manager.customizedSpread;

import com.facishare.marketing.api.arg.GetMarketingActivityDetailData;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPreviewArg;
import com.facishare.marketing.api.data.MarketingUserGroupData;
import com.facishare.marketing.api.result.MarketingMessageSendCountResult;
import com.facishare.marketing.api.result.MobileMarketingUserResult;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.CustomizedSpreadDetailResult;
import com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityPreviewData;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityActionService;
import com.facishare.marketing.common.contstant.CustomizeFunctionConstants;
import com.facishare.marketing.common.enums.AssociateIdTypeEnum;
import com.facishare.marketing.common.enums.MarketingActivityTypeEnum;
import com.facishare.marketing.common.enums.MarketingMessageSendRecordSendStatusEnum;
import com.facishare.marketing.common.enums.SendStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmMarketingMessageSendRecordFieldEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.whatsapp.SendTypeEnum;
import com.facishare.marketing.common.enums.whatsapp.TaskSendStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.customizedSpread.CustomizedSpreadChannelDAO;
import com.facishare.marketing.provider.dao.customizedSpread.CustomizedSpreadSendTaskDAO;
import com.facishare.marketing.provider.dto.MarketingUserWithEmail;
import com.facishare.marketing.provider.entity.ExternalConfig;
import com.facishare.marketing.provider.entity.customizedSpread.CustomizedSpreadChannelEntity;
import com.facishare.marketing.provider.entity.customizedSpread.CustomizedSpreadSendTaskEntity;
import com.facishare.marketing.provider.entity.data.CustomizedSpreadSendData;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.innerArg.crm.ExecuteCustomizeFunctionArg;
import com.facishare.marketing.provider.innerResult.crm.ExecuteCustomizeFunctionResult;
import com.facishare.marketing.provider.manager.CustomizeFunctionManager;
import com.facishare.marketing.provider.manager.MarketingUserGroupManager;
import com.facishare.marketing.provider.manager.NoticeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingMessageSendRecordObjManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasAddMarketingActivityArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("customizedSpreadManager")
public class CustomizedSpreadManager implements MarketingActivityActionService {

    private static final String MARKETING_USER = "MARKETING_USER";

    @Autowired
    private CustomizedSpreadChannelDAO customizedSpreadChannelDAO;

    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;

    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private CustomizedSpreadSendTaskDAO customizedSpreadSendTaskDAO;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private CustomizeFunctionManager customizeFunctionManager;

    @Autowired
    private MarketingMessageSendRecordObjManager marketingMessageSendRecordObjManager;

    @Autowired
    private NoticeManager noticeManager;

    @Override
    public AddMarketingActivityResult doAddAction(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        AddMarketingActivityArg.CustomizedSpreadArg customizedSpreadArg = addMarketingActivityArg.getCustomizedSpreadArg();
        // 检验参数
        Result<Void> checkResult = checkParam(customizedSpreadArg);
        if (!checkResult.isSuccess()) {
            return new AddMarketingActivityResult(SHErrorCode.getByCode(checkResult.getErrCode()));
        }

        // 创建营销活动
        Result<String> marketingActivityIdResult = createMarketingActivity(ea, fsUserId, addMarketingActivityArg);
        if (!marketingActivityIdResult.isSuccess()){
            AddMarketingActivityResult crmResult = new AddMarketingActivityResult();
            crmResult.setCrmErrorCode(marketingActivityIdResult.getErrCode());
            crmResult.setCrmErrorMsg(marketingActivityIdResult.getErrMsg());
            return crmResult;
        }
        String marketingActivityId = marketingActivityIdResult.getData();

        try {
            // 创建发送任务，这里只创建任务，不产生明细，明细在发送的时候创建
            String sendTaskId = createSendTask(ea, fsUserId, customizedSpreadArg);
            // 保存推广配置marketing_activity_external_config
            boolean needAudit = marketingActivityAuditManager.isNeedAudit(ea);
            createMarketingActivityExternalConfig(ea, sendTaskId, marketingActivityId, needAudit, addMarketingActivityArg);
            // 如果是立即发送，执行发送任务
            if (SendTypeEnum.IMMEDIATELY.getValue().equals(customizedSpreadArg.getSendType()) && !needAudit) {
                sendMessageByTaskId(ea, sendTaskId);
            }
        } catch (Exception e) {
            log.error("自定义推广营销活动创建异常, ea: {} arg: {}", ea, customizedSpreadArg, e);
            // 营销活动作废掉
            crmV2Manager.bulkInvalidIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), Lists.newArrayList(marketingActivityId));
            return new AddMarketingActivityResult(SHErrorCode.SYSTEM_ERROR);
        }
        return new AddMarketingActivityResult(marketingActivityId);
    }

    public Result<Void> sendMessageByTaskId(String ea, String taskId) {
        // 检查任务状态
        CustomizedSpreadSendTaskEntity taskEntity = customizedSpreadSendTaskDAO.getById(ea, taskId);
        if (taskEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        Integer createBy = taskEntity.getCreateBy();

        if (!Objects.equals(taskEntity.getSendStatus(), TaskSendStatusEnum.WAIT.getStatus())) {
            log.info("CustomizedSpreadManager -> sendMessageByTaskId task status is not wait, ea: {}, taskId: {}", ea, taskId);
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), "task status is not wait");
        }

        // 更新状态为发送中
        int updated = customizedSpreadSendTaskDAO.updateSendingStatus(ea, taskId);
        if (updated <= 0) {
            log.info("CustomizedSpreadManager -> sendMessageByTaskId updateSendingStatus failed, ea: {}, taskId: {}", ea, taskId);
            return Result.newSuccess();
        }

        // 查询推广配置
        MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.CUSTOMIZED_SPREAD.getType(), taskId);
        if (configEntity == null) {
            log.info("CustomizedSpreadManager -> sendMessageByTaskId configEntity is null, ea: {}, taskId: {}", ea, taskId);
            return Result.newError(SHErrorCode.NO_DATA);
        }

        String marketingActivityId = configEntity.getMarketingActivityId();
        String marketingEventId = configEntity.getMarketingEventId();
        CustomizedSpreadSendData customizedSpreadSendData = configEntity.getExternalConfig().getCustomizedSpreadSendData();
        if (customizedSpreadSendData == null) {
            log.info("CustomizedSpreadManager -> sendMessageByTaskId customizedSpreadSendData is null, ea: {}, taskId: {}", ea, taskId);
            return Result.newError(SHErrorCode.NO_DATA);
        }

        // 查询自定义推广渠道
        String sendChannelId = customizedSpreadSendData.getCustomizedSpreadChannelId();
        CustomizedSpreadChannelEntity channelEntity = customizedSpreadChannelDAO.getById(ea, sendChannelId);
        if (channelEntity == null) {
            log.info("CustomizedSpreadManager -> sendMessageByTaskId channelEntity is null, ea: {}, customizedSpreadChannelId: {}", ea, sendChannelId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String funcApiName = channelEntity.getFuncApiName();
        String sendChannelName = channelEntity.getName();
        String sendObject = channelEntity.getSendObject();
        // sendObject以.分隔，转成List
        List<String> sendObjectList = Lists.newArrayList(sendObject.split("\\."));
        if (sendObjectList.size() != 2) {
            log.info("CustomizedSpreadManager -> sendMessageByTaskId sendObjectList size is not 2, ea: {}, sendObject: {}", ea, sendObject);
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), "sendObject error");
        }

        String apiName = sendObjectList.get(0); // apiName
        String fieldApiName = sendObjectList.get(1); //字段名

        // 查询目标人群
        List<String> marketingUserGroupIds = customizedSpreadSendData.getMarketingUserGroupIds();
        List<SendTargetData> sendTargetDataList = getSendTargetData(ea, apiName, fieldApiName, marketingUserGroupIds);

        // 物料信息处理
        String objectId = customizedSpreadSendData.getObjectId();
        Integer objectType = customizedSpreadSendData.getObjectType();
        // 内容名称
        String contentName = objectManager.getObjectName(objectId, objectType);
        // 封面地址
        String contentCover = objectManager.getObjectCover(objectId, objectType, ea);
        // 内容长链
        String contentLink = null;
        // todo

        // 内容短链
        String contentShortLink = null;
        // todo

        // 调用APL函数
        String sendTargetApiName = StringUtils.equals(MARKETING_USER, apiName) ? null : apiName;
        FunctionParam functionParam = new FunctionParam(sendTargetDataList, sendTargetApiName, contentName, contentCover, contentLink, contentShortLink);
        ExecuteCustomizeFunctionArg executeCustomizeFunctionArg = new ExecuteCustomizeFunctionArg();
        List<ExecuteCustomizeFunctionArg.Parameters> parametersList = Lists.newArrayList();
        executeCustomizeFunctionArg.setApiName(funcApiName);
        executeCustomizeFunctionArg.setParameters(parametersList);
        ExecuteCustomizeFunctionArg.Parameters parameters = new ExecuteCustomizeFunctionArg.Parameters();
        parameters.setName(CustomizeFunctionConstants.ENROLL_DATA_NAME);
        parameters.setType(CustomizeFunctionConstants.PARAMETERS_TYPE.get("Map"));
        parameters.setValue(functionParam);
        parametersList.add(parameters);
        ExecuteCustomizeFunctionResult executeCustomizeFunctionResult = customizeFunctionManager.executeCustomizeFunctionV2(GsonUtil.toJson(executeCustomizeFunctionArg), ea, SuperUserConstants.USER_ID);
        if (executeCustomizeFunctionResult == null) {
            log.info("CustomizedSpreadManager -> sendMessageByTaskId executeCustomizeFunctionResult is null, ea: {}, taskId: {}", ea, taskId);
            updateTaskStatusAndMarketingActivityStatusToFail(ea, taskId, marketingActivityId);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        if (executeCustomizeFunctionResult.getErrCode() != null && !executeCustomizeFunctionResult.isSuccess()) {
            // 函数调用异常
            updateTaskStatusAndMarketingActivityStatusToFail(ea, taskId, marketingActivityId);
            return Result.newError(executeCustomizeFunctionResult.getErrCode(), executeCustomizeFunctionResult.getErrMessage());
        }
        FunctionResult functionResult = GsonUtil.fromJson(GsonUtil.toJson(executeCustomizeFunctionResult.getResult()), FunctionResult.class);
        log.info("CustomizedSpreadManager -> sendMessageByTaskId functionResult: {}", functionResult);

        // 异步写发送明细对象
        ThreadPoolUtils.execute(() -> {
            sendTargetDataList.forEach(sendTargetData -> {
                // 写发送明细对象
                Map<String, Object> createMap = Maps.newHashMap();
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.SEND_CHANNEL.getFieldName(), sendChannelName);
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.SEND_CHANNEL_ID.getFieldName(), sendChannelId);
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.SEND_CONTENT.getFieldName(), contentName);
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.CONTENT_LINK.getFieldName(), contentLink);
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.USER_ACCOUNT.getFieldName(), sendTargetData.getReceiveAccount());
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.MARKETING_USER_ID.getFieldName(), sendTargetData.getMarketingUserId());
                if (!StringUtils.equals(MARKETING_USER, apiName)) {
                    createMap.put(CrmMarketingMessageSendRecordFieldEnum.OBJECT_API_NAME.getFieldName(), apiName);
                }
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.OBJECT_DATA_ID.getFieldName(), sendTargetData.getObjectId());
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.SEND_STATUS.getFieldName(), MarketingMessageSendRecordSendStatusEnum.WAITING.getStatus());
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.ACTUAL_SEND_TIME.getFieldName(), new Date().getTime());
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.MARKETING_ACTIVITY_ID.getFieldName(), marketingActivityId);
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.MARKETING_EVENT_ID.getFieldName(), marketingEventId);
                createMap.put(CrmMarketingMessageSendRecordFieldEnum.OWNER.getFieldName(), createBy == null ? Lists.newArrayList("-10000") : Lists.newArrayList(String.valueOf(createBy)));
                // 根据函数返回结果，组装对象数据
                if (functionResult != null) {
                    if (StringUtils.isNotEmpty(functionResult.getSendStatus())) {
                        createMap.put(CrmMarketingMessageSendRecordFieldEnum.SEND_STATUS.getFieldName(), functionResult.getSendStatus());
                    }
                    if (StringUtils.isNotEmpty(functionResult.getFailureReason())) {
                        createMap.put(CrmMarketingMessageSendRecordFieldEnum.FAILURE_REASON.getFieldName(), functionResult.getFailureReason());
                    }
                    if (StringUtils.isNotEmpty(functionResult.getSendReceiptId())) {
                        createMap.put(CrmMarketingMessageSendRecordFieldEnum.SEND_RECEIPT_ID.getFieldName(), functionResult.getSendReceiptId());
                    }
                }
                marketingMessageSendRecordObjManager.createObj(ea, createBy, createMap);
            });
        }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);

        // 更新发送任务状态和营销活动对象状态
        SendStatusEnum marketingActivityStatus = SendStatusEnum.FINISHED;
        TaskSendStatusEnum taskSendStatusEnum = TaskSendStatusEnum.SENT;
        if (functionResult != null && Objects.equals(functionResult.getSendStatus(), MarketingMessageSendRecordSendStatusEnum.SEND_FAILURE.getStatus())) {
            taskSendStatusEnum = TaskSendStatusEnum.ERROR;
            marketingActivityStatus = SendStatusEnum.FAIL;
        }
        updateTaskStatusAndMarketingActivityStatus(ea, taskId, marketingActivityId, taskSendStatusEnum, marketingActivityStatus);

        return Result.newSuccess();
    }

    private void updateTaskStatusAndMarketingActivityStatusToFail(String ea, String taskId, String marketingActivityId) {
        updateTaskStatusAndMarketingActivityStatus(ea, taskId, marketingActivityId, TaskSendStatusEnum.ERROR, SendStatusEnum.FAIL);
    }

    /**
     * 更新发送任务状态和营销活动对象状态
     * @param ea
     * @param taskId
     * @param marketingActivityId
     * @param taskSendStatusEnum
     * @param sendStatusEnum
     */
    private void updateTaskStatusAndMarketingActivityStatus(String ea, String taskId, String marketingActivityId, TaskSendStatusEnum taskSendStatusEnum, SendStatusEnum sendStatusEnum) {
        customizedSpreadSendTaskDAO.updateStatus(ea, taskId, taskSendStatusEnum.getStatus());
        ObjectData marketingActivityUpdateObject = new ObjectData();
        marketingActivityUpdateObject.put("_id", marketingActivityId);
        marketingActivityUpdateObject.put("status", String.valueOf(sendStatusEnum.getStatus()));
        crmV2Manager.updateMarketingActivityObj(ea, marketingActivityUpdateObject, SuperUserConstants.USER_ID);
    }

    /**
     * 函数传递参数
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionParam implements Serializable{
        private List<SendTargetData> sendTargetDataList;//
        private String sendTargetApiName;//发送对象apiName
        private String contentName;//内容名称
        private String contentCover;//内容封面
        private String contentUrl;//内容长链
        private String contentShortUrl;//内容短链
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionResult implements Serializable{
        /**
         * @see MarketingMessageSendRecordSendStatusEnum
         */
        private String sendStatus; //发送状态
        private String failureReason; //失败原因
        private String sendReceiptId; //发送回执id
    }

    /**
     * 获取发送对象数据
     * @param ea
     * @param apiName
     * @param fieldApiName
     * @param marketingUserGroupIds
     * @return
     */
    public List<SendTargetData> getSendTargetData(String ea, String apiName, String fieldApiName, List<String> marketingUserGroupIds) {
        // 营销用户特殊处理，它不是一个对象apiName，是虚拟的一个名称
        if (MARKETING_USER.equals(apiName)) {
            if (Objects.equals(fieldApiName, "phone")) {
                // 营销用户手机号
                List<MobileMarketingUserResult> mobileMarketingUserResults = marketingUserGroupManager.listMobileByMarketingUserGroupIds(ea, marketingUserGroupIds);
                if (CollectionUtils.isEmpty(mobileMarketingUserResults)) {
                    return Lists.newArrayList();
                }
                return mobileMarketingUserResults.stream().map(mobileMarketingUserResult -> new SendTargetData(mobileMarketingUserResult.getMobile(), null, mobileMarketingUserResult.getMarketingUserId())).collect(Collectors.toList());
            } else if (Objects.equals(fieldApiName, "email")) {
                // 营销用户邮箱
                Set<MarketingUserWithEmail> marketingUserWithEmails = marketingUserGroupManager.listEmailByMarketingUserGroupIds(ea, marketingUserGroupIds);
                if (CollectionUtils.isEmpty(marketingUserWithEmails)) {
                    return Lists.newArrayList();
                }
                return marketingUserWithEmails.stream().map(marketingUserWithEmail -> new SendTargetData(marketingUserWithEmail.getEmail(), null, marketingUserWithEmail.getMarketingUserId())).collect(Collectors.toList());
            } else {
                log.info("CustomizedSpreadManager -> getSendTargetData objFieldName is not phone or email, ea: {}, apiName: {}, fieldApiName: {}", ea, apiName, fieldApiName);
                return null;
            }
        } else {
            return getSendTargetDataByCrmObj(ea, apiName, fieldApiName, marketingUserGroupIds);
        }
    }

    /**
     * 批量获取对象指定字段数据
     * @param ea
     * @param apiName
     * @param fieldApiName
     * @param marketingUserGroupIds
     * @return
     */
    public List<SendTargetData> getSendTargetDataByCrmObj(String ea, String apiName, String fieldApiName, List<String> marketingUserGroupIds) {
        // 目标人群查询营销用户
        Set<String> marketingUserIds = marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(ea, marketingUserGroupIds);
        List<String> objectIds = Lists.newArrayList();
        if (Objects.equals(apiName, CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            // 线索对象
            objectIds = marketingUserGroupManager.doListCrmLeadIdsByMarketingUserIds(ea, marketingUserIds);
        } else if (Objects.equals(apiName, CrmObjectApiNameEnum.CUSTOMER.getName())) {
            // 客户对象
            objectIds = marketingUserGroupManager.doListCrmCustomerIdsByMarketingUserIds(ea, marketingUserIds);
        } else if (Objects.equals(apiName, CrmObjectApiNameEnum.CONTACT.getName())) {
            // 联系人对象
            objectIds = marketingUserGroupManager.doListCrmContactIdsByMarketingUserIds(ea, marketingUserIds);
        } else if (Objects.equals(apiName, CrmObjectApiNameEnum.WECHAT.getName())) {
            // 微信用户对象
            objectIds = marketingUserGroupManager.doListCrmWxUserByMarketingUserIds(ea, marketingUserIds);
        } else if (Objects.equals(apiName, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())) {
            // 企微客户对象
            objectIds = marketingUserGroupManager.doListCrmWxWorkUserIdsByMarketingUserIds(ea, marketingUserIds);
        } else if (Objects.equals(apiName, CrmObjectApiNameEnum.MEMBER.getName())) {
            // 会员对象
            objectIds = marketingUserGroupManager.doListCrmMemberIdsByMarketingUserIds(ea, marketingUserIds);
        } else {
            // 其他对象
            objectIds = marketingUserGroupManager.doListCrmCustomizeObjectIdsByMarketingUserIds(ea, marketingUserIds);
        }

        // 对象id去重
        if (CollectionUtils.isEmpty(objectIds)) {
            return Lists.newArrayList();
        }
        Set<String> objectIdSet = Sets.newHashSet(objectIds);

        // 批量查询对象数据
        List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, apiName, Lists.newArrayList(fieldApiName), Lists.newArrayList(objectIdSet));
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Lists.newArrayList();
        }

        // 封装数据，过滤掉字段值位空的数据
        return objectDataList.stream()
                .filter(objectData -> StringUtils.isNotEmpty(objectData.getString(fieldApiName)))
                .map(objectData -> new SendTargetData(objectData.getString(fieldApiName), objectData.getId(), null))
                .collect(Collectors.toList());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SendTargetData implements Serializable {
        private String receiveAccount; //接收用户账号
        private String objectId; //当发送对象为crm对象时，字段才有值
        private String marketingUserId; //当发送对象为营销用户时，字段才有值
    }

    /**
     * 创建营销活动对象
     * @param ea
     * @param fsUserId
     * @param addMarketingActivityArg
     * @return
     */
    private Result<String> createMarketingActivity(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        AddMarketingActivityArg.CustomizedSpreadArg customizedSpreadArg = addMarketingActivityArg.getCustomizedSpreadArg();
        String customizedSpreadChannelId = customizedSpreadArg.getCustomizedSpreadChannelId();
        CustomizedSpreadChannelEntity channelEntity = customizedSpreadChannelDAO.getById(ea, customizedSpreadChannelId);
        if (channelEntity == null) {
            log.info("CustomizedSpreadManager -> createMarketingActivity channelEntity is null, ea: {}, customizedSpreadChannelId: {}", ea, customizedSpreadChannelId);
            return null;
        }
        // 内容名称
        String contentName = objectManager.getObjectName(customizedSpreadArg.getObjectId(), customizedSpreadArg.getObjectType());
        PaasAddMarketingActivityArg arg = new PaasAddMarketingActivityArg();
        arg.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
        arg.setName(contentName);
        arg.setSpreadType(channelEntity.getSpreadTypeOptionValue());
        arg.setStatus(String.valueOf(SendStatusEnum.WAIT_SEND.getStatus()));
        boolean needAudit = marketingActivityAuditManager.isNeedAudit(ea);
        if (needAudit) {
            ObjectData objectData = marketingActivityAuditManager.buildObjectData(ea, addMarketingActivityArg.getMarketingActivityAuditData());
            arg.setObjectData(objectData);
        }
        Result<String> marketingActivityIdResult = marketingActivityCrmManager.addMarketingActivity(ea, fsUserId, arg);
        log.info("自定义推广营销活动创建, ea: {} arg： {} 营销活动创建结果: {}", ea, arg, marketingActivityIdResult);
        return marketingActivityIdResult;
    }

    /**
     * 创建发送任务
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    private String createSendTask(String ea, Integer fsUserId, AddMarketingActivityArg.CustomizedSpreadArg arg) {
        CustomizedSpreadSendTaskEntity taskEntity = new CustomizedSpreadSendTaskEntity();
        taskEntity.setId(UUIDUtil.getUUID());
        taskEntity.setEa(ea);
        taskEntity.setSendType(arg.getSendType());
        Date sendTime = Objects.equals(arg.getSendType(), SendTypeEnum.IMMEDIATELY.getValue()) ? new Date() : new Date(arg.getSendTime());
        taskEntity.setSendTime(sendTime);
        taskEntity.setSendStatus(TaskSendStatusEnum.WAIT.getStatus());
        taskEntity.setCustomizedSpreadChannelId(arg.getCustomizedSpreadChannelId());
        taskEntity.setCreateBy(fsUserId);
        taskEntity.setUpdateBy(fsUserId);
        customizedSpreadSendTaskDAO.insert(taskEntity);
        return taskEntity.getId();
    }

    /**
     * 保存推广配置
     * @param ea
     * @param taskId
     * @param marketingActivityId
     * @param needAudit
     * @param addMarketingActivityArg
     */
    private void createMarketingActivityExternalConfig(String ea, String taskId, String marketingActivityId, boolean needAudit, AddMarketingActivityArg addMarketingActivityArg) {
        String marketingEventId = addMarketingActivityArg.getMarketingEventId();
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = new MarketingActivityExternalConfigEntity();
        marketingActivityExternalConfigEntity.setId(UUIDUtil.getUUID());
        marketingActivityExternalConfigEntity.setEa(ea);
        marketingActivityExternalConfigEntity.setAssociateIdType(AssociateIdTypeEnum.CUSTOMIZED_SPREAD.getType());
        marketingActivityExternalConfigEntity.setAssociateId(taskId);
        marketingActivityExternalConfigEntity.setMarketingActivityId(marketingActivityId);
        marketingActivityExternalConfigEntity.setMarketingActivityType(MarketingActivityTypeEnum.ENTERPRISE.getType());
        marketingActivityExternalConfigEntity.setMarketingEventId(marketingEventId);
        marketingActivityExternalConfigEntity.setIsNeedAudit(needAudit ? "true" : "false");
        CustomizedSpreadSendData customizedSpreadSendData = BeanUtil.copy(addMarketingActivityArg.getCustomizedSpreadArg(), CustomizedSpreadSendData.class);
        marketingActivityExternalConfigEntity.setExternalConfig(new ExternalConfig(customizedSpreadSendData));
        marketingActivityExternalConfigDao.insert(marketingActivityExternalConfigEntity);
    }

    private Result<Void> checkParam(AddMarketingActivityArg.CustomizedSpreadArg arg) {
        if (arg == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        SendTypeEnum sendTypeEnum = SendTypeEnum.getByValue(arg.getSendType());
        if (sendTypeEnum == null) {
            return Result.newError(SHErrorCode.SEND_TYPE_CAN_NOT_NULL);
        }

        if (sendTypeEnum == SendTypeEnum.SCHEDULED && arg.getSendTime() == null) {
            return Result.newError(SHErrorCode.SEND_TIME_CAN_NOT_NULL);
        }

        if (CollectionUtils.isEmpty(arg.getMarketingUserGroupIds())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), "marketingUserGroupIds is null");
        }
        
        return Result.newSuccess();
    }

    @Override
    public GetMarketingActivityResult doDetailAction(String ea, Integer fsUserId, GetMarketingActivityDetailData getMarketingActivityDetailData) {
        String marketingActivityId = getMarketingActivityDetailData.getId();
        GetMarketingActivityResult getMarketingActivityResult = new GetMarketingActivityResult();
        getMarketingActivityResult.setName(getMarketingActivityDetailData.getName());
        getMarketingActivityResult.setSpreadType(getMarketingActivityDetailData.getSpreadType());
        getMarketingActivityResult.setStatus(getMarketingActivityDetailData.getStatus());
        getMarketingActivityResult.setMarketingEventId(getMarketingActivityDetailData.getMarketingEventId());
        getMarketingActivityResult.setId(getMarketingActivityDetailData.getId());
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(getMarketingActivityDetailData.getId());
        if (marketingActivityExternalConfigEntity == null) {
            log.warn("CustomizedSpreadManager -> doDetailAction marketingActivityExternalConfigEntity is null arg:{}", getMarketingActivityDetailData);
            return getMarketingActivityResult;
        }
        CustomizedSpreadSendTaskEntity taskEntity = customizedSpreadSendTaskDAO.getById(ea, marketingActivityExternalConfigEntity.getAssociateId());
        if (taskEntity == null) {
            log.warn("CustomizedSpreadManager -> doDetailAction taskEntity is null arg:{}", getMarketingActivityDetailData);
            return getMarketingActivityResult;
        }

        String customizedSpreadChannelId = taskEntity.getCustomizedSpreadChannelId();
        CustomizedSpreadChannelEntity channelEntity = customizedSpreadChannelDAO.getById(ea, customizedSpreadChannelId);
        if (channelEntity == null) {
            log.warn("CustomizedSpreadManager -> doDetailAction channelEntity is null arg:{}", getMarketingActivityDetailData);
            return getMarketingActivityResult;
        }

        getMarketingActivityResult.setSendCancelable(false);

        CustomizedSpreadDetailResult customizedSpreadDetailResult = new CustomizedSpreadDetailResult();
        getMarketingActivityResult.setCustomizedSpreadDetailResult(customizedSpreadDetailResult);
        customizedSpreadDetailResult.setTaskId(taskEntity.getId());

        MarketingMessageSendCountResult marketingMessageSendCount = noticeManager.getMarketingMessageSendCount(ea, marketingActivityId);
        customizedSpreadDetailResult.setSendCount(marketingMessageSendCount.getSendCount());
        customizedSpreadDetailResult.setSuccessCount(marketingMessageSendCount.getSuccessCount());
        customizedSpreadDetailResult.setFailCount(marketingMessageSendCount.getFailCount());

        customizedSpreadDetailResult.setChannelId(channelEntity.getId());
        customizedSpreadDetailResult.setChannelName(channelEntity.getName());

        customizedSpreadDetailResult.setSendTime(taskEntity.getSendTime().getTime());
        customizedSpreadDetailResult.setSendStatus(taskEntity.getSendStatus());
        customizedSpreadDetailResult.setSendType(taskEntity.getSendType());

        List<MarketingUserGroupData> marketingUserGroupDataList = marketingUserGroupManager.batchGetMarketingUserGroupInfo(ea, marketingActivityExternalConfigEntity.getExternalConfig().getCustomizedSpreadSendData().getMarketingUserGroupIds());
        customizedSpreadDetailResult.setMarketingUserGroupList(marketingUserGroupDataList);

        return getMarketingActivityResult;
    }

    @Override
    public Result<Boolean> doDeleteAction(String ea, Integer fsUserId, String marketingActivityId) {
        return null;
    }

    @Override
    public AddMarketingActivityResult doUpdateAction(String ea, Integer fsUserId, AddMarketingActivityArg updateMarketingActivityArg) {
        return null;
    }

    @Override
    public AddMarketingActivityArg.MarketingActivityAuditData getMarketingActivityAuditData(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        return null;
    }

    @Override
    public Result<MarketingActivityPreviewData> getPreviewData(String ea, Integer fsUserId, MarketingActivityPreviewArg arg) {
        return null;
    }
}
