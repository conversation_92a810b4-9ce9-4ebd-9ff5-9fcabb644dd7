/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.util.ObjDescribeUtil;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EmployeePromoteDetailObjManager extends AbstractObjManager {

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Override
    public String getApiName() {
        return CrmObjectApiNameEnum.EMPLOYEE_PROMOTE_DETAIL_OBJ.getName();
    }

    public String getJsonData() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/employee_promote_detail_json_data.json");
    }

    public String getJsonLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/employee_promote_detail_json_layout.json");
    }

    public String getJsonListLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/employee_promote_detail_json_list_layout.json");
    }

    public void tryUpdateCustomFieldLabel(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        ObjectDescribe objectDescribe = super.getOrCreateObjDescribe(ea);
        if (objectDescribe == null) {
            log.warn("Ea:{} have not EmployeePromoteDetailObj", ea);
            return;
        }
        //spread_user_name
        if(!objectDescribe.getFields().containsKey("spread_user_name")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"label\":\"推广人员姓名\",\"api_name\":\"spread_user_name\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"is_index_field\":false,\"status\":\"new\",\"help_text\":\"\",\"is_extend\":false}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //spread_user_id
        if(!objectDescribe.getFields().containsKey("spread_user_id")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"label\":\"推广人ID\",\"api_name\":\"spread_user_id\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"is_index_field\":false,\"status\":\"new\",\"help_text\":\"\",\"is_extend\":false}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //spread_content_id
        if(!objectDescribe.getFields().containsKey("spread_content_id")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"label\":\"推广内容ID\",\"api_name\":\"spread_content_id\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"is_index_field\":false,\"status\":\"new\",\"help_text\":\"\",\"is_extend\":false}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //wechat_employee_obj_id 企微员工
        if(!objectDescribe.getFields().containsKey("wechat_employee_obj_id")){
            // 判断查找关联的对象是否存在
            boolean existObject = crmV2Manager.isExistObject(ea, CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName());
            if (existObject) {
                // 对象存在才刷字段
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(this.getApiName());
                arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_extend\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"企微员工\",\"target_api_name\":\"WechatEmployeeObj\",\"target_related_list_name\":\"target_related_list_1Bo1s__c\",\"target_related_list_label\":\"员工推广明细\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"wechat_employee_obj_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"}");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            }
        }

        //content_category 内容分类
        if(!objectDescribe.getFields().containsKey("content_category")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"微页面\",\"value\":\"1\"},{\"label\":\"文章\",\"value\":\"2\"},{\"label\":\"产品\",\"value\":\"3\"},{\"label\":\"文件\",\"value\":\"4\"},{\"label\":\"视频\",\"value\":\"5\"},{\"label\":\"图片\",\"value\":\"6\"},{\"label\":\"海报\",\"value\":\"7\"},{\"label\":\"直播\",\"value\":\"8\"},{\"label\":\"会议\",\"value\":\"9\"},{\"label\":\"名片\",\"value\":\"10\"},{\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"label_r\":\"内容分类\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"内容分类\",\"api_name\":\"content_category\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        } else {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"微页面\",\"value\":\"1\"},{\"label\":\"文章\",\"value\":\"2\"},{\"label\":\"产品\",\"value\":\"3\"},{\"label\":\"文件\",\"value\":\"4\"},{\"label\":\"视频\",\"value\":\"5\"},{\"label\":\"图片\",\"value\":\"6\"},{\"label\":\"海报\",\"value\":\"7\"},{\"label\":\"直播\",\"value\":\"8\"},{\"label\":\"会议\",\"value\":\"9\"},{\"label\":\"名片\",\"value\":\"10\"},{\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"label_r\":\"内容分类\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"内容分类\",\"api_name\":\"content_category\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.updateCustomFieldDescribe(systemHeader, arg);
        }

        //forward_count 转发次数
        if(!objectDescribe.getFields().containsKey("forward_count")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"is_show_thousands\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"show_tag\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"转发次数\",\"font_color\":null,\"api_name\":\"forward_count\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //click_chat_num 点击私信聊天人数
        if(!objectDescribe.getFields().containsKey("click_chat_num")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"is_show_thousands\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"show_tag\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"点击私信聊天人数\",\"font_color\":null,\"api_name\":\"click_chat_num\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //click_save_phone_num 点击存入手机人数
        if(!objectDescribe.getFields().containsKey("click_save_phone_num")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"is_show_thousands\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"show_tag\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"点击存入手机人数\",\"font_color\":null,\"api_name\":\"click_save_phone_num\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //click_call_num 点击拨打电话人数
        if(!objectDescribe.getFields().containsKey("click_call_num")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"is_show_thousands\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"show_tag\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"点击拨打电话人数\",\"font_color\":null,\"api_name\":\"click_call_num\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //click_wechat_num 点击企微联系人数
        if(!objectDescribe.getFields().containsKey("click_wechat_num")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"is_show_thousands\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"show_tag\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"点击企微联系人数\",\"font_color\":null,\"api_name\":\"click_wechat_num\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //add_wechat_friends_num 企微添加好友数
        if(!objectDescribe.getFields().containsKey("add_wechat_friends_num")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"is_show_thousands\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"show_tag\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"企微添加好友数\",\"font_color\":null,\"api_name\":\"add_wechat_friends_num\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

        //add_wechat_fans_num 公众号吸粉数
        if(!objectDescribe.getFields().containsKey("add_wechat_fans_num")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(this.getApiName());
            arg.setFieldDescribe("{\"describe_api_name\":\"EmployeePromoteDetailObj\",\"default_is_expression\":false,\"is_show_thousands\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"show_tag\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"公众号吸粉数\",\"font_color\":null,\"api_name\":\"add_wechat_fans_num\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }

}
