package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.provider.entity.mail.MailLabelEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Created by zhengh on 2020/6/2.
 */
public interface MailLabelDAO {
    @Insert("INSERT INTO mail_label(id, ea, label_name, label_id, create_time, update_time) VALUES(#{entity.id}, \n"
            + "#{entity.ea}, #{entity.labelName}, #{entity.labelId}, #{entity.createTime}, #{entity.updateTime})")
    void insert(@Param("entity")MailLabelEntity entity);

    @Select("<script>SELECT * FROM mail_label WHERE ea=#{ea}\n"
            + "<if test=\"keword != null\">AND label_name LIKE CONCAT('%',#{keyword},'%')</if>"
            + "ORDER BY create_time desc"
            + "</script>")
    List<MailLabelEntity> pageQueryLabel(@Param("ea")String ea, @Param("keyword")String keyword, @Param("page") Page page);

    @Select("SELECT * FROM mail_label WHERE id=#{id}")
    MailLabelEntity getById(@Param("id")String id, @Param("ea")String ea);

    @Delete("DELETE FROM mail_label WHERE id=#{id}")
    void deleteById(@Param("id")String id, @Param("ea")String ea);

    @Update("UPDATE mail_label SET label_name=#{labelName} WHERE id=#{id}")
    void updateMailLabel(@Param("id")String id, @Param("labelName")String labelName, @Param("ea")String ea);
}
