package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.HttpClientUtil;
import com.facishare.marketing.provider.innerResult.crm.ExecuteCustomizeFunctionResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.ning.http.client.Response;
import java.io.IOException;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2020/06/12
 **/
@Component
@Slf4j
public class CustomizeFunctionManager {

    @ReloadableProperty("customize.function.url")
    private String customizeFunctionUrl;

    @Autowired
    private EIEAConverter eieaConverter;

    public Object executeCustomizeFunction(String data, String ea, Integer fsUserId) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(ea) || fsUserId == null) {
            log.warn("CustomizeFunctionManager.executeCustomizeFunction param error");
            return null;
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        String url = customizeFunctionUrl + "/v1/function/currencyFunction";
        Response response = HttpClientUtil.doPost(url, data, buildHeader(ei, fsUserId));
        String responseResult = null;
        try {
            responseResult = response.getResponseBody();
            ExecuteCustomizeFunctionResult executeCustomizeFunctionResult =  GsonUtil.getGson().fromJson(responseResult, new TypeToken<ExecuteCustomizeFunctionResult>(){}.getType());
            if (executeCustomizeFunctionResult != null && executeCustomizeFunctionResult.isSuccess()) {
                return executeCustomizeFunctionResult.getResult();
            }
        } catch (IOException e) {
            log.warn("CustomizeFunctionManager.executeCustomizeFunction error e:{}", e);
        }
        log.warn("CustomizeFunctionManager.executeCustomizeFunction responseResult null :{}", responseResult);
        return null;
    }

    public ExecuteCustomizeFunctionResult executeCustomizeFunctionV2(String data, String ea, Integer fsUserId) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(ea) || fsUserId == null) {
            log.warn("CustomizeFunctionManager -> executeCustomizeFunctionV2 param error");
            return null;
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        String url = customizeFunctionUrl + "/v1/function/currencyFunction";
        Response response = HttpClientUtil.doPost(url, data, buildHeader(ei, fsUserId));
        String responseResult = null;
        try {
            responseResult = response.getResponseBody();
            ExecuteCustomizeFunctionResult executeCustomizeFunctionResult =  GsonUtil.getGson().fromJson(responseResult, new TypeToken<ExecuteCustomizeFunctionResult>(){}.getType());
            return executeCustomizeFunctionResult;
        } catch (IOException e) {
            log.warn("CustomizeFunctionManager -> executeCustomizeFunctionV2 error e:{}", e);
        }
        return null;
    }

    public Map<String, String> buildHeader(Integer ei, Integer fsUserId) {
        Map<String, String> header = Maps.newHashMap();
        header.put("x-fs-ei", ei.toString());
        header.put("x-fs-userInfo", fsUserId.toString());
        header.put("X-fs-Enterprise-Id", ei.toString());
        header.put("X-fs-Employee-Id", fsUserId.toString());
        header.put("Content-Type", "application/json; charset=UTF-8");
        return header;
    }

}
