/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.coupon;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.publicdata.UpdatePublicArg;
import com.facishare.marketing.api.result.wxcoupon.AccountFilter;
import com.facishare.marketing.api.vo.wxcoupon.CreateWxCouponVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.CrmCustomerFieldEnum;
import com.facishare.marketing.common.enums.CrmWechatWorkExternalUserFieldEnum;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.coupon.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.EnterprseInfoDao;
import com.facishare.marketing.provider.dao.marketingplugin.WeChatCouponDAO;
import com.facishare.marketing.provider.entity.EnterpriseInfoEntity;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.facishare.marketing.provider.innerArg.crm.PaasQueryCouponArg;
import com.facishare.marketing.provider.innerArg.crm.PaasQueryUserCouponArg;
import com.facishare.marketing.provider.manager.EnterpriseInfoManager;
import com.facishare.marketing.provider.manager.FileManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.crmobjectcreator.CouponDistributionObjManager;
import com.facishare.marketing.provider.manager.metadata.PublicMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.util.ExcelUtil;
import com.facishare.marketing.provider.util.ReadExcelUtil;
import com.facishare.paas.timezone.DateUtils;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.ActionChangeOwnerArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.IncrementUpdateArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionChangeOwnerResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.enterpriserelation2.arg.ListUpstreamEasArg;
import com.fxiaoke.enterpriserelation2.arg.TenantIdCascadeArg;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.github.trace.TraceContext;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import dev.langchain4j.agent.tool.P;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PublicCouponManager {

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private MetadataControllerService metadataControllerService;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private PublicMetadataManager publicMetadataManager;

    @Autowired
    private EnterpriseRelationService enterpriseRelationService;

    @Autowired
    private EnterprseInfoDao enterprseInfoDao;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private FileManager fileManager;

    @Autowired
    private WeChatCouponDAO weChatCouponDAO;

    @Autowired
    private MetadataActionService metadataActionService;

    @ReloadableProperty("coupon.top.ea")
    private String couponTopEaList;
    @ReloadableProperty("coupon.mengniu.dev.ea")
    private String couponMengniuDevEaList;
    @ReloadableProperty("coupon.mengniu.uat.ea")
    private String couponMengniuUATEaList;

    private static final String SELECT_ALL = "ALL";

    private static final String SELECT_CONDITION = "CONDITION";

    private static final String SELECT_FIXED = "FIXED";

    private static final String IMPORT = "IMPORT";


    public InnerPage<ObjectData> listPageCoupon(String ea, Integer fsUserId, PaasQueryCouponArg couponArg) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0, couponArg.getPageSize());
        if (couponArg.getStatus() != null) {
            query.addFilter("status",OperatorConstants.EQ,Lists.newArrayList(String.valueOf(couponArg.getStatus())));
        }
        if (StringUtils.isNotBlank(couponArg.getMarketingEventId())) {
            query.addFilter("marketing_event_id",OperatorConstants.EQ,Lists.newArrayList(couponArg.getMarketingEventId()));
        }
        if (StringUtils.isNotBlank(couponArg.getStockName())) {
            query.addFilter("stock_name",OperatorConstants.EQ,Lists.newArrayList(couponArg.getStockName()));
        }
        query.addOrderByAsc("create_time",false);
        filterArg.setQuery(query);
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilterV3(ea, fsUserId, filterArg, couponArg.getPageNumber(), couponArg.getPageSize());
        return objectDataInnerPage;
    }

    public List<ObjectData> queryPublicCouponList(String ea) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0,500);
        query.addFilter("status",OperatorConstants.EQ,Lists.newArrayList(String.valueOf(CouponStatusEnum.NORMAL.getStatus())));
        filterArg.setQuery(query);
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, filterArg);
        if (totalRelationCount <= 0) {
            return objectDataList;
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, filterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return objectDataList;
    }

    public Set<String> getDownTenantIds(Set<String> srcTenantIds, String thisTenantId) {
        if (srcTenantIds.contains("-999999")) {
            return srcTenantIds;
        }
        try {
            Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(HeaderObj.newInstance(Integer.parseInt(thisTenantId), -10000), "EnterpriseRelationObj");
            if (!describe.isSuccess() || describe.getData() == null || describe.getData().getDescribe() == null) {
                return srcTenantIds;
            }
        } catch (Exception e) {
            log.warn("objectDescribeService.getDescribe is error",e);
            return srcTenantIds;
        }
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(thisTenantId));
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setSelectFields(Lists.newArrayList("_id","enterprise_account"));
        filterArg.setObjectAPIName("EnterpriseRelationObj");
        PaasQueryArg query = new PaasQueryArg(0,500);
        query.addFilter("crm_open_status",OperatorConstants.EQ,Lists.newArrayList("2"));
        filterArg.setQuery(query);
        InnerPage<ObjectData> dataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, -10000, filterArg, null, 500);
        if (null != dataInnerPage && CollectionUtils.isNotEmpty(dataInnerPage.getDataList())) {
            for (ObjectData objectData : dataInnerPage.getDataList()) {
                String enterpriseAccount = objectData.getString("enterprise_account");
                if (StringUtils.isNotEmpty(enterpriseAccount)) {
                    int ei = eieaConverter.enterpriseAccountToId(enterpriseAccount);
                    String newTenant = String.valueOf(ei);
                    if (!srcTenantIds.contains(newTenant)) {
                        srcTenantIds.add(newTenant);
                        srcTenantIds = getDownTenantIds(srcTenantIds, newTenant);
                    }
                }
            }
        }
        return srcTenantIds;
    }

    /**
     * 获取下游企业tenantId
     * @param ea
     * @param type
     * @param value
     * @return
     */
    public List<String> getTenantIds(String ea,String type,String value,String sendScope) {
        List<String> tenantIds = Lists.newArrayList();
        List<String> accountIds = Lists.newArrayList();
        if (Objects.equals(type,SELECT_ALL) || Objects.equals(type, SELECT_CONDITION)) {
            //查询所有的下游企业
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("dealer__c","secondary_dealer__c","mollercular_company__c","shop_warehouse__c"));
            if (Objects.equals(type, SELECT_CONDITION)) {
                List<Wheres> wheres = GsonUtil.getGson().fromJson(value, new TypeToken<List<Wheres>>() {
                }.getType());
                query.setWheres(wheres);
            }
            //按照对象筛选条件查询
            queryFilterArg.setQuery(query);
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id"));
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            if (totalCount <= 0) {
                return Lists.newArrayList();
            }
            int currentCount = 0;
            String lastId = null;
            while (currentCount < totalCount) {
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 500);
                if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                    break;
                }
                accountIds.addAll(objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
                int tempSize = objectDataInnerPage.getDataList().size();
                currentCount += tempSize;
                lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
            }
        } else if (Objects.equals(type, SELECT_FIXED)) {
                //直接选择客户
                List<AccountFilter> accountFilters = GsonUtil.getGson().fromJson(value, new TypeToken<List<AccountFilter>>() {
                }.getType());
                accountIds.addAll(accountFilters.stream().map(AccountFilter::getAccountId).collect(Collectors.toList()));
        } else if (Objects.equals(type, IMPORT)) {
            CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = GsonUtil.getGson().fromJson(sendScope, new TypeToken<CreateWxCouponVO.PartnerNoticeVisibilityVO>() {
            }.getType());
            accountIds = getAccountIds(ea, partnerNoticeVisibilityVO.getOuterTenantIds());
        }
        if (CollectionUtils.isEmpty(accountIds)) {
            return tenantIds;
        }
        //根据客户id 查询下游企业
        PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        queryRelation.addFilter("mapper_account_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(),accountIds);
        queryRelationFilterArg.setQuery(queryRelation);
        queryRelationFilterArg.setObjectAPIName("EnterpriseRelationObj");
        queryRelationFilterArg.setSelectFields(Lists.newArrayList("_id","dest_outer_tenant_id","enterprise_account"));
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg);
        if (totalRelationCount <= 0) {
            return Lists.newArrayList();
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            tenantIds.addAll(objectDataInnerPage.getDataList().stream().filter(objectData -> StringUtils.isNotEmpty(objectData.getString("enterprise_account"))).map(objectData -> objectData.getString("enterprise_account")).collect(Collectors.toList()));
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        if (CollectionUtils.isNotEmpty(tenantIds)) {
            Map<String, Integer> enterpriseAccountToIdMap = eieaConverter.enterpriseAccountToId(tenantIds);
            tenantIds = enterpriseAccountToIdMap.values().stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
        }
        return tenantIds;
    }

    public List<String> getOutTenantIds(String ea,String type,String value,Integer visibilityType) {
        List<String> outerTenantIds = Lists.newArrayList();
        List<String> accountIds = Lists.newArrayList();
        if (Objects.equals(type,SELECT_ALL) || Objects.equals(type, SELECT_CONDITION)) {
            //查询所有的下游企业
            if (Objects.equals(type,SELECT_ALL) && visibilityType == VisibilityEnum.RECEIVE_SCOPE.getType()) {
                return Lists.newArrayList("-999999");
            }
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            if (visibilityType == VisibilityEnum.SEND_SCOPE.getType()) {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("dealer__c","secondary_dealer__c","mollercular_company__c","shop_warehouse__c"));
            } else {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("default__c"));
            }
            if (Objects.equals(type, SELECT_CONDITION)) {
                List<Wheres> wheres = GsonUtil.getGson().fromJson(value, new TypeToken<List<Wheres>>() {
                }.getType());
                query.setWheres(wheres);
            }
            //按照对象筛选条件查询
            queryFilterArg.setQuery(query);
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id"));
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            if (totalCount <= 0) {
                return Lists.newArrayList();
            }
            int currentCount = 0;
            String lastId = null;
            while (currentCount < totalCount) {
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 1000);
                if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                    break;
                }
                accountIds.addAll(objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
                int tempSize = objectDataInnerPage.getDataList().size();
                currentCount += tempSize;
                lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
            }
        } else if (Objects.equals(type, SELECT_FIXED)) {
            //直接选择客户
            List<AccountFilter> accountFilters = GsonUtil.getGson().fromJson(value, new TypeToken<List<AccountFilter>>() {
            }.getType());
            accountIds.addAll(accountFilters.stream().map(AccountFilter::getAccountId).collect(Collectors.toList()));
        }
        //根据客户id 查询下游企业
        PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        if (accountIds != null) {
            queryRelation.addFilter("mapper_account_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(),accountIds);
        }
        queryRelationFilterArg.setQuery(queryRelation);
        queryRelationFilterArg.setObjectAPIName("EnterpriseRelationObj");
        queryRelationFilterArg.setSelectFields(Lists.newArrayList("_id","dest_outer_tenant_id"));
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg);
        if (totalRelationCount <= 0) {
            return Lists.newArrayList();
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            outerTenantIds.addAll(objectDataInnerPage.getDataList().stream().map(objectData -> objectData.getString("dest_outer_tenant_id")).collect(Collectors.toList()));
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return outerTenantIds;
    }

    //修改对象可见范围
    public void updateObjDataRange(String ea,String objectId,Set<String> downTenantIds,String describeApiName){
        if (StringUtils.isBlank(objectId)) {
            return;
        }
        UpdatePublicArg arg = new UpdatePublicArg();
        arg.setEa(ea);
        arg.setDescribeApiName(describeApiName);
        Map<String, Set<String>> downTenantIdsMap = new HashMap<>();
        if (downTenantIds.contains("-999999")) {
            downTenantIdsMap.put(objectId, Sets.newHashSet("-99999"));
        } else {
            downTenantIdsMap.put(objectId,downTenantIds);
        }
        arg.setDownstreamTenants(downTenantIdsMap);
        //更新公共对象可见范围
        publicMetadataManager.updatePublicDataRange(arg);
    }

    public int queryCouponInstCount(Integer tenantId,String couponId,String status) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0, 1);
        if (StringUtils.isNotEmpty(couponId)) {
            query.addFilter("coupon_id",OperatorConstants.EQ, Lists.newArrayList(couponId));
        }
        if (StringUtils.isNotEmpty(status)) {
            query.addFilter("use_status",OperatorConstants.EQ,Lists.newArrayList(status));
        }
        filterArg.setQuery(query);
        return crmV2Manager.countCrmObjectByFilterV3(eieaConverter.enterpriseIdToAccount(tenantId), -10000, filterArg);
    }

    //分页查询领取优惠券实例
    public InnerPage<ObjectData> queryPageCouponInst(String ea, Integer fsUserId, PaasQueryUserCouponArg arg) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0, arg.getPageSize());
        if (StringUtils.isNotEmpty(arg.getMemberId())) {
            query.addFilter("member_id", OperatorConstants.EQ,Lists.newArrayList(arg.getMemberId()));
        }
        if (StringUtils.isNotEmpty(arg.getAccountId())) {
            query.addFilter("account_id",OperatorConstants.EQ,Lists.newArrayList(arg.getAccountId()));
        }
        if (StringUtils.isNotEmpty(arg.getPartnerId())) {
            query.addFilter("partner_id_backup",OperatorConstants.EQ,Lists.newArrayList(arg.getPartnerId()));
        }
        if (StringUtils.isNotEmpty(arg.getCouponId())) {
            query.addFilter("coupon_id",OperatorConstants.EQ,Lists.newArrayList(arg.getCouponId()));
        }
        if (StringUtils.isNotEmpty(arg.getStatus())) {
            if (Objects.equals(arg.getStatus(), CouponInstStatusEnum.USE.getStatus())) {
                query.addFilter("use_status",OperatorConstants.EQ,Lists.newArrayList("UNUSED"));
            } else if (Objects.equals(arg.getStatus(), CouponInstStatusEnum.CONFIRM.getStatus())){
                query.addFilter("use_status",OperatorConstants.EQ,Lists.newArrayList("USED"));
            }
        }
        filterArg.setQuery(query);
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilterV3(ea, fsUserId, filterArg, arg.getPageNumber(), arg.getPageSize());
        return objectDataInnerPage;
    }

    //查询领取优惠券实例列表
    public List<ObjectData> queryPublicCouponInstList(String ea,String accountId) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0,500);
        if (StringUtils.isNotEmpty(accountId)) {
            query.addFilter("account_id",OperatorConstants.EQ,Lists.newArrayList(accountId));
        }
        filterArg.setQuery(query);
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, filterArg);
        if (totalRelationCount <= 0) {
            return objectDataList;
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, filterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return objectDataList;
    }

    public List<ObjectData> queryAllPublicCouponInst(String ea,PaasQueryUserCouponArg arg) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0,500);
        if (StringUtils.isNotEmpty(arg.getMemberId())) {
            query.addFilter("member_id", OperatorConstants.EQ,Lists.newArrayList(arg.getMemberId()));
        }
        if (StringUtils.isNotEmpty(arg.getAccountId())) {
            query.addFilter("account_id",OperatorConstants.EQ,Lists.newArrayList(arg.getAccountId()));
        }
        if (StringUtils.isNotEmpty(arg.getPartnerId())) {
            query.addFilter("partner_id_backup",OperatorConstants.EQ,Lists.newArrayList(arg.getPartnerId()));
        }
        if (StringUtils.isNotEmpty(arg.getCouponId())) {
            query.addFilter("coupon_id",OperatorConstants.EQ,Lists.newArrayList(arg.getCouponId()));
        }
        if (StringUtils.isNotEmpty(arg.getStatus())) {
            if (Objects.equals(arg.getStatus(), CouponInstStatusEnum.USE.getStatus())) {
                query.addFilter("use_status",OperatorConstants.EQ,Lists.newArrayList("UNUSED"));
            } else if (Objects.equals(arg.getStatus(), CouponInstStatusEnum.CONFIRM.getStatus())){
                query.addFilter("use_status",OperatorConstants.EQ,Lists.newArrayList("USED"));
            }
        }
        filterArg.setQuery(query);
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, filterArg);
        if (totalRelationCount <= 0) {
            return objectDataList;
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, filterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return objectDataList;
    }

    //获取当前领取ea -> 直属下发企业ea
    public Set<String> getUpStreamTenantIds(Set<String> upTenantIds, Integer tenantId,Integer upCouponTenantId) {
        TenantIdCascadeArg arg = new TenantIdCascadeArg();
        arg.setTenantId(tenantId);
        RestResult<Set<Integer>> allUpstreamTenantIdsResult = enterpriseRelationService.listAllUpstreamTenantIds(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(tenantId), arg);
        // TODO: 2023/11/16 去掉层级
        if (!allUpstreamTenantIdsResult.isSuccess() || CollectionUtils.isEmpty(allUpstreamTenantIdsResult.getData())) {
            return upTenantIds;
        }
        Set<Integer> tenantIdsResultData = allUpstreamTenantIdsResult.getData();
        tenantIdsResultData.forEach(upTenantId ->{
            if (Objects.equals(upTenantId,upCouponTenantId)) {
                upTenantIds.add(String.valueOf(upTenantId));
                return;
            }
            TenantIdCascadeArg upArg = new TenantIdCascadeArg();
            arg.setTenantId(tenantId);
            arg.setIsCascade(true);
            RestResult<Set<Integer>> allCascadeUpstreamTenantIdsResult = enterpriseRelationService.listAllUpstreamTenantIds(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(tenantId), upArg);
            if (allCascadeUpstreamTenantIdsResult.isSuccess() && CollectionUtils.isNotEmpty(allCascadeUpstreamTenantIdsResult.getData())) {
                if (allCascadeUpstreamTenantIdsResult.getData().contains(upCouponTenantId)) {
                    upTenantIds.add(String.valueOf(upTenantId));
                }
            }
            getUpStreamTenantIds(upTenantIds,upTenantId,upCouponTenantId);
        });
        return upTenantIds;
    }

    //查询直属下发上游
    // TODO: 2023/11/16  考虑直接找所有上级
    public Set<Integer> getUpDirectTenantIds(Set<Integer> directTenantIds, Integer tenantId, List<String> sendScopeTenantIds) {
        //如果领取的上游企业是发送范围内,则直接加入
        if (sendScopeTenantIds.contains(String.valueOf(tenantId))) {
            directTenantIds.add(tenantId);
        }
        TenantIdCascadeArg arg = new TenantIdCascadeArg();
        arg.setTenantId(tenantId);
        arg.setIsCascade(true);
        RestResult<Set<Integer>> allUpstreamTenantIdsResult = enterpriseRelationService.listAllUpstreamTenantIds(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(tenantId), arg);
        if (!allUpstreamTenantIdsResult.isSuccess() || CollectionUtils.isEmpty(allUpstreamTenantIdsResult.getData())) {
            return directTenantIds;
        }
        Set<Integer> tenantIdsResultData = allUpstreamTenantIdsResult.getData();
        for (Integer upTenantId : tenantIdsResultData) {
            //如果上游直接是直属上游,则将其添加到集合中
            if (sendScopeTenantIds.contains(String.valueOf(upTenantId))) {
                directTenantIds.add(upTenantId);
            }
        }
        return directTenantIds;
    }

    //选择一个可领取的直属下发上游
    public Integer getEnableReceiveTenantId(Set<Integer> upDirectTenantIds,String couponId,Integer dealerCount,Set<String> range) {
        Integer tenantId = null;
        for (Integer upDirectTenantId : upDirectTenantIds) {
            int count = this.queryCouponInstCount(upDirectTenantId,couponId,null);
            if (count >= dealerCount) {
                continue;
            }
            if (!range.contains(String.valueOf(upDirectTenantId))) {
                continue;
            }
            tenantId = upDirectTenantId;
            break;
        }
       return tenantId;
    }

    //查询单人领取数量
    public int queryAccountIdCouponInstCount(Integer tenantId,String couponId,String accountId,String partnerId) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0, 1);
        if (StringUtils.isNotEmpty(couponId)) {
            query.addFilter("coupon_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(couponId));
        }
        if (StringUtils.isNotEmpty(accountId)) {
            query.addFilter("account_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(accountId));
        }
        if (StringUtils.isNotEmpty(partnerId)) {
            query.addFilter("partner_id_backup",PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(partnerId));
        }
        filterArg.setQuery(query);
        return crmV2Manager.countCrmObjectByFilterV3(eieaConverter.enterpriseIdToAccount(tenantId), -10000, filterArg);
    }

    //添加创建优惠券ea 的所有上游
    public void getCreateCouponAllUpTenantIds(String ea,Set<String> upStreamTenantIds) {
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        //添加发券1端
        upStreamTenantIds.add(String.valueOf(tenantId));
        TenantIdCascadeArg arg = new TenantIdCascadeArg();
        arg.setTenantId(tenantId);
        arg.setIsCascade(true);
        RestResult<Set<Integer>> allUpstreamTenantIdsResult = enterpriseRelationService.listAllUpstreamTenantIds(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(tenantId), arg);
        if (allUpstreamTenantIdsResult.isSuccess() && CollectionUtils.isNotEmpty(allUpstreamTenantIdsResult.getData())) {
            for (Integer upstreamTenantIdsResultDatum : allUpstreamTenantIdsResult.getData()) {
                upStreamTenantIds.add(String.valueOf(upstreamTenantIdsResultDatum));
            }
        }
    }

    public Map<String, String> queryBelongCompanyName(Set<String> eas) {
        Map<String,String> companyNameMap = Maps.newHashMap();
        List<String> eaList = Lists.newArrayList();
        eaList.addAll(eas);
        BatchGetEnterpriseDataArg batchArg = new BatchGetEnterpriseDataArg();
        batchArg.setEnterpriseAccounts(eaList);
        BatchGetEnterpriseDataResult batchGetEnterpriseDataResult = enterpriseEditionService.batchGetEnterpriseData(batchArg);
        if (CollectionUtils.isEmpty(batchGetEnterpriseDataResult.getEnterpriseDatas())) {
            return companyNameMap;
        }
        companyNameMap = batchGetEnterpriseDataResult.getEnterpriseDatas().stream().collect(Collectors.toMap(EnterpriseData::getEnterpriseAccount, EnterpriseData::getEnterpriseName));
        return companyNameMap;
    }

    public Integer queryParticipateTenantCount(String ea,String couponId) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0, 1);
        if (StringUtils.isNotEmpty(couponId)) {
            query.addFilter("coupon_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(couponId));
        }
        query.addFilter("add_coupon_activity",PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("1"));
        filterArg.setQuery(query);
        return crmV2Manager.countCrmObjectByFilterV3(ea, -10000, filterArg);
    }

    public Set<String> getAllUpStreamTenantIds(Integer tenantId) {
        Set<String> allUpStreamTenantIds = Sets.newHashSet();
        TenantIdCascadeArg arg = new TenantIdCascadeArg();
        arg.setTenantId(tenantId);
        arg.setIsCascade(true);
        RestResult<Set<Integer>> allUpstreamTenantIdsResult = enterpriseRelationService.listAllUpstreamTenantIds(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(tenantId), arg);
        if (!allUpstreamTenantIdsResult.isSuccess() || CollectionUtils.isEmpty(allUpstreamTenantIdsResult.getData())) {
            return allUpStreamTenantIds;
        }
        allUpStreamTenantIds = allUpstreamTenantIdsResult.getData().stream().map(String::valueOf).collect(Collectors.toSet());
        return allUpStreamTenantIds;
    }

    public List<String> getStoreAccountIds(String ea, String type, String value, int visibilityType) {
        List<String> accountIds = Lists.newArrayList();
        if (Objects.equals(type,SELECT_ALL) || Objects.equals(type, SELECT_CONDITION)) {
            //查询所有的下游企业
            if (Objects.equals(type,SELECT_ALL) && visibilityType == VisibilityEnum.RECEIVE_SCOPE.getType()) {
                return Lists.newArrayList("-999999");
            }
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            if (visibilityType == VisibilityEnum.SEND_SCOPE.getType()) {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("dealer__c","secondary_dealer__c","mollercular_company__c","shop_warehouse__c"));
            } else {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("default__c"));
            }
            if (Objects.equals(type, SELECT_CONDITION)) {
                List<Wheres> wheres = GsonUtil.getGson().fromJson(value, new TypeToken<List<Wheres>>() {
                }.getType());
                query.setWheres(wheres);
            }
            //按照对象筛选条件查询
            queryFilterArg.setQuery(query);
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id"));
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            if (totalCount <= 0) {
                return Lists.newArrayList();
            }
            int currentCount = 0;
            String lastId = null;
            while (currentCount < totalCount) {
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 1000);
                if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                    break;
                }
                accountIds.addAll(objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
                int tempSize = objectDataInnerPage.getDataList().size();
                currentCount += tempSize;
                lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
            }
        } else if (Objects.equals(type, SELECT_FIXED)) {
            //直接选择客户
            List<AccountFilter> accountFilters = GsonUtil.getGson().fromJson(value, new TypeToken<List<AccountFilter>>() {
            }.getType());
            accountIds.addAll(accountFilters.stream().map(AccountFilter::getAccountId).collect(Collectors.toList()));
        }
        return accountIds;
    }

    public boolean queryCountCouponDistribution(String ea, String couponId) {
        PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0,1);
        query.addFilter("coupon_id",PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList(couponId));
        query.addFilter("add_coupon_activity",PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList(String.valueOf(SendDownStatusEnum.SEND.getType())));
        paasFilterArg.setQuery(query);
        paasFilterArg.setSelectFields(Lists.newArrayList("_id"));
        paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName());
        int count = crmV2Manager.countCrmObjectByFilterV3(ea, -10000, paasFilterArg);
        return count > 0;
    }

    public int getStoreAccountIdsCount(String ea, String type, String value, int visibilityType) {
        int countStore = 0;
        if (Objects.equals(type,SELECT_ALL) || Objects.equals(type, SELECT_CONDITION)) {
            //查询所有的下游企业
            if (Objects.equals(type,SELECT_ALL) && visibilityType == VisibilityEnum.RECEIVE_SCOPE.getType()) {
                countStore = 9999;
                return countStore;
            }
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            if (visibilityType == VisibilityEnum.SEND_SCOPE.getType()) {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("dealer__c","secondary_dealer__c","mollercular_company__c","shop_warehouse__c"));
            } else {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("default__c"));
            }
            if (Objects.equals(type, SELECT_CONDITION)) {
                List<Wheres> wheres = GsonUtil.getGson().fromJson(value, new TypeToken<List<Wheres>>() {
                }.getType());
                query.setWheres(wheres);
            }
            //按照对象筛选条件查询
            queryFilterArg.setQuery(query);
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id"));
            countStore = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        } else if (Objects.equals(type, SELECT_FIXED)) {
            List<String> accountIds = Lists.newArrayList();
            //直接选择客户
            List<AccountFilter> accountFilters = GsonUtil.getGson().fromJson(value, new TypeToken<List<AccountFilter>>() {
            }.getType());
            accountIds.addAll(accountFilters.stream().map(AccountFilter::getAccountId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(accountIds)) {
                countStore = accountIds.size();
            }
        }
        return countStore;
    }

    public void importSendScope(String ea, String couponId, String ext, String partnerNoticeVisibilityVOFile) {
        if (StringUtils.isEmpty(partnerNoticeVisibilityVOFile)) {
            log.warn("PublicCouponManager.importSendScope partnerNoticeVisibilityVOFile is empty arg:{}", partnerNoticeVisibilityVOFile);
            return;
        }
        String filePath = partnerNoticeVisibilityVOFile +"."+ ext;
        //解析excel文件
        //byte[] excelByte = fileV2Manager.downloadAFile(filePath,ea);
        byte[] excelByte = fileV2Manager.downloadByStream(ea,-10000,"YXT",filePath,null,ext);
        if (excelByte == null) {
            log.warn("PublicCouponManager.importSendScope excelByte arg:{}", partnerNoticeVisibilityVOFile);
            return;
        }
        List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(filePath, excelByte);
        if (CollectionUtils.isEmpty(excelData)) {
            log.warn("PublicCouponManager.importSendScope excelData is empty arg:{}", partnerNoticeVisibilityVOFile);
            return;
        }
        //获取第一行第一列的表头apiName
        String apiNameSheet = excelData.get(0)[0];
        //解析出apiName 格式: 客户编号(account_no)
        String apiName = extractAccountNo(apiNameSheet);
        if (apiName == null) {
            log.warn("PublicCouponManager.importSendScope apiName is null arg:{}", partnerNoticeVisibilityVOFile);
            return;
        }

        //获取第一列除表头外的数据
        List<String> dataList = Lists.newArrayList();
        for (int i = 1; i < excelData.size(); i++) {
            String value = excelData.get(i)[0];
            if (StringUtils.isNotEmpty(value)) {     //过滤空值
                dataList.add(value);
            }
        }
        List<ObjectData> customerList = getSendCustomerList(ea, partnerNoticeVisibilityVOFile, apiName, dataList);
        //将customerList转换为Map,key为apiName,value为List<ObjectData>
        Map<String, List<ObjectData>> customerMap = customerList.stream().collect(Collectors.groupingBy(objectData -> objectData.getString(apiName)));
        //将excelData的apiName 列的每行数据与customerMap的value进行匹配,匹配到有且仅有一条数据,则将该行的第二列数据设置为"导入成功",否则设置为"导入失败"
        List<String> sendScopeList = Lists.newArrayList();
        //如果没有第二列数据,则新增第二列数据,并设置第二列的表头为"导入结果"
        List<String[]> newExcelData = Lists.newArrayList();
        //设置第一列表头 apiName, 第二列表头 "导入结果"
        String[] header = new String[2];
        header[0] = apiNameSheet;
        header[1] = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_804);
        newExcelData.add(header);
        for (int i = 1; i < excelData.size(); i++) {
            String customerId = excelData.get(i)[0];
            if (StringUtils.isEmpty(customerId)) {
                //设置该行数据
                String[] row = new String[2];
                row[0] = "";
                row[1] = "";
                newExcelData.add(row);
                continue;
            }
            String result = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_816);
            if (customerMap.containsKey(customerId)) {
                List<ObjectData> objectDataList = customerMap.get(customerId);
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    List<String> accountIds = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
                    sendScopeList.addAll(accountIds);
                    result = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_822);
                }
            }
            //将customerId作为每行的第一列数据,result作为每行的第二列数据
            String[] row = new String[2];
            row[0] = customerId;
            row[1] = result;
            newExcelData.add(row);
        }
        //写入excel文件
        String formattedDate =  new SimpleDateFormat("yyyyMMdd").format(new Date());
        String fileName = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_833) + formattedDate + "."+ext;
        byte[] bytes = ReadExcelUtil.writeExcelData(newExcelData, fileName);
        if (bytes == null) {
            log.warn("PublicCouponManager.importSendScope writeExcelByApathAndData bytes is null arg:{}", partnerNoticeVisibilityVOFile);
            return;
        }
        String path = fileManager.uploadFileToAWarehouse(ext,bytes);
        if (path == null) {
            log.warn("PublicCouponManager.importSendScope uploadAFile ossUrl is null arg:{}", partnerNoticeVisibilityVOFile);
            return;
        }
        //获取下载地址
        String downloadFileUrl = fileV2Manager.getDownloadFileUrl(ea, path, fileName);
        List<String> outTenantIds = getSendScopeOutTenantIds(ea, sendScopeList);
        //更新优惠券wechat_coupon 发送范围字段
        WechatCouponEntity wechatCouponEntity = new WechatCouponEntity();
        wechatCouponEntity.setId(couponId);
        CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = new CreateWxCouponVO.PartnerNoticeVisibilityVO();
        partnerNoticeVisibilityVO.setOuterTenantIds(outTenantIds);
        wechatCouponEntity.setSendScope(GsonUtil.getGson().toJson(partnerNoticeVisibilityVO));
        wechatCouponEntity.setSendScopeImportResult(downloadFileUrl);
        weChatCouponDAO.updateCouponInfo(wechatCouponEntity);
    }
    
    private List<ObjectData> getSendCustomerList(String ea, String partnerNoticeVisibilityVOFile, String apiName, List<String> dataList) {
        List<ObjectData> customerList = Lists.newArrayList();
        //根据获取的dataList查询客户对象
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("dealer__c","secondary_dealer__c","mollercular_company__c","shop_warehouse__c"));
        query.addFilter(apiName,PaasAndCrmOperatorEnum.IN.getCrmOperator(), dataList);
        queryFilterArg.setQuery(query);
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", apiName));
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            log.warn("PublicCouponManager.importSendScope customer not found arg:{}", partnerNoticeVisibilityVOFile);
            return customerList;
        }
        int currentCount = 0;
        String lastId = null;
        while (currentCount < totalCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 1000);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            customerList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentCount += tempSize;
            lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return customerList;
    }

    public void importStoreScope(String ea, String couponId, String ext, String storeReceiveVisibilityVOFile) {
        if (StringUtils.isEmpty(storeReceiveVisibilityVOFile)) {
            log.warn("PublicCouponManager.importStoreScope storeReceiveVisibilityVOFile is empty arg:{}", storeReceiveVisibilityVOFile);
            return;
        }
        //解析excel文件
        String filePath = storeReceiveVisibilityVOFile +"."+ ext;
        //解析excel文件
        byte[] excelByte = fileV2Manager.downloadByStream(ea,-10000,"YXT",filePath,null,ext);
        //byte[] excelByte = fileV2Manager.downloadAFile(filePath,ea);
        if (excelByte == null) {
            log.warn("PublicCouponManager.importStoreScope excelByte arg:{}", storeReceiveVisibilityVOFile);
            return;
        }
        List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(filePath, excelByte);
        if (CollectionUtils.isEmpty(excelData)) {
            log.warn("PublicCouponManager.importStoreScope excelData is empty arg:{}", storeReceiveVisibilityVOFile);
            return;
        }
        //获取第一行第一列的表头apiName
        String apiNameSheet = excelData.get(0)[0];
        //解析出apiName 格式: 客户编号(account_no)
        String apiName = extractAccountNo(apiNameSheet);
        if (apiName == null) {
            log.warn("PublicCouponManager.importStoreScope apiName is null arg:{}", storeReceiveVisibilityVOFile);
            return;
        }
        //获取第一列除表头外的数据
        List<String> dataList = Lists.newArrayList();
        for (int i = 1; i < excelData.size(); i++) {
            String value = excelData.get(i)[0];
            if (StringUtils.isNotEmpty(value)) {     //过滤空值
                dataList.add(value);
            }
        }
        List<ObjectData> customerList = getStoreCustomerList(ea, storeReceiveVisibilityVOFile, apiName, dataList);
        //将customerList转换为Map,key为apiName,value为List<ObjectData>
        Map<String, List<ObjectData>> customerMap = customerList.stream().collect(Collectors.groupingBy(objectData -> objectData.getString(apiName)));
        //将excelData的apiName 列的每行数据与customerMap的value进行匹配,匹配到有且仅有一条数据,则将该行的第二列数据设置为"导入成功",否则设置为"导入失败"
        List<String> storeScopeList = Lists.newArrayList();
        List<String[]> newExcelData = Lists.newArrayList();
        //设置第一列表头 apiName, 第二列表头 "导入结果"
        String[] header = new String[2];
        header[0] = apiNameSheet;
        header[1] = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_804);
        newExcelData.add(header);
        for (int i = 1; i < excelData.size(); i++) {
            String customerId = excelData.get(i)[0];
            if (StringUtils.isEmpty(customerId)) {
                //设置该行数据
                String[] row = new String[2];
                row[0] = "";
                row[1] = "";
                newExcelData.add(row);
                continue;
            }
            String result = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_816);
            if (customerMap.containsKey(customerId)) {
                List<ObjectData> objectDataList = customerMap.get(customerId);
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    List<String> accountIds = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
                    storeScopeList.addAll(accountIds);
                    result = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_822);
                }
            }
            //将customerId作为每行的第一列数据,result作为每行的第二列数据
            String[] row = new String[2];
            row[0] = customerId;
            row[1] = result;
            newExcelData.add(row);
        }
        //写入excel文件
        String formattedDate =  new SimpleDateFormat("yyyyMMdd").format(new Date());
        String fileName = I18nUtil.get(I18nKeyEnum.MARK_COUPON_PUBLICCOUPONMANAGER_960) + formattedDate + "."+ext;
        byte[] bytes = ReadExcelUtil.writeExcelData(newExcelData, fileName);
        if (bytes == null) {
            log.warn("PublicCouponManager.importStoreScope writeExcelByApathAndData bytes is null arg:{}", storeReceiveVisibilityVOFile);
            return;
        }
        String path = fileManager.uploadFileToAWarehouse(ext,bytes);
        if (path == null) {
            log.warn("PublicCouponManager.importStoreScope uploadAFile ossUrl is null arg:{}", storeReceiveVisibilityVOFile);
            return;
        }
        //获取下载地址
        String downloadFileUrl = fileV2Manager.getDownloadFileUrl(ea, path, fileName);
        //更新优惠券wechat_coupon 领取门店范围字段
        WechatCouponEntity wechatCouponEntity = new WechatCouponEntity();
        wechatCouponEntity.setId(couponId);
        CreateWxCouponVO.StoreVisibilityVO storeVisibilityVO = new CreateWxCouponVO.StoreVisibilityVO();
        storeVisibilityVO.setAccountIds(storeScopeList);
        wechatCouponEntity.setStoreScopeImportResult(downloadFileUrl);
        wechatCouponEntity.setStoreScope(GsonUtil.getGson().toJson(storeVisibilityVO));
        weChatCouponDAO.updateCouponInfo(wechatCouponEntity);
    }


    private List<ObjectData> getStoreCustomerList(String ea, String storeReceiveVisibilityVOFile, String apiName, List<String> dataList) {
        List<ObjectData> customerList = Lists.newArrayList();
        //根据获取的dataList查询客户对象
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("default__c"));
        query.addFilter(apiName,PaasAndCrmOperatorEnum.IN.getCrmOperator(), dataList);
        queryFilterArg.setQuery(query);
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", apiName));
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            log.warn("PublicCouponManager.importStoreScope customer not found arg:{}", storeReceiveVisibilityVOFile);
            return customerList;
        }
        int currentCount = 0;
        String lastId = null;
        while (currentCount < totalCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 1000);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            customerList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentCount += tempSize;
            lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return customerList;
    }

    public List<String> getSendScopeOutTenantIds(String ea, List<String> accountIds) {
        List<String> outerTenantIds = Lists.newArrayList();
        //根据客户id 查询下游企业
        PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        if (accountIds != null) {
            queryRelation.addFilter("mapper_account_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(),accountIds);
        }
        queryRelationFilterArg.setQuery(queryRelation);
        queryRelationFilterArg.setObjectAPIName("EnterpriseRelationObj");
        queryRelationFilterArg.setSelectFields(Lists.newArrayList("_id","dest_outer_tenant_id"));
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg);
        if (totalRelationCount <= 0) {
            return Lists.newArrayList();
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            outerTenantIds.addAll(objectDataInnerPage.getDataList().stream().map(objectData -> objectData.getString("dest_outer_tenant_id")).collect(Collectors.toList()));
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return outerTenantIds;
    }

    public List<String> getAccountIds(String ea,List<String> outTenantIds) {
        if (CollectionUtils.isEmpty(outTenantIds)) {
            return Lists.newArrayList();
        }
        List<String> accountIds = Lists.newArrayList();
        //根据下游企业id 查询客户
        PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        queryRelation.addFilter("dest_outer_tenant_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(),outTenantIds);
        queryRelationFilterArg.setQuery(queryRelation);
        queryRelationFilterArg.setObjectAPIName("EnterpriseRelationObj");
        queryRelationFilterArg.setSelectFields(Lists.newArrayList("_id","mapper_account_id"));
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg);
        if (totalRelationCount <= 0) {
            return Lists.newArrayList();
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            accountIds.addAll(objectDataInnerPage.getDataList().stream().map(objectData -> objectData.getString("mapper_account_id")).collect(Collectors.toList()));
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return accountIds;
    }

    public boolean checkImportScopeResult(String ea, String importResultUrl) {
        boolean result = true;
        byte[] bytes = fileV2Manager.getByteDataByUrl(importResultUrl);
        List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(importResultUrl, bytes);
        if (CollectionUtils.isEmpty(excelData)) {
            log.warn("PublicCouponManager.checkImportScopeResult excelData is empty arg:{}", importResultUrl);
            return result;
        }
        //获取第二列除表头外的数据
        for (int i = 1; i < excelData.size(); i++) {
            String value = excelData.get(i)[1];
            if (StringUtils.isNotEmpty(value) && !"导入成功".equals(value)) {
                result = false;
                break;
            }
        }
        return result;
    }

    public static String extractAccountNo(String inputString) {
        // 定义正则表达式模式，兼容中英文括号
        Pattern pattern = Pattern.compile("[\\(\\uFF08](.*?)[\\)\\uFF09]");
        Matcher matcher = pattern.matcher(inputString);
        // 查找匹配的内容
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    public List<ObjectData> queryPublicCouponByEa(String ea) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        //先查询未过期的优惠券方案
        PaasQueryFilterArg filterPlanArg = new PaasQueryFilterArg();
        filterPlanArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
        filterPlanArg.setSelectFields(Lists.newArrayList("_id"));
        PaasQueryArg planQuery = new PaasQueryArg(0,1);
        planQuery.addFilter("end_date",PaasAndCrmOperatorEnum.GTE.getPaasOperator(),Lists.newArrayList(String.valueOf(DateUtil.now().getTime())));
        planQuery.addFilter("start_date",PaasAndCrmOperatorEnum.LTE.getPaasOperator(),Lists.newArrayList(String.valueOf(DateUtil.now().getTime())));
        filterPlanArg.setQuery(planQuery);
        List<ObjectData> planObjectDataList = this.queryObjectByPaasFilter(ea, filterPlanArg);
        if (CollectionUtils.isEmpty(planObjectDataList)) {
            return objectDataList;
        }
        List<String> planIds = planObjectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        //再查询未删除的优惠券
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0,500);
        query.addFilter("status",OperatorConstants.EQ,Lists.newArrayList(String.valueOf(CouponStatusEnum.NORMAL.getStatus())));
        query.addFilter("coupon_plan_id",OperatorConstants.IN,planIds);
        filterArg.setQuery(query);
        filterArg.setSelectFields(Lists.newArrayList("_id"));
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, filterArg);
        if (totalRelationCount <= 0) {
            return objectDataList;
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, filterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        return objectDataList;
    }

    public void handleCouponReceiveRangeChange(String ea, String accountId, ObjectData objectData,String triggerType) {
        //查询当前ea 创建的优惠券
        List<WechatCouponEntity> wechatCouponEntities = new ArrayList<>();
        List<ObjectData> objectDataList = this.queryPublicCouponByEa(ea);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        List<String> couponIds = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        //将couponIds 拆成 500个一组
        List<List<String>> couponIdsList = Lists.partition(couponIds, 500);
        for (List<String> couponIdsSubList : couponIdsList) {
            List<WechatCouponEntity> wechatCouponEntitiesSubList = weChatCouponDAO.queryByCouponObjectIds(couponIdsSubList);
            if (CollectionUtils.isEmpty(wechatCouponEntitiesSubList)) {
                continue;
            }
            wechatCouponEntities.addAll(wechatCouponEntitiesSubList);
        }
        if (CollectionUtils.isEmpty(wechatCouponEntities)) {
            return;
        }
        ThreadPoolUtils.executeWithTraceContext(() -> {
            //进行判断是否有新增或修改客户对象
            for (WechatCouponEntity wechatCouponEntity : wechatCouponEntities) {
                try {
                    String receiveScope = wechatCouponEntity.getReceiveScope();
                    String storeScope = wechatCouponEntity.getStoreScope();
                    if (StringUtils.isEmpty(receiveScope) || StringUtils.isEmpty(storeScope)) {
                        continue;
                    }
                    CreateWxCouponVO.StoreReceiveVisibilityVO storeReceiveVisibilityVO = GsonUtil.getGson().fromJson(wechatCouponEntity.getReceiveScope(), CreateWxCouponVO.StoreReceiveVisibilityVO.class);
                    if (storeReceiveVisibilityVO == null) {
                        continue;
                    }
                    if (Objects.equals(storeReceiveVisibilityVO.getType(),SELECT_ALL) || Objects.equals(storeReceiveVisibilityVO.getType(),IMPORT)) {
                        continue;
                    }
                    //获取当前优惠券领取范围客户数据
                    CreateWxCouponVO.StoreVisibilityVO storeVisibilityVO = GsonUtil.getGson().fromJson(wechatCouponEntity.getStoreScope(), CreateWxCouponVO.StoreVisibilityVO.class);
                    if (storeVisibilityVO == null) {
                        continue;
                    }
                    List<String> accountIds = storeVisibilityVO.getAccountIds();
                    if (Objects.equals(storeReceiveVisibilityVO.getType(),SELECT_CONDITION)) {
                        List<String> storeAccountIds = this.getStoreAccountIds(ea, SELECT_CONDITION, storeReceiveVisibilityVO.getValue(), VisibilityEnum.RECEIVE_SCOPE.getType());
                        if (CollectionUtils.isEmpty(storeAccountIds)) {
                            continue;
                        }
                        if ("i".equals(triggerType)) {
                            if (accountIds.contains(accountId)) {
                                continue;
                            }
                            if (storeAccountIds.contains(accountId)) {
                                accountIds.add(accountId);
                                //如果当前客户在领取范围内，则更新优惠券领取范围
                                storeVisibilityVO.setAccountIds(accountIds);
                                weChatCouponDAO.updateStoreScope(wechatCouponEntity.getId(),GsonUtil.getGson().toJson(storeVisibilityVO));
                            }
                        } else if ("u".equals(triggerType)) {
                            if (accountIds.contains(accountId) && !storeAccountIds.contains(accountId)) {
                                //原本领取范围包含, 但是更新后不再范围内, 则删除该客户
                                accountIds.remove(accountId);
                                storeVisibilityVO.setAccountIds(accountIds);
                                weChatCouponDAO.updateStoreScope(wechatCouponEntity.getId(),GsonUtil.getGson().toJson(storeVisibilityVO));
                            } else if (!accountIds.contains(accountId) && storeAccountIds.contains(accountId)) {
                                //原本不包含, 但是更新后在范围内, 则新增该客户
                                accountIds.add(accountId);
                                storeVisibilityVO.setAccountIds(accountIds);
                                weChatCouponDAO.updateStoreScope(wechatCouponEntity.getId(),GsonUtil.getGson().toJson(storeVisibilityVO));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("PublicCouponManager handle wechatCouponEntity error:{}",wechatCouponEntity.getId());
                }
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    /**
     *
     * @param tenantIds 下游企业tenantId列表
     * @param ea 1端企业ea
     * @return
     */
    public Map<String,Integer> getEnterpriseRelationOwner(List<String> tenantIds,String ea) {
        Map<String,Integer> ownerMap = Maps.newHashMap();
        //将 tenantIds 转换成 int 类型集合
        List<Integer> tenantIdList = tenantIds.stream().map(Integer::parseInt).collect(Collectors.toList());
        Map<Integer, String> eaMap = eieaConverter.enterpriseIdToAccount(tenantIdList);
        //获取 eaMap 的value集合
        List<String> eas = new ArrayList<>(eaMap.values());
        //查询互联企业对象
        PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        queryRelation.addFilter("enterprise_account", PaasAndCrmOperatorEnum.IN.getCrmOperator(),eas);
        queryRelationFilterArg.setQuery(queryRelation);
        queryRelationFilterArg.setObjectAPIName("EnterpriseRelationObj");
        queryRelationFilterArg.setSelectFields(Lists.newArrayList("_id","dest_outer_tenant_id","enterprise_account"));
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg);
        if (totalRelationCount <= 0) {
            return ownerMap;
        }
        int currentRelationCount = 0;
        String lastRelationId = null;
        List<ObjectData> objectDataList = Lists.newArrayList();
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        if (CollectionUtils.isEmpty(objectDataList)) {
           return ownerMap;
        }
        //获取互联企业id 集合
        List<String> relationIds = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        //根据 relationIds 查询 PublicEmployeeObj 对象
        PaasQueryFilterArg queryOwnerFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryOwner = new PaasQueryArg(0, 1);
        queryOwner.addFilter("outer_tenant_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(),relationIds);
        queryOwner.addFilter("relation_owner", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList("true"));
        queryOwnerFilterArg.setQuery(queryOwner);
        queryOwnerFilterArg.setObjectAPIName("PublicEmployeeObj");
        queryOwnerFilterArg.setSelectFields(Lists.newArrayList("_id","employee_id","relation_owner","outer_tenant_id"));
        int totalOwnerCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryOwnerFilterArg);
        if (totalOwnerCount <= 0) {
            return ownerMap;
        }
        int currentOwnerCount = 0;
        String lastOwnerId = null;
        Map<String,Integer> ownerEmployeeMap = Maps.newHashMap();
        List<ObjectData> ownerDataList = Lists.newArrayList();
        while (currentOwnerCount < totalOwnerCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryOwnerFilterArg, lastOwnerId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            ownerDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentOwnerCount += tempSize;
            lastOwnerId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        if (CollectionUtils.isEmpty(ownerDataList)) {
            return ownerMap;
        }
        //将 ownerDataList 转换成 ownerEmployeeMap, outer_tenant_id 作为 key, employee_id 作为 value
        ownerEmployeeMap = ownerDataList.stream().collect(Collectors.toMap(objectData -> objectData.getString("outer_tenant_id"), objectData -> objectData.getInt("employee_id"), (k1, k2) -> k2));
        //获取objectDataList 的 enterpriseAccount 集合
        List<String> enterpriseAccounts = objectDataList.stream().map(objectData -> objectData.getString("enterprise_account")).collect(Collectors.toList());
        Map<String, Integer> enterpriseAccountToIdMap = eieaConverter.enterpriseAccountToId(enterpriseAccounts);
        for (ObjectData objectData : objectDataList) {
            String enterpriseAccount = objectData.getString("enterprise_account");
            Integer tenantId = enterpriseAccountToIdMap.get(enterpriseAccount);
            if (ownerEmployeeMap.containsKey(objectData.getId())){
                Integer employeeId = ownerEmployeeMap.get(objectData.getId());
                ownerMap.put(String.valueOf(tenantId),employeeId);
            }
        }
        return ownerMap;
    }

    /**
     * 获取互联企业关联的客户对象负责人(1端使用)
     * @param tenantId
     * @param ea
     * @return
     */
    public Integer getAccountOwner(String tenantId,String ea) {
        //mapper_account_id
        String enterpriseAccount = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        //查询互联企业对象
        PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        queryRelation.addFilter("enterprise_account", PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList(enterpriseAccount));
        queryRelationFilterArg.setQuery(queryRelation);
        queryRelationFilterArg.setObjectAPIName("EnterpriseRelationObj");
        queryRelationFilterArg.setSelectFields(Lists.newArrayList("_id","dest_outer_tenant_id","enterprise_account","mapper_account_id"));
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, null, 10);
        if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
            return null;
        }
        ObjectData objectData = objectDataInnerPage.getDataList().get(0);
        String mapperAccountId = objectData.getString("mapper_account_id");
        //查询客户对象
        ObjectData accountObj = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CUSTOMER.getName(), mapperAccountId);
        if (accountObj == null) {
            return null;
        }
        return accountObj.getOwner();
    }

    public boolean updatePublicObjOwner(String tenantId,Integer ownerId,String objectAPIName,String objectId) {
        if (ownerId == null) {
            return false;
        }
        try {
            ActionChangeOwnerArg changeOwnerArg = new ActionChangeOwnerArg(objectId,ownerId);
            changeOwnerArg.setUpdateDataOwnDepartment(true);
            Result<ActionChangeOwnerResult> actionChangeOwnerResultResult = metadataActionService.changeOwner(HeaderObj.newInstance(Integer.parseInt(tenantId), -10000), objectAPIName, changeOwnerArg);
            return actionChangeOwnerResultResult.isSuccess();
        } catch (Exception e) {
            return false;
        }
    }

    public String getCouponTopEa(String currentEa) {
        List<String> topEaList = GsonUtil.getGson().fromJson(couponTopEaList,ArrayList.class);
        List<String> sandboxDevEaList = GsonUtil.getGson().fromJson(couponMengniuDevEaList,ArrayList.class);
        List<String> sandboxUATEaList = GsonUtil.getGson().fromJson(couponMengniuUATEaList,ArrayList.class);
        String couponTopEa = topEaList.get(0);
        if (StringUtils.isEmpty(currentEa)) {
            return couponTopEa;
        }
        if (CollectionUtils.isNotEmpty(sandboxDevEaList) && sandboxDevEaList.contains(currentEa)) {
            couponTopEa = topEaList.get(1);
        }
        if (CollectionUtils.isNotEmpty(sandboxUATEaList) && sandboxUATEaList.contains(currentEa)) {
            couponTopEa = topEaList.get(2);
        }
        return couponTopEa;
    }

    public void handleCouponReceiveRangeJob(String ea) {
        //查询当前ea 创建的优惠券
        List<WechatCouponEntity> wechatCouponEntities = new ArrayList<>();
        //获取当前时间前一天的开始时间和结束时间的时间戳
        long startTime = DateUtils.getDayStartTime(DateUtils.getLastXDay(LocalDate.now(), 1));
        long endTime = DateUtils.getDayEndTime(DateUtils.getLastXDay(LocalDate.now(), 1));
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
        PaasQueryArg query = new PaasQueryArg(0,500);
        query.addFilter(CrmCustomerFieldEnum.LAST_MODIFIED_TIME.getFieldName(), PaasAndCrmOperatorEnum.GTE.getPaasOperator(), Lists.newArrayList(String.valueOf(startTime)));
        query.addFilter(CrmCustomerFieldEnum.LAST_MODIFIED_TIME.getFieldName(), PaasAndCrmOperatorEnum.LTE.getPaasOperator(), Lists.newArrayList(String.valueOf(endTime)));
        query.addFilter("record_type",PaasAndCrmOperatorEnum.EQUALS.getPaasOperator(),Lists.newArrayList("default__c"));
        filterArg.setQuery(query);
        filterArg.setSelectFields(Lists.newArrayList("_id"));
        List<ObjectData> customerObjectDataList = this.queryObjectByPaasFilter(ea,filterArg);
        if (CollectionUtils.isEmpty(customerObjectDataList)) {
            log.warn("PublicCouponManager handleCouponReceiveRangeJob customerObjectDataList is empty");
            return;
        }
        //获取在前一天有修改的客户id
        List<String> accountIds = customerObjectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        List<ObjectData> objectDataList = this.queryPublicCouponByEa(ea);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        List<String> couponIds = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        //将couponIds 拆成 500个一组
        List<List<String>> couponIdsList = Lists.partition(couponIds, 500);
        for (List<String> couponIdsSubList : couponIdsList) {
            List<WechatCouponEntity> wechatCouponEntitiesSubList = weChatCouponDAO.queryByCouponObjectIds(couponIdsSubList);
            if (CollectionUtils.isEmpty(wechatCouponEntitiesSubList)) {
                continue;
            }
            wechatCouponEntities.addAll(wechatCouponEntitiesSubList);
        }
        if (CollectionUtils.isEmpty(wechatCouponEntities)) {
            return;
        }
        CountDownLatch countDownLatch = new CountDownLatch(accountIds.size());
        List<List<String>> accountIdsPartition = Lists.partition(accountIds, 1000);
        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(accountIdsPartition.size(), "handle_coupon_receive_range_job_"+ea);
        //进行批量处理
        for (WechatCouponEntity wechatCouponEntity : wechatCouponEntities) {
            String receiveScope = wechatCouponEntity.getReceiveScope();
            String storeScope = wechatCouponEntity.getStoreScope();
            if (StringUtils.isEmpty(receiveScope) || StringUtils.isEmpty(storeScope)) {
                continue;
            }
            CreateWxCouponVO.StoreReceiveVisibilityVO storeReceiveVisibilityVO = GsonUtil.getGson().fromJson(wechatCouponEntity.getReceiveScope(), CreateWxCouponVO.StoreReceiveVisibilityVO.class);
            if (storeReceiveVisibilityVO == null) {
                continue;
            }
            if (Objects.equals(storeReceiveVisibilityVO.getType(),SELECT_ALL) || Objects.equals(storeReceiveVisibilityVO.getType(),IMPORT)
                    || Objects.equals(storeReceiveVisibilityVO.getType(),SELECT_FIXED)) {
                continue;
            }
            //获取当前优惠券领取范围客户数据
            CreateWxCouponVO.StoreVisibilityVO storeVisibilityVO = GsonUtil.getGson().fromJson(wechatCouponEntity.getStoreScope(), CreateWxCouponVO.StoreVisibilityVO.class);
            if (storeVisibilityVO == null) {
                continue;
            }
            List<String> storeAccountIds = storeVisibilityVO.getAccountIds();
            if (CollectionUtils.isEmpty(storeAccountIds)) {
                continue;
            }
            executorService.execute(() -> {
                try {
                    for (List<String> accountIdsSubList : accountIdsPartition) {
                        try {
                            if (Objects.equals(storeReceiveVisibilityVO.getType(),SELECT_CONDITION)) {
                                List<String> conformAccountIds = this.conformConditionAccount(ea, SELECT_CONDITION, storeReceiveVisibilityVO.getValue(), VisibilityEnum.RECEIVE_SCOPE.getType(), accountIdsSubList);
                                //需要添加到storeAccountIds中的客户
                                List<String> addAccountIds = conformAccountIds.stream().filter(id -> !storeAccountIds.contains(id)).collect(Collectors.toList());
                                //剩余不满足条件客户
                                List<String> nonConformAccountIds =  accountIdsSubList.stream().filter(id -> !conformAccountIds.contains(id)).collect(Collectors.toList());
                                //需要从storeAccountIds中移除客户
                                List<String> deleteAccountIds = nonConformAccountIds.stream().filter(storeAccountIds::contains).collect(Collectors.toList());
                                //添加满足条件的客户
                                if (CollectionUtils.isNotEmpty(addAccountIds)) {
                                    storeAccountIds.addAll(addAccountIds);
                                }
                                //移除当前范围已经不满足条件的客户
                                if (CollectionUtils.isNotEmpty(deleteAccountIds)) {
                                    storeAccountIds.removeAll(deleteAccountIds);
                                }
                                storeVisibilityVO.setAccountIds(storeAccountIds);
                                weChatCouponDAO.updateStoreScope(wechatCouponEntity.getId(),GsonUtil.getGson().toJson(storeVisibilityVO));
                            }
                        } catch (Exception e) {
                            log.warn("PublicCouponManager handleCouponReceiveRangeJob error:{}",wechatCouponEntity.getId());
                        }
                    }
                } catch (Exception e) {
                    log.warn("PublicCouponManager execute error:{}",e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(1,TimeUnit.HOURS);
        } catch (InterruptedException e) {
            log.warn("PublicCouponManager handleCouponReceiveRangeJob InterruptedException error:{}",e.getMessage());
        }
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    public List<ObjectData> queryObjectByPaasFilter(String ea, PaasQueryFilterArg filterArg) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        try {
            int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, filterArg);
            if (totalRelationCount <= 0) {
                return objectDataList;
            }
            int currentRelationCount = 0;
            String lastRelationId = null;
            while (currentRelationCount < totalRelationCount) {
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, filterArg, lastRelationId, 500);
                if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                    break;
                }
                objectDataList.addAll(objectDataInnerPage.getDataList());
                int tempSize = objectDataInnerPage.getDataList().size();
                currentRelationCount += tempSize;
                lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
            }
        } catch (Exception e) {
            log.warn("PublicCouponManager queryObjectByPaasFilter error:{}",e.getMessage());
        }
        return objectDataList;
    }

    public boolean isExistAccount(String ea, String type, String value, int visibilityType,String accountId) {
        if (Objects.equals(type,SELECT_ALL) || Objects.equals(type, SELECT_CONDITION)) {
            //查询所有的下游企业
            if (Objects.equals(type,SELECT_ALL) && visibilityType == VisibilityEnum.RECEIVE_SCOPE.getType()) {
                return true;
            }
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            if (visibilityType == VisibilityEnum.SEND_SCOPE.getType()) {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("dealer__c","secondary_dealer__c","mollercular_company__c","shop_warehouse__c"));
            } else {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("default__c"));
            }
            if (Objects.equals(type, SELECT_CONDITION)) {
                List<Wheres> wheres = GsonUtil.getGson().fromJson(value, new TypeToken<List<Wheres>>() {
                }.getType());
                query.setWheres(wheres);
            }
            query.addFilter("_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(accountId));
            //按照对象筛选条件查询
            queryFilterArg.setQuery(query);
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id"));
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            return totalCount > 0;
        } else if (Objects.equals(type, SELECT_FIXED)) {
            //直接选择客户
            List<String> accountIds = Lists.newArrayList();
            List<AccountFilter> accountFilters = GsonUtil.getGson().fromJson(value, new TypeToken<List<AccountFilter>>() {
            }.getType());
            accountIds.addAll(accountFilters.stream().map(AccountFilter::getAccountId).collect(Collectors.toList()));
            return CollectionUtils.isNotEmpty(accountIds);
        } else {
            log.warn("PublicCouponManager handleCouponReceiveRangeJob type error:{}",type);
            return false;
        }
    }

    public List<String> conformConditionAccount(String ea, String type, String value, int visibilityType,List<String> accountIds) {
        List<String> resultAccountIds = Lists.newArrayList();
        if (Objects.equals(type,SELECT_ALL) || Objects.equals(type, SELECT_CONDITION)) {
            //查询所有的下游企业
            if (Objects.equals(type,SELECT_ALL) && visibilityType == VisibilityEnum.RECEIVE_SCOPE.getType()) {
                return resultAccountIds;
            }
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            if (visibilityType == VisibilityEnum.SEND_SCOPE.getType()) {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("dealer__c","secondary_dealer__c","mollercular_company__c","shop_warehouse__c"));
            } else {
                query.addFilter("record_type",PaasAndCrmOperatorEnum.IN.getCrmOperator(),Lists.newArrayList("default__c"));
            }
            if (Objects.equals(type, SELECT_CONDITION)) {
                List<Wheres> wheres = GsonUtil.getGson().fromJson(value, new TypeToken<List<Wheres>>() {
                }.getType());
                query.setWheres(wheres);
            }
            query.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), accountIds);
            //按照对象筛选条件查询
            queryFilterArg.setQuery(query);
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id"));
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                return resultAccountIds;
            }
            resultAccountIds.addAll(objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
            return resultAccountIds;
        } else if (Objects.equals(type, SELECT_FIXED)) {
            //直接选择客户
            List<AccountFilter> accountFilters = GsonUtil.getGson().fromJson(value, new TypeToken<List<AccountFilter>>() {
            }.getType());
            resultAccountIds.addAll(accountFilters.stream().map(AccountFilter::getAccountId).collect(Collectors.toList()));
            return resultAccountIds;
        } else {
            log.warn("PublicCouponManager conformConditionAccount type error:{}",type);
            return resultAccountIds;
        }
    }

    public boolean isInReceiveCouponRange(String ea, String storeScope,String receiveScope, String accountId) {
        boolean flag = false;
        //如果是筛选条件，则判断是否在筛选条件内
        CreateWxCouponVO.StoreReceiveVisibilityVO storeReceiveVisibilityVO = GsonUtil.getGson().fromJson(receiveScope, CreateWxCouponVO.StoreReceiveVisibilityVO.class);
        if (SELECT_CONDITION.equals(storeReceiveVisibilityVO.getType())) {
            List<String> existAccountIds = conformConditionAccount(ea, SELECT_CONDITION, storeReceiveVisibilityVO.getValue(), VisibilityEnum.RECEIVE_SCOPE.getType(), Lists.newArrayList(accountId));
            if (CollectionUtils.isNotEmpty(existAccountIds)) {
                flag = true;
            }
            return flag;
        }
        //如果不是,则判断是否有storeScope
        if (StringUtils.isNotBlank(storeScope)) {
            List<String> accountIds = Lists.newArrayList();
            CreateWxCouponVO.StoreVisibilityVO storeVisibilityVO = GsonUtil.getGson().fromJson(storeScope, CreateWxCouponVO.StoreVisibilityVO.class);
            accountIds.addAll(storeVisibilityVO.getAccountIds());
            if(CollectionUtils.isNotEmpty(accountIds) && (accountIds.contains("-999999") || accountIds.contains(accountId))) {
                flag = true;
            }
            return flag;
        }
        return flag;
    }
}