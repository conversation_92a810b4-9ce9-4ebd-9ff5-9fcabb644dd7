/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.mq.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.marketing.api.arg.RecordUtmParamArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.result.MarketingWxServiceResult;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.common.contstant.DelayQueueTagConstants;
import com.facishare.marketing.common.contstant.RocketMqDelayLevelConstants;
import com.facishare.marketing.common.contstant.campaign.CampaignConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowAdditionalConfigDao;
import com.facishare.marketing.provider.dao.marketingplugin.MemberCouponBindDAO;
import com.facishare.marketing.provider.dao.marketingplugin.WeChatCouponDAO;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowAdditionalConfigEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MemberCouponBindEntity;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdLeadDataManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.baidu.UtmDataManger;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.coupon.PublicCouponManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingContentObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.cusomerDev.CustomerCustomizeFormDataManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.mq.handler.dto.CrmEventDTO;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.mq.sender.MarketingMessageSender;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.ObjMessageRebalancedUtil;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.paasrulerestapi.arg.DataRuleExpressionPatternArg;
import com.fxiaoke.paasrulerestapi.common.contants.SceneEnum;
import com.fxiaoke.paasrulerestapi.common.data.HeaderObj;
import com.fxiaoke.paasrulerestapi.common.data.RuleContext;
import com.fxiaoke.paasrulerestapi.common.result.Result;
import com.fxiaoke.paasrulerestapi.service.RuleGroupService;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.service.usermarketingaccount.UserMarketingAccountServiceImpl.getPhoneByChannelObject;

@Component
@Slf4j
public class CrmMqNewMessageHandler implements MessageListenerConcurrently {

    private static final Integer SUPER_USER = -10000;
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private MarketingFlowAdditionalConfigDao marketingFlowAdditionalConfigDao;
    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingFlowInstanceManager marketingFlowInstanceManager;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private RuleGroupService ruleGroupService;
    @Autowired
    private PaasRuleGroupManager paasRuleGroupManager;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private CustomerCustomizeFormDataManager customerCustomizeFormDataManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;
    @Autowired
    private UserMarketingCrmAccountAccountRelationDao userMarketingCrmAccountAccountRelationDao;
    @Autowired
    private UserMarketingCrmMemberRelationDao userMarketingCrmMemberRelationDao;
    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;
    @Autowired
    private WxMiniAppUserMemberBindDao wxMiniAppUserMemberBindDao;
    @Autowired
    private WxServiceUserMemberBindDao wxServiceUserMemberBindDao;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private LiveManager liveManager;
    @Autowired
    private SceneTriggerManager sceneTriggerManager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private MemberManager memberManager;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private DelayQueueSender delayQueueSender;

    @Value("${website.visitor.link.ea}")
    private String websiteVisitorLinkEA; // 官网 EA
    @Value("${website.visitor.link.api.name}")
    private String websiteVisitorLinkApiName; // 官网访客id
    @Autowired
    private ActionManager actionManager;

    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private WeChatServiceMarketingActivityService weChatServiceMarketingActivityService;

    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private RedisManager redisManager;
    private final static List<String> websiteVisitorLinkEAs = new ArrayList<>(); // 避免每次接收到消息都需要反序列化

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private AdLeadDataManager adLeadDataManager;

    @Autowired
    private WxCouponPayManager wxCouponPayManager;

    @Autowired
    private WeChatCouponDAO weChatCouponDAO;

    @Autowired
    private MemberCouponBindDAO memberCouponBindDAO;

    @Autowired
    @Qualifier("objectGrayMessageSender")
    private MarketingMessageSender objectGrayMessageSender;

    @Autowired
    @Qualifier("objMessageRebalancedSender")
    private MarketingMessageSender objMessageRebalancedSender;

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Autowired
    private MarketingEventService marketingEventService;

    @Autowired
    private MemberMarketingManager memberMarketingManager;
    @Autowired
    private MarketingContentObjManager marketingContentObjManager;

    @Autowired
    private UtmDataManger utmDataManger;

    public static final String OBJECT_MAP_KEY = "%s-%s";

    @ReloadableProperty("marketing_gray_list")
    private String marketingGrayList;

    private Set<String> handleApiList = ImmutableSet.of(CrmObjectApiNameEnum.CRM_LEAD.getName(), CrmObjectApiNameEnum.CUSTOMER.getName(), CrmObjectApiNameEnum.CONTACT.getName(),
            CrmObjectApiNameEnum.MEMBER.getName(), CrmObjectApiNameEnum.WECHAT.getName(), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName(),
            CrmObjectApiNameEnum.MARKETING_EVENT.getName(), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), CrmObjectApiNameEnum.CUSTOMER_SERVICE_SESSION_OBJ.getName(), CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), CrmObjectApiNameEnum.COUPON_INST_OBJ.getName(), "object_sR1so__c", CrmObjectApiNameEnum.LEADS_TRANSFER_LOG_OBJ.getName(), CrmObjectApiNameEnum.PUBLIC_EMPLOYEE_OBJ.getName());  //object_sR1so__c：双保胎自定义对象同步到到会议
    @Autowired
    private PublicCouponManager publicCouponManager;
    @Autowired
    private UserRelationManager userRelationManager;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        List<CrmEventDTO.CrmEvent> crmEventList = Lists.newArrayList();
        for (MessageExt messageExt : msgs) {
            CrmEventDTO.CrmEvent crmEvent = JSON.parseObject(messageExt.getBody(), CrmEventDTO.CrmEvent.class, Feature.IgnoreNotMatch);
            CrmEventDTO.MessageTraceContext messageTraceContext = new CrmEventDTO.MessageTraceContext();
            messageTraceContext.setEa(MessageHelper.getEnterpriseAccount(messageExt));
            String traceId = MessageHelper.getTraceId(messageExt);
            if (StringUtils.isBlank(traceId)) {
                traceId = UUIDUtil.getUUID();
            }
            messageTraceContext.setTraceId(traceId);
            messageTraceContext.setUserId(MessageHelper.getUserId(messageExt));
            messageTraceContext.setColor(MessageHelper.getTraceColor(messageExt));
            messageTraceContext.setMessageId(messageExt.getMsgId());
            crmEvent.setMessageTraceContext(messageTraceContext);
            String ea = eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId());
            if (enterpriseInfoManager.isEnterpriseStopAndVersionExpiredFilterLog(ea)) {
                continue;
            }
            if (StringUtils.isNotEmpty(ea) && isGrayEa(ea) && isNotGray()) {
                this.forwardHandle(messageExt);
            } else if (ObjMessageRebalancedUtil.isRebalancedMessage(ea) && isNotGray() && StringUtils.isBlank(messageExt.getProperty("is_mk_rebalanced"))) {
                //消息重平衡，发往灰度的消息已经随机了，这里只处理非灰度的情况 如果要新增环境或者企业，得看一下环境对应的消费者的状态是不是running以及检查对应的线程数量
                messageExt.putUserProperty("is_mk_rebalanced", "1");
                objMessageRebalancedSender.send(this.getClass().getSimpleName(), messageExt, null, getRebalancedHahKey(crmEvent));
            } else {
                crmEventList.add(crmEvent);
                // 是否需要处理广告回传
                if (adOCPCUploadManager.isHandleAdDataSendBack(crmEvent.getTenantId(), crmEvent)) {
                    /**
                     *  必须先写入到延迟队列，延迟消费， 一是因为查询CRM接口是查ES，ES会有延迟，如果变更事件过来直接去查，有一定的概率会查询不到，
                     *  二是因为有异步更新线索的市场活动的情况，如果不延迟，会导致线索的市场活动还没更新完，会导致会传失败
                     *  延迟也会有问题一个问题： 假设规则是字段A修改为B就触发回传，假设当前字段是B,在延迟时间内先改了为C,又改为了B,此时A字段的值是B,这两个操作会生成两条MQ
                     *  那么会规则去查询pass,会导致两条操作都命中规则(因为此时A字段值是B), 如果要同时杜绝ES延迟和上述问题，需要在本地解析规则或者直接查询pg,这两个个方法都有弊端
                     *  如果自己解析规则，1是复杂 2是pass那边改了或者加了东西我们也要对应改，我们无法及时响应。如果直接查询pg(不知pass是否支持),一旦使用客户量过多或者MQ消息过大，会导致pg压力过大
                     *  现在将延迟时间改为30S,一是30S应该足够应付ES的延迟 二是 绝大部分情况客户对于设置了规则的字段都是涉及有效商机的字段,改动不会这么频繁 这算是一个临时的折中方案
                     *
                     */
                    delayQueueSender.sendByByte(ea, messageExt.getBody(), DelayQueueTagConstants.CRM_EVEN, RocketMqDelayLevelConstants.THIRTY_SECOND);
                }
            }
        }
        // 由于下面会查到对象详情，当有大量的对象数据变更时，遍历去查询会导致pass的请求量激增，所以在这里提前将数据准备好
        // key：apiName-id 如： "LeadsObj-xxxxxx"
        Map<String, ObjectData> objectDataMap = batchGetObjectDataMap(crmEventList);

        for (CrmEventDTO.CrmEvent crmEvent : crmEventList) {
            try {
                initTraceContext(crmEvent);
                processMqMessage(crmEvent, objectDataMap);
            } catch (Exception e) {
                log.info("CrmMqNewConsumer consumeMessage exception messageExt:{} e:", crmEventList, e);
            } finally {
                TraceContext.remove();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private String getRebalancedHahKey(CrmEventDTO.CrmEvent crmEvent) {
        try {
            if (crmEvent == null || CollectionUtils.isEmpty(crmEvent.getBody())) {
                return UUIDUtil.getUUID();
            }
            return crmEvent.getBody().get(0).getObjectId();
        } catch (Exception e) {
            log.info("getRebalancedHahKey error, crmEvent:{}", crmEvent, e);
        }
        return UUIDUtil.getUUID();
    }

    private boolean isNotGray() {
        return !System.getProperty("process.profile").contains("gray");
    }

    private boolean isGrayEa(String ea) {
        return AbstractMessageHandler.isAnyMatchEa(marketingGrayList, ea);
    }

    private void forwardHandle(MessageExt messageExt) {
        objectGrayMessageSender.send(this.getClass().getSimpleName(), messageExt);
    }

    private void initTraceContext(CrmEventDTO.CrmEvent crmEvent) {
        TraceContext traceContext = TraceContext.get();
        traceContext.setEa(crmEvent.getMessageTraceContext().getEa());
        traceContext.setEi(String.valueOf(crmEvent.getTenantId()));
        String traceId = crmEvent.getMessageTraceContext().getTraceId();
        if (StringUtils.isBlank(traceId)) {
            traceId = UUIDUtil.getUUID();
        }
        traceContext.setTraceId(traceId);
        traceContext.setUid(crmEvent.getMessageTraceContext().getUserId());
        traceContext.setColor(crmEvent.getMessageTraceContext().isColor());
    }

    private Map<String, ObjectData> batchGetObjectDataMap(List<CrmEventDTO.CrmEvent> crmEventList) {
        Map<String, ObjectData> objectDataMap = Maps.newHashMap();
        try {
            Map<Integer, List<CrmEventDTO.CrmEvent>> tenantIdToEventList = crmEventList.stream().collect(Collectors.groupingBy(CrmEventDTO.CrmEvent::getTenantId));
            tenantIdToEventList.forEach((tenantId, eventList) -> {
                String ea = eieaConverter.enterpriseIdToAccount(tenantId);
                List<CrmEventDTO.Body> bodyList = Lists.newArrayList();
                for (CrmEventDTO.CrmEvent crmEvent : eventList) {
                    bodyList.addAll(crmEvent.getBody());
                }
                Map<String, List<String>> apiNameToIdListMap = bodyList.stream().filter(e -> handleApiList.contains(e.getEntityId())).collect(Collectors.groupingBy(CrmEventDTO.Body::getEntityId, Collectors.mapping(CrmEventDTO.Body::getObjectId, Collectors.toList())));
                apiNameToIdListMap.forEach((apiName, idList) -> {
                    List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3ForMq(ea, SuperUserConstants.USER_ID, apiName, null, idList);
                    for (ObjectData objectData : objectDataList) {
                        objectDataMap.put(String.format(OBJECT_MAP_KEY, apiName, objectData.getId()), objectData);
                    }
                });
            });
        } catch (Exception e) {
            log.error("crm mq consumer batchGetObjectDataMap error, data: {}", crmEventList, e);
        }
        return objectDataMap;
    }

    private void handleMarketingAssocaiateEvent(Integer ei, CrmEventDTO.CrmEvent crmEvent, Map<String, ObjectData> objectDataMap) {
        List<CrmEventDTO.Body> leadBodyList = null;
        List<CrmEventDTO.Body> customerBodyList = null;
        List<CrmEventDTO.Body> contactBodyList = null;
        List<CrmEventDTO.Body> wechatFriendRecordBodyList = null;
        List<CrmEventDTO.Body> leadsTransferLogList = null;
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        for (CrmEventDTO.Body body : crmEvent.getBody()) {
            if (!body.getTriggerType().equals(CrmEventDTO.Body.INSERT) && !(body.getTriggerType().equals(CrmEventDTO.Body.UPDATE) && !Objects.equals(body.getAfterTriggerData().get("life_status"), "invalid"))) {
                continue;
            }
            //关联从企微过来的联系人、线索、客户和企业微信身份（营销用户）身份做绑定
            if (org.apache.commons.lang.StringUtils.equals(body.getEntityId(), CrmObjectApiNameEnum.CRM_LEAD.getName())) {
                if (leadBodyList == null) {
                    leadBodyList = Lists.newArrayList();
                }
                leadBodyList.add(body);
                // 处理网易数帆官网营销用户身份打通
                if (CollectionUtils.isEmpty(websiteVisitorLinkEAs)) {
                    websiteVisitorLinkEAs.addAll(GsonUtil.getGson().fromJson(websiteVisitorLinkEA, ArrayList.class));
                    log.info("websiteVisitorLinkEAs：{}", websiteVisitorLinkEAs);
                }
                if (websiteVisitorLinkEAs.contains(ea) && body.getTriggerType().equals(CrmEventDTO.Body.INSERT)) { // 只处理网易数帆，只处理新增事件
                    log.info("接收到网易数帆线索数据， crmEvent:{}", crmEvent);
                    handleBrownUserAndLeadAssocaiateEvent(ea, body, websiteVisitorLinkApiName);
                }
            } else if (org.apache.commons.lang.StringUtils.equals(body.getEntityId(), CrmObjectApiNameEnum.CUSTOMER.getName())) {
                if (customerBodyList == null) {
                    customerBodyList = Lists.newArrayList();
                }
                customerBodyList.add(body);
            } else if (org.apache.commons.lang.StringUtils.equals(body.getEntityId(), CrmObjectApiNameEnum.CONTACT.getName())) {
                if (contactBodyList == null) {
                    contactBodyList = Lists.newArrayList();
                }
                contactBodyList.add(body);
            } else if (StringUtils.equals(body.getEntityId(), CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName())) {
                if (wechatFriendRecordBodyList == null) {
                    wechatFriendRecordBodyList = Lists.newArrayList();
                }
                wechatFriendRecordBodyList.add(body);
            } else if (StringUtils.equals(body.getEntityId(), CrmObjectApiNameEnum.LEADS_TRANSFER_LOG_OBJ.getName())) {
                if (leadsTransferLogList == null) {
                    leadsTransferLogList = Lists.newArrayList();
                }
                leadsTransferLogList.add(body);
            }
        }

        try {
            if (CollectionUtils.isNotEmpty(leadBodyList)) {
                Set<String> objectIds = leadBodyList.stream().map(CrmEventDTO.Body::getObjectId).collect(Collectors.toSet());
                for (String objectId : objectIds) {
                    //关联从企微过来的联系人、线索、客户和企业微信身份（营销用户）身份做绑定
                    String objectDataKey = String.format(OBJECT_MAP_KEY, CrmObjectApiNameEnum.CRM_LEAD.getName(), objectId);
                    ObjectData objectData = objectDataMap.get(objectDataKey);
                    if (objectData != null) {
                        userMarketingAccountAssociationManager.doBindLeadAndQywxExternalUser(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), objectData);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(customerBodyList)) {
                Set<String> objectIds = customerBodyList.stream().map(CrmEventDTO.Body::getObjectId).collect(Collectors.toSet());
                for (String objectId : objectIds) {
                    String objectDataKey = String.format(OBJECT_MAP_KEY, CrmObjectApiNameEnum.CUSTOMER.getName(), objectId);
                    ObjectData objectData = objectDataMap.get(objectDataKey);
                    if (objectData != null) {
                        userMarketingAccountAssociationManager.doBindUserMarketingByCrmObject(ea, CrmObjectApiNameEnum.CUSTOMER.getName(), objectData);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(contactBodyList)) {
                Set<String> objectIds = contactBodyList.stream().map(CrmEventDTO.Body::getObjectId).collect(Collectors.toSet());
                for (String objectId : objectIds) {
                    String objectDataKey = String.format(OBJECT_MAP_KEY, CrmObjectApiNameEnum.CONTACT.getName(), objectId);
                    ObjectData objectData = objectDataMap.get(objectDataKey);
                    if (objectData != null) {
                        userMarketingAccountAssociationManager.doBindUserMarketingByCrmObject(ea, CrmObjectApiNameEnum.CONTACT.getName(), objectData);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(wechatFriendRecordBodyList)) {
                Set<String> objectIds = wechatFriendRecordBodyList.stream().map(CrmEventDTO.Body::getObjectId).collect(Collectors.toSet());
                for (String objectId : objectIds) {
                    String objectDataKey = String.format(OBJECT_MAP_KEY, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), objectId);
                    ObjectData objectData = objectDataMap.get(objectDataKey);
                    if (objectData != null) {
                        userMarketingAccountAssociationManager.doBindUserMarketingByCrmObject(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), objectData);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(leadsTransferLogList)) {
                Set<String> objectIds = leadsTransferLogList.stream().map(CrmEventDTO.Body::getObjectId).collect(Collectors.toSet());
                for (String objectId : objectIds) {
                    String objectDataKey = String.format(OBJECT_MAP_KEY, CrmObjectApiNameEnum.LEADS_TRANSFER_LOG_OBJ.getName(), objectId);
                    ObjectData objectData = objectDataMap.get(objectDataKey);
                    if (objectData != null) {
                        userMarketingAccountAssociationManager.doBindUserMarketingByCrmObject(ea, CrmObjectApiNameEnum.LEADS_TRANSFER_LOG_OBJ.getName(), objectData);
                    }
                }
            }
        } catch (Exception e) {
            log.error("CrmMqNewConsumer.handleMarketingAssocaiateEvent exception ei:{} crmEvent:{} e:", ei, crmEvent, e);
        }
    }

    /**
     * 通过访客id打通营销用户和线索的关系(网易数帆自定义访客id websiteVisitorLinkApiName,预设访客id visitor_id)
     *
     * @param ea
     * @param body
     */
    public void handleBrownUserAndLeadAssocaiateEvent(String ea, CrmEventDTO.Body body, String browserIdKeyApiName) {
        try {
            // 获取CRM对象数据
            ObjectData objectData = crmMetadataManager.getById(ea, -10000, body.getEntityId(), body.getObjectId());
            if (Objects.isNull(objectData) || Objects.isNull(objectData.getString(browserIdKeyApiName))) {
                return;
            }
            // 存在访客id，需要根据访客id查询关联的营销用户并建立营销用户和CRM对象的关联
            String browserId = objectData.getString(browserIdKeyApiName);
            log.info("线索对象上的访客信息, browserIdKey:{}, body:{}", browserId, body);
            // 1 根据访客id查询营销用户
            AssociationArg browserAssociationArg = new AssociationArg();
            browserAssociationArg.setEa(ea);
            browserAssociationArg.setAssociationId(browserId);
            browserAssociationArg.setType(ChannelEnum.BROWSER_USER.getType());
            browserAssociationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
            browserAssociationArg.setTriggerAction("CrmMqNewMessageHandler");
            Boolean exists = userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(browserAssociationArg);
            AssociationResult associationResult = null;
            if (!exists) {
                //为访客创建营销用户
                associationResult = userMarketingAccountAssociationManager.associate(browserAssociationArg);
            } else {
                associationResult = userMarketingAccountRelationManager.getByEaAndKeyProperties(browserAssociationArg);
            }
            if (associationResult == null || org.apache.commons.lang.StringUtils.isEmpty(associationResult.getUserMarketingAccountId())) {
                return;
            }

            // 判断线索有无关联营销用户
            String browserUserMarketingAccountId = associationResult.getUserMarketingAccountId();
            AssociationArg leadAssociationArg = new AssociationArg();
            leadAssociationArg.setEa(ea);
            leadAssociationArg.setType(ChannelEnum.CRM_LEAD.getType());
            leadAssociationArg.setAssociationId(objectData.getId());
            leadAssociationArg.setTriggerAction("CrmMqNewMessageHandler");
            leadAssociationArg.setTriggerSource(ChannelEnum.CRM_LEAD.getDescription());
            AssociationResult leadAssociationResult = userMarketingAccountRelationManager.getByEaAndKeyProperties(leadAssociationArg);
            if (org.apache.commons.lang.StringUtils.isNotBlank(leadAssociationResult.getUserMarketingAccountId())) {
                // 线索已关联营销用户，做合并处理
                log.info("线索已关联营销用户，做合并处理");
                userMarketingAccountAssociationManager.merge(ea, browserUserMarketingAccountId, leadAssociationResult.getUserMarketingAccountId(), leadAssociationArg);
            } else {
                // 线索未关联营销用户，做绑定处理
                log.info("线索未关联营销用户，做绑定处理");
                AssociationArg bindAssociationArg = new AssociationArg();
                bindAssociationArg.setEa(ea);
                bindAssociationArg.setAssociationId(objectData.getId());
                bindAssociationArg.setUserName(objectData.getName());
                bindAssociationArg.setType(ChannelEnum.CRM_LEAD.getType());
                bindAssociationArg.setTriggerAction("CrmMqNewMessageHandler");
                bindAssociationArg.setTriggerSource(ChannelEnum.CRM_LEAD.getDescription());
                userMarketingAccountAssociationManager.doAssociateAccount(bindAssociationArg, browserUserMarketingAccountId);
            }
            // 根据手机号做营销用户合并处理
            AssociationArg associationArg = new AssociationArg();
            associationArg.setEa(ea);
            associationArg.setAssociationId(objectData.getId());
            associationArg.setType(ChannelEnum.CRM_LEAD.getType());
            associationArg.setUserName(objectData.getName());
            associationArg.setEmail(objectData.getString("email"));
            String phoneFromObjectData = userMarketingAccountManager.getPhoneFromObjectData(objectData, null);
            associationArg.setPhone(phoneFromObjectData);
            associationArg.setTriggerAction("CrmMqNewMessageHandler");
            associationArg.setTriggerSource(ChannelEnum.CRM_LEAD.getDescription());
            userMarketingAccountAssociationManager.associate(associationArg);
        } catch (Exception e) {
            log.error("官网线索打通身份异常 ea:{}, body:{}", ea, body, e);
        }
    }

    private void processMqMessage(CrmEventDTO.CrmEvent crmEvent, Map<String, ObjectData> objectDataMap) {
        Integer ei = crmEvent.getTenantId();
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        if (MarketingStatusEnum.OPEN.getType() != settingManager.getMarketingStatus(ea)) {
            return;
        }

        //处理企微转的线索、客户、联系人和营销用户的关联
        handleMarketingAssocaiateEvent(ei, crmEvent, objectDataMap);
        adLeadDataManager.handleLeadAndNewOpportunityChangeEvent(ea, crmEvent, objectDataMap);
        for (CrmEventDTO.Body body : crmEvent.getBody()) {
            if (!handleApiList.contains(body.getEntityId())) {
                continue;
            }
            String objectDataKey = String.format(OBJECT_MAP_KEY, body.getEntityId(), body.getObjectId());
            ObjectData objectData = objectDataMap.get(objectDataKey);
            trySyncCrmDataMsgToUserMarketingAccount(crmEvent, body);
            tryHandleMarketingEventTimeChange(crmEvent, body);
            // 销售线索处理
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(LeadsFieldContants.API_NAME)) {
                String apiName = body.getEntityId();
                if (!body.getTriggerType().equals(CrmEventDTO.Body.INSERT) && !body.getTriggerType().equals(CrmEventDTO.Body.UPDATE)) {
                    return;
                }
                String leadsId = body.getObjectId();
                if (objectData == null) {
                    // 做个兼容 查不到的在去调用接口
                    objectData = crmMetadataManager.findByIdV3(ea, -10000, CrmObjectApiNameEnum.CRM_LEAD.getName(), null, body.getObjectId());
                }
                // 如果线索上关联了好友记录，处理线索上的营销推广来源
                handleMarketingPromotionSourceByFriendRecord(ea, objectData);
                if (body.getTriggerType().equals(CrmEventDTO.Body.INSERT)) {
                    try {
                        //监听线上创建营销用户
                        AssociationArg associationArg = new AssociationArg();
                        associationArg.setAssociationId(objectData.getId());
                        associationArg.setEa(ea);
                        associationArg.setUserName(objectData.getName());
                        associationArg.setEmail(objectData.getString("email"));
                        associationArg.setType(ChannelEnum.CRM_LEAD.getType());
                        String phone = getPhoneByChannelObject(ChannelEnum.CRM_LEAD.getType(), objectData);
                        associationArg.setPhone(phone);
                        userMarketingAccountAssociationManager.associate(associationArg);
                    }catch (Exception e){
                        log.warn("监听线上创建营销用户异常, body: {}", body, e);
                    }
                    // 监听销售线索新建是否有访客id或者微信用户id
                    doLeadCreateStartFlowInstances(ea, LeadsFieldContants.API_NAME, leadsId, leadsId, objectData);
                    //判断是否有访客id或微信用户id
                    if (objectData != null && (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("visitor_id")) || org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("wechat_fan_id")))) {
                        log.info("CrmMqConsumer processMqMessage LeadsObj detail body:{}", body);
                        leadsOfChatOnLine(ea, body, objectData);
                        bindWxUserAndLead(ea, objectData);
                    }
                } else if (body.getTriggerType().equals(CrmEventDTO.Body.UPDATE)) {
                    for (String key : body.getAfterTriggerData().keySet()) {
                        doLeadUpdateStartFlowInstances(ea, leadsId, apiName, key, body.getAfterTriggerData().getString(key), objectData);
                    }
                    bindWxUserAndLead(ea, objectData);
                    //线索访客字段发生变更，重新关联新的访客和线索
                    handleBrownUserAndLeadAssocaiateEvent(ea, body, "visitor_id");
                }
            }

            //市场活动对象同步会议&直播
            if (body.getEntityId().equals(CrmObjectApiNameEnum.MARKETING_EVENT.getName())) {
                //处理已经作废的市场活动
                if (org.apache.commons.lang.StringUtils.equals("d", body.getTriggerType()) || org.apache.commons.lang.StringUtils.equals("invalid", body.getTriggerType())) {
                    //处理会议相关的推广物料及sop状态
                    activityManager.asyncInvalidQrPosterAndSop(ea, body.getObjectId());
                    conferenceManager.syncConferenceDataFromCrm(ea, body.getContext().getUserId(), body.getObjectId(), null, body.getTriggerType());
                    liveManager.syncLiveFromCrmMarketingEvent(ea, body.getContext().getUserId(), body.getObjectId(), null, body.getTriggerType());
                    materialTagRelationDao.deleteByObjectId(ea, body.getObjectId());
                    continue;
                }

                ControllerDetailArg arg = new ControllerDetailArg();
                arg.setObjectDataId(body.getObjectId());
                String apiName = body.getEntityId();
                arg.setObjectDescribeApiName(apiName);
                if (objectData == null) {
                    objectData = metadataControllerServiceManager.detailForMq(new com.fxiaoke.crmrestapi.common.data.HeaderObj(crmEvent.getTenantId(), SUPER_USER), apiName, arg);
                }
                if (objectData == null) {
                    log.info("CrmMqConsumer metadataControllerServiceManager get objectData detail retun null crmEvent:{}", crmEvent);
                    return;
                }
                if (objectData.get("event_type") != null && org.apache.commons.lang.StringUtils.equals(MarketingEventEnum.MEETING_SALES.getEventType(), (String) objectData.get("event_type"))) {
                    conferenceManager.syncConferenceDataFromCrm(ea, objectData.getCreateBy(), arg.getObjectDataId(), objectData, body.getTriggerType());
                } else if (objectData.get("event_type") != null && org.apache.commons.lang.StringUtils.equals(MarketingEventEnum.LIVE_MARKETING.getEventType(), (String) objectData.get("event_type"))) {
                    liveManager.syncLiveFromCrmMarketingEvent(ea, objectData.getCreateBy(), arg.getObjectDataId(), objectData, body.getTriggerType());
                } else if (objectData.get("event_type") != null && org.apache.commons.lang.StringUtils.equals(MarketingEventEnum.MULTIVENUE_MARKETING.getEventType(), (String) objectData.get("event_type"))) {
                    if (org.apache.commons.lang.StringUtils.equals("i", body.getTriggerType())) {
                        activityManager.asyncHexagonSite(ea, body.getObjectId(), (List<String>) objectData.get("owner"));
                    }
                }
                // 处理会员设置映射同步到活动会员一键报名设置
                if (body.getTriggerType().equals(CrmEventDTO.Body.INSERT)) {
                    memberManager.asyncMemberConfigToMarketingEvent(ea, body.getObjectId());
                }
            }

            // 活动成员数据同步
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName())) {
                log.info("recv CampaignMembersObj create mq:{}", crmEvent);
                if (body.getTriggerType().equals("u")){
                    campaignMergeDataManager.updateCampaignMergeDataByCampaignObj(ea, body);
                    campaignMergeDataManager.handleCampaignMembersStatusChangeEvent(ea, body);
                }else if (body.getTriggerType().equals("i")){
                    campaignMergeDataManager.addCampaignMergeDataByCampaignObj(ea, crmEvent.getTenantId(), body.getObjectId(), objectData);
                    marketingEventService.syncDataToTargetMarketingEventById(ea, objectData.getString("marketing_event_id"), objectData.getId());
                } else if (body.getTriggerType().equals("d") || body.getTriggerType().equals("invalid")){
                    campaignMergeDataManager.deleteCampaignMergeDataByCampaignObj(ea, body.getObjectId());
                }
            }

            //处理优惠券实例核销后生成营销动态
            if (body.getEntityId().equals(CrmObjectApiNameEnum.COUPON_INST_OBJ.getName())) {
                if (org.apache.commons.lang.StringUtils.equals(body.getTriggerType(), "u") && body.getAfterTriggerData().containsKey("use_status") && Objects.equals(body.getAfterTriggerData().getString("use_status"), "USED")) {
                    if (objectData == null) {
                        objectData = crmV2Manager.getObjectData(ea, SUPER_USER, body.getEntityId(), body.getObjectId());
                    }
                    if (objectData == null) {
                        log.info("CrmMqConsumer wxCouponPay get objectData return null crmEvent:{}", crmEvent);
                        return;
                    }
                    String couponId = objectData.getString("coupon_id");
                    WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryDetailByCouponIdAndEa(ea, couponId);
                    if (wechatCouponEntity == null) {
                        log.info("CrmMqConsumer queryDetailByCouponIdAndEa is null crmEvent:{}", crmEvent);
                        return;
                    }
                    //根据实例id查询领取的数据
                    MemberCouponBindEntity queryByCouponInstanceEntity = memberCouponBindDAO.queryByCouponInstanceId(ea, objectData.getId());
                    if (queryByCouponInstanceEntity != null) {
                        log.info("queryByCouponInstanceId, ea: {} id: {}", ea, objectData.getId());
                        //变更领取状态
                        memberCouponBindDAO.updateUsedStatus(queryByCouponInstanceEntity.getId());
                    } else {
                        //如果没查到则查询领取的数据(兼容以前的老数据)
                        MemberCouponBindEntity memberCouponBindEntity = memberCouponBindDAO.queryReceiveBindCoupon(ea, couponId);
                        if (memberCouponBindEntity != null) {
                            //变更领取状态
                            log.info("queryReceiveBindCoupon, ea: {} couponId: {}", ea, couponId);
                            memberCouponBindDAO.updateUsedStatus(memberCouponBindEntity.getId());
                        }
                    }
                    String marketingUserId = wxCouponPayManager.getMarketingUserIdByOutTenantId(objectData.getString("partner_id_backup"), ea);
                    //处理营销动态
                    wxCouponPayManager.syncRecordCouponMarketingActivityData(ea, wechatCouponEntity.getId(), wechatCouponEntity.getMarketingEventId(), marketingUserId, MarketingUserActionType.VERIFICATION_COUPON.getActionType());
                }
            }

            //监听互联用户变更
            if (body.getEntityId().equals(CrmObjectApiNameEnum.PUBLIC_EMPLOYEE_OBJ.getName())) {
                String publicEmployeeId = body.getObjectId();
                userRelationManager.handlePublicEmployeeChange(ea, publicEmployeeId);
            }
//            if (body.getEntityId().equals(CrmObjectApiNameEnum.ENTERPRISE_RELATION_OBJ.getName())) {
//                String enterpriseRelationId = body.getObjectId();
//                appMenuTemplateManager.handleEnterpriseRelationObjChange(ea, enterpriseRelationId);
//            }

            //自定义报名数据同步到营销通会议参会者
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) &&
                    (org.apache.commons.lang.StringUtils.equals(body.getTriggerType(), "i")
                            || org.apache.commons.lang.StringUtils.equals(body.getTriggerType(), "u")
                            || org.apache.commons.lang.StringUtils.equals(body.getTriggerType(), "invalid")
                            || org.apache.commons.lang.StringUtils.equals(body.getTriggerType(), "recover")
                    )) {
                customerCustomizeFormDataManager.syncCrmConferenceDataObjectToMarketing(crmEvent.getTenantId(),
                        body.getEntityId(), body.getObjectId(), body.getTriggerType());
            }

            // 作废CRM线索
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.CRM_LEAD.getName()) && "invalid".equals(body.getTriggerType())) {
                userMarketingCrmLeadAccountRelationDao.deleteByEaAndCrmLeadId(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()), body.getObjectId());
            }
            // 作废CRM客户
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.CUSTOMER.getName()) && "invalid".equals(body.getTriggerType())) {
                userMarketingCrmAccountAccountRelationDao.deleteByEaAndCrmAccountId(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()), body.getObjectId());
            }
            // 作废CRM联系人
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.CONTACT.getName()) && "invalid".equals(body.getTriggerType())) {
                userMarketingCrmContactAccountRelationDao.deleteByEaAndCrmContactId(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()), body.getObjectId());
            }
            // 作废CRM会员
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.MEMBER.getName()) && "invalid".equals(body.getTriggerType())) {
                userMarketingCrmMemberRelationDao.deleteByEaAndCrmObjectId(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()), body.getObjectId());
                wxServiceUserMemberBindDao.deleteByMemberId(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()), body.getObjectId());
                wxMiniAppUserMemberBindDao.deleteByMemberId(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()), body.getObjectId());
            }
            // 作废CRM企业微信用户
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName()) && "invalid".equals(body.getTriggerType())) {
                userMarketingCrmWxWorkExternalUserRelationDao.deleteByEaAndCrmObjectId(ea, body.getObjectId());
                if (body.getBeforeTriggerData() != null && body.getBeforeTriggerData().getString(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) != null) {
                    userMarketingWxWorkExternalUserRelationDao.deleteByEaAndWxWorkExternalUserId(ea, body.getBeforeTriggerData().getString(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()));
                }
            }

            // 处理CRM微信用户
            /*
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.WECHAT.getName())){
                handleWechatObjDataAction(ea, body, objectData);
            }
             */
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.WECHAT.getName()) && "invalid".equals(body.getTriggerType())) {
                userMarketingCrmWxUserAccountRelationDao.deleteByEaAndCrmWxUserId(ea, body.getObjectId());
                if (body.getBeforeTriggerData() != null && body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName()) != null && body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()) != null) {
                    userMarketingWxServiceAccountRelationDao.deleteByEaAndWxAppIdAndWxOpenId(ea, body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName()), body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()));
                }
            }

            // 监听客服会话列表新建访客id
            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.CUSTOMER_SERVICE_SESSION_OBJ.getName()) && "u".equals(body.getTriggerType())) {
                if (objectData == null) {
                    objectData = crmMetadataManager.getById(ea, -10000, CrmObjectApiNameEnum.CUSTOMER_SERVICE_SESSION_OBJ.getName(), body.getObjectId());
                }
                //判断是否有访客id或微信用户id
                if (objectData != null && (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("visitor_id")) || (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("session_channel")) &&
                        objectData.getString("session_channel").equals("wechat") && org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("external_account"))))
                        && org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("session_status")) && objectData.getString("session_status").equals("service_end")) {
                    //如果结束变更并且是更新会话时长才记录
                    if (ObjectUtils.notEqual(body.getBeforeTriggerData().getString("session_duration"), body.getAfterTriggerData().getString("session_duration"))) {
                        log.info("CrmMqConsumer processMqMessage CustomerServiceSessionObj detail body:{}", body);
                        createMarketingDynamics(ea, objectData);
                    }
                }
            }
            //根据客户对象新增或修改,处理优惠券领券范围动态变动 todo 这里有问题,蒙牛数据量太大了,暂时注释掉
//            if (CollectionUtils.isNotEmpty(crmEvent.getBody()) && body.getEntityId().equals(CrmObjectApiNameEnum.CUSTOMER.getName()) && ("i".equals(body.getTriggerType()) || "u".equals(body.getTriggerType()))) {
//                publicCouponManager.handleCouponReceiveRangeChange(ea, body.getObjectId(), objectData, body.getTriggerType());
//            }

            if (body.getEntityId().equals(CrmObjectApiNameEnum.MEMBER.getName()) && ("i".equals(body.getTriggerType()) || "u".equals(body.getTriggerType()))) {
                memberMarketingManager.handleMemberChangeEvent(ea, body.getObjectId());
            }

            if (body.getEntityId().equals(CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName()) && ("i".equals(body.getTriggerType()) || "u".equals(body.getTriggerType()))) {
                log.info("CrmMqConsumer processMqMessage MarketingContentObj detail body:{}", body);
                //新增或者如果同步字段有变动才更新
                if("i".equals(body.getTriggerType()) || body.getAfterTriggerData().containsKey("field_original_link") || body.getAfterTriggerData().containsKey("field_original_file") || body.getAfterTriggerData().containsKey("field_promotion")
                        ||body.getAfterTriggerData().containsKey("field_share_summary") ||body.getAfterTriggerData().containsKey("field_promotion_script")
                        ||body.getAfterTriggerData().containsKey("spread_cover") ){
                    boolean updateContent = false;
                    if(Objects.nonNull(body.getBeforeTriggerData())){
                        updateContent = ObjectUtils.notEqual(body.getBeforeTriggerData().getString("field_original_link"), body.getAfterTriggerData().getString("field_original_link")) ||
                                ObjectUtils.notEqual(body.getBeforeTriggerData().getString("field_original_file"), body.getAfterTriggerData().getString("field_original_file"));
                    }
                    marketingContentObjManager.handleMarketingContentChangeEvent(ea, body.getObjectId(),body.getTriggerType(),updateContent);
                }
            }
        }
    }

    private void handleWechatObjDataAction(String ea, CrmEventDTO.Body body, ObjectData objectData) {
        String wxUnionId = null;
        if ("invalid".equals(body.getTriggerType())){
            //作废微信粉丝对象
            userMarketingCrmWxUserAccountRelationDao.deleteByEaAndCrmWxUserId(ea, body.getObjectId());
            if (body.getBeforeTriggerData() != null && body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName()) != null && body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()) != null) {
                userMarketingWxServiceAccountRelationDao.deleteByEaAndWxAppIdAndWxOpenId(ea, body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName()), body.getBeforeTriggerData().getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()));
            }
        }
        //960完成新的营销用户关联后再放开
        /*
        if ("i".equals(body.getTriggerType())){
            //新增微信粉丝对象
            ObjectData wechatObjData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), body.getObjectId());
            wxUnionId = wechatObjData.getString(CrmWechatFanFieldEnum.WX_UNION_ID.getFieldName());
        }
        if ("u".equals(body.getTriggerType())){
            //更新微信粉丝对象
            ObjectData afterTriggerData = body.getAfterTriggerData();
            log.info("handleWechatObjDataAction update ea:{}, objectId:{}, afterTriggerData:{}", ea, objectData.getId() ,afterTriggerData);
            if (afterTriggerData.get(CrmWechatFanFieldEnum.WX_UNION_ID.getFieldName()) != null ) {
                wxUnionId = (String)afterTriggerData.get(CrmWechatFanFieldEnum.WX_UNION_ID.getFieldName());
            }
        }
        if (StringUtils.isNotBlank(wxUnionId)){
            //关联通过unionId关联营销用户
            AssociationArg associationArg = new AssociationArg();
            associationArg.setAssociationId(objectData.getId());
            associationArg.setEa(ea);
            associationArg.setUserName(objectData.getName());
            associationArg.setType(ChannelEnum.CRM_WX_USER.getType());
            associationArg.setTriggerSource(ChannelEnum.CRM_WX_USER.getDescription());
            associationArg.setTriggerAction("CrmMqNewMessageHandler");
            userMarketingAccountAssociationManager.associate(associationArg);
        }
         */
    }

    private void handleMarketingPromotionSourceByFriendRecord(String ea, ObjectData leadObjectData) {
        String existMarketingPromotionSourceId = leadObjectData.getString("marketing_promotion_source_id");
        if (StringUtils.isNotBlank(existMarketingPromotionSourceId)) {
            // 线索已经存在营销推广来源了，就不用处理了
            return;
        }
        String wechatFriendId = leadObjectData.getString("wechat_friend_id");
        if (StringUtils.isBlank(wechatFriendId)) {
            return;
        }
        ObjectData friendRecordData = crmV2Manager.getDetailIgnoreError(ea, SUPER_USER, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), wechatFriendId);
        if (friendRecordData == null) {
            log.warn("handleMarketingPromotionSource friendRecordData is null, ea:{}, wechatFriendId:{}", ea, wechatFriendId);
            return;
        }
        String marketingPromotionSourceId = friendRecordData.getString("marketing_promotion_source_id");
        if (StringUtils.isBlank(marketingPromotionSourceId)) {
            // 好友记录不存在营销推广来源，不处理
            return;
        }
        try {
            ObjectData forUpdate = new ObjectData();
            forUpdate.put("_id", leadObjectData.getId());
            forUpdate.put("marketing_promotion_source_id", marketingPromotionSourceId);
            com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> result = crmV2Manager.editWithNotValidateParam(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), forUpdate, true, false, true);
            log.info("handleMarketingPromotionSourceByFriendRecord update lead ea: {} arg: {} result:{}", ea, forUpdate, result);
        } catch (Exception e) {
            log.error("handleMarketingPromotionSourceByFriendRecord update lead error, ea: {} leadObjectData: {}", ea, leadObjectData, e);
        }
    }

    private void bindWxUserAndLead(String ea, ObjectData objectData) {
        if (objectData == null || org.apache.commons.lang.StringUtils.isEmpty(objectData.getString("wechat_fan_id"))) {
            return;
        }
        //判断是不是绑定了营销通的服务号
        ObjectData wxObjectData = crmMetadataManager.findByIdV3(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), null, objectData.getString("wechat_fan_id"));
        //这里员工id传-10000会会报错,默认使用初始员工id 1000
        com.facishare.marketing.common.result.Result<List<MarketingWxServiceResult>> result = weChatServiceMarketingActivityService.listMarketingWxServiceInfo(ea, 1000, false);
        List<String> list = result.getData().stream().map(MarketingWxServiceResult::getWxAppId).collect(Collectors.toList());

        if (!list.contains(wxObjectData.getString("wx_app_id"))) {
            return;
        }
        String wxAppId = wxObjectData.getString("wx_app_id");
        String wxOpenId = wxObjectData.getString("wx_open_id");
        String mobile = objectData.getString("mobile");
        if (org.apache.commons.lang.StringUtils.isBlank(mobile)) {
            mobile = objectData.getString("tel");
        }
        userMarketingAccountRelationManager.bindWxUserAndLead(ea, wxAppId, wxOpenId, objectData.getId(), mobile, "CrmMqNewMessageHandler");
    }

    private void leadsOfChatOnLine(String ea, CrmEventDTO.Body body, ObjectData objectData) {
        String userMarketingId = null;
        String browserUserId = null;
        if (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("visitor_id"))) {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setEa(ea);
            associationArg.setAssociationId(objectData.getString("visitor_id"));
            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
            AssociationResult associationResult = userMarketingAccountRelationManager.getByEaAndKeyProperties(associationArg);
            userMarketingId = associationResult.getUserMarketingAccountId();
            browserUserId = objectData.getString("visitor_id");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("wechat_fan_id"))) {
            //判断是不是绑定了营销通的服务号
            ObjectData wxObjectData = crmMetadataManager.findByIdV3(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), null, objectData.getString("wechat_fan_id"));

            if (wxObjectData == null) {
                return;
            }
            //这里员工id传-10000会会报错,默认使用初始员工id 1000
            com.facishare.marketing.common.result.Result<List<MarketingWxServiceResult>> result = weChatServiceMarketingActivityService.listMarketingWxServiceInfo(ea, 1000, false);
            List<String> list = result.getData().stream().map(MarketingWxServiceResult::getWxAppId).collect(Collectors.toList());
            //如果bu是营销通的
            if (!list.contains(wxObjectData.getString("wx_app_id"))) {
                return;
            }
            AssociationArg associationArg = new AssociationArg();
            associationArg.setEa(ea);
            associationArg.setWxAppId(wxObjectData.getString("wx_app_id"));
            associationArg.setAssociationId(wxObjectData.getString("wx_open_id"));
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            AssociationResult associationResult = userMarketingAccountRelationManager.getByEaAndKeyProperties(associationArg);
            userMarketingId = associationResult.getUserMarketingAccountId();
            UserMarketingBrowserUserRelationEntity oneByMarketingUserId = userMarketingBrowserUserRelationDao.getNewestByMarketingUserId(ea, userMarketingId);
            if (oneByMarketingUserId != null) {
                browserUserId = oneByMarketingUserId.getBrowserUserId();
            }
            // browserUserId = browserUserRelationManager.getBrowserUserIdByWxOpenId(ea, wxObjectData.getString("wx_app_id"), wxObjectData.getString("wx_open_id"));
        }
        //新建营销推广来源,记录utm参数
        RecordUtmParamArg recordUtmParamArg = redisManager.getUtmParam(ea, browserUserId);
        if (recordUtmParamArg != null && recordUtmParamArg.checkParam()) {
            String id = createMarketingPromotionSourceObj(ea, recordUtmParamArg, userMarketingId, objectData.getOwner());
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("marketing_promotion_source_id", id);
            dataMap.put(CrmV2LeadFieldEnum.ID.getNewFieldName(), objectData.getId());
            if (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("wechat_fan_id"))) {
                dataMap.put("visitor_id", browserUserId);
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getUtmMedium())) {
                dataMap.put("utm_medium__c", recordUtmParamArg.getUtmMedium());
            }

            if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getUtmSource())) {
                dataMap.put("utm_source__c", recordUtmParamArg.getUtmSource());
            }

            if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getUtmCampaign())) {
                dataMap.put("utm_campaign__c", recordUtmParamArg.getUtmCampaign());
            }

            if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getUtmContent())) {
                dataMap.put("utm_content__c", recordUtmParamArg.getUtmContent());
            }

            if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getUtmTerm())) {
                dataMap.put("utm_term__c", recordUtmParamArg.getUtmTerm());
            }
            //如果客服没传推广渠道
            if (org.apache.commons.lang.StringUtils.isBlank(objectData.getString("promotion_channel"))) {
                dataMap.put("promotion_channel", SpreadChannelManager.promotionChannelMap.get("在线客服"));
            }
            crmV2Manager.updateObjs(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), dataMap);
            try {
                CustomizeFormDataEnroll arg = new CustomizeFormDataEnroll();
                arg.setUtmCampaig(recordUtmParamArg.getUtmCampaign());
                arg.setUtmTerm(recordUtmParamArg.getUtmTerm());
                arg.setKeywordId(recordUtmParamArg.getKeywordId());
                arg.setMarketingSourceName(recordUtmParamArg.getUtmSource());
                arg.setNewSave(true);
                utmDataManger.syncUtmFormDataToROI(ea, SUPER_USER, objectData.getId(), arg);
            } catch (Exception e) {
                log.error("更新纷享客服创建的线索utm异常， ea: {} leadId: {} arg: {}", ea, objectData.getId(), recordUtmParamArg, e);
            }
        }
        //关联线索-营销用户
        handleBrownUserAndLeadAssocaiateEvent(ea, body, "visitor_id");
    }

    public void createMarketingDynamics(String ea, ObjectData objectData) {
        //新增营销动态
        RecordActionArg arg = new RecordActionArg();
        String channelAccountName = null;
        String channelAccountPlatform = null;
        String browserUserId = null;
        // 1 根据访客id查询营销用户
        if (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("visitor_id"))) {
            browserUserId = objectData.getString("visitor_id");
            arg.setFingerPrint(objectData.getString("visitor_id"));
        } else if (objectData.getString("session_channel").equals("wechat") && org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("external_account"))) {
            //判断是不是绑定了营销通的服务号
//            ObjectData wxObjectData = crmMetadataManager.findByIdV3(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), null,objectData.getString("wechat_fan_id"));
//            if (wxObjectData == null) {
//                return;
//            }
            String wxAppId = null;
            String openId = null;
            String[] external_accounts = objectData.getString("external_account").split("\\.");
            if (external_accounts.length == 3) {
                wxAppId = external_accounts[1];
                openId = external_accounts[2];
            }
            if (org.apache.commons.lang.StringUtils.isEmpty(wxAppId) || org.apache.commons.lang.StringUtils.isEmpty(openId)) {
                return;
            }
            //这里员工id传-10000会会报错,默认使用初始员工id 1000
            com.facishare.marketing.common.result.Result<List<MarketingWxServiceResult>> result = weChatServiceMarketingActivityService.listMarketingWxServiceInfo(ea, 1000, false);
            List<String> list = result.getData().stream().map(MarketingWxServiceResult::getWxAppId).collect(Collectors.toList());
            //如果bu是营销通的
            if (!list.contains(wxAppId)) {
                return;
            }

            browserUserId = browserUserRelationManager.getBrowserUserIdByWxOpenId(ea, wxAppId, openId);
            arg.setWxOpenId(openId);
            arg.setWxAppId(wxAppId);
            channelAccountName = wxAppId;
            channelAccountPlatform = ChannelAccountPlatformEnum.OFFICIAL_ACCOUNT.getPlatform();
        }
        RecordUtmParamArg recordUtmParamArg = redisManager.getUtmParam(ea, browserUserId);
        if (recordUtmParamArg == null || !recordUtmParamArg.checkParam()) {
            return;
        }
        arg.setEa(ea);
        arg.setActionType(ActionTypeEnum.CHAT_ONLINE.getAction());
        if (org.apache.commons.lang.StringUtils.isBlank(channelAccountName)) {
            channelAccountName = recordUtmParamArg.getChannelAccountName();
            channelAccountPlatform = ChannelAccountPlatformEnum.OFFICIAL_WEBSITE.getPlatform();
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("start_time"))) {
            arg.setActionTime(Double.valueOf(objectData.getString("start_time")).longValue());
        }
        arg.setObjectId(objectData.getId());
        arg.setObjectType(ObjectTypeEnum.CHAT_ONLINE.getType());
        HashMap<String, Object> map = Maps.newHashMap();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(objectData.getString("session_duration"))) {
            map.put("actionDurationTime", getSessionDuration(objectData.getString("session_duration")));
        }
        if (objectData.getString("session_channel").equals("web_im")) {
            arg.setChannelType(MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType());
            //推广方式
            map.put(RecordActionArg.MARKETING_SCENE_KEY, MarketingSceneEnum.OFFICIAL_WEBSITE.getCode());
        }
        if (objectData.getString("session_channel").equals("wechat")) {
            arg.setChannelType(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType());
            map.put(RecordActionArg.MARKETING_SCENE_KEY, MarketingSceneEnum.OFFICIAL_ACCOUNT.getCode());
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(channelAccountName)) {
            //渠道账号
            map.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, channelAccountName);
            map.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, channelAccountPlatform);
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getIpAddress())) {
            map.put("ipAddress", recordUtmParamArg.getIpAddress());
        }
        if (recordUtmParamArg.getClient() != null) {
            map.put("client", recordUtmParamArg.getClient());
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getOperateSystem())) {
            map.put("operateSystem", recordUtmParamArg.getOperateSystem());
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(recordUtmParamArg.getBrowser())) {
            map.put("browser", recordUtmParamArg.getBrowser());
        }
        map.put("spreadChannel", SpreadChannelEnum.ONLINES_SERVICE.getCode());
        arg.setExtensionParams(map);
        actionManager.sendMarketingActivityActionToMq(ea, arg);
    }

    public String createMarketingPromotionSourceObj(String ea, RecordUtmParamArg arg, String userMarketingId, Integer owner) {

        MarketingPromotionSourceArg marketingPromotionSourceArg = new MarketingPromotionSourceArg();

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getUtmMedium())) {
            marketingPromotionSourceArg.setUtmMedium(arg.getUtmMedium());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getUtmSource())) {
            marketingPromotionSourceArg.setUtmSource(arg.getUtmSource());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getUtmCampaign())) {
            marketingPromotionSourceArg.setUtmCampaign(arg.getUtmCampaign());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getUtmContent())) {
            marketingPromotionSourceArg.setUtmContent(arg.getUtmContent());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getUtmTerm())) {
            marketingPromotionSourceArg.setUtmTerm(arg.getUtmTerm());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(userMarketingId)) {
            marketingPromotionSourceArg.setUserMarketingId(userMarketingId);
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getLandingPageUrl())) {
            marketingPromotionSourceArg.setLandingPageUrl(arg.getLandingPageUrl());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getLandingPageName())) {
            marketingPromotionSourceArg.setLandingPageName(arg.getLandingPageName());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getReferrer())) {
            marketingPromotionSourceArg.setSourceUrl(arg.getReferrer());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getLandingPageType())) {
            marketingPromotionSourceArg.setLandingPageType(arg.getLandingPageType());
        }

        marketingPromotionSourceArg.setClient(arg.getClient());
        marketingPromotionSourceArg.setOperateSystem(arg.getOperateSystem());
        marketingPromotionSourceArg.setBrowser(arg.getBrowser());

        marketingPromotionSourceArg.setChannelValue("onlineservice");
        marketingPromotionSourceArg.setFsUserId(owner);
        marketingPromotionSourceArg.setEa(ea);

        marketingPromotionSourceArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.CONSULT.getLeadModule());
        marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.FX_ONLINE_SERVICE.getDataSource());
        marketingPromotionSourceArg.setUnitId(arg.getUnitId());
        marketingPromotionSourceArg.setAccountId(arg.getAccountId());
        marketingPromotionSourceArg.setKeywordId(arg.getKeywordId());
        marketingPromotionSourceArg.setHandleUtm(true);
        log.info("创建营销推广来源,ea:[{}], param:[{}]", ea, marketingPromotionSourceArg);
        return marketingPromotionSourceObjManager.tryGetOrCreateObj(marketingPromotionSourceArg);
    }

    public void tryHandleMarketingEventTimeChange(CrmEventDTO.CrmEvent crmEvent, CrmEventDTO.Body body) {
        try {
            if (crmEvent == null || crmEvent.getBody() == null || crmEvent.getBody().isEmpty() || body == null) {
                return;
            }
            String objectApiName = body.getEntityId();
            if (!CrmObjectApiNameEnum.MARKETING_EVENT.getName().equals(objectApiName)) {
                return;
            }
            ObjectData beforeData = body.getBeforeTriggerData();
            ObjectData afterData = body.getAfterTriggerData();
            if (!CrmEventDTO.Body.UPDATE.equals(body.getTriggerType()) || beforeData == null || afterData == null) {
                return;
            }
            boolean startTimeChanged = beforeData.containsKey(MarketingEventFieldContants.BEGIN_TIME);
            if (startTimeChanged) {
                sceneTriggerManager.handleMarketingEventStartTimeChange(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()),
                        body.getObjectId(), beforeData.getLong(MarketingEventFieldContants.BEGIN_TIME), afterData.getLong(MarketingEventFieldContants.BEGIN_TIME));
            }
            boolean endTimeChanged = beforeData.containsKey(MarketingEventFieldContants.END_TIME);
            if (endTimeChanged) {
                sceneTriggerManager.handleMarketingEventEndTimeChange(eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId()),
                        body.getObjectId(), beforeData.getLong(MarketingEventFieldContants.END_TIME), afterData.getLong(MarketingEventFieldContants.END_TIME));
            }
        } catch (Exception e) {
            log.warn("Error at handle marketingEvent change", e);
        }

    }

    public void trySyncCrmDataMsgToUserMarketingAccount(CrmEventDTO.CrmEvent crmEvent, CrmEventDTO.Body body) {
        try {
            if (crmEvent == null || crmEvent.getBody() == null || crmEvent.getBody().isEmpty() || body == null) {
                return;
            }
            String objectApiName = body.getEntityId();
            if (!CrmObjectApiNameEnum.getAllUserApiNames().contains(objectApiName)) {
                return;
            }
            ObjectData beforeData = body.getBeforeTriggerData();
            ObjectData afterData = body.getAfterTriggerData();
            if (!CrmEventDTO.Body.UPDATE.equals(body.getTriggerType()) || beforeData == null || afterData == null) {
                return;
            }
            boolean nameChanged = beforeData.containsKey("name");
            boolean emailChanged = beforeData.containsKey("email");
            if (!nameChanged && !emailChanged) {
                return;
            }
            String ea = eieaConverter.enterpriseIdToAccount(crmEvent.getTenantId());
            if (MarketingStatusEnum.OPEN.getType() != settingManager.getMarketingStatus(ea)) {
                return;
            }
            String objectId = body.getObjectId();
            String changedName = afterData.getName();
            String changedEmail = afterData.getString("email");
            if (CrmObjectApiNameEnum.CUSTOMER.getName().equals(objectApiName)) {
                if (nameChanged && !Strings.isNullOrEmpty(changedName)) {
                    userMarketingCrmAccountAccountRelationDao.updateUserName(ea, objectId, changedName);
                }
                if (emailChanged) {
                    userMarketingCrmAccountAccountRelationDao.updateEmail(ea, objectId, changedEmail);
                }
            }
            if (CrmObjectApiNameEnum.CONTACT.getName().equals(objectApiName)) {
                if (nameChanged && !Strings.isNullOrEmpty(changedName)) {
                    userMarketingCrmContactAccountRelationDao.updateUserName(ea, objectId, changedName);
                }
                if (emailChanged) {
                    userMarketingCrmContactAccountRelationDao.updateEmail(ea, objectId, changedEmail);
                }
            }
            if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(objectApiName)) {
                if (nameChanged && !Strings.isNullOrEmpty(changedName)) {
                    userMarketingCrmLeadAccountRelationDao.updateUserName(ea, objectId, changedName);
                }
                if (emailChanged) {
                    userMarketingCrmLeadAccountRelationDao.updateEmail(ea, objectId, changedEmail);
                }
            }
            if (CrmObjectApiNameEnum.MEMBER.getName().equals(objectApiName)) {
                if (nameChanged && !Strings.isNullOrEmpty(changedName)) {
                    userMarketingCrmMemberRelationDao.updateUserName(ea, objectId, changedName);
                }
                if (emailChanged) {
                    userMarketingCrmMemberRelationDao.updateEmail(ea, objectId, changedEmail);
                }
            }
            if (CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(objectApiName)) {
                if (nameChanged && !Strings.isNullOrEmpty(changedName)) {
                    userMarketingCrmWxWorkExternalUserRelationDao.updateUserName(ea, objectId, changedName);
                }
            }
            if (CrmObjectApiNameEnum.WECHAT.getName().equals(objectApiName)) {
                if (nameChanged && !Strings.isNullOrEmpty(changedName)) {
                    userMarketingCrmWxUserAccountRelationDao.updateUserName(ea, objectId, changedName);
                }
            }
        } catch (Exception e) {
            log.warn("Error at sync crm data, body: {}", body, e);
        }
    }

    private String doGetUserMarketingAccountIdByObjectId(String ea, ChannelEnum channelEnum, String objectId, ObjectData objectData) {
        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setIncludeLayout(false);
        controllerDetailArg.setObjectDataId(objectId);
        controllerDetailArg.setObjectDescribeApiName(channelEnum.getApiName());
        if (objectData == null) {
            objectData = metadataControllerServiceManager.detail(new com.fxiaoke.crmrestapi.common.data.HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), channelEnum.getApiName(), controllerDetailArg);
        }
        String phone = getPhoneByChannelObject(channelEnum.getType(), objectData);
        AssociationArg associationArg = new AssociationArg();
        associationArg.setType(channelEnum.getType());
        associationArg.setEa(ea);
        associationArg.setAssociationId(objectId);
        associationArg.setPhone(phone);
        associationArg.setUserName(objectData.getName());
        associationArg.setEmail(objectData.getString("email"));
        AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
        if (result == null || StringUtils.isEmpty(result.getUserMarketingAccountId())) {
            return null;
        }
        return result.getUserMarketingAccountId();
    }

    private void doLeadCreateStartFlowInstances(String ea, String apiName, String objectId, String leadsId, ObjectData objectData) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<MarketingFlowAdditionalConfigEntity> marketingFlowAdditionalConfigs = marketingFlowAdditionalConfigDao.listMatchedCrmObjCreateFlows(ei);
        if (marketingFlowAdditionalConfigs != null && !marketingFlowAdditionalConfigs.isEmpty()) {
            log.info("doLeadCreateStartFlowInstances, ea: {} objectId: {} marketingFlowAdditionalConfigs: {}", ea, objectId, marketingFlowAdditionalConfigs);
            Set<String> marketingFlowIds = marketingFlowAdditionalConfigs.stream().filter(val -> {
                String ruleCode = val.getRuleCode();
                DataRuleExpressionPatternArg arg = new DataRuleExpressionPatternArg();
                arg.setRuleCodes(Sets.newHashSet(ruleCode));
                arg.setDataIds(Sets.newHashSet(objectId));
                arg.setEntityId(apiName);
                RuleContext ruleContext = paasRuleGroupManager.getRuleEngineContext(ei, SUPER_USER, PaasRuleGroupAppIdEnum.MARKETING_PROCESS, SceneEnum.MARKETING_PROCESS);
                arg.setContext(ruleContext);
                Result<Map<String, Map<String, Object>>> result = ruleGroupService.dataRuleExpressionPattern(new HeaderObj(), arg);
                if (!result.isSuccess()) {
                    throw new OuterServiceRuntimeException(result.getErrCode(), result.getErrMessage());
                }
                Boolean success = (Boolean) result.getResult().get(objectId).get(ruleCode);
                if (success) {
                    return true;
                }
                return false;
            }).map(MarketingFlowAdditionalConfigEntity::getBpmFlowId).collect(Collectors.toSet());
            if (!marketingFlowIds.isEmpty()) {
                String userMarketingAccountId = doGetUserMarketingAccountIdByObjectId(ea, ChannelEnum.CRM_LEAD, leadsId, objectData);
                if (StringUtils.isNotEmpty(userMarketingAccountId)) {
                    marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), marketingFlowIds);
                }
            }
        }
    }

    private void doLeadUpdateStartFlowInstances(String ea, String leadsId, String updateObjectApiName, String updateObjectFieldApiName,
                                                String updateObjectFieldValue, ObjectData objectData) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<MarketingFlowAdditionalConfigEntity> marketingFlowAdditionalConfigs = marketingFlowAdditionalConfigDao
                .listMatchedCrmObjUpdateFlows(ei, updateObjectApiName, updateObjectFieldApiName, updateObjectFieldValue);
        if (marketingFlowAdditionalConfigs != null && !marketingFlowAdditionalConfigs.isEmpty()) {
            Set<String> marketingFlowIds = marketingFlowAdditionalConfigs.stream().map(MarketingFlowAdditionalConfigEntity::getBpmFlowId).collect(Collectors.toSet());
            String userMarketingAccountId = doGetUserMarketingAccountIdByObjectId(ea, ChannelEnum.CRM_LEAD, leadsId, objectData);
            if (StringUtils.isNotEmpty(userMarketingAccountId)) {
                marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), marketingFlowIds);
            }
        }
    }

    private Integer getSessionDuration(String duration) {
        if (org.apache.commons.lang.StringUtils.isBlank(duration)) {
            return 0;
        }
        Integer resultDuration = 0;
        if (duration.contains(I18nUtil.get(I18nKeyEnum.MARK_HANDLER_CRMMQNEWMESSAGEHANDLER_1159))) {
            String[] hour = duration.split(I18nUtil.get(I18nKeyEnum.MARK_HANDLER_CRMMQNEWMESSAGEHANDLER_1159));
            resultDuration += Integer.valueOf(hour[0].trim()) * 60 * 60 * 1000;
            if (hour.length < 2) {
                return resultDuration;
            }
            duration = hour[1];
        }
        if (duration.contains(I18nUtil.get(I18nKeyEnum.MARK_HANDLER_CRMMQNEWMESSAGEHANDLER_1167))) {
            String[] minute = duration.split(I18nUtil.get(I18nKeyEnum.MARK_HANDLER_CRMMQNEWMESSAGEHANDLER_1167));
            resultDuration += Integer.valueOf(minute[0].trim()) * 60 * 1000;
            if (minute.length < 2) {
                return resultDuration;
            }
            duration = minute[1];
        }
        if (duration.contains(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3065))) {
            String[] second = duration.split(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3065));
            resultDuration += Integer.valueOf(second[0].trim()) * 1000;
        }
        return resultDuration;
    }

}