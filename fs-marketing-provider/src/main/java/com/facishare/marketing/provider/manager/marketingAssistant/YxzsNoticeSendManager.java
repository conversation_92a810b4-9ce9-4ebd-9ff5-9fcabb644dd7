/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.marketingAssistant;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.marketing.api.arg.OfficeMessageArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingObjectResult;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.QywxSpreadTypeEnum;
import com.facishare.marketing.common.typehandlers.value.SendUnionMessageArg;
import com.facishare.marketing.common.typehandlers.value.SendWxTemplateMsgArg;
import com.facishare.marketing.common.typehandlers.value.YxzsSendUnionMessageExtendArg;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.TriggerSnapshotDao;
import com.facishare.marketing.provider.dao.UserMarketingCrmLeadAccountRelationDao;
import com.facishare.marketing.provider.dao.UserMarketingWxWorkExternalUserRelationDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.yxzs.YxzsUnionMessageExtendSettingDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.entity.NoticeEntity;
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsAllSpreadWxTemplateNoticeSettingEntity;
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsUnionMessageExtendSettingEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmLeadAccountRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.EmployeeMsgSender;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.qywx.GroupSendMessageManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.vo.BatchTemplateMessageVo;
import com.facishare.wechat.proxy.model.vo.TemplateMessageVo;
import com.facishare.wechat.proxy.service.WechatMessageService;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class YxzsNoticeSendManager {

    @Autowired
    private GroupSendMessageManager groupSendMessageManager;

    @Autowired
    private EmployeeMsgSender employeeMsgSender;

    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;

    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private TriggerSnapshotDao triggerSnapshotDao;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private YxzsUnionMessageExtendSettingDAO yxzsUnionMessageExtendSettingDao;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    @Autowired
    private WechatMessageService wechatMessageService;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private UserRelationManager userRelationManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @ReloadableProperty("host")
    private String host;

    @Value("${enterprise.environment}")
    private String enterpriseEnvironment;

    /**
     * 发送互动消息
     *
     */
    public void sendUnionMsg(MarketingUserActionEvent event) {
        String ea = event.getEa();
        YxzsUnionMessageExtendSettingEntity entity = yxzsUnionMessageExtendSettingDao.getByEa(ea);
        //没设置营销助手互动通知
        if (entity == null || entity.getSendUnionMessageExtendArg() == null) {
            return;
        }
        //浏览更新，不执行通知
        if(event.isUpdate()){
            return;
        }
        if(!checkActionValue(event.getActionType())){
            log.warn("不支持的互动类型:{}", event.getActionType());
            return;
        }
        UserMarketingObjectResult result = doGetUserMarketingId(event);
        if (result == null || StringUtils.isBlank(result.getMarketingUserId())) {
            log.warn("没有找到营销助手用户关联关系,event:{}", event);
            return;
        }
        String marketingUserId = result.getMarketingUserId();
        String name = null;
        String actionName = null;
        String targetName = null;
        String unionTime = null;

        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(ea, -10000, Collections.singletonList(marketingUserId), InfoStateEnum.BRIEF);
        if (null == userMarketingAccountDataMap || userMarketingAccountDataMap.isEmpty()) {
            log.info("营销用户不存在 {}", marketingUserId);
            return;
        }
        name = userMarketingAccountDataMap.get(marketingUserId).getName();
        if (StringUtils.isEmpty(name) || name.equals("微信用户") || name.equals("暂无")) {
            log.info("仅有姓名或昵称的用户产生行为才发通知，userMarketingId {}", marketingUserId);
            return;
        }

        List<SendUnionMessageArg> sendUnionMessageArgs = Lists.newArrayList();
        String sendUnionMessageArgStr = entity.getSendUnionMessageArg();
        try {
            if (sendUnionMessageArgStr.startsWith("{")) {
                SendUnionMessageArg sendUnionMessageArg = JSONObject.parseObject(sendUnionMessageArgStr, SendUnionMessageArg.class);
                sendUnionMessageArgs.add(sendUnionMessageArg);
            } else if (sendUnionMessageArgStr.startsWith("[")) {
                sendUnionMessageArgs.addAll(JSONObject.parseArray(sendUnionMessageArgStr, SendUnionMessageArg.class));
            }
        } catch (Exception e) {
            log.warn("arg参数无效:{} ", sendUnionMessageArgStr);
        }
        if (CollectionUtils.isEmpty(sendUnionMessageArgs)) {
            return;
        }


        String marketingEventId = event.getMarketingEventId();

        //查找互动通知人员
        YxzsSendUnionMessageExtendArg sendUnionMessageExtendArg = entity.getSendUnionMessageExtendArg();
        List<String> userIds = new ArrayList<>();
        for (SendUnionMessageArg sendUnionMessageArg : sendUnionMessageArgs) {
            String crmObjectApiName = sendUnionMessageArg.getLabel();
            String fieldName = sendUnionMessageArg.getFieldName();//对象字段名或“指定人员”
            if ("SPECIFIED".equals(fieldName)) {
                // 指定人员
                String userIdString = sendUnionMessageExtendArg.getUserIdString();
                if (StringUtils.isNotEmpty(userIdString)) {
                    userIds.addAll(Arrays.asList(userIdString.split(",")));
                }
            } else if ("PROMOTER".equals(fieldName)) {
                // 活动推广人
                Integer spreadFsUid = event.getSpreadFsUid();
                if (spreadFsUid != null) {
                    userIds.add(String.valueOf(spreadFsUid));
                }
            } else if ("MARKETING_OWNER".equals(fieldName) || "MARKETING_TEAM".equals(fieldName)) {
                // 活动负责人与有编辑权限的相关团队成员
                if (StringUtils.isNotBlank(marketingEventId)) {
                    ObjectData marketingEvent = crmV2Manager.getDetailIgnoreError(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
                    if (marketingEvent != null && "MARKETING_OWNER".equals(fieldName)) {
                        userIds.add(String.valueOf(marketingEvent.getOwner()));
                    } else if (marketingEvent != null && "MARKETING_TEAM".equals(fieldName)) {
                        JSONArray relevantTeamList = JSONArray.parseArray(JSONArray.toJSONString(marketingEvent.get("relevant_team")));
                        for (int i = 0; i < relevantTeamList.size(); i++) {
                            JSONObject relevantTeam = relevantTeamList.getJSONObject(i);
                            if ("2".equals(relevantTeam.getString("teamMemberPermissionType")) && null != relevantTeam.getJSONArray("teamMemberEmployee")) {
                                userIds.addAll(relevantTeam.getJSONArray("teamMemberEmployee").toJavaList(String.class));
                            }
                        }
                    }
                }
            } else if ("ALL_WECHATEXTERNALUSER_ADDER".equals(fieldName)) {
                // 所有添加客户企微好友的员工
                String crmId = crmV2Manager.getCrmIdByMarketingUserId(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), ea, marketingUserId);
                if (StringUtils.isNotBlank(crmId)) {
                    HashMap<String, Object> queryParams = Maps.newHashMap();
                    queryParams.put("external_user_id", crmId);
                    List<ObjectData> wechatFriendsRecordList = crmV2Manager.queryObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), queryParams);
                    if (CollectionUtils.isNotEmpty(wechatFriendsRecordList)) {
                        userIds.addAll(wechatFriendsRecordList.stream().filter(e -> e.get("user_id") != null).map(e -> e.getString("user_id")).collect(Collectors.toList()));
                    }
                }
            } else {
                CrmObjectApiNameEnum crmObjectApiNameEnum = CrmObjectApiNameEnum.fromName(crmObjectApiName);
                if (crmObjectApiNameEnum == null) {
                    log.warn("无此预设对象:{} ", crmObjectApiName);
                    continue;
                }
                String crmId = crmV2Manager.getCrmIdByMarketingUserId(crmObjectApiName, ea, marketingUserId);
                if (StringUtils.isBlank(crmId)) {
                    log.warn("用户无该对象:{} ", crmObjectApiName);
                    continue;
                }
                ObjectData detail = crmV2Manager.getOneByList(ea, -10000, crmObjectApiName, crmId);
                if (detail == null) {
                    log.warn("无该对象:{} crmId:{}", crmObjectApiName,  crmId);
                    continue;
                }
                if (detail.get(sendUnionMessageArg.getFieldName()) != null) {
                    userIds.addAll((List<String>) detail.get(sendUnionMessageArg.getFieldName()));
                }
            }
        }
        log.warn("根据参数查出员工是那些:{} ", userIds);
        Set<Integer> fsUserIds = userIds.stream().filter(id -> !"-10000".equals(id)).map(Integer::valueOf).collect(Collectors.toSet());
        // 条件过滤
        if (CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getUserIds()) || CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getDepartmentIds())) {
            List<Integer> filterUserIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getUserIds())) {
                filterUserIds.addAll(sendUnionMessageExtendArg.getUserIds());
            }
            if (CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getDepartmentIds())) {
                List<Integer> employeeIdsByCircleIds = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, sendUnionMessageExtendArg.getDepartmentIds());
                if (CollectionUtils.isNotEmpty(employeeIdsByCircleIds)) {
                    filterUserIds.addAll(employeeIdsByCircleIds);
                }
            }
            fsUserIds = fsUserIds.stream().filter(filterUserIds::contains).collect(Collectors.toSet());
        }

        if (fsUserIds.isEmpty()) {
            log.warn("无可发送用户");
            return;
        }

        actionName = getActionName(event.getActionType());
        targetName = objectManager.getObjectName(event.getObjectId(), event.getObjectType(), event.getEa());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        unionTime = dateFormat.format(event.getActionTime());
        String cmlUrl = "ava://marketing_app/pkgs/pkg-marketing-user/pages/customer-detail/customer-detail?objectId=" + marketingUserId + "&targetId="+ result.getTargetId()
                + "&targetType="+ result.getTargetType()+ "&associationId=";
        UserMarketingCrmLeadAccountRelationEntity leadEntity = userMarketingCrmLeadAccountRelationDao.getEachDataLastByUserMarketingId(ea, marketingUserId);
        if (leadEntity != null) {
            String leadId = leadEntity.getCrmLeadId();
            ArrayList<String> leadsIds = new ArrayList<>(1);
            leadsIds.add(leadId);
            Map<String, String> weChatAvatarUrlMap = userMarketingAccountManager.getLeadAndWeChatAvatarUrlMap(ea, -10000, leadsIds);
            cmlUrl += leadId + "&fromLead=true&avatar=" + weChatAvatarUrlMap.get(leadId);
        }
        List<OfficeMessageArg.LabelWarp> content = new LinkedList<>();
        String title = "你有一个客户产生新的互动";
        String description = null;
        content.add(OfficeMessageArg.LabelWarp.newInstance("名称", "qx.ot.mark.name", name));
        content.add(OfficeMessageArg.LabelWarp.newInstance("互动行为", "qx.ot.mark.interactive_behavior", actionName, getActionI18nKey(event.getActionType())));
        content.add(OfficeMessageArg.LabelWarp.newInstance("互动内容", "qx.ot.mark.interactive_content", targetName));
        content.add(OfficeMessageArg.LabelWarp.newInstance("互动时间", "qx.ot.mark.interactive_time", unionTime));

        String fsUrl = host + "/ec/kemai/release/notice.html?pageName=kanban_detail" + "&contentType=" + NoticeContentTypeEnum.SEND_UNION_MSG.getType() + "&objectId=" + marketingUserId
                + "&targetId="+ result.getTargetId() + "&targetType="+ result.getTargetType();
        String wxWorkExternalUserId = null;
        List<UserMarketingWxWorkExternalUserRelationEntity> entities = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(marketingUserId));
        if (CollectionUtils.isNotEmpty(entities)) {
            wxWorkExternalUserId = entities.get(0).getWxWorkExternalUserId();
        }
        String qywxUrl = "/pkgs/pkg-marketing-user/pages/customer-detail/customer-detail?objectId=" + marketingUserId + "&targetId="+ result.getTargetId()
                + "&targetType="+ result.getTargetType();
        if (wxWorkExternalUserId != null) {
            qywxUrl += "&externalUserId=" + wxWorkExternalUserId;
        }
        OfficeMessageArg.ContentWarp infoTitle = new OfficeMessageArg.ContentWarp();
        infoTitle.setContent(title);
        infoTitle.setInternationalContent("qx.ot.mark.interactive_title");
        employeeMsgSender.sendUnionMessage(ea, -10000, fsUserIds, infoTitle, description, content, fsUrl, cmlUrl, qywxUrl);
        //判断是否开启了企微h5(营销助手通知)
        boolean openQywxH5Notice = groupSendMessageManager.openQywxH5Notice(ea);
        if (openQywxH5Notice) {
            List<Integer> allUserIds = new ArrayList<>(fsUserIds);
            String url = host + "/proj/page/marketing/" + ea + "#/pkgs/pkg-marketing-user/pages/customer-detail/customer-detail?ea=" + ea + "&objectId=" + marketingUserId
                    + "&targetId="+ result.getTargetId() + "&targetType="+ result.getTargetType();
            if (wxWorkExternalUserId != null) {
                url += "&externalUserId=" + wxWorkExternalUserId;
            }
            groupSendMessageManager.sendQywxH5AgentMessage(ea, allUserIds, content, url);
        }
//        //插入记录或者更新最新通知时间
//        insertOrUpdateUserMsgRecord(ea, Lists.newArrayList(fsUserIds),new Date(event.getActionTime()));

        //没开设置
        if(entity.getOpenWxNotice() ==null || entity.getOpenWxNotice()==0 || entity.getWxTemplateMsg()==null || StringUtils.isEmpty(entity.getWxAppId())){
            return;
        }

        List<UserRelationEntity> relationEntities = userRelationManager.getByFsUserIdList(ea, Lists.newArrayList(fsUserIds));
        if(relationEntities.isEmpty()){
           return;
        }
        List<String> wxObjIds = relationEntities.stream().map(UserRelationEntity::getWechatFanId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wxObjIds)) {
            return;
        }
        List<String> wxOpenIds = Lists.newArrayList();
        PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
        paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        paasQueryArg.addFilter(CrmWechatFanFieldEnum.ID.getFieldName(), PaasAndCrmOperatorEnum.IN.getCrmOperator(), wxObjIds);

        paasQueryArg.addFilter(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(),PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(entity.getWxAppId()));
        paasFilterArg.setQuery(paasQueryArg);
        paasFilterArg.setSelectFields(Lists.newArrayList(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()));
        InnerPage<ObjectData> wechatFriendData = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
        if (wechatFriendData != null && CollectionUtils.isNotEmpty(wechatFriendData.getDataList())) {
            wxOpenIds = wechatFriendData.getDataList().stream().map(objectData -> objectData.getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(wxOpenIds)){
            log.info("未查询到微信粉丝信息");
            return;
        }
        //todo 分享员工转微信粉丝
        Map<String,String> params = Maps.newHashMap();
        params.put("{用户名称}",name);
        params.put("{互动行为}",actionName);
        params.put("{互动内容}",targetName);
        params.put("{互动时间}",unionTime);
        String page = "/pkgs/pkg-marketing-user/pages/customer-detail/customer-detail?objectId="+marketingUserId+"&ea="+ea + "&targetId="+ result.getTargetId()
                + "&targetType="+ result.getTargetType();
        params.put("page",page);
        String appIdByEa = eaWechatAccountBindDao.getWxAppIdByEa(ea, "YXT");
        params.put("miniAppId",appIdByEa);
        sendWxTemplateMsg(entity.getWxAppId(),entity.getWxTemplateMsg(),wxOpenIds,params);
    }

    private String getActionName(Integer actionType) {
        String actionName;
        //报名会议特殊
        if (actionType == MarketingUserActionType.ENROLL_CONFERENCE.getActionType()) {
//            actionName = "报名会议";
            actionName = I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3467);
        }else {
            //构建通知内容
            Map<Integer, String> actionEnumMap = userMarketingAccountManager.fromActionEnum();
            actionName = actionType  < 1000000 ? actionEnumMap.get(actionType + 1000000)
                    : actionEnumMap.get(actionType);
        }
        return actionName;
    }

    private String getActionI18nKey(Integer actionType) {
        String actionName;
        //报名会议特殊
        if (actionType == MarketingUserActionType.ENROLL_CONFERENCE.getActionType()) {
//            actionName = "报名会议";
            actionName = I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3467.getI18nKey();
        }else {
            //构建通知内容
            Map<Integer, String> actionEnumMap = userMarketingAccountManager.getI8nKeyFromActionEnum();
            actionName = actionType  < 1000000 ? actionEnumMap.get(actionType + 1000000)
                    : actionEnumMap.get(actionType);
        }
        return actionName;
    }


    /**
     * 全员推广，会员推广发模板消息
     * @param title
     * @param description
     * @param noticeEntity
     * @param marketingActivityId
     * @param finalMultiple
     * @param entity
     * @param fsUserIds
     * @param marketingEvenId
     */

    public void sendAllSpreardWxTemplateMsg(String title, String description, NoticeEntity noticeEntity, String marketingActivityId, boolean finalMultiple, YxzsAllSpreadWxTemplateNoticeSettingEntity entity, List<Integer> fsUserIds, String marketingEvenId) {
        List<UserRelationEntity> relationEntities = userRelationManager.getByFsUserIdList(noticeEntity.getFsEa(), fsUserIds);
        if(relationEntities.isEmpty()){
            return;
        }
        List<String> wxObjIds = relationEntities.stream().map(UserRelationEntity::getWechatFanId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wxObjIds)) {
            return;
        }
        List<String> wxOpenIds = Lists.newArrayList();
        PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
        paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        paasQueryArg.addFilter(CrmWechatFanFieldEnum.ID.getFieldName(), PaasAndCrmOperatorEnum.IN.getCrmOperator(), wxObjIds);
        paasQueryArg.addFilter(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(),PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(entity.getWxAppId()));
        paasFilterArg.setQuery(paasQueryArg);
        paasFilterArg.setSelectFields(Lists.newArrayList(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()));
        InnerPage<ObjectData> wechatFriendData = crmV2Manager.concurrentListCrmObjectByFilterV3(noticeEntity.getFsEa(), -10000, paasFilterArg);
        if (wechatFriendData != null && CollectionUtils.isNotEmpty(wechatFriendData.getDataList())) {
            wxOpenIds = wechatFriendData.getDataList().stream().map(objectData -> objectData.getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(wxOpenIds)){
            log.info("未查询到微信粉丝信息");
            return;
        }

        Map<String,String> params = Maps.newHashMap();
        params.put("{推广任务标题}", title);
        params.put("{宣传语}", description);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日 HH:mm");
        String strStart = formatter.format(noticeEntity.getStartTime());
        String strEnd = formatter.format(noticeEntity.getEndTime());
        params.put("{任务时间范围}",strStart+"~"+strEnd);
        //同一个活动再次推广场景
        if(StringUtils.isBlank(marketingEvenId)){
            MarketingActivityExternalConfigEntity activity = marketingActivityExternalConfigDao.getByMarketingActivityId(noticeEntity.getFsEa(), marketingActivityId);
            marketingEvenId = activity.getMarketingEventId();
        }
        MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(noticeEntity.getFsEa(), -10000, marketingEvenId);
        params.put("{市场活动}",marketingEventData.getName());

        String page = null;
        String appIdByEa = eaWechatAccountBindDao.getWxAppIdByEa(noticeEntity.getFsEa(), "YXT");
        if (finalMultiple) {
            page = "pkgs/pkg-spread/pages/multi-material/multi-material?marketingActivityId="+ marketingActivityId;
        } else {
            NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(noticeEntity.getContentType());
            int objectType = noticeContentTypeEnum.toObjectType();
            Map<String, String> param = Maps.newHashMap();
            param.put("objectId", noticeEntity.getContent());
            param.put("objectType", objectType + "");
            param.put("isGroupSend", 1 + "");
            param.put("marketingActivityId", marketingActivityId);
            param.put("spreadType", QywxSpreadTypeEnum.ALL_SPREAD.getType() + "");
            page = "/pages/share/share?" + httpManager.transformUrlParams(param);
            page += "&showShareBar=" + true;
        }
        WxAppInfoEnum wxAppInfo = WxAppInfoEnum.getByAppId(appIdByEa);
        if (WxAppInfoEnum.MankeepPro == wxAppInfo) {
            String hostType = "&hostType=" + enterpriseEnvironment;
            page += hostType;
        }
        params.put("page",page);
        params.put("miniAppId",appIdByEa);
        sendWxTemplateMsg(entity.getWxAppId(), entity.getWxTemplateMsg(),wxOpenIds,params);
    }

    /**
     *
     * @param wxAppId 公众号id
     * @param wxTemplateMsg 模板信息
     * @param wxOpenIds  微信openid
     * @param params  额外参数(替换参数，跳转参数)
     */
    public void sendWxTemplateMsg(String wxAppId, SendWxTemplateMsgArg wxTemplateMsg, List<String> wxOpenIds, Map<String,String> params) {
        if(MapUtils.isEmpty(params)){
            log.warn("params is null");
            return;
        }
        TemplateMessageVo.Data data = new TemplateMessageVo.Data();
        data.setFirst(new TemplateMessageVo.First(wxTemplateMsg.getMsgBody().getTitle(), wxTemplateMsg.getMsgBody().getTitleColor()));
        List<TemplateMessageVo.KeyNote> keyNotes = Lists.newArrayList();
        for (SendWxTemplateMsgArg.MsgItem msgItem : wxTemplateMsg.getMsgBody().getDataList()){
            if (StringUtils.equals(msgItem.getKey(), "first")){
                if (StringUtils.isNotEmpty(msgItem.getValue())) {
                    data.setFirst(new TemplateMessageVo.First(msgItem.getValue(), msgItem.getColor()));
                }
            } else if (StringUtils.equals(msgItem.getKey(), "remark")){
                if (StringUtils.isNotEmpty(msgItem.getValue())){
                    TemplateMessageVo.Remark remark = new TemplateMessageVo.Remark(msgItem.getValue(), msgItem.getColor());
                    data.setRemark(remark);
                }
            }else {
                keyNotes.add(new TemplateMessageVo.KeyNote(msgItem.getKey(), replaceVariables(msgItem.getValue(),params), msgItem.getColor()));
            }
        }
        data.setKeyNotes(keyNotes);
        TemplateMessageVo.Data dataToSend = BeanUtil.copyByGson(data, TemplateMessageVo.Data.class);

        BatchTemplateMessageVo batchTemplateMessageVo = new BatchTemplateMessageVo(wxOpenIds, wxAppId, wxTemplateMsg.getWxTemplateMsgId(), dataToSend);
        TemplateMessageVo.MiniProgram miniProgram = new TemplateMessageVo.MiniProgram();
        miniProgram.setMiniAppId(params.get("miniAppId"));
        miniProgram.setPagePath(params.get("page"));
        batchTemplateMessageVo.setMiniProgram(miniProgram);
        ModelResult<Void> mr = wechatMessageService.sendTemplateMessageByOpenIdsAsync(batchTemplateMessageVo);
        if (!mr.isSuccess()){
            log.error("sendTemplateMessageByOpenIdsAsync error:{}", mr.getErrorMessage());
        }

    }


    public static String replaceVariables(String a, Map<String, String> params) {
        if(StringUtils.isBlank(a)){
            return null;
        }
        Pattern pattern = Pattern.compile("\\{[^}]+}");
        Matcher matcher = pattern.matcher(a);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group();
            String replacement = params.getOrDefault(key, "");
            if(replacement!=null){
                matcher.appendReplacement(result, Matcher.quoteReplacement(replacement)); // 进行替换
            }
        }
        matcher.appendTail(result); // 将剩余的部分添加到结果中
        return result.toString();
    }


    public UserMarketingObjectResult doGetUserMarketingId(MarketingUserActionEvent event) {
        UserMarketingObjectResult result = new UserMarketingObjectResult();
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(event.getEa());
        associationArg.setPhone(event.getPhone());
        if (event.getChannelType().equals(MarketingUserActionChannelType.MANKEEP.getChannelType())) {
            associationArg.setType(ChannelEnum.MINIAPP.getType());
            associationArg.setAssociationId(event.getUid());
            associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
            result.setTargetId(event.getUid());
            result.setTargetType(ChannelEnum.MINIAPP.getType());
        } else if (event.getChannelType().equals(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType())) {
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            associationArg.setAssociationId(event.getWxOpenId());
            associationArg.setWxAppId(event.getWxAppId());
            associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
            result.setTargetId(event.getWxAppId()+":"+event.getWxOpenId());
            result.setTargetType(ChannelEnum.WX_SERVICE.getType());
        } else if (event.getChannelType().equals(MarketingUserActionChannelType.H5.getChannelType()) || event.getChannelType().equals(MarketingUserActionChannelType.EMAIL.getChannelType()) || event.getChannelType().equals(MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType()) || event.getChannelType().equals(MarketingUserActionChannelType.SMS.getChannelType()) ) {
            associationArg.setAssociationId(event.getFingerPrint());
            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
            associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
            result.setTargetId(event.getFingerPrint());
            result.setTargetType(ChannelEnum.BROWSER_USER.getType());
        }else {
            return null;
        }
        associationArg.setTriggerAction("sendUnionMsg");
        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
        if (associationResult == null) {
            return result;
        }
        result.setMarketingUserId(associationResult.getUserMarketingAccountId());
        return result;
    }
    private boolean checkActionValue(Integer actionType) {
        if (actionType == null) {
            return false;
        }
        //报名会议特殊
        if (actionType == MarketingUserActionType.ENROLL_CONFERENCE.getActionType()) {
            return true;
        }
        ActionTypeEnum actionTypeEnum = ActionTypeEnum.ofType(actionType);
        if(actionTypeEnum == null || YxzsUnionActionTypeEnum.ofLabel(actionTypeEnum.getActionCName()) == null){
            return false;
        }
        return true;
    }
//    private int insertOrUpdateUserMsgRecord(String ea,List<Integer> userIds, Date latestNoticeTime) {
//        List<YxzsStaffSendUnionMsgRecordEntity> entities = userIds.stream().map(userId -> {
//            YxzsStaffSendUnionMsgRecordEntity entity = new YxzsStaffSendUnionMsgRecordEntity();
//            entity.setId(UUIDUtil.getUUID());
//            entity.setFsUserId(userId);
//            entity.setEa(ea);
//            entity.setLatestNoticeTime(latestNoticeTime);
//            return entity;
//        }).collect(Collectors.toList());
//        return yxzsStaffSendUnionMsgRecordDAO.batchInsertOrUpdate(entities);
//    }
}
