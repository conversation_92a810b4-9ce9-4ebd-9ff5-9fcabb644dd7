package com.facishare.marketing.provider.dao.user;

import com.facishare.marketing.api.arg.userrelation.UserRelationQueryArg;
import com.facishare.marketing.provider.dto.UserRelationDTO;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface UserRelationDAO {

    @Insert("<script>"
            + "INSERT INTO user_relation"
            + " (id, ea, fs_user_id, ding_user_id, qywx_user_id, wechat_fan_id, uid, member_id, wx_union_id, open_id,original_user_type, create_time, update_time, user_name, mobile, email, status, outer_tenant_id, outer_uid, type)"
            + " VALUES "
            + "<foreach collection='userRelationEntityList' item='obj'  separator=','> "
            + " (#{obj.id}, #{obj.ea}, #{obj.fsUserId}, #{obj.dingUserId}, #{obj.qywxUserId}, #{obj.wechatFanId}, #{obj.uid}, #{obj.memberId}, #{obj.wxUnionId}, #{obj.openId}, #{obj.originalUserType}, now(), now() , #{obj.userName}, #{obj.mobile}, #{obj.email}, #{obj.status}, #{obj.outerTenantId}, #{obj.outerUid}, #{obj.type})"
            + "</foreach>"
            + "</script>")
    int batchInsert(@Param("userRelationEntityList") List<UserRelationEntity> userRelationEntityList);

    @Select("select * from user_relation where ea = #{ea} and fs_user_id = #{fsUserId} and status = 'NORMAL'")
    UserRelationEntity getByFsUserId(@Param("ea") String ea, @Param("fsUserId") int fsUserId);

    @Select("<script>"
            + "select * from user_relation where ea = #{ea} and status = 'NORMAL' and "
            + " qywx_user_id = ANY(ARRAY "
            +   "<foreach collection = 'qywxUserIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getByQywxUserIdList(@Param("ea") String ea, @Param("qywxUserIdList") List<String> qywxUserIdList);


    @Select("<script>"
            + "select * from user_relation where status = 'NORMAL' and "
            + " uid = ANY(ARRAY "
            +   "<foreach collection = 'uidList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getByUidList(@Param("uidList") List<String> uidList,@Param("ea") String ea);

    @Select("<script>"
            + "select * from user_relation where ea = #{ea} and status = 'NORMAL' and "
            + " ding_user_id = ANY(ARRAY "
            +   "<foreach collection = 'dingUserIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getByDingUserIdList(@Param("ea") String ea, @Param("dingUserIdList") List<String> dingUserIdList);

    @Update("<script>"
            + "update user_relation\n"
            + "<set>\n"
            + "<if test=\"obj.ea != null\">\n"
            + "\"ea\" = #{obj.ea},\n"
            + "</if>\n"
            + "<if test=\"obj.fsUserId != null\">\n"
            + "\"fs_user_id\" = #{obj.fsUserId},\n"
            + "</if>\n"
            + "<if test=\"obj.qywxUserId != null\">\n"
            + "\"qywx_user_id\" = #{obj.qywxUserId},\n"
            + "</if>\n"
            + "<if test=\"obj.dingUserId != null\">\n"
            + "\"ding_user_id\" = #{obj.dingUserId},\n"
            + "</if>\n"
            + "<if test=\"obj.wechatFanId != null\">\n"
            + "\"wechat_fan_id\" = #{obj.wechatFanId},\n"
            + "</if>\n"
            + "<if test=\"obj.memberId != null\">\n"
            + "\"member_id\" = #{obj.memberId},\n"
            + "</if>\n"
            + "<if test=\"obj.uid != null\">\n"
            + "\"uid\" = #{obj.uid},\n"
            + "</if>\n"
            + "<if test=\"obj.wxUnionId != null\">\n"
            + "\"wx_union_id\" = #{obj.wxUnionId},\n"
            + "</if>\n"
            + "<if test=\"obj.openId != null\">\n"
            + "\"open_id\" = #{obj.openId},\n"
            + "</if>\n"
            + "<if test=\"obj.userName != null\">\n"
            + "\"user_name\" = #{obj.userName},\n"
            + "</if>\n"
            + "<if test=\"obj.mobile != null\">\n"
            + "\"mobile\" = #{obj.mobile},\n"
            + "</if>\n"
            + "<if test=\"obj.email != null\">\n"
            + "\"email\" = #{obj.email},\n"
            + "</if>\n"
            + "<if test=\"obj.status != null\">\n"
            + "\"status\" = #{obj.status},\n"
            + "</if>\n"
            + "<if test=\"obj.outerUid != null\">\n"
            + "\"outer_uid\" = #{obj.outerUid},\n"
            + "</if>\n"
            + "<if test=\"obj.outerTenantId != null\">\n"
            + "\"outer_tenant_id\" = #{obj.outerTenantId},\n"
            + "</if>\n"
            + "<if test=\"obj.type != null\">\n"
            + "\"type\" = #{obj.type},\n"
            + "</if>\n"
            + "\"update_time\" = now()"
            + " </set>"
            + "  where \"id\" = #{obj.id}"
            +"</script>")
    int update(@Param("obj") UserRelationEntity userRelationEntity);

    @Update("update user_relation set fs_user_id = #{obj.fsUserId}, qywx_user_id = #{obj.qywxUserId}, ding_user_id = #{obj.dingUserId}, \n"
            + " wechat_fan_id = #{obj.wechatFanId}, member_id = #{obj.memberId}, uid = #{obj.uid}, wx_union_id = #{obj.wxUnionId}, open_id = #{obj.openId}, outer_tenant_id = #{obj.outerTenantId}, outer_uid = #{obj.outerUid},\n"
            + " update_time = now()"
            + " where ea = #{obj.ea} and id = #{obj.id}"
           )
    int replaceId(@Param("obj") UserRelationEntity userRelationEntity);

    @Select("select * from user_relation where ea = #{ea} and member_id = #{memberId} and status = 'NORMAL'")
    UserRelationEntity getByMemberId(@Param("ea") String ea, @Param("memberId") String memberId);

    @Select("select * from user_relation where ea = #{ea} and uid = #{uid} and status = 'NORMAL'")
    UserRelationEntity getByEaAndUid(@Param("ea") String ea, @Param("uid")  String uid);

    @Select("select * from user_relation where uid = #{uid} and status = 'NORMAL' order by create_time desc limit 1")
    UserRelationEntity getByUid(String uid,@Param("ea") String ea);

    @Select("SELECT MAX(fs_user_id) FROM user_relation WHERE ea = #{ea} AND fs_user_id >= 300000000 AND fs_user_id < 400000000 and status = 'NORMAL'")
    Integer getMaxMemberVirtualUserId(@Param("ea") String ea);

    @Select("SELECT MAX(fs_user_id) FROM user_relation WHERE ea = #{ea} AND fs_user_id >= 400000000 AND fs_user_id < 500000000 and status = 'NORMAL'")
    Integer getMaxPublicEmployeeVirtualUserId(@Param("ea") String ea);

    @Select("<script>"
            + "select * from user_relation where ea = #{ea} and status = 'NORMAL' and "
            + " member_id = ANY(ARRAY "
            +   "<foreach collection = 'memberIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getByMemberIdList(@Param("ea") String ea, @Param("memberIdList") List<String> memberIdList);

    @Select("<script>"
            + "update user_relation set status = 'DELETED'  where ea = #{ea}  "
            + " and id = ANY(ARRAY "
            +   "<foreach collection = 'idList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    Integer batchDeleteById(@Param("ea") String ea, @Param("idList") List<String> idList);

    @Select("<script>"
            + "select * from user_relation where ea = #{ea} and status = 'NORMAL' and "
            + " fs_user_id = ANY(ARRAY "
            +   "<foreach collection = 'userIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getByFsUserIdList(@Param("ea") String ea, @Param("userIdList") List<Integer> userIdList);

    @Select("<script>"
            + "select fs_user_id from user_relation where ea = #{ea} and status = 'NORMAL'  and fs_user_id is not null "
            + "<if test=\"includeVirtualUser == false\">\n"
            + " and fs_user_id <![CDATA[ < ]]> 100000000"
            + "</if>\n"
            + "</script>")
    List<Integer> getAllFsUserIdWithoutVirtualUser(@Param("ea") String ea , @Param("includeVirtualUser") boolean includeVirtualUser);


    @Select("select member_id from user_relation where ea = #{ea} and member_id is not null and status = 'NORMAL'")
    List<String> getAllMemberId(@Param("ea") String ea);

    @Select("<script>"
            + "select * from user_relation where ea = #{ea} and status = 'NORMAL' and "
            + " id = ANY(ARRAY "
            +   "<foreach collection = 'idList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getByIdList(@Param("ea") String ea, @Param("idList") List<String> idList);

    @Select("<script>"
            + "select * from user_relation where status = 'NORMAL' "
            + " <if test=\"ea != null and ea != ''\">\n"
            + "    and ea = #{ea} \n"
            + " </if>\n"
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "    and id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<UserRelationEntity> scanByLastId(@Param("ea") String ea, @Param("lastId") String lastId, @Param("limit") int limit);

    @Update("update user_relation set wechat_fan_id = #{wechatFanObjId}, update_time = now() where ea = #{ea} and id = #{id}")
    int updateWechatFanObjId(@Param("ea") String ea, @Param("id") String id, @Param("wechatFanObjId") String wechatFanObjId);

    @Update("update user_relation set uid = #{newUid}, update_time = now() where ea = #{ea} and uid = #{oldUid}")
    int updateUid(@Param("ea") String ea, @Param("oldUid") String oldUid, @Param("newUid") String newUid);

    @Update("update user_relation set qywx_user_id = #{qywxUserId}, update_time = now() where ea = #{ea} and id = #{id}")
    int updateQywxUserId(@Param("ea") String ea, @Param("id") String id, @Param("qywxUserId") String qywxUserId);

    @Update("update user_relation set fs_user_id = #{fsUserId}, update_time = now() where ea = #{ea} and id = #{id}")
    int updateFsUserId(@Param("ea") String ea, @Param("id") String id, @Param("fsUserId") Integer fsUserId);

    @Update("update user_relation set ding_user_id = #{dingUserId}, update_time = now() where ea = #{ea} and id = #{id}")
    int updateDingUserId(@Param("ea") String ea, @Param("id") String id, @Param("dingUserId") String dingUserId);

    @Update("update user_relation set member_id = #{memberId}, update_time = now() where ea = #{ea} and id = #{id}")
    int updateMemberId(@Param("ea") String ea, @Param("id") String id, @Param("memberId") String memberId);

    @Select("<script>"
            + "select ur.*, cd.uid cardUid from user_relation ur left join card cd on ur.uid = cd.uid where ur.ea = #{arg.ea}  and ur.status = 'NORMAL'  "
            + " <if test=\"arg.keyword != null and arg.keyword != ''\">\n"
            + "    and (ur.user_name like concat('%', #{arg.keyword}, '%') or ur.mobile like concat('%', #{arg.keyword}, '%') or ur.email like concat('%', #{arg.keyword}, '%') ) \n"
            + " </if>\n"
            + " <if test=\"arg.cardStatus != null and arg.cardStatus != '' \">\n"
                + " <if test=\" arg.cardStatus == 'OPEN' \">\n"
                + "    and cd.uid is not null \n"
                + " </if>\n"
                + " <if test=\" arg.cardStatus == 'NOT_OPEN' \">\n"
                + "    and cd.uid is null \n"
                + " </if>\n"
            + " </if>\n"
            + " <if test=\"arg.originUserType != null and arg.originUserType != ''\">\n"
            +      " and original_user_type = #{arg.originUserType} "
            + " </if>\n"
            + " <if test=\"arg.userType != null and arg.userType != '' and arg.userType != 'ALL' \">\n"
            +      " and ur.type = #{arg.userType} "
            + " </if>\n"
            + " order by update_time desc"
            + "</script>"
    )
    List<UserRelationDTO> listUserRelation(@Param("arg") UserRelationQueryArg arg, @Param("page") Page<UserRelationDTO> page,@Param("ea") String ea);

    @Select("select * from user_relation where ea = #{ea} and uid = #{uid} and fs_user_id = #{fsUserId} and status = 'NORMAL' ")
    UserRelationEntity getByFsUserIdAndUid(@Param("ea") String ea, @Param("fsUserId") int fsUserId, @Param("uid") String uid);

    @Select("<script>"
            + "select * from user_relation where ea = #{ea} and status = 'NORMAL' and "
            + " outer_tenant_id = ANY(ARRAY "
            +   "<foreach collection = 'outTenantIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + " and outer_uid = ANY(ARRAY "
            +   "<foreach collection = 'outerUidList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getByOutTenantIdAndOuterUid(@Param("ea") String ea, @Param("outTenantIdList") List<Long> outTenantIdList, @Param("outerUidList") List<Long> outerUidList);

    @Select("<script>"
            + "select * from user_relation where ea = #{ea} and status = 'NORMAL' and outer_uid is not null and outer_tenant_id is not null "
            + " and mobile = ANY(ARRAY "
            +   "<foreach collection = 'mobileList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<UserRelationEntity> getPartnerRelationByMobileList(@Param("ea") String ea, @Param("mobileList") List<String> mobileList);

    @Select("select distinct ea from user_relation where status = 'NORMAL'")
    List<String> getAllEa();

    @Select("<script>"
            + "select fs_user_id from user_relation where ea = #{ea} and status = 'NORMAL' and "
            + " outer_tenant_id = ANY(ARRAY "
            +   "<foreach collection = 'outTenantIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<Integer> getByOutTenantId(@Param("ea") String ea, @Param("outTenantIdList") List<Long> outTenantIdList);
}
