package com.facishare.marketing.provider.sharegpt.chain;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.result.ai.AgentErrorMsg;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.manager.ai.AiChatContentType;
import com.facishare.marketing.provider.sharegpt.agent.tool.AbstractCommand;
import com.facishare.marketing.provider.sharegpt.agent.tool.CommandExecutor;
import com.facishare.marketing.provider.sharegpt.agent.tool.CommandManager;
import com.facishare.marketing.provider.sharegpt.agent.tool.CommandType;
import com.facishare.marketing.provider.sharegpt.manager.AutoGPTChatContext;
import com.facishare.marketing.provider.sharegpt.model.chat.OpenAiChatModel;
import com.facishare.marketing.provider.sharegpt.model.chat.OpenAiHelper;
import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ToolExecutionResultMessage;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import lombok.Builder;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static dev.langchain4j.agent.tool.ToolExecutionRequestUtil.argumentsAsMap;
import static dev.langchain4j.data.message.UserMessage.userMessage;

public class ConversationalWithToolsChain {

    private final String ea;
    private final Integer userId;
    private final String businessName;
    private final Map<String, Object> userArgs;
    private final OpenAiChatModel chatLanguageModel;
    private final ChatMemory chatMemory;
    private final List<ToolSpecification> toolSpecifications;
    private final Map<String, CommandExecutor> toolExecutors = new HashMap<>();

    @Builder
    private ConversationalWithToolsChain(
            String ea,
            Integer userId,
            String businessName,
            Map<String, Object> userArgs,
            OpenAiChatModel chatLanguageModel,
            ChatMemory chatMemory,
            List<?> tools
    ) {
        this.ea = ea;
        this.userId = userId;
        this.businessName = businessName;
        this.userArgs = userArgs;
        this.chatLanguageModel = chatLanguageModel;
        this.chatMemory = chatMemory == null ? MessageWindowChatMemory.withMaxMessages(10) : chatMemory;
        this.toolSpecifications = new ArrayList<>();
        for (Object tool : tools) {
            Method[] declaredMethods = tool.getClass().getSuperclass().getDeclaredMethods();
            for (Method method : declaredMethods) {
                if (method.isAnnotationPresent(Tool.class)) {
                    ToolSpecification toolSpecification = OpenAiHelper.toolSpecificationFrom(method);
                    this.toolSpecifications.add(toolSpecification);
                    this.toolExecutors.put(toolSpecification.name(), new CommandExecutor(tool, method));
                }
            }
        }
    }

    public AutoGPTChatContext execute(AutoGPTChatContext chatContext) {
        String userContent = chatContext.getUserContent();
        if (userContent == null) {
            return chatContext;
        }
        chatMemory.add(userMessage(userContent));
        return combo(chatContext);
    }

    /**
     * user->ai->(tool->func->ai)...
     *
     * @return
     */
    private AutoGPTChatContext combo(AutoGPTChatContext chatContext) {
        AiMessage aiMessage = null;
        try {
            aiMessage = chatLanguageModel.generate(chatMemory.messages(), toolSpecifications).content();
        } catch (Exception e) {
            chatContext.setError(true);
            chatContext.setContent(JSONObject.toJSONString(AgentErrorMsg.buildSHErrorCode(SHErrorCode.SERVER_BUSY)));
            return chatContext;
        }
        chatMemory.add(aiMessage);
        if (aiMessage.hasToolExecutionRequests()) {
            for (ToolExecutionRequest toolExecutionRequest : aiMessage.toolExecutionRequests()) {
                String toolName = toolExecutionRequest.name();
                CommandExecutor toolExecutor = this.toolExecutors.get(toolName);
                Map<String, Object> argumentsMap = argumentsAsMap(toolExecutionRequest.arguments());
                argumentsMap.put("ea", ea);
                argumentsMap.put("userId", userId);
                argumentsMap.put("businessName", businessName);
                argumentsMap.putAll(userArgs);
                ToolExecutionRequest newToolExecutionRequest = ToolExecutionRequest.builder()
                        .id(toolExecutionRequest.id())
                        .name(toolName)
                        .arguments(JSONObject.toJSONString(argumentsMap))
                        .build();
                String toolExecutionResult = toolExecutor.execute(newToolExecutionRequest, "DEFAULT");
                ToolExecutionResultMessage toolExecutionResultMessage = ToolExecutionResultMessage.from(
                        toolExecutionRequest,
                        toolExecutionResult
                );
                chatMemory.add(toolExecutionResultMessage);
                AbstractCommand command = CommandManager.getCommandObjByMethodName(toolName);
                if (CommandType.EPILOG == command.type()) {
                    chatContext.setContent(toolExecutionResultMessage.text());
                    chatContext.setContentType(command.contentType().getType());
                    if (AiChatContentType.IMAGE == command.contentType()) {
                        chatContext.setCardType("IMAGE");
                    }
                    return chatContext;
                }
            }
            return combo(chatContext);
        }
        chatContext.setContent(aiMessage.text());
        return chatContext;
    }

}
