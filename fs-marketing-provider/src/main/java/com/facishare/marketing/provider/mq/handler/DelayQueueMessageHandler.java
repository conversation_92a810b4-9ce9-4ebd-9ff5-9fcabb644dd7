package com.facishare.marketing.provider.mq.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.conference.SignInArg;
import com.facishare.marketing.api.arg.hexagon.FileToHexagonDataArg;
import com.facishare.marketing.api.service.conference.ConferenceService;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.common.contstant.DelayQueueTagConstants;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.FileToHexagonStatusEnum;
import com.facishare.marketing.common.enums.qywx.AppScopeEnum;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.FileToHexagonDAO;
import com.facishare.marketing.provider.dto.FileToHexagonDataDTO;
import com.facishare.marketing.provider.entity.FileToHexagonEntity;
import com.facishare.marketing.provider.innerArg.MwSendDetailUpdateMqArg;
import com.facishare.marketing.provider.innerData.BehaviorSendEventData;
import com.facishare.marketing.provider.innerResult.qywx.QywxEventDelayMqArg;
import com.facishare.marketing.provider.manager.ActivityManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupObjDescribeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WhatsAppSendRecordObjManager;
import com.facishare.marketing.provider.manager.qywx.QywxEventCallBackManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.mq.handler.dto.CrmEventDTO;
import com.facishare.marketing.provider.mq.handler.dto.WhatsAppDelayHandlerDTO;
import com.facishare.marketing.provider.mq.sender.BehaviorSendEventSender;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class DelayQueueMessageHandler extends AbstractMessageHandler<MessageExt> {

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private MwSendManager mwSendManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private BehaviorSendEventSender behaviorSendEventSender;

    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;

    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;

    @Autowired
    private WhatsAppSendRecordObjManager whatsAppSendRecordObjManager;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private ConferenceService conferenceService;

    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;

    @Autowired
    private FileToHexagonDAO fileToHexagonDAO;
    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private QywxEventCallBackManager qywxEventCallBackManager;

    @Override
    protected MessageExt getMsgObj(MessageExt msg) {
        return msg;
    }

    @Override
    protected String getEa(MessageExt msgObj) {
        return msgObj.getUserProperty("ea");
    }

    @Override
    protected void directHandle(MessageExt message) {
        String tag = message.getTags();
        if (StringUtils.isBlank(tag)) {
            return;
        }
        handleAdDataSendBack(message);
        handleSmsSendRecordObj(message);
        handleBehaviorIntegral(message);
        handleQywxChangeEvent(message);
        handleWhatsAppCallBackEvent(message);
        handleActivitySignInEvent(message);
        handleFsFileProcessParse(message);
        handleAppViewChangeEvent(message);
    }

    private void handleAppViewChangeEvent(MessageExt message) {
        String tag = message.getTags();
        if (!DelayQueueTagConstants.APP_VIEW_SCOPE_CHANGE.equals(tag)) {
            return;
        }
        CrmAppViewChangeHandler.AppViewChangeEvent appViewChangeEvent = JSON.parseObject(message.getBody(), CrmAppViewChangeHandler.AppViewChangeEvent.class, Feature.IgnoreNotMatch);
        log.info("delay handleAppViewChangeEvent appViewChangeEvent: {}", appViewChangeEvent);
        userRelationManager.handleYxtAppViewChange(appViewChangeEvent, false);
    }

    private void handleActivitySignInEvent(MessageExt message) {
        String tag = message.getTags();
        if (!DelayQueueTagConstants.ACTIVITY_SIGN_IN.equals(tag)) {
            return;
        }
        SignInArg signInArg = JSON.parseObject(message.getBody(), SignInArg.class, Feature.IgnoreNotMatch);
        log.info("收到活动签到延迟消息，tag: {} msg： {}", tag, signInArg);
        if ("h5".equals(signInArg.getFrom())) {
            signInArg.setDelaySingIn(false);
            activityManager.activitySignIn(signInArg);
        } else {
            conferenceService.activitySign(signInArg.getId(), signInArg.getUid(), signInArg.getPhone(), signInArg.getTagId(), false,signInArg.getEmail());
        }
    }

    private void handleWhatsAppCallBackEvent(MessageExt message) {
        String tag = message.getTags();
        if (!DelayQueueTagConstants.WHATS_APP_CALL_BACK_EVENT.equals(tag)) {
            return;
        }
        WhatsAppDelayHandlerDTO whatsAppDelayHandlerDTO = JSON.parseObject(message.getBody(), WhatsAppDelayHandlerDTO.class, Feature.IgnoreNotMatch);
        whatsAppSendRecordObjManager.tryUpdateSendStatus(whatsAppDelayHandlerDTO.getEa(), whatsAppDelayHandlerDTO.getStatusModel(), whatsAppDelayHandlerDTO.getBusinessPhone(), true);
    }

    private void handleQywxChangeEvent(MessageExt message) {
        String tag = message.getTags();
        if (!DelayQueueTagConstants.QYWX_CHANGE_EVENT.equals(tag)) {
            return;
        }
        QywxEventDelayMqArg qywxEventDelayMqArg = JSON.parseObject(message.getBody(), QywxEventDelayMqArg.class, Feature.IgnoreNotMatch);
        log.info("收到企微微信延迟消息，tag: {} msg： {}", tag, qywxEventDelayMqArg);
        int ei = qywxEventDelayMqArg.getEi();
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        AppScopeEnum appScopeEnum = qywxEventDelayMqArg.getAppScopeEnum();
        if (QywxEventDelayMqArg.EXTERNAL_OBJECT_TYPE.equals(qywxEventDelayMqArg.getChangeObjectType())) {
            if (appScopeEnum == AppScopeEnum.MARKETING && qywxEventDelayMqArg.getExternalContactEventMsg() != null) {
                qywxEventCallBackManager.handleMarketingExternalUserData(ea, qywxEventDelayMqArg.getExternalContactEventMsg());
                return;
            }
            String externalUserId = qywxEventDelayMqArg.getExternalUserId();
            Result<Page<ObjectData>> result = wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(ei, Lists.newArrayList(externalUserId));
            if (QywxEventDelayMqArg.ADD_EVENT.equals(qywxEventDelayMqArg.getEvent())) {
                if (result == null || !result.isSuccess() || result.getData() == null || CollectionUtils.isEmpty(result.getData().getDataList())) {
                    wechatWorkExternalUserObjManager.doAddWxWorkExternalUserToCrm(ei, externalUserId, appScopeEnum, qywxEventDelayMqArg.getExternalContactDetailResult(), null);
                } else {
                    wechatWorkExternalUserObjManager.doUpdateWxWorkExternalUserToCrm(ei, appScopeEnum, result.getData().getDataList().get(0), qywxEventDelayMqArg.getExternalContactDetailResult(), null);
                }
            } else if (QywxEventDelayMqArg.UPDATE_EVENT.equals(qywxEventDelayMqArg.getEvent())) {
                wechatWorkExternalUserObjManager.doUpdateWxWorkExternalUserToCrm(ei, appScopeEnum, result.getData().getDataList().get(0), qywxEventDelayMqArg.getExternalContactDetailResult(), null);
            } else if (QywxEventDelayMqArg.DELETE_EVENT.equals(qywxEventDelayMqArg.getEvent())) {
                wechatWorkExternalUserObjManager.handleQywxDeleteEvent(ea, qywxEventDelayMqArg.getExternalUserId(), qywxEventDelayMqArg.getQywxUserId(), qywxEventDelayMqArg.getQywxOriginChangeType(), qywxEventDelayMqArg.getAppId(), appScopeEnum, qywxEventDelayMqArg.getQywxCorpId());
            }
        } else if (QywxEventDelayMqArg.GROUP_OBJECT_TYPE.equals(qywxEventDelayMqArg.getChangeObjectType())) {
            String chatId = qywxEventDelayMqArg.getChatId();
            Result<Page<ObjectData>> result = wechatGroupObjDescribeManager.listGroupObjectDataByIdsLimited(ei, Lists.newArrayList(chatId));
            if (QywxEventDelayMqArg.ADD_EVENT.equals(qywxEventDelayMqArg.getEvent())) {
                if (result == null || !result.isSuccess() || result.getData() == null || CollectionUtils.isEmpty(result.getData().getDataList())) {
                    wechatGroupObjDescribeManager.doAddWechatGroupToCrm(ei, 0, appScopeEnum, qywxEventDelayMqArg.getGroupDetailResult());
                } else {
                    wechatGroupObjDescribeManager.doUpdateWechatGroupToCrm(ei, 0, result.getData().getDataList().get(0), qywxEventDelayMqArg.getOwnerId(), appScopeEnum, qywxEventDelayMqArg.getGroupDetailResult());
                }
            } else if (QywxEventDelayMqArg.UPDATE_EVENT.equals(qywxEventDelayMqArg.getEvent())) {
                wechatGroupObjDescribeManager.doUpdateWechatGroupToCrm(ei, 0, result.getData().getDataList().get(0), -10000, appScopeEnum, qywxEventDelayMqArg.getGroupDetailResult());
            } else if (QywxEventDelayMqArg.ADD_MEMBER_EVENT.equals(qywxEventDelayMqArg.getEvent())) {
                wechatGroupObjDescribeManager.handleAddMemberEvent(ea, qywxEventDelayMqArg.getChatId(), qywxEventDelayMqArg.getAppScopeEnum(), qywxEventDelayMqArg.getGroupDetailResult());
            } else if (QywxEventDelayMqArg.DELETE_MEMBER_EVENT.equals(qywxEventDelayMqArg.getEvent())) {
                wechatGroupObjDescribeManager.handleDeleteMemberEvent(ea, qywxEventDelayMqArg.getChatId(), qywxEventDelayMqArg.getAppScopeEnum(), qywxEventDelayMqArg.getGroupDetailResult());
            }
        }
    }

    private void handleBehaviorIntegral(MessageExt message) {
        String tag = message.getTags();
        if (!DelayQueueTagConstants.BEHAVIOR_INTEGRAL.equals(tag)) {
            return;
        }
        BehaviorSendEventData behaviorSendEventData = JSON.parseObject(message.getBody(), BehaviorSendEventData.class, Feature.IgnoreNotMatch);
        log.info("DelayQueueConsumer handleBehaviorIntegral body: {}", behaviorSendEventData);
        String ea = behaviorSendEventData.getEa();
        List<String> leadIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(behaviorSendEventData.getObjectId()) && LeadsFieldContants.API_NAME.equals(behaviorSendEventData.getObjectApiName())) {
            leadIdList.add(behaviorSendEventData.getObjectId());
        }
        if (StringUtils.isNotBlank(behaviorSendEventData.getUserMarketingId())) {
            List<String> leadIdByUserMarketingIdList = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, ChannelEnum.CRM_LEAD.getType(), Lists.newArrayList(behaviorSendEventData.getUserMarketingId()));
            if (CollectionUtils.isNotEmpty(leadIdByUserMarketingIdList)) {
                leadIdList.addAll(leadIdByUserMarketingIdList);
            }
        }
        log.info("doProcessIntegral event:{}, leads:{} ", behaviorSendEventData, leadIdList);
        if (CollectionUtils.isEmpty(leadIdList)) {
            return;
        }
        for (String leadId : leadIdList) {
            BehaviorSendEventData data = new BehaviorSendEventData();
            data.setActionApiName(behaviorSendEventData.getActionApiName());
            data.setCategoryApiName(behaviorSendEventData.getCategoryApiName());
            data.setMaterialApiName(behaviorSendEventData.getMaterialApiName());
            data.setObjectApiName(LeadsFieldContants.API_NAME);
            data.setObjectId(leadId);
            data.setTenantId(String.valueOf(behaviorSendEventData.getTenantId()));
            data.setTimestamp(System.currentTimeMillis());
            data.setExtensionParams(behaviorSendEventData.getExtensionParams());
            behaviorSendEventSender.send(data);
        }

    }

    private void handleSmsSendRecordObj(MessageExt message) {
        String tag = message.getTags();
        if (DelayQueueTagConstants.SMS_RECORD_UPDATE.equals(tag)) {
            MwSendDetailUpdateMqArg mwSendDetailUpdateMqArg = JSON.parseObject(message.getBody(), MwSendDetailUpdateMqArg.class, Feature.IgnoreNotMatch);
            mwSendManager.updateSmsSendRecordObjStatusToFail(mwSendDetailUpdateMqArg);
        }

    }

    private void handleAdDataSendBack(MessageExt message) {
        String tag = message.getTags();
        if (DelayQueueTagConstants.CRM_EVEN.equals(tag)) {
            CrmEventDTO.CrmEvent crmEvent = JSON.parseObject(message.getBody(), CrmEventDTO.CrmEvent.class, Feature.IgnoreNotMatch);
            Integer ei = crmEvent.getTenantId();
            String ea = eieaConverter.enterpriseIdToAccount(ei);
            adOCPCUploadManager.bindCrmLandingPageToUserMarketing(ea, crmEvent);
            adOCPCUploadManager.handleAdDataSendBack(ei, crmEvent, null);
        } else if (DelayQueueTagConstants.LEAD_TRANSFER.equals(tag)) {
            SaleLeadTransferMessageHandler.SaleLeadTransferEven transferEven = JSON.parseObject(message.getBody(), SaleLeadTransferMessageHandler.SaleLeadTransferEven.class, Feature.IgnoreNotMatch);
            adOCPCUploadManager.handleAdDataSendBack(Integer.parseInt(transferEven.getTenantId()), null, transferEven);
        } else if (DelayQueueTagConstants.TAG_CHANGE.equals(tag)) {
            ObjTagChangeMessageHandler.ObjTagChangeEvent tagChangeEvent = JSON.parseObject(message.getBody(), ObjTagChangeMessageHandler.ObjTagChangeEvent.class, Feature.IgnoreNotMatch);
            CrmEventDTO.CrmEvent crmEvent = new CrmEventDTO.CrmEvent();
            crmEvent.setTenantId(tagChangeEvent.getEi());
            CrmEventDTO.Body body = new CrmEventDTO.Body();
            body.setEntityId(tagChangeEvent.getObjectApiName());
            body.setTriggerType(CrmEventDTO.Body.UPDATE);
            body.setObjectId(tagChangeEvent.getObjectDataId());
            ObjectData objectData = new ObjectData();
            objectData.put("tag", CollectionUtils.isEmpty(tagChangeEvent.getTagIds()) ? Lists.newArrayList() : tagChangeEvent.getTagIds());
            body.setAfterTriggerData(objectData);
            crmEvent.setBody(Lists.newArrayList(body));
            adOCPCUploadManager.handleAdDataSendBack(Integer.parseInt(tagChangeEvent.getEi()), crmEvent, null);
        }
    }

    private void handleFsFileProcessParse(MessageExt msgObj) {
        String tag = msgObj.getTags();
        if (!DelayQueueTagConstants.FILE_TO_HEXAGON.equals(tag)) {
            return;
        }
        FileToHexagonDataDTO arg = JSON.parseObject(msgObj.getBody(), FileToHexagonDataDTO.class);
        log.info("收到活动签到延迟消息，tag: {} arg： {}", tag, arg);
        List<FileToHexagonEntity> byTaskId = fileToHexagonDAO.getEaByTaskId(arg.getTaskId(), arg.getEa());
        if(CollectionUtils.isNotEmpty(byTaskId)){
//            List<String> urlList = arg.getPaths().stream().map(o -> fileV2Manager.getUrlByCPath(arg.getEa(), o.getPath())).collect(Collectors.toList());
//            int status = FileToHexagonStatusEnum.getEnumByFileStatus(arg.getStatus()).getStatus();
//            for (FileToHexagonEntity entity : byTaskId) {
//                hexagonService.updateFileToHexagonStatus(entity.getHexagonSiteId(),status, arg.getMessage(), urlList,arg.getPaths().get(0).getPath());
//            }
//            List<String> urlList = arg.getPaths().stream().map(o -> fileV2Manager.getUrlByCPath(arg.getEa(), o.getPath())).collect(Collectors.toList());
            int status = FileToHexagonStatusEnum.getEnumByFileStatus(arg.getStatus()).getStatus();
            List<FileToHexagonDataArg> args = BeanUtil.copy(arg.getPaths(), FileToHexagonDataArg.class);
            for (FileToHexagonEntity entity : byTaskId) {
                hexagonService.updateFileToHexagonStatus(entity.getHexagonSiteId(),status, arg.getMessage(), args);
            }
        }


    }
}
