package com.facishare.marketing.provider.innerResult.connector.douyin;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 抖音来客自然流线索数据
 */
@Data
@ToString
public class GetDouYinClueDataResult implements Serializable {

    @SerializedName("data")
    private DouYinClueDataResult clueDataResult;

    @SerializedName("extra")
    private ExtraDataResult extraDataResult;


    @Data
    public static class DouYinClueDataResult implements Serializable {
        @SerializedName("clue_data")
        private List<ClueData> clueDataList;

        @SerializedName("page")
        private Page page;

        @SerializedName("error_code")
        private Integer errorCode;

        @SerializedName("description")
        private String description;
    }

    @Data
    public static class ExtraDataResult implements Serializable {
        @SerializedName("error_code")
        private Integer errorCode;

        @SerializedName("description")
        private String description;

        @SerializedName("sub_error_code")
        private Integer subErrorCode;

        @SerializedName("sub_description")
        private String subDescription;

        @SerializedName("logid")
        private String logId;

        @SerializedName("now")
        private Long now;
    }

    @Data
    public static class ClueData implements Serializable{
        @SerializedName("advertiser_name")
        private String advertiserName;

        @SerializedName("clue_owner_name")
        private String clueOwnerName;

        @SerializedName("leads_page")
        private Integer leadsPage;

        @SerializedName("address")
        private String address;

        @SerializedName("flow_type")
        private Integer flowType;

        @SerializedName("tool_id")
        private String toolId;

        @SerializedName("clue_type")
        private Integer clueType;

        @SerializedName("county_name")
        private String countyName;

        @SerializedName("follow_life_account_id")
        private String followLifeAccountId;

        @SerializedName("tags")
        private List<String> tags;

        @SerializedName("city_name")
        private String cityName;

        @SerializedName("remark_dict")
        private String remarkDict;

        @SerializedName("action_type")
        private Integer actionType;

        @SerializedName("age")
        private Integer age;

        @SerializedName("clue_id")
        private String clueId;

        @SerializedName("create_time_detail")
        private String createTimeDetail;

        @SerializedName("follow_life_account_type")
        private Integer followLifeAccountType;

        @SerializedName("modify_time")
        private String modifyTime;

        @SerializedName("product_name")
        private String productName;

        @SerializedName("province_name")
        private String provinceName;

        @SerializedName("system_tags")
        private List<String> systemTags;

        @SerializedName("allocation_status")
        private Integer allocationStatus;

        @SerializedName("content_id")
        private String contentId;

        @SerializedName("follow_life_account_name")
        private String followLifeAccountName;

        @SerializedName("follow_state_name")
        private Integer followStateName;

        @SerializedName("promotion_name")
        private String promotionName;

        @SerializedName("remark")
        private String remark;

        @SerializedName("req_id")
        private String reqId;

        @SerializedName("telephone")
        private String telephone;

        @SerializedName("advertiser_id")
        private String advertiserId;

        @SerializedName("name")
        private String name;

        @SerializedName("order_id")
        private Long orderId;

        @SerializedName("effective_state")
        private Integer effectiveState;

        @SerializedName("gender")
        private Integer gender;

        @SerializedName("promotion_id")
        private Long promotionId;

        @SerializedName("root_life_account_id")
        private String rootLifeAccountId;

        @SerializedName("staff_douyin_id")
        private String staffDouyinId;

        @SerializedName("staff_nickname")
        private String staffNickname;

        @SerializedName("author_douyin_id")
        private String authorDouyinId;

        @SerializedName("author_nickname")
        private String authorNickname;

        @SerializedName("author_role")
        private String authorRole;

        @SerializedName("follow_poi_id")
        private String followPoiId;

        @SerializedName("intention_life_account_name")
        private String intentionLifeAccountName;

        @SerializedName("intention_poi_id")
        private String intentionPoiId;

        @SerializedName("staff_commerce_nickname")
        private String staffCommerceNickname;

        @SerializedName("auto_province_name")
        private String autoProvinceName;

        @SerializedName("auto_city_name")
        private String autoCityName;

        @SerializedName("tel_addr")
        private String telAddr;
    }

    @Data
    public static class Page implements Serializable {
        @SerializedName("page_number")
        private Integer pageNumber;

        @SerializedName("page_size")
        private Integer pageSize;

        @SerializedName("page_total")
        private Integer pageTotal;

        @SerializedName("total")
        private Integer total;
    }

}
