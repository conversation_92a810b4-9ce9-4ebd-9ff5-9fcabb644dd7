package com.facishare.marketing.provider.entity.live;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PolyvAccountEntity extends BaseEaEntity implements Serializable {
    private String id;
    
    private String userId;
    private String appId;
    private String appSecret;
    private Date createTime;
    private Date updateTime;
}
