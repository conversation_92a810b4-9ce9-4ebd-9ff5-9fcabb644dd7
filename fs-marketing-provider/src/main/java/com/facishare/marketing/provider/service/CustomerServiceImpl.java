/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.mankeep.api.service.TagService;
import com.facishare.mankeep.api.vo.DeleteTagVO;
import com.facishare.mankeep.api.vo.QueryFilterTagVO;
import com.facishare.mankeep.api.vo.UpdateTagVO;
import com.facishare.mankeep.api.vo.customer.QueryCustomerDetailVO;
import com.facishare.mankeep.api.vo.customer.QueryCustomerInteractVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.result.PageObject;
import com.facishare.marketing.api.result.QueryCustomerDetailResult;
import com.facishare.marketing.api.result.QueryCustomerInteractListUnitResult;
import com.facishare.marketing.api.service.CustomerService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.vo.BatchAddTagsVO;
import com.facishare.marketing.api.vo.UpdateCustomerVO;
import com.facishare.marketing.api.vo.UpgradeCustomerVO;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.CustomerEntity;
import com.facishare.marketing.provider.entity.TagEntity;
import com.facishare.marketing.provider.entity.TaggingEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.manager.FaciMankeepMsgManager;
import com.facishare.marketing.provider.manager.UserTagManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.qixin.api.model.open.kemai.result.GetKeMaiSessionIdResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service("customerService")
public class CustomerServiceImpl implements CustomerService {

    @Autowired
    private com.facishare.mankeep.api.service.CustomerService customerService;
    @Autowired
    private TagService tagService;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private FaciMankeepMsgManager faciMankeepMsgManager;
    @Autowired
    private CustomerDAO customerDAO;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private TaggingDAO taggingDAO;
    @Autowired
    private TagDAO tagDAO;
    @Autowired
    private UserTagManager userTagManager;
    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Override
    public Result updateCustomer(UpdateCustomerVO vo) {
        String uid = qywxUserManager.getUidByFsUserInfo(vo.getEa(), vo.getUserId());
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        com.facishare.mankeep.api.vo.customer.UpdateCustomerVO updateCustomerVO = BeanUtil.copy(vo, com.facishare.mankeep.api.vo.customer.UpdateCustomerVO.class);
        updateCustomerVO.setUid(uid);
        ModelResult modelResult = customerService.updateCustomer(updateCustomerVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return Result.newSuccess();
    }

    @Override
    public Result upgradeCustomer(UpgradeCustomerVO vo) {
        String uid = qywxUserManager.getUidByFsUserInfo(vo.getEa(), vo.getUserId());
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        com.facishare.mankeep.api.vo.customer.UpgradeCustomerVO upgradeCustomerVO = BeanUtil.copy(vo, com.facishare.mankeep.api.vo.customer.UpgradeCustomerVO.class);
        upgradeCustomerVO.setUid(uid);
        ModelResult modelResult = customerService.upgradeCustomer(upgradeCustomerVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return Result.newSuccess();
    }

    @Override
    public Result<QueryCustomerDetailResult> queryCustomerDetail(String ea, Integer fsUserId, String customerId) {
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        QueryCustomerDetailVO queryCustomerDetailVO = new QueryCustomerDetailVO();
        queryCustomerDetailVO.setCustomerId(customerId);
        queryCustomerDetailVO.setUid(uid);
        queryCustomerDetailVO.setNeedCardAuth(false);
        ModelResult<com.facishare.mankeep.api.result.customer.QueryCustomerDetailResult> modelResult = customerService.queryCustomerDetail(queryCustomerDetailVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        QueryCustomerDetailResult result = BeanUtil.copy(modelResult.getData(), QueryCustomerDetailResult.class);

        GetKeMaiSessionIdResult getKeMaiSessionIdResult = faciMankeepMsgManager.getSessionId(ea, fsUserId, result.getCustomerUid());
        if (null != getKeMaiSessionIdResult) {
            result.setSessionId(getKeMaiSessionIdResult.getSessionId());
            result.setParentSessionId(getKeMaiSessionIdResult.getParentSessionId());
        }
        if (StringUtils.isNotBlank(result.getName()) && StringUtils.isNotBlank(result.getNickName()) && result.getName().contains("客脉访客") && !result.getNickName().contains("客脉访客")) {
            result.setName(result.getNickName());
        }
        if (StringUtils.isNotBlank(result.getName())) {
            result.setName(result.getName().replace("客脉访客", "访客"));
        }
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity != null) {
            // 查询是否为营销用户
            result.setBindExternalUser(false);
            UserMarketingMiniappAccountRelationEntity accountRelationEntity = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, modelResult.getData().getCustomerUid());
            if (accountRelationEntity != null) {
                List<String> userMarketingIds = Lists.newArrayList();
                userMarketingIds.add(accountRelationEntity.getUserMarketingId());
                List<UserMarketingWxWorkExternalUserRelationEntity> wxAccountList =
                        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingIds);
                if (CollectionUtils.isNotEmpty(wxAccountList)) {
                    result.setBindExternalUser(true);
                    result.setExternalUserId(wxAccountList.get(0).getWxWorkExternalUserId());
                }
            }
        }
        return new Result(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<PageResult<QueryCustomerInteractListUnitResult>> queryCustomerInteract(String ea, Integer fsUserId, String customerId, Integer pageSize, Integer pageNum, Long time) {
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        QueryCustomerInteractVO queryCustomerInteractVO = new QueryCustomerInteractVO();
        queryCustomerInteractVO.setCustomerId(customerId);
        queryCustomerInteractVO.setPageSize(pageSize);
        queryCustomerInteractVO.setPageNum(pageNum);
        queryCustomerInteractVO.setTime(time);
        queryCustomerInteractVO.setUid(uid);
        ModelResult<PageObject<com.facishare.mankeep.api.result.customer.QueryCustomerInteractListUnitResult>> modelResult = customerService.queryCustomerInteract(queryCustomerInteractVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        PageResult<QueryCustomerInteractListUnitResult> result = new PageResult<>();
        result.setPageNum(modelResult.getData().getPageNum());
        result.setPageSize(modelResult.getData().getPageSize());
        result.setTime(modelResult.getData().getTime());
        result.setTotalCount(modelResult.getData().getTotalCount());

        List<QueryCustomerInteractListUnitResult> list = new ArrayList<>();
        result.setResult(list);
        for (com.facishare.mankeep.api.result.customer.QueryCustomerInteractListUnitResult queryCustomerInteractListUnitResult : modelResult.getData().getResult()) {
            QueryCustomerInteractListUnitResult unitResult = BeanUtil.copy(queryCustomerInteractListUnitResult, QueryCustomerInteractListUnitResult.class);
            list.add(unitResult);
        }

        return new Result(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result saveMankeepToCrm(String ea, Integer fsUserId, String mankeepCustomerId, String crmLeadRecordType, String crmRemark) {
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        ModelResult<String> modelResult = customerService.saveMankeepToCrm(uid, mankeepCustomerId, crmLeadRecordType, crmRemark);
        String leadId = modelResult.getData();
        if (StringUtils.isNotBlank(leadId)) {
            CustomerEntity customerEntity = customerDAO.queryCustomerById(mankeepCustomerId, uid);
            if (null != customerEntity) {
                log.info("miniappAssociationManager.bind finished ea={}, phone={}, uid={}, leadId={}", ea, customerEntity.getPhone(), uid, leadId);
                String userMarketingId = userMarketingAccountRelationManager.bindMiniappUserAndLead(ea, customerEntity.getFriendUid(), leadId, customerEntity.getPhone(), "saveMankeepToCrm").orElse(null);
                List<String> tags = new ArrayList<>();
                List<TaggingEntity> taggingDOS = taggingDAO.queryTagByTargetId(mankeepCustomerId);
                if (taggingDOS != null) {
                    for (TaggingEntity taggingDO : taggingDOS) {
                        TagEntity tagDO = tagDAO.queryTagById(taggingDO.getTagId());
                        if (tagDO != null && tagDO.getName().length() < 50) {
                            tags.add(tagDO.getName());
                        }
                    }
                    TagNameList tagNameList = TagNameList.convert(userTagManager.listBySecondGradeTagName(ea, fsUserId, tags));
                    Optional.ofNullable(tagNameList).ifPresent(val-> userMarketingAccountService.batchAddTagsToUserMarketings(ea, fsUserId, ChannelEnum.getAllChannelApiName(), Lists.newArrayList(userMarketingId), val));
                }
            }
        } else if (!modelResult.isSuccess()) {
            return new Result(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return Result.newSuccess();
    }

    @Override
    public Result deleteTag(String ea, Integer fsUserId, String tag, String targetId) {
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        DeleteTagVO deleteTagVO = new DeleteTagVO();
        deleteTagVO.setTag(tag);
        deleteTagVO.setTargetId(targetId);
        deleteTagVO.setUid(uid);
        ModelResult modelResult = tagService.deleteTag(deleteTagVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return Result.newSuccess();
    }

    @Override
    public Result updateTag(String ea, Integer fsUserId, String targetId, String targetUid, String oldTag, String newTag) {
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        UpdateTagVO updateTagVO = new UpdateTagVO();
        updateTagVO.setTargetId(targetId);
        updateTagVO.setTargetUid(targetUid);
        updateTagVO.setNewTag(newTag);
        updateTagVO.setOldTag(oldTag);
        updateTagVO.setUid(uid);
        ModelResult modelResult = tagService.updateTag(updateTagVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return Result.newSuccess();
    }

    @Override
    public Result batchUpdateTags(BatchAddTagsVO vo) {
        String uid = qywxUserManager.getUidByFsUserInfo(vo.getEa(), vo.getUserId());
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        com.facishare.mankeep.api.vo.BatchAddTagsVO batchAddTagsVO = BeanUtil.copy(vo, com.facishare.mankeep.api.vo.BatchAddTagsVO.class);
        batchAddTagsVO.setUid(uid);
        ModelResult modelResult = tagService.batchUpdateTags(batchAddTagsVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return Result.newSuccess();
    }

    @Override
    public Result queryFilterTags(String ea, Integer fsUserId) {
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isEmpty(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        com.facishare.mankeep.api.vo.QueryFilterTagVO queryFilterTagVO = new QueryFilterTagVO();
        queryFilterTagVO.setUid(uid);
        ModelResult modelResult = tagService.queryFilterTags(queryFilterTagVO);
        if (!modelResult.isSuccess()) {
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return new Result(SHErrorCode.SUCCESS, modelResult.getData());
    }
}
