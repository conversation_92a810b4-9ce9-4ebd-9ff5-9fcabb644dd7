package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.api.arg.qywx.staff.QueryQywxStaffPageArg;
import com.facishare.marketing.api.result.qywx.staff.QueryQywxStaffPageDTO;
import com.facishare.marketing.api.result.qywx.staff.QueryQywxStaffPageResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationCommonSettingEntity;
import java.util.List;

import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

/**
 * Created  By zhoux 2021/07/15
 **/
public interface QyWxAddressBookDAO {
    @FilterLog
    @Select("SELECT * FROM qywx_address_book where ea = #{ea}")
    List<QyWxAddressBookEntity> queryByEa(@Param("ea") String ea);

    @Select("SELECT * FROM qywx_address_book where ea = #{ea} and mobile=#{mobile}")
    List<QyWxAddressBookEntity> queryStaffByEaAndMobile(@Param("ea") String ea, @Param("mobile") String mobile);

    @Select("<script>"
        + " SELECT * FROM qywx_address_book WHERE ea = #{ea} AND user_id IN"
        + "<foreach collection = 'userId' item = 'id' index='num' open = '(' separator = ',' close = ')'>"
        + "#{id}"
        + "</foreach>"
        + "</script>")
    List<QyWxAddressBookEntity> queryEaAndUserId(@Param("ea") String ea, @Param("userId") List<String> userIds);

    @Select("<script>"
            + " SELECT * FROM qywx_address_book WHERE ea = #{ea} AND user_id = #{userId}"
            + "</script>")
    QyWxAddressBookEntity queryByEaAndUserId(@Param("ea") String ea, @Param("userId") String userId);

    @Select("SELECT count(1) FROM qywx_address_book WHERE ea = #{ea}")
    Integer countByEa(@Param("ea") String ea);

    @FilterLog
    @Insert("<script>"
        + "INSERT INTO qywx_address_book (\n"
        + "        \"id\",\n"
        + "        \"ea\",\n"
        + "        \"user_id\",\n"
        + "        \"name\",\n"
        + "        \"department\",\n"
        + "        \"order\",\n"
        + "        \"position\",\n"
        + "        \"mobile\",\n"
        + "        \"gender\",\n"
        + "        \"avatar\",\n"
        + "        \"status\",\n"
        + "        \"create_time\",\n"
        + "        \"update_time\"\n"
        + "        )VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.ea},\n"
        + "        #{obj.userId},\n"
        + "        #{obj.name},\n"
        + "        #{obj.department},\n"
        + "        #{obj.order},\n"
        + "        #{obj.position},\n"
        + "        #{obj.mobile},\n"
        + "        #{obj.gender},\n"
        + "        #{obj.avatar},\n"
        + "        #{obj.status},\n"
        + "        now(),\n"
        + "        now()\n"
        + "        ) ON CONFLICT (ea, user_id) DO UPDATE SET name = #{obj.name}, department = #{obj.department}, \"order\" = #{obj.order}, position = #{obj.position}, mobile = #{obj.mobile}, gender = #{obj.gender}, avatar = #{obj.avatar}, status = #{obj.status}, update_time = now();"
        + "</script>")
    void upsertQyWxAddressBookEntity(@Param("obj") QyWxAddressBookEntity qyWxAddressBookEntity);


    @Delete("<script>"
        + "DELETE FROM qywx_address_book WHERE id IN "
        + "<foreach collection = 'ids' item = 'id' index='num' open = '(' separator = ',' close = ')'>"
        + "#{id}"
        + "</foreach>"
        + "</script>")
    void deleteQyWxAddressBookEntity(@Param("ea") String ea, @Param("ids") List<String> ids);

    @Delete("<script>"
            + "DELETE FROM qywx_address_book WHERE ea = #{ea} and user_id IN "
            + "<foreach collection = 'userIds' item = 'userId' index='num' open = '(' separator = ',' close = ')'>"
                + "#{userId}"
            + "</foreach>"
            + "</script>")
    int deleteByUserId(@Param("ea") String ea, @Param("userIds") List<String> userIds);

    @Select("<script>" +
            "SELECT * FROM qywx_address_book WHERE ea = #{ea} and " +
            "<foreach collection = 'departmentIds' item = 'id' index='num' open = '(' separator = ' or ' close = ')'>" +
            "department::JSONB @> #{id}::JSONB" +
            "</foreach>" +
            "</script>")
    List<QyWxAddressBookEntity> queryStaffByEaAnddepartmentIds(@Param("ea") String ea, @Param("departmentIds") List<String> departmentIds);

    @Select("<script>" +
            "SELECT user_id FROM qywx_address_book WHERE ea = #{ea} and " +
            "<foreach collection = 'departmentIds' item = 'id' index='num' open = '(' separator = ' or ' close = ')'>" +
            "department::JSONB @> #{id}::JSONB" +
            "</foreach>" +
            "</script>")
    List<String> queryUserIdByDepartmentIds(@Param("ea") String ea, @Param("departmentIds") List<String> departmentIds);

    @Select("<script>"
            + "SELECT distinct(user_id) from qywx_address_book where ea = #{ea}"
            + "</script>")
    List<String> getAllUserId(@Param("ea") String ea);


    @Select("<script>"
            + "SELECT book.user_id as userId, book.name, book.department, aa.active_time activatedTime, aa.expire_time expireTime FROM qywx_address_book book"
            + " LEFT JOIN"
            + " qywx_activated_account aa on aa.ea = book.ea and aa.user_id = book.user_id"
            + " WHERE book.ea = #{arg.fsEa} "
            + " <if test =\"arg.userIdList != null and arg.userIdList.size != 0\">\n"
            + "     and book.user_id in  \n"
            + " <foreach open='(' close=')' separator=',' collection='arg.userIdList' index='idx'>"
            + "     #{arg.userIdList[${idx}]}"
            + "</foreach>"
            + "</if>\n"
            + "<if test =\"arg.keyword != null \">\n"
            +   " and name like CONCAT('%', #{arg.keyword}, '%')"
            + "</if>"
            + "<if test =\"arg.activatedStatus != null and arg.activatedStatus == 1\">\n"
            +   " and aa.id is not null and floor(extract(epoch from now())) <![CDATA[ <= ]]> aa.expire_time"
            + "</if>"
            + "<if test =\"arg.activatedStatus != null and arg.activatedStatus == 2\">\n"
            +   " and (aa.id is null or floor(extract(epoch from now())) > aa.expire_time) "
            + "</if>"
            + "<if test =\"arg.expireBeginTime != null and arg.expireEndTime != null\">\n"
            +   " and aa.id is not null and expire_time * 1000 >= #{arg.expireBeginTime} and expire_time * 1000 <![CDATA[ <= ]]> #{arg.expireEndTime}"
            + "</if>"
            + " order by book.create_time desc"
            + "</script>")
    List<QueryQywxStaffPageDTO> queryQywxStaffPage(@Param("arg") QueryQywxStaffPageArg arg,
                                                   @Param("page") Page page,@Param("ea") String ea);

    @Select("<script>"
            + "SELECT book.user_id as userId, book.name, book.department FROM qywx_address_book book"
            + " WHERE book.ea = #{arg.fsEa} "
            + " <if test =\"arg.userIdList != null and arg.userIdList.size != 0\">\n"
            + "     and book.user_id in  \n"
            + " <foreach open='(' close=')' separator=',' collection='arg.userIdList' index='idx'>"
            + "     #{arg.userIdList[${idx}]}"
            + "</foreach>"
            + "</if>\n"
            + "<if test =\"arg.keyword != null \">\n"
            +   " and name like CONCAT('%', #{arg.keyword}, '%')"
            + "</if>"
            + " order by book.create_time desc"
            + "</script>")
    List<QueryQywxStaffPageDTO> queryQywxStaffPageWithoutJoin(@Param("arg") QueryQywxStaffPageArg arg, @Param("page") Page page,@Param("ea") String ea);

    @Update("update qywx_address_book set user_id = #{userId} where ea = #{ea} and id = #{id}")
    int updateUserId(@Param("ea") String ea, @Param("id") String id, @Param("userId") String userId);

    @Update("update qywx_address_book set mobile = #{mobile} where ea = #{ea} and user_id = #{userId}")
    void updateMobile(@Param("ea") String ea, @Param("userId") String userId, @Param("mobile") String mobile);

    @Select("select * from qywx_address_book where user_id = #{userId}")
    List<QyWxAddressBookEntity> getByUserId(@Param("userId") String userId);
}
