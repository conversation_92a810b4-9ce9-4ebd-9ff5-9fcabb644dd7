/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.sharegpt.model.chat;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.expcetion.ServiceException;
import com.facishare.ai.api.model.service.FsAI;
import com.facishare.ai.api.model.service.Openai;
import com.facishare.marketing.api.result.ai.AgentErrorMsg;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.I18nUtil;
import com.google.common.collect.Lists;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.model.output.TokenUsage;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

import static com.facishare.marketing.provider.sharegpt.model.chat.OpenAiHelper.*;
import static dev.langchain4j.data.message.SystemMessage.systemMessage;
import static dev.langchain4j.data.message.UserMessage.userMessage;
import static dev.langchain4j.internal.Utils.getOrDefault;

@Slf4j
public class OpenAiChatModel {

    private final Openai openai;
    private final String tenantId;
    private final String user;
    private final String businessName;
    private final String modelName;
    private final Integer maxTokens;
    private final Double temperature;
    private List<String> imageStrings;

    @Builder
    public OpenAiChatModel(
            String tenantId,
            String user,
            String businessName,
            String modelName,
            Integer maxTokens,
            Double temperature,
            List<String> imageStrings
    ) {
        this.openai = FsAI.openai();
        this.tenantId = getOrDefault(tenantId, "88146");
        this.modelName = modelName;
        this.temperature = temperature;
        this.maxTokens = maxTokens;
        this.user = user;
        this.businessName = businessName;
        this.imageStrings = imageStrings;
    }

    public Response<AiMessage> generate(List<ChatMessage> messages) {
        return generate(messages, null, null);
    }

    public Response<AiMessage> generate(List<ChatMessage> messages, List<ToolSpecification> toolSpecifications) {
        return generate(messages, toolSpecifications, null);
    }

    private Response<AiMessage> generate(List<ChatMessage> messages,
                                         List<ToolSpecification> toolSpecifications,
                                         ToolSpecification toolThatMustBeExecuted
    ) {
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel(modelName);
        String language = I18nUtil.getLanguage();
        if (CollectionUtils.isNotEmpty(imageStrings)) {
            arg.setImageStrings(imageStrings);
            arg.setSupportImage(true);
            messages = Lists.newArrayList(userMessage("en".equals(language) ? "explain" : "详细解释"));
        }
        arg.setMessages(toOpenAiMessages(messages));
        if (temperature != null) {
            arg.setTemperature(temperature);
        }
        arg.setStream(false);
        if (maxTokens != null) {
            arg.setMaxTokens(maxTokens);
        }
        arg.setUser_id(user);

        if (toolSpecifications != null && !toolSpecifications.isEmpty()) {
            arg.setTools(toTools(toolSpecifications));
        }
        if (toolThatMustBeExecuted != null) {
            arg.setTool_choice(toolThatMustBeExecuted.name());
        }

        BaseArgument context = new BaseArgument();
        context.setTenantId(tenantId);
        context.setUserId(user);
        context.setBusiness(businessName);
        context.setLocale(language);
        log.info("请求参数:{}", JSONObject.toJSON(arg));
        OpenAIChatComplete.Result result = null;
        try {
            result = openai.chatComplete(context, arg);
        } catch (ServiceException e) {
            throw new RuntimeException(JSONObject.toJSONString(new AgentErrorMsg(e.getErrCode(), e.getMessage())));
        }
        log.info("返回结果:{}", JSONObject.toJSON(result));

        return Response.from(
                aiMessageFrom(result),
                new TokenUsage(0, 0, 0),
                finishReasonFrom(result.getFinish_reason())
        );
    }

}
