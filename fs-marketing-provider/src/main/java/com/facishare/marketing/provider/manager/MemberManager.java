package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.ConcurrentHashSet;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.MemberEnrollArg;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.BuildCrmObjectByEnrollDataResult;
import com.facishare.marketing.api.result.MemberCheckResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.result.member.MemberEnrollResult;
import com.facishare.marketing.api.result.memberCenter.QueryMemberContentResult;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.vo.conference.AddCampaignMembersObjVO;
import com.facishare.marketing.common.contstant.CustomizeFormDataConstants;
import com.facishare.marketing.common.contstant.OfficialWebsiteConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollSourceTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceNotificationTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO;
import com.facishare.marketing.provider.dao.member.MemberMarketingEventCrmConfigDAO;
import com.facishare.marketing.provider.dto.CustomizeFormDataUserObjectDTO;
import com.facishare.marketing.provider.dto.MemberCookieInfo;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity;
import com.facishare.marketing.provider.entity.member.MemberMarketingEventCrmConfigEntity;
import com.facishare.marketing.provider.entity.sms.ExtraSmsParamObject;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MemberDescribeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.UserBehaviorRecordObjManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2Manager.LeadDuplicateSearchResult;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.service.usermarketingaccount.UserMarketingAccountServiceImpl;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ContactData;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.MemberStatusResult;
import com.fxiaoke.crmrestapi.service.MemberService;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.paasauthrestapi.arg.BatchCreateEntityShareArg;
import com.fxiaoke.paasauthrestapi.common.data.PaasAuthContextData;
import com.fxiaoke.paasauthrestapi.common.data.ShareRuleData;
import com.fxiaoke.paasauthrestapi.service.PaasShareRuleService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class MemberManager {
    @Autowired
    private MemberService memberService;
    @Autowired
    private EIEAConverter eiEaConverter;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MemberAccessibleObjectDao memberAccessibleObjectDao;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MemberConfigDao memberConfigDao;
    @ReloadableProperty("defaultMemberRegisterSiteId")
    private String defaultMemberRegisterSiteId;
    @ReloadableProperty("defaultMemberLoginSiteId")
    private String defaultMemberLoginSiteId;
    @ReloadableProperty("defaultMemberContentCenterSiteId")
    private String defaultMemberContentCenterSiteId;
    @ReloadableProperty("defaultMemberUpdateInfoSiteId")
    private String defaultMemberUpdateInfoSiteId;
    @ReloadableProperty("defaultMemberForgotPasswordSiteId")
    private String defaultMemberForgotPasswordSiteId;
    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private WxMiniAppUserMemberBindDao wxMiniAppUserMemberBindDao;
    @Autowired
    private WxServiceUserMemberBindDao wxServiceUserMemberBindDao;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private PaasShareRuleService paasShareRuleService;
    @Autowired
    private MemberMarketingEventCrmConfigDAO memberMarketingEventCrmConfigDAO;
    @Autowired
    private MemberAccessibleCampaignDAO memberAccessibleCampaignDAO;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    public static final String MARKETING_MEMBER_CAMPAIGN_DATA_LOCK_KEY = "MARKETING_MEMBER_CAMPAIGN_DATA_LOCK";
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private MemberMarketingEventCrmConfigDAO memberObjectCrmConfigDAO;
    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private ClueManagementManager clueManagementManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private SpreadChannelManager spreadChannelManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;
    @Autowired
    private MemberDescribeManager memberDescribeManager;
    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;
    @Autowired
    private ObjectManager objectManager;
    @Value("${qywx.crm.appid}")
    private String qywxCrmAppid;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;
    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;
    @Autowired
    private ObjectTagDAO objectTagDAO;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private StaffMiniappUserBindManager staffMiniappUserBindManager;


    @Autowired
    private UserBehaviorRecordObjManager userBehaviorRecordObjManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    private Set<String> memberOpenedEas = new ConcurrentHashSet<>();
    private Cache<String, Integer> memberNotOpenedEasCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).build();

    public MemberStatusResult isOpenMember(String ea, Integer operatorFsUserId) {
        Integer ei = eaToEi(ea);
        return memberService.enableMember(new HeaderObj(ei, operatorFsUserId)).getData();
    }

    public boolean isOpenMember(String ea){
        try {
            if (memberOpenedEas.contains(ea)){
                return true;
            }
            if (memberNotOpenedEasCache.getIfPresent(ea) != null){
                return false;
            }
            boolean opened = isOpenMember(ea, -10000).getEnableStatus() == 1;
            if (opened){
                memberOpenedEas.add(ea);
            }else{
                memberNotOpenedEasCache.put(ea, 1);
            }
            return opened;
        }catch (Exception e){
            log.warn("Exception", e);
            return false;
        }
    }
    
    public Optional<String> getH5LoginMemberId(String ea, Map<String, String> allEaMemberCookies){
        if (Strings.isNullOrEmpty(allEaMemberCookies.get(ea))){
            return Optional.empty();
        }
        Optional<String> optionalMember = MemberCookieInfo.aesDecodeAndGetMemberId(allEaMemberCookies.get(ea));
        if (optionalMember.isPresent() && checkMemberValid(ea, optionalMember.get())){
            return optionalMember;
        }
        return Optional.empty();
    }
    
    private boolean checkMemberValid(String ea, String memberId){
        try {
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
            if (objectData != null && objectData.getId().equals(memberId)){
                return true;
            }
            return false;
        }catch (Exception e){
            log.warn("Exception occurred", e);
        }
        return false;
    }
    
    public Optional<String> getWxMiniAppUserBindMemberId(String ea, String uid){
        return Optional.ofNullable(wxMiniAppUserMemberBindDao.getMemberIdByUid(ea, uid));
    }
    
    public Optional<String> getWxServiceUserBindMemberId(String ea, String wxAppId, String wxOpenId){
        return Optional.ofNullable(wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(ea, wxAppId, wxOpenId));
    }
    
    public Optional<String> getWxOpenIdByMemberId(String ea, String wxAppId, String memberId){
        return Optional.ofNullable(wxServiceUserMemberBindDao.getWxOpenIdByMemberId(ea, wxAppId, memberId));
    }

    public double countAllAvailableIntegral(String ea){
        Integer tenantId = eiEaConverter.enterpriseAccountToId(ea);
        return memberService.countAllAvailableIntegral(new HeaderObj(tenantId, -10000)).getData().getAllAvailableIntegralCount();
    }

    private Integer eaToEi(String fsEa) {
        return eiEaConverter.enterpriseAccountToId(fsEa);
    }

    public Optional<ObjectData> getMemberByEaAndPhone(String ea, String phone){
        List<ObjectData> members = crmV2Manager.listByEaAndPhone(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), phone);
        return members.isEmpty() ? Optional.empty() : (phone.equals(members.get(0).getString(CrmMemberFieldEnum.PHONE.getApiName())) ? Optional.of(members.get(0)) : Optional.empty());
    }

    public Result<String> saveMemberByFormData(String ea, CustomizeFormDataEnrollArg arg, String addSource, Object avatar, String marketingPromotionSourceId){
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId());
        FieldMappings fieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
        Set<String> notNullCrmMemberFieldNames = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MEMBER).stream().filter(field -> BooleanUtils.isTrue(field.getIsNotNull())).map(CrmUserDefineFieldVo::getFieldName).collect(Collectors.toSet());
        boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmMemberFieldNames);
        arg.setEa(ea);
        if (!verifyResult) {
            return Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR);
        }
        if(arg.getSpreadFsUid()!=null){
            arg.getSubmitContent().setSpreadFsUid(arg.getSpreadFsUid());
        }
        if(!Strings.isNullOrEmpty(arg.getIpAddr())){
            arg.getSubmitContent().setIpAddr(arg.getIpAddr());
        }
        if(!Strings.isNullOrEmpty(arg.getUserAgent())){
            arg.getSubmitContent().setUserAgent(arg.getUserAgent());
        }
        Map<String, Object> crmMemberData = crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(customizeFormDataEntity, notNullCrmMemberFieldNames, arg.getSubmitContent());
        ActionAddArg actionAddArg = new ActionAddArg();
        ObjectData crmData = new ObjectData(CrmObjectApiNameEnum.MEMBER.getName());
        crmData.putAll(crmMemberData);
        crmData.setOwner(-10000);
        ObjectDescribe memberObjDescribe = memberDescribeManager.getMemberObjDescribe(ea);
        if(memberObjDescribe!=null && memberObjDescribe.getFields().containsKey("approval_status")){
            //如果开通注册审核,则状态为待审核
            MemberConfigEntity config = memberConfigDao.getByEa(ea);
            if(config.isRegisterReview()){
                crmData.put("approval_status",MemberApprovalStatusEnum.REVIEW_WAITING.getType());
            }else {
                crmData.put("approval_status",MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
            }
        }
        //处理会员推广相关字段
        //处理8.8会员预设对象字段
        initMemberObjFiled(ea,arg,crmData,memberObjDescribe);
        actionAddArg.setObjectData(crmData);
        crmData.putIfAbsent(CrmMemberFieldEnum.ADD_SOURCE.getApiName(), addSource);
        crmData.putIfAbsent(CrmMemberFieldEnum.AVATAR.getApiName(), avatar);
        if (StringUtils.isBlank(marketingPromotionSourceId)) {
            marketingPromotionSourceId = createMarketingPromotionSourceObj(arg);
        }
        if (StringUtils.isNotBlank(marketingPromotionSourceId)) {
            crmData.putIfAbsent(CrmMemberFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getApiName(), marketingPromotionSourceId);
        }
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> crmResult = metadataActionService.add(new HeaderObj(eiEaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.MEMBER.getName(), false, actionAddArg);
        if (!crmResult.isSuccess() || crmResult.getData() == null || crmResult.getData().getObjectData() == null || Strings.isNullOrEmpty(crmResult.getData().getObjectData().getId())){
            return Result.newError(SHErrorCode.SERVER_BUSY.getErrorCode(), crmResult.getMessage());
        }
        String memberId = crmResult.getData().getObjectData().getId();
        return Result.newSuccess(memberId);
    }

    private String createMarketingPromotionSourceObj(CustomizeFormDataEnrollArg arg) {
        MarketingPromotionSourceArg marketingPromotionSourceArg = BeanUtil.copy(arg, MarketingPromotionSourceArg.class);
        String id = null;
        try {
            marketingPromotionSourceArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule());
            marketingPromotionSourceArg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
            id = marketingPromotionSourceObjManager.tryGetOrCreateObj(marketingPromotionSourceArg);
        } catch (Exception e) {
            log.error("创建营销推广来源异常，arg:[{}]", marketingPromotionSourceArg, e);
        }
        return id;
    }

    //预设相关字段
    private void initMemberObjFiled(String ea, CustomizeFormDataEnrollArg arg, ObjectData crmData,ObjectDescribe memberDescribe) {
        //推广渠道
        if (!Strings.isNullOrEmpty(arg.getChannelValue())){
            getSpreadChannelValue(ea, arg.getChannelValue() , crmData);
        }
        Integer fsUserId = arg.getSpreadFsUid();
        if (fsUserId!= null){
            //虚拟id转fsUserId
            if(fsUserId>= QywxUserConstants.BASE_VIRTUAL_USER_ID){
                String qyUserId = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfo(ea, fsUserId);
                Result<Map<String, String>> mapResult = qyweixinAccountBindManager.outAccountToFsAccountBatch(ea, qywxCrmAppid, Lists.newArrayList(qyUserId));
                if (mapResult.isSuccess() && org.apache.commons.collections.MapUtils.isNotEmpty(mapResult.getData())) {
                    Map<String, String> userIdMap = mapResult.getData();
                    String userId = userIdMap.get(qyUserId);
                    if (StringUtils.isNotBlank(userId)) {
                        String[] split = userId.split("\\.");
                        if (split.length != 0) {
                            fsUserId = Integer.parseInt(split[split.length - 1]);
                            if (crmData.get("spread_employee") == null) {
                                crmData.put("spread_employee", Lists.newArrayList(String.valueOf(fsUserId)));
                            }
                            if (memberDescribe!=null && memberDescribe.getFields().containsKey("spread_user_id") && crmData.get("spread_user_id") == null) {
                                crmData.put("spread_user_id", Lists.newArrayList(String.valueOf(fsUserId)));
                            }
                        }
                    }
                }
            }else{
                if (crmData.get("spread_employee") == null) {
                    crmData.put("spread_employee", Lists.newArrayList(String.valueOf(fsUserId)));
                }
                if (memberDescribe!=null && memberDescribe.getFields().containsKey("spread_user_id") && crmData.get("spread_user_id") == null) {
                    crmData.put("spread_user_id", Lists.newArrayList(String.valueOf(fsUserId)));
                }
            }
        }
        CustomizeFormDataEnroll customizeFormDataEnroll = arg.getSubmitContent();
        if(!Strings.isNullOrEmpty(customizeFormDataEnroll.getSourceObjectId()) && customizeFormDataEnroll.getSourceObjectType()!=null){
            //来源推广内容创建人
            Integer createUser = objectManager.getObjectCreateUser(customizeFormDataEnroll.getSourceObjectId(), customizeFormDataEnroll.getSourceObjectType());
            if(createUser!=null && memberDescribe!=null && memberDescribe.getFields().containsKey("source_spread_creator") && crmData.get("source_spread_creator") == null){
                crmData.put("source_spread_creator", Lists.newArrayList(String.valueOf(createUser)));
            }
            //来源推广内容名称
            String name = objectManager.getObjectName(customizeFormDataEnroll.getSourceObjectId(), customizeFormDataEnroll.getSourceObjectType());
            if (memberDescribe!=null && memberDescribe.getFields().containsKey("source_spread_content") && crmData.get("source_spread_content") == null) {
                crmData.put("source_spread_content", name);
            }
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("marketing_source_type") && crmData.get("marketing_source_type") == null) {
            crmData.put("marketing_source_type",customizeFormDataEnroll.getMarketingSourceType());
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("marketing_source_site") && crmData.get("marketing_source_site") == null) {
            crmData.put("marketing_source_site",customizeFormDataEnroll.getMarketingSourceSite());
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("user_agent") && crmData.get("user_agent") == null) {
            crmData.put("user_agent",arg.getUserAgent());
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("ip_address") && crmData.get("ip_address") == null) {
            crmData.put("ip_address",arg.getIpAddr());
        }

    }

    //预设相关字段
    private void handleMemberObjFiled(String ea, CustomizeFormDataUserEntity arg, ObjectData crmData) {
        ObjectDescribe memberDescribe = memberDescribeManager.getMemberObjDescribe(ea);
        Integer fsUserId = arg.getSpreadFsUid();
        if (fsUserId!= null){
            //虚拟id转fsUserId
            if(fsUserId>= QywxUserConstants.BASE_VIRTUAL_USER_ID){
                String qyUserId = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfo(ea, fsUserId);
                Result<Map<String, String>> mapResult = qyweixinAccountBindManager.outAccountToFsAccountBatch(ea, qywxCrmAppid, Lists.newArrayList(qyUserId));
                if (mapResult.isSuccess() && org.apache.commons.collections.MapUtils.isNotEmpty(mapResult.getData())) {
                    Map<String, String> userIdMap = mapResult.getData();
                    String userId = userIdMap.get(qyUserId);
                    if (StringUtils.isNotBlank(userId)) {
                        String[] split = userId.split("\\.");
                        if (split.length != 0) {
                            fsUserId = Integer.parseInt(split[split.length - 1]);
                            if (crmData.get("spread_employee") == null) {
                                crmData.put("spread_employee", Lists.newArrayList(String.valueOf(fsUserId)));
                            }
                            if (memberDescribe!=null && memberDescribe.getFields().containsKey("spread_user_id") && crmData.get("spread_user_id") == null) {
                                crmData.put("spread_user_id", Lists.newArrayList(String.valueOf(fsUserId)));
                            }
                        }
                    }
                }
            }else{
                if (crmData.get("spread_employee") == null) {
                    crmData.put("spread_employee", Lists.newArrayList(String.valueOf(fsUserId)));
                }
                if (memberDescribe!=null && memberDescribe.getFields().containsKey("spread_user_id") && crmData.get("spread_user_id") == null) {
                    crmData.put("spread_user_id", Lists.newArrayList(String.valueOf(fsUserId)));
                }
            }
        }
        CustomizeFormDataEnroll customizeFormDataEnroll = arg.getSubmitContent();
        if(!Strings.isNullOrEmpty(customizeFormDataEnroll.getSourceObjectId()) && customizeFormDataEnroll.getSourceObjectType()!=null){
            //来源推广内容创建人
            Integer createUser = objectManager.getObjectCreateUser(customizeFormDataEnroll.getSourceObjectId(), customizeFormDataEnroll.getSourceObjectType());
            if(createUser!=null && memberDescribe!=null && memberDescribe.getFields().containsKey("source_spread_creator") && crmData.get("source_spread_creator") == null){
                crmData.put("source_spread_creator", Lists.newArrayList(String.valueOf(createUser)));
            }
            //来源推广内容名称
            String name = objectManager.getObjectName(customizeFormDataEnroll.getSourceObjectId(), customizeFormDataEnroll.getSourceObjectType());
            if (memberDescribe!=null && memberDescribe.getFields().containsKey("source_spread_content") && crmData.get("source_spread_content") == null) {
                crmData.put("source_spread_content", name);
            }
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("marketing_source_type") && crmData.get("marketing_source_type") == null) {
            crmData.put("marketing_source_type",customizeFormDataEnroll.getMarketingSourceType());
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("marketing_source_site") && crmData.get("marketing_source_site") == null) {
            crmData.put("marketing_source_site",customizeFormDataEnroll.getMarketingSourceSite());
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("user_agent") && crmData.get("user_agent") == null) {
            crmData.put("user_agent",arg.getUserAgent());
        }
        if (memberDescribe!=null && memberDescribe.getFields().containsKey("ip_address") && crmData.get("ip_address") == null) {
            crmData.put("ip_address",arg.getIpAddr());
        }
        if (StringUtils.isNotBlank(customizeFormDataEnroll.getMarketingPromotionSourceId())) {
            crmData.put("marketing_promotion_source_id", customizeFormDataEnroll.getMarketingPromotionSourceId());
        }
    }

    public Result updateMemberByFormData(String ea, String memberId, CustomizeFormDataEnrollArg arg) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId());
        FieldMappings fieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
        Set<String> notNullCrmMemberFieldNames = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MEMBER).stream().filter(field -> BooleanUtils.isTrue(field.getIsNotNull()))
            .map(CrmUserDefineFieldVo::getFieldName).collect(Collectors.toSet());
        boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmMemberFieldNames);
        if (!verifyResult) {
            return Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR);
        }
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> result = null;
        try {
            Map<String, Object> crmMemberData = crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(customizeFormDataEntity, notNullCrmMemberFieldNames, arg.getSubmitContent());
            ContactData data = new ContactData();
            data.putAll(crmMemberData);
            data.put(CrmV2LeadFieldEnum.ID.getNewFieldName(), memberId);
            ActionEditArg actionEditArg = new ActionEditArg();
            actionEditArg.setObjectData(data);
            result = metadataActionService.edit(crmV2Manager.createHeaderObj(ea, -10000), CrmObjectApiNameEnum.MEMBER.getName(), true, true, actionEditArg);
            if (result.isSuccess()) {
                return Result.newSuccess();
            } else {
                return Result.newError(result.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            log.warn("MemberManager.updateMemberByFormData error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    public Result<String> saveMemberByLoginFormData(String ea, CustomizeFormDataEnrollArg arg, String addSource, Object avatar){
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId());
        if (customizeFormDataEntity == null){
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        FieldMappings fieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
        Set<String> notNullCrmMemberFieldNames = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MEMBER).stream().filter(field -> BooleanUtils.isTrue(field.getIsNotNull())).map(CrmUserDefineFieldVo::getFieldName).collect(Collectors.toSet());
        boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmMemberFieldNames);
        if (!verifyResult) {
            return Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR);
        }
        if(arg.getSpreadFsUid()!=null){
            arg.getSubmitContent().setSpreadFsUid(arg.getSpreadFsUid());
        }
        if(!Strings.isNullOrEmpty(arg.getIpAddr())){
            arg.getSubmitContent().setIpAddr(arg.getIpAddr());
        }
        if(!Strings.isNullOrEmpty(arg.getUserAgent())){
            arg.getSubmitContent().setUserAgent(arg.getUserAgent());
        }
        Map<String, Object> crmMemberData = crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(customizeFormDataEntity, notNullCrmMemberFieldNames, arg.getSubmitContent());
        ActionAddArg actionAddArg = new ActionAddArg();
        ObjectData crmData = new ObjectData(CrmObjectApiNameEnum.MEMBER.getName());
        crmData.putAll(crmMemberData);
        crmData.setOwner(-10000);
        if(Strings.isNullOrEmpty(crmData.getName())){
            crmData.setName(arg.getSubmitContent().getPhone());
        }
        ObjectDescribe memberObjDescribe = memberDescribeManager.getMemberObjDescribe(ea);
        if(memberObjDescribe!=null && memberObjDescribe.getFields().containsKey("approval_status")){
            //如果开通注册审核,则状态为待审核
            MemberConfigEntity config = memberConfigDao.getByEa(ea);
            if(config.isRegisterReview()){
                crmData.put("approval_status",MemberApprovalStatusEnum.REVIEW_WAITING.getType());
            }else {
                crmData.put("approval_status",MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
            }
        }
        //处理会员推广相关字段
        initMemberObjFiled(ea,arg,crmData,memberObjDescribe);
        actionAddArg.setObjectData(crmData);
        crmData.putIfAbsent(CrmMemberFieldEnum.ADD_SOURCE.getApiName(), addSource);
        crmData.putIfAbsent(CrmMemberFieldEnum.AVATAR.getApiName(), avatar);
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> crmResult = metadataActionService.add(new HeaderObj(eiEaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.MEMBER.getName(), false, actionAddArg);
        if (!crmResult.isSuccess() || crmResult.getData() == null || crmResult.getData().getObjectData() == null || Strings.isNullOrEmpty(crmResult.getData().getObjectData().getId())){
            return Result.newError(SHErrorCode.SERVER_BUSY.getErrorCode(), crmResult.getMessage());
        }
        String memberId = crmResult.getData().getObjectData().getId();
        return Result.newSuccess(memberId);
    }

    public void getSpreadChannelValue(String ea,String channelValue, ObjectData crmData) {
        if (SpreadChannelManager.promotionChannelMap.containsValue(channelValue)) {
            HashBiMap<Object, Object> objectObjectHashBiMap = HashBiMap.create();
            objectObjectHashBiMap.putAll(SpreadChannelManager.promotionChannelMap);
            BiMap<Object, Object> inverse = objectObjectHashBiMap.inverse();
            crmData.put("spread_channel", inverse.get(channelValue));
        } else if (channelValue.contains("other")) {
            crmData.put("spread_channel", channelValue.replace("other","其他"));
        } else {
            crmData.put("spread_channel",channelValue);
            Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(ea);
            crmData.put("spread_channel",spreadChannelManager.getChannelLabelByChannelValue(ea, channelValueMap, channelValue));
        }
    }

    public Result<Map<String, Object>> buildMemberInfoByForm(CustomizeFormDataEntity customizeFormDataEntity, String memberId) {
        // 查询会员详情
        ObjectData objectData = null;
        Map<String, Object> resultMap = Maps.newHashMap();
        try {
            objectData = crmV2Manager.getDetail(customizeFormDataEntity.getEa(), -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        } catch (Exception e) {
            log.warn("MemberManager.buildMemberInfoByForm error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (objectData == null) {
            log.warn("MemberManager.buildMemberInfoByForm error objectData is null ea:{}, memberId:{}", customizeFormDataEntity.getEa(), memberId);
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }
        // 查询对象描述
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = crmV2Manager.getObjectFieldDescribesList(customizeFormDataEntity.getEa(), CrmObjectApiNameEnum.MEMBER);
        if (CollectionUtils.isEmpty(crmUserDefineFieldVoList)) {
            log.warn("MemberManager.buildMemberInfoByForm error crmUserDefineFieldVoList is null");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        Map<String, CrmUserDefineFieldVo> fieldMap = crmUserDefineFieldVoList.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, data -> data));
        FieldMappings fieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
        for (FieldMappings.FieldMapping fieldMapping : fieldMappings) {
            Object memberData = objectData.get(fieldMapping.getCrmFieldName());
            if (memberData != null) {
                CrmUserDefineFieldVo crmUserDefineFieldVo = fieldMap.get(fieldMapping.getCrmFieldName());
                if (crmUserDefineFieldVo != null && crmUserDefineFieldVo.getFieldTypeName().equals("image")) {
                    // 设置可展示图片
                    List<Map<String, Object>> imageDataMapList = GsonUtil.fromJson(GsonUtil.toJson(memberData), new TypeToken<List<Map<String,Object>>>(){}.getType());
                    List<Map<String, Object>> resultImage = Lists.newArrayList();
                    for (Map<String, Object> imagePathMap : imageDataMapList) {
                        Map<String, Object> newImagePathMap = Maps.newHashMap();
                        String imagePath = imagePathMap.get("path") + "." + imagePathMap.get("ext");
                        String url = fileV2Manager.getUrlByPath(imagePath, customizeFormDataEntity.getEa(), false);
                        newImagePathMap.putAll(imagePathMap);
                        newImagePathMap.put("showUrl", url);
                        resultImage.add(newImagePathMap);
                    }
                    resultMap.put(fieldMapping.getMankeepFieldName(), resultImage);
                } else {
                    resultMap.put(fieldMapping.getMankeepFieldName(), memberData);
                }
            }
        }
        return Result.newSuccess(resultMap);
    }

    public Optional<String> saveMemberToLead(String ea, String memberId, String marketingActivityId,
                                             String marketingEventId, Integer spreadFsUserId, String channelValue,
                                             String marketingPromotionSourceId, String formId,String keywordId, CustomizeFormDataEnrollArg arg){
        MemberConfigEntity memberConfig = tryInitMemberConfig(ea);
        if (memberConfig == null || memberConfig.getMemberToLeadFieldMappings() == null) {
            return Optional.empty();
        }
        ObjectData leadObjectDataToSave = crmV2Manager.convertObjectDataByFieldMapping(ea, CrmObjectApiNameEnum.MEMBER.getName(), memberId, CrmObjectApiNameEnum.CRM_LEAD.getName(), memberConfig.getMemberToLeadFieldMappings());
        if (leadObjectDataToSave == null){
            return Optional.empty();
        }
        String phone = UserMarketingAccountServiceImpl.getPhoneByChannelObject(ChannelEnum.CRM_LEAD.getType(), leadObjectDataToSave);
        if (!Strings.isNullOrEmpty(phone)){
            Optional<ObjectData> leadOptional = crmV2Manager.getCrmLeadByPhone(ea, -10000, phone);
            if (leadOptional.isPresent()){
                campaignMergeDataManager.addCampaignMembersObjByBindObj(Arrays.asList(new AddCampaignMembersObjVO.MemberObjDetail(CrmObjectApiNameEnum.CRM_LEAD.getName(), leadOptional.get().getId())), ea, marketingEventId, CampaignMembersObjMemberStatusEnum.REGISTERED.getValue(), -10000);
                return Optional.of(leadOptional.get().getId());
            }
        }
        if (StringUtils.isNotBlank(keywordId)) {
            leadObjectDataToSave.put("keyword_id", keywordId);
        }
        if (!Strings.isNullOrEmpty(memberConfig.getLeadRecordType())){
            leadObjectDataToSave.put("record_type", memberConfig.getLeadRecordType());
        }
        leadObjectDataToSave.setOwner(-10000);
        if (!Strings.isNullOrEmpty(memberConfig.getLeadPoolId())){
            leadObjectDataToSave.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), memberConfig.getLeadPoolId());
            leadObjectDataToSave.remove("owner");
        }
        leadObjectDataToSave.put(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), marketingEventId);
        leadObjectDataToSave.put("marketing_activity_id__c", marketingActivityId);
        if (spreadFsUserId != null){
            leadObjectDataToSave.put(OfficialWebsiteConstants.MARKETING_SPREAD_USER, Lists.newArrayList(spreadFsUserId.toString()));
        }
        leadObjectDataToSave.put(CrmV2LeadFieldEnum.PromotionChannel.getNewFieldName(), channelValue);
        leadObjectDataToSave.put("from_marketing", true);
        if (StringUtils.isNotBlank(marketingPromotionSourceId)) {
            leadObjectDataToSave.put(CrmMemberFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getApiName(), marketingPromotionSourceId);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
        if (customizeFormDataEntity != null) {
            clueManagementManager.addCustomizeFormOrganizationData(leadObjectDataToSave, ea,marketingEventId,customizeFormDataEntity);
        }
        if (!leadObjectDataToSave.containsKey(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION)) {
            List<String> orgList = clueManagementManager.getDefaultDataOwnOrganization(ea);
            if (CollectionUtils.isNotEmpty(orgList)) {
                leadObjectDataToSave.put(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION, orgList);
            }
        }
        ActionAddArg actionAddArg = new ActionAddArg();
        // 处理utm参数
        handleUtmParam(ea, marketingPromotionSourceId, marketingEventId, leadObjectDataToSave);
        if (arg != null) {
            // 如果有落地页 获取或者创建落地页的id
            String landingName = adCommonManager.getLandingName(arg);
            String landingObjId = adOCPCUploadManager.getOrCreateLandingObjId(ea, arg.getLandingUrl(), landingName, arg.getMarketingEventId());
            if (StringUtils.isNotBlank(landingObjId)) {
                leadObjectDataToSave.put("landing_page_id", landingObjId);
            }
        }
        actionAddArg.setObjectData(leadObjectDataToSave);
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService.add(new HeaderObj(eiEaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.CRM_LEAD.getName(), false, actionAddArg);
        if (!result.isSuccess() || result.getData() == null || result.getData().getObjectData() == null){
            log.warn("MemberManager saveMemberToLead error: {}", JSONObject.toJSONString(result));
            return Optional.empty();
        }
        String leadId = result.getData().getObjectData().getId();
        campaignMergeDataManager.addCampaignMembersObjByBindObj(Arrays.asList(new AddCampaignMembersObjVO.MemberObjDetail(CrmObjectApiNameEnum.CRM_LEAD.getName(), leadId)), ea, marketingEventId, CampaignMembersObjMemberStatusEnum.REGISTERED.getValue(), -10000);
        return Optional.of(leadId);
    }

    public void handleUtmParam(String ea, String marketingPromotionSourceId, String marketingEventId, ObjectData leadObjectDataToSave) {
        if (StringUtils.isBlank(marketingPromotionSourceId)) {
            return;
        }
        try {
            ObjectData objectData = marketingPromotionSourceObjManager.getById(ea, marketingPromotionSourceId);
            if (objectData == null) {
                return;
            }
            String utmCampaign = objectData.getString("utm_campaign");
            String utmMedium = objectData.getString("utm_medium");
            String utmSource = objectData.getString("utm_source");
            String utmContent = objectData.getString("utm_content");
            String utmTerm = objectData.getString("utm_term");
            marketingEventId = StringUtils.isNotBlank(marketingEventId) ? marketingEventId : objectData.getString("marketing_event_id");
            CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
            customizeFormDataEnroll.setUtmCampaig(utmCampaign);
            customizeFormDataEnroll.setUtmMedium(utmMedium);
            customizeFormDataEnroll.setUtmSource(utmSource);
            customizeFormDataEnroll.setUtmContent(utmContent);
            customizeFormDataEnroll.setUtmTerm(utmTerm);
            if (StringUtils.isNotBlank(marketingEventId)) {
                leadObjectDataToSave.put("marketing_event_id", marketingEventId);
            }
            customizeFormDataManager.handleLeadsSpecialField(ea, customizeFormDataEnroll, leadObjectDataToSave);
        } catch (Exception e) {
            log.error("会员转线索处理utm失败，ea: {} marketingPromotionSourceId: {}", ea, marketingPromotionSourceId, e);
        }
    }

    public SaveMemberToCampaignMergeDataResultContainer saveMemberToCampaignMergeData(String uid, String wxAppId, String openId, String fingerPrint, String ea,
                                                                                      String memberId, String marketingActivityId, String marketingEventId,
                                                                                      Integer spreadFsUserId, String channelValue, String objectId,
                                                                                      Integer objectType, boolean needCreateDataIfNoBindCRM, String marketingPromotionSourceId) {
        SaveMemberToCampaignMergeDataResultContainer resultContainer = new SaveMemberToCampaignMergeDataResultContainer();
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(memberId) || StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(objectId) || objectType == null) {
            log.warn("MemberManager.saveMemberToLead param error");
            return null;
        }
        // 查询该会员是否已存在绑定活动成员的数据
        MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(ea, marketingEventId, memberId);
        if (memberAccessibleCampaignEntity != null) {
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(memberAccessibleCampaignEntity.getCampaignId());
            if (StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                return null;
            }
        }
        // 操作数据上锁
        StringJoiner sj = new StringJoiner("_");
        sj.add(MARKETING_MEMBER_CAMPAIGN_DATA_LOCK_KEY);
        sj.add(ea);
        sj.add(marketingEventId);
        sj.add(memberId);
        boolean redisLock = redisManager.lock(sj.toString(), 100);
        if (!redisLock) {
            return null;
        }
        String leadId = null;
        boolean saveCrmLeadError = false;
        String saveCrmErrorMessage = null;
        LeadDuplicateSearchResult leadDuplicateSearchResultData = null;
        try {
            ObjectData memberObject = crmV2Manager.getDetail(ea, -10000,  CrmObjectApiNameEnum.MEMBER.getName(), memberId);
            if (memberObject == null) {
                log.warn("MemberManager.saveMemberToLead memberObject is null memberId:{}", memberId);
                return null;
            }
            SaveMemberToLeadForCampaignContainer saveMemberToLeadForCampaignContainer = saveMemberToLeadForCampaign(ea, memberId, marketingActivityId, marketingEventId, spreadFsUserId, channelValue, objectId, objectType, marketingPromotionSourceId);
            leadId = saveMemberToLeadForCampaignContainer.getLeadId();
            saveCrmLeadError = saveMemberToLeadForCampaignContainer.isSaveCrmLeadError();
            saveCrmErrorMessage = saveMemberToLeadForCampaignContainer.getSaveCrmErrorMessage();
            leadDuplicateSearchResultData = saveMemberToLeadForCampaignContainer.getLeadDuplicateSearchResultData();
            AddCampaignMergeDataByMemberArgContainer addCampaignMergeDataByMemberContainer = new AddCampaignMergeDataByMemberArgContainer();
            addCampaignMergeDataByMemberContainer.setEa(ea);
            addCampaignMergeDataByMemberContainer.setUid(uid);
            addCampaignMergeDataByMemberContainer.setWxAppId(wxAppId);
            addCampaignMergeDataByMemberContainer.setOpenId(openId);
            addCampaignMergeDataByMemberContainer.setFingerPrint(fingerPrint);
            addCampaignMergeDataByMemberContainer.setMarketingEventId(marketingEventId);
            addCampaignMergeDataByMemberContainer.setMarketingActivityId(marketingActivityId);
            addCampaignMergeDataByMemberContainer.setMemberId(memberId);
            addCampaignMergeDataByMemberContainer.setMemberAccessibleCampaignId(memberAccessibleCampaignEntity != null ? memberAccessibleCampaignEntity.getId() : null);
            addCampaignMergeDataByMemberContainer.setCampaignId(memberAccessibleCampaignEntity != null ? memberAccessibleCampaignEntity.getCampaignId() : null);
            addCampaignMergeDataByMemberContainer.setNeedCreateDataIfNoBindCRM(needCreateDataIfNoBindCRM);
            addCampaignMergeDataByMemberContainer.setName(memberObject.getName());
            addCampaignMergeDataByMemberContainer.setPhone(memberObject.getString("phone"));
            addCampaignMergeDataByMemberContainer.setObjectId(objectId);
            addCampaignMergeDataByMemberContainer.setObjectType(objectType);
            if (StringUtils.isNotBlank(leadId)) {
                addCampaignMergeDataByMemberContainer.setBindObjectType(StringUtils.isBlank(saveMemberToLeadForCampaignContainer.getCrmObjectType())
                        ? CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType() : CampaignMergeDataObjectTypeEnum.getTypeByName(saveMemberToLeadForCampaignContainer.getCrmObjectType()));
                addCampaignMergeDataByMemberContainer.setBindObjectId(leadId);
            }
            addCampaignMergeDataByMemberContainer.setChannelValue(channelValue);
            addCampaignMergeDataByMemberContainer.setSpreadFsUid(spreadFsUserId);
            if (saveCrmLeadError) {
                addCampaignMergeDataByMemberContainer.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                addCampaignMergeDataByMemberContainer.setSaveCrmErrorMessage(saveCrmErrorMessage);
            }
            if (StringUtils.isNotBlank(leadId)) {
                addCampaignMergeDataByMemberContainer.setSaveCrmStatus(SaveCrmStatusEnum.SUCCESS.getValue());
                //如果查重有相同的,并且只有一条数据,则直接进行关联
                if (leadDuplicateSearchResultData != null && !leadDuplicateSearchResultData.isDuplicate() && StringUtils.isNotBlank(leadDuplicateSearchResultData.getLeadId())) {
                    addCampaignMergeDataByMemberContainer.setSaveCrmStatus(SaveCrmStatusEnum.LINKED.getValue());
                }
            }
            if (leadDuplicateSearchResultData != null && leadDuplicateSearchResultData.isDuplicate()) {
                addCampaignMergeDataByMemberContainer.setSaveCrmStatus(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue());
            }
            addCampaignMergeDataByMemberContainer.setMarketingPromotionSourceId(marketingPromotionSourceId);
            String campaignMergeDataId = campaignMergeDataManager.addCampaignMergeDataByMember(addCampaignMergeDataByMemberContainer);
            resultContainer.setLeadId(leadId);
            resultContainer.setCampaignMergeDataId(campaignMergeDataId);

            // 若为会议则生成会议相关数据
            if (StringUtils.isNotBlank(campaignMergeDataId)) {
                conferenceManager
                    .createConferenceTicketAndAttachedInfo(ea, objectType, objectId, spreadFsUserId, ConferenceEnrollSourceTypeEnum.MEMBER_ENROLL.getType(), campaignMergeDataId, marketingEventId);
            }
        } catch (Exception e) {
            log.warn("MemberManager.saveMemberToLead error e: ", e);
            return null;
        } finally {
            redisManager.unLock(sj.toString());
        }
        return resultContainer;
    }


    public SaveMemberToLeadForCampaignContainer saveMemberToLeadForCampaign(String ea, String memberId, String marketingActivityId, String marketingEventId, Integer spreadFsUserId,
                                                                            String channelValue, String objectId, Integer objectType, String marketingPromotionSourceId) {
        String leadId = null;
        SaveMemberToLeadForCampaignContainer saveMemberToLeadForCampaignContainer = new SaveMemberToLeadForCampaignContainer();
        saveMemberToLeadForCampaignContainer.setSaveCrmLeadError(true);
        // 查询基础配置
        MemberConfigEntity memberConfig = getMemberCrmConfig(ea, marketingEventId);
        if (memberConfig == null || memberConfig.getMemberToLeadFieldMappings() == null) {
            saveMemberToLeadForCampaignContainer.setSaveCrmErrorMessage(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_835));
            return saveMemberToLeadForCampaignContainer;
        }
        ObjectData leadObjectDataToSave = crmV2Manager.convertObjectDataByFieldMapping(ea, CrmObjectApiNameEnum.MEMBER.getName(), memberId, CrmObjectApiNameEnum.CRM_LEAD.getName(), memberConfig.getMemberToLeadFieldMappings());
        if (leadObjectDataToSave == null){
            saveMemberToLeadForCampaignContainer.setSaveCrmErrorMessage(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_840));
            return saveMemberToLeadForCampaignContainer;
        }
        LeadDuplicateSearchResult leadDuplicateSearchResultData = null;
        if (StringUtils.isBlank(leadId)) {
            // 联合查重
            Result<LeadDuplicateSearchResult> leadDuplicateSearchResult = crmV2Manager.leadDuplicateSearchByObject(ea, leadObjectDataToSave);
            // 保存crm销售线索
            if (!leadDuplicateSearchResult.isSuccess()) {
                saveMemberToLeadForCampaignContainer.setSaveCrmErrorMessage(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8));
                return saveMemberToLeadForCampaignContainer;
            }
            leadDuplicateSearchResultData = leadDuplicateSearchResult.getData();
            if (!leadDuplicateSearchResultData.isDuplicate() && StringUtils.isNotBlank(leadDuplicateSearchResultData.getLeadId())) {
                leadId = leadDuplicateSearchResultData.getLeadId();
            } else if (!leadDuplicateSearchResultData.isDuplicate()) {
                boolean addLeadsObjectAuth = false;
                Integer owner = spreadFsUserId;
                if (owner != null) {
                    addLeadsObjectAuth = customizeFormDataManager.checkAddLeadsObjectAuth(ea, owner);
                    if (!addLeadsObjectAuth) {
                        owner = clueDefaultSettingService.getClueCreator(marketingEventId, ea, clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(channelValue));
                    }
                } else {
                    owner = clueDefaultSettingService.getClueCreator(marketingEventId, ea, clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(channelValue));
                }
                if (!Strings.isNullOrEmpty(memberConfig.getLeadRecordType())) {
                    leadObjectDataToSave.put("record_type", memberConfig.getLeadRecordType());
                }
                leadObjectDataToSave.setOwner(owner);
                if (StringUtils.isNotBlank(memberConfig.getLeadPoolId())) {
                    leadObjectDataToSave.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), memberConfig.getLeadPoolId());
                    leadObjectDataToSave.remove("owner");
                }
                leadObjectDataToSave.put(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), marketingEventId);
                leadObjectDataToSave.put(OfficialWebsiteConstants.MARKETING_ACTIVITY_ID, marketingActivityId);
                if (spreadFsUserId != null) {
                    leadObjectDataToSave.put(OfficialWebsiteConstants.MARKETING_SPREAD_USER, Lists.newArrayList(spreadFsUserId.toString()));
                }
                if (StringUtils.isNotBlank(marketingActivityId)) {
                    leadObjectDataToSave.put(OfficialWebsiteConstants.MARKETING_ACTIVITY_ID, marketingActivityId);
                }
                spreadChannelManager.buildCRMChannelData(leadObjectDataToSave, channelValue);
                leadObjectDataToSave.put("from_marketing", true);
                if (StringUtils.isNotBlank(marketingPromotionSourceId)) {
                    leadObjectDataToSave.put(CrmMemberFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getApiName(), marketingPromotionSourceId);
                }
                Integer objectCreator = objectManager.getObjectCreateUser(objectId, objectType);
                if (objectCreator != null) {
                    CustomizeFormDataEntity customizeFormDataEntity = new CustomizeFormDataEntity();
                    customizeFormDataEntity.setCreateBy(objectCreator);
                    clueManagementManager.addCustomizeFormOrganizationData(leadObjectDataToSave, ea, marketingEventId,customizeFormDataEntity);
                }
                clueManagementManager.addCustomizeFormCommonData(leadObjectDataToSave, ea, objectId, objectType, null);
                ActionAddArg actionAddArg = new ActionAddArg();
                actionAddArg.setObjectData(leadObjectDataToSave);
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService
                    .add(new HeaderObj(eiEaConverter.enterpriseAccountToId(ea), owner), CrmObjectApiNameEnum.CRM_LEAD.getName(), false, actionAddArg);
                if (!result.isSuccess() || result.getData() == null || result.getData().getObjectData() == null) {
                    saveMemberToLeadForCampaignContainer.setSaveCrmErrorMessage(result.getMessage());
                    return saveMemberToLeadForCampaignContainer;
                } else {
                    leadId = result.getData().getObjectData().getId();
                }
            }
        }
        saveMemberToLeadForCampaignContainer.setLeadId(leadId);
        saveMemberToLeadForCampaignContainer.setCrmObjectType(leadDuplicateSearchResultData.getCrmObjectType());
        saveMemberToLeadForCampaignContainer.setLeadDuplicateSearchResultData(leadDuplicateSearchResultData);
        saveMemberToLeadForCampaignContainer.setSaveCrmLeadError(false);
        return saveMemberToLeadForCampaignContainer;
    }

    public boolean saveMemberToLeadByCampaignIds(List<String> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return true;
        }
        List<MemberAccessibleCampaignEntity> memberAccessibleCampaignEntityList = memberAccessibleCampaignDAO.queryMemberAccessibleCampaignSaveErrorData(campaignIds);
        if (CollectionUtils.isEmpty(memberAccessibleCampaignEntityList)) {
            return true;
        }
        MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignEntityList.get(0);
        MemberConfigEntity memberConfigEntity = getMemberCrmConfig(memberAccessibleCampaignEntity.getEa(), memberAccessibleCampaignEntity.getMarketingEventId());
        if (memberConfigEntity == null || memberConfigEntity.getMemberToLeadFieldMappings() == null) {
            log.warn("MemberManager.saveMemberToLeadByCampaignIds error memberConfigEntity is null");
            return false;
        }
        String triggerAction = "saveConferenceParticipantsToCrm";
        for (MemberAccessibleCampaignEntity data : memberAccessibleCampaignEntityList) {
            SaveMemberToCampaignMergeDataResultContainer saveMemberToCampaignMergeDataResultContainer = saveMemberToCampaignMergeData(data.getUid(), data.getWxAppId(), data.getOpenId(), data.getFingerPrint(), data.getEa(), data.getMemberId(), data.getMarketingActivityId(), data.getMarketingEventId(), data.getSpreadFsUid(), data.getChannelValue(), data.getObjectId(),
                data.getObjectType(), false, null);
            if (saveMemberToCampaignMergeDataResultContainer == null) {
                continue;
            }
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
            if (campaignMergeDataEntity == null) {
                continue;
            }
            if (StringUtils.isBlank(saveMemberToCampaignMergeDataResultContainer.getLeadId())) {
                continue;
            }
            if (StringUtils.isNotBlank(data.getUid())) {
                userMarketingAccountRelationManager
                    .bindMiniappUserAndLead(memberAccessibleCampaignEntity.getEa(), data.getUid(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), campaignMergeDataEntity.getPhone(), triggerAction);
            } else if (StringUtils.isNotBlank(data.getWxAppId()) && StringUtils.isNotBlank(data.getOpenId())) {
                userMarketingAccountRelationManager
                    .bindWxUserAndLead(memberAccessibleCampaignEntity.getEa(), data.getWxAppId(), data.getOpenId(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), campaignMergeDataEntity.getPhone(), triggerAction);
            } else if (StringUtils.isNotBlank(data.getFingerPrint())) {
                userMarketingAccountRelationManager
                    .bindBrowserUserAndLead(memberAccessibleCampaignEntity.getEa(), data.getFingerPrint(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), campaignMergeDataEntity.getPhone(), triggerAction);
            }
            // 添加标签
            MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(data.getEa(), data.getMarketingEventId());
            if (memberMarketingEventCrmConfigEntity != null) {
                objectTagManager.batchAddTagsToUserMarketings(data.getEa(), campaignMergeDataEntity.getPhone(),
                    ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, memberMarketingEventCrmConfigEntity.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(), null,
                    data.getMarketingEventId(), triggerAction);
            }
        }
        return true;
    }

    public MemberConfigEntity getMemberCrmConfig(String ea, String marketingEventId) {
        MemberConfigEntity memberConfig = tryInitMemberConfig(ea);
        if (memberConfig == null) {
            return null;
        }
        // 查询市场活动下特殊配置(不为空使用市场活动下配置)
        MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, marketingEventId);
        if (memberMarketingEventCrmConfigEntity != null) {
            memberConfig.setMemberToLeadFieldMappings(memberMarketingEventCrmConfigEntity.getMemberToLeadFieldMappings());
            memberConfig.setLeadPoolId(memberMarketingEventCrmConfigEntity.getLeadPoolId());
            memberConfig.setLeadRecordType(memberMarketingEventCrmConfigEntity.getCrmRecordType());
        }
        return memberConfig;
    }

    public boolean marketingEventHasMemberToLeadMapping(String ea, String marketingEventId) {
        MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, marketingEventId);
        return memberMarketingEventCrmConfigEntity != null && CollectionUtils.isNotEmpty(memberMarketingEventCrmConfigEntity.getMemberToLeadFieldMappings());
    }

    public Result<BuildCrmObjectByEnrollDataResult> buildCrmObjectByMemberData(String ea, String memberAccessibleCampaignId) {
        BuildCrmObjectByEnrollDataResult result = new BuildCrmObjectByEnrollDataResult();
        MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(memberAccessibleCampaignId);
        if (memberAccessibleCampaignEntity == null) {
            log.warn("MemberManager.buildCrmObjectByMemberData error memberAccessibleCampaignEntity is null memberAccessibleCampaignId:{}", memberAccessibleCampaignId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        MemberConfigEntity memberConfigEntity = getMemberCrmConfig(memberAccessibleCampaignEntity.getEa(), memberAccessibleCampaignEntity.getMarketingEventId());
        if (memberConfigEntity == null || memberConfigEntity.getMemberToLeadFieldMappings() == null) {
            log.warn("MemberManager.buildCrmObjectByMemberData error memberConfigEntity is null");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        ObjectData memberObject = crmV2Manager.getDetail(ea, -10000,  CrmObjectApiNameEnum.MEMBER.getName(), memberAccessibleCampaignEntity.getMemberId());
        if (memberObject == null) {
            log.warn("MemberManager.buildCrmObjectByMemberData memberObject is null memberId:{}", memberAccessibleCampaignEntity.getMemberId());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        ObjectData leadObjectDataToSave = crmV2Manager.convertObjectDataByFieldMapping(ea, CrmObjectApiNameEnum.MEMBER.getName(), memberAccessibleCampaignEntity.getMemberId(), CrmObjectApiNameEnum.CRM_LEAD.getName(), memberConfigEntity.getMemberToLeadFieldMappings());
        result.setResult(leadObjectDataToSave);
        return Result.newSuccess(result);
    }

    public Result bindMemberAndCrmObj(String ea, String memberAccessibleCampaignId, String bindObjectId, String objectApiName) {
        MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(memberAccessibleCampaignId);
        if (memberAccessibleCampaignEntity == null) {
            log.warn("MemberManager.bindMemberAndCrmObj error memberAccessibleCampaignEntity is null memberAccessibleCampaignId:{}", memberAccessibleCampaignId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        // 查询绑定的数据是否存在/作废
        try {
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, objectApiName, bindObjectId);
            if (MapUtils.isEmpty(objectData)) {
                log.warn("MemberManager.bindMemberAndCrmObj objectData is null:{}");
                return Result.newError(SHErrorCode.CUSTOMIZE_FORM_BIND_CRM_DATA_ERROR);
            }
        } catch (Exception e) {
            log.warn("MemberManager.bindMemberAndCrmObj error e:{}", e);
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_BIND_CRM_DATA_ERROR);
        }
        Integer bindObjectType = CampaignMergeDataObjectTypeEnum.getTypeByName(objectApiName);
        if(bindObjectType == null) {
            log.warn("MemberManager.bindMemberAndCrmObj error objectType error objectApiName:{}", objectApiName);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        // 绑定数据
        memberAccessibleCampaignDAO.updateMemberAccessibleCampaign(SaveCrmStatusEnum.LINKED.getValue(), null, bindObjectType, bindObjectId, memberAccessibleCampaignEntity.getId());
        ObjectData memberObject = crmV2Manager.getDetail(ea, -10000,  CrmObjectApiNameEnum.MEMBER.getName(), memberAccessibleCampaignEntity.getMemberId());
        if (memberObject == null) {
            log.warn("MemberManager.bindMemberAndCrmObj memberObject is null memberId:{}", memberAccessibleCampaignEntity.getMemberId());
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String triggerAction = "bindCrmObjectByCampaignId";
        AddCampaignMergeDataByMemberArgContainer addCampaignMergeDataByMemberContainer = new AddCampaignMergeDataByMemberArgContainer();
        addCampaignMergeDataByMemberContainer.setNeedCreateDataIfNoBindCRM(false);
        addCampaignMergeDataByMemberContainer.setBindObjectType(bindObjectType);
        addCampaignMergeDataByMemberContainer.setBindObjectId(bindObjectId);
        addCampaignMergeDataByMemberContainer.setMemberAccessibleCampaignId(memberAccessibleCampaignEntity.getId());
        addCampaignMergeDataByMemberContainer.setSaveCrmErrorMessage(null);
        addCampaignMergeDataByMemberContainer.setSaveCrmStatus(SaveCrmStatusEnum.LINKED.getValue());
        String campaignMergeDataId = campaignMergeDataManager.addCampaignMergeDataByMember(addCampaignMergeDataByMemberContainer);
        ThreadPoolUtils.execute(() -> {
            Boolean needBindData = false;
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataId);
            AssociationArg associationArg = new AssociationArg();
            associationArg.setPhone(campaignMergeDataEntity.getPhone());
            if (StringUtils.isNotBlank(memberAccessibleCampaignEntity.getUid())) {
                needBindData = true;
                associationArg.setAssociationId(memberAccessibleCampaignEntity.getUid());
                associationArg.setEa(ea);
                associationArg.setType(ChannelEnum.MINIAPP.getType());
                associationArg.setPhone(campaignMergeDataEntity.getPhone());
            } else if (StringUtils.isNotBlank(memberAccessibleCampaignEntity.getWxAppId()) && StringUtils.isNotBlank(memberAccessibleCampaignEntity.getOpenId())) {
                needBindData = true;
                associationArg.setType(ChannelEnum.WX_SERVICE.getType());
                associationArg.setEa(ea);
                associationArg.setWxAppId(memberAccessibleCampaignEntity.getWxAppId());
                associationArg.setAssociationId(memberAccessibleCampaignEntity.getOpenId());
                associationArg.setPhone(campaignMergeDataEntity.getPhone());
            } else if (StringUtils.isNotBlank(memberAccessibleCampaignEntity.getFingerPrint())) {
                needBindData = true;
                associationArg.setAssociationId(memberAccessibleCampaignEntity.getFingerPrint());
                associationArg.setEa(ea);
                associationArg.setType(ChannelEnum.BROWSER_USER.getType());
                associationArg.setPhone(campaignMergeDataEntity.getPhone());
            }
            if (needBindData) {
                CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByApiName(objectApiName);
                ChannelEnum crmObjectType = campaignMergeDataObjectTypeEnum != null ? campaignMergeDataObjectTypeEnum.getUserMarketingChannel() : null;
                if (crmObjectType == null) {
                    return;
                }
                userMarketingAccountRelationManager.bindLoginIdentityAndCrmObject(ea, associationArg, crmObjectType, bindObjectId, campaignMergeDataEntity.getPhone(), triggerAction);

                // 添加标签
                MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, memberAccessibleCampaignEntity.getMarketingEventId());
                if (memberMarketingEventCrmConfigEntity != null) {
                    objectTagManager.batchAddTagsToUserMarketings(ea, campaignMergeDataEntity.getPhone(),
                        crmObjectType.getType(), bindObjectId, null, memberMarketingEventCrmConfigEntity.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(), null,
                        memberAccessibleCampaignEntity.getMarketingEventId(), triggerAction);
                }

            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }


    public Optional<String> saveLeadToMember(String ea, String leadId, String marketingPromotionSourceId){
        MemberConfigEntity memberConfig = tryInitMemberConfig(ea);
        if (memberConfig == null || memberConfig.getLeadToMemberFieldMappings() == null) {
            return Optional.empty();
        }
        ObjectData memberObjectDataToSave = crmV2Manager.convertObjectDataByFieldMapping(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), leadId, CrmObjectApiNameEnum.MEMBER.getName(), memberConfig.getLeadToMemberFieldMappings());
        if (memberObjectDataToSave == null || Strings.isNullOrEmpty(memberObjectDataToSave.getString(CrmMemberFieldEnum.PHONE.getApiName()))){
            return Optional.empty();
        }
        String phone = memberObjectDataToSave.getString(CrmMemberFieldEnum.PHONE.getApiName());
        Optional<ObjectData> memberOptional = getMemberByEaAndPhone(ea, phone);
        if (memberOptional.isPresent()){
            return Optional.of(memberOptional.get().getId());
        }
        memberObjectDataToSave.setOwner(-10000);
        if (!Strings.isNullOrEmpty(memberConfig.getMemberRecordType())){
            memberObjectDataToSave.put("record_type", memberConfig.getMemberRecordType());
        }
        ObjectDescribe memberObjDescribe = memberDescribeManager.getMemberObjDescribe(ea);
        if(memberObjDescribe!=null && memberObjDescribe.getFields().containsKey("approval_status")){
            //如果开通注册审核,则状态为待审核
            MemberConfigEntity config = memberConfigDao.getByEa(ea);
            if(config.isRegisterReview()){
                memberObjectDataToSave.put("approval_status",MemberApprovalStatusEnum.REVIEW_WAITING.getType());
            }else {
                memberObjectDataToSave.put("approval_status",MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
            }
        }
        if (StringUtils.isNotBlank(marketingPromotionSourceId)) {
            memberObjectDataToSave.put(CrmMemberFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getApiName(), marketingPromotionSourceId);
        }
        ActionAddArg actionAddArg = new ActionAddArg();
        actionAddArg.setObjectData(memberObjectDataToSave);
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService.add(new HeaderObj(eiEaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.MEMBER.getName(), false, actionAddArg);
        if (!result.isSuccess() || result.getData() == null || result.getData().getObjectData() == null){
            return Optional.empty();
        }
        // 异步关联会员帐号
        ThreadPoolUtils.execute(() -> {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setEa(ea);
            associationArg.setType(ChannelEnum.CRM_MEMBER.getType());
            associationArg.setAssociationId(result.getData().getObjectData().getId());
            associationArg.setPhone(result.getData().getObjectData().getString(CrmMemberFieldEnum.PHONE.getApiName()));
            associationArg.setUserName(result.getData().getObjectData().getName());
            associationArg.setEmail(result.getData().getObjectData().getString(CrmMemberFieldEnum.EMAIL.getApiName()));
            associationArg.setTriggerAction("saveLeadToMember");
            associationArg.setTriggerSource(ChannelEnum.CRM_MEMBER.getDescription());
            userMarketingAccountAssociationManager.associate(associationArg);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Optional.ofNullable(result.getData().getObjectData().getId());
    }

    /**
     * 处理会员对象 添加推广渠道,推广员工,推广会员
     * @param ea
     * @param memberId 推广会员id
     * @return
     */
    public Optional<String> saveLeadToMemberAndSpreadData(String ea, CustomizeFormDataUserEntity customizeFormDataUserEntity, String memberId){
        String leadId = customizeFormDataUserEntity.getLeadId();
        String spreadChannel = customizeFormDataUserEntity.getChannelValue();
        MemberConfigEntity memberConfig = tryInitMemberConfig(ea);
        if (memberConfig == null || memberConfig.getLeadToMemberFieldMappings() == null) {
            return Optional.empty();
        }
        ObjectData memberObjectDataToSave = crmV2Manager.convertObjectDataByFieldMapping(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), leadId, CrmObjectApiNameEnum.MEMBER.getName(), memberConfig.getLeadToMemberFieldMappings());
        if (memberObjectDataToSave == null || Strings.isNullOrEmpty(memberObjectDataToSave.getString(CrmMemberFieldEnum.PHONE.getApiName()))){
            return Optional.empty();
        }
        String phone = memberObjectDataToSave.getString(CrmMemberFieldEnum.PHONE.getApiName());
        Optional<ObjectData> memberOptional = getMemberByEaAndPhone(ea, phone);
        if (memberOptional.isPresent()){
            return Optional.of(memberOptional.get().getId());
        }
        memberObjectDataToSave.setOwner(-10000);
        if (!Strings.isNullOrEmpty(memberConfig.getMemberRecordType())){
            memberObjectDataToSave.put("record_type", memberConfig.getMemberRecordType());
        }
        if (!Strings.isNullOrEmpty(spreadChannel)){
            getSpreadChannelValue(ea,spreadChannel,memberObjectDataToSave);
        }
        if (!Strings.isNullOrEmpty(memberId)){
            memberObjectDataToSave.put("spread_member", memberId);
        }
        ObjectDescribe memberObjDescribe = memberDescribeManager.getMemberObjDescribe(ea);
        if(memberObjDescribe!=null && memberObjDescribe.getFields().containsKey("approval_status")){
            //如果开通注册审核,则状态为待审核
            MemberConfigEntity config = memberConfigDao.getByEa(ea);
            if(config.isRegisterReview()){
                memberObjectDataToSave.put("approval_status",MemberApprovalStatusEnum.REVIEW_WAITING.getType());
            }else {
                memberObjectDataToSave.put("approval_status",MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
            }
        }
        handleMemberObjFiled(ea,customizeFormDataUserEntity,memberObjectDataToSave);
        ActionAddArg actionAddArg = new ActionAddArg();
        actionAddArg.setObjectData(memberObjectDataToSave);
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService.add(new HeaderObj(eiEaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.MEMBER.getName(), false, actionAddArg);
        if (!result.isSuccess() || result.getData() == null || result.getData().getObjectData() == null){
            return Optional.empty();
        }
        // 异步关联会员帐号
        ThreadPoolUtils.execute(() -> {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setEa(ea);
            associationArg.setType(ChannelEnum.CRM_MEMBER.getType());
            associationArg.setAssociationId(result.getData().getObjectData().getId());
            associationArg.setPhone(result.getData().getObjectData().getString(CrmMemberFieldEnum.PHONE.getApiName()));
            associationArg.setUserName(result.getData().getObjectData().getName());
            associationArg.setEmail(result.getData().getObjectData().getString(CrmMemberFieldEnum.EMAIL.getApiName()));
            associationArg.setTriggerAction("saveLeadToMember");
            associationArg.setTriggerSource(ChannelEnum.CRM_MEMBER.getDescription());
            userMarketingAccountAssociationManager.associate(associationArg);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Optional.ofNullable(result.getData().getObjectData().getId());
    }

    public MemberConfigEntity tryInitMemberConfig(String ea){
        for (int i = 0; i < 6; i++) {
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            if (memberConfig != null && memberConfig.getInitStatus() == 1){
                asyncPresetMemberShareRule(memberConfig);
                memberDescribeManager.tryUpdateCustomFieldLabel(ea);
                marketingPromotionSourceObjManager.addMemberField(ea);
                userBehaviorRecordObjManager.tryAddSpreadMemberId(ea);
                if (memberConfig.getContentCenterSiteId() == null){
                    //老企业补刷个人会员中心的页面
                    HexagonCopyArg contentCenterHexagonCopyArg = new HexagonCopyArg();
                    contentCenterHexagonCopyArg.setId(defaultMemberContentCenterSiteId);
                    contentCenterHexagonCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1221));
                    Result<CreateSiteResult> contentCenterSiteResult = hexagonService.hexagonCopySite(ea, -10000, contentCenterHexagonCopyArg);
                    if (contentCenterSiteResult.isSuccess() && contentCenterSiteResult.getData() != null){
                        memberConfigDao.updateSiteId(ea, memberConfig.getRegistrationSiteId(), memberConfig.getLoginSiteId(), contentCenterSiteResult.getData().getId(), memberConfig.getUpdateInfoSiteId());
                        memberConfig.setContentCenterSiteId(contentCenterSiteResult.getData().getId());
                        memberAccessibleObjectDao.insertIgnore(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), contentCenterSiteResult.getData().getId());
                        hexagonSiteDAO.markAsSystemSite(memberConfig.getRegistrationSiteId());
                        hexagonSiteDAO.markAsSystemSite(memberConfig.getLoginSiteId());
                        hexagonSiteDAO.markAsSystemSite(contentCenterSiteResult.getData().getId());
                    }
                }
                if (StringUtils.isBlank(memberConfig.getUpdateInfoSiteId())) {
                    HexagonCopyArg updateInfoSiteArg = new HexagonCopyArg();
                    updateInfoSiteArg.setId(defaultMemberUpdateInfoSiteId);
                    updateInfoSiteArg.setName(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1235));
                    Result<CreateSiteResult> updateInfoSiteResult = hexagonService.hexagonCopySite(ea, -10000, updateInfoSiteArg);
                    if (updateInfoSiteResult.isSuccess() && updateInfoSiteResult.getData() != null) {
                        memberConfigDao
                            .updateSiteId(ea, memberConfig.getRegistrationSiteId(), memberConfig.getLoginSiteId(), memberConfig.getContentCenterSiteId(), updateInfoSiteResult.getData().getId());
                        hexagonSiteDAO.markAsSystemSite(updateInfoSiteResult.getData().getId());
                        memberConfig.setUpdateInfoSiteId(updateInfoSiteResult.getData().getId());
                        memberAccessibleObjectDao.insertIgnore(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), updateInfoSiteResult.getData().getId());
                    }
                }
                if (memberConfig.getForgotPasswordSiteId() == null) {
                    //老企业补刷忘记密码的页面
                    HexagonCopyArg contentCenterHexagonCopyArg = new HexagonCopyArg();
                    contentCenterHexagonCopyArg.setId(defaultMemberForgotPasswordSiteId);
                    contentCenterHexagonCopyArg.setName("会员忘记密码页面");
                    Result<CreateSiteResult> contentCenterSiteResult = hexagonService.hexagonCopySite(ea, -10000, contentCenterHexagonCopyArg);
                    if (contentCenterSiteResult.isSuccess() && contentCenterSiteResult.getData() != null) {
                        String forgotPasswordSiteId = contentCenterSiteResult.getData().getId();
                        memberConfigDao.updateForgotPasswordSiteId(ea, forgotPasswordSiteId);
                        memberConfig.setContentCenterSiteId(forgotPasswordSiteId);
                        hexagonSiteDAO.markAsSystemSite(forgotPasswordSiteId);
                    }
                }
                return memberConfig;
            }
            if (memberConfig == null){
                boolean updated = memberConfigDao.insertIgnore(ea) > 0;
                if (updated){
                    memberDescribeManager.tryUpdateCustomFieldLabel(ea);
                    marketingPromotionSourceObjManager.addMemberField(ea);
                    userBehaviorRecordObjManager.tryAddSpreadMemberId(ea);
                    HexagonCopyArg registerHexagonCopyArg = new HexagonCopyArg();
                    registerHexagonCopyArg.setId(defaultMemberRegisterSiteId);
                    registerHexagonCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1255));
                    Result<CreateSiteResult> registerSiteCopyResult = hexagonService.hexagonCopySite(ea, -10000, registerHexagonCopyArg);
                    if (!registerSiteCopyResult.isSuccess()){
                        throw new IllegalStateException(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1258));
                    }
                    HexagonCopyArg loginHexagonCopyArg = new HexagonCopyArg();
                    loginHexagonCopyArg.setId(defaultMemberLoginSiteId);
                    loginHexagonCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1262));
                    Result<CreateSiteResult> loginSiteCopyResult = hexagonService.hexagonCopySite(ea, -10000, loginHexagonCopyArg);
                    if (!loginSiteCopyResult.isSuccess()){
                        throw new IllegalStateException(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1265));
                    }
                    HexagonCopyArg contentCenterHexagonCopyArg = new HexagonCopyArg();
                    contentCenterHexagonCopyArg.setId(defaultMemberContentCenterSiteId);
                    contentCenterHexagonCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1221));
                    Result<CreateSiteResult> contentCenterSiteResult = hexagonService.hexagonCopySite(ea, -10000, contentCenterHexagonCopyArg);
                    if (!contentCenterSiteResult.isSuccess()){
                        throw new IllegalStateException(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1272));
                    }
                    HexagonCopyArg updateInfoSiteArg = new HexagonCopyArg();
                    updateInfoSiteArg.setId(defaultMemberUpdateInfoSiteId);
                    updateInfoSiteArg.setName(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1235));
                    Result<CreateSiteResult> updateInfoSiteResult = hexagonService.hexagonCopySite(ea, -10000, updateInfoSiteArg);
                    if(!updateInfoSiteResult.isSuccess()) {
                        throw new IllegalStateException(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1279));
                    }
                    memberAccessibleObjectDao.insertIgnore(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), contentCenterSiteResult.getData().getId());
                    memberAccessibleObjectDao.insertIgnore(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), updateInfoSiteResult.getData().getId());
                    String registrationSiteId = registerSiteCopyResult.getData().getId();
                    String loginSiteId = loginSiteCopyResult.getData().getId();
                    String contentCenterSiteId = contentCenterSiteResult.getData().getId();
                    String updateInfoSiteId = updateInfoSiteResult.getData().getId();
                    replaceContent(registrationSiteId, "fc333ac343564db88343a7c00d2d23ec", loginSiteId);
                    replaceContent(loginSiteId, "66711ebba0fe4317836c6da2410fb478", registrationSiteId);
                    memberConfigDao.updateSiteId(ea, registrationSiteId, loginSiteId, contentCenterSiteId, updateInfoSiteId);

                    HexagonCopyArg forgotPasswordHexagonCopyArg = new HexagonCopyArg();
                    forgotPasswordHexagonCopyArg.setId(defaultMemberForgotPasswordSiteId);
                    forgotPasswordHexagonCopyArg.setName("会员忘记密码页面");
                    Result<CreateSiteResult> forgotPasswordHexagonResult = hexagonService.hexagonCopySite(ea, -10000, forgotPasswordHexagonCopyArg);
                    if (forgotPasswordHexagonResult.isSuccess() && forgotPasswordHexagonResult.getData() != null) {
                        String forgotPasswordSiteId = forgotPasswordHexagonResult.getData().getId();
                        memberConfigDao.updateForgotPasswordSiteId(ea, forgotPasswordSiteId);
                        hexagonSiteDAO.markAsSystemSite(forgotPasswordSiteId);
                    }

                    MemberConfigEntity dbMemberConfig = memberConfigDao.getByEa(ea);
                    asyncPresetMemberShareRule(dbMemberConfig);
                    hexagonSiteDAO.markAsSystemSite(registrationSiteId);
                    hexagonSiteDAO.markAsSystemSite(loginSiteId);
                    hexagonSiteDAO.markAsSystemSite(contentCenterSiteId);
                    hexagonSiteDAO.markAsSystemSite(updateInfoSiteId);
                    return dbMemberConfig;
                }
            }
//            ThreadUtils.sleepSilently(300, TimeUnit.MILLISECONDS);
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                log.warn("exception:",  e);
            }
        }
        throw new IllegalStateException(I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1306) + ea);
    }
    
    private void asyncPresetMemberShareRule(MemberConfigEntity memberConfig){
        if (memberConfig == null || memberConfig.getCrmMemberShareRulePresetStatus() == null || memberConfig.getCrmMemberShareRulePresetStatus() != 0) {
            return;
        }
        if (memberConfig.getCrmMemberShareRulePresetStatus() == 0){
            ThreadPoolUtils.execute(() -> {
                if (!isOpenMember(memberConfig.getEa())){
                    return;
                }
                boolean tryUpdateSuccess = memberConfigDao.updateCrmMemberShareRulePresetStatus(memberConfig.getEa(), 0, 1) > 0;
                if (!tryUpdateSuccess){
                    return;
                }
                com.fxiaoke.paasauthrestapi.common.data.HeaderObj systemHeader = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(eiEaConverter.enterpriseAccountToId(memberConfig.getEa()));
                systemHeader.put("x-fs-userInfo", -10000);
                BatchCreateEntityShareArg arg = new BatchCreateEntityShareArg();
                PaasAuthContextData contextData = new PaasAuthContextData();
                contextData.setAppId("CRM");
                contextData.setTenantId(eiEaConverter.enterpriseAccountToId(memberConfig.getEa()) + "");
                contextData.setUserId("-10000");
                arg.setContext(contextData);
                ShareRuleData shareRule = new ShareRuleData();
                shareRule.setEntityId(CrmObjectApiNameEnum.MEMBER.getName());
                shareRule.setEntityShareType(0);
                shareRule.setPermission(2);
                shareRule.setShareType(2);
                shareRule.setShareId("999999");
                shareRule.setReceiveType(4);
                shareRule.setReceiveId("00000000000000000000000000000028");
                shareRule.setStatus(1);
                arg.setShareList(Arrays.asList(shareRule));
                com.fxiaoke.paasauthrestapi.common.result.Result<Void> createResult = paasShareRuleService.batchCreateEntityShare(systemHeader, arg);
                if (createResult.getErrCode() == 0){
                    userRoleManager.initMemberObjPrivilege(memberConfig.getEa());
                    memberConfigDao.updateCrmMemberShareRulePresetStatus(memberConfig.getEa(), 1, 2);
                }
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
    }

    private void replaceContent(String sourceSiteId, String toReplaceSiteId, String newSiteId){
        List<HexagonPageEntity> hexagonPages = hexagonPageDAO.getByHexagonSiteId(sourceSiteId);
        for (HexagonPageEntity hexagonPage : hexagonPages) {
            String newContent = hexagonPage.getContent().replace(toReplaceSiteId, newSiteId);
            hexagonPageDAO.updateContent(hexagonPage.getId(), newContent);
        }
    }

    private String getMarketingEventStatusByTime(Long startTime, Long endTime){
        if (startTime == null) {
            return I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1359);
        }
        long current = System.currentTimeMillis();
        if (endTime == null) {
            if (current < startTime){
                return I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1359);
            } else {
                return I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1366);
            }
        }
        if (current < startTime){
            return I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1359);
        }else if (startTime <= current && current < endTime){
            return I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1366);
        }else {
            return I18nUtil.get(I18nKeyEnum.MARK_REMOTE_MEMBERMANAGER_1374);
        }
    }

    public Optional<List<QueryMemberContentResult>> queryMemberContentList(String ea, List<MarketingEventData> marketingEventDataList, String memberId, String memberPhone){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(marketingEventDataList)){
            return Optional.empty();
        }

        List<QueryMemberContentResult> queryMemberContentResultList = Lists.newArrayList();
        Map<String, String> marketingEventIdCoverPathMap = new HashMap<>();
        for (MarketingEventData marketingEventData : marketingEventDataList){
            QueryMemberContentResult contentResult = new QueryMemberContentResult();
            contentResult.setMarketingEventId(marketingEventData.getId());
            contentResult.setTitle(marketingEventData.getName());
            contentResult.setMarketingStatus(getMarketingEventStatusByTime(marketingEventData.getBeginTime(), marketingEventData.getEndTime()));
            contentResult.setStartTime(marketingEventData.getBeginTime());
            contentResult.setEndTime(marketingEventData.getEndTime());
            contentResult.setLocation(marketingEventData.getLocation());
            contentResult.setEventType(marketingEventData.getEventType());
            queryMemberContentResultList.add(contentResult);
            if (marketingEventData.getCover() != null) {
                marketingEventIdCoverPathMap.put(marketingEventData.getId(), marketingEventData.getCover());
            }
        }

        configMemberConferenceData(ea, queryMemberContentResultList);
        configMemberLiveData(ea, queryMemberContentResultList);
        configMemberContentData(ea, queryMemberContentResultList, memberId, memberPhone);

        String defaultUrl = fileV2Manager.getUrlByPath(photoManager.getDefaultCoverApath(), ea, false);
        for (QueryMemberContentResult memberContentResult : queryMemberContentResultList ) {
            if (memberContentResult.getUrl() == null && marketingEventIdCoverPathMap.get(memberContentResult.getMarketingEventId()) != null){
                String coverUrl = fileV2Manager.getUrlByPath(marketingEventIdCoverPathMap.get(memberContentResult.getMarketingEventId()), ea, false);
                memberContentResult.setUrl(coverUrl);
                memberContentResult.setThumbnailUrl(coverUrl);
            }
            memberContentResult.setUrl(memberContentResult.getUrl() == null ? defaultUrl : memberContentResult.getUrl());
            memberContentResult.setThumbnailUrl(memberContentResult.getThumbnailUrl() == null ? defaultUrl : memberContentResult.getThumbnailUrl());
        }
        return Optional.of(queryMemberContentResultList);
    }

    public void configMemberContentData(String ea, List<QueryMemberContentResult> queryMemberContentResultList, String memberId, String memberPhone){
        if (CollectionUtils.isEmpty(queryMemberContentResultList)){
            return;
        }

        //报名数据
        List<String> marketingEventIds = queryMemberContentResultList.stream().map(QueryMemberContentResult::getMarketingEventId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingEventIds)){
            return;
        }

        Map<String, CustomizeFormDataUserObjectDTO> marketingEventIdObjMap = new HashMap<>();
        //表单报名
        List<CustomizeFormDataUserObjectDTO> customizeFormDataUserObjectDTOs = customizeFormDataUserDAO.getByMarketingEventIds(marketingEventIds, memberPhone);
        //会员报名
        List<CustomizeFormDataUserObjectDTO> memberDataUserObjectDTOs = memberAccessibleCampaignDAO.getByMarketingEventIds(marketingEventIds, memberId);
        if (CollectionUtils.isEmpty(customizeFormDataUserObjectDTOs) && CollectionUtils.isEmpty(memberDataUserObjectDTOs)){
            return;
        }


        if (CollectionUtils.isNotEmpty(customizeFormDataUserObjectDTOs)) {
            for (CustomizeFormDataUserObjectDTO dataUserObjectDTO : customizeFormDataUserObjectDTOs) {
                if (dataUserObjectDTO.getObjectType() == ObjectTypeEnum.HEXAGON_PAGE.getType()) {
                    HexagonSiteEntity hexagonSiteEntity = hexagonPageDAO.getHexagonSiteByHexagonPageId(dataUserObjectDTO.getObjectId());
                    if (hexagonSiteEntity != null) {
                        dataUserObjectDTO.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                        dataUserObjectDTO.setObjectId(hexagonSiteEntity.getId());
                    }
                }
                marketingEventIdObjMap.putIfAbsent(dataUserObjectDTO.getMarketingEventId(), dataUserObjectDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(memberDataUserObjectDTOs)){
            for (CustomizeFormDataUserObjectDTO dataUserObjectDTO : memberDataUserObjectDTOs) {
                if (dataUserObjectDTO.getObjectType() == ObjectTypeEnum.HEXAGON_PAGE.getType()) {
                    HexagonSiteEntity hexagonSiteEntity = hexagonPageDAO.getHexagonSiteByHexagonPageId(dataUserObjectDTO.getObjectId());
                    if (hexagonSiteEntity != null) {
                        dataUserObjectDTO.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                        dataUserObjectDTO.setObjectId(hexagonSiteEntity.getId());
                    }
                }
                marketingEventIdObjMap.putIfAbsent(dataUserObjectDTO.getMarketingEventId(), dataUserObjectDTO);
            }
        }

        for (QueryMemberContentResult memberContentResult : queryMemberContentResultList ) {
            if (marketingEventIdObjMap != null && marketingEventIdObjMap.get(memberContentResult.getMarketingEventId()) != null) {
                CustomizeFormDataUserObjectDTO dto = marketingEventIdObjMap.get(memberContentResult.getMarketingEventId());
                if (StringUtils.equals(MarketingEventEnum.CONTENT_MARKETING.getEventType(), memberContentResult.getEventType())){
                    memberContentResult.setSubmitMaterialId(dto.getObjectId());
                    memberContentResult.setSubmitMaterialType(dto.getObjectType());
                }
            }
        }
    }

    public void sendActivityNotificationSmsByCampaignMergeId(String campaignMergeDataId) {
        ThreadPoolUtils.execute(() -> {
            asyncSendActivityNotificationSmsByCampaignMergeId(campaignMergeDataId);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    public void asyncSendActivityNotificationSmsByCampaignMergeId(String campaignMergeDataId){
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataId);
        if (campaignMergeDataEntity == null) {
            log.warn("MemberManager.sendActivityNotificationSmsByCampaignMergeId campaignMergeDataEntity is null campaignMergeDataId:{}", campaignMergeDataId);
            return;
        }
        if (StringUtils.isBlank(campaignMergeDataEntity.getPhone())) {
            log.warn("MemberManager.sendActivityNotificationSmsByCampaignMergeId campaignMergeDataEntity phone is null campaignMergeDataId:{}", campaignMergeDataId);
            return;
        }
        String marketingEventId = campaignMergeDataEntity.getMarketingEventId();
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, campaignMergeDataEntity.getEa());
        if (activityEntity == null) {
            log.warn("MemberManager.sendActivityNotificationSmsByCampaignMergeId activityEntity is null campaignMergeDataId:{}", campaignMergeDataId);
            return;
        }
        Map<String, ExtraSmsParamObject> extraSmsParamObjectMap = Maps.newHashMap();
        conferenceManager.buildTicketParam(activityEntity.getId(), activityEntity.getEa(), campaignMergeDataEntity, extraSmsParamObjectMap);
        if (activityEntity.getEnrollReview()) {
            conferenceManager
                    .sendConferenceNotification(ConferenceNotificationTypeEnum.ENROLL_AUDIT.getType(), Lists.newArrayList(campaignMergeDataEntity.getPhone()), activityEntity, Maps.newHashMap());
        } else {
            conferenceManager
                    .sendConferenceNotification(ConferenceNotificationTypeEnum.ENROLL_SUCCESS.getType(), Lists.newArrayList(campaignMergeDataEntity.getPhone()), activityEntity,
                            extraSmsParamObjectMap);
        }
    }

    public void sendReviewMessageByCampaignMergeId(String campaignMergeDataId) {
        ThreadPoolUtils.execute(() -> {
            asyncSendReviewMessageByCampaignMergeId(campaignMergeDataId);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public void asyncSendReviewMessageByCampaignMergeId(String campaignMergeDataId){
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataId);
        if (campaignMergeDataEntity == null) {
            log.warn("MemberManager.sendReviewMessageByCampaignMergeId campaignMergeDataEntity is null campaignMergeDataId:{}", campaignMergeDataId);
            return;
        }
        String marketingEventId = campaignMergeDataEntity.getMarketingEventId();
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, campaignMergeDataEntity.getEa());
        if (activityEntity == null) {
            log.warn("MemberManager.sendReviewMessageByCampaignMergeId activityEntity is null campaignMergeDataId:{}", campaignMergeDataId);
            return;
        }
        conferenceManager.sendConferenceEnrollNoticeRealTime(activityEntity.getId());
    }

    public void sendConferenceQywxReviewMessageByCampaignMergeId(String campaignMergeDataId) {
        ThreadPoolUtils.execute(() -> {
            asyncSendConferenceQywxReviewMessageByCampaignMergeId(campaignMergeDataId);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public void asyncSendConferenceQywxReviewMessageByCampaignMergeId(String campaignMergeDataId){
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataId);
        if (campaignMergeDataEntity == null) {
            log.warn("MemberManager.sendConferenceQywxReviewMessageByCampaignMergeId campaignMergeDataEntity is null campaignMergeDataId:{}", campaignMergeDataId);
            return;
        }
        String marketingEventId = campaignMergeDataEntity.getMarketingEventId();
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, campaignMergeDataEntity.getEa());
        if (activityEntity == null) {
            log.warn("MemberManager.sendConferenceQywxReviewMessageByCampaignMergeId activityEntity is null campaignMergeDataId:{}", campaignMergeDataId);
            return;
        }
        conferenceManager.sendQywxConferenceEnrollNoticeRealTime(activityEntity.getId());
    }


    public void configMemberLiveData(String ea, List<QueryMemberContentResult> queryMemberContentResultList){
        if (CollectionUtils.isEmpty(queryMemberContentResultList)){
            return;
        }
        List<String> marketingEventIds = queryMemberContentResultList.stream().filter(content -> content.getEventType().equals(MarketingEventEnum.LIVE_MARKETING.getEventType())).map(QueryMemberContentResult::getMarketingEventId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingEventIds)){
            return;
        }
        List<MarketingLiveEntity> marketingLiveList = marketingLiveDAO.queryMarketingLiveByMarketingEventIds(eieaConverter.enterpriseAccountToId(ea), marketingEventIds);
        if (CollectionUtils.isEmpty(marketingLiveList)){
            return;
        }

        Map<String, MarketingLiveEntity> liveEntityMap = marketingLiveList.stream().collect(Collectors.toMap(MarketingLiveEntity::getMarketingEventId, v -> v, (v1, v2) -> v1));
        List<String> livePaths = marketingLiveList.stream().filter(liveEntity -> liveEntity.getCover() != null).map(MarketingLiveEntity::getCover).collect(Collectors.toList());
        for (QueryMemberContentResult memberContentResult : queryMemberContentResultList) {
            MarketingLiveEntity liveEntity = liveEntityMap.get(memberContentResult.getMarketingEventId());
            if (liveEntity != null) {
                if (liveEntity.getPlatform() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()){
                    memberContentResult.setFlowStatus(liveEntity.getStatus());
                }else {
                    Long startTime = liveEntity.getStartTime().getTime();
                    Long endTime = liveEntity.getEndTime().getTime();
                    Long now = System.currentTimeMillis();
                    if (startTime > now) {
                        memberContentResult.setFlowStatus(LiveStatusEnum.NOT_START.getStatus());
                    } else if (now > startTime && now < endTime) {
                        memberContentResult.setFlowStatus(LiveStatusEnum.PROCESSING.getStatus());
                    } else {
                        memberContentResult.setFlowStatus(LiveStatusEnum.FINISH.getStatus());
                    }
                }
                memberContentResult.setUrl(liveEntity.getCover());
                memberContentResult.setThumbnailUrl(liveEntity.getCover());
                memberContentResult.setSubmitMaterialId(liveEntity.getFormHexagonId());
                memberContentResult.setSubmitMaterialType(ObjectTypeEnum.HEXAGON_SITE.getType());
                memberContentResult.setObjectType(ObjectTypeEnum.LIVE.getType());
                memberContentResult.setObjectId(liveEntity.getId());
            }
        }
        Map<String, String> pathUrlMap = null;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(livePaths)){
            pathUrlMap = fileV2Manager.batchGetUrlByPath(livePaths, ea, false);
        }
        for (QueryMemberContentResult memberContentResult : queryMemberContentResultList) {
            MarketingLiveEntity liveEntity = liveEntityMap.get(memberContentResult.getMarketingEventId());
            if (liveEntity != null && pathUrlMap != null && pathUrlMap.get(liveEntity.getCover()) != null){
                memberContentResult.setUrl(pathUrlMap.get(liveEntity.getCover()));
                memberContentResult.setThumbnailUrl(pathUrlMap.get(liveEntity.getCover()));
            }
        }
    }

    public void configMemberConferenceData(String ea, List<QueryMemberContentResult> queryMemberContentResultList){
        if (CollectionUtils.isEmpty(queryMemberContentResultList)){
            return;
        }
        List<String> marketingEventIds = queryMemberContentResultList.stream().filter(content -> content.getEventType().equals(MarketingEventEnum.MEETING_SALES.getEventType())).map(QueryMemberContentResult::getMarketingEventId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingEventIds)){
           return;
        }
        List<ActivityEntity> activityEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, marketingEventIds);
        if (CollectionUtils.isEmpty(activityEntityList)){
            return;
        }

        Map<String, ActivityEntity> activityEntityMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, v -> v, (v1, v2) -> v1));
        List<String> conferenceIds = activityEntityList.stream().map(activityEntity -> activityEntity.getId()).collect(Collectors.toList());
        List<PhotoEntity> conferencePhotos = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), conferenceIds);
        Map<String, PhotoEntity> conferencePhotoPathMap = null;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(conferencePhotos)){
            conferencePhotoPathMap = conferencePhotos.stream().collect(Collectors.toMap(PhotoEntity::getTargetId, v->v, (v1,v2)->v1));
        }

        for (QueryMemberContentResult memberContentResult : queryMemberContentResultList){
            if (memberContentResult.getEventType().equals(MarketingEventEnum.MEETING_SALES.getEventType())){
                ActivityEntity activityEntity = activityEntityMap.get(memberContentResult.getMarketingEventId());
                if (activityEntity == null){
                    continue;
                }
                memberContentResult.setFlowStatus(conferenceManager.getConferenceTimeFlowStatus(activityEntity));
                if (conferencePhotoPathMap != null && conferencePhotoPathMap.get(activityEntity.getId()) != null){
                    memberContentResult.setUrl(conferencePhotoPathMap.get(activityEntity.getId()).getUrl());
                    memberContentResult.setThumbnailUrl(conferencePhotoPathMap.get(activityEntity.getId()).getThumbnailUrl());
                }
                memberContentResult.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                memberContentResult.setObjectId(activityEntity.getId());
            }
        }
    }

    public Map<String, String> getLatestAccessibleMemberIdByCampaignIds(List<String> campaignIds) {
        Map<String, String> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return resultMap;
        }
        List<MemberAccessibleCampaignEntity> memberAccessibleCampaignEntityList = memberAccessibleCampaignDAO.getLatestAccessibleMemberByCampaignIds(campaignIds);
        if (CollectionUtils.isEmpty(memberAccessibleCampaignEntityList)) {
            return resultMap;
        }
        resultMap = memberAccessibleCampaignEntityList.stream().collect(Collectors.toMap(MemberAccessibleCampaignEntity::getCampaignId, MemberAccessibleCampaignEntity::getMemberId, (v1,v2) -> v1));
        return resultMap;
    }

    public String getSystemPromotionChannelType(MemberEnrollArg memberEnrollArg, String wxAppId, String wxOpenId, String uid) {
        if (StringUtils.isNotBlank(memberEnrollArg.getChannelValue())) {
            return memberEnrollArg.getChannelValue();
        }
        if (StringUtils.isNotBlank(memberEnrollArg.getMarketingActivityId())) {
            String marketingActivityChannel = spreadChannelManager.getChannelByMarketingActivityId(memberEnrollArg.getMarketingActivityId(), SystemPromotionChannelEnum.WECHAT);
            if (StringUtils.isNotBlank(marketingActivityChannel)) {
                return marketingActivityChannel;
            }
        }
        if (StringUtils.isNotBlank(uid) || (StringUtils.isNotBlank(wxOpenId) && StringUtils
            .isNotBlank(wxAppId))) {
            return SystemPromotionChannelEnum.WECHAT.getValue();
        }
        return null;
    }

    public boolean memberNeedMemberEnroll(String ea, String marketingEventId, String memberId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(memberId)) {
            return false;
        }
        MemberAccessibleCampaignEntity memberAccessibleCampaignData = memberAccessibleCampaignDAO
            .getMemberAccessibleCampaignData(ea, marketingEventId, memberId);
        return memberAccessibleCampaignData == null;
    }

    public Map<String, Object> memberDataToMap(MemberCheckResult memberResultData,CustomizeFormDataEntity customizeFormDataEntity) {
        Map<String,Object> resultMap = Maps.newHashMap();
        Map<String,Object> memberMap = Maps.newHashMap();
        if (memberResultData == null) {
            return memberMap;
        }
        try {
            Field[] declaredFields = memberResultData.getClass().getDeclaredFields();
            for (Field declaredField : declaredFields) {
                //设置是否可以访问，如果不设置将报错
                declaredField.setAccessible(true);
                memberMap.put(declaredField.getName(), declaredField.get(memberResultData));
            }
            List<String> memberFields = Lists.newArrayList("name","phone","email","position","company");
            String ea = customizeFormDataEntity.getEa();
            MemberConfigEntity memberConfigEntity = memberConfigDao.getByEa(ea);
            //线索转会员的映射
            FieldMappings leadToMemberFieldMappings = memberConfigEntity.getLeadToMemberFieldMappings();
            //表单存入线索的映射
            FieldMappings formToLeadFieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
            //通过两个映射,找到会员 name, phone,email,company,position 对应表单的映射字段
            for (FieldMappings.FieldMapping leadToMemberFieldMapping : leadToMemberFieldMappings) {
                if (memberFields.contains(leadToMemberFieldMapping.getCrmFieldName())) {
                    String crmFieldName = leadToMemberFieldMapping.getCrmFieldName();
                    String mankeepFieldName = leadToMemberFieldMapping.getMankeepFieldName();
                    for (FieldMappings.FieldMapping formToLeadFieldMapping : formToLeadFieldMappings) {
                        if (Objects.equals(mankeepFieldName,formToLeadFieldMapping.getCrmFieldName())) {
                            resultMap.put(formToLeadFieldMapping.getMankeepFieldName(),memberMap.get(crmFieldName));
                        }
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.warn("memberDataToMap error",e);
        }
        return resultMap;
    }

    public void deleteSystemMemberHexagonSiteData(String ea) {
        memberConfigDao.deleteByEa(ea);
        //删除微页面站点,微页面,以及微页面挂载的表单
        HexagonSiteEntity centerSite = hexagonSiteDAO.getById("914f60694e1f4c19831abd9cd8e1ef0b");
        HexagonSiteEntity loginSite = hexagonSiteDAO.getById("8eb008c399c24ec7bd2646f29e090780");
        HexagonSiteEntity registerSite = hexagonSiteDAO.getById("6761e8fad02942de9173c0d98f9a0b58");

        //微页面
        List<HexagonPageEntity> centerHexagonPage = hexagonPageDAO.getByHexagonSiteId(centerSite.getId());
        List<HexagonPageEntity> loginHexagonPage = hexagonPageDAO.getByHexagonSiteId(loginSite.getId());
        List<HexagonPageEntity> registerHexagonPage = hexagonPageDAO.getByHexagonSiteId(registerSite.getId());


        //删除表单
        if (centerHexagonPage.get(0) != null && StringUtils.isNotBlank(centerHexagonPage.get(0).getFormId())) {
            customizeFormDataDAO.deleteCustomizeFormData(centerHexagonPage.get(0).getFormId());
        }
        if (loginHexagonPage.get(0) != null && StringUtils.isNotBlank(loginHexagonPage.get(0).getFormId())) {
            customizeFormDataDAO.deleteCustomizeFormData(loginHexagonPage.get(0).getFormId());
        }
        if (registerHexagonPage.get(0) != null && StringUtils.isNotBlank(registerHexagonPage.get(0).getFormId())) {
            customizeFormDataDAO.deleteCustomizeFormData(registerHexagonPage.get(0).getFormId());
        }

        //删除微页面
        if (centerHexagonPage.get(0) != null) {
            hexagonPageDAO.deleteHexagonPageById(centerHexagonPage.get(0).getId());
        }
        if (loginHexagonPage.get(0) != null) {
            hexagonPageDAO.deleteHexagonPageById(loginHexagonPage.get(0).getId());
        }
        if (registerHexagonPage.get(0) != null) {
            hexagonPageDAO.deleteHexagonPageById(registerHexagonPage.get(0).getId());
        }

        //删除站点
        if (centerSite != null) {
            hexagonSiteDAO.deleteHexagonSiteById(centerSite.getId());
        }
        if (loginSite != null) {
            hexagonSiteDAO.deleteHexagonSiteById(loginSite.getId());
        }
        if (registerSite != null) {
            hexagonSiteDAO.deleteHexagonSiteById(registerSite.getId());
        }

    }

    @Data
    public static class SaveMemberToCampaignMergeDataResultContainer implements Serializable {

        private String leadId;

        private String campaignMergeDataId;

    }

    @Data
    public static class SaveMemberToLeadForCampaignContainer implements Serializable {

        private String leadId;

        private String crmObjectType;

        private boolean saveCrmLeadError;

        private String saveCrmErrorMessage;

        private LeadDuplicateSearchResult leadDuplicateSearchResultData;

    }

    /**
     * 同步会员设置映射信息到活动上
     * @param ea
     * @param marketingEventId
     */
    public void asyncMemberConfigToMarketingEvent(String ea, String marketingEventId){
        ThreadPoolUtils.execute(() -> {
            try {
                memberConfigToMarketingEvent(ea, marketingEventId);
            } catch (Exception e) {
                log.warn("MemberManager.asyncMemberConfigToMarketingEvent error", e);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public void memberConfigToMarketingEvent(String ea, String marketingEventId){
            // 检查活动有无设置会员一键报名
            MemberMarketingEventCrmConfigEntity config = memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, marketingEventId);
            if (config != null && config.getMemberToLeadFieldMappings() != null) {
                // 已经设置过了，无需处理
                return;
            }

            // 查询会员设置
            MemberConfigEntity memberConfigEntity = memberConfigDao.getByEa(ea);
            if (memberConfigEntity == null || memberConfigEntity.getMemberToLeadFieldMappings() == null) {
                // 没有会员设置，无需处理
                return;
            }

            // 添加
            String id = UUIDUtil.getUUID();
            MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = new MemberMarketingEventCrmConfigEntity();
            memberMarketingEventCrmConfigEntity.setId(id);
            memberMarketingEventCrmConfigEntity.setEa(ea);
            memberMarketingEventCrmConfigEntity.setMarketingEventId(marketingEventId);
            memberMarketingEventCrmConfigEntity.setMemberToLeadFieldMappings(memberConfigEntity.getMemberToLeadFieldMappings());
            memberMarketingEventCrmConfigEntity.setLeadPoolId(memberConfigEntity.getLeadPoolId());
            memberMarketingEventCrmConfigEntity.setCrmRecordType(memberConfigEntity.getLeadRecordType());
            memberMarketingEventCrmConfigDAO.upsertMemberMarketingEventCrmConfig(memberMarketingEventCrmConfigEntity);
    }

    /**
     * 会员对象详情
     * @param id
     * @param ea
     * @return
     */
    public ObjectData getDetail(String id, String ea){
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), id);
        return objectData;
    }

    public Result<String> checkWxServiceUserIsMember(String ea,  String wxAppId, String wxOpenId) {
        Optional<String> optionalMemberId = getWxServiceUserBindMemberId(ea, wxAppId, wxOpenId);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            return Result.newSuccess(optionalMemberId.get());
        }

        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    public Result<String> checkH5UserIsMember(String ea, Map<String, String> allMemberCookieInfos) {
        Optional<String> optionalMemberId = getH5LoginMemberId(ea, allMemberCookieInfos);
        boolean memberIdExisted = optionalMemberId.isPresent();
        if (!memberIdExisted){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        String memberId = optionalMemberId.get();
        return Result.newSuccess(memberId);
    }

    public Result<String> checkWxMiniAppUserHaveMemberAuth(Integer objectType, String objectId, String uid, boolean checkObjectAccess) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        Optional<String> optionalMemberId = getWxMiniAppUserBindMemberId(ea, uid);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(Strings.isNullOrEmpty(approvalStatus) || Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_SUCCESS.getType()){
                    return Result.newSuccess(optionalMemberId.get());
                }
            }
        }
        if(checkObjectAccess && isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    //如果非会员，且物料不是会员相关物料，也允许访问
    public boolean isObjectNotNeedMemberAuth(Integer objectType, String objectId, String ea) {
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == objectType) {
            HexagonPageEntity hexagonPage = hexagonPageDAO.getInclueDeletedById(objectId);
            objectId = hexagonPage.getHexagonSiteId();
            objectType = ObjectTypeEnum.HEXAGON_SITE.getType();
        }
        return memberAccessibleObjectDao.countByEaAndObjectId(ea, objectId) <= 0;
    }

    public Optional<Integer> getMemberApproveStatus(String ea, String siteId, String wxAppId, String openId, String uid, Map<String, String> allMemberCookieInfos, IdentityCheckTypeEnum identityCheckType) {
        MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
        if(memberConfig == null){
            return Optional.empty();
        }
        List<String> accessibleIdList = memberAccessibleObjectDao.listAccessibleHexagonSitesByEa(ea);
        if (!siteId.equals(memberConfig.getRegistrationSiteId()) && !siteId.equals(memberConfig.getLoginSiteId()) && !accessibleIdList.contains(siteId)) {
            return Optional.empty();
        }
        Result<String> checkIsMemberResult = null;
        if(identityCheckType != null){
            if (identityCheckType.getType() == IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.type){
                checkIsMemberResult = checkWxServiceUserIsMember(ea, wxAppId, openId);
            }else if (identityCheckType.getType() == IdentityCheckTypeEnum.BROWSER_FINGERPRINT.type){
                checkIsMemberResult = checkH5UserIsMember(ea, allMemberCookieInfos);
            }
        }else{
            if(!Strings.isNullOrEmpty(uid)){
                checkIsMemberResult = checkWxMiniAppUserHaveMemberAuth(ObjectTypeEnum.HEXAGON_SITE.getType(), siteId, uid, true);
            }
        }

        //判断用户是不是会员，是会员返回审核状态
        if (checkIsMemberResult != null && checkIsMemberResult.isSuccess() && checkIsMemberResult.getData() != null) {
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), checkIsMemberResult.getData());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(Strings.isNullOrEmpty(approvalStatus)){
                    return Optional.of(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
                }else {
                    return Optional.of(Integer.parseInt(approvalStatus));
                }
            }
        }

        return Optional.empty();
    }


    public void checkEnrollReviewStatus(String ea, String marketingEventId, String memberId, MemberEnrollResult memberEnrollResult) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(marketingEventId) || StringUtils.isEmpty(memberId)) {
            return;
        }
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity == null || activityEntity.getEnrollReview() == null || !activityEntity.getEnrollReview()) {
            return;
        }
        memberEnrollResult.setEnrollPendingReviewTip(StringUtils.isNotBlank(activityEntity.getEnrollPendingReviewTip()) ? activityEntity.getEnrollPendingReviewTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_572));
        memberEnrollResult.setEnrollReviewFailureTip(StringUtils.isNotBlank(activityEntity.getEnrollReviewFailureTip()) ? activityEntity.getEnrollReviewFailureTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_573));
        //开启了报名审核，需要判断是否通过审核
        memberEnrollResult.setEnrollReview(true);
        MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(ea, marketingEventId, memberId);
        if (memberAccessibleCampaignEntity != null && StringUtils.isNotBlank(memberAccessibleCampaignEntity.getCampaignId())) {
            //已报名,获取审核状态
            String campaignId = memberAccessibleCampaignEntity.getCampaignId();
            ActivityEnrollDataEntity activityEnrollData = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(campaignId);
            if (activityEnrollData != null && activityEnrollData.getReviewStatus() != null) {
                memberEnrollResult.setEnrollStatus(activityEnrollData.getReviewStatus());
            }
            return;
        }
        ObjectData memberData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        String phone = memberData.getString(CrmMemberFieldEnum.PHONE.getApiName());
        if (StringUtils.isBlank(phone)) {
            log.warn("MemberManager.checkEnrollReviewStatus member phone is null, memberId:{}, marketingEventId:{}", memberId, marketingEventId);
            return;
        }
        //未进行会员报名,则查询报名列表
        List<CampaignMergeDataEntity> campaignMergeDataByPhone = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, marketingEventId, phone, false);
        if (CollectionUtils.isNotEmpty(campaignMergeDataByPhone)) {
            //按照创建时间倒序排序,并取第一条记录
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataByPhone.stream().max(Comparator.comparing(CampaignMergeDataEntity::getCreateTime)).get();
            String campaignId = campaignMergeDataEntity.getId();
            ActivityEnrollDataEntity activityEnrollData = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(campaignId);
            if (activityEnrollData != null && activityEnrollData.getReviewStatus() != null) {
                memberEnrollResult.setEnrollStatus(activityEnrollData.getReviewStatus());
            }
        }
    }

    public void checkCustomizeFormDataEnrollReview(String ea, String marketingEventId,List<CustomizeFormDataUserEntity> customizeFormDataUserEntities,List<CampaignMergeDataEntity> campaignMergeDataEntities,MemberEnrollResult result) {
        if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(ea)) {
            return;
        }
        //检查是否需要审核
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity != null && activityEntity.getEnrollReview() != null) {
            result.setEnrollReview(activityEntity.getEnrollReview());
            if (activityEntity.getEnrollReview()) {
                result.setEnrollPendingReviewTip(StringUtils.isNotBlank(activityEntity.getEnrollPendingReviewTip()) ? activityEntity.getEnrollPendingReviewTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_572));
                result.setEnrollReviewFailureTip(StringUtils.isNotBlank(activityEntity.getEnrollReviewFailureTip()) ? activityEntity.getEnrollReviewFailureTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_573));
                if (CollectionUtils.isNotEmpty(campaignMergeDataEntities)) {
                    CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntities.stream().max(Comparator.comparing(CampaignMergeDataEntity::getCreateTime)).get();
                    ActivityEnrollDataEntity activityEnrollData = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(campaignMergeDataEntity.getId());
                    if (activityEnrollData != null && activityEnrollData.getReviewStatus() != null) {
                        result.setEnrollStatus(activityEnrollData.getReviewStatus());
                    }
                    return;
                }
                if (CollectionUtils.isNotEmpty(customizeFormDataUserEntities)) {
                    //按照创建时间倒序排序,并取第一条记录
                    CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntities.stream().max(Comparator.comparing(CustomizeFormDataUserEntity::getCreateTime)).get();
                    String campaignMergeDataId = customizeFormDataUserEntity.getCampaignId();
                    if (StringUtils.isEmpty(campaignMergeDataId)) {
                        return;
                    }
                    ActivityEnrollDataEntity activityEnrollData = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(campaignMergeDataId);
                    if (activityEnrollData != null && activityEnrollData.getReviewStatus() != null) {
                        result.setEnrollStatus(activityEnrollData.getReviewStatus());
                    }
                }
            }
        }
    }


    public void checkActivityEnrollReview(String ea, String marketingEventId, MemberEnrollResult memberEnrollResult) {
        if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(ea)) {
            return;
        }
        //检查是否需要审核
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity != null && activityEntity.getEnrollReview() != null) {
            memberEnrollResult.setEnrollReview(activityEntity.getEnrollReview());
            if (activityEntity.getEnrollReview()) {
                memberEnrollResult.setEnrollPendingReviewTip(StringUtils.isNotBlank(activityEntity.getEnrollPendingReviewTip()) ? activityEntity.getEnrollPendingReviewTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_572));
                memberEnrollResult.setEnrollReviewFailureTip(StringUtils.isNotBlank(activityEntity.getEnrollReviewFailureTip()) ? activityEntity.getEnrollReviewFailureTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_573));
            }
        }
    }

    public Result<Boolean> doWxMiniAppUserMemberRegister(String ea, String uid, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg arg){
        if (com.google.common.base.Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) || com.google.common.base.Strings.isNullOrEmpty(uid)){
            return Result.newError(SHErrorCode.PARAMS_ERROR, true);
        }

        Optional<ObjectData> optionalMember = getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (optionalMember.isPresent()){
            return Result.newError(SHErrorCode.PHONE_HAVE_BEEN_REGISTERED, true);
        }
        if (org.apache.commons.lang.StringUtils.isBlank(arg.getEa())) {
            arg.setEa(ea);
        }
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
        Result<String> saveMemberResult = saveMemberByFormData(ea, arg, "other", null, marketingPromotionSourceId);
        if (!saveMemberResult.isSuccess()){
            return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg(), true);
        }
        ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
            metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
        }

        String boundMemberId = wxMiniAppUserMemberBindDao.getMemberIdByUid(ea, uid);
        if (!com.google.common.base.Strings.isNullOrEmpty(boundMemberId)){
            wxMiniAppUserMemberBindDao.deleteByMiniAppUser(ea, uid);
        }
        String memberId = saveMemberResult.getData();
        wxMiniAppUserMemberBindDao.insertIgnore(ea, uid, memberId);
        // 会员存入线索对象
        Optional<String> leadOpt = saveMemberToLead(ea, memberId, null, null,
                null, arg.getChannelValue(), marketingPromotionSourceId, arg.getFormId(),null, arg);
        //绑定身份uid&会员&线索
        if (org.apache.commons.lang.StringUtils.isNotEmpty(uid)) {
            userMarketingAccountRelationManager.bindMiniappUserAndMember(ea, uid, memberId, arg.getSubmitContent().getPhone(), "doWxMiniAppUserMemberRegister");
            if (leadOpt.isPresent()) {
                userMarketingAccountRelationManager.bindMiniappUserAndLead(ea, uid, leadOpt.get(), arg.getSubmitContent().getPhone(), "doWxMiniAppUserMemberRegister");
            }
        }

        // 将游客身份与员工身份绑定
        boolean bindStaff = staffMiniappUserBindManager.checkAndAddStaffUserBindSync(uid, ea, arg.getSubmitContent().getPhone());
        if (bindStaff) {
            // 需要重新登录
            return Result.newError(SHErrorCode.STAFF_MEMBER_LOGIN_REFRESH_TOKEN, true);
        }
        return Result.newSuccess(true);
    }

    public Result<Boolean> doWxMiniAppUserMemberLogin(String ea, String uid, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg arg){
        if (com.google.common.base.Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) || com.google.common.base.Strings.isNullOrEmpty(uid)){
            return Result.newError(SHErrorCode.PARAMS_ERROR, true);
        }
        Optional<ObjectData> optionalMember = getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (!optionalMember.isPresent()){
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            //开启一键注册登陆处理
            if(memberConfig != null && memberConfig.isDirectLoginAndRegistration()){
                Result<String> saveMemberResult = saveMemberByLoginFormData(ea, arg, "other", null);
                if (!saveMemberResult.isSuccess()){
                    return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg(), true);
                }
                ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
                if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
                    metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
                }
                // 将游客身份与员工身份绑定
                boolean bindStaff = staffMiniappUserBindManager.checkAndAddStaffUserBindSync(uid, ea, arg.getSubmitContent().getPhone());
                if(bindStaff){
                    return Result.newSuccess(true);
                }
                return Result.newSuccess(false);
            }
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED, true);
        }
        String boundMemberId = wxMiniAppUserMemberBindDao.getMemberIdByUid(ea, uid);
        if (!com.google.common.base.Strings.isNullOrEmpty(boundMemberId)){
            wxMiniAppUserMemberBindDao.deleteByMiniAppUser(ea, uid);
        }
        wxMiniAppUserMemberBindDao.insertIgnore(ea, uid, optionalMember.get().getId());
        // 将游客身份与员工身份绑定
        boolean bindStaff = staffMiniappUserBindManager.checkAndAddStaffUserBindSync(uid, ea, arg.getSubmitContent().getPhone());
        if (bindStaff) {
            // 需要重新登录
            return Result.newError(SHErrorCode.STAFF_MEMBER_LOGIN_REFRESH_TOKEN, true);
        }
        return Result.newSuccess(true);
    }

    public boolean verifyMemberPassword(String ea, String memberId, String encryptedPassword) {
        if (memberDescribeManager.getOrAddMemberObjPasswordFiled(ea)) {
            // 兼容db已有数据
            if (memberConfigDao.verifyMemberPassword(ea, memberId, encryptedPassword) == 1) {
                this.saveMemberPassword(ea, memberId, encryptedPassword);
                return true;
            }
            ObjectData memberObj = crmV2Manager.getDetailIgnoreError(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
            if (memberObj != null) {
                return encryptedPassword.equals(memberObj.getString("password"));
            }
        }
        return false;
    }

    public void saveMemberPassword(String ea, String memberId, String encryptedPassword) {
        if (memberDescribeManager.getOrAddMemberObjPasswordFiled(ea)) {
            HashMap<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("_id", memberId);
            dataMap.put("password", encryptedPassword);
            crmV2Manager.updateObjs(ea, CrmObjectApiNameEnum.MEMBER.getName(), dataMap);
        }
    }

}