package com.facishare.marketing.provider.dao.cta;

import com.facishare.marketing.provider.dto.cta.CtaRelationCountDTO;
import com.facishare.marketing.provider.dto.cta.CtaRelationEntityDTO;
import com.facishare.marketing.provider.entity.cta.CtaRelationEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface CtaRelationDAO {
    @Insert("<script>"
            + "INSERT INTO cta_relation( "
            + " \"id\", "
            + " \"ea\", "
            + " \"cta_id\", "
            + " \"object_type\", "
            + " \"object_id\", "
            + " \"create_by\", "
            + " \"update_by\", "
            + " \"create_time\", "
            + " \"update_time\""
            + " )"
            + "VALUES"
            + " <foreach collection='ctaRelationEntityList' item='item' separator=','>"
            + "   ( #{item.id}, #{item.ea}, #{item.ctaId}, #{item.objectType}, #{item.objectId}, -10000, -10000, now(), now() )"
            + "  </foreach>"
            + "ON CONFLICT DO NOTHING;"
            + "</script>")
    int batchAddCtaRelation(@Param("ctaRelationEntityList") List<CtaRelationEntity> ctaRelationEntityList);

    @Insert("INSERT INTO cta_relation(id, ea, cta_id, object_type, object_id, create_by, create_time, update_by, update_time)\n"
        + "        VALUES ( #{id}, #{ea}, #{ctaId}, #{objectType}, #{objectId}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})")
    boolean addCtaRelation(CtaRelationEntity entity);

    @Update("update cta_relation set cta_id = #{ctaId}, object_type = #{objectType}, "
        + "object_id = #{objectId}, update_by=#{updateBy}, update_time=#{updateTime} where ea = #{ea} and id = #{id}")
    boolean updateCtaRelation(CtaRelationEntity entity);

    @Delete("DELETE FROM cta_relation WHERE ea = #{ea} AND id = #{id}")
    boolean deleteCtaRelation(@Param("ea") String ea, @Param("id") String id);

    @Delete("<script>"
            +     "DELETE FROM cta_relation WHERE ea = #{ea} AND id IN "
            +     "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            +     "</foreach>"
            +"</script>")
    int batchDeleteCtaRelation(@Param("ea") String ea, @Param("ids") List<String> ids);

    @Select("<script>"
            +     "SELECT * FROM cta_relation WHERE ea = #{ea} AND id IN "
            +     "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            +     "</foreach>"
            +"</script>")
    List<CtaRelationEntity> getByIds(@Param("ea") String ea, @Param("ids") List<String> ids);

    @Select("<script>"
            +     "SELECT * FROM cta_relation WHERE ea = #{ea} AND cta_id IN "
            +     "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            +     "</foreach>"
            +"</script>")
    List<CtaRelationEntity> getByCtaIds(@Param("ea") String ea, @Param("ids") List<String> ids);

    @Select("<script> " +
            "  SELECT cta_id, COUNT(*) AS cta_relation_count FROM cta_relation WHERE ea = #{ea} AND cta_id IN " +
            "  <foreach collection = 'ctaIds' item = 'ctaId' open = '(' separator = ',' close = ')'> #{ctaId} </foreach> " +
            "  GROUP BY cta_id " +
            "</script>")
    List<CtaRelationCountDTO> getRelationCountByCtaIds(@Param("ea") String ea, @Param("ctaIds") List<String> ctaIds);

    @Select("SELECT * FROM cta_relation WHERE ea = #{ea} AND object_type = #{objectType} AND object_id = #{objectId}")
    List<CtaRelationEntity> getByObjectTypeAndObjectId(@Param("ea") String ea, @Param("objectType") int objectType, @Param("objectId") String objectId);

    @Select("<script>"
            +     "SELECT * FROM cta_relation WHERE ea = #{ea} AND object_type = #{objectType} AND object_id IN "
            +     "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            +     "</foreach>"
            +"</script>")
    List<CtaRelationEntity> getByObjectTypeAndObjectIds(@Param("ea") String ea, @Param("objectType") int objectType, @Param("ids") List<String> ids);

    @Select("SELECT COUNT(id) FROM cta_relation WHERE ea = #{ea} AND cta_id = #{ctaId}")
    int queryCountByCtaId(@Param("ea") String ea, @Param("ctaId") String ctaId);

    @Select("<script>"
            + "SELECT * FROM ("
            + "SELECT concat(hexagon_site.name, &apos;/&apos;, hexagon_page.name) AS object_name, cta_relation.id,cta_relation.object_type,cta_relation.object_id FROM cta_relation JOIN hexagon_page ON cta_relation.ea = hexagon_page.ea AND cta_relation.object_id = hexagon_page.id AND cta_relation.object_type = 27 JOIN hexagon_site ON hexagon_site.ea=hexagon_page.ea AND hexagon_page.hexagon_site_id=hexagon_site.id "
            + "WHERE cta_relation.ea = #{ea} AND cta_relation.cta_id = #{ctaId} \n"
            + "UNION "
            + "SELECT concat(hexagon_template_site.name, &apos;/&apos;,hexagon_template_page.name)  AS object_name, cta_relation.id,cta_relation.object_type,cta_relation.object_id FROM cta_relation JOIN hexagon_template_page ON cta_relation.ea = hexagon_template_page.ea AND cta_relation.object_id = hexagon_template_page.id AND cta_relation.object_type = 5001 JOIN hexagon_template_site ON hexagon_template_page.ea=hexagon_template_site.ea AND hexagon_template_page.hexagon_template_site_id=hexagon_template_site.id "
            + "WHERE cta_relation.ea = #{ea} AND cta_relation.cta_id = #{ctaId} "
            + "UNION "
            + "SELECT product.name  AS object_name, cta_relation.id,cta_relation.object_type,cta_relation.object_id FROM cta_relation JOIN product ON cta_relation.ea = product.fs_ea AND cta_relation.object_id = product.id AND cta_relation.object_type = 4 "
            + "WHERE cta_relation.ea = #{ea} AND cta_relation.cta_id = #{ctaId} "
            + "UNION "
            + "SELECT article.title AS object_name, cta_relation.id,cta_relation.object_type,cta_relation.object_id FROM cta_relation JOIN article ON cta_relation.ea = article.fs_ea AND cta_relation.object_id = article.id AND cta_relation.object_type = 6 "
            + "WHERE cta_relation.ea = #{ea} AND cta_relation.cta_id = #{ctaId} "
            + "UNION "
            + "SELECT card.name AS object_name, card.id AS id , 1 AS object_type, card.uid AS object_id FROM cta_relation JOIN card ON cta_relation.object_id = card.card_template_id AND cta_relation.object_type = 5003 JOIN fs_bind ON fs_bind.uid = card.uid AND fs_bind.fs_ea = #{ea} "
            + "WHERE cta_relation.ea = #{ea} AND cta_relation.cta_id = #{ctaId} "
            + ") cta_relation"
            + "</script>")
    List<CtaRelationEntityDTO> getRelationDetailsByCtaId(@Param("ea") String ea, @Param("ctaId") String ctaId);

    @Select("<script>"
            +     "SELECT ctarl.*, cta.name AS cta_name FROM cta_relation ctarl "
            +     "INNER JOIN cta cta ON ctarl.ea = cta.ea AND ctarl.cta_id=cta.id "
            +     "WHERE ctarl.ea = #{ea} AND ctarl.object_type = #{objectType} AND ctarl.object_id IN "
            +     "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            +     "</foreach>"
            +"</script>")
    List<CtaRelationEntityDTO> getCtaRelationList(@Param("ea") String ea, @Param("objectType") int objectType, @Param("ids") List<String> ids);

}
