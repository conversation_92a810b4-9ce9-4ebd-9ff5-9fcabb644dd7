/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.cardtemplate;

import java.util.ArrayList;
import java.util.List;

public class CardMessageUtil {

    public static String cropString(String str, double length) {
        double totalLength = 0; // 记录累计长度
        StringBuilder result = new StringBuilder(); // 裁剪后的结果

        // 英文符号的正则表达式
        String englishSymbolsRegex = "^[a-zA-Z0-9!\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~]$";

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            String charStr = String.valueOf(c); // 将字符转换为字符串用于正则匹配

            // 判断字符是否是英文字母、数字或英文符号
            if (charStr.matches("^[a-zA-Z0-9]$") || charStr.matches(englishSymbolsRegex)) {
                totalLength += 0.5; // 英文字母、数字和符号算0.5个长度
            } else {
                totalLength += 1; // 其他字符算1个长度
            }

            if (totalLength > length) {
                result.append("...");
                break;
            }

            result.append(c);
        }

        return result.toString();
    }

    /**
     * 根据给定的字符串、每行长度和行数，将字符串分割成对应行数的每行字符串
     *
     * @param inputString 要处理的原始字符串
     * @param lineLength  每行允许的最大长度（按照中文占1个长度，英文等字符占0.5个长度来计算）
     * @param numLines    期望分割成的行数
     * @return 包含每一行字符串的字符串数组，如果无法按照要求分割（比如行数过多等情况），则返回合理截断后的结果
     */
    public static String[] splitStringByLines(String inputString, double lineLength, int numLines) {
        List<String> lines = new ArrayList<>();
        int startIndex = 0;
        // 英文符号的正则表达式
        String englishSymbolsRegex = "^[a-zA-Z0-9!\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~]$";
        for (int currentLine = 0; currentLine < numLines && startIndex < inputString.length(); currentLine++) {
            StringBuilder currentLineBuilder = new StringBuilder();
            double currentLength = 0;
            int i;
            for (i = startIndex; i < inputString.length(); i++) {
                char c = inputString.charAt(i);
                String charStr = String.valueOf(c); // 将字符转换为字符串用于正则匹配

                // 判断字符是否是英文字母、数字或英文符号
                if (charStr.matches("^[a-zA-Z0-9]$") || charStr.matches(englishSymbolsRegex)) {
                    currentLength += 0.5; // 英文字母、数字和符号算0.5个长度
                } else {
                    currentLength += 1; // 其他字符算1个长度
                }
                currentLineBuilder.append(c);
                if (currentLength >= lineLength) {
                    break;
                }
            }

            // 如果当前行长度达到或超过限制长度，处理最后一个字符（若超出需回退）
            if (currentLength >= lineLength) {
                if (currentLength > lineLength) {
                    // 回退一个字符，因为这个字符导致超出长度了，应放到下一行开头
                    i--;
                    currentLineBuilder.deleteCharAt(currentLineBuilder.length() - 1);
                }
            }

            // 只有当最后一行且最后一行超长时才添加省略号
            if (currentLine == numLines - 1 && currentLength >= lineLength) {
                currentLineBuilder.append("...");
            }

            lines.add(currentLineBuilder.toString());
            startIndex = i;
        }

        return lines.toArray(new String[0]);
    }


    public static void main(String[] args) {
        String inputString = "Ta暂未";
        double lineLength = 6.0;
        int numLines = 2;
        String[] result = splitStringByLines(inputString, lineLength, numLines);
        for (String line : result) {
            System.out.println(line);
        }
    }

    /**
     // 名片文案最大数量限制&裁剪方法
     export const corporateCardText = (cardType, cardData) => {
     // 定义不同卡片类型的最大长度配置
     const cardTypeConfig = {
     0: { name: 6, vocation: 9, companyName: 9 },
     1: { name: 9, vocation: 11, companyName: 11, companyAddress: 11 },
     2: { name: 6, vocation: 9, companyName: 14, companyAddress: 14 },
     3: { name: 7, vocation: 7, companyName: 10, companyAddress: 10 },
     4: { name: 7, vocation: 7, companyName: 10, companyAddress: 10 },
     5: { name: 6, vocation: 9 }
     };

     // 获取当前卡片类型的长度限制
     const maxLengths = cardTypeConfig[cardType] || {};

     // 遍历需要裁剪的字段并进行裁剪
     Object.keys(maxLengths).forEach(field => {
     if (cardData[field]) {
     cardData[field] = cropString(cardData[field], maxLengths[field]);
     }
     });

     return cardData;
     }
     每个字段在不同模版的长度裁剪
     **/

}
