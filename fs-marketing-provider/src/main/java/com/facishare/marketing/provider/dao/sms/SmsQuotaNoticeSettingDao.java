package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.SmsQuotaNoticeSettingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface SmsQuotaNoticeSettingDao {

    @Select("SELECT * FROM sms_quota_notice_setting WHERE ea = #{ea}")
    SmsQuotaNoticeSettingEntity querySmsQuotaNoticeSetting(@Param("ea") String ea);

    @Insert("INSERT INTO sms_quota_notice_setting(\n" +
            "        \"id\",\n" +
            "        \"ea\",\n" +
            "        \"quota_notice\",\n" +
            "        \"create_time\",\n" +
            "        \"update_time\",\n" +
            "        \"user_id_list\",\n" +
            "        \"status\",\n" +
            "        \"context\"\n" +
            "        )\n" +
            "        VALUES (\n" +
            "        #{obj.id},\n" +
            "        #{obj.ea},\n" +
            "        #{obj.quotaNotice},\n" +
            "        now(),\n" +
            "        now(),\n" +
            "        #{obj.userIdList},\n" +
            "        #{obj.status},\n" +
            "        #{obj.context}\n" +
            "        )")
    int insertSmsQuotaNoticeSetting(@Param("obj") SmsQuotaNoticeSettingEntity obj);

    @Update("UPDATE sms_quota_notice_setting SET quota_notice=#{quotaNotice}, user_id_list=#{userIdList}, context=#{context}, status=#{status}, update_time=now() WHERE ea=#{ea}")
    int updateSmsQuotaNoticeSetting(@Param("ea")String ea, @Param("quotaNotice") int quotaNotice, @Param("userIdList") String userIdList,@Param("context") String context,@Param("status") Integer status);

    @Update("UPDATE sms_quota_notice_setting SET status=#{status}, update_time=now() WHERE ea=#{ea}")
    int updateNoticeSettingStatus(@Param("ea")String ea, @Param("status") int status);

    @Select("SELECT sqns.* FROM sms_quota_notice_setting sqns join sms_quota sq ON sqns.ea = sq.ea WHERE sqns.status=1 and sqns.quota_notice>=sq.\"left\"")
    List<SmsQuotaNoticeSettingEntity> getEaToSyncSmsQuotaNoticeTask(@Param("ea") String ea );
}
