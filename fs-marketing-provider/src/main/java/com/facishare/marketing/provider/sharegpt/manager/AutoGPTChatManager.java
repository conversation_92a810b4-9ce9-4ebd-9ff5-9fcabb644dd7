package com.facishare.marketing.provider.sharegpt.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.vo.ai.AgentInvokeRequest;
import com.facishare.marketing.api.vo.ai.ChatCompleteVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.api.result.ai.AgentErrorMsg;
import com.facishare.marketing.api.result.ai.AgentExecuteResponse;
import com.facishare.marketing.api.result.ai.AgentResponse;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.manager.FileManager;
import com.facishare.marketing.provider.manager.ai.AiChatContentType;
import com.facishare.marketing.provider.manager.ai.PaaSAgentManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.sharegpt.agent.tool.AbstractCommand;
import com.facishare.marketing.provider.sharegpt.agent.tool.CommandManager;
import com.facishare.marketing.provider.sharegpt.chain.ConversationalWithToolsChain;
import com.facishare.marketing.provider.sharegpt.model.chat.OpenAiChatModel;
import com.facishare.marketing.provider.sharegpt.model.chat.streaming.OpenAiStreamingChatModel;
import com.facishare.marketing.provider.sharegpt.model.chat.streaming.OpenAiStreamingResponseHandler;
import com.facishare.marketing.provider.sharegpt.store.chat.obj.AIChatRecordObj;
import com.facishare.marketing.provider.sharegpt.utils.ObjectDataUtil;
import com.facishare.stone.sdk.StoneProxyApi;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static dev.langchain4j.data.message.AiMessage.aiMessage;
import static dev.langchain4j.data.message.SystemMessage.systemMessage;
import static dev.langchain4j.data.message.UserMessage.userMessage;

@Service
@Slf4j
public class AutoGPTChatManager {

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private CommandManager commandManager;
    @Autowired
    private PaaSAgentManager paasAgentManager;
    @Autowired
    private StoneProxyApi stoneProxyApi;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private MergeJedisCmd jedisCmd;
    @ReloadableProperty("ai_helper_action_card_mapping")
    private String aiHelperActionCardMapping;

    private static final String LINE_BREAK = "\n\n";

    @FilterLog
    public AutoGPTChatContext autoGPTChat(String ea, Integer fsUserId, ChatCompleteVO.Property property, List<AIChatRecordObj> aiChatRecords, String prompt) {
        AutoGPTChatContext chatContext = new AutoGPTChatContext();
        if(StringUtils.isNotBlank(property.getReferenceText())) {
            prompt = String.format("```%s```\n--\n%s", property.getReferenceText(), prompt);
        }
        try {
            if (property.isPaasAgent()) {
                chatContext.setPaasAgent(property.getDefaultHelperName());
            } else {
                chatContext = this.buildChatContext(ea, property, prompt);
            }
            if (StringUtils.isNotEmpty(chatContext.getPaasAgent())) {
                paasAgentInvoke(ea, fsUserId, property, aiChatRecords, prompt, chatContext);
                return chatContext;
            }
            agentInvoke(ea, fsUserId, property, aiChatRecords, chatContext);
        } catch (Exception e) {
            log.warn("AutoGPTChatManager autoGPTChat error", e);
            chatContext.setContent(JSONObject.toJSONString(new AgentErrorMsg(SHErrorCode.SYSTEM_ERROR.getErrorCode(), e.getMessage())));
            chatContext.setError(true);
        }
        return chatContext;
    }

    private void paasAgentInvoke(String ea, Integer fsUserId, ChatCompleteVO.Property property, List<AIChatRecordObj> aiChatRecords, String prompt, AutoGPTChatContext chatContext) {
        log.warn("AutoGPTChatManager paasAgentInvoke start: ea={}, fsUserId={}, property={}, aiChatRecords={}, prompt={}", ea, fsUserId, property, aiChatRecords, prompt);
        AgentInvokeRequest req = new AgentInvokeRequest();
        req.setApiName(chatContext.getPaasAgent());
        if (StringUtils.isNotBlank(property.getButtonApiName())) {
            req.setButtonApiName(property.getButtonApiName());
        } else {
            req.setInstructions(prompt);
        }
        if (CollectionUtils.isNotEmpty(property.getVariables())) {
            req.setVariables(property.getVariables());
        }
        if (StringUtils.isNotBlank(property.getSessionId())) {
            req.setSessionId(property.getSessionId());
        }
        List<ChatCompleteVO.Attachments> contentList = property.getContentList();
        if (CollectionUtils.isNotEmpty(contentList) && StringUtils.isNotBlank(prompt)) {
            contentList.add(ChatCompleteVO.Attachments.ofTextAttachments(prompt));
        }
        req.setContent((List) contentList);
        req.setDebug(property.isDebug());
        if (CollectionUtils.isNotEmpty(aiChatRecords)) {
            for (AIChatRecordObj chatMemory : aiChatRecords) {
                String content = chatMemory.getContent();
                if (chatMemory.getSenderId() == -100) {
                    req.addAssistantMsg(content);
                } else {
                    req.addUserMsg(content);
                }
            }
        }
        AgentResponse<? extends AgentResponse.AgentInvokeResult> response = null;
        if (property.isStreaming()) {
            response = paasAgentManager.treamingAgentExecute(ea, fsUserId, property.getBusinessName(), property.getChannel(), req);
        } else {
            if (property.isNewApi()) {
                response = paasAgentManager.agentExecute(ea, fsUserId, property.getBusinessName(), req);
            } else {
                response = paasAgentManager.agentInvoke(ea, fsUserId, property.getBusinessName(), req);
            }
        }
        if (response != null && 0 == response.getErrCode() && response.getResult() != null) {
            AgentResponse.AgentInvokeResult result = response.getResult();
            if (result instanceof AgentExecuteResponse.AgentExecuteResult) {
                AgentExecuteResponse.AgentExecuteResult executeResult = (AgentExecuteResponse.AgentExecuteResult) response.getResult();
                AgentExecuteResponse.AgentMessage agentMessage = executeResult.getAgentMessage();
                if (agentMessage != null) {
                    chatContext.setTips(agentMessage.getTips());
                    chatContext.setContent(agentMessage.getContent());
                    chatContext.setDisplayData(agentMessage.getDisplayData());
                    chatContext.setActions(agentMessage.getActions());
                }
                AgentExecuteResponse.ActionResult actionResult = executeResult.getActionResult();
                if (actionResult != null) {
                    chatContext.setActionType(actionResult.getActionType());
                    chatContext.setOriginalData(actionResult.getOriginalData());
                }
                chatContext.setInstanceId(result.getInstanceId());
                chatContext.setProcess(executeResult.getProcess());
            } else {
                chatContext.setTips(result.getTips());
                chatContext.setContent(result.getContent());
                chatContext.setContentType(result.getContentType());
                AgentResponse.AgentAction action = result.getAction();
                if (action != null) {
                    chatContext.setCardType(getCardType(property.getDefaultHelperName(), action.getTarget()));
                    action.setImagePath(transNpathToCpathUrl(ea, action.getImagePath()));
                } else {
                    chatContext.setCardType(getCardType(null));
                }
                chatContext.setAction(action);
                chatContext.setActions(result.getActions());
                chatContext.setData(result.getData());
                chatContext.setInstanceId(result.getInstanceId());
                chatContext.setProcess(response.getProcess());
            }
        } else if (response != null){
            chatContext.setContent(JSONObject.toJSONString(new AgentErrorMsg(response.getErrCode(), response.getErrMessage())));
            chatContext.setError(true);
        } else {
            chatContext.setContent(JSONObject.toJSONString(AgentErrorMsg.buildSHErrorCode(SHErrorCode.SERVER_BUSY)));
            chatContext.setError(true);
        }
    }

    private String transNpathToCpathUrl(String ea, String path) {
        if (StringUtils.isBlank(path)) {
            return null;
        }
        return fileManager.getNPathOrCPathUrl(ea, path, false);
    }

    private void agentInvoke(String ea, Integer fsUserId, ChatCompleteVO.Property property, List<AIChatRecordObj> aiChatRecords, AutoGPTChatContext chatContext) {
        log.info("AutoGPTChatManager agentInvoke start: ea={}, fsUserId={}, property={}", ea, fsUserId, JSON.toJSONString(property));
        //暂不支持文件解析
        if (CollectionUtils.isNotEmpty(property.getContentList().stream()
                .filter(e -> "file".equals(e.getType())).map(e -> e.getSource().getPath()).filter(Objects::nonNull)
                .collect(Collectors.toList()))
        ) {
            throw new RuntimeException(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_218));
        }

        String modelName = chatContext.getModelName();
        ChatMemory chatMemory = MessageWindowChatMemory.withMaxMessages(7);
        if (StringUtils.isNotBlank(chatContext.getSystemtContent())) {
            chatMemory.add(systemMessage(chatContext.getSystemtContent()));
        }
        if (CollectionUtils.isNotEmpty(aiChatRecords)) {
            for (AIChatRecordObj aiChatRecord : aiChatRecords) {
                if (aiChatRecord.getSenderId() == -100) {
                    chatMemory.add(aiMessage(aiChatRecord.getContent()));
                } else {
                    chatMemory.add(userMessage(aiChatRecord.getContent()));
                }
            }
        }
        if (property.isStreaming()) {
            OpenAiChatModel chatLanguageModel = OpenAiChatModel.builder()
                    .tenantId(eieaConverter.enterpriseAccountToId(ea) + "")
                    .user(fsUserId + "")
                    .businessName(property.getBusinessName())
                    .modelName(modelName)
                    .temperature(chatContext.getTemperature())
                    .maxTokens(chatContext.getMaxToken())
                    .build();
            chatContext.setChatLanguageModel(chatLanguageModel);
            OpenAiStreamingChatModel streamingChatModel = OpenAiStreamingChatModel.builder()
                    .baseUrl(paasAgentManager.getBaseUrl())
                    .tenantId(eieaConverter.enterpriseAccountToId(ea) + "")
                    .user(fsUserId + "")
                    .businessName(property.getBusinessName())
                    .modelName(modelName)
                    .temperature(chatContext.getTemperature())
                    .maxTokens(chatContext.getMaxToken())
                    .imageStrings(property.getContentList().stream().filter(e->"image".equals(e.getType())).map(e->e.getSource().getPath()).filter(Objects::nonNull).collect(Collectors.toList()))
                    .build();
            OpenAiStreamingResponseHandler handler = OpenAiStreamingResponseHandler.builder()
                    .ea(ea)
                    .userId(fsUserId)
                    .businessName(property.getBusinessName())
                    .channel(property.getChannel())
                    .userArgs(chatContext.getUserArgs())
                    .streamingChatModel(streamingChatModel)
                    .chatMemory(chatMemory)
                    // 暂不支持function_calls
//                    .tools(chatContext.getCommands())
                    .jedisCmd(jedisCmd)
                    .build();
            chatMemory.add(userMessage(chatContext.getUserContent()));
            streamingChatModel.generate(chatMemory.messages(), handler.getToolSpecifications(), handler);
            try {
                chatContext.setContent(handler.getFutureResponse().get(120L, TimeUnit.SECONDS));
                chatContext.setContentType(handler.getContentType().getType());
                if (AiChatContentType.IMAGE == handler.getContentType()) {
                    chatContext.setCardType("IMAGE");
                }
                if (handler.isError()) {
                    chatContext.setError(true);
                }
            } catch (Exception e) {
                log.warn("AutoGPTChatManager agentInvoke error", e);
                chatContext.setContent(JSONObject.toJSONString(AgentErrorMsg.buildSHErrorCode(SHErrorCode.SERVER_BUSY)));
                chatContext.setError(true);
            }
        } else {
            OpenAiChatModel chatLanguageModel = OpenAiChatModel.builder()
                    .tenantId(eieaConverter.enterpriseAccountToId(ea) + "")
                    .user(fsUserId + "")
                    .businessName(property.getBusinessName())
                    .modelName(modelName)
                    .imageStrings(property.getContentList().stream().filter(e->"image".equals(e.getType())).map(e->e.getSource().getPath()).filter(Objects::nonNull).collect(Collectors.toList()))
                    .temperature(chatContext.getTemperature())
                    .build();
            chatContext.setChatLanguageModel(chatLanguageModel);
            ConversationalWithToolsChain chain = ConversationalWithToolsChain.builder()
                    .ea(ea)
                    .userId(fsUserId)
                    .businessName(property.getBusinessName())
                    .userArgs(chatContext.getUserArgs())
                    .chatLanguageModel(chatLanguageModel)
                    .chatMemory(chatMemory)
                    .tools(chatContext.getCommands())
                    .build();
            chain.execute(chatContext);
        }
    }

    public String getCardType(String target) {
        return this.getCardType(null, target);
    }

    private String getCardType(String agentName, String target) {
        String cardType = null;
        try {
            Map<String, String> aiHelperActionCardMappingMap = JSONObject.parseObject(aiHelperActionCardMapping, HashMap.class);
            if (StringUtils.isNotBlank(agentName) && StringUtils.isNotBlank(target)) {
                cardType = aiHelperActionCardMappingMap.get(agentName + "$" + target);
            }
            if (cardType == null && StringUtils.isNotBlank(target)) {
                cardType = aiHelperActionCardMappingMap.get(target);
            }
        } catch (Exception e) {
            log.info("action target to card type error", e);
        }
        return cardType == null ? "1" : cardType;
    }

    /**
     * 构建所有参数
     *
     * @param ea
     * @param property
     * @param prompt
     * @return
     */
    private AutoGPTChatContext buildChatContext(String ea, ChatCompleteVO.Property property, String prompt) {
        String templateId = property.getTemplateId();
        AutoGPTChatContext chatContext = new AutoGPTChatContext();
        chatContext.setEa(ea);
        chatContext.setTemplateId(templateId);
        chatContext.setPrompt(prompt);
        chatContext.setUserArgs(genUserArgs(ea, property));
        ObjectData promptObj = null;
        if (StringUtils.isNotBlank(templateId)) {
            promptObj = crmV2Manager.queryObjectData(ea, "PromptObj", Maps.newHashMap(ImmutableMap.of("_id", templateId)));
        }
        ObjectData aihelperObj = getAihelperObj(ea, promptObj, property.getDefaultHelperName());
        chatContext.setModelName(ObjectDataUtil.getObjectDataFieldStringValue(aihelperObj, "model_name", null));
        Double temperature = ObjectDataUtil.getObjectDataFieldDoubleValue(aihelperObj, "temperature", null);
        if (temperature != null) {
            chatContext.setTemperature(temperature / 10);
        }
        chatContext.setMaxToken(ObjectDataUtil.getObjectDataFieldIntValue(aihelperObj, "max_token", null));
        chatContext.setSystemtContent(ObjectDataUtil.getObjectDataFieldStringValue(aihelperObj, "sys_prompt", null));
        chatContext.setUserContent(mixedObjectsContent(ea, prompt, property));
        chatContext.setCommands(getCurrentCommands(ea, aihelperObj));
        chatContext.setFollowUp(ObjectDataUtil.getObjectDataFieldStringValue(aihelperObj, "follow_up", null));
        chatContext.setPaasAgent(ObjectDataUtil.getObjectDataFieldStringValue(aihelperObj, "paas_agent", null));
        return chatContext;
    }

    private String mixedObjectsContent(String ea, String prompt, ChatCompleteVO.Property property) {
        // 混入自定义对象数据
        if (CollectionUtils.isEmpty(property.getObjects())) {
            return prompt;
        }
        List<Map<String, Object>> objectMapList = Lists.newArrayList();
        for (ChatCompleteVO.CustomizeObject customizeObject : property.getObjects()) {
            try {
                String objectApiName = customizeObject.getObjectApiName();
                List<CrmUserDefineFieldVo> allObjectFieldDescribesList = crmV2Manager.getAllObjectFieldDescribesList(ea, objectApiName);
                ObjectData objectData = crmV2Manager.queryObjectData(ea, objectApiName, Maps.newHashMap(ImmutableMap.of("_id", customizeObject.getObjectId())));
                if (CollectionUtils.isEmpty(allObjectFieldDescribesList) || objectData == null) {
                    continue;
                }
                Map<String, Object> objectMap = Maps.newHashMap();
                for (CrmUserDefineFieldVo crmUserDefineFieldVo : allObjectFieldDescribesList) {
                    Object value = objectData.get(crmUserDefineFieldVo.getFieldName());
                    if (2 == crmUserDefineFieldVo.getFieldProperty() && value != null) {
                        objectMap.put(crmUserDefineFieldVo.getFieldName(), value);
                    }
                }
                objectMapList.add(objectMap);
            } catch (Exception e) {
                log.warn("AutoGPTChatManager mixedObjectsContent get custom obj fail", e);
            }
        }
        if (CollectionUtils.isNotEmpty(objectMapList)) {
            prompt = prompt + LINE_BREAK + GsonUtil.toJson(objectMapList);
        }
        return prompt;
    }

    // 混入用户参数
    private Map<String, Object> genUserArgs(String ea, ChatCompleteVO.Property property) {
        JSONObject args = JSON.parseObject(JSONObject.toJSONString(property, (ValueFilter) (o, k, v) -> {
            if (k != null && v instanceof String) {
                return StringUtils.isEmpty(String.valueOf(v)) ? null : String.valueOf(v);
            } else {
                return null;
            }
        }));
        JSONObject userArgs = new JSONObject();
        userArgs.put("ea", ea);
        args.forEach((k, v) -> userArgs.put(k.replaceAll("([A-Z]+)", "_$1").toLowerCase(), v));
        return userArgs;
    }

    private List<AbstractCommand> getCurrentCommands(String ea, ObjectData aihelperObj) {
        List<AbstractCommand> currentCommands = Lists.newArrayList();
        if (aihelperObj==null) {
            return currentCommands;
        }
        try {
            List<String> toolIds = ObjectDataUtil.getObjectDataFieldListValue(aihelperObj, "tools", Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(toolIds)) {
                List<ObjectData> toolObjs = crmV2Manager.queryObjectDatas(ea, "AIToolsObj", Maps.newHashMap(ImmutableMap.of("_id", toolIds)));
                if (CollectionUtils.isNotEmpty(toolObjs) && CollectionUtils.isNotEmpty(commandManager.getCommandObjs())) {
                    List<String> toolNames = toolObjs.stream().map(ObjectData::getName).collect(Collectors.toList());
                    for (AbstractCommand commandObj : commandManager.getCommandObjs()) {
                        if (toolNames.contains(commandObj.name())) {
                            currentCommands.add(commandObj);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("AutoGPTChatManager getCurrentCommands fail", e);
        }
        return currentCommands;
    }

    private ObjectData getAihelperObj(String ea, ObjectData promptObj, String defaultHelperName) {
        ObjectData aihelperObj = null;
        try {
            if (null != promptObj) {
                String aihelperId = ObjectDataUtil.getObjectDataFieldStringValue(promptObj, "aihelper_id", null);
                if (StringUtils.isNotEmpty(aihelperId)) {
                    aihelperObj = crmV2Manager.queryObjectData(ea, "AIHelperObj", Maps.newHashMap(ImmutableMap.of("_id", aihelperId)));
                }
            }
            if (aihelperObj == null) {
                if (StringUtils.isNotBlank(defaultHelperName)) {
                    ObjectData defaultAihelperObj = crmV2Manager.queryObjectData(ea, "AIHelperObj", Maps.newHashMap(ImmutableMap.of("name", defaultHelperName)));
                    if (defaultAihelperObj != null) {
                        aihelperObj = (ObjectData) defaultAihelperObj.clone();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("AutoGPTChatManager getAihelperObj fail", e);
        }
        return aihelperObj;
    }

    public String followUpChat(AutoGPTChatContext chatContext) {
        String followUp = chatContext.getFollowUp();
        if (StringUtils.isNotBlank(followUp)) {
            OpenAiChatModel chatLanguageModel = chatContext.getChatLanguageModel();
            if (chatLanguageModel != null) {
                Response<AiMessage> result = chatLanguageModel.generate(Collections.singletonList(userMessage(followUp)));
                return result.content().text();
            }
        }
        return null;
    }

}
