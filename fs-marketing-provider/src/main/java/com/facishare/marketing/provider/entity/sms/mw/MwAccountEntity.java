package com.facishare.marketing.provider.entity.sms.mw;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.enums.sms.mw.MwAccountTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsSendIndustryTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/9/29.
 */
@Data
public class MwAccountEntity extends BaseEaEntity implements Serializable  {
    private String id;
    private String mwUser;
    private String mwPwd;
    /** @see MwAccountTypeEnum */
    private int type;
    private int status;
    private Date createTime;
    private Date updateTime;
    private String remark;
    private String masterIp;
    private String backupIp;
    /** @see SmsSendIndustryTypeEnum */
    private Integer industry;
}
