/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.BatchUpdateUserCardArg;
import com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService;
import com.facishare.mankeep.api.service.FSBindService;
import com.facishare.mankeep.api.vo.BaseVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.arg.ListEmployeeArg;
import com.facishare.marketing.api.arg.qywx.miniapp.QueryDingMiniAppDepartmentArg;
import com.facishare.marketing.api.arg.qywx.staff.QueryUserCardDetailArg;
import com.facishare.marketing.api.result.ListEmployeeResult;
import com.facishare.marketing.api.result.fsBind.*;
import com.facishare.marketing.api.result.qywx.miniapp.DingMiniAppDepartmentResult;
import com.facishare.marketing.api.result.qywx.staff.QueryUserCardDetailResult;
import com.facishare.marketing.api.service.DingMiniAppDepartmentService;
import com.facishare.marketing.api.service.DingMiniAppStaffService;
import com.facishare.marketing.api.service.FsBindService;
import com.facishare.marketing.api.service.qywx.QywxStaffService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.digitalHumans.DigitalHumansStatusEnum;
import com.facishare.marketing.common.enums.qywx.QywxContactTypeEnum;
import com.facishare.marketing.common.enums.qywx.UserCardOpenStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.digitalHumans.DigitalHumansDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxSidebarMaterialSendSettingDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.digitalHumans.DigitalHumansEntity;
import com.facishare.marketing.provider.entity.qywx.QywxContactSettingEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxSidebarMaterialSendSettingEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.digitalHumans.DigitalHumansManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.param.BatchGetUpperDepartmentDtoMap;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: dzb
 * @Date: 2018/10/24
 * @Description: 员工开通情况
 */
@Service("fsBindService")
@Slf4j
public class FsBindServiceImpl implements FsBindService {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private AuthManager authManager;
    @Autowired
    private AccountDAO accountDAO;
    @Autowired
    private FsBindManager bindManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private FSBindService fsBindService;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private OutEnterpriseDefaultCardService outEnterpriseDefaultCardService;
    @Autowired
    private CoverImageManager coverImageManager;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private QywxStaffService qywxStaffService;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FsAddressBookSettingDAO fsAddressBookSettingDAO;
    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private DingAuthService dingAuthService;
    @Autowired
    private DingMiniAppStaffService dingMiniAppStaffService;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingMiniAppDepartmentService dingMiniAppDepartmentService;
    @Autowired
    private MarketingNoticeSettingDAO marketingNoticeSettingDAO;

    @Autowired
    private QywxContactSettingDAO qywxContactSettingDAO;

    @Autowired
    private PushSessionManager pushSessionManager;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private QywxSidebarMaterialSendSettingDAO qywxSidebarMaterialSendSettingDAO;

    @Autowired
    private DepartmentProviderService departmentProviderService;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private DigitalHumansManager digitalHumansManager;

    @Autowired
    private DigitalHumansDAO digitalHumansDAO;
    //默认全部
    private static final int defaultAllDepartment = 999999;

    @Override
    public Result<PageResult<ListEmployeeResult>> listEmployee(String ea, Integer fsUserId, ListEmployeeArg vo) {
        Preconditions.checkNotNull(ea, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        List<ListEmployeeResult> listEmployeeResults = new ArrayList<>();
        List<EmployeeDto> employeeDtoList = authManager.getIsolationAllEmployee(ea, fsUserId, vo.getDepartmentIds());
        if (dingManager.isDingAddressbook(ea)){
            employeeDtoList = dingManager.getIsolationEmployeeByDingDepartmentIds(ea,fsUserId,vo.getDepartmentIds());
            List<Integer> fsBindEntityList = bindManager.queryFSBindByFsUserId(ea, Lists.newArrayList(AccountTypeEnum.DING_MINI_APP.getType()), null);
            listEmployeeResults = getDingEmployeeResults(ea,employeeDtoList,fsBindEntityList,vo.getIsOpen());
        } else {
            List<Integer> fsUserIdsList = new ArrayList<>();
            List<String> mobileList = new ArrayList<>();
            if(employeeDtoList!=null&&!employeeDtoList.isEmpty()){
                fsUserIdsList = employeeDtoList.stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
                fsUserIdsList = fsUserIdsList.stream().distinct().collect(Collectors.toList());
                mobileList = employeeDtoList.stream().map(EmployeeDto::getMobile).collect(Collectors.toList());
                String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
                List<Integer> fsBindEntityList = Lists.newArrayList();
                //绑定企业的员工
                fsBindEntityList = bindManager.queryFSUserIdsBindByEmployeeIdsWithoutType(ea, wxAppId, fsUserIdsList);

                switch (vo.getIsOpen()) {
                    case 1://已开通:1
                        fsUserIdsList.retainAll(fsBindEntityList);
                        listEmployeeResults = getEmployeeResults(ea, fsUserId, fsUserIdsList, 1, null);
                        break;
                    case 2://未开通:2
                        fsUserIdsList.removeAll(fsBindEntityList);
                        listEmployeeResults = getEmployeeResults(ea, fsUserId, fsUserIdsList, 2, null);
                        break;
                    default://全部
                        List<Integer> list = new ArrayList(fsUserIdsList);
                        Map<Integer, Object> map = getStatusUser(fsUserIdsList, fsUserIdsList, fsBindEntityList);
                        listEmployeeResults = getEmployeeResults(ea, fsUserId, list, 2, map);
                        break;
                }
            }
        }

        if(StringUtils.isNotEmpty(vo.getEmployeeName())) {
            listEmployeeResults = listEmployeeResults.stream().filter(employeeResult -> {
                return StringUtils.contains(employeeResult.getUserName(), vo.getEmployeeName());
            }).collect(Collectors.toList());
        }
        PageResult<ListEmployeeResult> pageResult = new PageResult<>();
        if (listEmployeeResults.size() > 0) {
            int fromIndex = vo.getPageSize() * (vo.getPageNum() - 1);
            int toIndex = fromIndex + vo.getPageSize();
            if (toIndex >= listEmployeeResults.size()) {
                toIndex = listEmployeeResults.size();
            }
            //内存分页
            pageResult.setResult(listEmployeeResults.subList(fromIndex, toIndex));
        } else {
            pageResult.setResult(listEmployeeResults);
        }
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(listEmployeeResults.size());

        if(digitalHumansManager.getDigitalHumansSetting(ea) && CollectionUtils.isNotEmpty(pageResult.getResult())){
            List<Integer> list = pageResult.getResult().stream().map(ListEmployeeResult::getFsUserId).collect(Collectors.toList());
            List<DigitalHumansEntity> digitalHumansEntities = digitalHumansDAO.queryByEaAndUserIdList(ea, list, DigitalHumansStatusEnum.CHECK_COMPLETED.getStatus());
            List<Integer> digHumUserIds = digitalHumansEntities.stream().map(DigitalHumansEntity::getFsUserId).collect(Collectors.toList());
            pageResult.getResult().forEach(o->o.setDigitalHumansStatus(digHumUserIds.contains(o.getFsUserId())));
        }

        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }


    private List<ListEmployeeResult> getDingEmployeeResults(String ea,List<EmployeeDto> employeeDtoList, List<Integer> fsBindEntityList,Integer isOpen) {
        List<ListEmployeeResult> listEmployeeResults = new ArrayList<>();
        if (employeeDtoList.isEmpty()) {
            return listEmployeeResults;
        }
        //获取部门
        QueryDingMiniAppDepartmentArg deptArg = new QueryDingMiniAppDepartmentArg();
        deptArg.setFsEa(ea);
        com.facishare.marketing.common.result.Result<List<DingMiniAppDepartmentResult>> miniAppDepartmentResult = dingMiniAppDepartmentService.queryDingMiniAppDepartment(deptArg);
        Map<Integer, DingMiniAppDepartmentResult> departmentResultMap ;
        if (!miniAppDepartmentResult.isSuccess() || CollectionUtils.isEmpty(miniAppDepartmentResult.getData())){
            departmentResultMap = null;
        } else {
            departmentResultMap = miniAppDepartmentResult.getData().stream().collect(Collectors.toMap(DingMiniAppDepartmentResult::getDeptId, data -> data, (v1, v2) -> v1));
        }
        employeeDtoList.forEach(employeeDto -> {
            ListEmployeeResult result = new ListEmployeeResult();
            result.setUserName(employeeDto.getName());
            result.setFsUserId(employeeDto.getEmployeeId() == 0 ? null : employeeDto.getEmployeeId());
            List<Integer> departmentList = employeeDto.getDepartmentIds();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(departmentList) && departmentResultMap != null) {
                if (departmentList.get(0) != 1){
                    result.setDepartment(departmentResultMap.get(departmentList.get(0)).getName());
                } else if (departmentList.size() > 1) {
                    result.setDepartment(departmentResultMap.get(departmentList.get(departmentList.size()-1)).getName());
                }
            }
            Integer employeeId = employeeDto.getEmployeeId();
            if ( null != employeeId && fsBindEntityList.contains(employeeDto.getEmployeeId())){
                result.setIsOpen(1);
            } else {
                result.setIsOpen(2);
            }
            if (isOpen == 0){
                listEmployeeResults.add(result);
            } else if (Objects.equals(isOpen,result.getIsOpen())){
                listEmployeeResults.add(result);
            }
        });
        return listEmployeeResults;
    }


    /**
     * @param isOpen 开通情况
     * @param fsUserIdsList 用户ID集合
     **/
    private List<ListEmployeeResult> getEmployeeResults(String ea, Integer fsUserId, List<Integer> fsUserIdsList, Integer isOpen, Map<Integer, Object> map) {
        List<ListEmployeeResult> listEmployeeResults = new ArrayList<>();
        if (fsUserIdsList.isEmpty()) {
            return listEmployeeResults;
        }
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIdsList, false);

        //部门分级展示
        List<Integer> allCircleIds = fsEmployeeMsgMap.values().stream().filter(Objects::nonNull).map(FsAddressBookManager.FSEmployeeMsg::getMainDepartmentId).filter(Objects::nonNull).collect(Collectors.toList());
        BatchGetUpperDepartmentDtoMap.Arg dtosArg = new BatchGetUpperDepartmentDtoMap.Arg();
        dtosArg.setDepartmentIds(allCircleIds);
        dtosArg.setRecursive(false);
        dtosArg.setSelf(true);
        dtosArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        BatchGetUpperDepartmentDtoMap.Result dtoMap = departmentProviderService.batchGetUpperDepartmentDtoMap(dtosArg);
        Map<Integer, String> nameMap = dtoMap.getDepartmentDtoMap().entrySet().stream().collect(Collectors.toMap(o -> o.getKey(), v -> v.getValue().getName(), (k1, k2) -> k1));
        Map<Integer, List<Integer>> upperDepartmentIdMap = dtoMap.getUpperDepartmentIdMap();
        fsEmployeeMsgMap.forEach((k, v) -> {
            Integer mainDepartmentId = v.getMainDepartmentId();
            if (mainDepartmentId != null && upperDepartmentIdMap.containsKey(mainDepartmentId)) {
                List<String> nameList = upperDepartmentIdMap.get(v.getMainDepartmentId()).stream().filter(o -> o != 999999).map(u -> nameMap.get(u)).collect(Collectors.toList());
                v.setDepartment(String.join(">", nameList));
            }
        });

        fsUserIdsList.forEach(entity -> {
            if (fsEmployeeMsgMap.get(entity) != null) {
                ListEmployeeResult result = new ListEmployeeResult();
                result.setFsUserId(entity);
                result.setUserName(fsEmployeeMsgMap.get(entity).getName());
                result.setDepartment(fsEmployeeMsgMap.get(entity).getDepartment());
                if (map == null || map.isEmpty()) {
                    result.setIsOpen(isOpen);
                } else {
                    result.setIsOpen(Integer.valueOf(map.get(entity).toString()));
                }
                listEmployeeResults.add(result);
            }
        });
        return listEmployeeResults;
    }

    public Map<Integer, Object> getStatusUser(List<Integer> fsUserIdsList1, List<Integer> fsUserIdsList2, List<Integer> fsBindEntityList) {
        Map<Integer, Object> allMap = new HashMap<>();
        if (fsBindEntityList.isEmpty()) {
            return allMap;
        }
        List<Integer> list1 = new ArrayList<>(fsUserIdsList1);
        List<Integer> list2 = new ArrayList<>(fsUserIdsList2);
        //已经开通
        list1.retainAll(fsBindEntityList);
        Map<Integer, Object> openMap = new HashMap<>();
        if (!list1.isEmpty()) {
            list1.forEach(entity -> {
                openMap.put(entity, 1);
            });
            allMap.putAll(openMap);
        }

        //未开通
        list2.removeAll(fsBindEntityList);
        Map<Integer, Object> notOpenMap = new HashMap<>();
        if (!list2.isEmpty()) {
            list2.forEach(entity -> {
                notOpenMap.put(entity, 2);
            });
            allMap.putAll(notOpenMap);
        }

        return allMap;
    }

    @Override
    public Result bindForKIS(String ea, Integer userId) {
        if (StringUtils.isBlank(ea)) {
            log.warn("FsBindServiceImpl.bindForFS ea is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == userId) {
            log.warn("FsBindServiceImpl.bindForFS userId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        if (null == ei) {
            log.warn("FsBindServiceImpl.bindForFS ei is null");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        // 纷享企业员工信息接口数据
        GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEmployeeId(userId);
        employeeDtoArg.setEnterpriseId(ei);
        GetEmployeeDtoResult employeeDtoResult = employeeService.getEmployeeDto(employeeDtoArg);
        if (null == employeeDtoResult) {
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        EmployeeDto employeeDto = employeeDtoResult.getEmployee();
        if (null == employeeDto) {
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        if (StringUtils.isBlank(employeeDto.getMobile())) {
            return new Result<>(SHErrorCode.EMPLOYEE_MOBILE_NOT_FOUND);
        }
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        AccountEntity accountEntity = qywxUserManager.getAccountByPhone(ea, employeeDto.getMobile());
        if (accountEntity == null) {
            return new Result<>(SHErrorCode.ACCOUNT_NOT_FOUND);
        }
        if (qywxMiniappConfigEntity != null) {
            // 若为企业微信则直接返回
            return new Result(SHErrorCode.SUCCESS);
        }

        BaseVO baseVO = new BaseVO();
        baseVO.setUid(accountEntity.getUid());
        baseVO.setFsEa(ea);
        baseVO.setFsUserId(userId);
        baseVO.setFsCorpId(ei);
        baseVO.setMobile(employeeDto.getMobile());
        ModelResult modelResult = fsBindService.bindForFS(baseVO);
        if (!modelResult.isSuccess()) {
            return new Result(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result reBindForFS(String ea, Integer userId) {
        if (StringUtils.isBlank(ea)) {
            log.warn("FsBindServiceImpl.reBindForFS ea is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == userId) {
            log.warn("FsBindServiceImpl.reBindForFS userId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        if (null == ei) {
            log.warn("FsBindServiceImpl.reBindForFS ei is null");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        // 纷享企业员工信息接口数据
        GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEmployeeId(userId);
        employeeDtoArg.setEnterpriseId(ei);
        GetEmployeeDtoResult employeeDtoResult = employeeService.getEmployeeDto(employeeDtoArg);
        if (null == employeeDtoResult) {
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        EmployeeDto employeeDto = employeeDtoResult.getEmployee();
        if (null == employeeDto) {
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        if (StringUtils.isBlank(employeeDto.getMobile())) {
            return new Result<>(SHErrorCode.EMPLOYEE_MOBILE_NOT_FOUND);
        }
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        AccountEntity accountEntity = qywxUserManager.getAccountByPhone(ea, employeeDto.getMobile());
        if (accountEntity == null) {
            return new Result<>(SHErrorCode.ACCOUNT_NOT_FOUND);
        }
        if (qywxMiniappConfigEntity != null) {
            // 若为企业微信则直接返回
            return new Result(SHErrorCode.SUCCESS);
        }

        BaseVO baseVO = new BaseVO();
        baseVO.setUid(accountEntity.getUid());
        baseVO.setFsEa(ea);
        baseVO.setFsUserId(userId);
        baseVO.setFsCorpId(ei);
        baseVO.setMobile(employeeDto.getMobile());
        ModelResult modelResult = fsBindService.reBindForFS(baseVO);
        if (!modelResult.isSuccess()) {
            return new Result(modelResult.getErrCode(), modelResult.getErrMsg());
        }

        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result batchUpdateUserCard(String ea, Integer userId, List<Integer> updateFsUserId, Integer updateType) {
        ThreadPoolUtils.execute(() -> {
            List<String> cardUids;
            if (CollectionUtils.isEmpty(updateFsUserId)) {
                // 查询全企业已开通名片uid
                List<Integer> fsUserId = qywxUserManager.getFsUidByEa(ea);
                List<FSBindEntity> fsBindEntityList = qywxUserManager.getFsBindByFsUserInfos(ea, fsUserId);
                cardUids = fsBindEntityList.stream().map(FSBindEntity::getUid).collect(Collectors.toList());
            } else {
                List<FSBindEntity> fsBindEntityList = qywxUserManager.getFsBindByFsUserInfos(ea, updateFsUserId);
                cardUids = fsBindEntityList.stream().map(FSBindEntity::getUid).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(cardUids)) {
                return;
            }
            // 检验是否有名片
            PageUtil<String> pageUtil = new PageUtil<>(cardUids, 500);
            List<String> filterUid = Lists.newArrayList();
            for (int i = 1; i <= pageUtil.getPageCount(); i++) {
                List<CardEntity> cardEntityList = cardDAO.listByUids(pageUtil.getPagedList(i));
                if (CollectionUtils.isNotEmpty(cardEntityList)) {
                    filterUid.addAll(cardEntityList.stream().map(CardEntity::getUid).collect(Collectors.toList()));
                }
            }
            if (CollectionUtils.isEmpty(filterUid)) {
                return;
            }
            for(String uid : filterUid) {
                try {
                    BatchUpdateUserCardArg arg = new BatchUpdateUserCardArg();
                    arg.setEa(ea);
                    arg.setUid(uid);
                    arg.setUpdateType(updateType);
                    outEnterpriseDefaultCardService.batchUpdateUserCard(arg);
                    // 更新名片
                    coverImageManager.createCardShareCoverAsync(ea,uid);
                    //coverImageManager.createCardExchangeCoverAsync(uid);
                    //coverImageManager.createLuckyMoneyIconCoverAsync(ObjectTypeEnum.CARD, uid);
                } catch (Exception e) {
                    log.warn("FsBindServiceImpl.batchUpdateUserCard error e:", e);
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<ExportBindCardDetailResult> exportBindCardDetail(String ea, Integer fsUserId, Integer type) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            exportBindCardDetailInner(ea, fsUserId, type);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    public void exportBindCardDetailInner(String ea, Integer fsUserId, Integer type) {
        // 判断是是否绑定企业微信
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        Integer pageNum = 1;
        Integer pageSize = 50000;
        List<List<Object>> cardInfoList = Lists.newArrayList();
        //ExportBindCardDetailResult exportBindCardDetailResult = new ExportBindCardDetailResult();
        boolean mustUseFxiaokeAddressBook = fsAddressBookManager.mustUseFxiaokeAddressBook(ea);
        if (agentConfig == null || mustUseFxiaokeAddressBook) {
            ListEmployeeArg listEmployeeArg = new ListEmployeeArg();
            listEmployeeArg.setIsOpen(type);
            listEmployeeArg.setPageNum(pageNum);
            listEmployeeArg.setPageSize(pageSize);
            Result<PageResult<ListEmployeeResult>> result = listEmployee(ea, fsUserId, listEmployeeArg);
            if (result != null && result.isSuccess() && result.getData() != null) {
                for (ListEmployeeResult listEmployeeResult : result.getData().getResult()) {
                    List<Object> infoList = Lists.newArrayList();
                    infoList.add(listEmployeeResult.getUserName());
                    infoList.add(listEmployeeResult.getDepartment());
                    infoList.add(listEmployeeResult.getIsOpen().equals(UserCardOpenStatusEnum.OPEN.getStatus()) ? "已开通" : "未开通");
                    cardInfoList.add(infoList);
                }
            }

        } else {
            QueryUserCardDetailArg queryUserCardDetailArg = new QueryUserCardDetailArg();
            queryUserCardDetailArg.setPageNum(pageNum);
            queryUserCardDetailArg.setPageSize(pageSize);
            queryUserCardDetailArg.setIsOpen(type);
            queryUserCardDetailArg.setFsEa(ea);
            Result<PageResult<QueryUserCardDetailResult>> result = qywxStaffService.queryUserCardDetail(queryUserCardDetailArg);
            if (result != null && result.isSuccess() && result.getData() != null) {
                for (QueryUserCardDetailResult queryUserCardDetailResult : result.getData().getResult()) {
                    List<Object> infoList = Lists.newArrayList();
                    infoList.add(queryUserCardDetailResult.getUserName());
                    infoList.add(queryUserCardDetailResult.getDepartment());
                    infoList.add(queryUserCardDetailResult.getIsOpen().equals(UserCardOpenStatusEnum.NOT_OPEN.getStatus()) ? "未开通" : "已开通");
                    cardInfoList.add(infoList);
                }
            }
        }
//            exportBindCardDetailResult.setCardInfoList(cardInfoList);
//            exportBindCardDetailResult.setTitleList(Lists.newArrayList("姓名", "部门", "开通状态"));
//            exportBindCardDetailResult.setFileName("员工名片开通情况");

        String fileName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_FSBINDSERVICEIMPL_552) + ".xlsx";
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, fileName);
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "openSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, Lists.newArrayList(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_FSBINDSERVICEIMPL_558_2)), cardInfoList);
        pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, fileName, ea, fsUserId);
    }

    @Override
    public Result<GetEmployeeCardQrCodeResult> getEmployeeCardQrCode(String ea, String qywxUserId, Integer fsUserId) {
        if (StringUtils.isNotBlank(qywxUserId)) {
            fsUserId = qywxUserManager.getFsUserIdByQyWxInfo(ea, qywxUserId, true, false);
        }
        if (fsUserId == null) {
            return Result.newError(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isBlank(uid)) {
            return Result.newError(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), uid);
        if (CollectionUtils.isEmpty(photoEntityList)) {
            return Result.newError(SHErrorCode.QRCODE_NO_FOUND);
        }
        GetEmployeeCardQrCodeResult result = new GetEmployeeCardQrCodeResult();
        result.setQrUrl(photoEntityList.get(0).getUrl());
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetAddressBookSettingResult> getAddressBookSetting(String ea) {
        GetAddressBookSettingResult result =  fsAddressBookManager.getAddressBookSetting(ea);
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetCardInfoByBindFsUserIdResult> getCardInfoByBindFsUserId(String ea, Integer fsUserId, Boolean needCheckFsIdentity) {
        List<FSBindEntity> fsBindEntityList = qywxUserManager.getFsBindByFsUserInfos(ea, Lists.newArrayList(fsUserId));
        if(CollectionUtils.isEmpty(fsBindEntityList)) {
            log.warn("FsBindServiceImpl.getCardInfoByBindFsUserId fsBindEntityList is null ea:{}, fsUserId:{}", ea, fsUserId);
            return Result.newError(SHErrorCode.USER_NOT_CREATE_CARD);
        }
        FSBindEntity fsBindEntity = fsBindEntityList.get(0);
        if (needCheckFsIdentity && QywxUserConstants.isVirtualUserId(fsBindEntity.getFsUserId())) {
            log.warn("FsBindServiceImpl.getCardInfoByBindFsUserId user is isVirtualUser fsBindEntity:{}", fsBindEntity);
            return Result.newError(SHErrorCode.USER_NOT_CREATE_CARD);
        }
        CardEntity cardEntity = cardDAO.queryCardInfoByUid(fsBindEntity.getUid());
        if (cardEntity == null) {
            log.warn("FsBindServiceImpl.getCardInfoByBindFsUserId cardEntity is null uid:{}", fsBindEntity.getUid());
            return Result.newError(SHErrorCode.BUSINESSCARD_USER_NOFOUND);
        }
        GetCardInfoByBindFsUserIdResult result = new GetCardInfoByBindFsUserIdResult();
        result.setUid(cardEntity.getUid());
        result.setName(cardEntity.getName());
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetMarketingNoticeSettingResult> getMarketingNoticeSetting(String ea) {
        GetMarketingNoticeSettingResult result = new GetMarketingNoticeSettingResult();
        MarketingNoticeSettingEntity marketingNoticeSettingEntity = marketingNoticeSettingDAO.queryByEa(ea);
        List<String> noticeTypes = Lists.newArrayList();
        if (marketingNoticeSettingEntity == null) {
            //没有就进行创建默认配置
            noticeTypes.add("qywx");
            noticeTypes.add("app");
            MarketingNoticeSettingEntity newSettingEntity = new MarketingNoticeSettingEntity();
            newSettingEntity.setId(UUIDUtil.getUUID());
            newSettingEntity.setEa(ea);
            newSettingEntity.setNoticeType(GsonUtil.getGson().toJson(noticeTypes));
            marketingNoticeSettingDAO.addMarketingNoticeSetting(newSettingEntity);
            result.setId(newSettingEntity.getId());
            result.setEa(newSettingEntity.getEa());
            result.setNoticeType(noticeTypes);
        } else {
            noticeTypes = GsonUtil.getGson().fromJson(marketingNoticeSettingEntity.getNoticeType(), new TypeToken<ArrayList<String>>(){}.getType());
            result.setId(marketingNoticeSettingEntity.getId());
            result.setEa(marketingNoticeSettingEntity.getEa());
            result.setNoticeType(noticeTypes);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateMarketingNoticeSetting(String ea, String id, List<String> noticeType) {
        MarketingNoticeSettingEntity updateEntity = new MarketingNoticeSettingEntity();
        updateEntity.setId(id);
        updateEntity.setNoticeType(GsonUtil.getGson().toJson(noticeType));
        updateEntity.setEa(ea);
        marketingNoticeSettingDAO.updateMarketingNoticeSetting(updateEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<GetFsBindInfoResult> getBindInfoByUid(String uid) {
        FSBindEntity fsBindEntity = bindManager.queryFSBindByUid(uid);
        if (fsBindEntity == null) {
            return Result.newSuccess();
        }
        GetFsBindInfoResult result = BeanUtil.copy(fsBindEntity,GetFsBindInfoResult.class);
        if (StringUtils.isNotBlank(result.getFsEa())) {
            result.setFsEi(eieaConverter.enterpriseAccountToId(result.getFsEa()));
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetQywxSidebarSendSettingResult> getQywxSidebarMaterialSendSetting(String ea) {
        if(StringUtils.isBlank(ea)){
        return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetQywxSidebarSendSettingResult result = new GetQywxSidebarSendSettingResult();
        result.setEa(ea);
        QywxSidebarMaterialSendSettingEntity entity = qywxSidebarMaterialSendSettingDAO.queryByEa(ea);
        result.setSendType(entity==null ? QywxSidebarSendTypeEnum.MINIAPP.getType() : entity.getSendType());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateQywxSidebarMaterialSendSetting(String ea, Integer sendType) {
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QywxSidebarMaterialSendSettingEntity entity = qywxSidebarMaterialSendSettingDAO.queryByEa(ea);
        if(entity==null){
            QywxSidebarMaterialSendSettingEntity insertEntity = new QywxSidebarMaterialSendSettingEntity();
            insertEntity.setId(UUIDUtil.getUUID());
            insertEntity.setEa(ea);
            insertEntity.setSendType(sendType);
            qywxSidebarMaterialSendSettingDAO.insert(insertEntity);
        }else {
            qywxSidebarMaterialSendSettingDAO.updateByEa(ea,sendType);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<GetQywxContactSettingResult> getQywxContactSetting(String ea) {
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetQywxContactSettingResult result = new GetQywxContactSettingResult();
        result.setEa(ea);
        QywxContactSettingEntity entity = qywxContactSettingDAO.queryByEa(ea);
        result.setContactType(entity==null ? QywxContactTypeEnum.QYWX.getType() : entity.getContactType());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateQywxContactSetting(String ea, Integer contactType) {
        if(StringUtils.isBlank(ea) || contactType == null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QywxContactSettingEntity entity = qywxContactSettingDAO.queryByEa(ea);
        if(entity==null){
            QywxContactSettingEntity insertEntity = new QywxContactSettingEntity();
            insertEntity.setId(UUIDUtil.getUUID());
            insertEntity.setEa(ea);
            insertEntity.setContactType(contactType);
            qywxContactSettingDAO.insert(insertEntity);
        }else {
            qywxContactSettingDAO.updateByEa(ea,contactType);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> getFsEaByAppId(String appId) {
        QywxMiniappConfigEntity entity = qywxMiniappConfigDAO.getAnyOne(appId);
        if (entity != null) {
            return Result.newSuccess(entity.getEa());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> listEaByPlatformIdAndWxAppId(String appId) {
        String ea = null;
        if (!WxAppInfoEnum.isSystemApp(appId)){
            List<String> eas = eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(MKThirdPlatformConstants.PLATFORM_ID,appId);
            if (CollectionUtils.isNotEmpty(eas)) {
                ea = eas.get(0);
            }
        }
        return Result.newSuccess(ea);
    }
}