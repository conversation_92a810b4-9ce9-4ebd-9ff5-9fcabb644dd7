package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.QywxCorpEaMappingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/3/18
 * @apiNote
 */
public interface QywxCorpEaMappingDAO {
    @Insert("insert into qywx_corp_ea_mapping(\"id\", \"corp_id\",\"ea\", \"host\", \"create_time\", \"update_time\")\n"
            + "values("
            + " #{obj.id},\n"
            + " #{obj.corpId},\n"
            + " #{obj.ea},\n"
            + " #{obj.host},\n"
            + " now(),\n"
            + " now()\n"
            + ") ON CONFLICT DO NOTHING;")
    boolean addMapping(@Param("obj") QywxCorpEaMappingEntity corpMapping);


    @Select("SELECT * FROM qywx_corp_ea_mapping WHERE ea=#{ea}")
    QywxCorpEaMappingEntity queryMappingByEa(@Param("ea") String ea);

    @Select("SELECT * FROM qywx_corp_ea_mapping")
    List<QywxCorpEaMappingEntity> queryAllMapping();

    @Update("UPDATE qywx_corp_ea_mapping SET corp_id=#{corpId}, ea=#{ea}, host=#{host}, update_time=now() WHERE id=#{id}")
    int updateMappingById(@Param("id")String id,
                          @Param("corpId") String corpId,
                          @Param("ea")String ea,
                          @Param("host") String host);

    @Update("UPDATE qywx_corp_ea_mapping SET corp_id=#{corpId}, update_time=now() WHERE id=#{id}")
    int updateCorpIdById(@Param("id")String id, @Param("corpId") String corpId, @Param("ea")String ea);

    @Select(" SELECT * FROM qywx_corp_ea_mapping WHERE corp_id = #{corpId} order by create_time desc limit 1 ")
    QywxCorpEaMappingEntity queryMappingByCorpId(@Param("corpId") String corpId, @Param("ea") String ea);

    @Select(" SELECT * FROM qywx_corp_ea_mapping WHERE corp_id = #{corpId} and ea = #{ea} order by create_time desc limit 1 ")
    QywxCorpEaMappingEntity queryMappingByCorpIdAndEa(@Param("corpId") String corpId, @Param("ea") String ea);

}
