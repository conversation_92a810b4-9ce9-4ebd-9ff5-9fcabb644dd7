package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.api.result.mail.PageQueryMailTemplateResult;
import com.facishare.marketing.provider.entity.mail.MailCommonTemplateEntity;
import com.facishare.marketing.provider.entity.mail.MailTemplateEntity;
import com.facishare.marketing.provider.entity.mail.PagerMergeTemplateEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;
import org.mvel2.util.Make;

import java.util.List;

/**
 * Created by zhengh on 2020/6/5.
 */
public interface MailTemplateDAO {
    @Insert("INSERT INTO mail_template(id, ea, fs_user_id, name, status, type, create_time, update_time)\n"
            +"VALUES(#{entity.id}, #{entity.ea}, #{entity.fsUserId}, #{entity.name},  #{entity.status}, #{entity.type}, #{entity.createTime}, #{entity.updateTime})")
    void insert(@Param("entity")MailTemplateEntity entity);

    @Select("SELECT COUNT(*) FROM mail_template WHERE ea=#{ea} and status=0")
    int getTotalTemplateCount(@Param("ea")String ea);

    @Update("UPDATE mail_template SET status=1, update_time=now() WHERE id=#{id}")
    int deleteTemplateById(@Param("id")String id,@Param("ea")String ea);

    @Update("UPDATE mail_template SET name = #{name}, update_time = now() WHERE id = #{id}")
    int updateTemplateNameById(@Param("name")String name, @Param("id")String id,@Param("ea")String ea);

    @Select("<script>"
            + "SELECT * FROM mail_template WHERE ea=#{ea} AND status = 0"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%',#{keyword},'%')</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<MailTemplateEntity> pageList(@Param("ea")String ea, @Param("keyword")String keyword, @Param("page")Page page);

    @Select("<script>"
        + "SELECT * FROM mail_common_template WHERE status = 0"
        + "<if test=\"keyword != null\">AND name LIKE CONCAT('%',#{keyword},'%')</if>"
        + "ORDER BY create_time DESC"
        + "</script>")
    List<MailCommonTemplateEntity> pageCommonTemplate(@Param("keyword")String keyword, @Param("page")Page page, @Param("ea")String ea);

    @Select("SELECT * FROM mail_template WHERE id=#{id}")
    MailTemplateEntity getDetailById(@Param("id")String id, @Param("ea")String ea);

    @Select("SELECT * FROM mail_common_template WHERE id=#{id}")
    MailCommonTemplateEntity getCommonDetailById(@Param("id")String id, @Param("ea")String ea);

    @Select("<script>"+
            "select * from"+
            "("+
            "select id,ea,name,null as email_id,null as content,create_time,update_time,type as template_type,fs_user_id from mail_template where ea=#{ea} and status = 0 " +
            "<if test=\"keyword != null\">and name like concat('%',#{keyword},'%')</if>"+
            "union all " +
            "select id,ea,name,email_id,content,create_time,update_time,2 as template_type ,creator as fs_user_id from shanshan_edit_email_template  where ea=#{ea} and status = 0" +
            "<if test=\"keyword != null\">and name like concat('%',#{keyword},'%')</if>"+
            ")" +
            " as a order by create_time desc"
            + "</script>")
    List<PagerMergeTemplateEntity> pagerMergeTemplateList(@Param("ea")String ea, @Param("keyword")String keyword, @Param("page")Page page);
}
