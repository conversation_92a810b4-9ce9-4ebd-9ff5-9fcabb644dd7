package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.dbroute.TenantService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.*;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.officialWebsite.GetWebsiteStatisticByIdArg;
import com.facishare.marketing.api.arg.qywx.miniapp.QueryDingMiniAppStaffArg;
import com.facishare.marketing.api.data.WeChatAvatarData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.EnterpriseSpreadUserMarketingListResult;
import com.facishare.marketing.api.result.fsBind.GetAddressBookSettingResult;
import com.facishare.marketing.api.result.qywx.miniapp.DingMiniAppStaffResult;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.api.service.DingMiniAppStaffService;
import com.facishare.marketing.api.service.OfficialWebsiteService;
import com.facishare.marketing.api.service.StatisticService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.kis.EnterpriseStatisticSearchTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.common.typehandlers.value.UserSpreadTaskCount;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeStatisticDAO;
import com.facishare.marketing.provider.dao.kis.SpreadTaskDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDaySourceStatisticDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDayStatisticDAO;
import com.facishare.marketing.provider.dao.param.hexagon.HexagonSiteQueryParam;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.dao.statistic.*;
import com.facishare.marketing.provider.dto.*;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.MarketingActivityObjectRelationEntity;
import com.facishare.marketing.provider.entity.MarketingActivitySpreadStatisticsVisitRangeEntity;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteDaySourceStatisticEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteDayStatisticEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.qywx.*;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.qywx.VirtualUserManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.statistic.outapi.arg.*;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.facishare.marketing.statistic.outapi.result.*;
import com.facishare.marketing.statistic.outapi.service.ObjectStatisticService;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdsByDepartmentId;
import com.facishare.organization.api.model.employee.arg.GetAllSubordinateEmployeesDtoArg;
import com.facishare.organization.api.model.employee.result.GetAllSubordinateEmployeesDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.trace.TraceContext;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.AuthManager.defaultAllDepartment;
import static java.util.stream.Collectors.groupingBy;
import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;

@Service("statisticService")
@Slf4j
public class StatisticServiceImpl implements StatisticService {
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingActivityDayStatisticDao marketingActivityDayStatisticDao;
    @Autowired
    private MarketingActivityEmployeeDayStatisticDao marketingActivityEmployeeDayStatisticDao;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private ObjectDao objectDao;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private com.facishare.marketing.provider.manager.kis.ObjectManager kisObjectManager;
    @Autowired
    private ObjectStatisticService objectStatisticService;
    @Autowired
    private MarketingObjectAmountStatisticDao marketingObjectAmountStatisticDao;
    @Autowired
    private MarketingObjectDayStatisticDao marketingObjectDayStatisticDao;
    @Autowired
    private MarketingActivityAmountStatisticDao marketingActivityAmountStatisticDao;
    @Autowired
    private MarketingActivityEmployeeAmountStatisticDao marketingActivityEmployeeAmountStatisticDao;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private MarketingActivityEmployeeStatisticDAO marketingActivityEmployeeStatisticDao;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private SpreadTaskDAO spreadTaskDAO;
    @Autowired
    private DingMiniAppStaffService dingMiniAppStaffService;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private MarketingActivitySpreadStatisticsVisitRangeDAO marketingActivitySpreadStatisticsVisitRangeDAO;
    @Autowired
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private NoticeDAO noticeDAO;

    @Autowired
    private PushSessionManager pushSessionManager;
    @Autowired
    private StatisticDAO statisticDAO;
    @Autowired
    private VirtualUserManager virtualUserManager;

    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private UserMarketingActionStatisticUpgradeRecordDao upgradeRecordDao;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Autowired
    private QywxGroupSendTaskDAO qywxGroupSendTaskDAO;
    @Autowired
    private QywxGroupSendGroupResultDAO qywxGroupSendGroupResultDAO;
    @Autowired
    private QywxGroupSendResultDAO qywxGroupSendResultDAO;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private QYWXMomentTaskDAO qywxMomentTaskDAO;
    @Autowired
    private QYWXMomentSendResultDaO qywxMomentSendResultDaO;
    @Autowired
    private OfficialWebsiteDayStatisticDAO officialWebsiteDayStatisticDAO;
    @Autowired
    private OfficialWebsiteDaySourceStatisticDAO officialWebsiteDaySourceStatisticDAO;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private OfficialWebsiteService officialWebsiteService;
    @Autowired
    private OfficialWebsiteDAO officialWebsiteDAO;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @ReloadableProperty("open.mongo.search")
    private boolean openMongoSearch;
    @Autowired
    private TenantService tenantService;

    @Override
    public Result<GetAllEmployeeSpreadStatisticDataResult> getAllEmployeeSpreadStatisticData(String ea, Integer fsUserId, GetAllEmployeeSpreadStatisticDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));

        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        GetAllEmployeeSpreadStatisticDataResult result;
        if (arg.isPartner()) {
            String upstreamEa = arg.getUpstreamEa() == null ? ea : arg.getUpstreamEa();
            List<String> partnerEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listPartnerEmployeeActivityIdsInDateRangeWithObjectIdsBySendStartTime(upstreamEa, startDate, endDate, arg.getMarketingEventIdList(), objectIds);
            if (CollectionUtils.isEmpty(partnerEmployeeSpreadMarketingActivityIds)) {
                result = getSpreadDataWithOutData(startDate, endDate);
            } else {
                result = getPartnerEmployeeSpreadData(partnerEmployeeSpreadMarketingActivityIds, startDate, endDate, arg, upstreamEa);
            }
        } else {
            Integer marketingSpreadType = arg.getMarketingActivitySpreadType();
            int associateIdType = marketingSpreadType != null && marketingSpreadType == MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getSpreadType() ? AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType() : AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType();
            List<String> allEmployeeSpreadMarketingActivityIds = null;
            if(CollectionUtils.isEmpty(arg.getMarketingEventIdList()) && objectIds == null){
                //如果不按照活动和物料筛选，只按照时间筛选，只支持一年内的推广查询
                Date currentDate = new Date();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.YEAR, -1);
                Date lastYearDate = calendar.getTime();
                allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIdsBySendStartTime(ea, lastYearDate, currentDate, null, objectIds, associateIdType);
            }else if(CollectionUtils.isNotEmpty(arg.getMarketingEventIdList()) && arg.getMarketingEventIdList().size() > 1){
                //选多个市场活动不支持选物料
                allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIdsWithouTimeV2(ea,  arg.getMarketingEventIdList(), associateIdType);
            }else {
                //活动合物料至少有一个
                String marketingEvenId = CollectionUtils.isNotEmpty(arg.getMarketingEventIdList()) ? arg.getMarketingEventIdList().get(0) : null;
                allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIdsWithouTime(ea, marketingEvenId, objectIds, associateIdType);
            }
            // 根据归属组织过滤
            if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)) {
                result = getSpreadDataWithOutData(startDate, endDate);
            } else {
                Set<String> objectIdList = null;
                if (objectIds != null) {
                    objectIdList = Sets.newHashSet(objectIds);
                }
                if ( CollectionUtils.isEmpty(objectIdList)){
                    List<MarketingActivityObjectRelationEntity> objectRelationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityIdList(ea, allEmployeeSpreadMarketingActivityIds);
                    if (CollectionUtils.isEmpty(objectRelationEntityList)) {
                        return Result.newSuccess();
                    }
                    objectIdList = objectRelationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectId)
                            .collect(Collectors.toSet());
                    List<String> qrPosterIdsList = objectRelationEntityList.stream().filter(o->o.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()).map(MarketingActivityObjectRelationEntity::getObjectId)
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(qrPosterIdsList)){
                        List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrPosterIdsList,ea);
                        List<String> qrPostTargetId = qrPosterEntities.stream().map(QRPosterEntity::getTargetId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        qrPosterIdsList.forEach(objectIdList::remove);
                        objectIdList.addAll(qrPostTargetId);
                    }
                }
                result = getAllEmployeeSpreadData(ea, allEmployeeSpreadMarketingActivityIds,Lists.newArrayList(objectIdList), startDate, endDate);
//                //dongtai pv fv
//                BatchObjectActionStatisticsArg statisticsArg = new BatchObjectActionStatisticsArg();
//                statisticsArg.setEa(ea);
//                statisticsArg.setStartTime(startDate);
//                statisticsArg.setEndTime(endDate);
//                statisticsArg.setMarketingActivityIdList(allEmployeeSpreadMarketingActivityIds);
//                statisticsArg.setObjectIdList(Lists.newArrayList(objectIdList));
//                statisticsArg.setActionTypeList(Lists.newArrayList(NewActionTypeEnum.LOOK_UP.getActionType(), NewActionTypeEnum.FORWARD.getActionType()));
//                com.facishare.marketing.statistic.common.result.Result<EnterpriseSpreadStatisticResult> enterpriseSpreadStatistic = userMarketingStatisticService.getEnterpriseSpreadStatistic(statisticsArg);
//                if(enterpriseSpreadStatistic!=null && enterpriseSpreadStatistic.getData()!=null){
//                    result.setPv(enterpriseSpreadStatistic.getData().getLookUpCount());
//                    result.setForwardCount(enterpriseSpreadStatistic.getData().getForwardCount());
//                }
                List<String> createMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIdsBySendStartTimeV2(ea, startDate, endDate, arg.getMarketingEventIdList(), objectIds, associateIdType);
                if (CollectionUtils.isNotEmpty(createMarketingActivityIds)) {
                    Integer countNumber = marketingActivityExternalConfigDao.countByMarketingActivityIdsExcuCancle(ea, createMarketingActivityIds);
                    result.setAllEmployeeSpreadActivityCount(countNumber);
                }else {
                    result.setAllEmployeeSpreadActivityCount(0);
                }
            }
        }

        return Result.newSuccess(result);
    }

    private GetAllEmployeeSpreadStatisticDataResult getPartnerEmployeeSpreadData( List<String> marketingActivityIds, Date startDate, Date endDate, GetAllEmployeeSpreadStatisticDataArg arg,String ea) {
        GetAllEmployeeSpreadStatisticDataResult res = new GetAllEmployeeSpreadStatisticDataResult();
        Integer countNumber = marketingActivityExternalConfigDao.countByMarketingActivityIdsExcuCancle(ea, marketingActivityIds);
        res.setAllEmployeeSpreadActivityCount(countNumber);
        CommonStatisticData commonStatisticData;
        commonStatisticData = marketingActivityDayStatisticDao.sumUpStatisticInDateRangeForPartner(ea, marketingActivityIds, startDate, endDate);
        if (commonStatisticData != null) {
            res.setForwardCount(commonStatisticData.getForwardCount());
            res.setPv(commonStatisticData.getLookUpCount());
            res.setEmployeeSpreadCount(commonStatisticData.getSpreadCount());
        }
        //获取线索数
        res.setFormDataEnrollCount(customizeFormDataUserDAO.countByMarketingActivityIdsAndDateRange(ea, marketingActivityIds, startDate, endDate));
        //推广员工的人数
        res.setSpreadEmployeeCount(marketingActivityEmployeeDayStatisticDao.countSpreadEmployeeWithDayRangeForPartner(ea, marketingActivityIds, startDate, endDate));
        //获取线索数趋势
        res.setFormDataEnrollTrend(convertDateCountListToDayTrend(customizeFormDataUserDAO.dateCountByMarketingActivityIdsAndDateRange(ea, marketingActivityIds, startDate, endDate), startDate, endDate));
        //访问人数
        res.setPvTrend(convertDateCountListToDayTrend(marketingActivityDayStatisticDao.dateSumUpLookUpCountInDateRangeForPartner(ea, marketingActivityIds, startDate, endDate), startDate, endDate));
        res.setSpreadPartnerCount(marketingActivityEmployeeDayStatisticDao.countSpreadPartnerWithDayRangeForPartner(ea, marketingActivityIds, startDate, endDate));

        return res;
    }

    private GetAllEmployeeSpreadStatisticDataResult getSpreadDataWithOutData(Date startDate, Date endDate) {
        GetAllEmployeeSpreadStatisticDataResult result = new GetAllEmployeeSpreadStatisticDataResult();
        result.setAllEmployeeSpreadActivityCount(0);
        result.setForwardCount(0);
        result.setPv(0);
        result.setEmployeeSpreadCount(0);
        result.setFormDataEnrollCount(0);
        //推广员工的人数
        result.setSpreadEmployeeCount(0);
        List<DateCount> noData = new ArrayList<>(31);
        Date tempData = startDate;
        while (true) {
            if (tempData.compareTo(endDate) > 0) {
                break;
            }
            tempData = DateUtil.getSomeDay(tempData, 1);
            DateCount dateCount = new DateCount();
            dateCount.setDate(tempData);
            dateCount.setCount(0);
            noData.add(dateCount);
        }
        //获取线索数趋势
        result.setFormDataEnrollTrend(convertDateCountListToDayTrend(noData, startDate, endDate));
        //访问人数
        result.setPvTrend(convertDateCountListToDayTrend(noData, startDate, endDate));
        result.setSpreadPartnerCount(0);

        return result;
    }

    private GetAllEmployeeSpreadStatisticDataResult getAllEmployeeSpreadData(String ea, List<String> marketingActivityIds,List<String> obejcetIds, Date startDate, Date endDate) {
        GetAllEmployeeSpreadStatisticDataResult result = new GetAllEmployeeSpreadStatisticDataResult();
        Integer countNumber = marketingActivityExternalConfigDao.countByMarketingActivityIdsExcuCancle(ea, marketingActivityIds);
        result.setAllEmployeeSpreadActivityCount(countNumber);
        CommonStatisticData commonStatisticData = marketingActivityDayStatisticDao.sumUpStatisticInDateRange(ea, marketingActivityIds, startDate, endDate);
        if (commonStatisticData != null) {
            result.setForwardCount(commonStatisticData.getForwardCount());
            result.setPv(commonStatisticData.getLookUpCount());
            result.setEmployeeSpreadCount(commonStatisticData.getSpreadCount());
        }
        result.setFormDataEnrollCount(customizeFormDataUserDAO.countByMarketingActivityIdsAndDateRange(ea, marketingActivityIds, startDate, endDate));
        result.setSpreadEmployeeCount(marketingActivityEmployeeDayStatisticDao.countByMarketingActivityIdsAndDayRange(ea, marketingActivityIds, startDate, endDate));

        result.setFormDataEnrollTrend(convertDateCountListToDayTrend(customizeFormDataUserDAO.dateCountByMarketingActivityIdsAndDateRange(ea, marketingActivityIds, startDate, endDate), startDate, endDate));
        result.setPvTrend(convertDateCountListToDayTrend(marketingActivityDayStatisticDao.dateSumUpLookUpCountByActivityAndObject(ea, marketingActivityIds,obejcetIds, startDate, endDate), startDate, endDate));
        return result;
    }

    /**
     * 全员推广排序，包括未做推广的员工
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    public Result<com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult>> getAllEmployeeMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg){
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<EmployeeRankingDataResult> employeeRankingDataResults = new ArrayList<>(0);
        EmployeeRankingTotalStat employeeRankingTotalStat = new EmployeeRankingTotalStat();
        com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult> pageResult = new com.facishare.marketing.common.result.PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(employeeRankingDataResults);
        pageResult.setTotalCount(0);
        pageResult.setOtherData(employeeRankingTotalStat);

        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
//        List<String> allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIds(ea, startDate, endDate, arg.getMarketingEventId(), objectIds);
        List<String> allEmployeeSpreadMarketingActivityIds = null;
        if(CollectionUtils.isEmpty(arg.getMarketingEventIdList()) && objectIds == null){
            //如果不按照活动和物料筛选，只按照时间筛选，只支持一年内的推广查询
            Date currentDate = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            calendar.add(Calendar.YEAR, -1);
            Date lastYearDate = calendar.getTime();
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIdsBySendStartTime(ea, lastYearDate, currentDate, null, objectIds, AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType());
        }else if(CollectionUtils.isNotEmpty(arg.getMarketingEventIdList()) && arg.getMarketingEventIdList().size() > 1){
            //选多个市场活动不支持选物料
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIdsWithouTimeV2(ea,  arg.getMarketingEventIdList(), AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType());
        }else {
            //活动合物料至少有一个
            String marketingEvenId = CollectionUtils.isNotEmpty(arg.getMarketingEventIdList()) ? arg.getMarketingEventIdList().get(0) : null;
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIdsWithouTime(ea, marketingEvenId, objectIds, AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType());
        }
        if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)){
            return Result.newSuccess(pageResult);
        }
        List<Integer> targetEmployeeIdList = getStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        if (targetEmployeeIdList != null && targetEmployeeIdList.size() == 0) {
            return Result.newSuccess(pageResult);
        }

        //当请分页查询数据（包括已推广和未推广）
        List<Integer> pageActivityEmployeeIds = marketingActivityEmployeeStatisticDao.queryPageByMarketingActivityIdsAndEmployeeds(ea, allEmployeeSpreadMarketingActivityIds, CollectionUtils.isNotEmpty(targetEmployeeIdList) ? targetEmployeeIdList : null, page);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(pageActivityEmployeeIds)){
            return Result.newSuccess(pageResult);
        }
        int totalCount = marketingActivityEmployeeStatisticDao.queryPageTotalCountByMarketingActivityIdsAndEmployeeds(ea, allEmployeeSpreadMarketingActivityIds, CollectionUtils.isNotEmpty(targetEmployeeIdList) ? targetEmployeeIdList : null);
        pageResult.setTotalCount(totalCount);

        List<String> objectIdList = objectIds;
        if ( CollectionUtils.isEmpty(objectIdList)){
            List<MarketingActivityObjectRelationEntity> objectRelationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityIdList(ea, allEmployeeSpreadMarketingActivityIds);
            if (CollectionUtils.isEmpty(objectRelationEntityList)) {
                return Result.newSuccess();
            }
            objectIdList = objectRelationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectId)
                    .collect(Collectors.toList());
            List<String> qrPosterIdsList = objectRelationEntityList.stream().filter(o->o.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()).map(MarketingActivityObjectRelationEntity::getObjectId)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(qrPosterIdsList)){
                List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrPosterIdsList,ea);
                List<String> qrPostTargetId = qrPosterEntities.stream().filter(o->StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
                objectIdList.removeAll(qrPosterIdsList);
                objectIdList.addAll(qrPostTargetId);
            }
        }
        //dongtai pv fv
        BatchObjectActionStatisticsArg statisticsArg = new BatchObjectActionStatisticsArg();
        statisticsArg.setEa(ea);
        statisticsArg.setStartTime(startDate);
        statisticsArg.setEndTime(endDate);
        statisticsArg.setMarketingActivityIdList(allEmployeeSpreadMarketingActivityIds);
        statisticsArg.setSpreadFsUserIdList(pageActivityEmployeeIds);
        statisticsArg.setObjectIdList(objectIdList);
        statisticsArg.setActionTypeList(Lists.newArrayList(NewActionTypeEnum.LOOK_UP.getActionType(), NewActionTypeEnum.FORWARD.getActionType()));
        com.facishare.marketing.statistic.common.result.Result<List<EmployeeSpreadStatisticList>> listResult = userMarketingStatisticService.queryEmployeeSpreadStatisticList(statisticsArg);
        Map<Integer, EmployeeSpreadStatisticList> userPvFvMap = Maps.newHashMap();
        if(listResult!=null && CollectionUtils.isNotEmpty(listResult.getData())){
            userPvFvMap = listResult.getData().stream().collect(Collectors.toMap(EmployeeSpreadStatisticList::getUserId, Function.identity(), (v1, v2) -> v1));
        }

        List<Integer> queryEmployeeIds = Lists.newArrayList(pageActivityEmployeeIds);
        Map<Integer, EmployeeRankingDataResult> employeeRankingDataMap = new HashMap<>();
        Map<Integer, EmployeeRankingDataResult> employeeCustomizeDataMap = new HashMap<>();
        List<EmployeeRankingDataResult> employeeRankingData = marketingActivityEmployeeDayStatisticDao.sumByMarketingActivityIdsAndDayRangeAndUserIds(ea, allEmployeeSpreadMarketingActivityIds,  startDate, endDate, queryEmployeeIds);
        if (CollectionUtils.isNotEmpty(employeeRankingData)){
            employeeRankingDataMap = employeeRankingData.stream().collect(Collectors.toMap(EmployeeRankingDataResult::getEmployeeId, Function.identity(), (k1, k2)->k2));
        }
        List<EmployeeRankingDataResult> customizeFormData = customizeFormDataUserDAO.groupCountByMarketingActivityIdsAndDateRangeAndUserIds(ea, allEmployeeSpreadMarketingActivityIds, startDate, endDate, queryEmployeeIds);
        if (CollectionUtils.isNotEmpty(customizeFormData)){
            employeeCustomizeDataMap = customizeFormData.stream().collect(Collectors.toMap(EmployeeRankingDataResult::getEmployeeId, Function.identity(), (k1, k2)->k2));
        }

        //提交表单
        for (Integer employeeId : queryEmployeeIds) {
            EmployeeRankingDataResult employeeRankingDataResult = employeeRankingDataMap.get(employeeId);
            EmployeeRankingDataResult employeeCustomizeDataResult = employeeCustomizeDataMap.get(employeeId);
            EmployeeSpreadStatisticList statisticList = userPvFvMap.get(employeeId);
            if (employeeRankingDataResult == null) {
                employeeRankingDataResult = new EmployeeRankingDataResult();
                employeeRankingDataResult.setEmployeeId(employeeId);
                employeeRankingDataResult.setFormDataEnrollCount(0);
                employeeRankingDataResult.setEmployeeSpreadCount(0);
                employeeRankingDataResult.setPv(0);
                employeeRankingDataResult.setForwardCount(0);
            }
            if(statisticList != null){
                employeeRankingDataResult.setPv(statisticList.getLookUpCount());
                employeeRankingDataResult.setForwardCount(statisticList.getForwardCount());
            }
            if (employeeCustomizeDataResult != null) {
                employeeRankingDataResult.setFormDataEnrollCount(employeeCustomizeDataResult.getFormDataEnrollCount() == null ? 0 : employeeCustomizeDataResult.getFormDataEnrollCount());
            }else {
                employeeRankingDataResult.setFormDataEnrollCount(0);
            }
            employeeRankingDataResults.add(employeeRankingDataResult);
        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, queryEmployeeIds, true);
        for (EmployeeRankingDataResult employeeRankingDataResult : employeeRankingDataResults) {
            if (employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()) != null){
                employeeRankingDataResult.setEmployeeName(employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getName());
                employeeRankingDataResult.setDepartmentName(employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getDepartment());
            }else {
                employeeRankingDataResult.setEmployeeName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
                employeeRankingDataResult.setDepartmentName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1704));
            }
        }

        // 收到任务的总数
        List<UserSpreadTaskCount> userSpreadTaskCounts = spreadTaskDAO.queryUserSpreadTaskCountV2(ea, startDate, endDate, arg.getMarketingEventIdList(), objectIds, targetEmployeeIdList);
        // 推广率=已推广任务数/收到的任务总数
        List<UserSpreadTaskCount> userSpreadedTaskCounts = spreadTaskDAO.queryUserSpreadedTaskCountV2(ea, startDate, endDate, arg.getMarketingEventIdList(), objectIds, targetEmployeeIdList);
        if (null != userSpreadTaskCounts && null != userSpreadedTaskCounts) {
            Map<Integer, Integer> userSpreadTaskCountsMap = new HashMap<>();
            Map<Integer, Integer> userSpreadedTaskCountsMap = new HashMap<>();
            userSpreadTaskCounts.forEach(e -> userSpreadTaskCountsMap.put(e.getUserId(), e.getCount()));
            userSpreadedTaskCounts.forEach(e -> userSpreadedTaskCountsMap.put(e.getUserId(), e.getCount()));
            for (EmployeeRankingDataResult employeeRankingDataResult : employeeRankingDataResults) {
                Integer receivedTaskCount = null == userSpreadTaskCountsMap.get(employeeRankingDataResult.getEmployeeId()) ? 0 : userSpreadTaskCountsMap.get(employeeRankingDataResult.getEmployeeId());
                Integer spreaedTaskCount = null == userSpreadedTaskCountsMap.get(employeeRankingDataResult.getEmployeeId()) ? 0 : userSpreadedTaskCountsMap.get(employeeRankingDataResult.getEmployeeId());
                Double spreadRate = BigDecimal.valueOf(receivedTaskCount == 0 ? 0d : spreaedTaskCount * 100 / (receivedTaskCount * 1d)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                employeeRankingDataResult.setReceivedTaskCount(receivedTaskCount);
                employeeRankingDataResult.setSpreadTaskCount(spreaedTaskCount);
                employeeRankingDataResult.setSpreadRate(spreadRate);
            }
        }


        int searchType = arg.getSearchType() == null ? 0:arg.getSearchType();
        if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_PROMOTION.getValue()) {
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getEmployeeSpreadCount() == null ? 0 : data1.getEmployeeSpreadCount();
                Integer employeeSpreadCount2 = data2.getEmployeeSpreadCount() == null ? 0 : data2.getEmployeeSpreadCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_FORWARDS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getForwardCount() == null ? 0 : data1.getForwardCount();
                Integer employeeSpreadCount2 = data2.getForwardCount() == null ? 0 : data2.getForwardCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_VISITS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getPv() == null ? 0 : data1.getPv();
                Integer employeeSpreadCount2 = data2.getPv() == null ? 0 : data2.getPv();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getFormDataEnrollCount() == null ? 0 : data1.getFormDataEnrollCount();
                Integer employeeSpreadCount2 = data2.getFormDataEnrollCount() == null ? 0 : data2.getFormDataEnrollCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_RECEIVED_TASK_COUNT.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer receivedTaskCount1 = data1.getReceivedTaskCount() == null ? 0 : data1.getReceivedTaskCount();
                Integer receivedTaskCount2 = data2.getReceivedTaskCount() == null ? 0 : data2.getReceivedTaskCount();
                return receivedTaskCount1.compareTo(receivedTaskCount2);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SPREAD_RATE.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Double spreadRate1 = data1.getSpreadRate() == null ? 0 : data1.getSpreadRate();
                Double spreadRate2 = data2.getSpreadRate() == null ? 0 : data2.getSpreadRate();
                return spreadRate1.compareTo(spreadRate2);
            });
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> exportAllEmployeeMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg) {

        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult>> exportEmployeeRankingResult = getAllEmployeeMarketingActivityStatisticsRankingDate(ea, fsUserId, arg);
            if(exportEmployeeRankingResult.isSuccess()) {
                String fileName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_558) + ".xlsx";
                Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
                excelConfigMap.put(ExcelConfigEnum.FILE_NAME, fileName);
                excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
                excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
                XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
                XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
                xssfSheet.setDefaultColumnWidth(20);
                List<String> titleList = ImmutableList.of(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_92), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_564_2), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_564_3), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_564_4), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_564_5), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1842), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_96), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_564_8));
                List<List<Object>> dataList = exportEmployeeRankingResult.getData().getResult().stream().map(employeeRankingDataResult -> {
                    List<Object> row = new ArrayList<>(6);
                    row.add(employeeRankingDataResult.getEmployeeName());
                    row.add(employeeRankingDataResult.getDepartmentName());
                    row.add(employeeRankingDataResult.getReceivedTaskCount());
                    row.add(employeeRankingDataResult.getSpreadTaskCount());
                    row.add(employeeRankingDataResult.getSpreadRate()+"%");
                    row.add(employeeRankingDataResult.getEmployeeSpreadCount());
                    row.add(employeeRankingDataResult.getPv());
                    row.add(employeeRankingDataResult.getForwardCount());
                    row.add(employeeRankingDataResult.getFormDataEnrollCount());
                    return row;
                }).collect(Collectors.toList());
                ExcelUtil.fillContent(xssfSheet, titleList, dataList);
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, fileName, ea, fsUserId);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    public List<Integer> getStaticsticsVisitUserRange(String ea, Integer userId, List<Integer> userRange, List<Integer> departmentRanges) {
        return this.getStaticsticsVisitUserRange(ea, userId, userRange, departmentRanges, true);
    }

    public List<Integer> getStaticsticsVisitUserRange(String ea, Integer userId, List<Integer> userRange, List<Integer> departmentRange, boolean isStatistic){
        boolean allCompany = CollectionUtils.isEmpty(userRange) && CollectionUtils.isEmpty(departmentRange) ? true : false;
        Set<Integer> selectEmployeeIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userRange)){
            selectEmployeeIdSet.addAll(userRange);
        }
        if (CollectionUtils.isNotEmpty(departmentRange)){
            selectEmployeeIdSet.addAll(fsAddressBookManager.getEmployeeIdsByCircleIds(ea, departmentRange));
        }

        Set<Integer> configVisitRangSet = new HashSet<>();
        List<Integer> targetVisitRangeList = Lists.newArrayList();
        MarketingActivitySpreadStatisticsVisitRangeEntity visitRangeEntity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, userId);
        if (visitRangeEntity != null){
            AddStatisticsVisitRangeArg.StatisticsVisitRange statisticsVisitRange = GsonUtil.fromJson(visitRangeEntity.getVisitRange(), AddStatisticsVisitRangeArg.StatisticsVisitRange.class);
            if (CollectionUtils.isNotEmpty(statisticsVisitRange.getUserRange())){
                configVisitRangSet.addAll(statisticsVisitRange.getUserRange());
            }
            if (CollectionUtils.isNotEmpty(statisticsVisitRange.getDepartmentRange())){
                configVisitRangSet.addAll(fsAddressBookManager.getEmployeeIdsByCircleIds(ea, statisticsVisitRange.getDepartmentRange()));
            }

            if (allCompany){
                targetVisitRangeList = Lists.newArrayList(configVisitRangSet);
            }else {
                List<Integer> selectEmployeeIdContains = selectEmployeeIdSet.stream().filter(user -> configVisitRangSet.contains(user)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(selectEmployeeIdContains)){
                    targetVisitRangeList.addAll(selectEmployeeIdContains);
                }else{
                    targetVisitRangeList.add(-1000);
                }
            }
        }else {
            boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);
            if (isOpen && isStatistic) {
                GetAddressBookSettingResult addressBookSetting  = fsAddressBookManager.getAddressBookSetting(ea);
                List<Integer>  visitUserRange = Lists.newArrayList();
                if(addressBookSetting.getType().equals(FsAddressBookSettingTypeEnum.FXIAOKE.getType())){
                    //分享通讯录
                    List<Integer> departmentIds = dataPermissionManager.getFsAccessibleDepartmentIds(ea, userId, false);
                    BatchGetEmployeeIdsByDepartmentId.Arg arg = new BatchGetEmployeeIdsByDepartmentId.Arg();
                    arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
                    arg.setDepartmentIds(departmentIds);
                    arg.setMainDepartment(MainDepartment.ALL);//员工的部门属性
                    arg.setRunStatus(RunStatus.ACTIVE);//员工状态
                    arg.setIncludeLowDepartment(true);//是否向下递归下级部门
                    BatchGetEmployeeIdsByDepartmentId.Result result = employeeProviderService.batchGetEmployeeIdsByDepartmentId(arg);
                    visitUserRange = result.getEmployeeIds();
                }else if (addressBookSetting.getType().equals(FsAddressBookSettingTypeEnum.QYWX.getType())){
                    // 开启数据权限
                    List<Integer> qywxAccessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(ea, userId,true);
                    if(CollectionUtils.isEmpty(qywxAccessibleDepartmentIds)){
                        return null;
                    }
                    if (!qywxAccessibleDepartmentIds.contains(defaultAllDepartment)){
                        visitUserRange = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, qywxAccessibleDepartmentIds);
                    }
                }
                List<Integer> finalVisitUserRange = visitUserRange;
                if (allCompany) {
                    //有查询条件
                    targetVisitRangeList.addAll(finalVisitUserRange);
                } else {
                    selectEmployeeIdSet.stream().filter(finalVisitUserRange::contains).forEach(targetVisitRangeList::add);
                }
            } else {
                if (allCompany) {
                    return null;
                } else {
                    targetVisitRangeList.addAll(selectEmployeeIdSet);
                }
            }
        }

        return targetVisitRangeList;
    }
    
    /**
     * 伙伴营销推广统计-伙伴推广排行
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    @Override
    public Result<com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult>> getPartnerMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<EmployeeRankingDataResult> employeeRankingDataResults = new ArrayList<>(0);
        EmployeeRankingTotalStat employeeRankingTotalStat = new EmployeeRankingTotalStat();
        com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult> pageResult = new com.facishare.marketing.common.result.PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(employeeRankingDataResults);
        pageResult.setTotalCount(0);
        pageResult.setOtherData(employeeRankingTotalStat);

        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        //所有伙伴营销 marketingactivityid
        List<String> partnerSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listPartnerEmployeeActivityIdsInDateRangeWithObjectIdsV2(ea, startDate, endDate, arg.getMarketingEventIdList(), objectIds);

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(partnerSpreadMarketingActivityIds)){
            return Result.newSuccess(pageResult);
        }
        //前端传的下游企业和小企业组 --获取所有下游企业id
        List<String> targetOutTenantIdList = fsAddressBookManager.getOutTenantId(arg.getEaList(), arg.getTenantGroupIdList(), ea, fsUserId);
        List<Long> queryOutTenantIdList = targetOutTenantIdList.stream().map(Long::parseLong).distinct().collect(Collectors.toList());
        //当请分页查询数据（包括已推广和未推广）
       // List<String> pageActivityOutTenantIds = marketingActivityEmployeeStatisticDao.queryPageByMarketingActivityIdsAndOutTennatId(ea, partnerSpreadMarketingActivityIds, CollectionUtils.isNotEmpty(targetOutTenantIdList) ?targetOutTenantIdList : null, page);
        List<MarketingActivityCompanyByClueDTO>  marketingActivityCompanyByClueList = spreadTaskDAO.queryPagePartnerSpreadTask(ea, partnerSpreadMarketingActivityIds, queryOutTenantIdList, page);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(marketingActivityCompanyByClueList)){
            return Result.newSuccess(pageResult);
        }
        int totalCount = spreadTaskDAO.countPartnerSpreadTask(ea, partnerSpreadMarketingActivityIds, queryOutTenantIdList);
        pageResult.setTotalCount(totalCount);

        List<Long> queryOutTenantIdLongList = marketingActivityCompanyByClueList.stream().map(e -> Long.parseLong(e.getOuterTenantId())).distinct().collect(Collectors.toList());
        Map<String, EmployeeRankingDataResult> employeeRankingDataMap = new HashMap<>();
        Map<String, EmployeeRankingDataResult> employeeCustomizeDataMap = new HashMap<>();
        List<EmployeeRankingDataResult> employeeRankingData = marketingActivityEmployeeDayStatisticDao.sumByMarketingActivityIdsAndDayRangeAndOutTenantIds(ea, partnerSpreadMarketingActivityIds,  startDate, endDate, queryOutTenantIdLongList);
       // List<OutEmployeeSpreadData> spreadEmployeeNum = marketingActivityEmployeeDayStatisticDao.sumSpreadNumByMarketingActivityIdsAndDayRangeAndOutTenantIds(ea, partnerSpreadMarketingActivityIds,  startDate, endDate, queryOutTenantIds);
//        Map<String, Integer> outTenantIdToSpreadEmployeeNum = Maps.newHashMap();
//        if (CollectionUtils.isNotEmpty(spreadEmployeeNum)) {
//            outTenantIdToSpreadEmployeeNum = spreadEmployeeNum.stream().collect(Collectors.toMap(OutEmployeeSpreadData::getOutTenantId, OutEmployeeSpreadData::getOutEmployeeSpread, (k1, k2) -> k2));
//        }
        if (CollectionUtils.isNotEmpty(employeeRankingData)) {
            for (EmployeeRankingDataResult employeeRankingDatum : employeeRankingData) {
//                if (null != outTenantIdToSpreadEmployeeNum && outTenantIdToSpreadEmployeeNum.containsKey(employeeRankingDatum.getOutTenantId())) {
//                    employeeRankingDatum.setOutTenantSpreadEmployee(outTenantIdToSpreadEmployeeNum.get(employeeRankingDatum.getOutTenantId()));
//                } else {
//                    employeeRankingDatum.setOutTenantSpreadEmployee(0);
//                }
                employeeRankingDataMap.put(employeeRankingDatum.getOutTenantId(), employeeRankingDatum);
            }
        }
        List<EmployeeRankingDataResult> customizeFormData = customizeFormDataUserDAO.groupCountByMarketingActivityIdsAndDateRangeAndOutTenantIds(ea, partnerSpreadMarketingActivityIds, startDate, endDate, queryOutTenantIdLongList);
        if (CollectionUtils.isNotEmpty(customizeFormData)){
            employeeCustomizeDataMap = customizeFormData.stream().collect(Collectors.toMap(EmployeeRankingDataResult::getOutTenantId, Function.identity(), (k1, k2)->k2));
        }
        List<String> queryOutTenantIds = marketingActivityCompanyByClueList.stream().map(MarketingActivityCompanyByClueDTO::getOuterTenantId).distinct().collect(Collectors.toList());
        List<Integer> intQueryOutTenantIds = queryOutTenantIds.stream().map(q -> Integer.valueOf(q)).collect(Collectors.toList());
        //提交表单
        for (String outEa : queryOutTenantIds) {
            EmployeeRankingDataResult employeeRankingDataResult = employeeRankingDataMap.get(outEa);
            EmployeeRankingDataResult employeeCustomizeDataResult = employeeCustomizeDataMap.get(outEa);
            if (employeeRankingDataResult == null) {
                employeeRankingDataResult = new EmployeeRankingDataResult();
                employeeRankingDataResult.setFormDataEnrollCount(0);
                employeeRankingDataResult.setEmployeeSpreadCount(0);
                employeeRankingDataResult.setPv(0);
                employeeRankingDataResult.setForwardCount(0);
                employeeRankingDataResult.setOutTenantId(outEa);
                employeeRankingDataResult.setOutTenantSpreadEmployee(0);
            }
            if (employeeCustomizeDataResult != null) {
                employeeRankingDataResult.setFormDataEnrollCount(employeeCustomizeDataResult.getFormDataEnrollCount() == null ? 0 : employeeCustomizeDataResult.getFormDataEnrollCount());
            }else {
                employeeRankingDataResult.setFormDataEnrollCount(0);
            }
            employeeRankingDataResults.add(employeeRankingDataResult);
        }
        Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(queryOutTenantIds);
//        Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, queryEmployeeIds, true);
        for (EmployeeRankingDataResult employeeRankingDataResult : employeeRankingDataResults) {
            employeeRankingDataResult.setOutTenantName(companyIdToCompanyMap.get(employeeRankingDataResult.getOutTenantId()));
        }

        int searchType = arg.getSearchType() == null ? 0:arg.getSearchType();
        if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_PROMOTION.getValue()) {
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getEmployeeSpreadCount() == null ? 0 : data1.getEmployeeSpreadCount();
                Integer employeeSpreadCount2 = data2.getEmployeeSpreadCount() == null ? 0 : data2.getEmployeeSpreadCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_FORWARDS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getForwardCount() == null ? 0 : data1.getForwardCount();
                Integer employeeSpreadCount2 = data2.getForwardCount() == null ? 0 : data2.getForwardCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_VISITS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getPv() == null ? 0 : data1.getPv();
                Integer employeeSpreadCount2 = data2.getPv() == null ? 0 : data2.getPv();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getFormDataEnrollCount() == null ? 0 : data1.getFormDataEnrollCount();
                Integer employeeSpreadCount2 = data2.getFormDataEnrollCount() == null ? 0 : data2.getFormDataEnrollCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }

        return Result.newSuccess(pageResult);
    }


    @Override
    public Result<Void> exportPartnerMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg) {

        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult>> exportEmployeeRankingResult = getPartnerMarketingActivityStatisticsRankingDate(ea, fsUserId, arg);
            if(!exportEmployeeRankingResult.isSuccess()) {
                log.warn("CustomizeFormDataController.exportEnrollsData error");
                return;
            }
            String fileName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_814) + ".xlsx";
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, fileName);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = ImmutableList.of(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_820), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_820_1), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_94), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1842), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1844), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_564_8));
            List<List<Object>> dataList = exportEmployeeRankingResult.getData().getResult().stream().map(employeeRankingDataResult -> {
                List<Object> row = new ArrayList<>(6);
                row.add(employeeRankingDataResult.getOutTenantName());
                row.add(employeeRankingDataResult.getOutTenantSpreadEmployee());
                row.add(employeeRankingDataResult.getEmployeeSpreadCount());
                row.add(employeeRankingDataResult.getPv());
                row.add(employeeRankingDataResult.getForwardCount());
                row.add(employeeRankingDataResult.getFormDataEnrollCount());
                return row;
            }).collect(Collectors.toList());
            ExcelUtil.fillContent(xssfSheet, titleList, dataList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, fileName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result<List<EmployeeRankingDataResult>> getAllEmployeeSpreadEmployeeRankingData(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());


        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        List<String> allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIds(ea, startDate, endDate, arg.getMarketingEventId(), objectIds);

      //  List<String> allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRange(ea, startDate, endDate, arg.getMarketingEventId(), arg.getObjectId());
        List<EmployeeRankingDataResult> employeeRankingDataResults = new ArrayList<>(0);
        if (!allEmployeeSpreadMarketingActivityIds.isEmpty()){
            employeeRankingDataResults = marketingActivityEmployeeDayStatisticDao.sumByMarketingActivityIdsAndDayRange(ea, allEmployeeSpreadMarketingActivityIds, startDate, endDate);
            Map<Integer, Integer> employeeIdToFormDataEnrollMap = customizeFormDataUserDAO.groupCountByMarketingActivityIdsAndDateRange(ea, allEmployeeSpreadMarketingActivityIds, startDate, endDate).stream()
                    .collect(Collectors.toMap(EmployeeRankingDataResult::getEmployeeId, EmployeeRankingDataResult::getFormDataEnrollCount, (v1, v2) -> v1));
            for (EmployeeRankingDataResult employeeRankingDataResult : employeeRankingDataResults) {
                employeeRankingDataResult.setFormDataEnrollCount(employeeIdToFormDataEnrollMap.get(employeeRankingDataResult.getEmployeeId()) == null ? 0 : employeeIdToFormDataEnrollMap.get(employeeRankingDataResult.getEmployeeId()));
            }
        }

        if ((arg.getEmployeeRange() != null && !arg.getEmployeeRange().isEmpty()) || (arg.getDepartmentRange() != null && !arg.getDepartmentRange().isEmpty()) && !arg.getDepartmentRange().contains(999999)){
            Set<Integer> targetEmployeeIdSet = new HashSet<>();
            if (arg.getEmployeeRange() != null){
                targetEmployeeIdSet.addAll(arg.getEmployeeRange());
            }
            if (arg.getDepartmentRange() != null && !arg.getDepartmentRange().isEmpty()){
                targetEmployeeIdSet.addAll(fsAddressBookManager.getEmployeeIdsByCircleIds(ea, arg.getDepartmentRange()));
            }
            employeeRankingDataResults = employeeRankingDataResults.stream().filter(result -> targetEmployeeIdSet.contains(result.getEmployeeId())).collect(Collectors.toList());
        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, employeeRankingDataResults.stream().map(EmployeeRankingDataResult::getEmployeeId).collect(Collectors.toList()), true);
        for (EmployeeRankingDataResult employeeRankingDataResult : employeeRankingDataResults) {
            if (employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()) != null){
                employeeRankingDataResult.setEmployeeName(employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getName());
                employeeRankingDataResult.setDepartmentName(employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getDepartment());
            }else {
                employeeRankingDataResult.setEmployeeName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
                employeeRankingDataResult.setDepartmentName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1704));
            }
        }
        int searchType = arg.getSearchType() == null ? 0:arg.getSearchType();
        if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_PROMOTION.getValue()) {
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getEmployeeSpreadCount() == null ? 0 : data1.getEmployeeSpreadCount();
                Integer employeeSpreadCount2 = data2.getEmployeeSpreadCount() == null ? 0 : data2.getEmployeeSpreadCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_FORWARDS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getForwardCount() == null ? 0 : data1.getForwardCount();
                Integer employeeSpreadCount2 = data2.getForwardCount() == null ? 0 : data2.getForwardCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_VISITS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getPv() == null ? 0 : data1.getPv();
                Integer employeeSpreadCount2 = data2.getPv() == null ? 0 : data2.getPv();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getFormDataEnrollCount() == null ? 0 : data1.getFormDataEnrollCount();
                Integer employeeSpreadCount2 = data2.getFormDataEnrollCount() == null ? 0 : data2.getFormDataEnrollCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }

        return Result.newSuccess(employeeRankingDataResults);
    }

    private List<DayTrendData> convertDateCountListToDayTrend(List<DateCount> dateCounts, Date startDate, Date endDate){
        final Map<String, Integer> dateCountMap = dateCounts.stream().map(DateCount::toDayTrendData).collect(Collectors.toMap(DayTrendData::getDate, DayTrendData::getCount, (v1, v2) -> v1));
        List<String> dateRange = DateUtil.listDateBetweenStartTimeAndEndTime2String(startDate, endDate, "yyyy-MM-dd");
        return dateRange.stream().map(date -> new DayTrendData(date, dateCountMap.get(date) == null ? 0 : dateCountMap.get(date))).collect(Collectors.toList());
    }

    @Override
    public Result<PageResult<ObjectStatisticResult>> listObjectStatisticData(String ea, Integer fsUserId, ListObjectStatisticDataArg arg) {
        Page<ObjectDto> page = new Page<>(arg.getPageNo(), arg.getPageSize(), true);
        List<ObjectDto> objects;
        List<ObjectGroupEntity> objectGroupEntityList = Lists.newArrayList();
        if (arg.getObjectType() == null){
            objectGroupEntityList.addAll(objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType()));
            objectGroupEntityList.addAll(objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType()));
            objectGroupEntityList.addAll(objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType()));
            List<String> objectGroupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            objects = objectDao.unionSearchObject(ea, fsUserId, arg.getName(), objectGroupIds, page);
        }else{
            objectGroupEntityList.addAll(objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, arg.getObjectType()));
            List<String> objectGroupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            objects = objectDao.searchObjectByType(ea, fsUserId, arg.getObjectType(), arg.getName(), objectGroupIds, page);
        }
        if (objects.isEmpty()){
            return Result.newSuccess(new PageResult<>(0, new ArrayList<>(0)));
        }
        
        objectManager.fillObjectHeadImage(ea, objects);

        Set<String> objectIds = objects.stream().map(ObjectDto::getId).collect(Collectors.toSet());
        Map<String, Integer> objectUvMap = objectStatisticService.getUVs(ea, objectIds).getData();
        List<ObjectStatisticData> objectStatisticData = marketingObjectAmountStatisticDao.listStatisticData(ea, objectIds);
        Map<String, Integer> objectPvMap = objectStatisticData.stream().collect(Collectors.toMap(ObjectStatisticData::getObjectId, ObjectStatisticData::getLookUpCount, (v1, v2) -> v1));

        List<String> hexagonSiteIdList = objects.stream().filter(obj -> obj.getObjectType() == ObjectTypeEnum.HEXAGON_SITE.getType()).map(ObjectDto::getId).collect(Collectors.toList());
        Map<String, String> hexagonSiteIdToMainPageIdMap = new HashMap<>();
        if (!hexagonSiteIdList.isEmpty()){
            hexagonSiteIdToMainPageIdMap = hexagonSiteDAO.getFormBySiteIds(hexagonSiteIdList,ea).stream().collect(Collectors.toMap(HexagonSiteListDTO::getHexagonSiteId, HexagonSiteListDTO::getHexagonPageId, (v1, v2) -> v1));
        }
        Set<String> objectIdToGetLeadCount = new HashSet<>(objectIds);
        objectIdToGetLeadCount.removeAll(hexagonSiteIdToMainPageIdMap.keySet());
        objectIdToGetLeadCount.addAll(hexagonSiteIdToMainPageIdMap.values());
        objectIdToGetLeadCount.remove(null);
        List<DataCount> dataCounts = new ArrayList<>(0);
        if (!objectIdToGetLeadCount.isEmpty()){
            dataCounts = customizeFormDataUserDAO.groupCountByMarketingObjectIds(ea, objectIdToGetLeadCount);
        }
        Map<String, Integer> objectFormDataEnrollCountMap = dataCounts.stream().collect(DataCount.getMapCollector());
        Map<String, Integer> parentObjectFormDataEnrollCountMap = customizeFormDataUserDAO.groupCountByMarketingParentObjectIds(ea, objectIds).stream().collect(DataCount.getMapCollector());
        if (parentObjectFormDataEnrollCountMap != null) {
            parentObjectFormDataEnrollCountMap.forEach((key, value) -> objectFormDataEnrollCountMap.merge(key, value, Integer::sum));
        }

        Map<String, String> finalHexagonSiteIdToMainPageIdMap = hexagonSiteIdToMainPageIdMap;
        List<ObjectStatisticResult> statisticResults = objects.stream().map(object -> {
            ObjectStatisticResult sr = new ObjectStatisticResult();
            BeanUtils.copyProperties(object, sr, "createTime");
            sr.setCreateTime(object.getCreateTime().getTime());
            sr.setUv(objectUvMap == null || objectUvMap.get(object.getId()) == null ? 0 : objectUvMap.get(object.getId()));
            sr.setPv(objectPvMap.get(object.getId()));
            if (object.getObjectType() != ObjectTypeEnum.HEXAGON_SITE.getType()){
                sr.setFormDataEnrollCount(objectFormDataEnrollCountMap.get(object.getId()));
            }else{
                sr.setFormDataEnrollCount(objectFormDataEnrollCountMap.get(finalHexagonSiteIdToMainPageIdMap.get(object.getId())));
            }
            return sr;
        }).collect(Collectors.toList());
        return Result.newSuccess(new PageResult<>(page.getTotalNum(), statisticResults));
    }

    @Override
    public Result<ObjectStatisticResult> getObjectStatisticDetail(String ea, Integer fsUserId, GetObjectStatisticDetailArg arg) {
        Preconditions.checkArgument(arg.getObjectId() != null, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_968));
        Preconditions.checkArgument(arg.getObjectType() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_995));
        Preconditions.checkArgument(arg.getTrendDataStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_996));
        Preconditions.checkArgument(arg.getTrendDataEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_997));

        ObjectDto object = objectDao.getById(ea, arg.getObjectType(), arg.getObjectId());
        Preconditions.checkArgument(object != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1000));

        ObjectStatisticResult objectStatisticResult = new ObjectStatisticResult();
        BeanUtils.copyProperties(object, objectStatisticResult, "createTime");
        objectStatisticResult.setCreateTime(object.getCreateTime().getTime());

        Set<String> objectIds = Sets.newHashSet(object.getId());
        Map<String, Integer> objectUvMap = objectStatisticService.getUVs(ea, objectIds).getData();
        objectStatisticResult.setUv(objectUvMap == null || objectUvMap.get(object.getId()) == null ? 0 : objectUvMap.get(object.getId()));

        List<ObjectStatisticData> objectStatisticData = marketingObjectAmountStatisticDao.listStatisticData(ea, objectIds);
        if (!objectStatisticData.isEmpty()){
            objectStatisticResult.setPv(objectStatisticData.get(0).getLookUpCount());
            objectStatisticResult.setForwardCount(objectStatisticData.get(0).getForwardCount());
        }

        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<String> targetObjectIds = null;
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                targetObjectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isNotEmpty(targetObjectIds)) {
                objectIds.addAll(targetObjectIds);
            }
        }

        List<String> allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsByObjectIds(ea, new ArrayList<>(objectIds));
        if (!allEmployeeSpreadMarketingActivityIds.isEmpty()){
            CommonStatisticData commonStatisticData = marketingActivityAmountStatisticDao.sumUpStatistc(ea, allEmployeeSpreadMarketingActivityIds);
            if (commonStatisticData != null){
                objectStatisticResult.setEmployeeSpreadCount(commonStatisticData.getSpreadCount());
            }
            int spreadEmployeeCount = marketingActivityEmployeeAmountStatisticDao.countByMarketingActivityIds(ea, allEmployeeSpreadMarketingActivityIds);
            objectStatisticResult.setSpreadEmployeeCount(spreadEmployeeCount);
        }

        Date startDate = new Date(arg.getTrendDataStartDate());
        Date endDate = new Date(arg.getTrendDataEndDate());
        if (arg.getObjectType() != ObjectTypeEnum.HEXAGON_SITE.getType()){
            Map<String, Integer> objectFormDataEnrollCountMap = customizeFormDataUserDAO.groupCountByMarketingObjectIds(ea, objectIds).stream().collect(DataCount.getMapCollector());
            Map<String, Integer> parentObjectFormDataEnrollCountMap = customizeFormDataUserDAO.groupCountByMarketingParentObjectIds(ea, objectIds).stream().collect(DataCount.getMapCollector());
            parentObjectFormDataEnrollCountMap.forEach((key, value) -> objectFormDataEnrollCountMap.merge(key, value, Integer::sum));
            objectStatisticResult.setFormDataEnrollCount(objectFormDataEnrollCountMap.get(object.getId()));
            objectStatisticResult.setFormDataEnrollTrend(convertDateCountListToDayTrend(customizeFormDataUserDAO.dateCountByMarketingObjectIdsAndDateRange(ea, object.getId(), startDate, endDate), startDate, endDate));
        }else{
            Map<String, String> hexagonSiteIdToMainPageIdMap = hexagonSiteDAO.getFormBySiteIds(ImmutableList.of(object.getId()),ea).stream().collect(Collectors.toMap(HexagonSiteListDTO::getHexagonSiteId, HexagonSiteListDTO::getHexagonPageId, (v1, v2) -> v1));
            if (hexagonSiteIdToMainPageIdMap.get(object.getId()) != null){
                Map<String, Integer> objectFormDataEnrollCountMap = customizeFormDataUserDAO.groupCountByMarketingObjectIds(ea, hexagonSiteIdToMainPageIdMap.values()).stream().collect(DataCount.getMapCollector());
                objectStatisticResult.setFormDataEnrollCount(objectFormDataEnrollCountMap.get(hexagonSiteIdToMainPageIdMap.get(object.getId())));
                objectStatisticResult.setFormDataEnrollTrend(convertDateCountListToDayTrend(customizeFormDataUserDAO.dateCountByMarketingObjectIdsAndDateRange(ea, hexagonSiteIdToMainPageIdMap.get(object.getId()), startDate, endDate), startDate, endDate));
            }
        }
        objectStatisticResult.setPvTrend(convertDateCountListToDayTrend(marketingObjectDayStatisticDao.listLookUpCountInDateRange(ea, object.getId(), startDate, endDate), startDate, endDate));
        if (objectStatisticResult.getPvTrend() == null){
            objectStatisticResult.setPvTrend(convertDateCountListToDayTrend(new ArrayList<>(0), startDate, endDate));
        }
        if (objectStatisticResult.getFormDataEnrollTrend() == null){
            objectStatisticResult.setFormDataEnrollTrend(convertDateCountListToDayTrend(new ArrayList<>(0), startDate, endDate));
        }

        return Result.newSuccess(objectStatisticResult);
    }

    @Override
    public Result<GetEmployeeSpreadStatisticDataWithSubordinateResult> getEmployeeSpreadStatisticDataWithSubordinate(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDataWithSubordinateArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());

        GetEmployeeSpreadStatisticDataWithSubordinateResult result = new GetEmployeeSpreadStatisticDataWithSubordinateResult();
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        List<String> allEmployeeSpreadMarketingActivityIds;
        // 小程序首页——我的简报，现在会员营销只有这个报表，其他的暂时不处理，这里先单独处理这个
        if (QywxUserConstants.isMemberVirtualUserId(fsUserId)) {
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listEmployeeActivityIdsInDateRangeWithObjectIdsAndType(ea, startDate, endDate, arg.getMarketingEventId(), objectIds, AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType());
        } else if (QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listEmployeeActivityIdsInDateRangeWithObjectIdsAndType(ea, startDate, endDate, arg.getMarketingEventId(), objectIds, AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType());
        } else {
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIds(ea, startDate, endDate, arg.getMarketingEventId(), objectIds);
        }
        if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)){
            return Result.newSuccess(result);
        }

        List<Integer> targetEmployeeIds = getTargetEmployeeIdList(ea, fsUserId, arg);
        if (CollectionUtils.isEmpty(targetEmployeeIds)){
            log.info("StatisticServiceImpl.getEmployeeSpreadStatisticDataWithSubordinate return null ea:{} userId:{} arg:{}", ea, fsUserId, arg);
            return Result.newSuccess(result);
        }
        SpreadStatisticDataWithSubordinateDTO spreadStatisticDataWithSubordinateDTO = marketingActivityEmployeeDayStatisticDao.getEmployeeSpreadCountByMarketingActivityIdsAndDayRange(ea,
                allEmployeeSpreadMarketingActivityIds, targetEmployeeIds, new Date(arg.getStartDate()), new Date(arg.getEndDate()));
        if (spreadStatisticDataWithSubordinateDTO == null) {
            result.setEmployeeSpreadCount(0);
        } else {
            result.setEmployeeSpreadCount(spreadStatisticDataWithSubordinateDTO.getEmployeeSpreadCount() == null ? 0 : spreadStatisticDataWithSubordinateDTO.getEmployeeSpreadCount());
        }
        BatchObjectActionStatisticsArg statisticArg = new BatchObjectActionStatisticsArg();
        statisticArg.setEa(ea);
        statisticArg.setMarketingActivityIdList(allEmployeeSpreadMarketingActivityIds);
        statisticArg.setSpreadFsUserIdList(targetEmployeeIds);
        statisticArg.setStartTime(startDate);
        statisticArg.setEndTime(endDate);
        statisticArg.setActionTypeList(Lists.newArrayList(NewActionTypeEnum.FORWARD.getActionType(),NewActionTypeEnum.LOOK_UP.getActionType()));
        com.facishare.marketing.statistic.common.result.Result<EnterpriseSpreadStatisticResult> spreadStatistic = userMarketingStatisticService.getEnterpriseSpreadStatistic(statisticArg);
        if (spreadStatistic == null || spreadStatistic.getData() == null) {
            return Result.newSuccess(result);
        }
        EnterpriseSpreadStatisticResult spreadStatisticData = spreadStatistic.getData();
        Integer enrollCount = customizeFormDataUserDAO.countByMarketingActivityIdsAndUserIdRange(ea, allEmployeeSpreadMarketingActivityIds,
                targetEmployeeIds, new Date(arg.getStartDate()), new Date(arg.getEndDate()));

        result.setFormDataEnrollCount(enrollCount == null ? 0 : enrollCount);
        result.setPv(spreadStatisticData.getLookUpCount() == null ? 0 : spreadStatisticData.getLookUpCount());
        result.setForwardCount(spreadStatisticData.getForwardCount() == null ? 0 : spreadStatisticData.getForwardCount());

        return Result.newSuccess(result);
    }

    private List<Integer> getTargetEmployeeIdList(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDataWithSubordinateArg arg) {
        // 会员营销只能看自己的数据
        if (QywxUserConstants.isMemberVirtualUserId(fsUserId) || QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
            return Lists.newArrayList(fsUserId);
        }
        List<Integer> subordinateEmployeeIds = null;
        //兼容钉钉版营销通小程序,获取下属员工，钉钉&企业微信全全部员工，纷享员工取自己的下属
        if (dingManager.isDingAddressbook(ea)){
            subordinateEmployeeIds = getDingEmployeeByDepartment(ea,fsUserId,arg.getDepartmentRange());
        } else {
            subordinateEmployeeIds = getSubordinateEmployeesByUserAndDepartment(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        }

        MarketingActivitySpreadStatisticsVisitRangeEntity visitRangeEntity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, fsUserId);
        if (visitRangeEntity != null){
            return getStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        }
        List<Integer> targetEmployeeIds  = subordinateEmployeeIds;
        if (CollectionUtils.isEmpty(targetEmployeeIds) && (CollectionUtils.isNotEmpty(arg.getEmployeeRange()) || CollectionUtils.isNotEmpty(arg.getDepartmentRange()))){
            return Lists.newArrayList();
        }
        targetEmployeeIds.add(fsUserId);
        return targetEmployeeIds;
    }

    @Override
    public Result<GetEmployeeSpreadStatisticDataWithSubordinateResult> getEmployeeSpreadStatisticDataForPartner(String upstreamEa, String outTenantId,String outUserId, GetEmployeeSpreadStatisticDataWithSubordinateArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());

        GetEmployeeSpreadStatisticDataWithSubordinateResult result = new GetEmployeeSpreadStatisticDataWithSubordinateResult();
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(upstreamEa, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        //所有活动id
        List<String> allEmployeeSpreadMarketingActivityIds = spreadTaskDAO.getPartnerMarketingActivityIds(upstreamEa, outTenantId, outTenantId);
        if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)){
            return Result.newSuccess(result);
        }
        Map<Integer,String> outUserToNameMap = new HashMap<>();
        List<String> outTenantIdList = new ArrayList<>();
        outTenantIdList.add(outTenantId);
        //获取该outtenant下所有对接人
        List<Integer> dockUserId = fsAddressBookManager.getDockUserId(outTenantIdList, null, upstreamEa, -1000, new HashMap<>());
        if (CollectionUtils.isEmpty(dockUserId)){
            log.info("StatisticServiceImpl.getEmployeeSpreadStatisticDataWithSubordinate return null ea:{} userId:{} arg:{}", outTenantIdList, outUserId, arg);
            return Result.newSuccess(result);
        }
        SpreadStatisticDataWithSubordinateDTO spreadStatisticDataWithSubordinateDTO = marketingActivityEmployeeDayStatisticDao.getEmployeeSpreadCountByMarketingActivityIdsAndDayRangeForPartner(upstreamEa,
               outTenantId, allEmployeeSpreadMarketingActivityIds, dockUserId, new Date(arg.getStartDate()), new Date(arg.getEndDate()));
        if (spreadStatisticDataWithSubordinateDTO == null){
            return Result.newSuccess(result);
        }
        Integer enrollCount = customizeFormDataUserDAO.countByMarketingActivityIdsAndUserIdAndOutTenantIdRange(upstreamEa,outTenantId, allEmployeeSpreadMarketingActivityIds,
                dockUserId, new Date(arg.getStartDate()), new Date(arg.getEndDate()), upstreamEa);

        result.setFormDataEnrollCount(enrollCount == null ? 0 : enrollCount);
        result.setEmployeeSpreadCount(spreadStatisticDataWithSubordinateDTO.getEmployeeSpreadCount() == null ? 0 : spreadStatisticDataWithSubordinateDTO.getEmployeeSpreadCount());
        result.setPv(spreadStatisticDataWithSubordinateDTO.getPv() == null ? 0 : spreadStatisticDataWithSubordinateDTO.getPv());
        result.setForwardCount(spreadStatisticDataWithSubordinateDTO.getForwardCount() == null ? 0 : spreadStatisticDataWithSubordinateDTO.getForwardCount());

        return Result.newSuccess(result);
    }

    @Override
    public Result<com.facishare.marketing.api.result.PageResult<EmployeeRankingDataResult>> getEmployeeSpreadRankWithSubordinate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));

       com.facishare.marketing.api.result.PageResult<EmployeeRankingDataResult> employeeRankingDataResultPageResult = new com.facishare.marketing.api.result.PageResult<>();
        employeeRankingDataResultPageResult.setTotalCount(0);
        Page page = new Page(arg.getPageNum(), arg.getPageSize());
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        List<String> allEmployeeSpreadMarketingActivityIds;
        if (QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listEmployeeActivityIdsInDateRangeWithObjectIdsAndType(ea, startDate, endDate, arg.getMarketingEventId(), objectIds, AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType());
        } else {
            allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIds(ea, startDate, endDate, arg.getMarketingEventId(), objectIds);
        }
        if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)){
            employeeRankingDataResultPageResult.setData(Lists.newArrayList());
            return Result.newSuccess(employeeRankingDataResultPageResult);
        }
        List<Integer> subordinateEmployeeIds = null;
        List<Integer> targetEmployeeIds = null;
        //兼容钉钉版营销通小程序,获取下属员工，钉钉&企业微信全全部员工，纷享员工取自己的下属
        if (dingManager.isDingAddressbook(ea)){
            subordinateEmployeeIds = getDingEmployeeByDepartment(ea,fsUserId,arg.getDepartmentRange());
        } else {
            subordinateEmployeeIds = getSubordinateEmployeesByUserAndDepartment(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        }
        MarketingActivitySpreadStatisticsVisitRangeEntity visitRangeEntity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, fsUserId);
        List<EmployeeRankingDataResult> employeeRankingDataResults = Lists.newArrayList();
        List<Integer> visitUserRange = getStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        //未配置全员营销权限,则查询下级数据, 如果已经配置,则查询能查看的范围内所有数据
        if (visitRangeEntity == null){
            targetEmployeeIds = subordinateEmployeeIds;
            if (CollectionUtils.isEmpty(targetEmployeeIds)){
                if (CollectionUtils.isNotEmpty(arg.getEmployeeRange()) || CollectionUtils.isNotEmpty(arg.getDepartmentRange())) {
                    log.info("StatisticServiceImpl.getSubordinateEmployeesByUserAndDepartment return null ea:{} userId:{} arg:{}", ea, fsUserId, arg);
                    employeeRankingDataResultPageResult.setData(Lists.newArrayList());
                    return Result.newSuccess(employeeRankingDataResultPageResult);
                } else {
                    targetEmployeeIds.add(fsUserId);
                }
            }
            log.info("targetEmployeeIds size:{}", targetEmployeeIds.size());
            employeeRankingDataResultPageResult.setTotalCount(targetEmployeeIds.size());
            List<EmployeeRankingDataResult> employeeRankingDataLists = marketingActivityEmployeeDayStatisticDao.sumByMarketingActivityIdsAndDayRangeAndUserIds(ea, allEmployeeSpreadMarketingActivityIds,  startDate, endDate, targetEmployeeIds);
            if (CollectionUtils.isEmpty(employeeRankingDataLists)) {
                EmployeeRankingDataResult employeeRankingDataResult = new EmployeeRankingDataResult();
                Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(fsUserId), false);
                FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = employeeMsgMap.get(fsUserId);
                String employeeName = fsEmployeeMsg == null ? "" : fsEmployeeMsg.getName();
                employeeRankingDataResult.setEmployeeName(employeeName);
                employeeRankingDataResult.setEmployeeId(fsUserId);
                employeeRankingDataResult.setFormDataEnrollCount(0);
                employeeRankingDataResult.setEmployeeSpreadCount(0);
                employeeRankingDataResult.setPv(0);
                employeeRankingDataResult.setForwardCount(0);
                employeeRankingDataResult.setFormDataEnrollCount(0);
                employeeRankingDataResultPageResult.setData(Lists.newArrayList(employeeRankingDataResult));
                return Result.newSuccess(employeeRankingDataResultPageResult);
            }
            //根据条件进行mongodb查询访问数和转发数
            Map<Integer, com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult> statisticMap = getUserMarketingListResultMap(ea, startDate, endDate, allEmployeeSpreadMarketingActivityIds, targetEmployeeIds);
            List<Integer> currentEmployeeIds = employeeRankingDataLists.stream().map(EmployeeRankingDataResult -> EmployeeRankingDataResult.getEmployeeId()).collect(Collectors.toList());
            Map<Integer, Integer> employeeIdToFormDataEnrollMap = new HashMap<>();
            Map<Integer, EmployeeRankingDataResult> employeeRankingDataMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(employeeRankingDataLists)){
                employeeRankingDataMap = employeeRankingDataLists.stream().collect(Collectors.toMap(EmployeeRankingDataResult::getEmployeeId, Function.identity(), (k1, k2)->k2));
            }
            List<EmployeeRankingDataResult> groupCountByMarketingActivityIdsAndUserId = customizeFormDataUserDAO.groupCountByMarketingActivityIdsAndUserId(ea,
                    allEmployeeSpreadMarketingActivityIds, currentEmployeeIds, new Date(arg.getStartDate()), new Date(arg.getEndDate()));
            if (CollectionUtils.isNotEmpty(groupCountByMarketingActivityIdsAndUserId)) {
                employeeIdToFormDataEnrollMap = groupCountByMarketingActivityIdsAndUserId.stream()
                        .collect(Collectors.toMap(EmployeeRankingDataResult::getEmployeeId, EmployeeRankingDataResult::getFormDataEnrollCount, (v1, v2) -> v1));
            }
            Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, targetEmployeeIds, false);
            for (Integer employeeId : targetEmployeeIds) {
                EmployeeRankingDataResult employeeRankingDataResult = employeeRankingDataMap.get(employeeId);
                Integer enrollCount = employeeIdToFormDataEnrollMap.get(employeeId);
                if (employeeRankingDataResult == null) {
                    employeeRankingDataResult = new EmployeeRankingDataResult();
                    employeeRankingDataResult.setEmployeeId(employeeId);
                    employeeRankingDataResult.setFormDataEnrollCount(0);
                    employeeRankingDataResult.setEmployeeSpreadCount(0);
                    employeeRankingDataResult.setPv(0);
                    employeeRankingDataResult.setForwardCount(0);
                }
                employeeRankingDataResult.setFormDataEnrollCount(enrollCount == null ? 0 : enrollCount);
                if (employeeMsgMap != null && employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()) != null){
                    employeeRankingDataResult.setEmployeeName(employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getName());
                    String avatar = employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getProfileImage();
                    employeeRankingDataResult.setAvatar(avatar);
                    if(avatar != null && (avatar.startsWith("N") || avatar.startsWith("A_"))){
                        employeeRankingDataResult.setAvatar(fileV2Manager.getUrlByPath(avatar, ea, false));
                    }
                }
                if (statisticMap.containsKey(employeeRankingDataResult.getEmployeeId())) {
                    com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult enterpriseSpreadUserMarketingListResult = statisticMap.get(employeeRankingDataResult.getEmployeeId());
                    if (enterpriseSpreadUserMarketingListResult != null) {
                        employeeRankingDataResult.setForwardCount(enterpriseSpreadUserMarketingListResult.getForwardCount());
                        employeeRankingDataResult.setPv(enterpriseSpreadUserMarketingListResult.getLookUpCount());
                    }
                }
                employeeRankingDataResults.add(employeeRankingDataResult);
            }
            //默认按照推广次数排序
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getEmployeeSpreadCount() == null ? 0 : data1.getEmployeeSpreadCount();
                Integer employeeSpreadCount2 = data2.getEmployeeSpreadCount() == null ? 0 : data2.getEmployeeSpreadCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else {
            //当请分页查询数据（包括已推广和未推广）
            List<Integer> pageActivityEmployeeIds = marketingActivityEmployeeStatisticDao.queryPageByMarketingActivityIdsAndEmployeeds(ea, allEmployeeSpreadMarketingActivityIds, CollectionUtils.isNotEmpty(visitUserRange) ? visitUserRange : null, page);
            int totalCount = marketingActivityEmployeeStatisticDao.queryPageTotalCountByMarketingActivityIdsAndEmployeeds(ea, allEmployeeSpreadMarketingActivityIds, CollectionUtils.isNotEmpty(visitUserRange) ? visitUserRange : null);
            if(!org.apache.commons.collections4.CollectionUtils.isEmpty(pageActivityEmployeeIds)){
                targetEmployeeIds = Lists.newArrayList(pageActivityEmployeeIds);
            }
            if (CollectionUtils.isEmpty(targetEmployeeIds)){
                log.info("StatisticServiceImpl.getSubordinateEmployeesByUserAndDepartment return null ea:{} userId:{} arg:{}", ea, fsUserId, arg);
                employeeRankingDataResultPageResult.setData(Lists.newArrayList());
                return Result.newSuccess(employeeRankingDataResultPageResult);
            }
            //根据条件进行mongodb查询访问数和转发数
            Map<Integer, com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult> statisticMap = getUserMarketingListResultMap(ea, startDate, endDate, allEmployeeSpreadMarketingActivityIds, targetEmployeeIds);
            Map<Integer, EmployeeRankingDataResult> employeeRankingDataMap = new HashMap<>();
            Map<Integer, EmployeeRankingDataResult> employeeCustomizeDataMap = new HashMap<>();
            List<EmployeeRankingDataResult> employeeRankingDataLists = marketingActivityEmployeeDayStatisticDao.sumByMarketingActivityIdsAndDayRangeAndUserIds(ea, allEmployeeSpreadMarketingActivityIds,  startDate, endDate, targetEmployeeIds);
            employeeRankingDataResultPageResult.setTotalCount(totalCount);
            if (CollectionUtils.isNotEmpty(employeeRankingDataLists)){
                employeeRankingDataMap = employeeRankingDataLists.stream().collect(Collectors.toMap(EmployeeRankingDataResult::getEmployeeId, Function.identity(), (k1, k2)->k2));
            }
            List<EmployeeRankingDataResult> customizeFormData = customizeFormDataUserDAO.groupCountByMarketingActivityIdsAndDateRangeAndUserIds(ea, allEmployeeSpreadMarketingActivityIds, startDate, endDate, targetEmployeeIds);
            if (CollectionUtils.isNotEmpty(customizeFormData)){
                employeeCustomizeDataMap = customizeFormData.stream().collect(Collectors.toMap(EmployeeRankingDataResult::getEmployeeId, Function.identity(), (k1, k2)->k2));
            }

            //提交表单
            for (Integer employeeId : targetEmployeeIds) {
                EmployeeRankingDataResult employeeRankingDataResult = employeeRankingDataMap.get(employeeId);
                EmployeeRankingDataResult employeeCustomizeDataResult = employeeCustomizeDataMap.get(employeeId);
                if (employeeRankingDataResult == null) {
                    employeeRankingDataResult = new EmployeeRankingDataResult();
                    employeeRankingDataResult.setEmployeeId(employeeId);
                    employeeRankingDataResult.setFormDataEnrollCount(0);
                    employeeRankingDataResult.setEmployeeSpreadCount(0);
                    employeeRankingDataResult.setPv(0);
                    employeeRankingDataResult.setForwardCount(0);
                }
                if (employeeCustomizeDataResult != null) {
                    employeeRankingDataResult.setFormDataEnrollCount(employeeCustomizeDataResult.getFormDataEnrollCount() == null ? 0 : employeeCustomizeDataResult.getFormDataEnrollCount());
                }else {
                    employeeRankingDataResult.setFormDataEnrollCount(0);
                }
                if (statisticMap.containsKey(employeeRankingDataResult.getEmployeeId())) {
                    com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult enterpriseSpreadUserMarketingListResult = statisticMap.get(employeeRankingDataResult.getEmployeeId());
                    if (enterpriseSpreadUserMarketingListResult != null) {
                        employeeRankingDataResult.setForwardCount(enterpriseSpreadUserMarketingListResult.getForwardCount());
                        employeeRankingDataResult.setPv(enterpriseSpreadUserMarketingListResult.getLookUpCount());
                    }
                }
                employeeRankingDataResults.add(employeeRankingDataResult);
            }
            Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, targetEmployeeIds, true);
            for (EmployeeRankingDataResult employeeRankingDataResult : employeeRankingDataResults) {
                if (employeeMsgMap != null && employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()) != null){
                    employeeRankingDataResult.setEmployeeName(employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getName());
                    String avatar = employeeMsgMap.get(employeeRankingDataResult.getEmployeeId()).getProfileImage();
                    employeeRankingDataResult.setAvatar(avatar);
                    if(avatar != null && (avatar.startsWith("N") || avatar.startsWith("A_"))){
                        employeeRankingDataResult.setAvatar(fileV2Manager.getUrlByPath(avatar, ea, false));
                    }
                }
            }
        }

        int searchType = arg.getSearchType() == null ? 0:arg.getSearchType();
        if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_PROMOTION.getValue()) {
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getEmployeeSpreadCount() == null ? 0 : data1.getEmployeeSpreadCount();
                Integer employeeSpreadCount2 = data2.getEmployeeSpreadCount() == null ? 0 : data2.getEmployeeSpreadCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_FORWARDS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getForwardCount() == null ? 0 : data1.getForwardCount();
                Integer employeeSpreadCount2 = data2.getForwardCount() == null ? 0 : data2.getForwardCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_VISITS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getPv() == null ? 0 : data1.getPv();
                Integer employeeSpreadCount2 = data2.getPv() == null ? 0 : data2.getPv();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getFormDataEnrollCount() == null ? 0 : data1.getFormDataEnrollCount();
                Integer employeeSpreadCount2 = data2.getFormDataEnrollCount() == null ? 0 : data2.getFormDataEnrollCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }

        employeeRankingDataResultPageResult.setData(employeeRankingDataResults);
        return Result.newSuccess(employeeRankingDataResultPageResult);
    }

    private Map<Integer, com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult> getUserMarketingListResultMap(String ea, Date startDate, Date endDate, List<String> allEmployeeSpreadMarketingActivityIds, List<Integer> targetEmployeeIds) {
        Map<Integer,com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult> statisticMap = Maps.newHashMap();
        BatchObjectActionStatisticsArg statisticArg = new BatchObjectActionStatisticsArg();
        statisticArg.setEa(ea);
        statisticArg.setActionTypeList(Lists.newArrayList(NewActionTypeEnum.LOOK_UP.getActionType(),NewActionTypeEnum.FORWARD.getActionType()));
        statisticArg.setStartTime(startDate);
        statisticArg.setEndTime(endDate);
        statisticArg.setSpreadFsUserIdList(targetEmployeeIds);
        statisticArg.setMarketingActivityIdList(allEmployeeSpreadMarketingActivityIds);
        com.facishare.marketing.statistic.common.result.Result<List<com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult>> employeeSpreadStatisticResult = userMarketingStatisticService.getEmployeeSpreadStatisticList(statisticArg);
        if (employeeSpreadStatisticResult != null && CollectionUtils.isNotEmpty(employeeSpreadStatisticResult.getData())) {
            Map<Integer, List<com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult>> collectMap = employeeSpreadStatisticResult.getData().stream()
                    .collect(groupingBy(com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult::getSpreadFsUserId));
            collectMap.forEach((key, value) -> {
                value.forEach(result -> {
                    statisticMap.merge(key, result, (r1, r2) -> {
                        r1.setForwardCount(r1.getForwardCount() + r2.getForwardCount());
                        r1.setLookUpCount(r1.getLookUpCount() + r2.getLookUpCount());
                        return r1;
                    });
                });
            });
        }
        return statisticMap;
    }

    @Override
    public Result<com.facishare.marketing.api.result.PageResult<EmployeeRankingDataResult>> getEmployeeSpreadRankWithSubordinateForPartner(String upstreamEa, String outTenantId, String outUserId,GetAllEmployeeSpreadEmployeeRankingDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));

        com.facishare.marketing.api.result.PageResult<EmployeeRankingDataResult> employeeRankingDataResultPageResult = new com.facishare.marketing.api.result.PageResult<>();
        employeeRankingDataResultPageResult.setTotalCount(0);
        Page page = new Page(arg.getPageNum(), arg.getPageSize());
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(upstreamEa, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        //所有活动id
        List<String> allEmployeeSpreadMarketingActivityIds = spreadTaskDAO.getPartnerMarketingActivityIds(upstreamEa, outTenantId, outTenantId);
        if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)){
            employeeRankingDataResultPageResult.setData(Lists.newArrayList());
            return Result.newSuccess(employeeRankingDataResultPageResult);
        }
        List<String> outTenantIdList = new ArrayList<>();
        outTenantIdList.add(outTenantId);
        //获取该outtenant下所有对接人
        List<Integer> dockUserId = fsAddressBookManager.getDockUserId(outTenantIdList, null, upstreamEa, -1000, new HashMap<>());
        Map<Integer,String> outUserToNameMap = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(upstreamEa,dockUserId);

        List<EmployeeRankingDataResult> employeeRankingDataResults = marketingActivityEmployeeDayStatisticDao.sumByMarketingActivityIdsAndUserIdsForPartner(outTenantId,upstreamEa, allEmployeeSpreadMarketingActivityIds,dockUserId, new Date(arg.getStartDate()), new Date(arg.getEndDate()), page);
        employeeRankingDataResultPageResult.setTotalCount(dockUserId.size());
        List<Integer> currentEmployeeIds = employeeRankingDataResults.stream().map(EmployeeRankingDataResult -> EmployeeRankingDataResult.getEmployeeId()).collect(Collectors.toList());
        for (EmployeeRankingDataResult employeeRankingDataResult : employeeRankingDataResults) {
            employeeRankingDataResult.setEmployeeName(outUserToNameMap.get(employeeRankingDataResult.getEmployeeId()));
        }
        int searchType = arg.getSearchType() == null ? 0:arg.getSearchType();
        if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_PROMOTION.getValue()) {
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getEmployeeSpreadCount() == null ? 0 : data1.getEmployeeSpreadCount();
                Integer employeeSpreadCount2 = data2.getEmployeeSpreadCount() == null ? 0 : data2.getEmployeeSpreadCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_FORWARDS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getForwardCount() == null ? 0 : data1.getForwardCount();
                Integer employeeSpreadCount2 = data2.getForwardCount() == null ? 0 : data2.getForwardCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_VISITS.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getPv() == null ? 0 : data1.getPv();
                Integer employeeSpreadCount2 = data2.getPv() == null ? 0 : data2.getPv();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }else if (searchType == EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue()){
            Collections.sort(employeeRankingDataResults, (data1, data2) -> {
                Integer employeeSpreadCount1 = data1.getFormDataEnrollCount() == null ? 0 : data1.getFormDataEnrollCount();
                Integer employeeSpreadCount2 = data2.getFormDataEnrollCount() == null ? 0 : data2.getFormDataEnrollCount();
                return employeeSpreadCount2.compareTo(employeeSpreadCount1);
            });
        }

        employeeRankingDataResultPageResult.setData(employeeRankingDataResults);
        return Result.newSuccess(employeeRankingDataResultPageResult);
    }

    /**
     * 钉钉小程序获取部门下所有员工userId
     * @author: mingqiao
     * @date: 2021/11/2 20:25
     * @param ea:
     * @param fsUserId:
     * @param departmentRange:
     * @return: java.util.List<java.lang.Integer>
     */
    public List<Integer> getDingEmployeeByDepartment(String ea, Integer fsUserId, List<Integer> departmentRange) {
        QueryDingMiniAppStaffArg arg = new QueryDingMiniAppStaffArg();
        arg.setFsEa(ea);
        Result<List<DingMiniAppStaffResult>> staffResult = dingMiniAppStaffService.queryDingMiniAppStaff(arg);
        if (!staffResult.isSuccess() || CollectionUtils.isEmpty(staffResult.getData())){
            return new ArrayList<>();
        }

        List<DingMiniAppStaffResult> staffResultData = staffResult.getData();
        List<String> userIds = staffResultData.stream().map(DingMiniAppStaffResult::getUserid).collect(Collectors.toList());
        List<QywxVirtualFsUserEntity> fsUserEntityList = virtualUserManager.getVirtualUserByEaAndQyIds(ea, userIds);
        if (CollectionUtils.isEmpty(fsUserEntityList)){
            return new ArrayList<>();
        }
        Set<Integer> users = fsUserEntityList.stream().map(QywxVirtualFsUserEntity::getUserId).collect(Collectors.toSet());
        users.add(fsUserId);

        return new ArrayList<>(users);
    }

    /**
     * 查询在指定员工列表和部门列表下，当前员工的下属员工列表，包含自己
     * @param ea
     * @param fsUserId
     * @param employeeRange
     * @param deparmentIds
     * @return
     */
    public List<Integer> getSubordinateEmployeesByUserAndDepartment(String ea, Integer fsUserId, List<Integer> employeeRange, List<Integer> deparmentIds){
        Set<Integer> targetEmployeeIdSet = new HashSet<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(employeeRange)){
            targetEmployeeIdSet.addAll(employeeRange);
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(deparmentIds)){
            List<Integer> userIdsByDeparments = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, deparmentIds);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userIdsByDeparments)) {
                targetEmployeeIdSet.addAll(userIdsByDeparments);
            }
        }

        GetAllSubordinateEmployeesDtoArg arg = new GetAllSubordinateEmployeesDtoArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setEmployeeId(fsUserId);
        arg.setRunStatus(RunStatus.ACTIVE);
        GetAllSubordinateEmployeesDtoResult allSubordinateEmployeesDtoResult = employeeProviderService.getAllSubordinateEmployees(arg);
        Set<Integer> employeeSet = null;
        if (allSubordinateEmployeesDtoResult != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(allSubordinateEmployeesDtoResult.getEmployeeDtos())){
            employeeSet = allSubordinateEmployeesDtoResult.getEmployeeDtos().stream().map(employeeDto -> employeeDto.getEmployeeId()).collect(Collectors.toSet());
        }
        if (employeeSet == null){
            employeeSet = new HashSet<>();
        }
        employeeSet.add(fsUserId);

        Set<Integer> subEmployee = new HashSet<>();
        if (CollectionUtils.isEmpty(targetEmployeeIdSet)) {
            subEmployee.addAll(employeeSet);
            return new ArrayList<>(subEmployee);
        }
        for (Integer employeeId : targetEmployeeIdSet){
            if (employeeSet.contains(employeeId)){
                subEmployee.add(employeeId);
            }
        }

        return new ArrayList<>(subEmployee);
    }
    
    @Override
    public Result<Void> recordEmployeeAction(RecordEmployeeActionArg arg) {
        Preconditions.checkArgument(arg != null);
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getEa()));
        Preconditions.checkArgument(arg.getFsUserId() != null);
        Preconditions.checkArgument("visit_page".equals(arg.getAction()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTargetObjectId()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getChannel()));
        DataPersistor.asyncLog("employee_action", arg.toMap());
        return Result.newSuccess();
    }
    
    @Override
    public Result<Void> recordVisitorAction(RecordVisitorActionArg arg) {
        Preconditions.checkArgument(arg != null);
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getUserId()));
        Preconditions.checkArgument("h5".equals(arg.getChannel()) || "mini_app".equals(arg.getChannel()));
        Preconditions.checkArgument("visit_page".equals(arg.getAction()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTargetObjectId()));
        Preconditions.checkArgument(arg.getPageLoadMills() != null);
        DataPersistor.asyncLog("visit_page_record", arg.toMap());
        return Result.newSuccess();
    }

    @Override
//    @Transactional
    public Result addStatisticsVisitRange(String ea, Integer userId, AddStatisticsVisitRangeArg arg) {
        if (CollectionUtils.isEmpty(arg.getUserIds())){
            return Result.newSuccess();
        }

        List<MarketingActivitySpreadStatisticsVisitRangeEntity> existUsers = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserIds(ea, arg.getUserIds());
        List<Integer> addUserIds = null;
        if (CollectionUtils.isNotEmpty(existUsers)){
            List<Integer> updateUserList = Lists.newArrayList();
            Set<Integer> userIdSet = new HashSet<>();
            Set<Integer> departmentIdSet = new HashSet<>();
            for (MarketingActivitySpreadStatisticsVisitRangeEntity entity : existUsers){
                updateUserList.add(entity.getUserId());
                AddStatisticsVisitRangeArg.StatisticsVisitRange entityRange = GsonUtil.getGson().fromJson(entity.getVisitRange(), AddStatisticsVisitRangeArg.StatisticsVisitRange.class);
                userIdSet.clear();
                departmentIdSet.clear();
                if(CollectionUtils.isNotEmpty(arg.getStatisticsVisitRange().getUserRange())){
                    if (CollectionUtils.isNotEmpty(entityRange.getUserRange() )) {
                        userIdSet.addAll(entityRange.getUserRange());
                        userIdSet.addAll(arg.getStatisticsVisitRange().getUserRange());
                    }
                }
                if (CollectionUtils.isNotEmpty(arg.getStatisticsVisitRange().getDepartmentRange())){
                    if (CollectionUtils.isNotEmpty(entityRange.getDepartmentRange())){
                        departmentIdSet.addAll(entityRange.getDepartmentRange());
                    }
                    departmentIdSet.addAll(arg.getStatisticsVisitRange().getDepartmentRange());
                }

                AddStatisticsVisitRangeArg.StatisticsVisitRange statisticsVisitRange = new AddStatisticsVisitRangeArg.StatisticsVisitRange();
                statisticsVisitRange.setDepartmentRange(Lists.newArrayList(departmentIdSet));
                statisticsVisitRange.setUserRange(Lists.newArrayList(userIdSet));
                entity.setVisitRange(GsonUtil.getGson().toJson(statisticsVisitRange));
            }
            marketingActivitySpreadStatisticsVisitRangeDAO.batchUpdate(ea, existUsers);
            addUserIds = arg.getUserIds().stream().filter(user -> !updateUserList.contains(user)).collect(Collectors.toList());
        }else {
            addUserIds = arg.getUserIds();
        }

        if (CollectionUtils.isNotEmpty(addUserIds)){
            String visitRangeJson = GsonUtil.toJson(arg.getStatisticsVisitRange());
            List<MarketingActivitySpreadStatisticsVisitRangeEntity> addEntityList = Lists.newArrayList();
            Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, addUserIds, true);
            if (employeeMsgMap == null){
                employeeMsgMap = new HashMap<>(0);
            }

            if (CollectionUtils.isNotEmpty(addUserIds)){
                for (Integer user : addUserIds){
                    MarketingActivitySpreadStatisticsVisitRangeEntity entity = new MarketingActivitySpreadStatisticsVisitRangeEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(ea);
                    entity.setUserId(user);
                    entity.setVisitRange(visitRangeJson);
                    entity.setUserName(employeeMsgMap.get(user) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1675) : employeeMsgMap.get(user).getName());
                    addEntityList.add(entity);
                }
                marketingActivitySpreadStatisticsVisitRangeDAO.batchInsert(addEntityList);
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result deleteStatisticsVisitRange(String ea, Integer userId, DeleteStatisticsVisitRangeArg arg) {
        MarketingActivitySpreadStatisticsVisitRangeEntity entity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, arg.getUserId());
        if (entity == null){
            return Result.newSuccess();
        }

        marketingActivitySpreadStatisticsVisitRangeDAO.deleteByUserId(ea, arg.getUserId());
        return Result.newSuccess();
    }

    @Override
    public Result<QueryStatisticsVisitRangeResult> queryEmployeeStatisticsVisitRange(String ea, Integer userId, QueryStatisticsVisitRangeArg arg) {
        QueryStatisticsVisitRangeResult queryStatisticsVisitRangeResult = null;
        Optional<QueryStatisticsVisitRangeResult> visitRangeResultOptional = queryStatisticsVisitRangeByUser(ea, arg.getUserId());
        if (visitRangeResultOptional.isPresent()){
            queryStatisticsVisitRangeResult = visitRangeResultOptional.get();
        }
        return Result.newSuccess(queryStatisticsVisitRangeResult);
    }

    @Override
    public Result<List<QueryStatisticsVisitRangeResult>> queryEnterpriseStatisticsVisitRange(String ea, Integer userId, QueryEnterpriseStatisticsVisitRangeArg arg) {
        List<QueryStatisticsVisitRangeResult> queryStatisticsVisitRangeResultList = queryStatisticsVisitRangeByEa(ea, arg.getEmployeeName());
        return Result.newSuccess(queryStatisticsVisitRangeResultList);
    }

    @Override
    public Result updateEmployeeStatisticsVisitRange(String ea, Integer userId, AddStatisticsVisitRangeArg arg) {
        MarketingActivitySpreadStatisticsVisitRangeEntity entity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, arg.getUserIds().get(0));
        if (entity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }

        String range = GsonUtil.getGson().toJson(arg.getStatisticsVisitRange());
        entity.setVisitRange(range);
        marketingActivitySpreadStatisticsVisitRangeDAO.batchUpdate(ea, Lists.newArrayList(entity));
        return Result.newSuccess();
    }

    private Optional<QueryStatisticsVisitRangeResult> queryStatisticsVisitRangeByUser(String ea, Integer userId){
        MarketingActivitySpreadStatisticsVisitRangeEntity entity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, userId);
        if (entity == null){
            return Optional.empty();
        }
        QueryStatisticsVisitRangeResult statisticsVisitRangeResult = new QueryStatisticsVisitRangeResult();
        List<Integer> userIds = Lists.newArrayList(userId);
        AddStatisticsVisitRangeArg.StatisticsVisitRange statisticsVisitRange = GsonUtil.fromJson(entity.getVisitRange(), AddStatisticsVisitRangeArg.StatisticsVisitRange.class);
        if (CollectionUtils.isNotEmpty(statisticsVisitRange.getUserRange())){
            userIds.addAll(statisticsVisitRange.getUserRange());
        }

        List<QueryStatisticsVisitRangeResult.UserInfo> userRange = Lists.newArrayList();
        List<QueryStatisticsVisitRangeResult.DepartmentInfo> departmentRange = Lists.newArrayList();
        statisticsVisitRangeResult.setUserRange(userRange);
        statisticsVisitRangeResult.setDepartmentRange(departmentRange);
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);
        if (employeeMsgMap == null){
            employeeMsgMap = new HashMap<>(0);
        }

        statisticsVisitRangeResult.setUserId(userId);
        statisticsVisitRangeResult.setUserName(employeeMsgMap.get(userId) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1675) : employeeMsgMap.get(userId).getName());
        if (CollectionUtils.isNotEmpty(statisticsVisitRange.getUserRange())){
            for (Integer rangeUserId : statisticsVisitRange.getUserRange()){
                QueryStatisticsVisitRangeResult.UserInfo userInfo = new QueryStatisticsVisitRangeResult.UserInfo();
                userInfo.setUserId(rangeUserId);
                userInfo.setUserName(employeeMsgMap.get(userId) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1675) : employeeMsgMap.get(userId).getName());
                userRange.add(userInfo);
            }
        }

        if (CollectionUtils.isNotEmpty(statisticsVisitRange.getDepartmentRange())){
            BatchGetDepartmentDtoArg departmentDtoArg = new BatchGetDepartmentDtoArg();
            departmentDtoArg.setDepartmentIds(statisticsVisitRange.getDepartmentRange());
            BatchGetDepartmentDtoResult departmentDtoResult = departmentProviderService.batchGetDepartmentDto(departmentDtoArg);

            Map<Integer, String> departmentMap = new HashMap<>(0);
            if (departmentDtoResult != null && CollectionUtils.isNotEmpty(departmentDtoResult.getDepartments())){
                departmentMap = departmentDtoResult.getDepartments().stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getName, (v1, v2)->v2));

            }
            for (Integer departmentId : statisticsVisitRange.getDepartmentRange()){
                QueryStatisticsVisitRangeResult.DepartmentInfo departmentInfo = new QueryStatisticsVisitRangeResult.DepartmentInfo();
                departmentInfo.setDepartmentId(departmentId);
                departmentInfo.setDepartmentName(departmentMap.get(departmentId) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1770) : departmentMap.get(departmentId));
                departmentRange.add(departmentInfo);
            }
        }

        return Optional.ofNullable(statisticsVisitRangeResult);
    }

    private List<QueryStatisticsVisitRangeResult> queryStatisticsVisitRangeByEa(String ea, String keyword){
        List<QueryStatisticsVisitRangeResult> visitRangeResultListResult = Lists.newArrayList();
        List<MarketingActivitySpreadStatisticsVisitRangeEntity> visitRangeEntityList = marketingActivitySpreadStatisticsVisitRangeDAO.getByEa(ea, keyword);
        if (CollectionUtils.isEmpty(visitRangeEntityList)){
            return visitRangeResultListResult;
        }

        Set<Integer> userIdsSet = new HashSet<>();
        Set<Integer> departmentIdsSet = new HashSet<>();
        for (MarketingActivitySpreadStatisticsVisitRangeEntity entity : visitRangeEntityList){
            AddStatisticsVisitRangeArg.StatisticsVisitRange statisticsVisitRange = GsonUtil.fromJson(entity.getVisitRange(), AddStatisticsVisitRangeArg.StatisticsVisitRange.class);
            if (CollectionUtils.isNotEmpty(statisticsVisitRange.getUserRange())){
                userIdsSet.addAll(statisticsVisitRange.getUserRange());
            }
            if (CollectionUtils.isNotEmpty(statisticsVisitRange.getDepartmentRange())){
                departmentIdsSet.addAll(statisticsVisitRange.getDepartmentRange());
            }
            userIdsSet.add(entity.getUserId());
        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = null;
        Map<Integer, String> departmentNameMap = null;
        if (CollectionUtils.isNotEmpty(userIdsSet)){
            employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, new ArrayList<>(userIdsSet), true);
            if (employeeMsgMap == null){
                employeeMsgMap = new HashMap<>(0);
            }
        }
        if (CollectionUtils.isNotEmpty(departmentIdsSet)){
            BatchGetDepartmentDtoArg departmentDtoArg = new BatchGetDepartmentDtoArg();
            departmentDtoArg.setDepartmentIds(new ArrayList<>(departmentIdsSet));
            departmentDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            BatchGetDepartmentDtoResult departmentDtoResult = departmentProviderService.batchGetDepartmentDto(departmentDtoArg);
            if (departmentDtoResult != null && CollectionUtils.isNotEmpty(departmentDtoResult.getDepartments())){
                departmentNameMap = departmentDtoResult.getDepartments().stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getName, (v1, v2)->v2));
            }
            if (departmentNameMap == null){
                departmentNameMap = new HashMap<>(0);
            }
        }
        for (MarketingActivitySpreadStatisticsVisitRangeEntity entity : visitRangeEntityList){
            QueryStatisticsVisitRangeResult visitRangeResult = new QueryStatisticsVisitRangeResult();
            visitRangeResultListResult.add(visitRangeResult);
            List<QueryStatisticsVisitRangeResult.UserInfo> userInfoList = Lists.newArrayList();
            List<QueryStatisticsVisitRangeResult.DepartmentInfo> departmentInfoList = Lists.newArrayList();
            visitRangeResult.setUserRange(userInfoList);
            visitRangeResult.setDepartmentRange(departmentInfoList);
            visitRangeResult.setUserId(entity.getUserId());
            visitRangeResult.setUserName(employeeMsgMap.get(entity.getUserId()) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1675) : employeeMsgMap.get(entity.getUserId()).getName());
            visitRangeResult.setDepartmentName(employeeMsgMap.get(entity.getUserId()) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1770) : employeeMsgMap.get(entity.getUserId()).getDepartment());
            AddStatisticsVisitRangeArg.StatisticsVisitRange statisticsVisitRange = GsonUtil.fromJson(entity.getVisitRange(), AddStatisticsVisitRangeArg.StatisticsVisitRange.class);
            if (CollectionUtils.isNotEmpty(statisticsVisitRange.getUserRange())){
                for (Integer userId : statisticsVisitRange.getUserRange()){
                    QueryStatisticsVisitRangeResult.UserInfo userInfo = new QueryStatisticsVisitRangeResult.UserInfo();
                    userInfo.setUserId(userId);
                    userInfo.setUserName(employeeMsgMap.get(userId) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1675) : employeeMsgMap.get(userId).getName());
                    userInfoList.add(userInfo);
                }
            }
            if (CollectionUtils.isNotEmpty(statisticsVisitRange.getDepartmentRange())){
                for (Integer departmentId : statisticsVisitRange.getDepartmentRange()){
                    QueryStatisticsVisitRangeResult.DepartmentInfo departmentInfo = new QueryStatisticsVisitRangeResult.DepartmentInfo();
                    departmentInfo.setDepartmentId(departmentId);
                    departmentInfo.setDepartmentName(departmentNameMap.get(departmentId) == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1770) : departmentNameMap.get(departmentId));
                    departmentInfoList.add(departmentInfo);
                }
            }
        }

        return visitRangeResultListResult;
    }

    @Override
    public Result marketingUserSpreadOverviewAndTrend(String ea, Integer fsUserId, MarketingUserSpreadStatisticsArg arg) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateBegin = sdf.format(new Date(arg.getStartDate()));
        String dateEnd = sdf.format(new Date(arg.getEndDate()));
        List<String> dates = DateUtil.listDateBetweenStartTimeAndEndTime2String(new Date(arg.getStartDate()), new Date(arg.getEndDate()), "yyyy-MM-dd");
        MarketingUserSpreadStatisticsResult data = new MarketingUserSpreadStatisticsResult();
        List<MarketingUserSpreadStatisticsResult> marketingUserSpreadStatisticsResults = statisticDAO.querySpreadStatistics(ea, dateBegin, dateEnd, arg.getObjectId());
        int forwardCount = 0;
        int forwardUserCount = 0;
        int lookUpCount = 0;
        int lookUpUserCount = 0;
        int submitFormCount = 0;
        int clueNum = 0;
        int initiativeForwardCount = 0;
        List<DayTrendData> forwardCountDayTrendData = new ArrayList<>();
        List<DayTrendData> forwardUserCountDayTrendData = new ArrayList<>();
        List<DayTrendData> lookUpCountDayTrendData = new ArrayList<>();
        List<DayTrendData> lookUpUserCountDayTrendData = new ArrayList<>();
        List<DayTrendData> submitFormCountDayTrendData = new ArrayList<>();
        List<DayTrendData> clueNumDayTrendData = new ArrayList<>();
        List<DayTrendData> initiativeForwardCountDayTrendData = new ArrayList<>();
        if (marketingUserSpreadStatisticsResults != null) {
            for (MarketingUserSpreadStatisticsResult item : marketingUserSpreadStatisticsResults) {
                forwardCount += item.getForwardCount();
                forwardUserCount += item.getForwardUserCount();
                lookUpCount += item.getLookUpCount();
                lookUpUserCount += item.getLookUpUserCount();
                submitFormCount += item.getSubmitFormCount();
                clueNum += item.getClueNum();
                initiativeForwardCount += item.getInitiativeForwardCount();
            }
            for (String date : dates) {
                MarketingUserSpreadStatisticsResult item = marketingUserSpreadStatisticsResults.stream()
                        .filter(e -> date.equals(e.getDate())).findFirst().orElse(null);
                forwardCountDayTrendData.add(new DayTrendData(date, item == null ? 0 : item.getForwardCount()));
                forwardUserCountDayTrendData.add(new DayTrendData(date, item == null ? 0 : item.getForwardUserCount()));
                lookUpCountDayTrendData.add(new DayTrendData(date, item == null ? 0 : item.getLookUpCount()));
                lookUpUserCountDayTrendData.add(new DayTrendData(date, item == null ? 0 : item.getLookUpUserCount()));
                submitFormCountDayTrendData.add(new DayTrendData(date, item == null ? 0 : item.getSubmitFormCount()));
                clueNumDayTrendData.add(new DayTrendData(date, item == null ? 0 : item.getClueNum()));
                initiativeForwardCountDayTrendData.add(new DayTrendData(date, item == null ? 0 : item.getInitiativeForwardCount()));
            }
        }
        data.setForwardCount(forwardCount);
        data.setForwardUserCount(forwardUserCount);
        data.setLookUpCount(lookUpCount);
        data.setLookUpUserCount(lookUpUserCount);
        data.setSubmitFormCount(submitFormCount);
        data.setClueNum(clueNum);
        data.setInitiativeForwardCount(initiativeForwardCount);
        data.setForwardCountDayTrendData(forwardCountDayTrendData);
        data.setForwardUserCountDayTrendData(forwardUserCountDayTrendData);
        data.setLookUpCountDayTrendData(lookUpCountDayTrendData);
        data.setLookUpUserCountDayTrendData(lookUpUserCountDayTrendData);
        data.setSubmitFormCountDayTrendData(submitFormCountDayTrendData);
        data.setClueNumDayTrendData(clueNumDayTrendData);
        data.setInitiativeForwardCountDayTrendData(initiativeForwardCountDayTrendData);
        return Result.newSuccess(data);
    }

    @Override
    public Result marketingUserSpreadRanking(String ea, Integer fsUserId, MarketingUserSpreadStatisticsArg arg) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateBegin = sdf.format(new Date(arg.getStartDate()));
        String dateEnd = sdf.format(new Date(arg.getEndDate()));
        com.facishare.marketing.common.result.PageResult<MarketingUserSpreadStatisticsResult> pageResult = new com.facishare.marketing.common.result.PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        ArrayList<MarketingUserSpreadStatisticsResult> data = new ArrayList<>();
        pageResult.setResult(data);
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<MarketingUserSpreadStatisticsResult> marketingUserSpreadStatisticsResults = statisticDAO.querySpreadStatisticsList(ea, dateBegin, dateEnd, arg.getObjectId(), arg.getName(), arg.getHumpField(), arg.getSortOrderField(), page);
        pageResult.setTotalCount(page.getTotalNum());
        if (CollectionUtils.isNotEmpty(marketingUserSpreadStatisticsResults)) {
            List<String> uids = marketingUserSpreadStatisticsResults.stream().map(MarketingUserSpreadStatisticsResult::getUid).collect(Collectors.toList());
            List<String> userMarketingIds = marketingUserSpreadStatisticsResults.stream().map(MarketingUserSpreadStatisticsResult::getUserMarketingId).collect(Collectors.toList());
            // 查询员工信息
            List<String> empInfos = statisticDAO.queryEmpInfo(uids, ea);
            // 查询会员信息
            List<Map<String, String>> memberInfoMaps = statisticDAO.queryMemberInfo(ea, userMarketingIds);
            Map<String, String> memberInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(memberInfoMaps)) {
                memberInfoMaps.forEach(e -> memberInfoMap.put(e.get("id"), e.get("name")));
            }
            marketingUserSpreadStatisticsResults.forEach(e -> {
                e.setUserType(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_563));
                if (empInfos.contains(e.getUid())) {
                    e.setUserType(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285));
                } else if (memberInfoMap.containsKey(e.getUserMarketingId())) {
                    e.setUserType(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1851));
                    e.setUserName(memberInfoMap.get(e.getUserMarketingId()));
                }
            });
            data.addAll(marketingUserSpreadStatisticsResults);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result exportMarketingUserSpreadRanking(String ea, Integer fsUserId, MarketingUserSpreadStatisticsArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<com.facishare.marketing.common.result.PageResult<MarketingUserSpreadStatisticsResult>> marketingUserSpreadRanking = this.marketingUserSpreadRanking(ea, fsUserId, arg);
            if (!marketingUserSpreadRanking.isSuccess()) {
                log.warn("StatisticServiceImpl.exportMarketingUserSpreadRanking error");
                return;
            }
            String fileName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1960) + ".xlsx";
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, fileName);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = ImmutableList.of(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_708), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1442), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1966_2), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_96),  I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1966_4), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_1966_5),I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_95), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_564_8));
            List<List<Object>> dataList = marketingUserSpreadRanking.getData().getResult().stream().map(e -> {
                List<Object> row = new ArrayList<>(6);
                row.add(e.getNickName());
                row.add(e.getUserName());
                row.add(e.getUserType());
                row.add(e.getInitiativeForwardCount());
                row.add(e.getForwardCount());
                row.add(e.getForwardUserCount());
                row.add(e.getLookUpCount());
                row.add(e.getSubmitFormCount());
                return row;
            }).collect(Collectors.toList());
            ExcelUtil.fillContent(xssfSheet, titleList, dataList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, fileName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectUserMarketingStatisticsResult> getObjectMarketingActivityStatistics(ObjectUserMarketingStatisticsArg arg) {
        List<MarketingActivityObjectRelationEntity> relationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
        if (CollectionUtils.isEmpty(relationEntityList)) {
            return Result.newSuccess();
        }
        ObjectActionStatisticsArg actionTypeStatisticsArg = new ObjectActionStatisticsArg();
        actionTypeStatisticsArg.setMarketingActivityId(arg.getMarketingActivityId());
        actionTypeStatisticsArg.setEa(arg.getEa());
        actionTypeStatisticsArg.setUserId(arg.getUserId());
        List<Integer> objectTypeList = relationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectType)
                .distinct().collect(Collectors.toList());
        List<Integer> actionTypeList = getActionType(objectTypeList, arg.getEa());

        ObjectUserMarketingStatisticsResult result = new ObjectUserMarketingStatisticsResult();
        result.setForwardCount(0);
        result.setForwardUserCount(0);
        result.setLookUpCount(0);
        result.setLookUpUserCount(0);
        result.setLookUpStatusCount(0);

        if (CollectionUtils.isEmpty(actionTypeList)) {
            return Result.newSuccess(result);
        }
        actionTypeStatisticsArg.setActionTypeList(actionTypeList);
        List<String> objectIdList = relationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectId)
                .collect(Collectors.toList());
        List<String> qrPosterIdsList = relationEntityList.stream().filter(o->o.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()).map(MarketingActivityObjectRelationEntity::getObjectId)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(qrPosterIdsList)){
            List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrPosterIdsList,arg.getEa());
            List<String> qrPostTargetId = qrPosterEntities.stream().filter(o->StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
            objectIdList.removeAll(qrPosterIdsList);
            objectIdList.addAll(qrPostTargetId);
        }
        if(CollectionUtils.isEmpty(objectIdList)){
            return Result.newSuccess();
        }
        actionTypeStatisticsArg.setObjectIdList(objectIdList);
        if(arg.getStartDate()!=null&&arg.getEndDate()!=null){
            actionTypeStatisticsArg.setStartTime(arg.getStartDate());
            actionTypeStatisticsArg.setEndTime(arg.getEndDate());
        }
        if(CollectionUtils.isNotEmpty(arg.getSpreadFsUserIdList())){
            actionTypeStatisticsArg.setSpreadFsUserIdList(arg.getSpreadFsUserIdList());
        }
        com.facishare.marketing.statistic.common.result.Result<List<ObjectActionStatisticsResult>> statisticsResult = userMarketingStatisticService.getObjectActionStatistics(actionTypeStatisticsArg);
        if (statisticsResult.getCode() != 0) {
            return Result.newError(statisticsResult.getCode(), statisticsResult.getMessage());
        }

        if (CollectionUtils.isNotEmpty(statisticsResult.getData())) {
            for (ObjectActionStatisticsResult data : statisticsResult.getData()) {
                int actionType =  getOldActionType(data.getActionType());
                if (data.getActionType() == NewActionTypeEnum.FORWARD.getActionType() || ActionTypeEnum.isForwardActon(actionType)) {
                    result.setForwardCount(result.getForwardCount() + data.getCount());
                    result.setForwardUserCount(result.getForwardUserCount() + data.getUserCount());
                } else if (data.getActionType() == NewActionTypeEnum.LOOK_UP.getActionType() || ActionTypeEnum.isLookUpAction(actionType)) {
                    result.setLookUpCount(result.getLookUpCount() + data.getCount());
                    result.setLookUpUserCount(result.getLookUpUserCount() + data.getUserCount());
                }
            }
        }
        int lookUpStatusCount = spreadTaskDAO.getLookUpStatusCount(arg.getEa(),arg.getMarketingActivityId());
        result.setLookUpStatusCount(lookUpStatusCount);
        return Result.newSuccess(result);
    }

    @Override
    public Result<ObjectUserMarketingStatisticsResult> getRadarObjectMarketingActivityStatistics(ObjectUserMarketingStatisticsArg arg) {

        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
        if(marketingActivityExternalConfigEntity==null){
            return Result.newSuccess();
        }
//        //最终查询的物料id
//        List<String> resultIds = Lists.newArrayList();
//        //海报的物料id
//        List<String> qrIds = Lists.newArrayList();
//
//        //全员推广的营销活动id
//        if(marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.NOTICE.getType()){
//            List<MarketingActivityObjectRelationEntity> objectRelationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityId(arg.getEa(), marketingActivityExternalConfigEntity.getMarketingActivityId());
//            for (MarketingActivityObjectRelationEntity entity : objectRelationEntityList) {
//                if(entity.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()){
//                    qrIds.add(entity.getObjectId());
//                }else {
//                    resultIds.add(entity.getObjectId());
//                }
//            }
//        }else {
//            if(marketingActivityExternalConfigEntity.getAssociateIdType()==ObjectTypeEnum.QR_POSTER.getType()){
//                qrIds.add(marketingActivityExternalConfigEntity.getAssociateId());
//            }else {
//                resultIds.add(marketingActivityExternalConfigEntity.getAssociateId());
//            }
//        }
//
//        if(CollectionUtils.isNotEmpty(qrIds)){
//            List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrIds);
//            List<String> qrPostTargetId = qrPosterEntities.stream().map(QRPosterEntity::getTargetId).collect(Collectors.toList());
//            resultIds.addAll(qrPostTargetId);
//        }

        //最终查询的物料id
        String resultId = arg.getObjectId();

        //海报的目标物料id
        if(arg.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()){
            QRPosterEntity qrPoster = qrPosterDAO.queryById(arg.getObjectId(),arg.getEa());
            if (StringUtils.isNotBlank(qrPoster.getTargetId())){
                resultId = qrPoster.getTargetId();
            }
        }

        ObjectActionStatisticsArg actionTypeStatisticsArg = new ObjectActionStatisticsArg();
        actionTypeStatisticsArg.setMarketingActivityId(arg.getMarketingActivityId());
        actionTypeStatisticsArg.setEa(arg.getEa());
        actionTypeStatisticsArg.setUserId(arg.getUserId());

        ObjectUserMarketingStatisticsResult result = new ObjectUserMarketingStatisticsResult();
        result.setForwardCount(0);
        result.setForwardUserCount(0);
        result.setLookUpCount(0);
        result.setLookUpUserCount(0);
        result.setLookUpStatusCount(0);

        actionTypeStatisticsArg.setActionTypeList(Lists.newArrayList(NewActionTypeEnum.LOOK_UP.getActionType(),NewActionTypeEnum.FORWARD.getActionType()));
        actionTypeStatisticsArg.setObjectIdList(Lists.newArrayList(resultId));
        if(arg.getStartDate()!=null&&arg.getEndDate()!=null){
            actionTypeStatisticsArg.setStartTime(arg.getStartDate());
            actionTypeStatisticsArg.setEndTime(arg.getEndDate());
        }
        if(CollectionUtils.isNotEmpty(arg.getSpreadFsUserIdList())){
            actionTypeStatisticsArg.setSpreadFsUserIdList(arg.getSpreadFsUserIdList());
        }
        com.facishare.marketing.statistic.common.result.Result<List<ObjectActionStatisticsResult>> statisticsResult = userMarketingStatisticService.getObjectActionStatistics(actionTypeStatisticsArg);
        if (statisticsResult.getCode() != 0) {
            return Result.newError(statisticsResult.getCode(), statisticsResult.getMessage());
        }

        if (CollectionUtils.isNotEmpty(statisticsResult.getData())) {
            for (ObjectActionStatisticsResult data : statisticsResult.getData()) {
                int actionType =  getOldActionType(data.getActionType());
                if (data.getActionType() == NewActionTypeEnum.FORWARD.getActionType() || ActionTypeEnum.isForwardActon(actionType)) {
                    result.setForwardCount(result.getForwardCount() + data.getCount());
                    result.setForwardUserCount(result.getForwardUserCount() + data.getUserCount());
                } else if (data.getActionType() == NewActionTypeEnum.LOOK_UP.getActionType() || ActionTypeEnum.isLookUpAction(actionType)) {
                    result.setLookUpCount(result.getLookUpCount() + data.getCount());
                    result.setLookUpUserCount(result.getLookUpUserCount() + data.getUserCount());
                }
            }
        }
        int lookUpStatusCount = spreadTaskDAO.getLookUpStatusCount(arg.getEa(),arg.getMarketingActivityId());
        result.setLookUpStatusCount(lookUpStatusCount);
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<ObjectUserMarketingStatisticsResult>> batchGetObjectMarketingActivityStatistics(BatchObjectUserMarketingStatisticsArg arg) {

        if (CollectionUtils.isEmpty(arg.getMarketingActivityIdList())) {
            return Result.newSuccess();
        }
        List<MarketingActivityObjectRelationEntity> objectRelationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityIdList(arg.getEa(), arg.getMarketingActivityIdList());
        if (CollectionUtils.isEmpty(objectRelationEntityList)) {
            return Result.newSuccess();
        }

        BatchObjectActionStatisticsArg actionTypeStatisticsArg = new BatchObjectActionStatisticsArg();
        actionTypeStatisticsArg.setMarketingActivityIdList(arg.getMarketingActivityIdList());
        actionTypeStatisticsArg.setEa(arg.getEa());
        actionTypeStatisticsArg.setUserId(arg.getUserId());
        List<Integer> objectTypeList = objectRelationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectType)
                .distinct().collect(Collectors.toList());
        List<Integer> actionTypeList = getActionType(objectTypeList, arg.getEa());

        if (CollectionUtils.isEmpty(actionTypeList)) {
            return Result.newSuccess();
        }
        if(CollectionUtils.isNotEmpty(arg.getSpreadFsUserIdList())){
            actionTypeStatisticsArg.setSpreadFsUserIdList(arg.getSpreadFsUserIdList());
        }
        List<ObjectUserMarketingStatisticsResult> resultList = Lists.newArrayList();
        actionTypeStatisticsArg.setActionTypeList(actionTypeList);
        List<String> objectIdList = objectRelationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectId)
                .collect(Collectors.toList());
        List<String> qrPosterIdsList = objectRelationEntityList.stream().filter(o->o.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()).map(MarketingActivityObjectRelationEntity::getObjectId)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(qrPosterIdsList)){
            List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrPosterIdsList,arg.getEa());
            List<String> qrPostTargetId = qrPosterEntities.stream().filter(o->StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
            objectIdList.removeAll(qrPosterIdsList);
            objectIdList.addAll(qrPostTargetId);
        }
        if(CollectionUtils.isEmpty(objectIdList)){
            return Result.newSuccess();
        }
        actionTypeStatisticsArg.setObjectIdList(objectIdList);
        com.facishare.marketing.statistic.common.result.Result<List<ObjectActionStatisticsResult>> statisticsResult = userMarketingStatisticService.batchGetObjectActionStatistics(actionTypeStatisticsArg);
        if (statisticsResult.getCode() != 0) {
            return Result.newError(statisticsResult.getCode(), statisticsResult.getMessage());
        }
        List<DataCount> lookupStatusCountList = spreadTaskDAO.getLookUpStatusCountGroupByMarketingActivityId(arg.getEa(),arg.getMarketingActivityIdList());
        Map<String, Integer> marketingActivityIdToLookUpStatusMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(lookupStatusCountList)) {
            lookupStatusCountList.forEach(e -> marketingActivityIdToLookUpStatusMap.put(e.getId(), e.getCount()));
        }
        if (CollectionUtils.isNotEmpty(statisticsResult.getData())) {
            Map<String, List<ObjectActionStatisticsResult>> marketingActivityIdToResultMap = statisticsResult.getData().stream().collect(groupingBy(ObjectActionStatisticsResult::getMarketingActivityId));
            marketingActivityIdToResultMap.forEach((marketingActivityId, results) -> {
                ObjectUserMarketingStatisticsResult result = new ObjectUserMarketingStatisticsResult();
                result.setMarketingActivityId(marketingActivityId);
                result.setLookUpStatusCount(marketingActivityIdToLookUpStatusMap.getOrDefault(marketingActivityId, 0));
                for (ObjectActionStatisticsResult data : results) {
                    int actionType =  getOldActionType(data.getActionType());
                    if (data.getActionType() == NewActionTypeEnum.FORWARD.getActionType() || ActionTypeEnum.isForwardActon(actionType)) {
                        result.setForwardCount(result.getForwardCount() + data.getCount());
                        result.setForwardUserCount(result.getForwardUserCount() + data.getUserCount());
                    } else if (data.getActionType() == NewActionTypeEnum.LOOK_UP.getActionType() || ActionTypeEnum.isLookUpAction(actionType)) {
                        result.setLookUpCount(result.getLookUpCount() + data.getCount());
                        result.setLookUpUserCount(result.getLookUpUserCount() + data.getUserCount());
                    }
                }
                resultList.add(result);
            });
        }
        return Result.newSuccess(resultList);
    }

    //雷达全员推广+个人推广
    @Override
    public Result<List<ObjectUserMarketingStatisticsResult>> batchGetRadarObjectMarketingActivityStatistics(BatchObjectUserMarketingStatisticsArg arg) {

        if (CollectionUtils.isEmpty(arg.getMarketingActivityIdList())) {
            return Result.newSuccess();
        }
        //最终查询的物料id
        List<String> resultIds = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(arg.getObjectIds())){
            resultIds = arg.getObjectIds();
        }else {
            List<MarketingActivityExternalConfigEntity> configEntities = marketingActivityExternalConfigDao.getByMarketingActivityIds(arg.getEa(), arg.getMarketingActivityIdList());

            //海报的物料id
            List<String> qrIds = Lists.newArrayList();

            //全员推广的营销活动id
            List<String> marketingIds = configEntities.stream().filter(o -> o.getAssociateIdType() == ObjectTypeEnum.NOTICE.getType()).map(o -> o.getMarketingActivityId()).collect(Collectors.toList());
            List<MarketingActivityObjectRelationEntity> objectRelationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityIdList(arg.getEa(), marketingIds);
            for (MarketingActivityObjectRelationEntity entity : objectRelationEntityList) {
                if(entity.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()){
                    qrIds.add(entity.getObjectId());
                }else {
                    resultIds.add(entity.getObjectId());
                }
            }
            //个人主动推广的营销活动
            List<MarketingActivityExternalConfigEntity> activeMarketingActivityList = configEntities.stream().filter(o -> o.getAssociateIdType() != ObjectTypeEnum.NOTICE.getType()).collect(Collectors.toList());

            for (MarketingActivityExternalConfigEntity entity : activeMarketingActivityList) {
                if(entity.getAssociateIdType()==ObjectTypeEnum.QR_POSTER.getType()){
                    qrIds.add(entity.getAssociateId());
                }else {
                    resultIds.add(entity.getAssociateId());
                }
            }

            if(CollectionUtils.isNotEmpty(qrIds)){
                List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrIds,arg.getEa());
                List<String> qrPostTargetId = qrPosterEntities.stream().filter(o->StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
                resultIds.addAll(qrPostTargetId);
            }
        }

        BatchObjectActionStatisticsArg actionTypeStatisticsArg = new BatchObjectActionStatisticsArg();
        actionTypeStatisticsArg.setMarketingActivityIdList(arg.getMarketingActivityIdList());
        actionTypeStatisticsArg.setEa(arg.getEa());
        actionTypeStatisticsArg.setUserId(arg.getUserId());
        if(StringUtils.isNotBlank(arg.getFromUserMarketingId())){
            actionTypeStatisticsArg.setFromUserMarketingId(arg.getFromUserMarketingId());
        }
        if(CollectionUtils.isNotEmpty(arg.getSpreadFsUserIdList())){
            actionTypeStatisticsArg.setSpreadFsUserIdList(arg.getSpreadFsUserIdList());
        }
        List<ObjectUserMarketingStatisticsResult> resultList = Lists.newArrayList();
        actionTypeStatisticsArg.setActionTypeList(Lists.newArrayList(NewActionTypeEnum.LOOK_UP.getActionType(),NewActionTypeEnum.FORWARD.getActionType()));

        actionTypeStatisticsArg.setObjectIdList(resultIds);
        com.facishare.marketing.statistic.common.result.Result<List<ObjectActionStatisticsResult>> statisticsResult = userMarketingStatisticService.batchGetObjectActionStatistics(actionTypeStatisticsArg);
        if (statisticsResult.getCode() != 0) {
            return Result.newError(statisticsResult.getCode(), statisticsResult.getMessage());
        }
        List<DataCount> lookupStatusCountList = spreadTaskDAO.getLookUpStatusCountGroupByMarketingActivityId(arg.getEa(),arg.getMarketingActivityIdList());
        Map<String, Integer> marketingActivityIdToLookUpStatusMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(lookupStatusCountList)) {
            lookupStatusCountList.forEach(e -> marketingActivityIdToLookUpStatusMap.put(e.getId(), e.getCount()));
        }
        if (CollectionUtils.isNotEmpty(statisticsResult.getData())) {
            Map<String, List<ObjectActionStatisticsResult>> marketingActivityIdToResultMap = statisticsResult.getData().stream().collect(groupingBy(ObjectActionStatisticsResult::getMarketingActivityId));
            marketingActivityIdToResultMap.forEach((marketingActivityId, results) -> {
                ObjectUserMarketingStatisticsResult result = new ObjectUserMarketingStatisticsResult();
                result.setMarketingActivityId(marketingActivityId);
                result.setLookUpStatusCount(marketingActivityIdToLookUpStatusMap.getOrDefault(marketingActivityId, 0));
                for (ObjectActionStatisticsResult data : results) {
                    int actionType =  getOldActionType(data.getActionType());
                    if (data.getActionType() == NewActionTypeEnum.FORWARD.getActionType() || ActionTypeEnum.isForwardActon(actionType)) {
                        result.setForwardCount(result.getForwardCount() + data.getCount());
                        result.setForwardUserCount(result.getForwardUserCount() + data.getUserCount());
                    } else if (data.getActionType() == NewActionTypeEnum.LOOK_UP.getActionType() || ActionTypeEnum.isLookUpAction(actionType)) {
                        result.setLookUpCount(result.getLookUpCount() + data.getCount());
                        result.setLookUpUserCount(result.getLookUpUserCount() + data.getUserCount());
                    }
                }
                resultList.add(result);
            });
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<List<ObjectUserMarketingStatisticsResult>> getObjectMarketingActivityStatisticsBySpreadUserIdList(ObjectUserMarketingStatisticsArg arg) {
        if (CollectionUtils.isEmpty(arg.getSpreadFsUserIdList())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<MarketingActivityObjectRelationEntity> relationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
        List<ObjectUserMarketingStatisticsResult> resultList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(relationEntityList)) {
            return Result.newSuccess(resultList);
        }
        ObjectActionStatisticsArg actionTypeStatisticsArg = new ObjectActionStatisticsArg();
        actionTypeStatisticsArg.setMarketingActivityId(arg.getMarketingActivityId());
        actionTypeStatisticsArg.setEa(arg.getEa());
        actionTypeStatisticsArg.setUserId(arg.getUserId());
        List<Integer> objectTypeList = relationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectType)
                .distinct().collect(Collectors.toList());
        List<Integer> actionTypeList = getActionType(objectTypeList, arg.getEa());
        actionTypeStatisticsArg.setActionTypeList(actionTypeList);
        actionTypeStatisticsArg.setSpreadFsUserIdList(arg.getSpreadFsUserIdList());
        List<String> objectIdList = relationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectId)
                .collect(Collectors.toList());
        //海报转海报下的物料
        List<String> qrPosterIdsList = relationEntityList.stream().filter(o->o.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()).map(MarketingActivityObjectRelationEntity::getObjectId)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(qrPosterIdsList)){
            List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrPosterIdsList,arg.getEa());
            List<String> qrPostTargetId = qrPosterEntities.stream().filter(o->StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
            objectIdList.removeAll(qrPosterIdsList);
            objectIdList.addAll(qrPostTargetId);
        }
        actionTypeStatisticsArg.setObjectIdList(objectIdList);
        com.facishare.marketing.statistic.common.result.Result<List<ObjectActionStatisticsResult>> statisticsResult = userMarketingStatisticService.getObjectActionStatistics(actionTypeStatisticsArg);
        if (statisticsResult.getCode() != 0) {
            return Result.newError(statisticsResult.getCode(), statisticsResult.getMessage());
        }
        Map<Integer, ObjectUserMarketingStatisticsResult> userIdToStatisticsMap = Maps.newHashMap();
        for (ObjectActionStatisticsResult actionStatisticsResult : statisticsResult.getData()) {
            ObjectUserMarketingStatisticsResult result = userIdToStatisticsMap.get(actionStatisticsResult.getSpreadFsUserId());
            if (result == null) {
                result = new ObjectUserMarketingStatisticsResult();
                result.setSpreadUserId(actionStatisticsResult.getSpreadFsUserId());
                result.setForwardCount(0);
                result.setForwardUserCount(0);
                result.setLookUpCount(0);
                result.setLookUpUserCount(0);
                userIdToStatisticsMap.put(actionStatisticsResult.getSpreadFsUserId(), result);
                resultList.add(result);
            }
            int actionType =  getOldActionType(actionStatisticsResult.getActionType());
            if (actionStatisticsResult.getActionType() == NewActionTypeEnum.FORWARD.getActionType() || ActionTypeEnum.isForwardActon(actionType)) {
                result.setForwardCount(result.getForwardCount() + actionStatisticsResult.getCount());
                result.setForwardUserCount(result.getForwardUserCount() + actionStatisticsResult.getUserCount());
            } else if (actionStatisticsResult.getActionType() == NewActionTypeEnum.LOOK_UP.getActionType() || ActionTypeEnum.isLookUpAction(actionType)) {
                result.setLookUpCount(result.getLookUpCount() + actionStatisticsResult.getCount());
                result.setLookUpUserCount(result.getLookUpUserCount() + actionStatisticsResult.getUserCount());
            }

        }
        return Result.newSuccess(resultList);
    }

    private List<Integer> getActionType(List<Integer> objectTypeList, String ea) {
        if (isRemoveToMongo(ea)) {
            // 升级到mongo后，actionType重新定义了
            return Lists.newArrayList(NewActionTypeEnum.FORWARD.getActionType(), NewActionTypeEnum.LOOK_UP.getActionType());
        }
        List<Integer> actionTypeList = Lists.newArrayList();
        // 只要转发和查看的
        for (Integer objectType : objectTypeList) {
            ObjectTypeEnum objectTypeEnum = ObjectTypeEnum.getByType(objectType);
            if (objectTypeEnum == null) {
                continue;
            }
            switch (objectTypeEnum) {
                case HEXAGON_SITE:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_HEXAGON_SITE.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_HEXAGON_SITE.getAction()));
                    break;
                case ARTICLE:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_ARTICLE.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_ARTICLE.getAction()));
                    break;
                case PRODUCT:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_PRODUCT.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_PRODUCT.getAction()));
                    break;
                case CUSTOMIZE_FORM:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_FORM_NEW.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_FORM_NEW.getAction()));
                    break;
                case QR_POSTER:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_QR_POSTER.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_QR_POSTER.getAction()));
                    break;
                case IMAGE:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_IMAGE.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_IMAGE.getAction()));
                    break;
                case FILE:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_FILE.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_FILE.getAction()));
                    break;
                case VIDEO:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_VIDEO.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_VIDEO.getAction()));
                    break;
                case ACTIVITY:
                    actionTypeList.add(getNewActionType(ActionTypeEnum.LOOK_UP_ACTIVITY.getAction()));
                    actionTypeList.add(getNewActionType(ActionTypeEnum.FORWARD_ACTIVITY.getAction()));
                    break;
                default:
                    break;
            }
        }
        return actionTypeList;
    }

    /**
     * 之前修改过这个定义，现在都是新加 1000000
     */
    private int getNewActionType(int oldActionType) {
        return oldActionType + 1000000;
    }

    private int getOldActionType(int newActionType) {
        return newActionType - 1000000;
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult>> getObjectUserMarketingList(ObjectUserMarketingListArg arg) {
        if (!arg.checkParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        //个人推广
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
        if(marketingActivityExternalConfigEntity==null){
            return Result.newSuccess();
        }
            //最终查询的物料id
            List<String> resultIds = Lists.newArrayList();
            //海报的物料id
            List<String> qrIds = Lists.newArrayList();

            //企业推广的营销活动id
            if(marketingActivityExternalConfigEntity.getMarketingActivityType()== MarketingActivityTypeEnum.ENTERPRISE.getType()){
                List<MarketingActivityObjectRelationEntity> objectRelationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityId(arg.getEa(), marketingActivityExternalConfigEntity.getMarketingActivityId());
                for (MarketingActivityObjectRelationEntity entity : objectRelationEntityList) {
                    if(entity.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()){
                        qrIds.add(entity.getObjectId());
                    }else {
                        resultIds.add(entity.getObjectId());
                    }
                }
            }else {     //个人推广
                if(marketingActivityExternalConfigEntity.getAssociateIdType()==ObjectTypeEnum.QR_POSTER.getType()){
                    qrIds.add(marketingActivityExternalConfigEntity.getAssociateId());
                }else {
                    resultIds.add(marketingActivityExternalConfigEntity.getAssociateId());
                }
            }

            if(CollectionUtils.isNotEmpty(qrIds)){
                List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrIds,arg.getEa());
                List<String> qrPostTargetId = qrPosterEntities.stream().filter(o->StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
                resultIds.addAll(qrPostTargetId);
            }
        String ea = arg.getEa();
        Integer userId = arg.getUserId();
        ObjectActionUserMarketingListArg listArg = new ObjectActionUserMarketingListArg();
        listArg.setMarketingActivityId(arg.getMarketingActivityId());
        listArg.setEa(ea);
        listArg.setUserId(userId);
        List<Integer> actionTypeList = Lists.newArrayList(NewActionTypeEnum.LOOK_UP.getActionType(),NewActionTypeEnum.FORWARD.getActionType());
        listArg.setSpreadFsUserId(arg.getEmployeeId());
        if (arg.getOperateType() == 1) {
            actionTypeList = actionTypeList.stream().filter(e -> e == NewActionTypeEnum.LOOK_UP.getActionType() || ActionTypeEnum.isLookUpAction(getOldActionType(e))).collect(Collectors.toList());
        } else {
            actionTypeList = actionTypeList.stream().filter(e -> e == NewActionTypeEnum.FORWARD.getActionType() || ActionTypeEnum.isForwardActon(getOldActionType(e))).collect(Collectors.toList());
        }
        listArg.setActionTypeList(actionTypeList);
        listArg.setObjectIdList(resultIds);
        if(arg.getStartDate()!=null&&arg.getEndDate()!=null){
            listArg.setStartTime(arg.getStartDate());
            listArg.setEndTime(arg.getEndDate());
        }
        listArg.setPageNum(arg.getPageNum());
        listArg.setPageSize(arg.getPageSize());
        // 查询埋点数据
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<ObjectActionUserMarketingListResult>>
                objectActionUserMarketingPage =  userMarketingStatisticService.getObjectActionUserMarketingList(listArg);
        List<ObjectUserMarketingListResult> resultList = Lists.newArrayList();
        com.facishare.marketing.statistic.common.model.PageResult<ObjectActionUserMarketingListResult> objectActionUserMarketingPageResult = objectActionUserMarketingPage.getData();

        com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult> resultPage = com.facishare.marketing.common.result.PageResult.newPageResult(arg.getPageNum(), arg.getPageSize(),
                objectActionUserMarketingPageResult.getTotalCount(), resultList);

        List<ObjectActionUserMarketingListResult> objectActionUserMarketingListResults  = objectActionUserMarketingPage.getData().getData();
        if (CollectionUtils.isEmpty(objectActionUserMarketingListResults)) {
            return Result.newSuccess(resultPage);
        }

        List<String> userMarketingAccountIds = objectActionUserMarketingListResults.stream().map(ObjectActionUserMarketingListResult::getUserMarketingId).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(ea, userId, userMarketingAccountIds, InfoStateEnum.DETAIL);
        List<String> nPathList = userMarketingAccountDataMap.values().stream().filter(o->CollectionUtils.isNotEmpty(o.getWeChatAvatar())).map(o -> (String)o.getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)).collect(Collectors.toList());
        Map<String, String> wechatAvaterUrlMap = fileV2Manager.batchGetUrlByPath(nPathList, ea, false);
        for (ObjectActionUserMarketingListResult objectActionUserMarketingItem : objectActionUserMarketingPage.getData().getData()) {
            String userMarketingId = objectActionUserMarketingItem.getUserMarketingId();
            ObjectUserMarketingListResult item = new ObjectUserMarketingListResult();
            item.setUserMarketingId(userMarketingId);
            int actionType = objectActionUserMarketingItem.getActionType();
            int opType = actionType == NewActionTypeEnum.LOOK_UP.getActionType() || ActionTypeEnum.isLookUpAction(getOldActionType(actionType)) ? 1 : 2;
            item.setOperateType(opType);
            item.setCount(objectActionUserMarketingItem.getCount());
            UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingId);
            String name = userMarketingAccountData.getName();
            if (name != null) {
                item.setName(name);
                String wechatName = StringUtils.isBlank(userMarketingAccountData.getWeChatName()) ?
                        name : userMarketingAccountData.getWeChatName();
                item.setWeChatName(wechatName);
                if(CollectionUtils.isNotEmpty(userMarketingAccountData.getWeChatAvatar())){
                    item.setWeChatAvatarUrl(wechatAvaterUrlMap.get((String) userMarketingAccountData.getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)));
                }
                item.setWeChatAvatar(userMarketingAccountData.getWeChatAvatar());
                item.setIsVisitor(false);
            } else {
                name = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1170) + userMarketingId.substring(userMarketingId.length() - 6);
                item.setName(name);
                item.setWeChatName(name);
                item.setIsVisitor(true);
            }
            resultList.add(item);
        }

        return Result.newSuccess(resultPage);

    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<EnterpriseSpreadUserMarketingListResult>> enterpriseSpreadUserMarketingList(EnterpriseSpreadUserMarketingListArg arg) {
        // 查询营销活动的物料
        List<MarketingActivityObjectRelationEntity> relationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());

        if (CollectionUtils.isEmpty(relationEntityList)) {
            return Result.newSuccess();
        }
        // 根据物料获取对应的actionType
        List<Integer> objectTypeList = relationEntityList.stream().map(MarketingActivityObjectRelationEntity::getObjectType)
                .distinct().collect(Collectors.toList());
        List<Integer> actionTypeList = getActionType(objectTypeList, arg.getEa());

        // 查询营销动态数据
        GetEnterpriseSpreadUserMarketingListArg userMarketingListArg = BeanUtil.copyProperties(arg, GetEnterpriseSpreadUserMarketingListArg.class);
        userMarketingListArg.setActionTypeList(actionTypeList);
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.
                PageResult<com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult>> userMarketingListResult = userMarketingStatisticService.getEnterpriseSpreadUserMarketingList(userMarketingListArg);

        List<com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult> userMarketingList  = userMarketingListResult.getData().getData();
        List<EnterpriseSpreadUserMarketingListResult> resultList = Lists.newArrayList();

        com.facishare.marketing.common.result.PageResult<EnterpriseSpreadUserMarketingListResult> resultPage = new com.facishare.marketing.common.result.PageResult<>();
        resultPage.setPageNum(arg.getPageNum());
        resultPage.setPageSize(arg.getPageSize());
        resultPage.setTotalCount(userMarketingListResult.getData().getTotalCount());
        resultPage.setResult(resultList);

        if (CollectionUtils.isEmpty(userMarketingList)) {
            return Result.newSuccess(resultPage);
        }
        List<String> userMarketingIdList = Lists.newArrayList();
        List<Integer> fsUserIdList = Lists.newArrayList();
        userMarketingList.forEach(e -> {
            userMarketingIdList.add(e.getUserMarketingId());
            fsUserIdList.add(e.getSpreadFsUserId());
        });
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap =
                userMarketingAccountManager.getBaseInfosByIds(arg.getEa(), SuperUserConstants.USER_ID, userMarketingIdList, InfoStateEnum.DETAIL);
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(arg.getEa(), fsUserIdList, true);
        for (com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult statisticsResult : userMarketingList) {
            EnterpriseSpreadUserMarketingListResult result = BeanUtil.copy(statisticsResult, EnterpriseSpreadUserMarketingListResult.class);
            UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(result.getUserMarketingId());
            if (userMarketingAccountData != null) {
                String name = StringUtils.isBlank(userMarketingAccountData.getWeChatName()) ?
                        userMarketingAccountData.getName() : userMarketingAccountData.getWeChatName();
                result.setUserMarketingName(name);
            }
            FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(result.getSpreadFsUserId());
            if (fsEmployeeMsg != null) {
                result.setFsUserName(fsEmployeeMsg.getName());
                result.setDepartment(fsEmployeeMsg.getDepartment());
            }
            resultList.add(result);
        }
        return Result.newSuccess(resultPage);
    }

    @Override
    public Result<List<ObjectStatisticResult>> listObjectStatisticTop(String ea, Integer fsUserId, Integer recentDay, Integer top) {
        if (recentDay == null || top == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(recentDay - 1);
        List<ObjectStatisticResult> result;
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        if (isOpen) {
            HexagonSiteQueryParam queryParam = new HexagonSiteQueryParam();
            queryParam.setEa(ea);
            queryParam.setUserId(fsUserId);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getObjectTypeListAccessibleGroup(ea, fsUserId);
            List<String> groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            result = marketingObjectDayStatisticDao.queryObjectStatisticTopByGroupIds(ea, startDate.toDate(), endDate.toDate(), top,groupIds);
        }else {
            result = marketingObjectDayStatisticDao.queryObjectStatisticTop(ea, startDate.toDate(), endDate.toDate(), top);
        }
        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(e->{
                ObjectDto object = objectDao.getById(ea, e.getObjectType(), e.getId());
                if (null != object) {
                    objectManager.fillObjectHeadImage(ea, Collections.singleton(object));
                    e.setName(object.getName());
                    e.setHeadPicUrl(object.getHeadPicUrl());
                }
            });
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<StatisticSpreadRankingResult>> StatisticSpreadRanking(StatisticSpreadRankingArg vo) {
        if(vo==null||StringUtils.isBlank(vo.getEa())||StringUtils.isBlank(vo.getMarketingActivityId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(vo.getEa(), vo.getMarketingActivityId());
        if(marketingActivityExternalConfigEntity==null){
            return Result.newSuccess();
        }

        //最终查询的物料id
        String resultId = vo.getObjectId();

        //海报的目标物料id
        if(vo.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()){
            QRPosterEntity qrPoster = qrPosterDAO.queryById(vo.getObjectId(),vo.getEa());
            resultId = qrPoster.getTargetId();
            if (StringUtils.isBlank(resultId)){
                return Result.newSuccess();
            }
        }

        FromUserMarketingStatisticSpreadRankingArg arg = BeanUtil.copy(vo, FromUserMarketingStatisticSpreadRankingArg.class);
        arg.setStartTime(vo.getStartDate());
        arg.setEndTime(vo.getEndDate());
        arg.setObjectIds(Lists.newArrayList(resultId));
        com.facishare.marketing.statistic.common.result.Result<List<FromUserMarketingStatisticSpreadRankingResult>> statisticSpreadRankingResult = userMarketingStatisticService.fromUserMarketingStatisticSpreadRanking(arg);

        List<String> userMarketingIds = statisticSpreadRankingResult.getData().stream().map(FromUserMarketingStatisticSpreadRankingResult::getFromUserMarketingId).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(vo.getEa(), 1000, userMarketingIds, InfoStateEnum.DETAIL);
        List<String> nPathList = userMarketingAccountDataMap.values().stream().filter(o->o.getWeChatAvatar()!=null).map(o -> (String)o.getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)).collect(Collectors.toList());
        Map<String, String> wechatAvatarUrlMap = fileV2Manager.batchGetUrlByPath(nPathList, vo.getEa(), false);
        List<StatisticSpreadRankingResult> resultList = Lists.newArrayList();
        for (FromUserMarketingStatisticSpreadRankingResult entity : statisticSpreadRankingResult.getData()) {
            StatisticSpreadRankingResult result = new StatisticSpreadRankingResult();
            result.setFromUserMarketingId(entity.getFromUserMarketingId());
            result.setFromUserMarketingName(userMarketingAccountDataMap.get(entity.getFromUserMarketingId()).getName());
            if(wechatAvatarUrlMap!=null) {
                result.setFromUserMarketingAvatar(wechatAvatarUrlMap.get(entity.getFromUserMarketingId()));
            }
            result.setLookUpCount(entity.getLookUpCount());
            result.setForwardCount(entity.getForwardCount());
            result.setEnrollCount(entity.getEnrollCount());
            resultList.add(result);
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<PageResult<pageUserMarketingActionCountResult>> pageFromUserMarketingActionStatisticSpreadRanking(StatisticSpreadRankingArg arg) {
        if (arg==null||StringUtils.isBlank(arg.getEa())||StringUtils.isBlank(arg.getMarketingActivityId()) || CollectionUtils.isEmpty(arg.getFromUserMarketingIds())||arg.getActionType()==null||arg.getPageNum()==null||arg.getPageSize()==null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getEa(), arg.getMarketingActivityId());
        if(marketingActivityExternalConfigEntity==null){
            return Result.newSuccess();
        }

        //最终查询的物料id
        String resultId = arg.getObjectId();

        //海报的目标物料id
        if(arg.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()){
            QRPosterEntity qrPoster = qrPosterDAO.queryById(arg.getObjectId(),arg.getEa());
            resultId = qrPoster.getTargetId();
            if(StringUtils.isBlank(resultId)){
                return Result.newSuccess();
            }
        }

        PageResult<pageUserMarketingActionCountResult> pageResult = new PageResult<>();
        FromUserMarketingStatisticSpreadRankingArg statisticSpreadRankingArg = BeanUtil.copy(arg, FromUserMarketingStatisticSpreadRankingArg.class);
        statisticSpreadRankingArg.setStartTime(arg.getStartDate());
        statisticSpreadRankingArg.setEndTime(arg.getEndDate());
        statisticSpreadRankingArg.setObjectIds(Lists.newArrayList(resultId));
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<ObjectActionUserMarketingListResult>> pageResultResult = userMarketingStatisticService.pageFromUserMarketingActionStatisticList(statisticSpreadRankingArg);
        List<String> userMarketingIds = pageResultResult.getData().getData().stream().map(ObjectActionUserMarketingListResult::getUserMarketingId).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(arg.getEa(), 1000, userMarketingIds, InfoStateEnum.DETAIL);
        List<String> nPathList = userMarketingAccountDataMap.values().stream().filter(o->o.getWeChatAvatar()!=null).map(o -> (String)o.getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)).collect(Collectors.toList());
        Map<String, String> wechatAvatarUrlMap = fileV2Manager.batchGetUrlByPath(nPathList, arg.getEa(), false);
        List<pageUserMarketingActionCountResult> resultList = Lists.newArrayList();
        for (ObjectActionUserMarketingListResult entity : pageResultResult.getData().getData()) {
            pageUserMarketingActionCountResult result = new pageUserMarketingActionCountResult();
            result.setUserMarketingId(entity.getUserMarketingId());
            result.setUserMarketingName(userMarketingAccountDataMap.get(entity.getUserMarketingId()).getName());
            if(wechatAvatarUrlMap!=null) {
                result.setUserMarketingAvatar(wechatAvatarUrlMap.get(entity.getUserMarketingId()));
            }
            result.setCount(entity.getCount());
            resultList.add(result);
        }
        pageResult.setTotalCount(pageResultResult.getData().getTotalCount());
        pageResult.setData(resultList);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult>> getEmployeeSpreadStatisticDetailsData(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDetailsDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));

        com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult> objectUserMarketingListResultPageResult = new com.facishare.marketing.common.result.PageResult<>();
        objectUserMarketingListResultPageResult.setTotalCount(0);
        List<ObjectUserMarketingListResult> resultList = Lists.newArrayList();
        objectUserMarketingListResultPageResult.setResult(resultList);
        objectUserMarketingListResultPageResult.setPageNum(arg.getPageNum());
        objectUserMarketingListResultPageResult.setPageSize(arg.getPageSize());
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        List<String> allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIds(ea, startDate, endDate, arg.getMarketingEventId(), objectIds);
        if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)){
            return Result.newSuccess(objectUserMarketingListResultPageResult);
        }
        List<Integer> subordinateEmployeeIds = null;
        List<Integer> targetEmployeeIds = null;
        //兼容钉钉版营销通小程序,获取下属员工，钉钉&企业微信全全部员工，纷享员工取自己的下属
        if (dingManager.isDingAddressbook(ea)){
            subordinateEmployeeIds = getDingEmployeeByDepartment(ea,fsUserId,arg.getDepartmentRange());
        } else {
            subordinateEmployeeIds = getSubordinateEmployeesByUserAndDepartment(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        }
        MarketingActivitySpreadStatisticsVisitRangeEntity visitRangeEntity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, fsUserId);
        if (visitRangeEntity != null){
            List<Integer> visitUserRange = getStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
            targetEmployeeIds = visitUserRange;
        }else {
            targetEmployeeIds = subordinateEmployeeIds;
            if (CollectionUtils.isEmpty(targetEmployeeIds)){
                if (CollectionUtils.isNotEmpty(arg.getEmployeeRange()) || CollectionUtils.isNotEmpty(arg.getDepartmentRange())) {
                    log.info("StatisticServiceImpl.getEmployeeSpreadStatisticDetailsData return null ea:{} userId:{} arg:{}", ea, fsUserId, arg);
                    return Result.newSuccess(objectUserMarketingListResultPageResult);
                } else {
                    targetEmployeeIds.add(fsUserId);
                }
            }
        }
        if (CollectionUtils.isEmpty(targetEmployeeIds)){
            log.info("StatisticServiceImpl.getEmployeeSpreadStatisticDetailsData return null ea:{} userId:{} arg:{}", ea, fsUserId, arg);
            return Result.newSuccess(objectUserMarketingListResultPageResult);
        }
        EnterpriseSpreadStatisticDetailsArg detailsArg = new EnterpriseSpreadStatisticDetailsArg();
        detailsArg.setActionType(arg.getActionType());
        detailsArg.setPageNum(arg.getPageNum());
        detailsArg.setPageSize(arg.getPageSize());
        detailsArg.setStartTime(DateUtil.fromTimestamp(arg.getStartDate()));
        detailsArg.setEndTime(DateUtil.fromTimestamp(arg.getEndDate()));
        detailsArg.setEa(ea);
        detailsArg.setMarketingActivityIdList(allEmployeeSpreadMarketingActivityIds);
        detailsArg.setSpreadFsUserIdList(targetEmployeeIds);
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult>> statisticDetailsPage = userMarketingStatisticService.getEnterpriseSpreadStatisticDetailsPage(detailsArg);
        com.facishare.marketing.statistic.common.model.PageResult<com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult> detailsPageData = statisticDetailsPage.getData();
        if (detailsPageData == null) {
            return Result.newSuccess(objectUserMarketingListResultPageResult);
        }
        List<com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult> userMarketingListResults = detailsPageData.getData();
        if (CollectionUtils.isEmpty(userMarketingListResults)) {
            return Result.newSuccess(objectUserMarketingListResultPageResult);
        }
        objectUserMarketingListResultPageResult.setTotalCount(detailsPageData.getTotalCount());
        List<String> userMarketingAccountIds = userMarketingListResults.stream().map(com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult::getUserMarketingId).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(ea, fsUserId, userMarketingAccountIds, InfoStateEnum.DETAIL);
        List<String> nPathList = userMarketingAccountDataMap.values().stream().filter(o->o.getWeChatAvatar()!=null).map(o -> (String)o.getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)).collect(Collectors.toList());
        Map<String, String> wechatAvaterUrlMap = fileV2Manager.batchGetUrlByPath(nPathList, ea, false);
        for (com.facishare.marketing.statistic.outapi.result.EnterpriseSpreadUserMarketingListResult enterpriseSpreadUserMarketingListResult : userMarketingListResults) {
            String userMarketingId = enterpriseSpreadUserMarketingListResult.getUserMarketingId();
            ObjectUserMarketingListResult item = new ObjectUserMarketingListResult();
            item.setUserMarketingId(userMarketingId);
            if (Objects.equals(NewActionTypeEnum.FORWARD.getActionType(),arg.getActionType())) {
                item.setCount(enterpriseSpreadUserMarketingListResult.getForwardCount());
            } else if (Objects.equals(NewActionTypeEnum.LOOK_UP.getActionType(),arg.getActionType())) {
                item.setCount(enterpriseSpreadUserMarketingListResult.getLookUpCount());
            }
            UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingId);
            String name = userMarketingAccountData.getName();
            if (name != null) {
                item.setName(name);
                String wechatName = StringUtils.isBlank(userMarketingAccountData.getWeChatName()) ?
                        name : userMarketingAccountData.getWeChatName();
                item.setWeChatName(wechatName);
                if(userMarketingAccountData.getWeChatAvatar()!=null){
                    item.setWeChatAvatarUrl(wechatAvaterUrlMap.get((String) userMarketingAccountData.getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)));
                }
                item.setWeChatAvatar(userMarketingAccountData.getWeChatAvatar());
                item.setIsVisitor(false);
            } else {
                name = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1170) + userMarketingId.substring(userMarketingId.length() - 6);
                item.setName(name);
                item.setWeChatName(name);
                item.setIsVisitor(true);
            }
            resultList.add(item);
        }
        return Result.newSuccess(objectUserMarketingListResultPageResult);
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<QueryFormUserDataResult>> getEmployeeSpreadClueDetailsData(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDetailsDataArg arg) {
        com.facishare.marketing.common.result.PageResult<QueryFormUserDataResult> pageResult = new  com.facishare.marketing.common.result.PageResult<>();
        pageResult.setTotalCount(0);
        List<QueryFormUserDataResult> queryFormUserDataResults = Lists.newArrayList();
        pageResult.setResult(queryFormUserDataResults);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(),true);
        List<String> objectIds = null;
        if (arg.getObjectId() != null){
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, arg.getObjectId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)){
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());;
            }
            if (CollectionUtils.isEmpty(objectIds)){
                objectIds = Lists.newArrayList();
            }
            objectIds.add(arg.getObjectId());
        }
        List<String> allEmployeeSpreadMarketingActivityIds = marketingActivityExternalConfigDao.listAllEmployeeActivityIdsInDateRangeWithObjectIds(ea, startDate, endDate, arg.getMarketingEventId(), objectIds);
        if (CollectionUtils.isEmpty(allEmployeeSpreadMarketingActivityIds)){
            return Result.newSuccess(pageResult);
        }
        List<Integer> subordinateEmployeeIds = null;
        List<Integer> targetEmployeeIds = null;
        //兼容钉钉版营销通小程序,获取下属员工，钉钉&企业微信全全部员工，纷享员工取自己的下属
        if (dingManager.isDingAddressbook(ea)){
            subordinateEmployeeIds = getDingEmployeeByDepartment(ea,fsUserId,arg.getDepartmentRange());
        } else {
            subordinateEmployeeIds = getSubordinateEmployeesByUserAndDepartment(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        }
        MarketingActivitySpreadStatisticsVisitRangeEntity visitRangeEntity = marketingActivitySpreadStatisticsVisitRangeDAO.getByUserId(ea, fsUserId);
        if (visitRangeEntity != null){
            List<Integer> visitUserRange = getStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
            targetEmployeeIds = visitUserRange;
        }else {
            targetEmployeeIds = subordinateEmployeeIds;
            if (CollectionUtils.isEmpty(targetEmployeeIds)){
                if (CollectionUtils.isNotEmpty(arg.getEmployeeRange()) || CollectionUtils.isNotEmpty(arg.getDepartmentRange())) {
                    log.info("StatisticServiceImpl.getEmployeeSpreadClueDetailsData return null ea:{} userId:{} arg:{}", ea, fsUserId, arg);
                    return Result.newSuccess(pageResult);
                } else {
                    targetEmployeeIds.add(fsUserId);
                }
            }
        }
        if (CollectionUtils.isEmpty(targetEmployeeIds)){
            log.info("StatisticServiceImpl.getEmployeeSpreadClueDetailsData return null ea:{} userId:{} arg:{}", ea, fsUserId, arg);
            return Result.newSuccess(pageResult);
        }
        queryFormUserDataResults = customizeFormDataService.querySpreadFormDataStatistic(ea, allEmployeeSpreadMarketingActivityIds, targetEmployeeIds, startDate, endDate, page);
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(queryFormUserDataResults);
        return Result.newSuccess(pageResult);
    }

    private boolean isRemoveToMongo(String ea) {
//        try {
//            UserMarketingActionStatisticUpgradeRecord upgradeRecord = upgradeRecordDao.getByEa(ea);
//            if (upgradeRecord == null) {
//                return false;
//            }
//            return upgradeRecord.getStatus() == 1;
//        } catch (Exception e) {
//            log.error("check isRemoveToMongo error，ea:[{}]", ea, e);
//        }
//        return false;
        return openMongoSearch;
    }


    @Override
    public Result<QywxGroupSendStatisticDataResult> getQywxGroupSendStatisticData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        // 获取物料(兼容海报跳转)
        List<String> objectIds = getObjectIds(ea, arg.getObjectId());
        QywxGroupSendStatisticDataResult result;
        List<QywxGroupSendTaskEntity> qywxGroupSendTaskEntityList = qywxGroupSendTaskDAO.listActivityIdsInDateRangeWithObjectIds(ea, arg.getStartDate(), arg.getEndDate(), arg.getMarketingEventId(), objectIds);
        List<String> marketingActivityIds = CollectionUtils.isEmpty(qywxGroupSendTaskEntityList) ? null :
                qywxGroupSendTaskEntityList.stream().map(QywxGroupSendTaskEntity::getMarketingActivityId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingActivityIds)) {
            // 构建空数据
            result = getQywxStatisticDataResult(startDate, endDate);
        } else {
            result = getQywxStatisticDataResult(ea, marketingActivityIds, startDate, endDate);
            List<String> sendIds = qywxGroupSendTaskEntityList.stream()
                    .map(QywxGroupSendTaskEntity::getId)
                    .collect(Collectors.toList());
            AtomicInteger receiveCount = new AtomicInteger();
            AtomicInteger confirmCount = new AtomicInteger();
            AtomicInteger deliveryCount = new AtomicInteger();
            ExecutorService executorService = Executors.newFixedThreadPool(3);
            CountDownLatch countDownLatch = new CountDownLatch(3);
            TraceContext context = TraceContext.get();
            executorService.execute(() -> {
                TraceContext._set(context);
                receiveCount.set(qywxGroupSendResultDAO.calculateReceiveCountBySendIds(ea, sendIds, startDate, endDate));
                countDownLatch.countDown();
            });
            executorService.execute(() -> {
                TraceContext._set(context);
                confirmCount.set(qywxGroupSendResultDAO.calculateConfirmCountBySendIds(ea, sendIds, startDate, endDate));
                countDownLatch.countDown();
            });
            executorService.execute(() -> {
                TraceContext._set(context);
                deliveryCount.set(qywxGroupSendResultDAO.calculateDeliveryCountBySendIds(ea, sendIds, startDate, endDate));
                countDownLatch.countDown();
            });
            try {
                countDownLatch.await(5L, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.warn("getQywxGroupSendStatisticData awaitawait error", e);
            }
            if (!executorService.isShutdown()) {
                executorService.shutdown();
            }
            result.setReceiveCount(receiveCount.get());
            result.setConfirmCount(confirmCount.get());
            result.setDeliveryCount(deliveryCount.get());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult>> getQywxGroupSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        List<QywxGroupSendEmployeeRankingDataResult> employeeRankingDataResults = new ArrayList<>(0);
        com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult> pageResult = new com.facishare.marketing.common.result.PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(employeeRankingDataResults);
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<String> objectIds = getObjectIds(ea, arg.getObjectId());
        List<QywxGroupSendTaskEntity> qywxGroupSendTaskEntityList = qywxGroupSendTaskDAO.listActivityIdsInDateRangeWithObjectIds(ea, arg.getStartDate(), arg.getEndDate(), arg.getMarketingEventId(), objectIds);
        List<String> marketingActivityIds = CollectionUtils.isEmpty(qywxGroupSendTaskEntityList) ? null :
                qywxGroupSendTaskEntityList.stream().map(QywxGroupSendTaskEntity::getMarketingActivityId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingActivityIds)) {
            return Result.newSuccess(pageResult);
        }
        List<String> qywxStaticsticsVisitUserRange = getQywxStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        if (qywxStaticsticsVisitUserRange == null) {
            return Result.newSuccess(pageResult);
        }
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<String> sendIds = qywxGroupSendTaskEntityList.stream()
                .map(QywxGroupSendTaskEntity::getId)
                .collect(Collectors.toList());
        List<QywxGroupSendEmployeeRankingDataResult> rankingDataResults = qywxGroupSendResultDAO.queryPageByMarketingActivityIdsAndEmployeeds(ea, sendIds, qywxStaticsticsVisitUserRange, page);
        if (CollectionUtils.isEmpty(rankingDataResults)) {
            return Result.newSuccess(pageResult);
        }
        employeeRankingDataResults.addAll(rankingDataResults);
        pageResult.setTotalCount(page.getTotalNum());
        Map<String,Integer> employeeConfirmCountMap = Maps.newHashMap();
        Map<String,Integer> employeeDeliveryCountMap = Maps.newHashMap();
        ExecutorService executorService = Executors.newFixedThreadPool(2);
        CountDownLatch countDownLatch = new CountDownLatch(2);
        TraceContext context = TraceContext.get();
        executorService.execute(() -> {
            TraceContext._set(context);
            List<QywxGroupSendEmployeeRankingDataResult> employeeConfirmCountList = qywxGroupSendGroupResultDAO.calculateEmployeeConfirmCountBySendIds(ea, sendIds, startDate, endDate);
            if (CollectionUtils.isNotEmpty(employeeConfirmCountList)) {
                for (QywxGroupSendEmployeeRankingDataResult rankingDataResult : employeeConfirmCountList) {
                    employeeConfirmCountMap.put(rankingDataResult.getQywxUserId(), rankingDataResult.getConfirmCount());
                }
            }
            countDownLatch.countDown();
        });
        executorService.execute(() -> {
            TraceContext._set(context);
            List<QywxGroupSendEmployeeRankingDataResult> employeeDeliveryCountList = qywxGroupSendGroupResultDAO.calculateEmployeeDeliveryCountBySendIds(ea, sendIds, startDate, endDate);
            if (CollectionUtils.isNotEmpty(employeeDeliveryCountList)) {
                for (QywxGroupSendEmployeeRankingDataResult rankingDataResult : employeeDeliveryCountList) {
                    employeeDeliveryCountMap.put(rankingDataResult.getQywxUserId(), rankingDataResult.getDeliveryCount());
                }
            }
            countDownLatch.countDown();
        });
        List<String> qywxUserIds = rankingDataResults.stream().map(QywxGroupSendEmployeeRankingDataResult::getQywxUserId).collect(Collectors.toList());
        List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(ea, qywxUserIds);
        Map<String, Integer> qywxUserIdFsUserIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxVirtualFsUserEntityList)) {
            qywxUserIdFsUserIdMap.putAll(qywxVirtualFsUserEntityList.stream().collect(Collectors.toMap(QywxVirtualFsUserEntity::getQyUserId, QywxVirtualFsUserEntity::getUserId)));
        }
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(qywxUserIdFsUserIdMap.values()), true);
        try {
            countDownLatch.await(5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("getQywxGroupSendStatisticData awaitawait error", e);
        }
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
        for (QywxGroupSendEmployeeRankingDataResult rankingDataResult : rankingDataResults) {
            String qywxUserId = rankingDataResult.getQywxUserId();
            rankingDataResult.setEmployeeId(qywxUserIdFsUserIdMap.get(qywxUserId));
            if (employeeMsgMap.get(rankingDataResult.getEmployeeId()) != null) {
                rankingDataResult.setEmployeeName(employeeMsgMap.get(rankingDataResult.getEmployeeId()).getName());
                rankingDataResult.setDepartmentName(employeeMsgMap.get(rankingDataResult.getEmployeeId()).getDepartment());
            } else {
                rankingDataResult.setEmployeeName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
                rankingDataResult.setDepartmentName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1704));
            }
            int confirmCount = employeeConfirmCountMap.get(qywxUserId) == null ? 0 : employeeConfirmCountMap.get(qywxUserId);
            rankingDataResult.setConfirmCount(confirmCount);
            Double rate = BigDecimal.valueOf(rankingDataResult.getReceiveCount() == 0 ? 0d : confirmCount * 100 / (rankingDataResult.getReceiveCount() * 1d)).setScale(2, RoundingMode.HALF_UP).doubleValue();
            rankingDataResult.setRate(rate);
            int deliveryCount = employeeDeliveryCountMap.get(qywxUserId) == null ? 0 : employeeDeliveryCountMap.get(qywxUserId);
            rankingDataResult.setDeliveryCount(deliveryCount);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public void exportQywxGroupSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult>> result = getQywxGroupSendEmployeeRankingData(ea, fsUserId, arg);
            if (result.isSuccess()) {
                String fileName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3076) + ".xlsx";
                Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
                excelConfigMap.put(ExcelConfigEnum.FILE_NAME, fileName);
                excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
                excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
                XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
                XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
                xssfSheet.setDefaultColumnWidth(20);
                List<String> titleList = ImmutableList.of(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_92), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3082_2), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3082_3), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3082_4), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3082_5));
                List<List<Object>> dataList = result.getData().getResult().stream().map(e -> {
                    List<Object> row = new ArrayList<>(6);
                    row.add(e.getEmployeeName());
                    row.add(e.getDepartmentName());
                    row.add(e.getReceiveCount());
                    row.add(e.getConfirmCount());
                    row.add(e.getRate() + "%");
                    row.add(e.getDeliveryCount());
                    return row;
                }).collect(Collectors.toList());
                ExcelUtil.fillContent(xssfSheet, titleList, dataList);
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, fileName, ea, fsUserId);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    @Override
    public Result<QywxGroupSendStatisticDataResult> getQywxMomentSendStatisticData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        QywxGroupSendStatisticDataResult result;
        List<QywxMomentTaskEntity> qywxMomentTaskEntityList = qywxMomentTaskDAO.listActivityIdsInDateRangeWithObjectIds(ea, arg.getStartDate(), arg.getEndDate(), arg.getMarketingEventId());
        List<String> marketingActivityIds = CollectionUtils.isEmpty(qywxMomentTaskEntityList) ? null :
                qywxMomentTaskEntityList.stream().map(QywxMomentTaskEntity::getMarketingActivityId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingActivityIds)) {
            // 构建空数据
            result = getQywxStatisticDataResult(startDate, endDate);
        } else {
            result = getQywxStatisticDataResult(ea, marketingActivityIds, startDate, endDate);
            List<String> momentIds = qywxMomentTaskEntityList.stream()
                    .map(QywxMomentTaskEntity::getMomentId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            AtomicInteger receiveCount = new AtomicInteger();
            AtomicInteger confirmCount = new AtomicInteger();
            AtomicInteger deliveryCount = new AtomicInteger();
            if (CollectionUtils.isNotEmpty(momentIds)) {
                ExecutorService executorService = Executors.newFixedThreadPool(3);
                CountDownLatch countDownLatch = new CountDownLatch(3);
                TraceContext context = TraceContext.get();
                executorService.execute(() -> {
                    TraceContext._set(context);
                    receiveCount.set(qywxMomentSendResultDaO.calculateReceiveCountByMomentIds(ea, momentIds, startDate, endDate));
                    countDownLatch.countDown();
                });
                executorService.execute(() -> {
                    TraceContext._set(context);
                    confirmCount.set(qywxMomentSendResultDaO.calculateConfirmCountByMomentIds(momentIds, startDate, endDate,ea));
                    countDownLatch.countDown();
                });
                executorService.execute(() -> {
                    TraceContext._set(context);
                    deliveryCount.set(qywxMomentSendResultDaO.calculateDeliveryCountByMomentIds(momentIds, startDate, endDate,ea));
                    countDownLatch.countDown();
                });
                try {
                    countDownLatch.await(5L, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    log.warn("getQywxGroupSendStatisticData awaitawait error", e);
                }
                if (!executorService.isShutdown()) {
                    executorService.shutdown();
                }
            }
            result.setReceiveCount(receiveCount.get());
            result.setConfirmCount(confirmCount.get());
            result.setDeliveryCount(deliveryCount.get());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult>> getQywxMomentSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg) {
        Preconditions.checkArgument(arg.getStartDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_249));
        Preconditions.checkArgument(arg.getEndDate() != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_250));
        Preconditions.checkArgument(arg.getEndDate() >= arg.getStartDate(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_251));
        List<QywxGroupSendEmployeeRankingDataResult> employeeRankingDataResults = new ArrayList<>(0);
        com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult> pageResult = new com.facishare.marketing.common.result.PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(employeeRankingDataResults);
        Date startDate = new Date(arg.getStartDate());
        Date endDate = new Date(arg.getEndDate());
        List<QywxMomentTaskEntity> qywxMomentTaskEntityList = qywxMomentTaskDAO.listActivityIdsInDateRangeWithObjectIds(ea, arg.getStartDate(), arg.getEndDate(), arg.getMarketingEventId());
        List<String> marketingActivityIds = CollectionUtils.isEmpty(qywxMomentTaskEntityList) ? null :
                qywxMomentTaskEntityList.stream().map(QywxMomentTaskEntity::getMarketingActivityId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingActivityIds)) {
            return Result.newSuccess(pageResult);
        }
        List<String> qywxStaticsticsVisitUserRange = getQywxStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange());
        if (qywxStaticsticsVisitUserRange == null) {
            return Result.newSuccess(pageResult);
        }
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<QywxGroupSendEmployeeRankingDataResult> rankingDataResults = qywxMomentSendResultDaO.queryPageByMarketingActivityIdsAndEmployeeds(ea, marketingActivityIds, qywxStaticsticsVisitUserRange, page);
        if (CollectionUtils.isEmpty(rankingDataResults)) {
            return Result.newSuccess(pageResult);
        }
        employeeRankingDataResults.addAll(rankingDataResults);
        pageResult.setTotalCount(page.getTotalNum());

        List<String> momentIds = qywxMomentTaskEntityList.stream()
                .map(QywxMomentTaskEntity::getMomentId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<String,Integer> employeeConfirmCountMap = Maps.newHashMap();
        Map<String,Integer> employeeDeliveryCountMap = Maps.newHashMap();
        ExecutorService executorService = Executors.newFixedThreadPool(2);
        CountDownLatch countDownLatch = new CountDownLatch(2);
        TraceContext context = TraceContext.get();
        executorService.execute(() -> {
            TraceContext._set(context);
            List<QywxGroupSendEmployeeRankingDataResult> employeeConfirmCountList = qywxMomentSendResultDaO.calculateEmployeeConfirmCountByMomentIds(ea, momentIds, startDate, endDate);
            if (CollectionUtils.isNotEmpty(employeeConfirmCountList)) {
                for (QywxGroupSendEmployeeRankingDataResult rankingDataResult : employeeConfirmCountList) {
                    employeeConfirmCountMap.put(rankingDataResult.getQywxUserId(), rankingDataResult.getConfirmCount());
                }
            }
            countDownLatch.countDown();
        });
        executorService.execute(() -> {
            TraceContext._set(context);
            List<QywxGroupSendEmployeeRankingDataResult> employeeDeliveryCountList = qywxMomentSendResultDaO.calculateEmployeeDeliveryCountByMomentIds(momentIds, startDate, endDate,ea);
            if (CollectionUtils.isNotEmpty(employeeDeliveryCountList)) {
                for (QywxGroupSendEmployeeRankingDataResult rankingDataResult : employeeDeliveryCountList) {
                    employeeDeliveryCountMap.put(rankingDataResult.getQywxUserId(), rankingDataResult.getDeliveryCount());
                }
            }
            countDownLatch.countDown();
        });
        List<String> qywxUserIds = rankingDataResults.stream().map(QywxGroupSendEmployeeRankingDataResult::getQywxUserId).collect(Collectors.toList());
        List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(ea, qywxUserIds);
        Map<String, Integer> qywxUserIdFsUserIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxVirtualFsUserEntityList)) {
            qywxUserIdFsUserIdMap.putAll(qywxVirtualFsUserEntityList.stream().collect(Collectors.toMap(QywxVirtualFsUserEntity::getQyUserId, QywxVirtualFsUserEntity::getUserId)));
        }
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(qywxUserIdFsUserIdMap.values()), true);
        try {
            countDownLatch.await(5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("getQywxGroupSendStatisticData awaitawait error", e);
        }
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
        for (QywxGroupSendEmployeeRankingDataResult rankingDataResult : rankingDataResults) {
            String qywxUserId = rankingDataResult.getQywxUserId();
            rankingDataResult.setEmployeeId(qywxUserIdFsUserIdMap.get(qywxUserId));
            if (employeeMsgMap.get(rankingDataResult.getEmployeeId()) != null) {
                rankingDataResult.setEmployeeName(employeeMsgMap.get(rankingDataResult.getEmployeeId()).getName());
                rankingDataResult.setDepartmentName(employeeMsgMap.get(rankingDataResult.getEmployeeId()).getDepartment());
            } else {
                rankingDataResult.setEmployeeName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
                rankingDataResult.setDepartmentName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1704));
            }
            int confirmCount = employeeConfirmCountMap.get(qywxUserId) == null ? 0 : employeeConfirmCountMap.get(qywxUserId);
            rankingDataResult.setConfirmCount(confirmCount);
            Double rate = BigDecimal.valueOf(rankingDataResult.getReceiveCount() == 0 ? 0d : confirmCount * 100 / (rankingDataResult.getReceiveCount() * 1d)).setScale(2, RoundingMode.HALF_UP).doubleValue();
            rankingDataResult.setRate(rate);
            int deliveryCount = employeeDeliveryCountMap.get(qywxUserId) == null ? 0 : employeeDeliveryCountMap.get(qywxUserId);
            rankingDataResult.setDeliveryCount(deliveryCount);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public void exportQywxMomentSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult>> result = getQywxMomentSendEmployeeRankingData(ea, fsUserId, arg);
            if (result.isSuccess()) {
                String fileName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3259) + ".xlsx";
                Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
                excelConfigMap.put(ExcelConfigEnum.FILE_NAME, fileName);
                excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
                excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
                XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
                XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
                xssfSheet.setDefaultColumnWidth(20);
                List<String> titleList = ImmutableList.of(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_92), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3265_2), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3265_3), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_STATISTICSERVICEIMPL_3265_4), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_289));
                List<List<Object>> dataList = result.getData().getResult().stream().map(e -> {
                    List<Object> row = new ArrayList<>(6);
                    row.add(e.getEmployeeName());
                    row.add(e.getDepartmentName());
                    row.add(e.getReceiveCount());
                    row.add(e.getConfirmCount());
                    row.add(e.getRate() + "%");
                    row.add(e.getDeliveryCount());
                    return row;
                }).collect(Collectors.toList());
                ExcelUtil.fillContent(xssfSheet, titleList, dataList);
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, fileName, ea, fsUserId);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    private QywxGroupSendStatisticDataResult getQywxStatisticDataResult(Date startDate, Date endDate) {
        QywxGroupSendStatisticDataResult result = new QywxGroupSendStatisticDataResult();
        result.setSendCount(0);
        result.setReceiveCount(0);
        result.setConfirmCount(0);
        result.setDeliveryCount(0);
        result.setPv(0);
        result.setUv(0);
        result.setForwardCount(0);
        result.setForwardNum(0);
        result.setFormDataEnrollCount(0);
        List<DateCount> noData = new ArrayList<>(31);
        Date tempData = startDate;
        while (tempData.compareTo(endDate) <= 0) {
            tempData = DateUtil.getSomeDay(tempData, 1);
            DateCount dateCount = new DateCount();
            dateCount.setDate(tempData);
            dateCount.setCount(0);
            noData.add(dateCount);
        }
        //访问次数
        result.setPvTrend(convertDateCountListToDayTrend(noData, startDate, endDate));
        //获取提交表单数趋势
        result.setFormDataEnrollTrend(convertDateCountListToDayTrend(noData, startDate, endDate));
        return result;
    }

    private List<String> getObjectIds(String ea, String objectId) {
        List<String> objectIds = null;
        if (objectId != null) {
            //兼容海报跳转物料情况
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByTargetIds(ea, objectId);
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)) {
                objectIds = qrPosterEntityList.stream().map(QRPosterEntity::getId).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(objectIds)) {
                objectIds = Lists.newArrayList();
            }
            objectIds.add(objectId);
        }
        return objectIds;
    }

    private QywxGroupSendStatisticDataResult getQywxStatisticDataResult(String ea, List<String> marketingActivityIds, Date startDate, Date endDate) {
        QywxGroupSendStatisticDataResult result = new QywxGroupSendStatisticDataResult();
        result.setSendCount(marketingActivityIds.size());
        CommonStatisticData commonStatisticData = marketingActivityDayStatisticDao.sumUpStatisticInDateRange(ea, marketingActivityIds, startDate, endDate);
        if (commonStatisticData != null) {
            result.setPv(commonStatisticData.getLookUpCount());
            result.setForwardCount(commonStatisticData.getForwardCount());
        }
        result.setFormDataEnrollCount(customizeFormDataUserDAO.countByMarketingActivityIdsAndDateRange(ea, marketingActivityIds, startDate, endDate));
        result.setPvTrend(convertDateCountListToDayTrend(marketingActivityDayStatisticDao.dateSumUpLookUpCountInDateRange(ea, marketingActivityIds, startDate, endDate), startDate, endDate));
        result.setFormDataEnrollTrend(convertDateCountListToDayTrend(customizeFormDataUserDAO.dateCountByMarketingActivityIdsAndDateRange(ea, marketingActivityIds, startDate, endDate), startDate, endDate));
        return result;
    }

    /**
     * 获取企微发生可见范围:
     * 返回null, 查询时不进行过滤
     * 若非非null, 查询时根据可见范围进行过滤
     *
     * @param ea
     * @param fsUserId
     * @param employeeRange
     * @param departmentRange
     * @return
     */
    private List<String> getQywxStaticsticsVisitUserRange(String ea, Integer fsUserId, List<String> employeeRange, List<Integer> departmentRange) {
        List<String> qywxStaticsticsVisitUserRange = Lists.newArrayList();
        boolean allCompany = CollectionUtils.isEmpty(employeeRange) && CollectionUtils.isEmpty(departmentRange);
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);
        if (!allCompany && !isOpen) {
            // 非全公司且未开启数据权限
            List<Integer> fsStaticsticsVisitUserRange = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, departmentRange);
            if (CollectionUtils.isNotEmpty(employeeRange)) {
                fsStaticsticsVisitUserRange.addAll(employeeRange.stream().map(Integer::valueOf).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(fsStaticsticsVisitUserRange)) {
                qywxStaticsticsVisitUserRange = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfos(ea, fsStaticsticsVisitUserRange);
            }
            return CollectionUtils.isEmpty(qywxStaticsticsVisitUserRange) ? null : qywxStaticsticsVisitUserRange;
        } else if (isOpen) {
            // 开启数据权限
            String accessToken = qywxManager.getAccessToken(ea);
            List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, false, false);
            if(CollectionUtils.isEmpty(staffInfoList)){
                log.warn("getQywxStaticsticsVisitUserRange staffInfoList is null, ea:{}", ea);
                return null;
            }
            List<Integer> qywxAccessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(ea, fsUserId,true);
            if(CollectionUtils.isEmpty(qywxAccessibleDepartmentIds)){
                return null;
            }
            if (!qywxAccessibleDepartmentIds.contains(defaultAllDepartment)){
                qywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds.stream().map(o -> o- QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                List<Integer> finalQywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds;
                qywxStaticsticsVisitUserRange = staffInfoList.stream().filter(staffInfo -> CollectionUtils.containsAny(staffInfo.getDepartment(), finalQywxAccessibleDepartmentIds)).map(DepartmentStaffResult.StaffInfo::getUserId).collect(Collectors.toList());
            }
            //没权限直接返回
            if(CollectionUtils.isEmpty(qywxStaticsticsVisitUserRange)){
                return null;
            }
            // 条件过滤
            if(!allCompany){
                List<Integer> fsStaticsticsVisitUserRange = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, departmentRange);
                if (CollectionUtils.isNotEmpty(employeeRange)) {
                    fsStaticsticsVisitUserRange.addAll(employeeRange.stream().map(Integer::valueOf).collect(Collectors.toList()));
                }
                if(CollectionUtils.isEmpty(fsStaticsticsVisitUserRange)){
                    return null;
                }
                qywxStaticsticsVisitUserRange = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfosAndQyUserIds(ea, fsStaticsticsVisitUserRange,qywxStaticsticsVisitUserRange);
            }
        }
        return qywxStaticsticsVisitUserRange;
    }


    @Override
    public void officialWebsiteStatisticJob(){
        tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
            List<OfficialWebsiteEntity> entities = officialWebsiteDAO.listNormalOfficialWebsite(dbRouteEa);
            if (CollectionUtils.isEmpty(entities)){
                return;
            }
            log.info("StatisticServiceImpl -> entities size:{}", entities.size());
            entities.forEach(entity -> {
                ThreadPoolUtils.execute(() -> {
                    if (marketingActivityRemoteManager.enterpriseStop(entity.getEa()) || appVersionManager.getCurrentAppVersion(entity.getEa()) == null){
                        log.info("企业已经停止使用, ea:[{}]", entity.getEa());
                        return;
                    }
                    officialWebsiteStatistic(entity.getEa(),entity.getId());
                }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
            });
        });
    }



    /**
     * 每天计算官网统计数据
     */
    public void officialWebsiteStatistic(String ea,String officialWebsiteId){
        
        //其他官网数据
        officialWebsiteDayStatistic(ea, officialWebsiteId);

        //渠道数据
        officialWebsiteDaySourceStatistic(ea, officialWebsiteId);

    }
    /**
     * 每天计算官网统计数据(ip,pv,uv等)
     */
    private void officialWebsiteDayStatistic(String ea, String officialWebsiteId) {
        String timeStr = DateUtil.format2(new Date());

        //统计数据
        List<String> needFindDates = null;
        String latestDateStr = officialWebsiteDayStatisticDAO.getLatestDateStr(ea, officialWebsiteId);
        if(StringUtils.isBlank(latestDateStr)){
            //只算昨天
            needFindDates = Lists.newArrayList(DateUtil.getLastDate(timeStr));
        }else {
            //查出需要计算的天数
            needFindDates = getDaysUntil(latestDateStr);
        }
        officialWebsiteDayStatisticInsert(ea, officialWebsiteId, needFindDates);
    }


    /**
     * 每天计算官网渠道数据统计数据
     */
    private void officialWebsiteDaySourceStatistic(String ea, String officialWebsiteId) {
        String timeStr = DateUtil.format2(new Date());
        List<String> needFindDates = null;
        String latestDateStr = officialWebsiteDaySourceStatisticDAO.getLatestDateStr(ea, officialWebsiteId);
        if(StringUtils.isBlank(latestDateStr)){
            //只算昨天
            needFindDates = Lists.newArrayList(DateUtil.getLastDate(timeStr));
        }else {
            //查出需要计算的天数
            needFindDates = getDaysUntil(latestDateStr);
        }
        officialWebsiteDaySourceInsert(ea, officialWebsiteId, needFindDates);
    }


    private void officialWebsiteDaySourceInsert(String ea, String officialWebsiteId, List<String> needFindDates) {
        for (String needFindDate : needFindDates) {
            CountOfficialWebsiteDataArg arg = new CountOfficialWebsiteDataArg();
            arg.setEa(ea);
            arg.setOfficialWebsiteId(officialWebsiteId);
            arg.setTimeStr(needFindDate);
            com.facishare.marketing.statistic.common.result.Result<CountOfficialWebsiteSourceResult> countOfficialWebsiteSourceResultResult = userMarketingStatisticService.countOfficialWebsiteSource(arg);
            if (countOfficialWebsiteSourceResultResult != null && countOfficialWebsiteSourceResultResult.getData() != null) {
                List<CountOfficialWebsiteSourceResult.SourceNum> sourceNumList = countOfficialWebsiteSourceResultResult.getData().getSourceNumList();
                if (CollectionUtils.isNotEmpty(sourceNumList)) {
                    for (CountOfficialWebsiteSourceResult.SourceNum sourceResult : sourceNumList) {
                        OfficialWebsiteDaySourceStatisticEntity sourceStatisticEntity = new OfficialWebsiteDaySourceStatisticEntity();
                        sourceStatisticEntity.setId(UUIDUtil.getUUID());
                        sourceStatisticEntity.setEa(ea);
                        sourceStatisticEntity.setOfficialWebsiteId(officialWebsiteId);
                        sourceStatisticEntity.setTimeStr(needFindDate);
                        sourceStatisticEntity.setSource(sourceResult.getMarketingSourceType());
                        sourceStatisticEntity.setCount(sourceResult.getNum());
                        sourceStatisticEntity.setClient(sourceResult.getClient());
                        officialWebsiteDaySourceStatisticDAO.insert(sourceStatisticEntity);
                    }
                }
            }
        }
    }


    private void officialWebsiteDayStatisticInsert(String ea, String officialWebsiteId, List<String> needFindDates) {
        for (String needFindDate : needFindDates) {
            CountOfficialWebsiteDataArg arg = new CountOfficialWebsiteDataArg();
            arg.setEa(ea);
            arg.setOfficialWebsiteId(officialWebsiteId);
            arg.setTimeStr(needFindDate);

            com.facishare.marketing.statistic.common.result.Result<CountOfficialWebsiteResult> officialWebsiteStatistic = userMarketingStatisticService.officialWebsiteStatistic(arg);
            OfficialWebsiteDayStatisticEntity statisticEntity = new OfficialWebsiteDayStatisticEntity();
            if(officialWebsiteStatistic!=null && officialWebsiteStatistic.getData()!=null){
                statisticEntity.setPv(Math.toIntExact(officialWebsiteStatistic.getData().getTotalPv()));
                statisticEntity.setUv(Math.toIntExact(officialWebsiteStatistic.getData().getTotalUv()));
                statisticEntity.setIpCount(Math.toIntExact(officialWebsiteStatistic.getData().getTotalIp()));
                statisticEntity.setJumpUpCount(Math.toIntExact(officialWebsiteStatistic.getData().getTotalJump()));
                statisticEntity.setActionDurationTime(officialWebsiteStatistic.getData().getTotalDuration());
            }
            GetWebsiteStatisticByIdArg leadArg = new GetWebsiteStatisticByIdArg();
            leadArg.setId(officialWebsiteId);
            leadArg.setEa(ea);
            leadArg.setStartTime(DateUtil.getStartTimeByDateStr(needFindDate));
            leadArg.setEndTime(DateUtil.getEndTimeByDateStr(needFindDate));
            statisticEntity.setLeadCount(officialWebsiteService.websiteLeadsCount(leadArg));

            statisticEntity.setId(UUIDUtil.getUUID());
            statisticEntity.setEa(ea);
            statisticEntity.setOfficialWebsiteId(officialWebsiteId);
            statisticEntity.setTimeStr(needFindDate);
            officialWebsiteDayStatisticDAO.insert(statisticEntity);
        }
    }

    public static List<String> getDaysUntil(String endDateStr) {
        List<String> days = new ArrayList<>();
        LocalDate currentDate = LocalDate.now().minusDays(1); // 获取昨天的日期
        LocalDate endDate = LocalDate.parse(endDateStr, DateUtil.DATE_FORMAT_DAY);
        while (currentDate.isAfter(endDate)) {
            days.add(currentDate.format(DateUtil.DATE_FORMAT_DAY));
            currentDate = currentDate.minusDays(1);
        }
        return days;
    }

}