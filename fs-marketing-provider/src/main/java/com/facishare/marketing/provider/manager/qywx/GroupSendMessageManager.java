package com.facishare.marketing.provider.manager.qywx;
import java.util.Date;

import com.facishare.marketing.api.vo.qywx.QuerySendGroupMessageDataVO;
import com.facishare.marketing.common.dbroute.TenantService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.ListQywxMarketingActivityEmployeeRankingArg;
import com.facishare.marketing.api.arg.ListSopQywxMsgEmployeeRankingArg;
import com.facishare.marketing.api.arg.MomentCustomerArg;
import com.facishare.marketing.api.arg.OfficeMessageArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.result.MomentCustomer;
import com.facishare.marketing.api.result.qywx.ListEmployeeQywxGroupSendDetailResult;
import com.facishare.marketing.api.result.qywx.ListGroupSendMessageResult;
import com.facishare.marketing.api.result.qywx.ListQywxMarketingActivityEmployeeRankingResult;
import com.facishare.marketing.api.result.qywx.QywxEmployeeResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryCustomerGroupListResult;
import com.facishare.marketing.api.vo.qywx.ListGroupSendMessageVO;
import com.facishare.marketing.api.vo.qywx.QywxAttachmentsVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupSendMessageVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import com.facishare.marketing.common.enums.qywx.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.dto.ListEmployeeQywxGroupSendDetailDto;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.dto.qywx.*;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qywx.*;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.innerArg.qywx.*;
import com.facishare.marketing.provider.innerResult.qywx.*;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupObjDescribeManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.mq.sender.MarketingRecordActionSender;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.marketing.provider.util.MarketingJobUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.wechat.proxy.common.utils.GsonUtil;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.MarketingActivityData;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

import static com.facishare.marketing.common.enums.AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE;
import static com.facishare.marketing.provider.manager.AuthManager.defaultAllDepartment;

/**
 * <AUTHOR>
 * @Description
 * @Date 16:24 2020/2/6
 * @ModifyBy
 */
@Component
@Slf4j
public class GroupSendMessageManager {
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxGroupSendTaskDAO sendTaskDAO;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Resource(name = "httpSupport")
    private OkHttpSupport httpSupport;
    @Autowired
    private QywxMiniappConfigDAO miniappConfigDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private QywxGroupSendResultDAO sendResultDAO;
    @Autowired
    private QywxGroupSendGroupResultDAO qywxGroupSendGroupResultDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private NoticeManager noticeManager;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private CustomerGroupManager customerGroupManager;
    @Autowired
    private MarketingCrmManager marketingCrmManager;
    @Autowired
    private CustomizeFormClueManager customizeFormClueManager;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Value("${qywx.group.message.default.cover}")
    private String groupMessageDefaultCoverPath;
    @Autowired
    private EnterpriseSpreadRecordManager enterpriseSpreadRecordManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;

    @Autowired
    private TriggerSnapshotDao triggerSnapshotDao;
    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;
    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;
    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;

    @Autowired
    private QYWXMomentTaskDAO qywxMomentTaskDAO;

    @Autowired
    private QYWXMomentSendResultDaO qywxMomentSendResultDao;

    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;

    @Autowired
    private MarketingNoticeSettingDAO marketingNoticeSettingDAO;
    @Autowired
    private ExecuteTaskDetailManager executeTaskDetailManager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private VirtualUserManager virtualUserManager;
    @Autowired
    private MarketingRecordActionSender marketingRecordActionSender;
    @Value("${qywx.group.message.batchNum:1000}")
    private Integer groupMessageBatchNum;
    @ReloadableProperty("host")
    private String host;
    @Value("${enterprise.environment}")
    private String enterpriseEnvironment;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private MetadataTagManager metadataTagManager;

    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private EnterpriseEditionManager enterpriseEditionManager;

    @Autowired
    private QywxAttachmentsRelationDAO qywxAttachmentsRelationDAO;

    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private QywxGroupSendTaskDAO qywxGroupSendTaskDAO;

    @Autowired
    private OutLinkMktParamManager outLinkMktParamManager;

    @Autowired
    private QywxActivatedAccountManager qywxActivatedAccountManager;
    @Autowired
    private  TenantService tenantService;

    private Gson gson = GsonUtil.getGson();

    private Gson adapterGson = new GsonBuilder().setNumberToNumberStrategy(ToNumberPolicy.BIG_DECIMAL).setObjectToNumberStrategy(ToNumberPolicy.BIG_DECIMAL).create();



//    private Date currentTime = new Date();

    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @PostConstruct
    public void init(){
        log.info("GroupSendMessageManager init");
        tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
            executeTaskDetailManager.taskComplete(dbRouteEa, ExecuteTaskDetailTypeEnum.QYWX_SEND_RESULT_PULL,"qywx_send_result_callback");
        });
    }
    public AddMsgTemplateResult addMsgTemplate(String accessToken, AddMsgTemplateArg arg) {
        if (Strings.isNullOrEmpty(accessToken) || arg == null) {
            return null;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_msg_template?access_token=" + accessToken;
        AddMsgTemplateResult result = httpManager.executePostHttp(arg, url, new TypeToken<AddMsgTemplateResult>() {
        });
        log.warn("addMsgTemplate result:{}", result);
        return result;
    }

    public GroupMsgResult getGroupMsgResult(String accessToken, GetGroupMsgResultArg arg) {
        if (Strings.isNullOrEmpty(accessToken) || arg == null) {
            return null;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_group_msg_result?access_token=" + accessToken;
        GroupMsgResult result = httpManager.executePostHttp(arg, url, new TypeToken<GroupMsgResult>() {
        });
        int tryCnt = 1;
        while (result != null && 45033 == result.getErrcode() && tryCnt <= 3) {
            result = httpManager.executePostHttp(arg, url, new TypeToken<GroupMsgResult>() {
            });
            tryCnt++;
            try {
                Thread.sleep(tryCnt * 1000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        log.warn("getGroupMsgResult result:{}", result);
        return result;
    }

    public String createGroupSendMsgTask(QywxGroupSendMessageVO vo, String marketingActivityId, String marketingEventId, String ea) {
        QywxGroupSendTaskEntity groupSendTask = new QywxGroupSendTaskEntity();
        groupSendTask.setId(UUIDUtil.getUUID());
        groupSendTask.setCorpId(vo.getCorpId());
        groupSendTask.setEa(vo.getEnterpriseAccount());
        groupSendTask.setFsUserId(vo.getUserId());
        groupSendTask.setMsgType(vo.getMsgType());
        groupSendTask.setMarketingActivityId(marketingActivityId);
        groupSendTask.setTitle(vo.getTitle());
        groupSendTask.setFilterNDaySentUser(vo.getFilterNDaySentUser());
        groupSendTask.setAllowSelect(vo.getAllowSelect());
        if (vo.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()) {
            groupSendTask.setChatType(QywxGroupSendOjbectTypeEnum.SEND_TO_GROUP.getType());
        } else {
            groupSendTask.setChatType(QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType());
        }

        if (vo.getText() != null) {
            groupSendTask.setContent(vo.getText().getContent());
        }
        if (groupSendTask.getContent() == null) {
            groupSendTask.setContent("");
        }

        //处理发送员工选择器
        if (CollectionUtils.isNotEmpty(vo.getUserIds())) {
            groupSendTask.setUserId(gson.toJson(vo.getUserIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
            groupSendTask.setDepartmentId(gson.toJson(vo.getDepartmentIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
            groupSendTask.setTagId(gson.toJson(vo.getTagIds()));
        }
        if (vo.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()){
            groupSendTask.setGroupMsgSenderIds(gson.toJson(vo.getOwnerList()));
        }

        groupSendTask.setSendRange(vo.getSendRange());
        if (CollectionUtils.isNotEmpty(vo.getMarketingUserGroupIds())) {
            groupSendTask.setMarketingUserGroupIds(gson.toJson(vo.getMarketingUserGroupIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getFilters())) {
            groupSendTask.setFilters(gson.toJson(vo.getFilters()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTagIdList())) {
            groupSendTask.setTagIdList(gson.toJson(vo.getTagIdList()));
        }
        if (CollectionUtils.isNotEmpty(vo.getQywxGroupList()) && QywxSendFilterGroupEnum.OPEN.getType().equals(vo.getFilterQywxGroupUser())) {
            groupSendTask.setQywxGroupList(gson.toJson(vo.getQywxGroupList()));
        }
        if (vo.getType().equals(QywxGroupSendTypeEnum.IMMEDIATELY_SEND.getType())) {
            groupSendTask.setFixedTime(new DateTime().plusSeconds(15).getMillis());
        } else {
            groupSendTask.setFixedTime(vo.getFixedTime());
        }
        groupSendTask.setSendType(vo.getType());
        if (marketingActivityAuditManager.isNeedAudit(vo.getEnterpriseAccount())) {
            groupSendTask.setSendType(2);
        }
        if (CollectionUtils.isNotEmpty(vo.getChatGroupFilters())) {
            groupSendTask.setChatGroupFilters(gson.toJson(vo.getChatGroupFilters()));
        }
        groupSendTask.setStatus(QywxGroupSendTaskStatusEnum.WAITING.getStatus());
        groupSendTask.setCreateTime(new Date());
        groupSendTask.setUpdateTime(new Date());
        /*String wxUserId = qywxUserManager.getQyUserIdByFsUserInfo(vo.getEnterpriseAccount(), vo.getUserId());
        if (StringUtils.isBlank(wxUserId)) {
            return null;
        }
        groupSendTask.setSender(wxUserId);*/
        if (vo.getMsgType().intValue() == QywxGroupSendMsgTypeEnum.IMAGE.getType() && vo.getImage() != null) {
            if (StringUtils.isNotBlank(vo.getImage().getMaterialId()) && vo.getImage().getMaterialType() != null) {
                int objectType = objectManager.convertNoticeContentTypeToObjectType(vo.getImage().getMaterialType());
                groupSendTask.setObjectId(vo.getImage().getMaterialId());
                groupSendTask.setObjectType(objectType);
            }
            groupSendTask.setImagePath(vo.getImage().getImagePath());
        }
        if (vo.getMsgType().intValue() == QywxGroupSendMsgTypeEnum.LINK.getType() && vo.getLink() != null) {
            groupSendTask.setLinkTitle(vo.getLink().getTitle());
            groupSendTask.setLinkPicPath(vo.getLink().getPicPath());
            groupSendTask.setLinkDesc(vo.getLink().getDesc());
            groupSendTask.setLinkUrl(vo.getLink().getUrl());
        }
        if (vo.getMsgType().intValue() == QywxGroupSendMsgTypeEnum.MINIPROGRAM.getType() && vo.getMiniprogram() != null) {
            if (vo.getMiniprogram().getMiniProgramType() != null && MiniProgramTypeEnum.OUTLINK.getType().equals(vo.getMiniprogram().getMiniProgramType())) {
                groupSendTask.setMiniPage(vo.getMiniprogram().getPage());
                groupSendTask.setAppId(vo.getMiniprogram().getAppId());
                groupSendTask.setMiniPicPath(vo.getMiniprogram().getPicPath());
                groupSendTask.setMiniTitle(vo.getMiniprogram().getTitle());
            } else {
                vo.getMiniprogram().setPage(vo.getMiniprogram().getPage() + "&marketingActivityId=" + marketingActivityId + "&marketingEventId=" + marketingEventId);
                int objectType = objectManager.convertNoticeContentTypeToObjectType(vo.getMiniprogram().getMaterialType());
                groupSendTask.setObjectType(objectType);
                groupSendTask.setObjectId(vo.getMiniprogram().getMaterialId());
                groupSendTask.setMiniTitle(vo.getMiniprogram().getTitle());
                groupSendTask.setMiniPicPath(vo.getMiniprogram().getPicPath());
                groupSendTask.setMiniPage(vo.getMiniprogram().getPage());
                if (Strings.isNullOrEmpty(vo.getMiniprogram().getPicPath())) {
                    List<PhotoEntity> photoEntities = null;
                    String hexagonCover = null;
                    if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                        photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), vo.getMiniprogram().getMaterialId(),ea);
                    } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                        photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), vo.getMiniprogram().getMaterialId(),ea);
                    } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                        photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), vo.getMiniprogram().getMaterialId(),ea);

                    } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                        List<String> siteIds = Lists.newArrayList();
                        siteIds.add(vo.getMiniprogram().getMaterialId());
                        List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds,ea);
                        if (CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                            hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                        }
                    }
                    if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                        groupSendTask.setMiniPicPath(hexagonCover);
                    } else {
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            groupSendTask.setMiniPicPath(photoEntities.get(0).getPath());
                        }
                    }
                }
            }
        }

        //企微群发支持多附件
        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            for (QywxAttachmentsVO attachmentvo : vo.getQywxAttachmentsVO()) {
                //处理小程序内容
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.MINIPROGRAM.getType() && attachmentvo.getMiniprogram() != null && attachmentvo.getMiniprogram().getMiniProgramType()==MiniProgramTypeEnum.CONTENT.getType()) {
                    attachmentvo.getMiniprogram().setPage(attachmentvo.getMiniprogram().getPage() + "&marketingActivityId=" + marketingActivityId + "&marketingEventId=" + marketingEventId);
                    int objectType = objectManager.convertNoticeContentTypeToObjectType(attachmentvo.getMiniprogram().getMaterialType());
                    attachmentvo.getMiniprogram().setObjectType(objectType);
                    if(attachmentvo.getMiniprogram().getObjectType()!=null && StringUtils.isNotBlank(attachmentvo.getMiniprogram().getMaterialId())){
                        attachmentvo.getMiniprogram().setObjectName(objectManager.getObjectName(attachmentvo.getMiniprogram().getMaterialId(),attachmentvo.getMiniprogram().getObjectType(), ea));
                    }
                    if (Strings.isNullOrEmpty(attachmentvo.getMiniprogram().getPicPath())) {
                        List<PhotoEntity> photoEntities = null;
                        String hexagonCover = null;
                        if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId(),ea);
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId(),ea);
                        } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), attachmentvo.getMiniprogram().getMaterialId(),ea);

                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> siteIds = Lists.newArrayList();
                            siteIds.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds,ea);
                            if (CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        }
                        if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            attachmentvo.getMiniprogram().setPicPath(hexagonCover);
                        } else {
                            if (CollectionUtils.isNotEmpty(photoEntities)) {
                                attachmentvo.getMiniprogram().setPicPath(photoEntities.get(0).getPath());
                            }
                        }
                    }

                }
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.H5.getType() && attachmentvo.getLink()!=null) {
                    if(StringUtils.isNotBlank(attachmentvo.getLink().getUrl())){
                        String url = attachmentvo.getLink().getUrl();
                        url = ReplaceUtil.replaceWxAppId(url,"");
                        url = ReplaceUtil.replaceMarketingActivityId(url,marketingActivityId);
                        url = ReplaceUtil.replaceMarketingEventId(url,marketingEventId);
                        attachmentvo.getLink().setUrl(url);
                    }
                    if(attachmentvo.getLink().getObjectType()!=null && StringUtils.isNotBlank(attachmentvo.getLink().getMaterialId())){
                        attachmentvo.getLink().setObjectName(objectManager.getObjectName(attachmentvo.getLink().getMaterialId(),attachmentvo.getLink().getObjectType(), ea));
                    }
                }
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.VIDEO.getType() && attachmentvo.getVideo()!=null&&StringUtils.isNotBlank(attachmentvo.getVideo().getVideoUrl())) {
                    String url = attachmentvo.getVideo().getVideoUrl();
                    url = ReplaceUtil.replaceWxAppId(url,"");
                    url = ReplaceUtil.replaceMarketingActivityId(url,marketingActivityId);
                    url = ReplaceUtil.replaceMarketingEventId(url,marketingEventId);
                    attachmentvo.getVideo().setVideoUrl(url);
                }
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.FILE.getType() && attachmentvo.getFile()!=null&&StringUtils.isNotBlank(attachmentvo.getFile().getFileUrl())) {
                    String url = attachmentvo.getFile().getFileUrl();
                    url = ReplaceUtil.replaceMarketingActivityId(url,marketingActivityId);
                    url = ReplaceUtil.replaceMarketingEventId(url,marketingEventId);
                    attachmentvo.getFile().setFileUrl(url);
                }
            }
        }

        QywxAttachmentsRelationEntity qywxAttachmentsRelationEntity = new QywxAttachmentsRelationEntity();
        qywxAttachmentsRelationEntity.setId(UUIDUtil.getUUID());
        qywxAttachmentsRelationEntity.setEa(groupSendTask.getEa());
        qywxAttachmentsRelationEntity.setTargetId(groupSendTask.getId());
        qywxAttachmentsRelationEntity.setTargetType(QywxAttachmentScenesTypeEnum.QYWX_GROUP_SEND.getType());
        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            qywxAttachmentsRelationEntity.setAttachments(gson.toJson(vo.getQywxAttachmentsVO()));
        }
        qywxAttachmentsRelationDAO.insert(qywxAttachmentsRelationEntity);

        sendTaskDAO.insertEntity(groupSendTask);
        return groupSendTask.getId();
    }

    public String updateGroupSendMsgTask(QywxGroupSendMessageVO vo, String marketingActivityId, String marketingEventId, String ea) {
        QywxGroupSendTaskEntity groupSendTask = new QywxGroupSendTaskEntity();
        groupSendTask.setFsUserId(vo.getUserId());
        groupSendTask.setMsgType(vo.getMsgType());
        groupSendTask.setMarketingActivityId(marketingActivityId);
        groupSendTask.setTitle(vo.getTitle());
        groupSendTask.setFilterNDaySentUser(vo.getFilterNDaySentUser());
        if (vo.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()){
            groupSendTask.setChatType( QywxGroupSendOjbectTypeEnum.SEND_TO_GROUP.getType());
        }else {
            groupSendTask.setChatType( QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType());
        }

        if (vo.getText() != null) {
            groupSendTask.setContent(vo.getText().getContent());
        }
        if (groupSendTask.getContent() == null){
            groupSendTask.setContent("");
        }

        if (CollectionUtils.isNotEmpty(vo.getUserIds())) {
            groupSendTask.setUserId(gson.toJson(vo.getUserIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
            groupSendTask.setDepartmentId(gson.toJson(vo.getDepartmentIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
            groupSendTask.setTagId(gson.toJson(vo.getTagIds()));
        }
        if (vo.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()){
            groupSendTask.setGroupMsgSenderIds(gson.toJson(vo.getOwnerList()));
        }

        groupSendTask.setSendRange(vo.getSendRange());
        if (CollectionUtils.isNotEmpty(vo.getMarketingUserGroupIds())) {
            groupSendTask.setMarketingUserGroupIds(gson.toJson(vo.getMarketingUserGroupIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getFilters())) {
            groupSendTask.setFilters(gson.toJson(vo.getFilters()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTagIdList())) {
            groupSendTask.setTagIdList(gson.toJson(vo.getTagIdList()));
        }
        if (CollectionUtils.isNotEmpty(vo.getQywxGroupList()) && 1==vo.getFilterQywxGroupUser()) {
            groupSendTask.setQywxGroupList(gson.toJson(vo.getQywxGroupList()));
        }
        if (vo.getType().equals(QywxGroupSendTypeEnum.IMMEDIATELY_SEND.getType())) {
            groupSendTask.setFixedTime(new DateTime().plusSeconds(15).getMillis());
        } else {
            groupSendTask.setFixedTime(vo.getFixedTime());
        }
        if (CollectionUtils.isNotEmpty(vo.getChatGroupFilters())) {
            groupSendTask.setChatGroupFilters(gson.toJson(vo.getChatGroupFilters()));
        }
        groupSendTask.setAllowSelect(vo.getAllowSelect());
        groupSendTask.setSendType(vo.getType());
        groupSendTask.setStatus(QywxGroupSendTaskStatusEnum.WAITING.getStatus());
        groupSendTask.setCreateTime(new Date());
        groupSendTask.setUpdateTime(new Date());
        /*String wxUserId = qywxUserManager.getQyUserIdByFsUserInfo(vo.getEnterpriseAccount(), vo.getUserId());
        if (StringUtils.isBlank(wxUserId)) {
            return null;
        }
        groupSendTask.setSender(wxUserId);*/
        if (vo.getMsgType().intValue() == QywxGroupSendMsgTypeEnum.IMAGE.getType() &&  vo.getImage() != null) {
            if (StringUtils.isNotBlank(vo.getImage().getMaterialId()) && vo.getImage().getMaterialType() != null) {
                int objectType = objectManager.convertNoticeContentTypeToObjectType(vo.getImage().getMaterialType());
                groupSendTask.setObjectId(vo.getImage().getMaterialId());
                groupSendTask.setObjectType(objectType);
            }
            groupSendTask.setImagePath(vo.getImage().getImagePath());
        }
        if (vo.getMsgType().intValue() == QywxGroupSendMsgTypeEnum.LINK.getType() &&  vo.getLink() != null){
            groupSendTask.setLinkTitle(vo.getLink().getTitle());
            groupSendTask.setLinkPicPath(vo.getLink().getPicPath());
            groupSendTask.setLinkDesc(vo.getLink().getDesc());
            groupSendTask.setLinkUrl(vo.getLink().getUrl());
        }
        if (vo.getMsgType().intValue() == QywxGroupSendMsgTypeEnum.MINIPROGRAM.getType() && vo.getMiniprogram() != null) {
            if (vo.getMiniprogram().getMiniProgramType() != null && 2 == vo.getMiniprogram().getMiniProgramType()) {
                groupSendTask.setMiniPage(vo.getMiniprogram().getPage());
                groupSendTask.setAppId(vo.getMiniprogram().getAppId());
                groupSendTask.setMiniPicPath(vo.getMiniprogram().getPicPath());
                groupSendTask.setMiniTitle(vo.getMiniprogram().getTitle());
            } else {
                vo.getMiniprogram().setPage(vo.getMiniprogram().getPage() + "&marketingActivityId=" + marketingActivityId + "&marketingEventId=" + marketingEventId);
                int objectType = objectManager.convertNoticeContentTypeToObjectType(vo.getMiniprogram().getMaterialType());
                groupSendTask.setObjectType(objectType);
                groupSendTask.setObjectId(vo.getMiniprogram().getMaterialId());
                groupSendTask.setMiniTitle(vo.getMiniprogram().getTitle());
                groupSendTask.setMiniPicPath(vo.getMiniprogram().getPicPath());
                groupSendTask.setMiniPage(vo.getMiniprogram().getPage());
                if (Strings.isNullOrEmpty(vo.getMiniprogram().getPicPath())) {
                    List<PhotoEntity> photoEntities = null;
                    String hexagonCover = null;
                    if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                        photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), vo.getMiniprogram().getMaterialId(),ea);
                    } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                        photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), vo.getMiniprogram().getMaterialId(),ea);
                    } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                        photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), vo.getMiniprogram().getMaterialId(),ea);

                    }else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()){
                        List<String> siteIds = Lists.newArrayList();
                        siteIds.add(vo.getMiniprogram().getMaterialId());
                        List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds,ea);
                        if (CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)){
                            hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                        }
                    }
                    if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()){
                        groupSendTask.setMiniPicPath(hexagonCover);
                    }else {
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            groupSendTask.setMiniPicPath(photoEntities.get(0).getPath());
                        }
                    }
                }
            }
        }



        //企微群发支持多附件
        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            for (QywxAttachmentsVO attachmentvo : vo.getQywxAttachmentsVO()) {
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.MINIPROGRAM.getType() && attachmentvo.getMiniprogram() != null&& attachmentvo.getMiniprogram().getMiniProgramType()==MiniProgramTypeEnum.CONTENT.getType()) {
                    attachmentvo.getMiniprogram().setPage(attachmentvo.getMiniprogram().getPage() + "&marketingActivityId=" + marketingActivityId + "&marketingEventId=" + marketingEventId);
                    int objectType = objectManager.convertNoticeContentTypeToObjectType(attachmentvo.getMiniprogram().getMaterialType());
                    attachmentvo.getMiniprogram().setObjectType(objectType);
                    if (Strings.isNullOrEmpty(attachmentvo.getMiniprogram().getPicPath())) {
                        List<PhotoEntity> photoEntities = null;
                        String hexagonCover = null;
                        if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId(),ea);
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId(),ea);
                        } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), attachmentvo.getMiniprogram().getMaterialId(),ea);

                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> siteIds = Lists.newArrayList();
                            siteIds.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds,ea);
                            if (CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        }
                        if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            attachmentvo.getMiniprogram().setPicPath(hexagonCover);
                        } else {
                            if (CollectionUtils.isNotEmpty(photoEntities)) {
                                attachmentvo.getMiniprogram().setPicPath(photoEntities.get(0).getPath());
                            }
                        }
                    }
                }
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.H5.getType() && attachmentvo.getLink()!=null&&StringUtils.isNotBlank(attachmentvo.getLink().getUrl())) {
                    String url = attachmentvo.getLink().getUrl();
                    url = ReplaceUtil.replaceWxAppId(url,"");
                    url = ReplaceUtil.replaceMarketingActivityId(url,marketingActivityId);
                    url = ReplaceUtil.replaceMarketingActivityId(url,marketingActivityId);
                    attachmentvo.getLink().setUrl(url);
                }
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.VIDEO.getType() && attachmentvo.getVideo()!=null&&StringUtils.isNotBlank(attachmentvo.getVideo().getVideoUrl())) {
                    String url = attachmentvo.getVideo().getVideoUrl();
                    url = ReplaceUtil.replaceWxAppId(url,"");
                    url = ReplaceUtil.replaceMarketingActivityId(url,marketingActivityId);
                    url = ReplaceUtil.replaceMarketingEventId(url,marketingEventId);
                    attachmentvo.getVideo().setVideoUrl(url);
                }
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.FILE.getType() && attachmentvo.getFile()!=null&&StringUtils.isNotBlank(attachmentvo.getFile().getFileUrl())) {
                    String url = attachmentvo.getFile().getFileUrl();
                    url = ReplaceUtil.replaceMarketingActivityId(url,marketingActivityId);
                    url = ReplaceUtil.replaceMarketingEventId(url,marketingEventId);
                    attachmentvo.getFile().setFileUrl(url);
                }
            }
        }
        QywxGroupSendTaskEntity byMarketingActivityId = sendTaskDAO.getByMarketingActivityId(marketingActivityId, vo.getEnterpriseAccount());
        QywxAttachmentsRelationEntity attachmentsRelationEntities = qywxAttachmentsRelationDAO.getDetailByTargetId(ea, byMarketingActivityId.getId(), QywxAttachmentScenesTypeEnum.QYWX_GROUP_SEND.getType());
        if(Objects.isNull(attachmentsRelationEntities)){
            QywxAttachmentsRelationEntity qywxAttachmentsRelationEntity = new QywxAttachmentsRelationEntity();
            qywxAttachmentsRelationEntity.setId(UUIDUtil.getUUID());
            qywxAttachmentsRelationEntity.setEa(byMarketingActivityId.getEa());
            qywxAttachmentsRelationEntity.setTargetId(byMarketingActivityId.getId());
            qywxAttachmentsRelationEntity.setTargetType(QywxAttachmentScenesTypeEnum.QYWX_GROUP_SEND.getType());
            if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
                qywxAttachmentsRelationEntity.setAttachments(gson.toJson(vo.getQywxAttachmentsVO()));
            }
            qywxAttachmentsRelationDAO.insert(qywxAttachmentsRelationEntity);
        }else {
            qywxAttachmentsRelationDAO.updateByTargetId(ea, CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())?JSON.toJSONString(vo.getQywxAttachmentsVO()):null,byMarketingActivityId.getId(),QywxAttachmentScenesTypeEnum.QYWX_GROUP_SEND.getType());
        }
//        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
//            qywxAttachmentsRelationDAO.updateByTargetId(JSON.toJSONString(vo.getQywxAttachmentsVO()),byMarketingActivityId.getId(),QywxAttachmentScenesTypeEnum.QYWX_GROUP_SEND.getType());
//        }else {
//            qywxAttachmentsRelationDAO.updateByTargetId(null,byMarketingActivityId.getId(),QywxAttachmentScenesTypeEnum.QYWX_GROUP_SEND.getType());
//        }

        sendTaskDAO.updateEntity(groupSendTask);
        return byMarketingActivityId.getId();
    }


    public void groupSendMsgSchedule(String ea) {
        try {
            List<QywxGroupSendTaskEntity> needSendTaskList = sendTaskDAO.getNeedSendTaskByEa(ea);
            if (CollectionUtils.isEmpty(needSendTaskList)) {
                return;
            }
            if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null) {
                log.info("GroupSendMessageManager.groupSendMsgSchedule failed enterprise stop or license expire ea:{}", ea);
                return;
            }
            needSendTaskList.forEach(groupSendTaskEntity -> {

                if (MarketingJobUtil.isMarketingJobForbidExec(groupSendTaskEntity.getEa())) {
                    log.warn("当前时间禁止发送营销消息, QywxGroupSendTaskEntity: {}", groupSendTaskEntity);
                    sendTaskDAO.updateStatusAndErrorCodeById(groupSendTaskEntity.getId(), QywxGroupSendTaskStatusEnum.FAILED.getStatus(), SHErrorCode.FORBID_SEND_MARKETING_MESSAGE.getErrorCode(), ea);
                    return;
                }
                ThreadPoolUtils.execute(() -> handlerSendTask(groupSendTaskEntity), ThreadPoolTypeEnums.QYWX_MSG_SEND);
            });
        } catch (Exception e) {
            log.warn("GroupSendMessageManager groupSendMsgSchedule exception:{}", e.fillInStackTrace());
        }
    }

    public void groupSendMsgById(String id, String ea) {
        try {
            QywxGroupSendTaskEntity groupSendTaskEntity = sendTaskDAO.queryById(id, ea);
            if (groupSendTaskEntity == null) {
                return;
            }
            handlerSendTask(groupSendTaskEntity);
        } catch (Exception e) {
            log.warn("GroupSendMessageManager groupSendMsgSchedule exception:{}", e.fillInStackTrace());
        }
    }

    public void handlerSendTask(QywxGroupSendTaskEntity groupSendTask) {
        if (groupSendTask == null) {
            return;
        }

        boolean isNormall = marketingActivityAuditManager.checkAudtStatus(groupSendTask.getEa(), groupSendTask.getMarketingActivityId());
        if (!isNormall) {
            return;
        }

        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(groupSendTask.getEa());

        if (agentConfig == null) {
            log.warn("GroupSendMessageManager handlerSendTask agentConfig is null, groupSendTask={}", groupSendTask);
            return;
        }

        boolean updateResult = sendTaskDAO.updateTaskStatusToRunningById(groupSendTask.getId(), QywxGroupSendTaskStatusEnum.WAITING.getStatus(), groupSendTask.getEa());
        if (!updateResult) {
            log.info("GroupSendMessageManager handlerSendTask updateResult is false, groupSendTask:{}", groupSendTask);
            return;
        }
        marketingActivityManager.updateMarketingActivityStatus(groupSendTask.getEa(), groupSendTask.getMarketingActivityId(), String.valueOf(SendStatusEnum.PROCESSING.getStatus()));
        AddMsgTemplateArg arg = new AddMsgTemplateArg();
        String accessToken = qywxManager.getAccessToken(groupSendTask.getEa());
        QywxAttachmentsRelationEntity attachmentsRelation = qywxAttachmentsRelationDAO.getDetailByTargetId(groupSendTask.getEa(), groupSendTask.getId(), QywxAttachmentScenesTypeEnum.QYWX_GROUP_SEND.getType());

        //群发支持多附件
        if(Objects.nonNull(attachmentsRelation)){
            try {
                List<SendWelcomeMessageNewArg.Attachment> attachmentList = qywxManager.builtNewAttachmentsArg(groupSendTask.getEa(), accessToken, attachmentsRelation,null,null,null);
                if (CollectionUtils.isEmpty(attachmentList)) {
                    log.warn("GroupSendMessageManager builtNewAttachmentsArg fail,attachmentsRelation={}", attachmentsRelation);
                }
                arg.setAttachments(attachmentList);
                if (groupSendTask.getContent() != null) {
                    AddMsgTemplateArg.Text text = new AddMsgTemplateArg.Text();
                    if (StringUtils.isNotBlank(groupSendTask.getContent())) {
                        byte[] bytes = groupSendTask.getContent().getBytes("utf-8");
                        if (bytes.length > 4000) {
                            String substr = cutStringByU8(groupSendTask.getContent(), 4000);
                            text.setContent(substr);
                        } else {
                            text.setContent(groupSendTask.getContent());
                        }
                    }
                    arg.setText(text);
                }
            } catch (Exception e) {
                log.warn("GroupSendMessageManager handlerSendTask happen exception, groupSendTask={}, exception : {}", groupSendTask, e.fillInStackTrace());
                sendTaskDAO.updateStatusById(groupSendTask.getId(), QywxGroupSendTaskStatusEnum.FAILED.getStatus(), groupSendTask.getEa());
                marketingActivityManager.updateMarketingActivityStatus(groupSendTask.getEa(), groupSendTask.getMarketingActivityId(), String.valueOf(SendStatusEnum.FAIL.getStatus()));
                return;
            }
        }else {
            try {
                builtOldAttachmentsArg(groupSendTask, arg,accessToken);
            } catch (Exception e) {
                log.warn("GroupSendMessageManager handlerSendTask happen exception, groupSendTask={}, exception : {}", groupSendTask, e.fillInStackTrace());
                sendTaskDAO.updateStatusById(groupSendTask.getId(), QywxGroupSendTaskStatusEnum.FAILED.getStatus(), groupSendTask.getEa());
                marketingActivityManager.updateMarketingActivityStatus(groupSendTask.getEa(), groupSendTask.getMarketingActivityId(), String.valueOf(SendStatusEnum.FAIL.getStatus()));
                return;
            }
        }

        List<String> senderIds = null;
        int batchCount = 0;
        int failedCount = 0;
        List<String> msgIdList = new ArrayList<>();
        List<String> failList = new ArrayList<>();
        AddMsgTemplateResult errorResult = new AddMsgTemplateResult();
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(groupSendTask.getEa(), groupSendTask.getMarketingActivityId());

        try {
            boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(groupSendTask.getEa()) ;
            // 根据激活状态过滤
            boolean shouldFiltering = qywxActivatedAccountManager.shouldFiltering(groupSendTask.getEa());
            if (groupSendTask.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
                arg.setChatType(QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getDesc());
                //客户群发是否允许成员在待发送客户列表中重新进行选择
                if (groupSendTask.getAllowSelect() != null) {
                    arg.setAllowSelect(groupSendTask.getAllowSelect());
                }

                List<QuerySendGroupMessageDataVO.Filter> filters = gson.fromJson(groupSendTask.getFilters(), new TypeToken<List<QuerySendGroupMessageDataVO.Filter>>() {}.getType());
                List<TagName> tagIdList = gson.fromJson(groupSendTask.getTagIdList(), new TypeToken<List<TagName>>() {}.getType());
                List<String> marketingUserGroupIds = gson.fromJson(groupSendTask.getMarketingUserGroupIds(), new TypeToken<List<String>>() {}.getType());
                List<String> userIdList = gson.fromJson(groupSendTask.getUserId(), new TypeToken<List<String>>() {}.getType());
                List<Integer> departmentIds = gson.fromJson(groupSendTask.getDepartmentId(), new TypeToken<List<Integer>>() {}.getType());
                List<Integer> tagIds = gson.fromJson(groupSendTask.getTagId(), new TypeToken<List<Integer>>() {}.getType());

                boolean isFilterNDay = groupSendTask.getFilterNDaySentUser() != null && groupSendTask.getFilterNDaySentUser() > 0;
                boolean isFilterGroupUser = StringUtils.isNotBlank(groupSendTask.getQywxGroupList());
                if (!shouldFiltering && !isOpen && userIdList != null && userIdList.size() == 1 && "-999999".equals(userIdList.get(0))) {
                    // 全部员工发送全部对象
                    // 全部员工发送指定对象
                    List<String> externalUserIds = this.getExternalUserIds(groupSendTask, filters, tagIdList, marketingUserGroupIds);
                    externalUserIds = enterpriseSpreadRecordManager.filterAndUpsert(externalUserIds, MarketingActivityActionEnum.QYWX_GROUP_SEND.getSpreadType(), groupSendTask.getEa(), groupSendTask.getFilterNDaySentUser());
                    if (isFilterGroupUser) {
                        List<String> groupIds = gson.fromJson(groupSendTask.getQywxGroupList(), new TypeToken<List<String>>() {}.getType());
                        externalUserIds = wechatGroupObjDescribeManager.filterGroupId(externalUserIds, groupSendTask.getEa(), groupIds);
                    }
                    List<List<String>> partition = Lists.partition(externalUserIds, 10000);
                    batchCount += partition.size();
                    for (List<String> partExternalUserIds : partition) {
                        arg.setExternalUserid(partExternalUserIds);
                        AddMsgTemplateResult addMsgResult = addMsgTemplate(qywxManager.getAccessToken(groupSendTask.getEa()), arg);
                        if (addMsgResult == null || addMsgResult.getErrcode() != 0) {
                            failedCount++;
                            if (addMsgResult != null) {
                                errorResult = addMsgResult;
                            }
                        } else {
                            msgIdList.add(addMsgResult.getMsgid());
                            failList.addAll(addMsgResult.getFailList() != null ? addMsgResult.getFailList() : Lists.newArrayList());
                        }
                    }
                } else if ((!isFilterNDay || !isFilterGroupUser) && !isOpen && groupSendTask.getSendRange() == QywxGroupSendRangeTypeEnum.ALL.getType()){
                    List<String> externalUserIds = this.getExternalUserIds(groupSendTask, filters, tagIdList, marketingUserGroupIds);
                    enterpriseSpreadRecordManager.filterAndUpsert(externalUserIds, MarketingActivityActionEnum.QYWX_GROUP_SEND.getSpreadType(), groupSendTask.getEa(), groupSendTask.getFilterNDaySentUser());
                    // 指定员工发送全部对象，externalUserid不传
                    senderIds = qywxManager.handleQywxEmployeeUserId(groupSendTask.getEa(), userIdList, departmentIds, tagIds);
                    batchCount = senderIds.size();

                    if (shouldFiltering) {
                        List<String> activatedUserIds = qywxActivatedAccountManager.getActivatedUserIds(groupSendTask.getEa());
                        senderIds = senderIds.stream().filter(activatedUserIds::contains).collect(Collectors.toList());
                    }

                    for (String senderId : senderIds) {
                        arg.setSender(senderId);

                        if (CollectionUtils.isNotEmpty(arg.getAttachments())) {
                            // 外部内容添加活动参数
                            try {
                                QyWxAddressBookEntity qyWxAddressBook = qywxAddressBookManager.queryByEaAndUserId(groupSendTask.getEa(), senderId);
                                OutLinkMktParamManager.MktParam mktParam = new OutLinkMktParamManager.MktParam(marketingActivityExternalConfigEntity.getMarketingEventId(), groupSendTask.getMarketingActivityId(), senderId, qyWxAddressBook.getMobile());
                                outLinkMktParamManager.attachmentAppendMktParam(arg.getAttachments(), mktParam);
                            } catch (Exception e) {
                                log.warn("attachmentAppendMktParam fail e:", e);
                            }
                        }

                        AddMsgTemplateResult addMsgResult = addMsgTemplate(qywxManager.getAccessToken(groupSendTask.getEa()), arg);
                        if (addMsgResult == null || addMsgResult.getErrcode() != 0) {
                            failedCount++;
                            if (addMsgResult != null) {
                                errorResult = addMsgResult;
                            }
                        } else {
                            msgIdList.add(addMsgResult.getMsgid());
                            failList.addAll(addMsgResult.getFailList() != null ? addMsgResult.getFailList() : Lists.newArrayList());
                        }
                    }
                } else {
                    // 指定员工发送指定对象，交叉计算，都传
                    List<Integer> dataPermission = null;
                    if (isOpen) {
                        // 权限交叉部门id
                        dataPermission = dataPermissionManager.filterUserAccessibleQywxDeptIds(groupSendTask.getEa(), groupSendTask.getFsUserId(), departmentIds);
                        senderIds = qywxManager.handleQywxEmployeeUserId(groupSendTask.getEa(),userIdList, dataPermission, null);
                    } else {
                        senderIds = qywxManager.handleQywxEmployeeUserId(groupSendTask.getEa(), userIdList, departmentIds, tagIds);
                    }
                    Map<String, Set<String>> employeeExternalUserIds = this.getEmployeeExternalUserIds(groupSendTask.getEa(), senderIds, isOpen, dataPermission);
                    List<String> externalUserIds = this.getExternalUserIds(groupSendTask, filters, tagIdList, marketingUserGroupIds);
                    externalUserIds = enterpriseSpreadRecordManager.filterAndUpsert(externalUserIds, MarketingActivityActionEnum.QYWX_GROUP_SEND.getSpreadType(), groupSendTask.getEa(), groupSendTask.getFilterNDaySentUser());
                    if (StringUtils.isNotBlank(groupSendTask.getQywxGroupList())) {
                        List<String> groupIds = gson.fromJson(groupSendTask.getQywxGroupList(), new TypeToken<List<String>>() {}.getType());
                        externalUserIds = wechatGroupObjDescribeManager.filterGroupId(externalUserIds, groupSendTask.getEa(), groupIds);
                    }

                    if (shouldFiltering) {
                        List<String> activatedUserIds = qywxActivatedAccountManager.getActivatedUserIds(groupSendTask.getEa());
                        senderIds = senderIds.stream().filter(activatedUserIds::contains).collect(Collectors.toList());
                    }

                    for (String senderId : senderIds) {
                        List<String> employee2externalUserIds;
                        Set<String> employeeExternalUserIdSet = employeeExternalUserIds.get(senderId);
                        if (CollectionUtils.isNotEmpty(employeeExternalUserIdSet)) {
                            employee2externalUserIds = new ArrayList<>(CollectionUtils.intersection(externalUserIds, employeeExternalUserIdSet));
                        } else {
                            continue;
                        }

                        List<List<String>> partition = Lists.partition(employee2externalUserIds, 10000);
                        batchCount += partition.size();

                        for (List<String> partExternalUserIds : partition) {
                            arg.setExternalUserid(partExternalUserIds);
                            arg.setSender(senderId);

                            if (CollectionUtils.isNotEmpty(arg.getAttachments())) {
                                // 外部内容添加活动参数
                                try {
                                    QyWxAddressBookEntity qyWxAddressBook = qywxAddressBookManager.queryByEaAndUserId(groupSendTask.getEa(), senderId);
                                    OutLinkMktParamManager.MktParam mktParam = new OutLinkMktParamManager.MktParam(marketingActivityExternalConfigEntity.getMarketingEventId(), groupSendTask.getMarketingActivityId(), senderId, qyWxAddressBook.getMobile());
                                    outLinkMktParamManager.attachmentAppendMktParam(arg.getAttachments(), mktParam);
                                } catch (Exception e) {
                                    log.warn("attachmentAppendMktParam fail e:", e);
                                }
                            }

                            AddMsgTemplateResult addMsgResult = addMsgTemplate(qywxManager.getAccessToken(groupSendTask.getEa()), arg);

                            if (addMsgResult == null || addMsgResult.getErrcode() != 0) {
                                failedCount++;
                                if (addMsgResult != null) {
                                    errorResult = addMsgResult;
                                }
                            } else {
                                msgIdList.add(addMsgResult.getMsgid());
                                failList.addAll(addMsgResult.getFailList() != null ? addMsgResult.getFailList() : Lists.newArrayList());
                            }
                        }
                    }
                }

            } else {
                List<String> ownerList = gson.fromJson(groupSendTask.getGroupMsgSenderIds(), new TypeToken<List<String>>() {
                }.getType());
                List<Integer> groupMsgSenderBydepartmentIdList = gson.fromJson(groupSendTask.getDepartmentId(), new TypeToken<List<Integer>>() {
                }.getType());
                List<Integer> groupMsgSenderByTagIdList = gson.fromJson(groupSendTask.getTagId(), new TypeToken<List<Integer>>() {
                }.getType());
                List<String> groupMsgSenderIds = qywxManager.handleQywxOwnerUserId(groupSendTask.getEa(), groupSendTask.getFsUserId(), ownerList, groupMsgSenderBydepartmentIdList, groupMsgSenderByTagIdList);
                //添加客户群筛选条件
                List<Map<String, Object>> chatGroupFilters = adapterGson.fromJson(groupSendTask.getChatGroupFilters(), new TypeToken<List<Map<String, Object>>>() {
                }.getType());
                boolean flag = false;
                Map<String, List<String>> chatGroupIdListMap = this.getChatGroupIdListByFilters(groupSendTask.getEa(), chatGroupFilters,groupSendTask.getFsUserId());
                if (CollectionUtils.isEmpty(groupMsgSenderIds) && chatGroupIdListMap != null) {
                    groupMsgSenderIds.addAll(chatGroupIdListMap.keySet());
                    flag = true;
                }
                if (shouldFiltering) {
                    List<String> activatedUserIds = qywxActivatedAccountManager.getActivatedUserIds(groupSendTask.getEa());
                    groupMsgSenderIds = groupMsgSenderIds.stream().filter(activatedUserIds::contains).collect(Collectors.toList());
                }
                batchCount = groupMsgSenderIds.size();
                List<QywxGroupSendGroupFlatResultEntity> flatResultEntities = Lists.newArrayList();
                for (String senderId : groupMsgSenderIds) {
                    AddMsgTemplateArg groupArg = BeanUtil.copy(arg, AddMsgTemplateArg.class);
                    groupArg.setChatType(QywxGroupSendOjbectTypeEnum.SEND_TO_GROUP.getDesc());
                    groupArg.setSender(senderId);
                    if (flag) {
                        groupArg.setChatIdList(chatGroupIdListMap.get(senderId));
                    }
                    AddMsgTemplateResult addMsgResult = addMsgTemplate(qywxManager.getAccessToken(groupSendTask.getEa()), groupArg);
                    QywxGroupSendGroupResultEntity qywxGroupSendGroupResultEntity = new QywxGroupSendGroupResultEntity();
                    qywxGroupSendGroupResultEntity.setId(UUIDUtil.getUUID());
                    qywxGroupSendGroupResultEntity.setEa(groupSendTask.getEa());
                    qywxGroupSendGroupResultEntity.setSendid(groupSendTask.getId());
                    qywxGroupSendGroupResultEntity.setSender(senderId);
                    qywxGroupSendGroupResultEntity.setMsgid(addMsgResult.getMsgid());
                    qywxGroupSendGroupResultEntity.setEa(groupSendTask.getEa());

                    if (addMsgResult == null || addMsgResult.getErrcode() != 0) {
                        if (addMsgResult != null) {
                            qywxGroupSendGroupResultEntity.setMsgid(addMsgResult.getMsgid());
                            qywxGroupSendGroupResultEntity.setErrcode(addMsgResult.getErrcode());
                            qywxGroupSendGroupResultEntity.setErrmsg(addMsgResult.getErrmsg());
                            errorResult = addMsgResult;
                        }
                        failedCount++;
                    }
                    CustomerGroupListResult result = customerGroupManager.queryCustomerListNew(groupSendTask.getEa(), Lists.newArrayList(senderId), 1000);
                    if (result.isSuccess()) {
                        if (result.getGroupList() != null) {
                            qywxGroupSendGroupResultEntity.setTotalGroupCount(result.getGroupList().size());
                        }
                    }
                    //如果是群筛选条件,则直接获取群数量
                    if (flag) {
                        qywxGroupSendGroupResultEntity.setTotalGroupCount(chatGroupIdListMap.get(senderId).size());
                    }
                    qywxGroupSendGroupResultDAO.insert(qywxGroupSendGroupResultEntity);

                    // 扁平化结果表同步写入
                    QywxGroupSendGroupFlatResultEntity flatResultEntity = new QywxGroupSendGroupFlatResultEntity();
                    flatResultEntity.setId(UUIDUtil.getUUID());
                    flatResultEntity.setEa(groupSendTask.getEa());
                    flatResultEntity.setSendid(qywxGroupSendGroupResultEntity.getSendid());
                    flatResultEntity.setSender(qywxGroupSendGroupResultEntity.getSender());
                    flatResultEntity.setMsgid(qywxGroupSendGroupResultEntity.getMsgid());
                    flatResultEntity.setStatus(0);
                    flatResultEntity.setSendGroupId("");
                    flatResultEntity.setCreateTime(new Date());
                    flatResultEntity.setUpdateTime(new Date());
                    flatResultEntities.add(flatResultEntity);
                }
                try {
                    qywxGroupSendGroupResultDAO.batchInsertFlatResult(flatResultEntities);
                } catch (Exception e) {
                    log.warn("batchInsertFlatResult error flatResultEntities:{}", flatResultEntities, e);
                }
            }
        } catch (Exception e) {
            log.warn("GroupSendMessageManager handlerSendTask addMsgTemplate happen exception, groupSendTask={}", groupSendTask, e);
        } finally {
            log.info("handlerSendTask finish ea:{} taskId:{} batchCount:{} failedCount:{}", groupSendTask.getEa(), groupSendTask.getId(), batchCount, failedCount);
            if (batchCount == 0) {
                log.info("qywx handlerSendTask send failed, batchCount == 0");
                sendTaskDAO.updateSendResult(groupSendTask.getId(), QywxGroupSendTaskStatusEnum.FAILED.getStatus(), 41048, null, null, null, groupSendTask.getEa());
                marketingActivityManager.updateMarketingActivityStatus(groupSendTask.getEa(), groupSendTask.getMarketingActivityId(), String.valueOf(SendStatusEnum.FAIL.getStatus()));
                return;
            }
            if (batchCount != 0 && failedCount == batchCount) {
                //所有发送失败，才认为失败，取最后一个错误描述
                sendTaskDAO.updateSendResult(groupSendTask.getId(), QywxGroupSendTaskStatusEnum.FAILED.getStatus(), errorResult.getErrcode(), errorResult.getErrmsg(), errorResult.getMsgid(), null, groupSendTask.getEa());
                marketingActivityManager.updateMarketingActivityStatus(groupSendTask.getEa(), groupSendTask.getMarketingActivityId(), String.valueOf(SendStatusEnum.FAIL.getStatus()));
                return;
            }
            if (batchCount != 0) {
                String msgId = String.join(",", msgIdList);
                sendTaskDAO.updateSendResult(groupSendTask.getId(), QywxGroupSendTaskStatusEnum.COMPLETE.getStatus(), 0, "ok", msgId, gson.toJson(failList), groupSendTask.getEa());
                marketingActivityManager.updateMarketingActivityStatus(groupSendTask.getEa(), groupSendTask.getMarketingActivityId(), String.valueOf(SendStatusEnum.FINISHED.getStatus()));
                return;
            }
        }
    }


    private void builtOldAttachmentsArg(QywxGroupSendTaskEntity groupSendTask, AddMsgTemplateArg arg, String accessToken) {
        try {
            if (groupSendTask.getContent() != null) {
                AddMsgTemplateArg.Text text = new AddMsgTemplateArg.Text();
                if (StringUtils.isNotBlank(groupSendTask.getContent())) {
                    byte[] bytes = groupSendTask.getContent().getBytes("utf-8");
                    if (bytes.length > 4000) {
                        String substr = cutStringByU8(groupSendTask.getContent(), 4000);
                        text.setContent(substr);
                    } else {
                        text.setContent(groupSendTask.getContent());
                    }
                }
                arg.setText(text);
            }
            if (groupSendTask.getMsgType() == QywxGroupSendMsgTypeEnum.IMAGE.getType()) {
                String aPath = groupSendTask.getImagePath();
                byte[] data;
                if (aPath.startsWith("A_")) {
                    data = fileV2Manager.downloadAFile(aPath, groupSendTask.getEa());
                } else {
                    data = fileV2Manager.downloadFileByUrl(aPath, groupSendTask.getEa());
                }
                AddMsgTemplateArg.Image image = new AddMsgTemplateArg.Image();


                UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, aPath, "image");
                if (mediaResult != null && mediaResult.getErrcode() == 0) {
                    image.setMediaId(mediaResult.getMediaId());
                }
                arg.setImage(image);
            }

            if (groupSendTask.getMsgType() == QywxGroupSendMsgTypeEnum.LINK.getType()) {
                AddMsgTemplateArg.Link link = new AddMsgTemplateArg.Link();
                link.setTitle(groupSendTask.getLinkTitle());
                if (StringUtils.isNotBlank(groupSendTask.getLinkDesc())) {
                    byte[] bytes = groupSendTask.getLinkDesc().getBytes("utf-8");
                    if (bytes.length > 512) {
                        String substr = cutStringByU8(groupSendTask.getLinkDesc(), 512);
                        link.setDesc(substr);
                    } else {
                        link.setDesc(groupSendTask.getLinkDesc());
                    }
                }
                link.setUrl(groupSendTask.getLinkUrl());

                String picUrl = fileV2Manager.getUrlByPath(groupSendTask.getLinkPicPath(), groupSendTask.getEa(), false);
                link.setPicUrl(picUrl);
                arg.setLink(link);
            }

            if (groupSendTask.getMsgType() == QywxGroupSendMsgTypeEnum.MINIPROGRAM.getType()) {
                AddMsgTemplateArg.Miniprogram miniprogram = new AddMsgTemplateArg.Miniprogram();
                byte[] bytes = groupSendTask.getMiniTitle().getBytes("utf-8");
                if (bytes.length > 64) {
                    String substr = cutStringByU8(groupSendTask.getMiniTitle(), 64);
                    miniprogram.setTitle(substr);
                } else {
                    miniprogram.setTitle(groupSendTask.getMiniTitle());
                }
                miniprogram.setPage(groupSendTask.getMiniPage());
                if (StringUtils.isNotBlank(groupSendTask.getAppId())) {
                    miniprogram.setAppId(groupSendTask.getAppId());
                } else {
                    QywxMiniappConfigEntity miniappConfigEntity = miniappConfigDAO.getByEa(groupSendTask.getEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(groupSendTask.getEa()));
                    miniprogram.setAppId(miniappConfigEntity.getAppid());
                }

                String apath = groupSendTask.getMiniPicPath();
                if (StringUtils.isBlank(apath)) {
                    apath = groupMessageDefaultCoverPath;
                }
                byte[] data = fileV2Manager.downloadFileByUrl(apath, groupSendTask.getEa());
                UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, apath, "image");
                if (mediaResult != null && mediaResult.getErrcode() == 0) {
                    miniprogram.setPicMediaId(mediaResult.getMediaId());
                }
                arg.setMiniprogram(miniprogram);
            }
        }catch (Exception e){

        }
    }

    private int getListTotalCount(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg) {
        paasQueryFilterArg.getQuery().addOrderByAsc(ObjectDescribeContants.ID, true);
        paasQueryFilterArg.getQuery().setOffset(0);
        paasQueryFilterArg.getQuery().setLimit(1);
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
        findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
        findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
        InnerPage<ObjectData> result = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
        if (result != null && result.getTotalCount() != null) {
            return result.getTotalCount();
        }
        return 0;
    }

    private List<ObjectData> batchGetList(String ea, Integer fsUserId, int limit, int offset, PaasQueryFilterArg paasQueryFilterArg) {
        ArrayList<ObjectData> dataList = new ArrayList<>();
        paasQueryFilterArg.getQuery().addOrderByAsc(ObjectDescribeContants.ID, true);
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
        findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
        findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
        try {
            paasQueryFilterArg.getQuery().setOffset(offset);
            paasQueryFilterArg.getQuery().setLimit(limit);
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
            findByQueryV3Arg.setNeedCount(false);
            InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
            if (pageResult != null && pageResult.getDataList() != null && !pageResult.getDataList().isEmpty()) {
                dataList.addAll(pageResult.getDataList());
            }
        } catch (Exception e) {
            log.warn("batchGetList error offset:{} arg:{}", offset, JSON.toJSONString(findByQueryV3Arg));
        }
        return dataList;
    }

    public Map<String, Set<String>> getEmployeeExternalUserIds(String ea, List<String> senderIds) {
        return this.getEmployeeExternalUserIds(ea, senderIds, false, null);
    }

    public Map<String, Set<String>> getEmployeeExternalUserIds(String ea, List<String> senderIds, boolean isOpen, List<Integer> dataPermission) {
        Map<String, Set<String>> user2ExternalUserId = Maps.newHashMap();
        //查询企微加好友记录
        Map<String, Set<String>> user2ObjIdMap = Maps.newHashMap();
        Set<String> allObjIdSet = Sets.newHashSet();
        Map<String, String> objId2ExternalUserIdMap = Maps.newHashMap();
        List<List<String>> userIdsPartition = Lists.partition(senderIds, 1000);
        for (List<String> userPartition : userIdsPartition) {
            List<String> selectFields = com.google.common.collect.Lists.newArrayList("external_user_id", "qywx_user_id");
            PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
            paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), userPartition);
            paasQueryArg.addFilter("friend_status", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Collections.singletonList("0"));
            paasQueryArg.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("default__c"));
            paasQueryArg.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, PaasAndCrmOperatorEnum.IN.getCrmOperator(), Collections.singletonList(AppScopeEnum.MARKETING.getValue()));
            paasFilterArg.setQuery(paasQueryArg);
            paasFilterArg.setSelectFields(selectFields);
//            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
//            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
//                objectDataInnerPage.getDataList().forEach(e -> {
//                    String qywxUserId = e.getString("qywx_user_id");
//                    String externalUserId = e.getString("external_user_id");
//                    if (StringUtils.isNotBlank(qywxUserId) && StringUtils.isNotBlank(externalUserId)) {
//                        Set<String> objIdSet = user2ObjIdMap.get(qywxUserId);
//                        if (objIdSet == null) {
//                            user2ObjIdMap.put(qywxUserId, Sets.newHashSet(externalUserId));
//                        } else {
//                            objIdSet.add(externalUserId);
//                            user2ObjIdMap.put(qywxUserId, objIdSet);
//                        }
//                        allObjIdSet.add(externalUserId);
//                    }
//                });
//            }
            crmV2Manager.listCrmObjectScanByIdAndHandle(ea, SuperUserConstants.USER_ID, paasFilterArg, 1000, objectData -> {
                String qywxUserId = objectData.getString("qywx_user_id");
                String externalUserId = objectData.getString("external_user_id");
                if (StringUtils.isNotBlank(qywxUserId) && StringUtils.isNotBlank(externalUserId)) {
                    Set<String> objIdSet = user2ObjIdMap.get(qywxUserId);
                    if (objIdSet == null) {
                        user2ObjIdMap.put(qywxUserId, Sets.newHashSet(externalUserId));
                    } else {
                        objIdSet.add(externalUserId);
                        user2ObjIdMap.put(qywxUserId, objIdSet);
                    }
                    allObjIdSet.add(externalUserId);
                }
            });
        }
        if (CollectionUtils.isEmpty(allObjIdSet)) {
            return user2ExternalUserId;
        }
        //根据企微客户对象id 查询externalUserId
        List<List<String>> partitionObjectIds = Lists.partition(new ArrayList<>(allObjIdSet), 1000);
        for (List<String> partitionObjectId : partitionObjectIds) {
            List<PaasQueryArg.Condition> contions = new ArrayList<>();
            List<String> fileds = Lists.newArrayList("_id", "external_user_id");
            PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
            filterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            PaasQueryArg queryArg = new PaasQueryArg(0, partitionObjectId.size());
            PaasQueryArg.Condition idCondition = new PaasQueryArg.Condition("_id", partitionObjectId, PaasAndCrmOperatorEnum.IN.getCrmOperator());
            contions.add(idCondition);
            PaasQueryArg.Condition appScopeCondition = new PaasQueryArg.Condition(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, Lists.newArrayList(AppScopeEnum.MARKETING.getValue()), PaasAndCrmOperatorEnum.IN.getCrmOperator());
            contions.add(appScopeCondition);
            queryArg.setFilters(contions);
            queryArg.addFilter("add_status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("normal"));
            queryArg.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("default__c"));
//            if (isOpen && CollectionUtils.isNotEmpty(dataPermission)) {
//                queryArg.addFilter("data_own_organization", OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
//            }
            filterArg.setQuery(queryArg);
            filterArg.setSelectFields(fileds);
//            InnerPage<ObjectData> innerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, filterArg);
//            if (innerPage != null && CollectionUtils.isNotEmpty(innerPage.getDataList())) {
//                innerPage.getDataList().forEach(data -> {
//                    String id = data.getId();
//                    String externalUserId = data.getString("external_user_id");
//                    if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(externalUserId)) {
//                        objId2ExternalUserIdMap.put(id, externalUserId);
//                    }
//                });
//            }
            crmV2Manager.listCrmObjectScanByIdAndHandle(ea, SuperUserConstants.USER_ID, filterArg, 1000, data -> {
                String id = data.getId();
                String externalUserId = data.getString("external_user_id");
                if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(externalUserId)) {
                    objId2ExternalUserIdMap.put(id, externalUserId);
                }
            });
        }
        user2ObjIdMap.forEach((k, v) -> {
            Set<String> externalUserId = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(v)) {
                v.forEach(e -> {
                    if (StringUtils.isNotBlank(objId2ExternalUserIdMap.get(e))) {
                        externalUserId.add(objId2ExternalUserIdMap.get(e));
                    }
                });
                user2ExternalUserId.put(k, externalUserId);
            }
        });
        log.warn("GroupSendMessageManager getEmployeeExternalUserIds size:{}", user2ExternalUserId.size());
        return user2ExternalUserId;
    }

    public Map<String, Set<String>> getEmployeeExternalUserIdsV2(String ea, List<String> senderIds) {
        Map<String, Set<String>> user2ExternalUserId = Maps.newHashMap();
        //查询企微加好友记录
        Map<String, Set<String>> user2ObjIdMap = Maps.newConcurrentMap();
        Set<String> allObjIdSet = Sets.newCopyOnWriteArraySet();
        List<List<String>> userIdsPartition = Lists.partition(senderIds, 1000);
        CountDownLatch countDownLatch = new CountDownLatch(userIdsPartition.size());
        for (List<String> userPartition : userIdsPartition) {
            executorService.submit(()->{
                try {
                    List<String> selectFields = com.google.common.collect.Lists.newArrayList("external_user_id", "qywx_user_id");
                    PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
                    paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                    PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                    paasQueryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), userPartition);
                    paasQueryArg.addFilter("friend_status", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Collections.singletonList("0"));
                    paasFilterArg.setQuery(paasQueryArg);
                    paasFilterArg.setSelectFields(selectFields);
//                    InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
//                    if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
//                        objectDataInnerPage.getDataList().forEach(e -> {
//                            String qywxUserId = e.getString("qywx_user_id");
//                            String externalUserId = e.getString("external_user_id");
//                            if (StringUtils.isNotBlank(qywxUserId) && StringUtils.isNotBlank(externalUserId)) {
//                                Set<String> objIdSet = user2ObjIdMap.get(qywxUserId);
//                                if (objIdSet == null) {
//                                    user2ObjIdMap.put(qywxUserId, Sets.newHashSet(externalUserId));
//                                } else {
//                                    objIdSet.add(externalUserId);
//                                    user2ObjIdMap.put(qywxUserId, objIdSet);
//                                }
//                                allObjIdSet.add(externalUserId);
//                            }
//                        });
//                    }
                    crmV2Manager.listCrmObjectScanByIdAndHandle(ea, SuperUserConstants.USER_ID, paasFilterArg, 1000, e -> {
                        String qywxUserId = e.getString("qywx_user_id");
                        String externalUserId = e.getString("external_user_id");
                        if (StringUtils.isNotBlank(qywxUserId) && StringUtils.isNotBlank(externalUserId)) {
                            Set<String> objIdSet = user2ObjIdMap.get(qywxUserId);
                            if (objIdSet == null) {
                                user2ObjIdMap.put(qywxUserId, Sets.newHashSet(externalUserId));
                            } else {
                                objIdSet.add(externalUserId);
                                user2ObjIdMap.put(qywxUserId, objIdSet);
                            }
                            allObjIdSet.add(externalUserId);
                        }
                    });
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(10L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("getEmployeeExternalUserIdsV2 user2ObjIdMap out times");
        }
        if (CollectionUtils.isEmpty(allObjIdSet)) {
            return user2ExternalUserId;
        }
        //根据企微客户对象id 查询externalUserId
        Map<String, String> objId2ExternalUserIdMap = Maps.newConcurrentMap();
        List<List<String>> partitionObjectIds = Lists.partition(new ArrayList<>(allObjIdSet), 1000);
        CountDownLatch countDownLatch2 = new CountDownLatch(partitionObjectIds.size());
        for (List<String> partitionObjectId : partitionObjectIds) {
            executorService.submit(()->{
                try {
                    PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
                    PaasQueryArg queryArg = new PaasQueryArg(0, partitionObjectId.size());
                    queryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), partitionObjectId);
                    queryArg.addFilter("add_status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("normal"));
                    filterArg.setQuery(queryArg);
                    filterArg.setSelectFields(Lists.newArrayList("_id", "external_user_id"));
                    filterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                    InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, filterArg, partitionObjectId.size());
                    if (innerPage != null && CollectionUtils.isNotEmpty(innerPage.getDataList())) {
                        innerPage.getDataList().forEach(data -> {
                            String id = data.getId();
                            String externalUserId = data.getString("external_user_id");
                            if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(externalUserId)) {
                                objId2ExternalUserIdMap.put(id, externalUserId);
                            }
                        });
                    }
                } finally {
                    countDownLatch2.countDown();
                }
            });
        }
        try {
            countDownLatch2.await(10L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("getEmployeeExternalUserIdsV2 objId2ExternalUserIdMap out times");
        }
        user2ObjIdMap.forEach((k, v) -> {
            Set<String> externalUserId = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(v)) {
                v.forEach(e -> {
                    if (StringUtils.isNotBlank(objId2ExternalUserIdMap.get(e))) {
                        externalUserId.add(objId2ExternalUserIdMap.get(e));
                    }
                });
                user2ExternalUserId.put(k, externalUserId);
            }
        });
        log.warn("GroupSendMessageManager getEmployeeExternalUserIdsV2 size:{}", user2ExternalUserId.size());
        return user2ExternalUserId;
    }

    private List<String> getExternalUserIds(QywxGroupSendTaskEntity groupSendTask, List<QuerySendGroupMessageDataVO.Filter> filters, List<TagName> tagList, List<String> marketingUserGroupIds){
        Set<String> externalUserIds = Sets.newHashSet();
        try {
            if(groupSendTask.getSendRange() == QywxGroupSendRangeTypeEnum.MARKETING_USER_GROUP.getType()) {
                List<String> wxUserIds = marketingUserGroupManager.listWxWorkExternalUserIdsByMarketingUserGroupIds(groupSendTask.getEa(), marketingUserGroupIds);
                if (CollectionUtils.isNotEmpty(wxUserIds)){
                    externalUserIds.addAll(wxUserIds);
                }
            } else {
                PaasQueryFilterArg paasQueryFilterArg = this.getPaasQueryFilterArg(groupSendTask.getEa(), groupSendTask.getSendRange(), filters, tagList);
//                InnerPage<ObjectData> result = crmV2Manager.concurrentListCrmObjectByFilterV3(groupSendTask.getEa(), -10000, paasQueryFilterArg);
//                if (result != null && CollectionUtils.isNotEmpty(result.getDataList())) {
//                    result.getDataList().forEach(objectData -> {
//                        Object externalUserId = objectData.get("external_user_id");
//                        if (externalUserId != null ) {
//                            externalUserIds.add(String.valueOf(externalUserId));
//                        }
//                    });
//                }
                crmV2Manager.listCrmObjectScanByIdAndHandle(groupSendTask.getEa(), SuperUserConstants.USER_ID, paasQueryFilterArg, 1000, objectData -> {
                    Object externalUserId = objectData.get("external_user_id");
                    if (externalUserId != null) {
                        externalUserIds.add(String.valueOf(externalUserId));
                    }
                });
            }
        } catch (Exception e) {
            log.warn("GroupSendMessageManager getExternalUserIds exception, groupSendTask={}, exception={}", groupSendTask, e.fillInStackTrace());
        }
        log.warn("GroupSendMessageManager getExternalUserIds size:{}", externalUserIds.size());
        return Lists.newArrayList(externalUserIds);
    }

    public PaasQueryFilterArg getPaasQueryFilterArg(String ea, Integer sendRange, List<QuerySendGroupMessageDataVO.Filter> filters, List<TagName> tagList) {
        List<PaasQueryArg.Condition> conditionFilters = new ArrayList<>();
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        if (QywxGroupSendRangeTypeEnum.FILTER.getType().equals(sendRange) && CollectionUtils.isNotEmpty(filters)) {
            filters.forEach(e -> {
                PaasQueryArg.Condition e1 = new PaasQueryArg.Condition(e.getFieldName(), e.getFieldValues(), e.getOperator());
                e1.setValueType(e.getValueType());
                e1.setIsCascade(e.getIsCascade());
                conditionFilters.add(e1);
            });
        }
        if (QywxGroupSendRangeTypeEnum.TAG.getType().equals(sendRange)) {
            List<String> tagIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(tagList)) {
                Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), tagList);
                if (MapUtils.isNotEmpty(tagNamesIdMap)) {
                    tagIds = tagNamesIdMap.values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
                }
            }
            if (CollectionUtils.isNotEmpty(tagIds)) {
                PaasQueryArg.Condition e = new PaasQueryArg.Condition("tag", tagIds, OperatorConstants.HASANYOF);
                e.setValueType(11);
                conditionFilters.add(e);
            }
        }
        query.setFilters(conditionFilters);
        query.addFilter("add_status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("normal"));
        query.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(AppScopeEnum.MARKETING.getValue()));
        paasQueryFilterArg.setQuery(query);
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        paasQueryFilterArg.setSelectFields(Arrays.asList("_id", "external_user_id"));
        return paasQueryFilterArg;
    }

    //删除掉多余的数据
    public void deleteMoreResultRecord() {
        tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
            List<String> msgids = sendResultDAO.getTaskMsgids(dbRouteEa);
            if (CollectionUtils.isEmpty(msgids)) {
                return;
            }

            for (String msgid : msgids) {
                List<QywxGroupSendResultEntity> qywxGroupSendResultEntityList = sendResultDAO.queryListByMsgid(dbRouteEa, msgid);
                if (CollectionUtils.isEmpty(qywxGroupSendResultEntityList)) {
                    continue;
                }

                HashMap<String, List<QywxGroupSendResultEntity>> sendResultEntityHashMap = new HashMap<>();
                for (QywxGroupSendResultEntity entity : qywxGroupSendResultEntityList) {
                    if (sendResultEntityHashMap.get(entity.getExternalUserid()) == null) {
                        sendResultEntityHashMap.put(entity.getExternalUserid(), Lists.newArrayList());
                    }
                    sendResultEntityHashMap.get(entity.getExternalUserid()).add(entity);
                }

                for (Map.Entry<String, List<QywxGroupSendResultEntity>> entry : sendResultEntityHashMap.entrySet()) {
                    String externalUserId = entry.getKey();
                    List<QywxGroupSendResultEntity> value = entry.getValue();
                    if (CollectionUtils.isNotEmpty(value) && value.size() > 1) {
                        log.info("sendTaskResult msgid:{} externalUserId:{} total:{}", msgid, externalUserId, value.size());
                        for (int i = 1; i < value.size(); i++) {
                            sendResultDAO.deleteByIdAndMsgid(value.get(i).getEa(), value.get(i).getId(), msgid);
                            log.info("delete sendTaskResult id:{}  msgid:{} externalUserId:{}", value.get(i).getId(), msgid, externalUserId);
                        }
                    }
                }
            }
        });
    }

    public void getGroupMsgResultSchedule(boolean scanAllTask) {
        tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
            if (executeTaskDetailManager.checkTaskAndAddIfNotExist(dbRouteEa, ExecuteTaskDetailTypeEnum.QYWX_SEND_RESULT_PULL, "qywx_send_result_callback")) {
                if (!scanAllTask) {
                    return;
                }
            }

            List<QywxGroupSendTaskEntity> singleResultEntities = null;
            List<SendGroupResultDTO> groupResultEntities = null;
            if (scanAllTask){
                singleResultEntities = sendTaskDAO.getNeedSendSingleResultTask(dbRouteEa);
                groupResultEntities = qywxGroupSendGroupResultDAO.getNeedSendGroupResultTask(dbRouteEa);
            }else {
                singleResultEntities = sendTaskDAO.getNeedSendSingleResultTaskThisDay(dbRouteEa);
                groupResultEntities = qywxGroupSendGroupResultDAO.getNeedSendGroupResultTaskThisDay(dbRouteEa);
            }

            if (CollectionUtils.isEmpty(singleResultEntities) && CollectionUtils.isEmpty(groupResultEntities)) {
                executeTaskDetailManager.taskComplete(dbRouteEa, ExecuteTaskDetailTypeEnum.QYWX_SEND_RESULT_PULL, "qywx_send_result_callback");
                return;
            }

            final List<QywxGroupSendTaskEntity> finalSingleResultEntities = singleResultEntities;
            final List<SendGroupResultDTO> finalGroupResultEntities = groupResultEntities;
            TraceContext context = TraceContext.get();
            ExecutorService executorService = NamedThreadPool.newFixedThreadPool(1, "marketing_qywx_send_result_thread");
            executorService.execute(() -> {
                try {
                    if (context != null) {
                        TraceContext._set(context);
                    }
                    if (CollectionUtils.isNotEmpty(finalSingleResultEntities)){
                        finalSingleResultEntities.forEach(groupSendTaskEntity -> {
                            //过滤掉停用企业、营销通配额过期企业
                            if (marketingActivityRemoteManager.enterpriseStop(groupSendTaskEntity.getEa()) || appVersionManager.getCurrentAppVersion(groupSendTaskEntity.getEa()) == null) {
                                log.info("GroupSendMessageManager.getGroupMsgResultSchedule failed enterprise stop or license expire groupSendTaskEntity ea:{}", groupSendTaskEntity.getEa());
                                return;
                            }
                            handlerSendSingleTaskResult(groupSendTaskEntity);
                        });
                    }

                    if (CollectionUtils.isNotEmpty(finalGroupResultEntities)) {
                        //过滤掉停用企业、营销通配额过期企业
                        finalGroupResultEntities.forEach(taskEntity -> {
                            if (marketingActivityRemoteManager.enterpriseStop(taskEntity.getEa()) || appVersionManager.getCurrentAppVersion(taskEntity.getEa()) == null) {
                                log.info("GroupSendMessageManager.getGroupMsgResultSchedule failed enterprise stop or license expire taskEntity ea:{}", taskEntity.getEa());
                                return;
                            }
                            handlerSendGroupTaskResult(taskEntity, null);
                        });
                    }
                    log.info("getGroupMsgResultSchedule task finish");
                }catch (Exception e){
                    log.info("getGroupMsgResultSchedule failed e:", e);
                }finally {
                    if (context != null ) {
                        TraceContext.remove();
                    }
                    if (executorService != null && !executorService.isShutdown()) {
                        executorService.shutdown();
                    }
                    executeTaskDetailManager.taskComplete(dbRouteEa, ExecuteTaskDetailTypeEnum.QYWX_SEND_RESULT_PULL,"qywx_send_result_callback");
                }
            });
        });
    }

    public void hanlerSendGroupTaskResultByPager(String taskId, String id, List<GroupMsgResult.Detail> currentPage, String ea){
        if (CollectionUtils.isEmpty(currentPage)){
            return;
        }

        int successCount = 0;
        int failedCount = 0;
        int unsendCount = 0;
        List<String> groupIds = Lists.newArrayList();
        List<QywxGroupSendGroupFlatResultEntity> entities = Lists.newArrayList();
        QywxGroupSendTaskEntity qywxGroupSendTaskEntity = qywxGroupSendTaskDAO.queryById(taskId, ea);
        for (GroupMsgResult.Detail detail : currentPage) {
            QywxGroupSendGroupFlatResultEntity entity = new QywxGroupSendGroupFlatResultEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(qywxGroupSendTaskEntity.getEa());
            entity.setSendid(taskId);
            entity.setSender(detail.getUserid());
            entity.setCreateTime(qywxGroupSendTaskEntity.getCreateTime());
            entity.setUpdateTime(new Date());
            if (detail.getStatus() == QywxGroupSendMsgStatus.SEND.getType()) {
                successCount++;
                if (!groupIds.contains(detail.getChatId())) {
                    groupIds.add(detail.getChatId());
                }
                entity.setStatus(1);
                entity.setSendGroupId(detail.getChatId());
            } else if (detail.getStatus() == QywxGroupSendMsgStatus.NOT_SEND.getType()) {
                unsendCount++;
                entity.setStatus(0);
                entity.setSendGroupId("");
            } else {
                failedCount++;
                entity.setStatus(detail.getStatus());
                entity.setSendGroupId(detail.getChatId()==null?"":detail.getChatId());
            }
            entities.add(entity);
        }
        String groupIdsJson = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            groupIdsJson = gson.toJson(groupIds);
        }
        qywxGroupSendGroupResultDAO.updateResultById(ea, id, successCount, failedCount, unsendCount, groupIdsJson);
        if (CollectionUtils.isNotEmpty(entities)) {
            try {
                // 同一个msgid发送人一样
                QywxGroupSendGroupFlatResultEntity flatResultEntity = entities.get(0);
                qywxGroupSendGroupResultDAO.deleteFlatResult(ea, flatResultEntity.getSendid(), flatResultEntity.getSender());
                qywxGroupSendGroupResultDAO.batchInsertFlatResult(entities);
            } catch (Exception e) {
                log.warn("QywxGroupSendGroupFlatResult data operate error entities:{}", entities, e);
            }
        }
    }

    public void handlerSendGroupTaskResult(SendGroupResultDTO dto, String cursor) {
        try {
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(dto.getEa());
            if (agentConfig == null) {
                log.warn("GroupSendMessageManager handlerSendTaskResult agentConfig is null, groupSendTaskDTO={}", dto);
                return;
            }
            GetGroupMsgResultArg arg = new GetGroupMsgResultArg();
            arg.setMsgid(dto.getMsgid());
            if (StringUtils.isNotEmpty(cursor)){
                arg.setCursor(cursor);
            }
            arg.setLimit(1000);  //https://developer.work.weixin.qq.com/document/16251  返回的最大记录数，整型，最大值10000，默认值10000
            GroupMsgResult groupMsgResult = getGroupMsgResult(qywxManager.getAccessToken(dto.getEa()), arg);
            if (groupMsgResult == null || !groupMsgResult.isSuccess()){
                log.info("handlerSendGroupTaskResult getGroupMsgResult failed arg:{} result:{}", arg, groupMsgResult);
                return;
            }
            log.info("handlerSendGroupTaskResult msgId:{} currentCursor:{} nextCursor:{} limit:{}",
                    dto.getMsgid(), arg.getCursor(), groupMsgResult.getNextCursor(), arg.getLimit());
            if (CollectionUtils.isNotEmpty(groupMsgResult.getDetailList())){
                PageUtil<GroupMsgResult.Detail> pageUtil = new PageUtil<>(groupMsgResult.getDetailList(), 2000);//分页处理，每页2000条
                for (int i = 1; i <= pageUtil.getPageCount(); i++){
                    List<GroupMsgResult.Detail> currentPage = pageUtil.getPagedList(i);
                    hanlerSendGroupTaskResultByPager(dto.getTaskId(), dto.getGroupResultId(), currentPage, dto.getEa());
                }
            }

            String nextCursor = groupMsgResult.getNextCursor();
            if (StringUtils.isEmpty(nextCursor)){
                return;
            }
            //如果未取完，取剩下的数据
            handlerSendGroupTaskResult(dto, nextCursor);
        } catch (Exception e) {
            log.warn("GroupSendMessageManager handlerSendGroupTaskResult exception, SendGroupResultDTO={}, exception=", dto, e);
        }
    }

    public void handSopQywxGroupMsgTaskResult(String ea, String msgid, String cursor) {
        try {
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
            if (agentConfig == null) {
                log.warn("handSopQywxGroupMsgTaskResult queryAgentByEa agentConfig is null, ea={}", ea);
                return;
            }
            GetGroupMsgResultArg arg = new GetGroupMsgResultArg();
            arg.setMsgid(msgid);
            if (StringUtils.isNotEmpty(cursor)) {
                arg.setCursor(cursor);
            }
            GroupMsgResult groupMsgResult = getGroupMsgResult(qywxManager.getAccessToken(ea), arg);
            if (groupMsgResult == null || !groupMsgResult.isSuccess()) {
                log.info("handSopQywxGroupMsgTaskResult getGroupMsgResult failed arg:{} result:{}", arg, groupMsgResult);
                return;
            }
            log.info("handSopQywxGroupMsgTaskResult msgId:{} currentCursor:{} nextCursor:{} limit:{}",
                    msgid, arg.getCursor(), groupMsgResult.getNextCursor(), arg.getLimit());
            if (CollectionUtils.isNotEmpty(groupMsgResult.getDetailList())) {
                PageUtil<GroupMsgResult.Detail> pageUtil = new PageUtil<>(groupMsgResult.getDetailList(), 2000);//分页处理，每页2000条
                for (int i = 1; i <= pageUtil.getPageCount(); i++) {
                    List<GroupMsgResult.Detail> currentPage = pageUtil.getPagedList(i);
                    handlerSopQywxGroupMsgTaskResultByPager(ea, msgid, currentPage);
                }
            }

            String nextCursor = groupMsgResult.getNextCursor();
            if (StringUtils.isEmpty(nextCursor)) {
                return;
            }
            handSopQywxGroupMsgTaskResult(ea, msgid, cursor);
        } catch (Exception e) {
            log.warn("GroupSendMessageManager handSopQywxGroupMsgTaskResult exception, ea={}, msgid={}, exception=", ea, msgid, e);
        }
    }

    public void handlerSopQywxGroupMsgTaskResultByPager(String ea, String msgid, List<GroupMsgResult.Detail> currentPage) {
        if (CollectionUtils.isEmpty(currentPage)) {
            return;
        }
        int successCount = 0;
        int failedCount = 0;
        int unsendCount = 0;
        List<String> groupIds = Lists.newArrayList();
        for (GroupMsgResult.Detail detail : currentPage) {
            if (detail.getStatus() == QywxGroupSendMsgStatus.SEND.getType()) {
                successCount++;
                if (!groupIds.contains(detail.getChatId())) {
                    groupIds.add(detail.getChatId());
                }
            } else if (detail.getStatus() == QywxGroupSendMsgStatus.NOT_SEND.getType()) {
                unsendCount++;
            } else {
                failedCount++;
            }
        }
        String groupIdsJson = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            groupIdsJson = gson.toJson(groupIds);
        }
        qywxGroupSendGroupResultDAO.updateResultByMsgid(ea, msgid, successCount, failedCount, unsendCount, groupIdsJson);
    }

    public void handlerSopQywxMsgTaskResult(String msgid, String ea) {
        try {
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
            if (agentConfig == null) {
                log.warn("GroupSendMessageManager handlerSopQywxMsgTaskResult agentConfig is null, groupSendTask={}", msgid);
                return;
            }
            GetGroupMsgResultArg arg = new GetGroupMsgResultArg();
            arg.setMsgid(msgid);
            arg.setLimit(10000); //https://developer.work.weixin.qq.com/document/16251  返回的最大记录数，整型，最大值10000，默认值10000
            GroupMsgResult groupMsgResult = getGroupMsgResult(qywxManager.getAccessToken(ea), arg);
            if (groupMsgResult != null && groupMsgResult.getErrcode() == 0) {
                if (CollectionUtils.isNotEmpty(groupMsgResult.getDetailList())) {
                    groupMsgResult.getDetailList().forEach(detail -> {
                        try {
                            QywxGroupSendResultEntity qywxGroupSendResultEntity =
                                    sendResultDAO.queryByMsgidAndexternalUserid(ea, msgid, detail.getExternalUserId());
                            if (qywxGroupSendResultEntity == null) {
                                QywxGroupSendResultEntity groupSendResultEntity = new QywxGroupSendResultEntity();
                                groupSendResultEntity.setId(UUIDUtil.getUUID());
                                groupSendResultEntity.setEa(ea);
                                groupSendResultEntity.setMsgid(msgid);
                                groupSendResultEntity.setExternalUserid(detail.getExternalUserId());
                                groupSendResultEntity.setUserid(detail.getUserid());
                                groupSendResultEntity.setStatus(detail.getStatus());
                                groupSendResultEntity.setSendTime(detail.getSendTime());
                                groupSendResultEntity.setCreateTime(new Date());
                                groupSendResultEntity.setUpdateTime(new Date());
                                sendResultDAO.insert(groupSendResultEntity);
                                if (detail.getStatus() == QywxGroupSendMsgStatus.SEND.getType()){
                                    sopSendUserMarketingMsg(ea, qywxGroupSendResultEntity.getExternalUserid(), qywxGroupSendResultEntity.getUserid());
                                }
                            } else {
                                if (qywxGroupSendResultEntity.getStatus() == QywxGroupSendMsgStatus.NOT_SEND.getType() && detail.getStatus() != null && detail.getStatus() != QywxGroupSendMsgStatus.NOT_SEND.getType()) {
                                    sendResultDAO.updateStatusById(ea, qywxGroupSendResultEntity.getId(), detail.getStatus(), detail.getUserid(), detail.getSendTime());
                                    if (detail.getStatus() == QywxGroupSendMsgStatus.SEND.getType()){
                                        sopSendUserMarketingMsg(ea, qywxGroupSendResultEntity.getExternalUserid(), qywxGroupSendResultEntity.getUserid());
                                    }
                                }
                            }

                        } catch (Exception e) {
                            log.warn("GroupSendMessageManager handlerSopQywxMsgTaskResult exception, groupSendTask={}, detail={}, exception={}", msgid, detail, e.fillInStackTrace());
                        }
                    });
                }
            } else {
                log.warn("GroupSendMessageManager handlerSopQywxMsgTaskResult groupMsgResult is error, groupSendTask={}, groupMsgResult={}", msgid, groupMsgResult);
            }
        } catch (Exception e) {
            log.warn("GroupSendMessageManager handlerSopQywxMsgTaskResult exception, groupSendTask={}, exception={}", msgid, e.fillInStackTrace());
        }
    }

    public void sopSendUserMarketingMsg(String ea, String externalUserid, String qywxUserid) {
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyId(ea, qywxUserid);
        MarketingUserActionEvent recordArg = new MarketingUserActionEvent();
        recordArg.setEa(ea);
        recordArg.setActionTime(System.currentTimeMillis());
        recordArg.setChannelType(MarketingUserActionChannelType.QYWX.getChannelType());
        recordArg.setActionType(MarketingUserActionType.RECV_QYWX_MSG.getActionType());
        recordArg.setSceneType(ActionSourceTypeEnum.H5.getType());
        recordArg.setExternalUserid(externalUserid);
        recordArg.setActionTime(System.currentTimeMillis());
        if (qywxVirtualFsUserEntity != null && QywxUserConstants.isFsUserId(qywxVirtualFsUserEntity.getUserId())) {
            recordArg.setSpreadFsUid(qywxVirtualFsUserEntity.getUserId());
        }
        recordArg.setExtensionParams(new HashMap<>());
        recordArg.getExtensionParams().put(RecordActionArg.MARKETING_SCENE_KEY, MarketingSceneEnum.OTHER.getCode());
        marketingRecordActionSender.send(recordArg);
    }

    public void handleQywxSingleTaskResultByMsgId(String ea, String taskId, String msgId, String cursor){
        GetGroupMsgResultArg arg = new GetGroupMsgResultArg();
        arg.setMsgid(msgId);
        if (StringUtils.isNotEmpty(cursor)){
            arg.setCursor(cursor);
        }
        arg.setLimit(1000);  //https://developer.work.weixin.qq.com/document/16251  返回的最大记录数，整型，最大值10000，默认值10000
        log.info("handleQywxSingleTaskResultByMsgId msgId:{} nextCursor:{} limit:{}", arg.getMsgid(), arg.getCursor(), arg.getLimit());
        GroupMsgResult groupMsgResult = getGroupMsgResult(qywxManager.getAccessToken(ea), arg);
        if (groupMsgResult == null || !groupMsgResult.isSuccess()){
            log.info("handleQywxSingleTaskResultByMsgId result null msg:{} result:{}", msgId, groupMsgResult);
            return;
        }
        log.info("handleQywxSingleTaskResultByMsgId msgId:{} currentCursor:{} nextCursor:{} limit:{}",
                msgId, arg.getCursor(), groupMsgResult.getNextCursor(), arg.getLimit());
        if(CollectionUtils.isNotEmpty(groupMsgResult.getDetailList())){
            PageUtil<GroupMsgResult.Detail> pageUtil = new PageUtil<>(groupMsgResult.getDetailList(), 2000);//分页处理，每页2000条
            for (int i = 1; i <= pageUtil.getPageCount(); i++){
                List<GroupMsgResult.Detail> currentPage = pageUtil.getPagedList(i);
                handleQywxSingleTaskResultByPage(ea, taskId, msgId, currentPage);
            }
        }

        String nextCursor = groupMsgResult.getNextCursor();
        if (StringUtils.isEmpty(nextCursor)){
            return;
        }
        //如果未取完，取剩下的数据
        handleQywxSingleTaskResultByMsgId(ea, taskId, msgId, nextCursor);
    }

    public void handleQywxSingleTaskResultByPage(String ea, String taskId, String msgId, List<GroupMsgResult.Detail> currentPage){
        List<String> externalUseridList = currentPage.stream().map(GroupMsgResult.Detail::getExternalUserId).collect(Collectors.toList());
        List<QywxGroupSendResultEntity> qywxGroupSendResultEntityList = sendResultDAO.queryByMsgidAndexternalUseridList(ea, msgId, externalUseridList);
        Map<String, QywxGroupSendResultEntity> externalUseridToSendResultMap = null;
        if (CollectionUtils.isNotEmpty(qywxGroupSendResultEntityList)) {
            externalUseridToSendResultMap = qywxGroupSendResultEntityList.stream().collect(Collectors.toMap(QywxGroupSendResultEntity::getExternalUserid, e -> e, (v1, v2) -> v1));
        }
        List<QywxGroupSendResultEntity> insertList = Lists.newArrayList();
        for (GroupMsgResult.Detail detail : currentPage){
            if (externalUseridToSendResultMap == null || externalUseridToSendResultMap.get(detail.getExternalUserId()) == null){
                QywxGroupSendResultEntity groupSendResultEntity = new QywxGroupSendResultEntity();
                groupSendResultEntity.setId(UUIDUtil.getUUID());
                groupSendResultEntity.setEa(ea);
                groupSendResultEntity.setMsgid(msgId);
                groupSendResultEntity.setSendid(taskId);
                groupSendResultEntity.setExternalUserid(detail.getExternalUserId());
                groupSendResultEntity.setUserid(detail.getUserid());
                groupSendResultEntity.setStatus(detail.getStatus());
                groupSendResultEntity.setSendTime(detail.getSendTime());
                groupSendResultEntity.setCreateTime(new Date());
                groupSendResultEntity.setUpdateTime(new Date());
                insertList.add(groupSendResultEntity);
            }else {
                QywxGroupSendResultEntity qywxGroupSendResultEntity =  externalUseridToSendResultMap.get(detail.getExternalUserId());
                if (qywxGroupSendResultEntity.getStatus() == QywxGroupSendMsgStatus.NOT_SEND.getType() && detail.getStatus() != null && detail.getStatus() != QywxGroupSendMsgStatus.NOT_SEND.getType()) {
                    sendResultDAO.updateStatusById(ea, qywxGroupSendResultEntity.getId(), detail.getStatus(), detail.getUserid(), detail.getSendTime());
                    if (detail.getStatus() == QywxGroupSendMsgStatus.SEND.getType()){
                        sendUserMarketingMsg(ea, qywxGroupSendResultEntity.getSendid(), qywxGroupSendResultEntity.getExternalUserid(), qywxGroupSendResultEntity.getUserid());
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)){
            sendResultDAO.batchInsert(insertList);
            for (QywxGroupSendResultEntity qywxGroupSendResultEntity : insertList){
                if (qywxGroupSendResultEntity.getStatus() == QywxGroupSendMsgStatus.SEND.getType()) {
                    sendUserMarketingMsg(ea, qywxGroupSendResultEntity.getSendid(), qywxGroupSendResultEntity.getExternalUserid(), qywxGroupSendResultEntity.getUserid());
                }
            }
        }
    }

    public void sendUserMarketingMsg(String ea, String taskId, String externalUserid, String qywxUserid){
        MarketingActivityExternalConfigEntity externalConfig = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType(), taskId);
        if (externalConfig == null){
            return;
        }

        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyId(ea, qywxUserid);
        MarketingUserActionEvent recordArg = new MarketingUserActionEvent();
        recordArg.setEa(ea);
        recordArg.setActionTime(System.currentTimeMillis());
        recordArg.setChannelType(MarketingUserActionChannelType.QYWX.getChannelType());
        recordArg.setActionType(MarketingUserActionType.RECV_QYWX_MSG.getActionType());
        recordArg.setMarketingActivityId(externalConfig.getMarketingActivityId());
        recordArg.setMarketingEventId(externalConfig.getMarketingEventId());
        recordArg.setSceneType(ActionSourceTypeEnum.H5.getType());
        recordArg.setExternalUserid(externalUserid);
        recordArg.setActionTime(System.currentTimeMillis());
        if (qywxVirtualFsUserEntity != null && QywxUserConstants.isFsUserId(qywxVirtualFsUserEntity.getUserId())){
            recordArg.setSpreadFsUid(qywxVirtualFsUserEntity.getUserId());
        }
        recordArg.setExtensionParams(new HashMap<>());
        recordArg.getExtensionParams().put(RecordActionArg.MARKETING_SCENE_KEY, MarketingSceneEnum.QYWX.getCode());
        marketingRecordActionSender.send(recordArg);
    }

    public void handlerSendSingleTaskResult(QywxGroupSendTaskEntity groupSendTask) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(groupSendTask.getEa());
        if (agentConfig == null) {
            log.warn("GroupSendMessageManager handlerSendTaskResult agentConfig is null, groupSendTask={}", groupSendTask);
            return;
        }
        List<String> msgIds = Splitter.on(",").trimResults().splitToList(groupSendTask.getMsgid());
        for (String msgId : msgIds) {
            handleQywxSingleTaskResultByMsgId(groupSendTask.getEa(), groupSendTask.getId(), msgId, null);
        }
    }

    public Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> listQywxMarketingActivityEmployeeRanking(String ea,
                                                                                                                       ListQywxMarketingActivityEmployeeRankingArg arg) {
        PageResult<ListQywxMarketingActivityEmployeeRankingResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        List<ListQywxMarketingActivityEmployeeRankingResult> employeeRankingResults = Lists.newArrayList();
        pageResult.setResult(employeeRankingResults);

        QywxGroupSendTaskEntity taskEntity = sendTaskDAO.getByMarketingActivityId(arg.getMarketingActivityId(), ea);
        if (taskEntity == null) {
            log.info("listQywxMarketingActivityEmployeeRanking send task is not exist ea{} arg:{}", ea, arg);
            return Result.newSuccess(pageResult);
        }
        List<String> qywxUserIds = null;
        if (CollectionUtils.isNotEmpty(arg.getDepartmentIdList())) {
            boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea) || fsBindManager.isQywxContactFsSetting(ea);
            if (isOpen) {
                qywxUserIds = qywxManager.handleQywxEmployeeUserIdWithOpenDataPermission(ea, taskEntity.getFsUserId(), null, arg.getDepartmentIdList());
            } else {
                List<Integer> qywxDepartmentIdList = arg.getDepartmentIdList().stream().map(e -> e - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(ea, qywxDepartmentIdList);
            }
            if (CollectionUtils.isEmpty(qywxUserIds)) {
                return Result.newSuccess(pageResult);
            }
        }
        if (taskEntity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
            List<SendGroupMsgByStatusDTO> groupMsgList = sendResultDAO.querySendMsgByStatus(ea, arg.getMarketingActivityId(), QywxGroupSendMsgStatus.NOT_SEND.getType());
            if (CollectionUtils.isNotEmpty(groupMsgList)) {
                int employeeCount = 0;
                int unSendMsgCount = 0;
                for (SendGroupMsgByStatusDTO dto : groupMsgList) {
                    employeeCount++;
                    unSendMsgCount += dto.getMsgCount();
                }
                Map<String, Integer> otherDataMap = new HashMap<>();
                otherDataMap.put("employeeCount", employeeCount);
                otherDataMap.put("unSendMsgCount", unSendMsgCount);
                pageResult.setOtherData(otherDataMap);
            } else {
                Map<String, Integer> otherDataMap = new HashMap<>();
                otherDataMap.put("employeeCount", 0);
                otherDataMap.put("unSendMsgCount", 0);
                pageResult.setOtherData(otherDataMap);
            }

            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), true);
            List<QywxGroupSendResultEntity> sendResultEntityList = sendResultDAO.queryDetailByPager(ea, arg.getMarketingActivityId(), qywxUserIds, arg.getStatus(), page);
            if (CollectionUtils.isNotEmpty(sendResultEntityList)) {
                Map<String, ListQywxMarketingActivityEmployeeRankingResult> customerMap = new HashMap<>();
                HashSet<String> employeeIdSet = new HashSet<>();
                List<String> customerIds = Lists.newArrayList();
                pageResult.setTotalCount(page.getTotalNum());
                for (QywxGroupSendResultEntity entity : sendResultEntityList) {
                    ListQywxMarketingActivityEmployeeRankingResult employee = new ListQywxMarketingActivityEmployeeRankingResult();
                    employee.setStatus(entity.getStatus());
                    employee.setEmployeeUserId(entity.getUserid());
                    customerMap.put(entity.getExternalUserid(), employee);
                    employeeIdSet.add(entity.getUserid());
                    customerIds.add(entity.getExternalUserid());
                    if (entity.getSendTime() != null) {
                        employee.setSendTime(entity.getSendTime() * 1000L);
                    }
                    employeeRankingResults.add(employee);
                }

                //查询客户姓名
                Set<String> externalUserIds = new HashSet<>(customerIds);
                Map<String, ObjectData> crmWxWorkExternalDataMap = wechatWorkExternalUserObjManager.getObjectDataMap(ea, externalUserIds);
                customerMap.forEach((k, value) -> {
                    if (crmWxWorkExternalDataMap.get(k) != null) {
                        value.setCustomerName(crmWxWorkExternalDataMap.get(k).getString("name"));
                    }
                });

                //查下员工姓名
                Map<String, Integer> fsUserIdQywxUserIdMap = new HashMap<>();
                Result<Map<String, String>> employeeResult =
                        qyweixinAccountBindManager.outAccountToFsAccountBatch("qywx", ea, new ArrayList<>(employeeIdSet));
                List<Integer> fsUserIds = Lists.newArrayList();
                if (employeeResult.isSuccess() && MapUtils.isNotEmpty(employeeResult.getData())) {
                    employeeResult.getData().forEach((k, v) -> {
                        String[] split = StringUtils.split(v, ".");
                        Integer fsUserId = Integer.parseInt(split[2]);
                        fsUserIds.add(fsUserId);
                        fsUserIdQywxUserIdMap.put(k, fsUserId);
                    });
                }

                // 增加虚拟身份查询
                List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = virtualUserManager.getVirtualUserByEaAndQyIds(ea, Lists.newArrayList(employeeIdSet));
                if (CollectionUtils.isNotEmpty(qywxVirtualFsUserEntityList)) {
                    qywxVirtualFsUserEntityList.forEach(data -> {
                        fsUserIds.add(data.getUserId());
                        fsUserIdQywxUserIdMap.put(data.getQyUserId(), data.getUserId());
                    });
                }

                if (CollectionUtils.isNotEmpty(fsUserIds)) {
                    Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIds, true);
                    if (MapUtils.isNotEmpty(fsEmployeeMsgMap)) {
                        for (ListQywxMarketingActivityEmployeeRankingResult employee : employeeRankingResults) {
                            Integer fsUserId = fsUserIdQywxUserIdMap.get(employee.getEmployeeUserId());
                            if (fsUserId != null) {
                                FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(fsUserIdQywxUserIdMap.get(employee.getEmployeeUserId()));
                                employee.setEmployeeName(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
                            }
                        }
                    }
                }
            }
            fillDepartment(ea, employeeRankingResults, false);
        } else {
            List<String> taskIds = Lists.newArrayList();
            taskIds.add(taskEntity.getId());
            //这里的状态筛选没有用
            List<QywxGroupSendGroupResultEntity> sendGroupResultEntities = qywxGroupSendGroupResultDAO.queryGroupResultBySender(ea, taskIds, qywxUserIds, null);
            if (CollectionUtils.isEmpty(sendGroupResultEntities)) {
                return Result.newSuccess(pageResult);
            }
            for (QywxGroupSendGroupResultEntity entity : sendGroupResultEntities) {
                if (entity.getSuccessCount() != 0) {
                    List<String> sendGroupIds = com.facishare.marketing.common.util.GsonUtil
                            .fromJson(entity.getSendGroupIds(), new TypeToken<List<String>>() {
                            }.getType());
                    for (String groupId : sendGroupIds) {
                        QueryCustomerGroupListResult groupInfo = queryCachedGroupDetail(taskEntity.getEa(), groupId);
                        if (groupInfo != null) {
                            ListQywxMarketingActivityEmployeeRankingResult rankingResult = new ListQywxMarketingActivityEmployeeRankingResult();
                            rankingResult.setChatType(taskEntity.getChatType());
                            rankingResult.setCustomerName(groupInfo.getGroupName());
                            rankingResult.setStatus(QywxGroupSendMsgStatus.SEND.getType());
                            rankingResult.setEmployeeUserId(entity.getSender());
                            rankingResult.setSendTime(entity.getCreateTime().getTime());
                            employeeRankingResults.add(rankingResult);
                        }
                    }

                }
            }
            if (CollectionUtils.isNotEmpty(employeeRankingResults)) {
                int pageNum = arg.getPageNum() == null ? 1 : arg.getPageNum();
                int pageSize = arg.getPageSize() == null ? 5 : arg.getPageSize();
                pageResult.setTotalCount(employeeRankingResults.size());
                employeeRankingResults = employeeRankingResults.stream().skip((long) (pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
                pageResult.setResult(employeeRankingResults);
            }
            fillDepartment(ea, employeeRankingResults, true);
        }

        return Result.newSuccess(pageResult);
    }

    private void fillDepartment(String ea, List<ListQywxMarketingActivityEmployeeRankingResult> employeeRankingResultList, boolean fillEmployeeName) {
        if (CollectionUtils.isEmpty(employeeRankingResultList)) {
            return;
        }
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea) || fsBindManager.isQywxContactFsSetting(ea);
        if (isOpen) {
            List<String> userIds = employeeRankingResultList.stream().map(ListQywxMarketingActivityEmployeeRankingResult::getEmployeeUserId).collect(Collectors.toList());
            Map<String, Integer> fsUserIdByQyWxInfo = qywxUserManager.getFsUserIdByQyWxInfo(ea, userIds, true, false);
            List<Integer> fsUserIds = Lists.newArrayList(fsUserIdByQyWxInfo.values());
            Map<Integer, FSEmployeeMsg> employeeInfoByUserIds = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIds, true);
            for (ListQywxMarketingActivityEmployeeRankingResult result : employeeRankingResultList) {
                FSEmployeeMsg fsEmployeeMsg = employeeInfoByUserIds.get(fsUserIdByQyWxInfo.get(result.getEmployeeUserId()));
                if (fsEmployeeMsg != null) {
                    result.setEmployeeName(fsEmployeeMsg.getName());
                    result.setDepartment(fsEmployeeMsg.getDepartment());
                }
            }
        } else {
            List<String> qyUserIds = employeeRankingResultList.stream().map(ListQywxMarketingActivityEmployeeRankingResult::getEmployeeUserId).collect(Collectors.toList());
            List<QywxEmployeeResult> qywxEmployeeResults = qywxEmployeeManager.batchByQyUserIds(ea, qyUserIds, false);
            Map<String, QywxEmployeeResult> qywxEmployeeMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(qywxEmployeeResults)) {
                qywxEmployeeMap = qywxEmployeeResults.stream().collect(Collectors.toMap(QywxEmployeeResult::getUserId, Function.identity(), (v1, v2) -> v1));
            }
            for (ListQywxMarketingActivityEmployeeRankingResult result : employeeRankingResultList) {
                QywxEmployeeResult qywxEmployeeResult = qywxEmployeeMap.get(result.getEmployeeUserId());
                if (qywxEmployeeResult != null) {
                    result.setEmployeeName(qywxEmployeeResult.getName());
                    result.setDepartment(qywxEmployeeResult.getWechatDepartmentName());
                }
            }
        }
    }

    public Result<PageResult<ListEmployeeQywxGroupSendDetailResult>> listEmployeeQywxGroupSendDetail(String ea, ListQywxMarketingActivityEmployeeRankingArg arg) {
        PageResult<ListEmployeeQywxGroupSendDetailResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        QywxGroupSendTaskEntity taskEntity = sendTaskDAO.getByMarketingActivityId(arg.getMarketingActivityId(), ea);
        if (taskEntity == null) {
            log.info("listEmployeeQywxGroupSendDetail send task is not exist ea{} arg:{}", ea, arg);
            return Result.newSuccess(pageResult);
        }
        List<String> qywxUserIds = null;
        if (CollectionUtils.isNotEmpty(arg.getDepartmentIdList())) {
            boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea) || fsBindManager.isQywxContactFsSetting(ea);
            if (isOpen) {
                qywxUserIds = qywxManager.handleQywxEmployeeUserIdWithOpenDataPermission(ea, taskEntity.getFsUserId(), null, arg.getDepartmentIdList());
            } else {
                List<Integer> qywxDepartmentIdList = arg.getDepartmentIdList().stream().map(e -> e - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(ea, qywxDepartmentIdList);
            }
            if (CollectionUtils.isEmpty(qywxUserIds)) {
                return Result.newSuccess(pageResult);
            }
        }

        com.github.mybatis.pagination.Page<ListEmployeeQywxGroupSendDetailResult> page = new com.github.mybatis.pagination.Page<>(arg.getPageNum(), arg.getPageSize(), false);
        List<ListEmployeeQywxGroupSendDetailDto> dtoList;
        if (taskEntity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
            dtoList = sendResultDAO.queryQywxGroupSendDetailByEmployee(ea, arg.getMarketingActivityId(), qywxUserIds, arg.getStatus(), page);
            int totalCount = sendResultDAO.countQywxGroupSendDetailByEmployee(ea, arg.getMarketingActivityId(), qywxUserIds, arg.getStatus());
            pageResult.setTotalCount(totalCount);
        } else {
            List<String> taskIds = Lists.newArrayList();
            taskIds.add(taskEntity.getId());
            dtoList = qywxGroupSendGroupResultDAO.listEmployeeQywxGroupSendDetail(ea, taskIds, qywxUserIds, page);
            dtoList.forEach(e -> {
                int successGroupCount = 0;
                try {
                    successGroupCount = Integer.parseInt(e.getSuccessGroupCount());
                } catch (Exception ignor) {}
                if (successGroupCount != 0) {
                    e.setStatus(1);
                } else {
                    e.setStatus(0);
                }
            });
            int totalCount = qywxGroupSendGroupResultDAO.countEmployeeQywxGroupSendDetail(ea, taskIds, qywxUserIds);
            pageResult.setTotalCount(totalCount);
        }
        if (CollectionUtils.isEmpty(dtoList)) {
            return Result.newSuccess(pageResult);
        }
        List<ListEmployeeQywxGroupSendDetailResult> employeeGroupSendDetailResultList = Lists.newArrayList();
        for (ListEmployeeQywxGroupSendDetailDto dto : dtoList) {
            ListEmployeeQywxGroupSendDetailResult result = BeanUtil.copyProperties(dto, ListEmployeeQywxGroupSendDetailResult.class);
            if (taskEntity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
                result.setChatType(QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType());
            } else {
                result.setChatType(QywxGroupSendOjbectTypeEnum.SEND_TO_GROUP.getType());
                result.setSendTime(dto.getGroupSendTime().getTime());
            }
            employeeGroupSendDetailResultList.add(result);
        }
        pageResult.setResult(employeeGroupSendDetailResultList);
        fillNameAndDepartment(taskEntity.getEa(), employeeGroupSendDetailResultList);

        return Result.newSuccess(pageResult);
    }

    private void fillNameAndDepartment(String ea, List<ListEmployeeQywxGroupSendDetailResult> employeeGroupSendDetailResultList) {
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea) || fsBindManager.isQywxContactFsSetting(ea);
        if (isOpen) {
            List<String> userIds = employeeGroupSendDetailResultList.stream().map(ListEmployeeQywxGroupSendDetailResult::getEmployeeId).collect(Collectors.toList());
            Map<String, Integer> fsUserIdByQyWxInfo = qywxUserManager.getFsUserIdByQyWxInfo(ea, userIds, true, false);
            List<Integer> fsUserIds = Lists.newArrayList(fsUserIdByQyWxInfo.values());
            Map<Integer, FSEmployeeMsg> employeeInfoByUserIds = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIds, true);
            for (ListEmployeeQywxGroupSendDetailResult result : employeeGroupSendDetailResultList) {
                FSEmployeeMsg fsEmployeeMsg = employeeInfoByUserIds.get(fsUserIdByQyWxInfo.get(result.getEmployeeId()));
                if (fsEmployeeMsg != null) {
                    result.setEmployeeName(fsEmployeeMsg.getName());
                    result.setDepartment(fsEmployeeMsg.getDepartment());
                }
            }
        } else {
            List<String> qyUserIds = employeeGroupSendDetailResultList.stream().map(ListEmployeeQywxGroupSendDetailResult::getEmployeeId).collect(Collectors.toList());
            List<QywxEmployeeResult> qywxEmployeeResults = qywxEmployeeManager.batchByQyUserIds(ea, qyUserIds, false);
            Map<String, QywxEmployeeResult> qywxEmployeeMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(qywxEmployeeResults)) {
                qywxEmployeeMap = qywxEmployeeResults.stream().collect(Collectors.toMap(QywxEmployeeResult::getUserId, Function.identity(), (v1, v2) -> v1));
            }
            for (ListEmployeeQywxGroupSendDetailResult result : employeeGroupSendDetailResultList) {
                QywxEmployeeResult qywxEmployeeResult = qywxEmployeeMap.get(result.getEmployeeId());
                if (qywxEmployeeResult != null) {
                    result.setEmployeeName(qywxEmployeeResult.getName());
                    result.setDepartment(qywxEmployeeResult.getWechatDepartmentName());
                }
            }
        }
    }

    public Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> listSopQywxMsgEmployeeRanking(String ea,
                                                                                                            ListSopQywxMsgEmployeeRankingArg arg) {
        PageResult<ListQywxMarketingActivityEmployeeRankingResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        List<ListQywxMarketingActivityEmployeeRankingResult> employeeRankingResults = Lists.newArrayList();
        pageResult.setResult(employeeRankingResults);
        TriggerSnapshotEntity currentUseSnapshot = triggerSnapshotDao.getCurrentUseSnapshot(ea, arg.getTriggerId());
        Long start = null;
        Long end = null;
        if (TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(currentUseSnapshot.getTriggerType())) {
            Preconditions.checkNotNull(arg.getQueryTime() != null, I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2208));
            Date date = new Date(arg.getQueryTime());
            start = DateUtil.getTimesMorning(date);
            end = DateUtil.getTimesNight(date);
        }
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), true);
        List<SopQywxTaskResultDTO> resultDTOS = triggerTaskInstanceDao.listQywxMsgCustomer(ea, currentUseSnapshot.getTriggerId(), currentUseSnapshot.getId(), start, end, page);

        if (CollectionUtils.isEmpty(resultDTOS)) {
            log.info("listSopQywxMsgEmployeeRanking sop qywx is not exist ea{} arg:{}", ea, arg);
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        Map<String, String> customerNameMap = new HashMap<>();
        List<String> employeeIdSet = resultDTOS.stream().filter(r -> StringUtils.isNotBlank(r.getUserId())).map(r -> r.getUserId()).collect(Collectors.toList());
        List<String> customerIds = resultDTOS.stream().filter(r -> StringUtils.isNotBlank(r.getExternalUserId())).map(r -> r.getExternalUserId()).collect(Collectors.toList());
        //查询客户姓名
        Set<String> externalUserIds = new HashSet<>(customerIds);
        Map<String, ObjectData> crmWxWorkExternalDataMap = wechatWorkExternalUserObjManager.getObjectDataMap(ea, externalUserIds);
        crmWxWorkExternalDataMap.forEach((k, v) -> customerNameMap.put(k, v.getName()));
        List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryEaAndUserId(ea, employeeIdSet);
        if (CollectionUtils.isEmpty(qyWxAddressBookEntities)) {
            log.warn("listSopQywxMsgEmployeeRanking qywxAddressBookManager.queryEaAndUserId qyWxAddressBookEntities is null");
            return Result.newSuccess(pageResult);
        }
        Map<String, String> employeeNameMap = qyWxAddressBookEntities.stream().filter(qyWxAddressBookEntity -> qyWxAddressBookEntity.getName() != null).collect(Collectors.toMap(QyWxAddressBookEntity::getUserId, QyWxAddressBookEntity::getName, (o, n) -> o));
        for (SopQywxTaskResultDTO resultDTO : resultDTOS) {
            ListQywxMarketingActivityEmployeeRankingResult temp = new ListQywxMarketingActivityEmployeeRankingResult();
            temp.setStatus(null == resultDTO.getStatus() ? 0 : resultDTO.getStatus());
            temp.setCustomerName(customerNameMap.get(resultDTO.getExternalUserId()));
            temp.setEmployeeName(employeeNameMap.get(resultDTO.getUserId()));
            employeeRankingResults.add(temp);
        }
        return Result.newSuccess(pageResult);
    }

    public QueryCustomerGroupListResult queryCachedGroupDetail(String ea, String groupId) {
        QueryCustomerGroupListResult groupListResult = redisManager.getQYWXCustomerGroup(groupId);
        if (groupListResult == null) {
            ThreadPoolUtils.execute(() -> {
                CustomerGroupDetailResult groupDetailResult = customerGroupManager.queryQywxGroupDetail(ea, groupId);
                if (groupDetailResult == null || groupDetailResult.getQywxGroupChat() == null) {
                    log.warn("query qywx customer group faild call groupchat/get api return error ea:{} groupId:{}", ea, groupId);
                    return;
                }

                CustomerGroupDetailResult.QywxGroupChat groupChat = groupDetailResult.getQywxGroupChat();
                QueryCustomerGroupListResult newGroupListResult = new QueryCustomerGroupListResult();
                newGroupListResult.setGroupId(groupChat.getGroupId());
                newGroupListResult.setGroupName(groupChat.getGroupName());
                newGroupListResult.setGroupOwner(StringUtils.isNotBlank(groupChat.getOwnerName()) ? groupChat.getOwnerName() : groupChat.getGroupOwner());
                newGroupListResult.setCreateTime(groupChat.getCreateTime());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupChat.getQywxGroupMembers())) {
                    newGroupListResult.setGroupMemberCount(groupChat.getQywxGroupMembers().size());
                }

                //写到redis缓存
                redisManager.setQYWXCustomerGroup(groupId, newGroupListResult);
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }

        return groupListResult;
    }

    /**
     * 查询企业微信群发消息列表
     *
     * @param vo
     * @return
     */
    public Result<PageResult<ListGroupSendMessageResult>> listGroupSendMessage(String ea, Integer fsUserId, ListGroupSendMessageVO vo) {
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        List<ListGroupSendMessageResult> groupSendMessageResult = Lists.newArrayList();
        pageResult.setResult(groupSendMessageResult);

        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (StringUtils.isNotEmpty(vo.getTitle())) {
            marketingActivityArg.setName(vo.getTitle());
        }
        marketingActivityArg.setSpreadType(MarketingActivitySpreadTypeEnum.QYWX_GROUP_SEND.getSpreadType());
        //从营销活动对象获取数据
        marketingActivityArg.setPageSize(vo.getPageSize());
        marketingActivityArg.setPageNumber((vo.getPageNum() - 1) * vo.getPageSize());
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> pageMarketingActivityList = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg);
        if (pageMarketingActivityList == null || CollectionUtils.isEmpty(pageMarketingActivityList.getDataList())) {
            return Result.newSuccess(pageResult);
        }
        List<String> marketingEventIds = pageMarketingActivityList.getDataList().stream().map(objectData -> objectData.getString("marketing_event_id")).collect(Collectors.toList());
        List<com.facishare.marketing.api.data.MarketingEventData> eventDataList = null;
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            eventDataList = marketingEventManager.listMarketingEventData(ea, fsUserId, marketingEventIds);
        }
        Map<String, com.facishare.marketing.api.data.MarketingEventData> marketingEventTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(eventDataList)) {
            for (com.facishare.marketing.api.data.MarketingEventData marketingEventData : eventDataList) {
                marketingEventTypeMap.put(marketingEventData.getId(), marketingEventData);
            }
        }

        Map<String, String> activityEntityMap = null;
        List<ActivityEntity> activityEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, marketingEventIds);
        if (CollectionUtils.isNotEmpty(activityEntityList)) {
            activityEntityMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, ActivityEntity::getId, (k1, k2) -> k1));
        }
        Map<String, ObjectData> idToMarketingActivityDataMap = pageMarketingActivityList.getDataList().stream().collect(Collectors.toMap(ObjectData::getId, Function.identity()));
        List<String> marketingActivityIds = pageMarketingActivityList.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        List<QywxGroupSendTaskEntity> sendTaskEntityList = sendTaskDAO.listGroupMessageByMarketingActivityIds(vo.getEa(), vo.getTitle(), marketingActivityIds);
        if (CollectionUtils.isEmpty(sendTaskEntityList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(pageMarketingActivityList.getTotal());

        List<Integer> createUserIds = Lists.newArrayList();
        List<String> msgIds = Lists.newArrayList();
        List<String> taskIds = Lists.newArrayList();
        List<String> covers = Lists.newArrayList();
        Map<String, ListGroupSendMessageResult> coverMap = new HashMap<>();
        sendTaskEntityList.forEach(entity -> {
            ListGroupSendMessageResult sendMessageResult = new ListGroupSendMessageResult();
            sendMessageResult.setId(entity.getId());
            sendMessageResult.setChatType(entity.getChatType());
            sendMessageResult.setTitle(entity.getTitle());
            sendMessageResult.setErrorCode(entity.getErrcode());
            sendMessageResult.setErrorMsg(entity.getErrmsg());
            sendMessageResult.setStatus(entity.getStatus());
            sendMessageResult.setContent(entity.getContent());
            MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, QYWX_GROUP_SEND_MESSAGE.getType(), entity.getId());
            if(configEntity.getExternalConfig()!=null && configEntity.getExternalConfig().getQywxGroupSendMessageVO() != null&&configEntity.getExternalConfig().getQywxGroupSendMessageVO().getQywxGroupSendMessageType()!=null){
                sendMessageResult.setQywxGroupSendMessageType(configEntity.getExternalConfig().getQywxGroupSendMessageVO().getQywxGroupSendMessageType());
            }

            if (entity.getStatus() == QywxGroupSendTaskStatusEnum.WAITING.getStatus()
                    && (entity.getSendType() != null && entity.getSendType() == QywxGroupSendTypeEnum.SCHEDULE_SEND.getType())) {
                sendMessageResult.setSendCancelable(true);
            } else {
                sendMessageResult.setSendCancelable(false);
            }

            String coverPath = null;
            if (entity.getMsgType() == QywxGroupSendMsgTypeEnum.MINIPROGRAM.getType()) {
                coverPath = entity.getMiniPicPath();
            }
            if (entity.getMsgType() == QywxGroupSendMsgTypeEnum.IMAGE.getType()) {
                //兼容一下数据问题
                if (StringUtils.isNotBlank(entity.getImagePath())) {
                    if (entity.getImagePath().startsWith("A_")) {
                        coverPath = entity.getImagePath();
                    } else if (entity.getImagePath().startsWith("C_")) {
                        sendMessageResult.setCover(fileV2Manager.getUrlByPath(ea, entity.getImagePath()));
                    } else {
                        sendMessageResult.setCover(entity.getImagePath());
                    }
                }
            }
            if (StringUtils.isNotBlank(coverPath)) {
                sendMessageResult.setCover(coverPath);
                coverMap.put(coverPath, sendMessageResult);
                covers.add(coverPath);
            }
            sendMessageResult.setCreateUserId(entity.getFsUserId());
            sendMessageResult.setMarketingActivityId(entity.getMarketingActivityId());
            if (entity.getFixedTime() != null) {
                sendMessageResult.setCreateTime(entity.getFixedTime());
            } else {
                sendMessageResult.setCreateTime(entity.getCreateTime().getTime());
            }
            sendMessageResult.setMsgId(entity.getMsgid());
            Integer createUserId = entity.getFsUserId();
            createUserIds.add(createUserId);
            marketingActivityIds.add(entity.getMarketingActivityId());
            groupSendMessageResult.add(sendMessageResult);
            if (entity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
                if (StringUtils.isNotBlank(entity.getMsgid())) {
                    msgIds.addAll(Splitter.on(",").trimResults().splitToList(entity.getMsgid()));
                }
            } else {
                taskIds.add(entity.getId());
            }
        });

        if (CollectionUtils.isNotEmpty(covers)) {
            Map<String, String> pathMap = fileV2Manager.batchGetUrlByPath(covers, vo.getEa(), false);
            for (ListGroupSendMessageResult messageResult : groupSendMessageResult) {
                if (pathMap.containsKey(messageResult.getCover())) {
                    String url = pathMap.get(messageResult.getCover());
                    messageResult.setCover(url);
                }
            }
        }

        //获取统计结果，获取线索数
        Map<String, Integer> marketingActivityClueNum = null;
        if (CollectionUtils.isNotEmpty(marketingActivityIds)) {
            // 线索从表单获取
            marketingActivityClueNum = customizeFormClueManager.batchCountClueNumByMarketingActivityIds(vo.getEa(), marketingActivityIds, false, 0);
        }

        //批量查询创建人的姓名
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), createUserIds, true);

        //查询发送结果
        List<QueryUserGroupByMsgIdsDTO> userGroupByMsgIds = null;
        Map<String, SendGroupStatDTO> groupResultStatMap = null;
        if (CollectionUtils.isNotEmpty(msgIds)) {
            userGroupByMsgIds = sendResultDAO.queryUserGroupByMsgIds(ea, msgIds);
        }
        if (CollectionUtils.isNotEmpty(taskIds)) {
            groupResultStatMap = getSendGroupStat(ea, taskIds);
        }
        for (ListGroupSendMessageResult result : groupSendMessageResult) {
            ObjectData objectData = idToMarketingActivityDataMap.get(result.getMarketingActivityId());
            if (objectData != null) {
                MarketingActivityData marketingActivityData = MarketingActivityData.wrap(objectData);
                result.setMarketingActivityName(marketingActivityData.getName());
                result.setMarketingEventId(marketingActivityData.getMarketingEventId());
                result.setMarketingEventName(marketingActivityData.getMarketingEventIdName());
                if (marketingEventTypeMap.get(marketingActivityData.getMarketingEventId()) != null) {
                    result.setMarketingEventType(marketingEventTypeMap.get(marketingActivityData.getMarketingEventId()).getEventType());
                    if (activityEntityMap != null && activityEntityMap.get(marketingActivityData.getMarketingEventId()) != null) {
                        result.setMarketingObjectId(activityEntityMap.get(marketingActivityData.getMarketingEventId()));
                    }
                }
                result.setAuditStatus(objectData.getLifeStatus());
            }
            //从统计结果中取值，填充
            if (marketingActivityClueNum.get(result.getMarketingActivityId()) != null) {
                result.setLeadCount(marketingActivityClueNum.get(result.getMarketingActivityId()));
            } else {
                result.setLeadCount(0);
            }

            if (MapUtils.isNotEmpty(fsEmployeeMsgMap)) {
                FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(result.getCreateUserId());
                if (fsEmployeeMsg != null) {
                    result.setCreator(fsEmployeeMsg.getName());
                }
            }
            if (CollectionUtils.isNotEmpty(userGroupByMsgIds) && result.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
                int sendEmployeeCount = 0;
                int sendCustomerCount = 0;
                int unsendEmployeeCount = 0;
                int unsendCustomerCount = 0;
                Set<String> sendEmployeeSet = new HashSet<>();
                List<String> msgIdList = new ArrayList<>();
                if (StringUtils.isNotBlank(result.getMsgId())) {
                    msgIdList = Splitter.on(",").trimResults().splitToList(result.getMsgId());
                }
                for (QueryUserGroupByMsgIdsDTO dto : userGroupByMsgIds) {
                    if (msgIdList.contains(dto.getMsgId())) {
                        if (dto.getStatus() == QywxGroupSendMsgStatus.NOT_SEND.getType()) {
                            unsendEmployeeCount++;
                            unsendCustomerCount += dto.getCount();
                        } else {
                            sendEmployeeSet.add(dto.getUserId());   //防止同一个员工既有失败，又有成功，计算成2个人。
                            sendCustomerCount += dto.getCount();
                        }
                    }
                }
                sendEmployeeCount = sendEmployeeSet.size();
                result.setSendEmployeeCount(sendEmployeeCount);
                result.setSendCustomerCount(sendCustomerCount);
                result.setUnsendEmployeeCount(unsendEmployeeCount);
                result.setUnsendCustomerCount(unsendCustomerCount);
            }

            if (result.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_GROUP.getType() && groupResultStatMap != null
                    && groupResultStatMap.get(result.getId()) != null) {
                SendGroupStatDTO dto = groupResultStatMap.get(result.getId());
                if (dto != null) {
                    result.setSendEmployeeCount(dto.getSendEmployeeCount());
                    result.setSendCustomerCount(dto.getSendCustomerCount());
                    result.setUnsendEmployeeCount(dto.getUnsendEmployeeCount());
                    result.setUnsendCustomerCount(dto.getUnsendCustomerCount());
                }
            }
        }

        return Result.newSuccess(pageResult);
    }

    public Map<String, SendGroupStatDTO> getSendGroupStat(String ea, List<String> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return null;
        }

        List<QywxGroupSendGroupResultEntity> groupResultEntities = null;
        if (CollectionUtils.isNotEmpty(taskIds)) {
            groupResultEntities = qywxGroupSendGroupResultDAO.queryGroupResult(ea, taskIds);
            if (CollectionUtils.isEmpty(groupResultEntities)) {
                return null;
            }
        }

        Map<String, SendGroupStatDTO> statMap = new HashMap<>();
        Map<String, List<QywxGroupSendGroupResultEntity>> entityMap = new HashMap<>();
        for (QywxGroupSendGroupResultEntity entity : groupResultEntities) {
            if (entityMap.get(entity.getSendid()) == null) {
                entityMap.put(entity.getSendid(), new ArrayList<>());

            }
            entityMap.get(entity.getSendid()).add(entity);
        }

        for (Map.Entry<String, List<QywxGroupSendGroupResultEntity>> entry : entityMap.entrySet()) {
            String sendId = entry.getKey();
            List<QywxGroupSendGroupResultEntity> entityList = entry.getValue();
            if (CollectionUtils.isEmpty(entityList)) {
                SendGroupStatDTO dto = new SendGroupStatDTO();
                statMap.put(sendId, dto);
            } else {
                int sendEmployeeCount = 0;   //已发送员工数量
                int sendCustomerCount = 0;   //已发送群数量
                int unsendEmployeeCount = 0; //未发送员工数量
                int unsendCustomerCount = 0; //未发送群数量
                for (QywxGroupSendGroupResultEntity resultEntity : entityList) {
                    if (resultEntity.getSuccessCount() != 0) {
                        sendEmployeeCount++;
                        sendCustomerCount += resultEntity.getSuccessCount();
                    } else {
                        unsendEmployeeCount++;
                        unsendCustomerCount += resultEntity.getTotalGroupCount();
                    }
                }
                SendGroupStatDTO dto = new SendGroupStatDTO();
                dto.setSendEmployeeCount(sendEmployeeCount);
                dto.setSendCustomerCount(sendCustomerCount);
                dto.setUnsendEmployeeCount(unsendEmployeeCount);
                dto.setUnsendCustomerCount(unsendCustomerCount);
                statMap.put(sendId, dto);
            }
        }
        return statMap;
    }

    /**
     * 全员推广发送微信小程序消息
     *
     * @param ea
     * @param allUserIds
     * @param title
     * @param description
     * @param startTime
     * @param contentType
     * @param objectId
     * @param marketingActivityId
     */
    public void sendQywxSpreadMiniAppMessage(String ea, List<Integer> allUserIds, String title, String description, Date startTime,
                                             int contentType, String objectId, String marketingActivityId,boolean multiple) {
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null) {
            return;
        }
        if (CollectionUtils.isEmpty(allUserIds)) {
            return;
        }
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (miniappConfigEntity == null) {
            log.info("sendQywxSpreadMiniAppMessage failed not set miniapp config ea:{}", ea);
            return;
        }
        if (title == null) {
            log.info("sendQywxSpreadMiniAppMessage failed title is null ea:{} title:{}", ea, title);
            return;
        }
        Map<Integer, String> qywxUserIdMap = qywxUserManager.getQyUserIdByFsUserInfo(ea, allUserIds);
        if (MapUtils.isEmpty(qywxUserIdMap)) {
            return;
        }
        List<String> touser;
        touser = Lists.newArrayList(qywxUserIdMap.values());
        touser = touser.stream().distinct().collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(touser, groupMessageBatchNum);
        for (List<String> partition : partitions) {
            List<String> finalTouser = touser;
            ThreadPoolUtils.execute(() -> {
                SpreadQywxMiniappMessageResult result = this.sendSpreadQywxMiniappMessage(ea, title, description, startTime, contentType, objectId, marketingActivityId, miniappConfigEntity, partition,multiple);
                if (result.isSuccess()) {
                    log.info("sendQywxSpreadMiniAppMessage partition:{}/{} success invalidUser:{}", partition.size(), finalTouser.size(), result.getInvalidUser());
                } else {
                    log.warn("sendQywxSpreadMiniAppMessage partition:{}/{} failed ea:{} errorCode:{} errorMsg:{}", partition.size(), finalTouser.size(), ea, result.getErrcode(), result.getErrmsg());
                }
            }, ThreadPoolTypeEnums.SEND_NOTCIE);
        }
    }

    /**
     * 全员推广发送代开发消息
     *
     * @param noticeId
     * @param ea
     * @param allUserIds
     * @param title
     * @param description
     * @param startTime
     * @param objectId
     * @param marketingActivityId
     */
    public void sendQywxAgentAppMessage(String noticeId, String ea, List<Integer> allUserIds, String title, String description, Date startTime,
                                        int contentType, String objectId, String marketingActivityId, String cover,boolean multiple) {
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null) {
            return;
        }
        List<QywxCustomerAppInfoEntity> appInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(qywxCorpAgentConfigEntity.getCorpid(), qywxCorpAgentConfigEntity.getEa());
        if (appInfoEntities.size() == 0) {
            log.info("sendQywxAgentAppMessage failed not set agentApp config ea:{}", ea);
            return;
        }
        if (CollectionUtils.isEmpty(allUserIds)) {
            return;
        }
        if (title == null) {
            log.info("sendQywxAgentAppMessage failed title is null ea:{} title:{}", ea, title);
            return;
        }
        Map<Integer, String> qywxUserIdMap = qywxUserManager.getQyUserIdByFsUserInfo(ea, allUserIds);
        if (MapUtils.isEmpty(qywxUserIdMap)) {
            return;
        }
        List<String> touser;
        touser = Lists.newArrayList(qywxUserIdMap.values());
        touser = touser.stream().distinct().collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(touser, groupMessageBatchNum);
        for (List<String> partition : partitions) {
            List<String> finalTouser = touser;
            ThreadPoolUtils.execute(() -> {
                SpreadQywxMiniappMessageResult result = this.sendSpreadQywxAgentppMessage(noticeId, ea, title, description, startTime, contentType, objectId, marketingActivityId, appInfoEntities.get(0), partition, cover,multiple);
                if (result.isSuccess()) {
                    log.info("sendQywxAgentAppMessage partition:{}/{} success invalidUser:{}  invalidParty:{}  invalidtag:{} unlicensedUser:{}", partition.size(), finalTouser.size(), result.getInvalidUser(), result.getInvalidParty(), result.getInvalidTag(), result.getUnlicensedUser());
                } else {
                    log.warn("sendQywxAgentAppMessage partition:{}/{} failed ea:{} errorCode:{} errorMsg:{}", partition.size(), finalTouser.size(), ea, result.getErrcode(), result.getErrmsg());
                }
            }, ThreadPoolTypeEnums.SEND_NOTCIE);
        }
    }

    /**
     * 营销助手发送SOP 员工互动通知
     * @param ea
     * @param allUserIds
     * @param content
     * @param url
     */
    public void sendQywxH5AgentMessage(String ea, List<Integer> allUserIds, List<OfficeMessageArg.LabelWarp> content,String url){
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null){
            return;
        }
        List<QywxCustomerAppInfoEntity> appInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(qywxCorpAgentConfigEntity.getCorpid(), qywxCorpAgentConfigEntity.getEa());
        if (appInfoEntities.size() == 0){
            log.info("sendQywxH5AgentMessage failed not set agentApp config ea:{}", ea);
            return;
        }
        if (CollectionUtils.isEmpty(allUserIds)){
            return;
        }
        Map<Integer, String> qywxUserIdMap = qywxUserManager.getQyUserIdByFsUserInfo(ea, allUserIds);
        if (MapUtils.isEmpty(qywxUserIdMap)) {
            return;
        }
        List<String> touser;
        touser = Lists.newArrayList(qywxUserIdMap.values());
        touser = touser.stream().distinct().collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(touser, groupMessageBatchNum);
        for (List<String> partition : partitions) {
            List<String> finalTouser = touser;
            ThreadPoolUtils.execute(() -> {
                SpreadQywxMiniappMessageResult result = this.sendSopQywxAgentH5Message(ea, content,url, appInfoEntities.get(0), partition);
                if (result.isSuccess()) {
                    log.info("sendQywxH5AgentMessage partition:{}/{} success invalidUser:{}  invalidParty:{}  invalidtag:{} unlicensedUser:{}", partition.size(), finalTouser.size(), result.getInvalidUser(), result.getInvalidParty(), result.getInvalidTag(), result.getUnlicensedUser());
                } else {
                    log.warn("sendQywxH5AgentMessage partition:{}/{} failed ea:{} errorCode:{} errorMsg:{}", partition.size(), finalTouser.size(), ea, result.getErrcode(), result.getErrmsg());
                }
            }, ThreadPoolTypeEnums.SEND_NOTCIE);
        }
    }

    private SpreadQywxMiniappMessageResult sendSopQywxAgentH5Message(String ea, List<OfficeMessageArg.LabelWarp> content, String url, QywxCustomerAppInfoEntity appInfoEntity, List<String> touser) {
        String accessToken = qywxManager.getAgentAccessToken(appInfoEntity.getCorpId(), appInfoEntity.getSuitId(), appInfoEntity.getAuthCode());
        QywxAgentMessageArg arg = new QywxAgentMessageArg();
        StringBuilder toUserString = new StringBuilder();
        for (int i = 0; i < touser.size(); i++){
            toUserString.append(touser.get(i));
            if (i != touser.size() -1){
                toUserString.append("|");
            }
        }
        arg.setTouser(toUserString.toString());
        arg.setMsgtype("template_card");
        QywxAgentMessageArg.TemplateCard templateCard = new QywxAgentMessageArg.TemplateCard();
        arg.setTemplate_card(templateCard);
        QywxAgentMessageArg.TemplateCard.Source source = new QywxAgentMessageArg.TemplateCard.Source();
        source.setDesc(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2704));
        source.setIcon_url(qywxManager.getAppLogo(ea));
        templateCard.setSource(source);
        templateCard.setCard_type("text_notice");
        QywxAgentMessageArg.TemplateCard.Main_title mainTitle = new QywxAgentMessageArg.TemplateCard.Main_title();
        mainTitle.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2709));
        templateCard.setMain_title(mainTitle);
        if (content != null){
            ArrayList<QywxAgentMessageArg.TemplateCard.Horizontal_content_list> horizontalContentLists = new ArrayList<>();
            content.forEach(item -> {
                QywxAgentMessageArg.TemplateCard.Horizontal_content_list hct = new QywxAgentMessageArg.TemplateCard.Horizontal_content_list();
                hct.setKeyname(item.getLabel());
                hct.setValue(item.getValue());
                horizontalContentLists.add(hct);
            });
            templateCard.setHorizontal_content_list(horizontalContentLists);
        }
        QywxAgentMessageArg.TemplateCard.Jump_list jumpList = new QywxAgentMessageArg.TemplateCard.Jump_list();
        jumpList.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2722));
        jumpList.setUrl(url);
        templateCard.setJump_list(Lists.newArrayList(jumpList));
        QywxAgentMessageArg.TemplateCard.Card_action cardAction = new QywxAgentMessageArg.TemplateCard.Card_action();
        cardAction.setType(1);
        cardAction.setUrl(url);
        templateCard.setCard_action(cardAction);
        arg.setAgentid(Integer.parseInt(appInfoEntity.getAgentId()));
        SpreadQywxMiniappMessageResult result = qywxManager.sendAgentMessage(accessToken, arg);
        return result;
    }

    private SpreadQywxMiniappMessageResult sendSpreadQywxAgentppMessage(String noticeId, String ea, String title, String description, Date startTime, int contentType, String objectId, String marketingActivityId, QywxCustomerAppInfoEntity appInfoEntity, List<String> touser, String cover,boolean multiple) {
        String accessToken = qywxManager.getAgentAccessToken(appInfoEntity.getCorpId(), appInfoEntity.getSuitId(), appInfoEntity.getAuthCode());
        QywxAgentMessageArg arg = new QywxAgentMessageArg();
        String toUserString = "";
        for (int i = 0; i < touser.size(); i++) {
            toUserString += touser.get(i);
            if (i != touser.size() - 1) {
                toUserString += "|";
            }
        }
        arg.setTouser(toUserString);
        arg.setMsgtype("template_card");
        QywxAgentMessageArg.TemplateCard templateCard = new QywxAgentMessageArg.TemplateCard();
        arg.setTemplate_card(templateCard);
        QywxAgentMessageArg.TemplateCard.Source source = new QywxAgentMessageArg.TemplateCard.Source();
        source.setDesc(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2704));
        source.setIcon_url(qywxManager.getAppLogo(ea));
        templateCard.setSource(source);
        QywxAgentMessageArg.TemplateCard.Main_title mainTitle = new QywxAgentMessageArg.TemplateCard.Main_title();
        mainTitle.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2753));
        templateCard.setMain_title(mainTitle);
        QywxAgentMessageArg.TemplateCard.Card_image cardImage = new QywxAgentMessageArg.TemplateCard.Card_image();
        cardImage.setUrl(cover);
        cardImage.setAspect_ratio(1.8);
        templateCard.setCard_image(cardImage);
        QywxAgentMessageArg.TemplateCard.Vertical_content_list verticalContentList = new QywxAgentMessageArg.TemplateCard.Vertical_content_list();
        verticalContentList.setTitle(title);
        ArrayList<QywxAgentMessageArg.TemplateCard.Vertical_content_list> vertical_content_lists = Lists.newArrayList(verticalContentList);
        templateCard.setVertical_content_list(vertical_content_lists);

        ArrayList<QywxAgentMessageArg.TemplateCard.Horizontal_content_list> horizontalContentLists = new ArrayList<>();
        QywxAgentMessageArg.TemplateCard.Horizontal_content_list horizontalContentDescList = new QywxAgentMessageArg.TemplateCard.Horizontal_content_list();
        horizontalContentDescList.setKeyname(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2766));
        horizontalContentDescList.setValue(description);
        QywxAgentMessageArg.TemplateCard.Horizontal_content_list horizontalContentTimeList = new QywxAgentMessageArg.TemplateCard.Horizontal_content_list();
        horizontalContentTimeList.setKeyname(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2769));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        horizontalContentTimeList.setValue(month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972) + day + I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1));
        horizontalContentLists.add(horizontalContentDescList);
        horizontalContentLists.add(horizontalContentTimeList);
        templateCard.setHorizontal_content_list(horizontalContentLists);

        QywxAgentMessageArg.TemplateCard.Jump_list jumpList = new QywxAgentMessageArg.TemplateCard.Jump_list();
        jumpList.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2780));
        String url = null;
        if (multiple) {
            url = host + "/proj/page/marketing/" + ea + "#/pkgs/pkg-spread/pages/multi-material/multi-material?marketingActivityId="+marketingActivityId+"&ea="+ea;
        } else if (noticeManager.isEmployeeEmailSpread(ea, marketingActivityId)){
            MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByEaAndMarketingActivityId(ea, marketingActivityId);
            url = host + "/proj/page/marketing/" + ea + "/#/mail-notice?noticeId=" + noticeId + "&ea=" + ea + "&marketingEventId=" + configEntity.getMarketingEventId() + "&marketingActivityId=" + marketingActivityId;
        } else {
            NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(contentType);
            int objectType = noticeContentTypeEnum.toObjectType();
            Map<String, String> param = Maps.newHashMap();
            param.put("objectId", objectId);
            param.put("objectType", objectType + "");
            param.put("isGroupSend", 1 + "");
            param.put("marketingActivityId", marketingActivityId);
            param.put("spreadType", QywxSpreadTypeEnum.ALL_SPREAD.getType() + "");
            param.put("ea", ea);
            //区分来源
            param.put("source", MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType() + "");
            param.put("noticeId", noticeId);
            url = host + "/proj/page/marketing/" + ea + "#" + "/pages/share/share?" + httpManager.transformUrlParams(param);
        }
        jumpList.setUrl(url);
        templateCard.setJump_list(Lists.newArrayList(jumpList));
        QywxAgentMessageArg.TemplateCard.Card_action cardAction = new QywxAgentMessageArg.TemplateCard.Card_action();
        cardAction.setType(1);
        cardAction.setUrl(url);
        templateCard.setCard_action(cardAction);
        arg.setAgentid(Integer.valueOf(appInfoEntity.getAgentId()));
        SpreadQywxMiniappMessageResult result = qywxManager.sendAgentMessage(accessToken, arg);
        return result;
    }

    private SpreadQywxMiniappMessageResult sendSpreadQywxMiniappMessage(String ea, String title, String description, Date startTime, int contentType, String objectId, String marketingActivityId, QywxMiniappConfigEntity miniappConfigEntity, List<String> touser,boolean multiple) {
        String accessToken = qywxManager.getMiniAppAccessToken(ea);

        QywxSpreadMiniAppMessageArg arg = new QywxSpreadMiniAppMessageArg();
        String toUserString = "";
        for (int i = 0; i < touser.size(); i++) {
            toUserString += touser.get(i);
            if (i != touser.size() - 1) {
                toUserString += "|";
            }
        }
        arg.setTouser(toUserString);
        QywxSpreadMiniAppMessageArg.MiniprogramNotice miniprogramNotice = new QywxSpreadMiniAppMessageArg.MiniprogramNotice();
        arg.setMiniprogramNotice(miniprogramNotice);
        miniprogramNotice.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2824));

        List<Map<String, String>> contentItem = new ArrayList<>();
        miniprogramNotice.setContentItem(contentItem);
        Map<String, String> titleContent = Maps.newHashMap();
        titleContent.put("key", I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_565));
        if (title.length() > 30) {
            titleContent.put("value", title.substring(0, 30));
        } else {
            titleContent.put("value", title);
        }
        contentItem.add(titleContent);
        Map<String, String> descMap = new HashMap<>();
        descMap.put("key", I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2766));
        if (description.length() > 30) {
            descMap.put("value", description.substring(0, 30));
        } else {
            descMap.put("value", description);
        }
        contentItem.add(descMap);
        Map<String, String> startTimeMap = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        startTimeMap.put("key", I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2769));
        startTimeMap.put("value", month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972) + day + I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1));
        contentItem.add(startTimeMap);

        String page = null;
        if (multiple) {
            page = "pkgs/pkg-spread/pages/multi-material/multi-material?marketingActivityId="+marketingActivityId;
        } else if (noticeManager.isEmployeeEmailSpread(ea, marketingActivityId)) {
            MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByEaAndMarketingActivityId(ea, marketingActivityId);
            page = "pkgs/pkg-spread/pages/mail-notice/index?" +
                    "noticeId=" + configEntity.getAssociateId() + "&ea=" + ea + "&marketingEventId=" + configEntity.getMarketingEventId() + "&marketingActivityId=" + configEntity.getMarketingActivityId();
        } else {
            NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(contentType);
            int objectType = noticeContentTypeEnum.toObjectType();
            Map<String, String> param = Maps.newHashMap();
            param.put("objectId", objectId);
            param.put("objectType", objectType + "");
            param.put("isGroupSend", 1 + "");
            param.put("marketingActivityId", marketingActivityId);
            param.put("spreadType", QywxSpreadTypeEnum.ALL_SPREAD.getType() + "");
            page = "/pages/share/share?" + httpManager.transformUrlParams(param);
            page += "&showShareBar=" + true;
        }
        WxAppInfoEnum wxAppInfo = WxAppInfoEnum.getByAppId(miniappConfigEntity.getAppid());
        if (WxAppInfoEnum.MankeepPro == wxAppInfo) {
            String hostType = "&hostType=" + enterpriseEnvironment;
            page += hostType;
        }
        miniprogramNotice.setPage(page);
        miniprogramNotice.setAppid(miniappConfigEntity.getAppid());
        SpreadQywxMiniappMessageResult result = qywxManager.sendSpreadQywxMiniappMessage(accessToken, arg);
        return result;
    }

    //按照utf-8截取字符串
    private String cutStringByU8(String str, int len) throws IOException {
        byte[] buf = str.getBytes("utf-8");
        int count = 0;
        for (int x = len - 1; x >= 0; x--) {
            if (buf[x] < 0) {
                count++;
            } else {
                break;
            }
        }
        if (count % 3 == 0) {
            return new String(buf, 0, len, "utf-8");
        } else if (count % 3 == 1) {
            return new String(buf, 0, len - 1, "utf-8");
        } else {
            return new String(buf, 0, len - 2, "utf-8");
        }
    }

    public void cachedGroupDetail() {
        int idx = 1;
        int size = 5;
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(1, 1, true);
        int total = Math.min(page.getTotalNum(), 100);
        for (; (idx - 1) * size < total; idx++) {
            int finalIdx = idx;
            tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
                List<QywxGroupSendTaskEntity> qywxGroupSendTaskEntityList = sendTaskDAO.getBy(new com.github.mybatis.pagination.Page(finalIdx, size), dbRouteEa);
                if (qywxGroupSendTaskEntityList != null) {
                    ThreadPoolUtils.execute(() -> {
                        for (QywxGroupSendTaskEntity taskEntity : qywxGroupSendTaskEntityList) {
                            List<QywxGroupSendGroupResultEntity> sendGroupResultEntities = qywxGroupSendGroupResultDAO.queryGroupResult(taskEntity.getEa(), Collections.singletonList(taskEntity.getId()));
                            if (org.apache.commons.collections.CollectionUtils.isEmpty(sendGroupResultEntities)) {
                                continue;
                            }
                            for (QywxGroupSendGroupResultEntity entity : sendGroupResultEntities) {
                                if (entity.getSuccessCount() != 0) {
                                    List<String> sendGroupIds = com.facishare.marketing.common.util.GsonUtil
                                            .fromJson(entity.getSendGroupIds(), new TypeToken<List<String>>() {
                                            }.getType());
                                    for (String groupId : sendGroupIds) {
                                        this.queryCachedGroupDetail(taskEntity.getEa(), groupId);
                                    }
                                }
                            }
                        }
                    }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
                }
            });
        }
    }

    public PageResult<MomentCustomer> listMomentCustomer(String ea, MomentCustomerArg arg) {
        PageResult<MomentCustomer> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        List<MomentCustomer> momentCustomers = Lists.newArrayList();
        pageResult.setResult(momentCustomers);

        QywxMomentTaskEntity taskEntity = qywxMomentTaskDAO.getByMarketingActivityId(ea, arg.getMarketingActivityId());
        List<String> externalUserIds = null;
        List<ObjectData> qywxCustemers = Lists.newArrayList();
        List<QywxMomentSendResultEntity> resultList = Lists.newArrayList();
        //Map<String, QywxMomentSendResultEntity> externalAndUserIdMap = Maps.newHashMap();
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea) || fsBindManager.isQywxContactFsSetting(ea);
        if (taskEntity != null) {
            List<String> qywxUserIds = null;
            if (CollectionUtils.isNotEmpty(arg.getDepartmentIdList())) {
                if (isOpen) {
                    qywxUserIds = qywxManager.handleQywxEmployeeUserIdWithOpenDataPermission(ea, taskEntity.getCreateUserId(), null, arg.getDepartmentIdList());
                } else {
                    List<Integer> qywxDepartmentIdList = arg.getDepartmentIdList().stream().map(e -> e - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                    qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(ea, qywxDepartmentIdList);
                }
                if (CollectionUtils.isEmpty(qywxUserIds)) {
                    return pageResult;
                }
            }
            if ("USER_ASSOCIATED".equals(arg.getQueryType())) {
                Preconditions.checkArgument(StringUtils.isNotBlank(arg.getUserId()), I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2958));
                PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
                paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                PaasQueryArg query = new PaasQueryArg(0, 1);
                query.addFilter("qywx_user_id", OperatorConstants.IN, Collections.singletonList(arg.getUserId()));
                query.addFilter("friend_status", OperatorConstants.IN, Collections.singletonList("0"));
                query.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, OperatorConstants.IN, Lists.newArrayList(AppScopeEnum.MARKETING.getValue()));
                paasQueryFilterArg.setQuery(query);
//                InnerPage<ObjectData> crmObjectResult = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg);
                List<String> qywxCustemerIds = new ArrayList<>();
//                if (crmObjectResult != null && CollectionUtils.isNotEmpty(crmObjectResult.getDataList())) {
//                    for (Map<String, Object> objectMap : crmObjectResult.getDataList()) {
//                        Object external_user_id = objectMap.get("external_user_id");
//                        if (external_user_id != null) {
//                            qywxCustemerIds.add(external_user_id.toString());
//                        }
//                    }
//                }
                crmV2Manager.listCrmObjectScanByIdAndHandle(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, 1000, objectData -> {
                    Object external_user_id = objectData.get("external_user_id");
                    if (external_user_id != null) {
                        qywxCustemerIds.add(external_user_id.toString());
                    }
                });
                if (!qywxCustemerIds.isEmpty()) {
                    PaasQueryFilterArg paasQueryFilterArg1 = new PaasQueryFilterArg();
                    paasQueryFilterArg1.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                    PaasQueryArg query1 = new PaasQueryArg(0, 1);
                    query1.addFilter("_id", OperatorConstants.IN, qywxCustemerIds);
                    paasQueryFilterArg1.setQuery(query1);
//                    qywxCustemers = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg1);
                    crmV2Manager.listCrmObjectScanByIdAndHandle(ea, SuperUserConstants.USER_ID, paasQueryFilterArg1, 1000, qywxCustemers::add);
                }
                if (!qywxCustemers.isEmpty()) {
                    pageResult.setTotalCount(qywxCustemers.size());
                    int offset = (arg.getPageNum() - 1) * arg.getPageSize();
                    int limit = arg.getPageSize();
                    qywxCustemers.addAll(qywxCustemers.stream().skip(offset).limit(limit).collect(Collectors.toList()));
                    externalUserIds = qywxCustemers.stream()
                            .map(e -> e.get("external_user_id"))
                            .filter(Objects::nonNull)
                            .map(String::valueOf)
                            .collect(Collectors.toList());
                }
            } else {
                com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), true);
                switch (arg.getQueryType()) {
                    case "ALL_UNSEND":
                        resultList = qywxMomentSendResultDao.getByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), null, 0, qywxUserIds, page);
                        pageResult.setTotalCount(qywxMomentSendResultDao.countByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), arg.getUserId(), 0, qywxUserIds));
                        break;
                    case "ALL_SEND":
                        resultList = qywxMomentSendResultDao.getByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), null, 1, qywxUserIds, page);
                        pageResult.setTotalCount(qywxMomentSendResultDao.countByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), arg.getUserId(), 1, qywxUserIds));
                        break;
                    case "USER_SEND":
                        Preconditions.checkArgument(StringUtils.isNotEmpty(arg.getUserId()), I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2958));
                        resultList = qywxMomentSendResultDao.getByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), arg.getUserId(), 1, qywxUserIds, page);
                        pageResult.setTotalCount(qywxMomentSendResultDao.countByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), arg.getUserId(), 1, qywxUserIds));
                        break;
                    case "ALL":
                        resultList = qywxMomentSendResultDao.getByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), null, null, qywxUserIds, page);
                        pageResult.setTotalCount(qywxMomentSendResultDao.countByMomentIdAndUserIdAndStatus(taskEntity.getEa(), taskEntity.getMomentId(), arg.getUserId(), null, qywxUserIds));
                        break;
                }
                //resultList.forEach(e -> externalAndUserIdMap.put(e.getExternalUserId() + "," + e.getUserId(), e));
                externalUserIds = resultList.stream().map(QywxMomentSendResultEntity::getExternalUserId).distinct().collect(Collectors.toList());
                if (!externalUserIds.isEmpty()) {
                    PaasQueryFilterArg paasQueryFilterArg1 = new PaasQueryFilterArg();
                    paasQueryFilterArg1.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                    PaasQueryArg query1 = new PaasQueryArg(0, 1);
                    query1.addFilter("external_user_id", OperatorConstants.IN, externalUserIds);
                    paasQueryFilterArg1.setQuery(query1);
//                    qywxCustemers = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg1);
                    crmV2Manager.listCrmObjectScanByIdAndHandle(ea, SuperUserConstants.USER_ID, paasQueryFilterArg1, 1000, qywxCustemers::add);
                }

            }
            if (externalUserIds != null && !externalUserIds.isEmpty()) {
                Map<Integer, Department> departmentMap = getAllDepartment(ea);
                //Set<String> userIdSet = externalAndUserIdMap.values().stream().map(QywxMomentSendResultEntity::getUserId).collect(Collectors.toSet());
                Set<String> userIdSet = resultList.stream().map(QywxMomentSendResultEntity::getUserId).collect(Collectors.toSet());
                Map<String, QyWxAddressBookEntity> userIdToQyWxAddressBookMap = Maps.newHashMap();
                Map<String, Integer> fsUserIdByQyWxInfo = Maps.newHashMap();
                Map<Integer, FSEmployeeMsg> employeeInfoByUserIds = Maps.newHashMap();
                if (isOpen) {
                    fsUserIdByQyWxInfo = qywxUserManager.getFsUserIdByQyWxInfo(ea, Lists.newArrayList(userIdSet), true, false);
                    List<Integer> fsUserIds = Lists.newArrayList(fsUserIdByQyWxInfo.values());
                    employeeInfoByUserIds = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIds, true);
                } else {
                    userIdToQyWxAddressBookMap = getQyWxAddressBookEntity(Lists.newArrayList(userIdSet), ea);
                }
                Map<String, Long> externalUserIdAndcountMap = new HashMap<>();
                List<Map<String, Object>> externalUserIdAndcountList = qywxMomentSendResultDao.getExternalUserIdAndcount(ea, new ArrayList<>(externalUserIds));
                if (externalUserIdAndcountList != null) {
                    externalUserIdAndcountMap = externalUserIdAndcountList.stream().collect(Collectors.toMap(o -> String.valueOf(o.get("externaluserid")), o -> (Long) o.get("count"), (v1, v2) -> v1));
                }
                Map<String, Map<String, Object>> qywxCustemerMap = null;
                if (CollectionUtils.isNotEmpty(qywxCustemers)) {
                    qywxCustemerMap = qywxCustemers.stream().collect(Collectors.toMap(o -> String.valueOf(o.get("external_user_id")), Function.identity(), (v1, v2) -> v1));
                }
                if ("USER_ASSOCIATED".equals(arg.getQueryType())) {
                    for (String externalUserId : externalUserIds) {
                        MomentCustomer momentCustomer = new MomentCustomer();
                        momentCustomer.setExternalUserIds(externalUserId);
                        if (externalUserIdAndcountMap.get(externalUserId) != null) {
                            momentCustomer.setSpreadCount(externalUserIdAndcountMap.get(externalUserId).intValue());
                        } else {
                            momentCustomer.setSpreadCount(0);
                        }
                        momentCustomers.add(momentCustomer);
                    }
                } else {
                    for (QywxMomentSendResultEntity resultEntity : resultList) {
                        String externalUserId = resultEntity.getExternalUserId();
                        MomentCustomer momentCustomer = new MomentCustomer();
                        momentCustomer.setExternalUserIds(externalUserId);
                        if (externalUserIdAndcountMap.get(externalUserId) != null) {
                            momentCustomer.setSpreadCount(externalUserIdAndcountMap.get(externalUserId).intValue());
                        } else {
                            momentCustomer.setSpreadCount(0);
                        }
                        //QywxMomentSendResultEntity entity = externalAndUserIdMap.get(externalUserId + "," + resultEntity.getUserId());
                        if (resultEntity.getPublishStatus() != null && resultEntity.getPublishStatus() == 1) {
                            momentCustomer.setSendTime(resultEntity.getCreateTime().getTime());
                        }
                        momentCustomer.setPublishStatus(resultEntity.getPublishStatus());
                        if (isOpen) {
                            FSEmployeeMsg fsEmployeeMsg = employeeInfoByUserIds.get(fsUserIdByQyWxInfo.get(resultEntity.getUserId()));
                            if (fsEmployeeMsg != null) {
                                momentCustomer.setDepartment(fsEmployeeMsg.getDepartment());
                                momentCustomer.setEmployeeName(fsEmployeeMsg.getName());
                            }
                        } else {
                            QyWxAddressBookEntity qyWxAddressBookEntity = userIdToQyWxAddressBookMap.get(resultEntity.getUserId());
                            if (qyWxAddressBookEntity != null) {
                                momentCustomer.setEmployeeName(qyWxAddressBookEntity.getName());
                                momentCustomer.setDepartment(qyWxAddressBookEntity.getDepartmentName());
                            }
                        }
                        momentCustomers.add(momentCustomer);
                    }
                }
                for (MomentCustomer momentCustomer : momentCustomers) {
                    if (qywxCustemerMap != null) {
                        Map<String, Object> e = qywxCustemerMap.get(momentCustomer.getExternalUserIds());
                        if (e != null) {
                            momentCustomer.setNickName(e.get("name") == null ? "" : e.get("name").toString());
                            momentCustomer.setCompanyName(e.get("enterprise_name") == null ? "" : e.get("enterprise_name").toString());
                            momentCustomer.setUserName(e.get("remark_name") == null ? "" : e.get("remark_name").toString());
                            momentCustomer.setId(e.get("_id").toString());
                            List<Map<String, Object>> avatarMap = com.facishare.marketing.common.util.GsonUtil
                                    .fromJson(com.facishare.marketing.common.util.GsonUtil.getGson().toJson(e.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName())), new TypeToken<List<Map<String, String>>>() {
                                    }.getType());
                            if (avatarMap != null) {
                                momentCustomer.setAvatar(getAvatarFormCrmData(avatarMap, ea));
                            }
                        }
                    }
                }
            }
        }
        return pageResult;
    }

    private Map<Integer, Department> getAllDepartment(String ea) {
        Map<Integer, Department> departmentMap = Maps.newHashMap();
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = null;
        if (agentConfig != null) {
            accessToken = qywxManager.getAccessToken(ea);
            DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
            if (departmentListResult != null && CollectionUtils.isNotEmpty(departmentListResult.getDepartmentList())) {
                Map<Integer, Department> tempMap = departmentListResult.getDepartmentList().stream().collect(Collectors.toMap(Department::getId, data -> data, (v1, v2) -> v1));
                departmentMap.putAll(tempMap);
            }
        }
        return departmentMap;
    }

    private Map<String, QyWxAddressBookEntity> getQyWxAddressBookEntity(List<String> userIdList, String ea) {
        Map<String, QyWxAddressBookEntity> res = new HashMap<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return res;
        }
        List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryEaAndUserId(ea, userIdList);
        if (null == qyWxAddressBookEntities) {
            return res;
        }
        qyWxAddressBookEntities.forEach(address -> res.put(address.getUserId(), address));
        return res;
    }

    private String getAvatarFormCrmData(List<Map<String, Object>> avatarMap, String ea) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(avatarMap)) {
            return fileV2Manager.getUrlByPath(avatarMap.get(0).get("path") + "." + avatarMap.get(0).get("ext"), ea, false);
        }
        return null;
    }

    public boolean openQywxH5Notice(String ea) {
        boolean flag = false;
        MarketingNoticeSettingEntity noticeSettingEntity = marketingNoticeSettingDAO.queryByEa(ea);
        if (noticeSettingEntity != null) {
            List<String> stringList = JSON.parseArray(noticeSettingEntity.getNoticeType(), String.class);
            if (CollectionUtils.isNotEmpty(stringList) && stringList.contains("qywx_h5")) {
                flag = true;
            }
        }
        return flag;
    }

    public Map<String,List<String>> checkChatGroupIdListByFilters(String ea,List<Map<String, Object>> filters) {
        Map<String,List<String>> chatGroupIdListMap = Maps.newHashMap();
        List<Filter> searchFilters = new ArrayList<>();
        if (CollectionUtils.isEmpty(filters)) {
            return null;
        }
        filters.forEach(val -> {
            if (val.get("fieldValues") instanceof List) {
                List<Object> datas = ((List<Object>) val.get("fieldValues"));
                List<String> values = Lists.newArrayList();
                datas.forEach(data ->{
                    String value = data.toString();
                    if (value.contains("E")){
                        try {
                            BigDecimal bigDecimal = new BigDecimal(value);
                            value = bigDecimal.toPlainString();
                        } catch (Exception e) {
                        }
                    }
                    values.add(value);
                });
                Filter filter = new Filter();
                if (val.get("valueType") != null) {
                    filter.setValueType((Integer) val.get("valueType"));
                }
                filter.setFieldName(String.valueOf(val.get("fieldName")));
                filter.setFieldValues(Lists.newArrayList(values));
                filter.setOperator(String.valueOf(val.get("operator")));
                if (val.get("isCascade") != null){
                    filter.setIsCascade((Boolean)val.get("isCascade"));
                }
                if ("tag".equals(filter.getFieldName())) {
                    filter.setValueType(11);
                    filter.setOperator("IN".equals(filter.getOperator()) ? "LIKE" : filter.getOperator());
                }
                searchFilters.add(filter);
            } else {
                Filter filter = new Filter();
                if (val.get("valueType") != null) {
                    filter.setValueType((Integer) val.get("valueType"));
                }
                String fieldValues = String.valueOf(val.get("fieldValues"));
                filter.setFieldName(String.valueOf(val.get("fieldName")));
                if (fieldValues.contains("E")){
                    try {
                        fieldValues = new BigDecimal(fieldValues).toPlainString();
                    } catch (Exception e) {
                    }
                }
                filter.setFieldValues(Lists.newArrayList(fieldValues));
                filter.setOperator(String.valueOf(val.get("operator")));
                if (val.get("isCascade") != null){
                    filter.setIsCascade((Boolean)val.get("isCascade"));
                }
                if ("tag".equals(filter.getFieldName())) {
                    filter.setValueType(11);
                    filter.setOperator("IN".equals(filter.getOperator()) ? "LIKE" : filter.getOperator());
                }
                searchFilters.add(filter);
            }
        });
        List<PaasQueryArg.Condition> conditionFilters = new ArrayList<>();
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        if (CollectionUtils.isNotEmpty(searchFilters)) {
            searchFilters.forEach(e-> {
                PaasQueryArg.Condition e1 = new PaasQueryArg.Condition(e.getFieldName(), e.getFieldValues(), e.getOperator());
                e1.setValueType(e.getValueType());
                e1.setIsCascade(e.getIsCascade());
                conditionFilters.add(e1);
            });
        }
        query.setFilters(conditionFilters);
        query.addFilter("status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("0"));
        query.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("default__c"));
        paasQueryFilterArg.setQuery(query);
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        paasQueryFilterArg.setSelectFields(Arrays.asList("_id", "chat_id","leader_id"));
        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg);
        if (totalRelationCount <= 0) {
            return null;
        }
        List<ObjectData> objectDataList = Lists.newArrayList();
        int currentRelationCount = 0;
        String lastRelationId = null;
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, lastRelationId, 500);
            if (objectDataInnerPage == null || org.apache.commons.collections4.CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }
        if (CollectionUtils.isNotEmpty(objectDataList)) {
            chatGroupIdListMap = objectDataList.stream().collect(Collectors.groupingBy(e -> e.getString("leader_id"), Collectors.mapping(k -> k.getString("chat_id"), Collectors.toList())));
        }
        return chatGroupIdListMap;
    }

    public Map<String,List<String>> getChatGroupIdListByFilters(String ea,List<Map<String, Object>> filters,Integer fsUserId) {
        Map<String,List<String>> chatGroupIdListMap = Maps.newConcurrentMap();
        List<Filter> searchFilters = new ArrayList<>();
        if (CollectionUtils.isEmpty(filters)) {
            return null;
        }
        filters.forEach(val -> {
            if (val.get("fieldValues") instanceof List) {
                List<Object> datas = ((List<Object>) val.get("fieldValues"));
                List<String> values = Lists.newArrayList();
                datas.forEach(data ->{
                    String value = data.toString();
                    if (value.contains("E")){
                        try {
                            BigDecimal bigDecimal = new BigDecimal(value);
                            value = bigDecimal.toPlainString();
                        } catch (Exception e) {
                        }
                    }
                    values.add(value);
                });
                Filter filter = new Filter();
                if (val.get("valueType") instanceof BigDecimal) {
                    filter.setValueType(((BigDecimal) val.get("valueType")).intValue());
                }
                filter.setFieldName(String.valueOf(val.get("fieldName")));
                filter.setFieldValues(Lists.newArrayList(values));
                filter.setOperator(String.valueOf(val.get("operator")));
                if (val.get("isCascade") != null){
                    filter.setIsCascade((Boolean)val.get("isCascade"));
                }
                if ("tag".equals(filter.getFieldName())) {
                    filter.setValueType(11);
                    filter.setOperator("IN".equals(filter.getOperator()) ? "LIKE" : filter.getOperator());
                }
                searchFilters.add(filter);
            } else {
                Filter filter = new Filter();
                if (val.get("valueType") instanceof BigDecimal) {
                    filter.setValueType(((BigDecimal) val.get("valueType")).intValue());
                }
                String fieldValues = String.valueOf(val.get("fieldValues"));
                filter.setFieldName(String.valueOf(val.get("fieldName")));
                if (fieldValues.contains("E")){
                    try {
                        fieldValues = new BigDecimal(fieldValues).toPlainString();
                    } catch (Exception e) {
                    }
                }
                filter.setFieldValues(Lists.newArrayList(fieldValues));
                filter.setOperator(String.valueOf(val.get("operator")));
                if (val.get("isCascade") != null){
                    filter.setIsCascade((Boolean)val.get("isCascade"));
                }
                if ("tag".equals(filter.getFieldName())) {
                    filter.setValueType(11);
                    filter.setOperator("IN".equals(filter.getOperator()) ? "LIKE" : filter.getOperator());
                }
                searchFilters.add(filter);
            }
        });
        List<PaasQueryArg.Condition> conditionFilters = new ArrayList<>();
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        if (CollectionUtils.isNotEmpty(searchFilters)) {
            searchFilters.forEach(e-> {
                PaasQueryArg.Condition e1 = new PaasQueryArg.Condition(e.getFieldName(), e.getFieldValues(), e.getOperator());
                e1.setValueType(e.getValueType());
                e1.setIsCascade(e.getIsCascade());
                conditionFilters.add(e1);
            });
        }
        query.setFilters(conditionFilters);
        query.addFilter("status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("0"));
        query.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("default__c"));
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);
        if(isOpen && fsUserId!=null){
            List<String> staffIds = Lists.newArrayList();
            String accessToken = qywxManager.getAccessToken(ea);
            List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, false, false);
            if(CollectionUtils.isEmpty(staffInfoList)){
                log.warn("getAllChatGroup staffInfoList is null, ea:{}", ea);
                return null;
            }
            List<Integer> qywxAccessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(ea, fsUserId,true);
            if(CollectionUtils.isEmpty(qywxAccessibleDepartmentIds)){
                return null;
            }
            if (!qywxAccessibleDepartmentIds.contains(defaultAllDepartment)){
                qywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds.stream().map(o -> o-QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                List<Integer> finalQywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds;
                staffIds = staffInfoList.stream().filter(staffInfo -> CollectionUtils.containsAny(staffInfo.getDepartment(), finalQywxAccessibleDepartmentIds)).map(DepartmentStaffResult.StaffInfo::getUserId).collect(Collectors.toList());
                query.addFilter("leader_id", "IN", staffIds.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        }
        paasQueryFilterArg.setQuery(query);
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        paasQueryFilterArg.setSelectFields(Arrays.asList("_id", "chat_id","leader_id"));
        List<ObjectData> objectDataList = Lists.newArrayList();
        crmV2Manager.listCrmObjectScanByIdAndHandle(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, 1000, objectDataList::add);

        if (CollectionUtils.isNotEmpty(objectDataList)) {
            chatGroupIdListMap = objectDataList.stream().collect(Collectors.groupingBy(e -> e.getString("leader_id"), Collectors.mapping(k -> k.getString("chat_id"), Collectors.toList())));
        }
        return chatGroupIdListMap;
    }
}