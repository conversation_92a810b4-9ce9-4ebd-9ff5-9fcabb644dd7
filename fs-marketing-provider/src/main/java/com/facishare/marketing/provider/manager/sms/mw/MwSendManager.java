/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.sms.mw;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.CloudSmsReportArg;
import com.facishare.marketing.api.arg.QueryMiniAppForwardUrlArg;
import com.facishare.marketing.api.arg.sms.GroupSenderArg;
import com.facishare.marketing.api.arg.sms.SmsVarArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.MobileMarketingUserResult;
import com.facishare.marketing.api.result.ReportResult;
import com.facishare.marketing.api.result.sms.OneSMSDetailResult;
import com.facishare.marketing.api.result.sms.ParamsOfExcelResult;
import com.facishare.marketing.api.result.sms.ShortUrlResult;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.vo.GroupSmsStatusVO;
import com.facishare.marketing.common.contstant.*;
import com.facishare.marketing.common.contstant.sms.SmsContants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import com.facishare.marketing.common.enums.sms.ApplySignatureStatusEnum;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.ExcelCheckResultTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.*;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.SmsContentParam;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.sms.QuotaDAO;
import com.facishare.marketing.provider.dao.sms.SmsChannelConfigDAO;
import com.facishare.marketing.provider.dao.sms.SmsTrialDao;
import com.facishare.marketing.provider.dao.sms.mw.MwAccountDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendMarketingEventRelationDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSignatureDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.ExternalConfig;
import com.facishare.marketing.provider.entity.data.MarketingActivityGroupSenderData;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.sms.SmsChannelConfigEntity;
import com.facishare.marketing.provider.entity.sms.SmsTrialEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwAccountEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSendDetailEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSendEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSendMarketingEventRelationEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSignatureEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.innerArg.MwSendDetailUpdateMqArg;
import com.facishare.marketing.provider.innerArg.crm.SmsRecordObjCreateArg;
import com.facishare.marketing.provider.innerArg.mw.CreateSendTaskArg;
import com.facishare.marketing.provider.innerArg.mw.MultiMtArg;
import com.facishare.marketing.provider.innerResult.BatchShortUrlResult;
import com.facishare.marketing.provider.innerResult.mw.ReportResults;
import com.facishare.marketing.provider.innerResult.mw.SendRequestResult;
import com.facishare.marketing.provider.innerResult.sms.mw.TemplateQueryResult.TemplateQueryDetail;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.SmsSendRecordObjManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.sms.QuotaManager;
import com.facishare.marketing.provider.manager.sms.SmsTemplateManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.mq.sender.GroupSmsStatusSender;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.BatchCreateShortUrlsArg;
import com.facishare.marketing.provider.remote.smsplatform.SmsPlatformRestManager;
import com.facishare.marketing.provider.remote.smsplatform.arg.SpRestSmsSendArg;
import com.facishare.marketing.provider.remote.smsplatform.result.SpRestGetSmsTemplateResult;
import com.facishare.marketing.provider.util.ReadExcelUtil;
import com.facishare.marketing.provider.util.RexUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.smsplatform.common.enums.SmsTemplateStatusEnum;
import com.facishare.smsplatform.common.model.SmsPhoneData;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.io.Serializable;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * Created by ranluch on 2019/2/18.
 */
@Service
@Slf4j
public class MwSendManager {
    // 短信内容创建失败
    public static int ERROR_999999 = -999999;
    @ReloadableProperty("sms.trial.signatureId")
    private String trialSignatureId;

    @ReloadableProperty("use_fs_sms_signature_eas")
    private String useFsSmsSignatureEas;

    @Value("${host}")
    private String host;
    @Value("${short.link.domain}")
    private String shortLinkDomain;

    @Autowired
    private MwSmsTemplateDao templateDao;

    @Autowired
    private MwSmsSignatureDao signatureDao;

    @Autowired
    private MwSmsSendDao smsSendDao;

    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private QuotaManager quotaManager;

    @Autowired
    private MwSmsManager mwSmsManager;

    @Autowired
    private SendService sendService;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private QuotaDAO quotaDAO;
    @Autowired
    private LiveManager liveManager;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private SmsChannelConfigDAO smsChannelConfigDAO;
    @Autowired
    private MwAccountDao mwAccountDao;
    @Autowired
    private SmsParamManager smsParamManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private GroupSmsStatusSender groupSmsStatusSender;
    @Autowired
    private EnterpriseSpreadRecordManager enterpriseSpreadRecordManager;
    @Autowired
    private MwTemplateManager mwTemplateManager;
    @Autowired
    private SmsPlatformRestManager smsPlatformRestManager;
    @Autowired
    private SmsTemplateManager smsTemplateManager;
    @Autowired
    private MiniProgramAuthManager miniProgramAuthManager;
    @Autowired
    private MwSmsSendMarketingEventRelationDao mwSmsSendMarketingEventRelationDao;
    @Autowired
    private ShortUrlManager shortUrlManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private SmsTrialDao smsTrialDao;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private CustomizeMiniAuthorizeManager customizeMiniAuthorizeManager;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private HttpManager httpManager;
    @ReloadableProperty("center.host")
    private String centerHost;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private SmsSendRecordObjManager smsSendRecordObjManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private MetadataActionService metadataActionService;

    @Autowired
    private EIEAConverter eIEAConverter;

    @Autowired
    private DelayQueueSender delayQueueSender;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    private volatile String hostName;

    public static Integer MAX_INSERT_NUM = 2000;

    /**
     * 创建发送任务
     * @param arg 任务参数
     * @param isSyncSend 是否为同步发送短信： true为同步发送，直接创建detail表；false为异步发送，线程池创建detail表
     * @param isSendType true：发送类型   false：保存草稿类型
     * @return
     */
    public Result<MwSmsSendEntity> createSendTask(CreateSendTaskArg arg, boolean isSyncSend, boolean isSendType) {
        MwSmsSignatureEntity signatureEntity;
        if (StringUtils.isNotEmpty(arg.getSignatureId())) {
            signatureEntity = signatureDao.getSignatureById(arg.getSignatureId());
        } else {
            signatureEntity = getSmsSignature(arg.getEa(), arg.getChannelType());
        }
        if (signatureEntity == null) {
            log.info("MwSendManager createSendTask signatureEntity is null, ea = {}", arg.getEa());
            return Result.newError(SHErrorCode.SIGNATURE_NOT_EXIST);
        }

        MwSmsTemplateEntity templateEntity;
        if (StringUtils.isNotEmpty(arg.getTemplateId())) {
            templateEntity = templateDao.getTemplateById(arg.getTemplateId());
        } else {
            templateEntity = arg.getTemplateEntity();
        }

        MwSmsSendEntity sendEntity = new MwSmsSendEntity();
        sendEntity.setId(UUIDUtil.getUUID());
        sendEntity.setEa(arg.getEa());
        sendEntity.setSignatureId(signatureEntity.getId());
        sendEntity.setTemplateId(templateEntity.getId());
        sendEntity.setStatus(MwSendStatusEnum.NEW.getStatus());
        sendEntity.setType(arg.getTaskType());
        sendEntity.setChannelType(arg.getChannelType());
        sendEntity.setTotalFee(0);
        sendEntity.setActualSenderCount(0);
        sendEntity.setToSenderCount(0);
        sendEntity.setBusinessType(arg.getBusinessType());
        if (StringUtils.isNotEmpty(arg.getReceiver())) {
            String receiver = arg.getReceiver();
            if (receiver.length() > 100) {
                receiver = receiver.substring(0, 100);
            }
            sendEntity.setReceiver(receiver);
        }
        sendEntity.setSendNode(arg.getSendNode());
        sendEntity.setNodeType(arg.getNodeType());
        sendEntity.setObjectId(arg.getObjectId());
        if (MwSendTaskTypeEnum.SCHEDULE_SEND.getType().equals(arg.getTaskType())) {
            if (arg.getScheduleTime() == null) {
                log.info("MwSendManager createSendTask scheduleTime is null, arg = {}", arg);
                return Result.newError(SHErrorCode.SMS_SCHEDULE_TIME_ERROR);
            }
            sendEntity.setScheduleTime(arg.getScheduleTime());
        }
        sendEntity.setHostName(getHostName());
        sendEntity.setCreatorUserId(arg.getCreatorUserId());
        sendEntity.setCreatorName(arg.getCreatorName());
        sendEntity.setUserGroups(arg.getUserGroupIds());
        /** 同步执行 */
        if (isSyncSend) {
            Result<List<MwSendDetailEntity>> createResult = createSendDetail(sendEntity, signatureEntity, templateEntity, arg, false);
            if (createResult == null) {
                log.info("MwSendManager createSendTask createSendDetail is null, arg = {}", arg);
                return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
            }
            if (!createResult.isSuccess()) {
                log.info("MwSendManager createSendTask createSendDetail is not success, arg = {}", arg);
                return new Result(createResult.getErrCode(), createResult.getErrMsg());
            }

            List<MwSendDetailEntity> detailEntityList = createResult.getData();

            if (CollectionUtils.isEmpty(detailEntityList)) {
                log.info("MwSendManager createSendTask fail, detailEntityList is empty, arg = {}", arg);
                if (StringUtils.isNotEmpty(arg.getTaPath())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                } if (CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {
                    return Result.newError(SHErrorCode.SMS_USER_GROUP_ERROR);
                }else {
                    return Result.newError(SHErrorCode.SMS_CHOOSE_PHONES_ERROR);
                }
            }
            boolean toDbResult = saveTaskAndDetailToDb(sendEntity, detailEntityList, arg.getTemplateEntity()); //这里需要用arg中的模板对象
            if (!toDbResult) {
                log.info("MwSendManager createSendTask saveTaskAndDetailToDb fail, arg = {}", arg);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } else {
            // excel导入的方式，把校验提前
            if (arg.getGroupType().equals(SmsGroupTypeEnum.EXCEL.getType()) && StringUtils.isNotEmpty(arg.getTaPath())) {
                Result validate4ExcelResult = validate4Excel(arg, templateEntity);
                log.info("MwSendManager createSendTask validate4ExcelResult, result = {}", validate4ExcelResult);
                if (!validate4ExcelResult.isSuccess()) {
                    return validate4ExcelResult;
                }
            }

            sendEntity.setStatus(MwSendStatusEnum.CALCULATE.getStatus());
            boolean toDbResult = saveSendEntityAndTemplateToDb(sendEntity, arg.getTemplateEntity());
            if (!toDbResult) {
                log.info("MwSendManager createSendTask saveSendEntityAndTemplateToDb fail, arg = {}", arg);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            ThreadPoolUtils.executeWithNewThread("sendSmsTask-" + sendEntity.getId(), () -> {
                Result<List<MwSendDetailEntity>> createResult = createSendDetail(sendEntity, signatureEntity, templateEntity, arg, false);
                if (createResult == null) {
                    log.warn("MwSendManager createSendTask createSendDetail is null, arg = {}", arg);
                    sendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                    smsSendDao.updateSendEntityStatus(sendEntity);
                    return ;
                }
                if (!createResult.isSuccess()) {
                    log.warn("MwSendManager createSendTask createSendDetail is not success, arg = {} errorCode:{} errorMsg:{}", arg, createResult.getErrCode(), createResult.getErrMsg());
                    sendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                    smsSendDao.updateSendEntityStatus(sendEntity);
                    return ;
                }
                List<MwSendDetailEntity> detailEntityList = createResult.getData();
                if (CollectionUtils.isEmpty(detailEntityList)) {
                    log.warn("MwSendManager createSendTask fail, detailEntityList is empty, arg = {}", arg);
                    sendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                    smsSendDao.updateSendEntityStatus(sendEntity);
                    return ;
                }
                // detail计算完成,保存至db
                sendEntity.setStatus(MwSendStatusEnum.NEW.getStatus());
                saveDetailAndUpdateSendEntity(detailEntityList, sendEntity);
                // 如果是发送类型，则需要扣费并更新status
                if (isSendType) {
                    reduceQuotaAndSend(sendEntity, arg.isVerifyCodeSms());
                }
            });
        }
        return Result.newSuccess(sendEntity);
    }

    public Result<MwSmsSendEntity> createSendTask4SmsPlatform(CreateSendTaskArg arg, boolean isSyncSend, boolean isSendType) {
        String ea = arg.getEa();
        String templateId = arg.getTemplateId();
        // 构造虚拟签名实体
        MwSmsSignatureEntity signatureEntity = new MwSmsSignatureEntity();
        signatureEntity.setId("-");
        signatureEntity.setSvrName("");

        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(ea, templateId);

        MwSmsSendEntity sendEntity = new MwSmsSendEntity();
        sendEntity.setId(UUIDUtil.getUUID());
        sendEntity.setEa(arg.getEa());
        sendEntity.setSignatureId(signatureEntity.getId());
        sendEntity.setTemplateId(templateEntity.getId());
        sendEntity.setStatus(MwSendStatusEnum.NEW.getStatus());
        sendEntity.setType(arg.getTaskType());
        sendEntity.setChannelType(arg.getChannelType());
        sendEntity.setTotalFee(0);
        sendEntity.setActualSenderCount(0);
        sendEntity.setToSenderCount(0);
        sendEntity.setBusinessType(arg.getBusinessType());
        if (StringUtils.isNotEmpty(arg.getReceiver())) {
            String receiver = arg.getReceiver();
            if (receiver.length() > 100) {
                receiver = receiver.substring(0, 100);
            }
            sendEntity.setReceiver(receiver);
        }
        sendEntity.setSendNode(arg.getSendNode());
        sendEntity.setNodeType(arg.getNodeType());
        sendEntity.setObjectId(arg.getObjectId());
        if (MwSendTaskTypeEnum.SCHEDULE_SEND.getType().equals(arg.getTaskType())) {
            if (arg.getScheduleTime() == null) {
                log.info("MwSendManager -> createSendTask4SmsPlatform scheduleTime is null, arg = {}", arg);
                return Result.newError(SHErrorCode.SMS_SCHEDULE_TIME_ERROR);
            }
            sendEntity.setScheduleTime(arg.getScheduleTime());
        }
        sendEntity.setHostName(getHostName());
        sendEntity.setCreatorUserId(arg.getCreatorUserId());
        sendEntity.setCreatorName(arg.getCreatorName());
        sendEntity.setUserGroups(arg.getUserGroupIds());
        /** 同步执行 */
        if (isSyncSend) {
            if (sendEntity.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType())) {
                sendEntity.setStatus(MwSendStatusEnum.SCHEDULE_SENDING.getStatus());
            } else {
                sendEntity.setStatus(MwSendStatusEnum.READY.getStatus());
            }
            Result<List<MwSendDetailEntity>> createResult = createSendDetail(sendEntity, signatureEntity, templateEntity, arg, false);
            if (createResult == null) {
                log.info("MwSendManager -> createSendTask4SmsPlatform createSendDetail is null, arg = {}", arg);
                return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
            }
            if (!createResult.isSuccess()) {
                log.info("MwSendManager -> createSendTask4SmsPlatform createSendDetail is not success, arg = {}, result = {}", arg, createResult);
                return new Result(createResult.getErrCode(), createResult.getErrMsg());
            }

            List<MwSendDetailEntity> detailEntityList = createResult.getData();

            if (CollectionUtils.isEmpty(detailEntityList)) {
                log.info("MwSendManager -> createSendTask4SmsPlatform fail, detailEntityList is empty, arg = {}", arg);
                if (StringUtils.isNotEmpty(arg.getTaPath())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                } if (CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {
                    return Result.newError(SHErrorCode.SMS_USER_GROUP_ERROR);
                }else {
                    return Result.newError(SHErrorCode.SMS_CHOOSE_PHONES_ERROR);
                }
            }
            boolean toDbResult = saveTaskAndDetailToDb(sendEntity, detailEntityList, null);
            if (!toDbResult) {
                log.info("MwSendManager -> createSendTask4SmsPlatform saveTaskAndDetailToDb fail, arg = {}", arg);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } else {
            // excel导入的方式，把校验提前
            if (arg.getGroupType().equals(SmsGroupTypeEnum.EXCEL.getType()) && StringUtils.isNotEmpty(arg.getTaPath())) {
                Result validate4ExcelResult = validate4Excel(arg, templateEntity);
                log.info("MwSendManager -> createSendTask4SmsPlatform validate4ExcelResult, result = {}", validate4ExcelResult);
                if (!validate4ExcelResult.isSuccess()) {
                    return validate4ExcelResult;
                }
            }

            sendEntity.setStatus(MwSendStatusEnum.CALCULATE.getStatus());
            boolean toDbResult = saveSendEntityAndTemplateToDb(sendEntity, null);
            if (!toDbResult) {
                log.info("MwSendManager -> createSendTask4SmsPlatform saveSendEntityAndTemplateToDb fail, arg = {}", arg);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            ThreadPoolUtils.executeWithNewThread("sendSmsTask-" + sendEntity.getId(), () -> {
                Result<List<MwSendDetailEntity>> createResult = createSendDetail(sendEntity, signatureEntity, templateEntity, arg, false);
                if (createResult == null) {
                    log.warn("MwSendManager -> createSendTask4SmsPlatform createSendDetail is null, arg = {}", arg);
                    sendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                    smsSendDao.updateSendEntityStatus(sendEntity);
                    return ;
                }
                if (!createResult.isSuccess()) {
                    log.warn("MwSendManager -> createSendTask4SmsPlatform createSendDetail is not success, arg = {} errorCode:{} errorMsg:{}", arg, createResult.getErrCode(), createResult.getErrMsg());
                    sendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                    smsSendDao.updateSendEntityStatus(sendEntity);
                    return ;
                }
                List<MwSendDetailEntity> detailEntityList = createResult.getData();
                if (CollectionUtils.isEmpty(detailEntityList)) {
                    log.warn("MwSendManager -> createSendTask4SmsPlatform fail, detailEntityList is empty, arg = {}", arg);
                    sendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                    smsSendDao.updateSendEntityStatus(sendEntity);
                    return ;
                }
                // detail计算完成,保存至db
                sendEntity.setStatus(MwSendStatusEnum.NEW.getStatus());
                saveDetailAndUpdateSendEntity(detailEntityList, sendEntity);
                // 如果是发送类型，则需要扣费并更新status
                if (isSendType) {
                    if (sendEntity.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType())) {
                        sendEntity.setStatus(MwSendStatusEnum.SCHEDULE_SENDING.getStatus());
                    } else {
                        sendEntity.setStatus(MwSendStatusEnum.READY.getStatus());
                    }
                    smsSendDao.updateSendEntityStatus(sendEntity);
                }
            });
        }
        return Result.newSuccess(sendEntity);
    }

    public Result<MwSmsSendEntity> updateSendTask(CreateSendTaskArg arg) {
        MwSmsSignatureEntity signatureEntity;
        if (StringUtils.isNotEmpty(arg.getSignatureId())) {
            signatureEntity = signatureDao.getSignatureById(arg.getSignatureId());
        } else {
            signatureEntity = getSmsSignature(arg.getEa(), arg.getChannelType());
        }
        if (signatureEntity == null) {
            log.info("MwSendManager updateSendTask signatureEntity is null, ea = {}", arg.getEa());
            return Result.newError(SHErrorCode.SIGNATURE_NOT_EXIST);
        }

        MwSmsTemplateEntity templateEntity;
        if (StringUtils.isNotEmpty(arg.getTemplateId())) {
            templateEntity = templateDao.getTemplateById(arg.getTemplateId());
        } else {
            templateEntity = arg.getTemplateEntity();
        }

        if (templateEntity == null) {
            log.info("MwSendManager updateSendTask templateEntity is null, templateId = {}", arg.getTemplateId());
            return Result.newError(SHErrorCode.SMS_TEMPLATE_NOT_EXIST);
        }
        MwSmsSendEntity sendEntity = smsSendDao.getSMSSendById(arg.getSmsSendId());

        if (sendEntity == null) {
            log.info("MwSendManager updateSendTask sendEntity is null, smsSendId = {}", arg.getSmsSendId());
            return Result.newError(SHErrorCode.SMS_SEND_NOT_EXIST_ERROR);
        }

        if (!sendEntity.getStatus().equals(MwSendStatusEnum.NEW.getStatus()) && !sendEntity.getStatus().equals(MwSendStatusEnum.SEND_CANCEL.getStatus())
            && !sendEntity.getStatus().equals(MwSendStatusEnum.SEND_FAIL.getStatus())) {
            log.info("MwSendManager updateSendTask sendEntity status error, sendEntity = {}", sendEntity);
            return Result.newError(SHErrorCode.SMS_SEND_STATUS_ERROR);
        }

        sendEntity.setType(arg.getTaskType());
        if (arg.getTaskType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType())) {
            if (arg.getScheduleTime() == null) {
                log.info("MwSendManager updateSendTask scheduleTime is null, arg = {}", arg);
                return Result.newError(SHErrorCode.SMS_SCHEDULE_TIME_ERROR);
            }
            sendEntity.setScheduleTime(arg.getScheduleTime());
        }
        sendEntity.setUserGroups(arg.getUserGroupIds());

        List<MwSendDetailEntity> detailEntityList = null;
        // 如果修改了excel 或者选择的电话号码，那我们直接新建明细
        if (StringUtils.isNotEmpty(arg.getTaPath()) || CollectionUtils.isNotEmpty(arg.getPhones())|| CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {
            Result<List<MwSendDetailEntity>> createResult = createSendDetail(sendEntity, signatureEntity, templateEntity, arg, false);
            if (createResult == null) {
                log.info("MwSendManager createSendTask createSendDetail is null, arg = {}", arg);
                return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
            }
            if (!createResult.isSuccess()) {
                log.info("MwSendManager updateSendTask createSendDetail is not success, arg = {}", arg);
                return Result.newError(createResult.getErrCode(), createResult.getErrMsg());
            }
            detailEntityList = createResult.getData();
            if (CollectionUtils.isEmpty(detailEntityList)) {
                log.info("MwSendManager updateSendTask createSendDetail fail, detailEntityList is empty, arg = {}", arg);
                if (StringUtils.isNotEmpty(arg.getTaPath())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                } if (CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {
                    return Result.newError(SHErrorCode.SMS_USER_GROUP_ERROR);
                }else {
                    return Result.newError(SHErrorCode.SMS_CHOOSE_PHONES_ERROR);
                }
            }
        } else if (CollectionUtils.isEmpty(arg.getUserGroupIds()) && !Objects.equals(sendEntity.getTemplateId(), arg.getTemplateId())) { //仅仅只是更改了模板
            detailEntityList = reCreateSendDetailByTemplate(sendEntity, signatureEntity, templateEntity);
            if (CollectionUtils.isEmpty(detailEntityList)) {
                log.info("MwSendManager updateSendTask reCreateSendDetailByTemplate fail, detailEntityList is empty, arg = {}", arg);
                if (StringUtils.isNotEmpty(arg.getTaPath())) {
                    return Result.newError(SHErrorCode.SMS_UPDATE_TEMPLATE_FAILED);
                }
            }
        } else {
            log.info("MwSendManager updateSendTask fail, param is error, arg = {}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        sendEntity.setTemplateId(arg.getTemplateId());

        boolean toDbResult = updateTaskAndDetailToDb(sendEntity, detailEntityList, arg.getTemplateEntity());
        if (!toDbResult) {
            log.info("MwSendManager updateSendTask updateTaskAndDetailToDb fail, arg = {}", arg);
            Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
        return Result.newSuccess(sendEntity);
    }

    @Transactional
    public boolean saveDetailAndUpdateSendEntity(List<MwSendDetailEntity> detailEntityList, MwSmsSendEntity sendEntity) {
        detailEntityList.forEach(e -> {
            e.setEa(sendEntity.getEa());
            e.setChannelType(sendEntity.getChannelType());
            e.setTemplateId(sendEntity.getTemplateId());
        });
        if (detailEntityList.size() > MAX_INSERT_NUM) {
            PageUtil<MwSendDetailEntity> mwSendDetailEntityPageUtil = new PageUtil<>(detailEntityList, MAX_INSERT_NUM);
            int pageCount = mwSendDetailEntityPageUtil.getPageCount();
            for (int i = 1; i <= pageCount; i++) {
                smsSendDao.batchInsertDetail(mwSendDetailEntityPageUtil.getPagedList(i));
            }
        } else {
            smsSendDao.batchInsertDetail(detailEntityList);
        }
        smsSendDao.updateSendEntity(sendEntity);
        return true;
    }

    @Transactional
    public boolean saveTaskAndDetailToDb(MwSmsSendEntity sendEntity, List<MwSendDetailEntity> detailEntityList, MwSmsTemplateEntity templateEntity){
        if (sendEntity == null || CollectionUtils.isEmpty(detailEntityList)) {
            log.info("MwSendManager saveTaskAndDetailToDb fail, sendEntity = {}, detailEntityList = {}", sendEntity, detailEntityList);
            return false;
        }
        try {
            if (templateEntity != null) {
                if (templateDao.addMwTemplate(templateEntity)) {
                    Result<String> uploadResult =  mwTemplateManager.uploadOrModifyTemplate(sendEntity.getEa(), templateEntity, MwTemplateActionEnum.UPLOAD_TEMPLATE.getType());
                    if (uploadResult.isSuccess() && uploadResult.getData() != null){
                        templateDao.setTemplateTplid(templateEntity.getId(), uploadResult.getData());
                    }
                }
            }

            smsSendDao.insertSendEntity(sendEntity);
            registerMaterial(sendEntity);
            detailEntityList.forEach(e -> {
                e.setEa(sendEntity.getEa());
                e.setChannelType(sendEntity.getChannelType());
                e.setTemplateId(sendEntity.getTemplateId());
            });
            if (detailEntityList.size() > MAX_INSERT_NUM) {
                PageUtil<MwSendDetailEntity> mwSendDetailEntityPageUtil = new PageUtil<>(detailEntityList, MAX_INSERT_NUM);
                int pageCount = mwSendDetailEntityPageUtil.getPageCount();
                for (int i = 1; i <= pageCount; i++) {
                    smsSendDao.batchInsertDetail(mwSendDetailEntityPageUtil.getPagedList(i));
                }
            } else {
                smsSendDao.batchInsertDetail(detailEntityList);
            }
        } catch (Exception e) {
            log.info("MwSendManager saveTaskAndDetailToDb fail, sendEntity = {}, detailEntityList = {}, exception = {}", sendEntity, detailEntityList, e.toString());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    private void registerMaterial(MwSmsSendEntity sendEntity) {
        try {
            int channelType = sendEntity.getChannelType();
            if (channelType == ChannelTypeEnum.MARKETING.getType() || channelType == ChannelTypeEnum.MARKETING_FLOW.getType() || channelType == ChannelTypeEnum.CONFERENCE_NOTIFICATION.getType()) {
                MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(sendEntity.getEa(), sendEntity.getTemplateId());
                String label = templateEntity.getName() + "-" + DateUtil.format(new Date());
                integralServiceManager.asyncRegisterMaterial(sendEntity.getEa(), MankeepEventHandleManager.SMS_CATEGORY_NAME, sendEntity.getId(), label);
            }
        } catch (Exception e) {
            log.error("sms registerMaterial error, data: {}", sendEntity, e);
        }
    }

    @Transactional
    public boolean saveSendEntityAndTemplateToDb(MwSmsSendEntity sendEntity, MwSmsTemplateEntity templateEntity) {
        if (sendEntity == null) {
            log.info("MwSendManager saveSendEntityAndTemplateToDb fail, sendEntity = {}", sendEntity);
            return false;
        }
        try {
            if (templateEntity != null) {
                if (templateDao.addMwTemplate(templateEntity)) {
                    Result<String> uploadResult =  mwTemplateManager.uploadOrModifyTemplate(sendEntity.getEa(), templateEntity, MwTemplateActionEnum.UPLOAD_TEMPLATE.getType());
                    if (uploadResult.isSuccess() && uploadResult.getData() != null){
                        templateDao.setTemplateTplid(templateEntity.getId(), uploadResult.getData());
                    }
                }
            }
            smsSendDao.insertSendEntity(sendEntity);
            registerMaterial(sendEntity);
        } catch (Exception e) {
            log.info("MwSendManager saveSendEntityAndTemplateToDb fail, sendEntity = {}, exception = {}", sendEntity, e.toString());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    @Transactional
    public boolean updateTaskAndDetailToDb(MwSmsSendEntity sendEntity, List<MwSendDetailEntity> detailEntityList, MwSmsTemplateEntity templateEntity){
        if (sendEntity == null) {
            log.info("MwSendManager updateTaskAndDetailToDb fail, sendEntity = {}", sendEntity);
            return false;
        }
        try {
            if (templateEntity != null) {
                if (templateDao.addMwTemplate(templateEntity)) {
                    Result<String> uploadResult =  mwTemplateManager.uploadOrModifyTemplate(sendEntity.getEa(), templateEntity, MwTemplateActionEnum.UPLOAD_TEMPLATE.getType());
                    if (uploadResult.isSuccess() && uploadResult.getData() != null){
                        templateDao.setTemplateTplid(templateEntity.getId(), uploadResult.getData());
                    }
                }
            }
            smsSendDao.updateSendEntity(sendEntity);
            if (CollectionUtils.isNotEmpty(detailEntityList)) { // 只有修改了明细才需要处理
                smsSendDao.deleteSMSSendDetailById(sendEntity.getId());// 先删除旧的明细
                detailEntityList.forEach(e -> {
                    e.setEa(sendEntity.getEa());
                    e.setChannelType(sendEntity.getChannelType());
                    e.setTemplateId(sendEntity.getTemplateId());
                });
                if (detailEntityList.size() > MAX_INSERT_NUM) {
                    PageUtil<MwSendDetailEntity> mwSendDetailEntityPageUtil = new PageUtil<>(detailEntityList, MAX_INSERT_NUM);
                    int pageCount = mwSendDetailEntityPageUtil.getPageCount();
                    for (int i = 1; i <= pageCount; i++) {
                        smsSendDao.batchInsertDetail(mwSendDetailEntityPageUtil.getPagedList(i));
                    }
                } else {
                    smsSendDao.batchInsertDetail(detailEntityList); // 再插入新的明细
                }
            }
        } catch (Exception e) {
            log.info("MwSendManager updateTaskAndDetailToDb fail, sendEntity = {}, detailEntityList = {}, exception = {}", sendEntity, detailEntityList, e.toString());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    public Result<List<MwSendDetailEntity>> createSendDetail4New(MwSmsSendEntity sendEntity, MwSmsSignatureEntity signatureEntity, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg, boolean isCalcQuoto){
        String ea = arg.getEa();
        // 短信发送两个要素：手机号和发送内容，手机号的来源包括excel导入、目标人群选择、活动成员选择，直接传入手机号，发送内容就是模板变量的替换，有了这两个东西，就很好解决问题
        // 思路：先解决手机号的问题，把手机号和变量整合到一个对象中，然后再通过这个对象组装发送内容
        try {
            log.info("MwSendManager -> createSendDetail4New start");
            // 1 构造手机号发送对象，组装成PhoneContentResult对象
            List<PhoneContentResult> phoneContentResultList = Lists.newArrayList();
            Result<List<PhoneContentResult>> buildPhoneContentResult = Result.newSuccess(Lists.newArrayList());

            // excel导入
            if (Objects.equals(arg.getGroupType(), SmsGroupTypeEnum.EXCEL.getType()) && StringUtils.isNotEmpty(arg.getTaPath())) {
                log.info("MwSendManager -> createSendDetail4New handle excel");
                buildPhoneContentResult = buildPhoneContentFromExcel(arg);
            }
            // 目标人群选择
            if (Objects.equals(arg.getGroupType(), SmsGroupTypeEnum.USER_GROUP.getType()) && CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {
                //计算发送额度
                if (isCalcQuoto){
                    return calcSpendingQuotaInfoForUserGroup(sendEntity, signatureEntity.getSvrName(), templateEntity.getContent(), arg);
                }

                // 判断市场活动类型
                String marketingEventId = arg.getMarketingEventId();
                MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, SuperUserConstants.USER_ID, marketingEventId);
                String eventType = marketingEventData.getEventType();
                if (Objects.equals(eventType, MarketingEventEnum.LIVE_MARKETING.getEventType())) {
                    // 直播邀约
                    log.info("MwSendManager -> createSendDetail4New handle live Invite");
                    buildPhoneContentResult = buildPhoneContentFromLiveInvite(arg);
                } else if (Objects.equals(eventType, MarketingEventEnum.MEETING_SALES.getEventType())) {
                    // 会议邀约
                    log.info("MwSendManager -> createSendDetail4New handle conference Invite");
                    buildPhoneContentResult = buildPhoneContentFromConferenceInvite(arg);
                } else {
                    // 活动邀约
                    log.info("MwSendManager -> createSendDetail4New handle event Invite");
                    buildPhoneContentResult = buildPhoneContentFromEventInvite(arg);
                }
            }
            // 活动成员选择
            if (CollectionUtils.isNotEmpty(arg.getCampaignIds())) {
                // 判断市场活动类型
                String marketingEventId = arg.getMarketingEventId();
                MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, SuperUserConstants.USER_ID, marketingEventId);
                String eventType = marketingEventData.getEventType();
                if (Objects.equals(eventType, MarketingEventEnum.LIVE_MARKETING.getEventType())) {
                    // 直播通知
                    log.info("MwSendManager -> createSendDetail4New handle live enroll");
                    buildPhoneContentResult = buildPhoneContentFromLiveEnroll(arg);
                } else if (Objects.equals(eventType, MarketingEventEnum.MEETING_SALES.getEventType())) {
                    // 会议通知
                    log.info("MwSendManager -> createSendDetail4New handle conference enroll");
                    buildPhoneContentResult = buildPhoneContentFromConferenceEnroll(arg);
                } else {
                    // 活动通知
                    log.info("MwSendManager -> createSendDetail4New handle event enroll");
                    buildPhoneContentResult = buildPhoneContentFromEventEnroll(arg);
                }
            }
            if (buildPhoneContentResult.isSuccess()) {
                phoneContentResultList.addAll(buildPhoneContentResult.getData());
            } else {
                log.info("MwSendManager -> createSendDetail4New handle failed, buildPhoneContentResult = {}", buildPhoneContentResult);
                return Result.newError(buildPhoneContentResult.getErrCode(), buildPhoneContentResult.getErrMsg());
            }

            // 兼容直接传入手机号的场景
            if (CollectionUtils.isEmpty(phoneContentResultList) && CollectionUtils.isNotEmpty(arg.getPhones())) {
                phoneContentResultList.addAll(arg.getPhones());
            }

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(phoneContentResultList)) {
                log.info("MwSendManager -> createSendDetail4New handle failed, 发送手机号为空");
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            // 2 根据第一步生成的对象，组装发送内容，并生成发送明细实体类MwSendDetailEntity
            //log.info("MwSendManager -> createSendDetail4New handle success, phoneContentResultList = {}", phoneContentResultList);
            arg.setPhones(phoneContentResultList);
            arg.setCalcQuoto(isCalcQuoto);
            return createSendDetailFromPhones(sendEntity, signatureEntity.getSvrName(), templateEntity, arg);
        } catch (Exception e) {
            log.error("MwSendManager -> createSendDetail4New error, sendEntity: {}, arg: {} isCalcQuoto: {}", sendEntity, arg, isCalcQuoto, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 是否为新模板能力
     * @param mwSmsTemplateEntity
     * @return
     */
    private boolean isNewTemplate(MwSmsTemplateEntity mwSmsTemplateEntity){
        return mwSmsTemplateEntity != null && Objects.equals(mwSmsTemplateEntity.getTplVersion(), SmsTemplateVersionEnum.NEW_MARKETING.getType());
    }

    public Result<List<MwSendDetailEntity>> createSendDetail(MwSmsSendEntity sendEntity, MwSmsSignatureEntity signatureEntity, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg, boolean isCalcQuoto){
        try {
            log.info("MwSendManager.createSendDetail MwSmsSendEntity:{} MwSmsSignatureEntity:{} MwSmsTemplateEntity:{} CreateSendTaskArg:{} isCalcQuoto:{}", sendEntity, signatureEntity, templateEntity, arg, isCalcQuoto);
            return createSendDetail4New(sendEntity, signatureEntity, templateEntity, arg, isCalcQuoto);
        } catch (Exception e) {
            log.error("createSendDetail error, sendEntity: {}, arg: {} isCalcQuoto: {}", sendEntity, arg, isCalcQuoto, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    public Integer createSendDetailCount(CreateSendTaskArg arg){
        if (arg.getGroupType().equals(SmsGroupTypeEnum.EXCEL.getType()) && StringUtils.isNotEmpty(arg.getTaPath())) {
            String taPath = arg.getTaPath();
            byte[] excelByte = fileV2Manager.downloadAFile(taPath, null);
            if (excelByte == null) {
                return 0;
            }
            List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(taPath, excelByte);
            ParamsOfExcelResult excelResult = ReadExcelUtil.readList2ParamAndPhone(excelData, arg.isIgnoreErrorPhone());
            if (excelResult.getType().equals(ExcelCheckResultTypeEnum.SUCCESS.getType())) {
                List<OneSMSDetailResult> smsDetails = excelResult.getOneSMSDetailResults();
                return smsDetails.size();
            }
        }

        MwSmsTemplateEntity templateEntity;
        if (StringUtils.isNotEmpty(arg.getTemplateId())) {
            templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), arg.getTemplateId());
        } else {
            templateEntity = arg.getTemplateEntity();
        }
        Integer sceneType = templateEntity.getSceneType();
        if (SmsSceneTypeEnum.CONFERENCE_INVITE.getType().equals(sceneType) || arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_INVITE.getType())) {
            return marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(arg.getEa(), arg.getUserGroupIds());
        }
        if (SmsSceneTypeEnum.LIVE_INVITE.getType().equals(sceneType)) {
            return marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(arg.getEa(), arg.getUserGroupIds());
        }

        if (arg.getGroupType().equals(SmsGroupTypeEnum.PHONE_LIST.getType()) && CollectionUtils.isNotEmpty(arg.getPhones())) {
            List<PhoneContentResult> phones = arg.getPhones();
            if (CollectionUtils.isEmpty(phones)) {
                return 0;
            }
            if (CollectionUtils.isNotEmpty(phones) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
                //过滤days天内发送过短信的手机，防止短信骚扰
                phones = filterPhoneObjectUsingPhoneContentResultType(arg.getEa(), arg.getFilterNDaySentUser(), phones, arg.getChannelType(), false);
            }
            int toSenderCount = 0;
            List<String> hasPhones = Lists.newArrayList();
            for (PhoneContentResult phoneContent : phones) {
                if (hasPhones.contains(phoneContent.getPhone().trim())) {
                    continue;
                }
                if (!RexUtil.rexForPhone(phoneContent.getPhone().trim())) {
                    continue;
                }
                toSenderCount += 1;
                hasPhones.add(phoneContent.getPhone().trim());
            }
            return toSenderCount;
        }

        if (arg.getGroupType().equals(SmsGroupTypeEnum.VERIFICATION_CODE.getType()) && CollectionUtils.isNotEmpty(arg.getPhones())) {
            List<PhoneContentResult> phones = arg.getPhones();
            if (CollectionUtils.isEmpty(phones)) {
                return 0;
            }
            if (CollectionUtils.isNotEmpty(phones) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
                //过滤days天内发送过短信的手机，防止短信骚扰
                phones = filterPhoneObjectUsingPhoneContentResultType(arg.getEa(), arg.getFilterNDaySentUser(), phones, arg.getChannelType(), false);
            }
            int toSenderCount = 0;
            List<String> hasPhones = Lists.newArrayList();
            for (PhoneContentResult phoneContent : phones) {
                if (hasPhones.contains(phoneContent.getPhone().trim())) {
                    continue;
                }
                if (!RexUtil.rexForPhone(phoneContent.getPhone().trim())) {
                    continue;
                }
                toSenderCount += 1;
                hasPhones.add(phoneContent.getPhone().trim());
            }
            return toSenderCount;
        }

        if (arg.getGroupType().equals(SmsGroupTypeEnum.USER_GROUP.getType()) && CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {

            List<String> userGroupIds = arg.getUserGroupIds();
            if (CollectionUtils.isEmpty(userGroupIds)) {
                return 0;
            }

            List<MobileMarketingUserResult> marketingUserResults = marketingUserGroupManager.listMobileByMarketingUserGroupIds(arg.getEa(), userGroupIds);

            if (CollectionUtils.isNotEmpty(marketingUserResults) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
                //过滤days天内发送过短信的手机，防止短信骚扰
                marketingUserResults = filterPhoneObjectUsingMobileMarketingUserResultType(arg.getEa(), arg.getFilterNDaySentUser(), marketingUserResults, arg.getChannelType());
            }
            if (CollectionUtils.isEmpty(marketingUserResults)) {
                return 0;
            }
            //需要发送人数
            int toSenderCount = 0;
            SHErrorCode firstError = null;
            List<String> hasPhones = Lists.newArrayList();
            for (MobileMarketingUserResult marketingUser : marketingUserResults) {
                if (StringUtils.isEmpty(marketingUser.getMobile()) || !RexUtil.rexForPhone(marketingUser.getMobile().trim())) {
                    if (firstError == null) {
                        firstError = SHErrorCode.SMS_USER_GROUP_PHONE_ERROR;
                    }
                    continue;
                }
                if (hasPhones.contains(marketingUser.getMobile().trim())) {
                    continue;
                }
                toSenderCount += 1;
                hasPhones.add(marketingUser.getMobile().trim().trim());
            }
            return toSenderCount;
        }
        if (arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_ENROLL.getType()) && CollectionUtils.isNotEmpty(arg.getCampaignIds())) {
            List<String> campaignMergeDataIds = arg.getCampaignIds();
            if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
                return 0;
            }
            List<String> conferenceEnrollIds = campaignMergeDataManager.campaignIdToActivityEnrollId(campaignMergeDataIds);
            List<PhoneContentResult> phoneContentResults = conferenceManager.buildPhoneContentEnrollList(conferenceEnrollIds, false);
            return phoneContentResults.size();
        }

        // 直播类型
        if (arg.getGroupType().equals(SmsGroupTypeEnum.LIVE_ENROLL.getType()) && CollectionUtils.isNotEmpty(arg.getCampaignIds())){
            List<String> campaignMergeDataIds = arg.getCampaignIds();
            if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
                return 0;
            }
            List<PhoneContentResult> phoneContentResults = liveManager.buildLivePhoneContentList(campaignMergeDataIds, false);
            return phoneContentResults.size();
        }

        // 活动营销
        if (arg.getGroupType().equals(SmsGroupTypeEnum.MARKETING_ACTIVITY.getType()) && CollectionUtils.isNotEmpty(arg.getCampaignIds())) {
            List<String> campaignMergeDataIds = arg.getCampaignIds();
            if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
                return 0;
            }
            List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignMergeDataIds);
            return campaignMergeDataEntityList.size();
        }

        return 0;
    }

    private Optional<List<PhoneContentResult>> getPhoneList(String taPath) {
        if (StringUtils.isEmpty(taPath)) {
            return Optional.empty();
        }
        byte[] excelByte = fileV2Manager.downloadAFile(taPath, null);
        if (excelByte == null) {
            return Optional.empty();
        }
        List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(taPath, excelByte);
        ParamsOfExcelResult excelResult = ReadExcelUtil.readList2ParamAndPhone(excelData, false);
        if (excelResult.getType().equals(ExcelCheckResultTypeEnum.SUCCESS.getType())) {
            List<OneSMSDetailResult> oneSMSDetailResults = excelResult.getOneSMSDetailResults();
            if (CollectionUtils.isNotEmpty(oneSMSDetailResults)) {
                List<PhoneContentResult> result = new LinkedList<>();
                for (OneSMSDetailResult phoneInfo : oneSMSDetailResults) {
                    result.add(new PhoneContentResult(phoneInfo.getMobile()));
                }
                return Optional.ofNullable(result);
            }
        }
        return Optional.empty();
    }

    private Optional<List<PhoneContentResult>> getPhoneList(String ea, List<String> userGroupIds) {
        if (CollectionUtils.isEmpty(userGroupIds)) {
            return Optional.empty();
        }
        List<MobileMarketingUserResult> mobileMarketingUserResults = marketingUserGroupManager.listMobileByMarketingUserGroupIds(ea, userGroupIds);
        if (CollectionUtils.isEmpty(mobileMarketingUserResults)) {
            return Optional.empty();
        }
        List<PhoneContentResult> result = new LinkedList<>();
        for (MobileMarketingUserResult phoneInfo : mobileMarketingUserResults) {
            if (StringUtils.isNotEmpty(phoneInfo.getMobile())) {
                result.add(new PhoneContentResult(phoneInfo.getMobile()));
            }
        }
        return Optional.of(result);
    }

//    /**
//     * 5.5需求为每个手机号添加埋点信息
//     * 用于给长链 xxx&marketingActivityId& 拼接为 xxx&marketingActivityId&phone=xxx， 并转化为短链接
//     * @return
//     */
//    public String changeLongUrl2ShortUrlWithPhone(MwSendDetailEntity mwSendDetailEntity, CreateSendTaskArg arg, MwSmsTemplateEntity templateEntity) {
//        log.info("MwSendManager.changeLongUrl2ShortUrlWithPhone mwSendDetailEntity:{}  arg:{}", mwSendDetailEntity, arg);
//        arg.getLongUrls().forEach(longUrl -> {
//            String longUrlWithPhone = longUrl + "phone=" + mwSendDetailEntity.getPhone();
//            String shortUrlContent = smsParamManager.getShortUrl(longUrlWithPhone);
//            String content = mwSendDetailEntity.getContent();
//            String resultContent = content.replace(longUrl, shortUrlContent);
//            mwSendDetailEntity.setContent(resultContent);
//        });
//        return mwSendDetailEntity.getContent();
//    }

    //计算营销用户短信发送数量
    private Result<List<MwSendDetailEntity>> calcSpendingQuotaInfoForUserGroup(MwSmsSendEntity sendEntity, String signature, String template, CreateSendTaskArg arg){
        List<String> userGroupIds = arg.getUserGroupIds();
        if (CollectionUtils.isEmpty(userGroupIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }

        int toSenderCount = marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(sendEntity.getEa(), userGroupIds);
        if (toSenderCount == 0){
            return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
        }
        int totalFee;
        String smsContent = buildSmsContent(signature, template);
        if (smsContent.length() > SmsContants.MW_SMS_CONTENT_WORD_LIMIT){
            return Result.newError(SHErrorCode.SMS_WORD_LIMIT_ERROR);
        }else {
            int fee = quotaManager.calcSendSMSCount(smsContent.length());
            totalFee = fee * toSenderCount;
        }
        sendEntity.setTotalFee(totalFee);
        sendEntity.setActualSenderCount(toSenderCount);
        sendEntity.setToSenderCount(toSenderCount);

        return Result.newSuccess(null);
    }

    //仅仅只是修改模板的时候重新创建短信明细
    private List<MwSendDetailEntity> reCreateSendDetailByTemplate(MwSmsSendEntity sendEntity, MwSmsSignatureEntity signatureEntity, MwSmsTemplateEntity templateEntity) {
        List<MwSendDetailEntity> newDetailEntityList = Lists.newArrayList();
        List<MwSendDetailEntity> oldDetailEntityList = smsSendDao.querySendDetailBySendId(sendEntity.getId(), MwSendDetailStatusEnum.NO_SEND.getStatus());
        if (CollectionUtils.isNotEmpty(oldDetailEntityList)) {
            int totalFee = 0;
            //实际发送人数
            int actualSenderCount = 0;
            //需要发送人数
            int toSenderCount = 0;
            for (MwSendDetailEntity oldDetail : oldDetailEntityList) {
                toSenderCount += 1;
                MwSendDetailEntity detailEntity = new MwSendDetailEntity();
                detailEntity.setId(UUIDUtil.getUUID());
                detailEntity.setSendId(sendEntity.getId());
                detailEntity.setPhone(oldDetail.getPhone());

                int templateParamsCnt = quotaManager.getTemplateParamCnt(templateEntity.getContent());
                if (oldDetail.getParams().size() < templateParamsCnt){
                    return null;
                }

                String detailContent = replaceTemplateParamWithValue(templateEntity.getContent(), oldDetail.getParams());
                if (StringUtils.isEmpty(detailContent)) {
                    return null;
                }

                detailEntity.setContent(detailContent);

                String smsContent = buildSmsContent(signatureEntity.getSvrName(), detailContent);
                if (StringUtils.isEmpty(smsContent)) {
                    return null;
                }
                if (smsContent.length() > SmsContants.MW_SMS_CONTENT_WORD_LIMIT) {
                    return null;
                }

                int fee = quotaManager.calcSendSMSCount(smsContent.length());
                detailEntity.setFee(fee);
                totalFee += fee;
                actualSenderCount += 1;
                detailEntity.setStatus(MwSendDetailStatusEnum.NO_SEND.getStatus());
                newDetailEntityList.add(detailEntity);
            }
            sendEntity.setTotalFee(totalFee);
            sendEntity.setActualSenderCount(actualSenderCount);
            sendEntity.setToSenderCount(toSenderCount);
        }

        return newDetailEntityList;
    }

    /*private Result<List<MwSendDetailEntity>> readSendDetailFromExcel(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg, boolean isCalcQuoto) {
        String template = null;
        if (CollectionUtils.isEmpty(arg.getLongUrls())) {
            template = templateEntity.getContent();
        } else {
            template = arg.getTemplateContentWithMarketingActivityId();
        }
        String taPath = arg.getTaPath();
        List<MwSendDetailEntity> detailEntityList = Lists.newArrayList();
        try{
            byte[] excelByte = fileV2Manager.downloadAFile(taPath, null);
            if (excelByte == null) {
                return Result.newError(SHErrorCode.SMS_EXCEL_DOWNLOAD_ERROR);
            }
            List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(taPath, excelByte);
            ParamsOfExcelResult excelResult = ReadExcelUtil.readList2ParamAndPhone(excelData, arg.isIgnoreErrorPhone());
            if (excelResult.getType().equals(ExcelCheckResultTypeEnum.SUCCESS.getType())) {
                List<OneSMSDetailResult> smsDetails = excelResult.getOneSMSDetailResults();
                if (CollectionUtils.isNotEmpty(smsDetails) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())){
                    //过滤days天内发送过短信的手机，防止短信骚扰
                    smsDetails = filterPhoneObjectUsingOneSMSDetailResultType(arg.getEa(), arg.getFilterNDaySentUser(), smsDetails, arg.getChannelType(), isCalcQuoto);
                    if (CollectionUtils.isEmpty(smsDetails)) {
                        return Result.newError(SHErrorCode.EMPTY_MARKETING_ACTIVITY_SEND_USER_AFTER_FILTER);
                    }
                }
                if (CollectionUtils.isNotEmpty(smsDetails)) {
                    int templateParamsCnt = quotaManager.getTemplateParamCnt(template);
                    int totalFee = 0;
                    //实际发送人数
                    int actualSenderCount = 0;
                    //需要发送人数
                    int toSenderCount = 0;
                    for (OneSMSDetailResult smsDetail : smsDetails) {
                        if (smsDetail.getParams().size() < templateParamsCnt){
                            return Result.newError(SHErrorCode.EXCEL_PARAM_NOT_EQUAL_TEMPLATE);
                        }
                        toSenderCount += 1;
                        MwSendDetailEntity detailEntity = new MwSendDetailEntity();
                        detailEntity.setId(UUIDUtil.getUUID());
                        detailEntity.setSendId(sendEntity.getId());
                        detailEntity.setPhone(smsDetail.getMobile());
                        detailEntity.setParams(smsDetail.getParams());

                        String detailContent = replaceTemplateParamWithValue(template, smsDetail.getParams());
                        if (StringUtils.isEmpty(detailContent)) {
                            return Result.newError(SHErrorCode.SMS_PARAM_REPLACE_ERROR);
                        }

                        detailEntity.setContent(detailContent);

                        String smsContent = buildSmsContent(signature, detailContent);
                        if (smsContent.length() > SmsContants.MW_SMS_CONTENT_WORD_LIMIT) {
                            return Result.newError(SHErrorCode.SMS_WORD_LIMIT_ERROR);
                        }

                        int fee = quotaManager.calcSendSMSCount(smsContent.length());
                        detailEntity.setFee(fee);
                        totalFee += fee;
                        actualSenderCount += 1;
                        detailEntity.setStatus(MwSendDetailStatusEnum.NO_SEND.getStatus());
                        detailEntityList.add(detailEntity);
                    }
                    sendEntity.setTotalFee(totalFee);
                    sendEntity.setActualSenderCount(actualSenderCount);
                    sendEntity.setToSenderCount(toSenderCount);
                }
            } else {
                log.warn("readSendDetailFromExcel excelResult is not success, excelResult:{}", excelResult);
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.EXCEL_EMPTY.getType())) {
                    return Result.newError(SHErrorCode.APPLY_TEMPLATE_NULL_FAILED);
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.PHONE_ERROR.getType())) {
                    StringBuilder sb = new StringBuilder();
                    int errorTotal = 0;
                    if (CollectionUtils.isNotEmpty(excelResult.getErrorList())) {
                        for (String errorPhone : excelResult.getErrorList()) {
                            sb.append(errorPhone).append(",");
                            errorTotal++;
                        }
                    }
                    Result result = new Result(SHErrorCode.EXCEL_TEMPLATE_PHONE_ERROR.getErrorCode(), sb.toString());
                //    result.setErrMsg(String.valueOf(errorTotal));

                    return result;
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.PARAMETER_ERROR.getType())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.TITLE_ERROR.getType())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.WRONG_FORMAT.getType())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                }
            }
        } catch (Exception e) {
            log.info("MwSendManager readSendDetailFromExcel failed, exception:{}", e.toString());
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_EXCEPTION_ERROR);
        }
        return Result.newSuccess(detailEntityList);
    }*/

    /**
     * 过滤当前活动中已成功收到此短信内容的用户
     */
    private List<PhoneContentResult> filterReceived(List<PhoneContentResult> phoneContentResults, String marketingEventId, String templateId, String ea) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(phoneContentResults) || org.apache.commons.lang3.StringUtils.isBlank(marketingEventId) || org.apache.commons.lang3.StringUtils.isBlank(templateId)) {
            return phoneContentResults;
        }
        List<String> successPhones = smsSendDao.querySuccessPhoneByMktIdAndTemplateId(ea, templateId, marketingEventId);
        List<PhoneContentResult> collect = phoneContentResults.stream().filter(phoneContentResult -> !successPhones.contains(phoneContentResult.getPhone())).collect(Collectors.toList());
        log.info("filterReceived phones:{}", collect);
        return collect;
    }

    //通过电话号码和参数列表创建短信
    private Result<List<MwSendDetailEntity>> createSendDetailFromPhones(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg) {
        String template = null;
        if (CollectionUtils.isEmpty(arg.getLongUrls())) {
            template = templateEntity.getContent();
        } else {
            template = arg.getTemplateContentWithMarketingActivityId();
        }
        List<PhoneContentResult> phones = arg.getPhones();
        // 是否过滤当前活动中已成功收到此短信内容的用户
        if (arg.isFilterReceived()) {
            phones = filterReceived(phones, arg.getMarketingEventId(), arg.getTemplateId(), arg.getEa());
        }
        if (isNewTemplate(templateEntity)) {
            reBuildPhoneContentResults4New(phones, arg.getSmsVarArgs());
        }
        List<MwSendDetailEntity> detailEntityList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(phones)) {
            return Result.newError(SHErrorCode.SMS_PHONE_EMPTY_ERROR);
        }
        if (CollectionUtils.isNotEmpty(phones) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
            //过滤days天内发送过短信的手机，防止短信骚扰
            if (arg.getFilterNDaySentUser() != null && arg.getFilterNDaySentUser() > 0) {
                phones = filterPhoneObjectUsingPhoneContentResultType(arg.getEa(), arg.getFilterNDaySentUser(), phones, arg.getChannelType(), arg.isCalcQuoto());
            }
            if (CollectionUtils.isEmpty(phones)) {
                return Result.newError(SHErrorCode.EMPTY_MARKETING_ACTIVITY_SEND_USER_AFTER_FILTER);
            }
        }
        try{
            int totalFee = 0;
            //实际发送人数
            int actualSenderCount = 0;
            //需要发送人数
            int toSenderCount = 0;
            boolean mwTemplate = smsTemplateManager.isMwTemplate(sendEntity.getEa(), sendEntity.getTemplateId());
            List<String> hasPhones = Lists.newArrayList();
            log.info("createSendDetailFromPhones prepare send:{}", phones);
            String sourceDetailContent = "";
            // 这里的变量有2种格式，营销通为{}，其他为${}，${}里面有分为对象变量和自定义变量
            // 在处理变量替换逻辑时，考虑到性能问题，对象数据替换只需替换一次，在循环外处理，自定义变量替换则需要在循环内处理
            // 因此这里的替换步骤：1、替换对象变量 2、替换自定义变量 3、替换营销通{}格式变量
            // 1、对象变量及特殊格式变量替换
            sourceDetailContent = handleContent(sendEntity, templateEntity, arg);
            for (PhoneContentResult phoneContent : phones) {
                String detailContent = sourceDetailContent;
                if (hasPhones.contains(phoneContent.getPhone().trim())) {
                    log.info("createSendDetailFromPhones duplicate phone: {}", phoneContent);
                    continue;
                }
                if (!RexUtil.rexForPhone(phoneContent.getPhone().trim())) {
                    log.info("createSendDetailFromPhones phone error: {}", phoneContent);
                    continue;
                }
                toSenderCount += 1;
                MwSendDetailEntity detailEntity = new MwSendDetailEntity();
                detailEntity.setId(UUIDUtil.getUUID());
                detailEntity.setSendId(sendEntity.getId());
                detailEntity.setPhone(phoneContent.getPhone().trim());

                // 先替换自定义变量
                detailContent = replaceTemplateParamWith$(detailContent, phoneContent.getParamMap());
                // 再替换营销通变量
                detailContent = replaceTemplateParamWithValue(detailContent, phoneContent.getParams());
                if (phoneContent.getParamMap() != null && phoneContent.getParamMap().size() > 0) {
                    detailContent = replaceTemplateParamByMap(detailContent, phoneContent.getParamMap());
                }

                detailEntity.setContent(detailContent);
                detailEntity.setParams(phoneContent.getParams());
                if (MapUtils.isNotEmpty(phoneContent.getParamMap())) {
                    detailEntity.setVariables(GsonUtil.toJson(phoneContent.getParamMap()));
                }
                String smsContent = buildSmsContent(signature, detailContent);

                if (smsContent.length() > SmsContants.MW_SMS_CONTENT_WORD_LIMIT) {
                    log.error("createSendDetailFromPhones send sms content too long size:{}", smsContent.length());
                    detailEntity.setStatus(MwSendDetailStatusEnum.SEND_FAIL.getStatus());
                    detailEntity.setErrStatus(ERROR_999999);
                    return Result.newError(SHErrorCode.SMS_WORD_LIMIT_ERROR);
                }

                int fee = 0;
                if (mwTemplate) {
                    fee = quotaManager.calcSendSMSCount(smsContent.length());
                }
                detailEntity.setFee(fee);
                totalFee += fee;
                actualSenderCount += 1;
                detailEntity.setStatus(MwSendDetailStatusEnum.NO_SEND.getStatus());
                detailEntityList.add(detailEntity);
                hasPhones.add(phoneContent.getPhone().trim());
            }
            log.info("createSendDetailFromPhones prepare send phone count:{} real send phone count:{}", phones.size(), detailEntityList.size());
            if (CollectionUtils.isEmpty(detailEntityList)) {
                return Result.newError(SHErrorCode.SMS_PHONE_EMPTY_ERROR);
            }
            if (mwTemplate) {
                sendEntity.setTotalFee(totalFee);
                sendEntity.setActualSenderCount(actualSenderCount);
            } else {
                sendEntity.setTotalFee(0);
                sendEntity.setActualSenderCount(0);
            }
            sendEntity.setToSenderCount(toSenderCount);
        } catch (Exception e) {
            log.info("MwSendManager createSendDetailFromPhones failed, exception:{}", e);
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_EXCEPTION_ERROR);
        }
        return Result.newSuccess(detailEntityList);
    }

    @Data
    public static class MiniAppSmsParam implements Serializable {
        private String wxAppId;
    }

    /**
     * 通用模板短信内容解析
     * @param sendEntity
     * @param templateEntity
     * @param arg
     * @return
     */
    public String handleContent(MwSmsSendEntity sendEntity, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg){
        String ea = arg.getEa();
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        String templateContent = templateEntity.getContent();
        String finalContent = templateContent;
        List<SmsContentParam> smsContentParam = templateEntity.getSmsContentParam();
        Map<String, SmsContentParam> smsContentParamMap = Maps.newHashMap();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(smsContentParam)) {
            smsContentParamMap = smsContentParam.stream().collect(Collectors.toMap(SmsContentParam::getKey, Function.identity(), (v1, v2) -> v1));
        }
        // 找出所有的变量占位符
        Map<String, String> contentMap = Maps.newHashMap();
        List<String> keyList = handleVar(templateContent);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(keyList)) {
            for (String key : keyList) {
                if (key.startsWith(".##")) {
                    // 说明非crm对象
                    SmsContentParam contentParam = smsContentParamMap.get(key);
                    if (contentParam != null) {
                        String type = contentParam.getType();
                        String value = contentParam.getValue();
                        if (Objects.equals(type, "minip")) {
                            // 小程序链接
                            // 1 将value转化为对象
                            String realValue = "";
                            MiniAppSmsParam miniAppSmsParam = GsonUtil.fromJson(value, MiniAppSmsParam.class);
                            if (miniAppSmsParam != null) {
                                Result<String> stringResult = miniProgramAuthManager.generateScheme(miniAppSmsParam.getWxAppId(), ea);
                                if (stringResult.isSuccess()) {
                                    Result<ShortUrlResult> urlResultResult = sendService.getShortUrl(stringResult.getData());
                                    if (urlResultResult.isSuccess()) {
                                        realValue = urlResultResult.getData().getShortUrl();
                                    }
                                }
                            }
                            contentMap.put(key, realValue);
                        } else if (Objects.equals(type, "h5obj")) {
                            // 对象h5跳转链接
                            // 1 替换链接中的对象id占位符为具体值
                            value = value.replace("${dataId}", sendEntity.getObjectId());
                            // 2 将长链转成短链
                            Result<ShortUrlResult> shortUrl = sendService.getShortUrl(value);
                            contentMap.put(key, shortUrl.getData().getShortUrl());
                        } else {
                            // 外部链接
                            contentMap.put(key, value);
                        }
                    }
                } else {
                    // crm对象处理
                    String value = "";
                    String[] fields = key.split("\\.");
                    if (fields.length == 2) {
                        // 说明是当前对象 ${SalesOrderObj.creator} ​${销售订单.创建者}
                        String apiName = fields[0];
                        String fieldName = fields[1];
                        value = handleValue(ea, ei, apiName, sendEntity.getObjectId(), fieldName);
                        contentMap.put(key, value);
                    } else if (fields.length == 3) {
                        // 说明是当前对象的关联对象 ${SalesOrderObj.belong_to_supplier.is_enable} ​${销售订单.所属供应商.启用状态}
                        String apiName = fields[0];
                        String fieldName = fields[1];
                        String relationObjFieldName = fields[2]; // 关联对象字段名
                        // 1 查询当前对象详情
                        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, apiName, sendEntity.getObjectId());
                        // 2 查询当前对象描述
                        FieldDescribe fieldDescribe = crmV2Manager.getFieldDescribe(ei, apiName, fieldName);
                        String type = fieldDescribe.getType();
                        if (Objects.equals(type, "object_reference")) {
                            // 查找关联
                            String targetApiName = fieldDescribe.getTargetApiName();
                            String relationObjId = Objects.toString(objectData.get(fieldName), ""); // 关联对象id
                            if (StringUtils.isNotEmpty(relationObjId)) {
                                value = handleValue(ea, ei, targetApiName, relationObjId, relationObjFieldName);
                            }
                        } else if (Objects.equals(type, "employee")){
                            // 人员
                            Object fieldValue = objectData.get(fieldName);
                            if (fieldValue instanceof List) {
                                List<Object> fieldValues = (List)fieldValue;
                                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fieldValues)){
                                    String relationObjId = Objects.toString(fieldValues.get(0), ""); // 关联对象id
                                    if (StringUtils.isNotEmpty(relationObjId)) {
                                        // 查询人员对象详情
                                        if (Objects.equals(relationObjId, "-10000")) {
                                            value = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193);
                                        } else {
                                            value = handleValue(ea, ei, "PersonnelObj", relationObjId, relationObjFieldName);
                                        }
                                    }
                                }
                            }
                        }
                        contentMap.put(key, value);
                    }
                }
            }

            // 替换占位符
            for (String key : keyList) {
                if (contentMap.containsKey(key)) {
                    finalContent = finalContent.replace("${" + key + "}", contentMap.get(key));
                }
            }
        }
        log.info("短信发送->对象参数替换，替换前内容：{}，替换后内容：{}", templateContent, finalContent);
        return finalContent;
    }

    private String handleValue(String ea, Integer ei, String apiName, String objectId, String fieldName){
        String value = "";
        // 获取对象详情
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, apiName, objectId);
        // 获取对象字段描述
        FieldDescribe fieldDescribe = crmV2Manager.getFieldDescribe(ei, apiName, fieldName);
        String type = fieldDescribe.getType();
        if (Objects.equals(type, "object_reference")) {
            // 查找关联
            String targetApiName = fieldDescribe.getTargetApiName();
            String relationObjId = Objects.toString(objectData.get(fieldName), ""); // 关联对象id
            if (StringUtils.isNotEmpty(relationObjId)) {
                // 查询关联对象详情
                ObjectData relationObjectData = crmV2Manager.getDetail(ea, -10000, targetApiName, relationObjId);
                value = Objects.toString(relationObjectData.getName(), "");
            }
        } else if (Objects.equals(type, "employee")){
            // 人员
            Object fieldValue = objectData.get(fieldName);
            if (fieldValue instanceof List) {
                List<Object> fieldValues = (List)fieldValue;
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fieldValues)){
                    String relationObjId = Objects.toString(fieldValues.get(0), ""); // 关联对象id
                    if (StringUtils.isNotEmpty(relationObjId)) {
                        // 查询人员对象详情
                        if (Objects.equals(relationObjId, "-10000")) {
                            value = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193);
                        } else {
                            ObjectData relationObjectData = crmV2Manager.getDetail(ea, -10000, "PersonnelObj", relationObjId);
                            value = Objects.toString(relationObjectData.getName(), "");
                        }
                    }
                }
            }
        } else if (Objects.equals(type, "select_one")){
            // 单选
            List<Map<String, Object>> options = fieldDescribe.getOptions(); // 例如：[{label: "促销活动", value: "1"},{label: "品牌活动", value: "2"}]
            String fieldValue = objectData.getString(fieldName);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(options)) {
                for (Map<String, Object> option : options) {
                    if (Objects.equals(option.get("value"), fieldValue)) {
                        if (fieldValue.equals("other")) {
                            // value为"其他"，要特殊处理，取值为fieldName + "__o"
                            String s = Objects.toString(objectData.get(fieldName + "__o"), "");
                            value =  I18nUtil.get(I18nKeyEnum.MARK_MW_MWSENDMANAGER_1287) + s;
                        } else {
                            value =  Objects.toString(option.get("label"), "");
                        }
                        break;
                    }
                }
            }
        } else if (Objects.equals(type, "select_many")){
            // 多选
            List<Map<String, Object>> options = fieldDescribe.getOptions(); // 例如：[{label: "促销活动", value: "1"},{label: "品牌活动", value: "2"}]
            Object fieldValue = objectData.get(fieldName);
            if (fieldValue instanceof List) {
                List<Object> fieldValues = (List)fieldValue;
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fieldValues)){
                    for (Object fValue : fieldValues) {
                        for (Map<String, Object> option : options) {
                            if (Objects.equals(option.get("value"), fValue)) {
                                if (fValue.equals("other")) {
                                    // value为"其他"，要特殊处理，取值为fieldName + "__o"
                                    String s = Objects.toString(objectData.get(fieldName + "__o"), "");
                                    value +=  I18nUtil.get(I18nKeyEnum.MARK_MW_MWSENDMANAGER_1287) + s + ",";
                                } else {
                                    value +=  Objects.toString(option.get("label"), "") + ",";
                                }
                                break;
                            }
                        }
                    }
                    value = value.substring(0, value.length() - 1);
                }
            }
        } else if (Objects.equals(type, "department")){
            // 部门
            Object fieldValue = objectData.get(fieldName);
            if (fieldValue instanceof List) {
                List<Object> fieldValues = (List)fieldValue;
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fieldValues)){
                    String relationObjId = Objects.toString(fieldValues.get(0), ""); // 关联对象id
                    if (StringUtils.isNotEmpty(relationObjId)) {
                        // 查询人员对象详情
                        ObjectData relationObjectData = crmV2Manager.getDetail(ea, -10000, "DepartmentObj", relationObjId);
                        value = Objects.toString(relationObjectData.getName(), "");
                    }
                }
            }
        } else {
            value = formatValue(type, objectData, fieldName);
        }
        return value;
    }

    /**
     * 普通字段类型处理
     * @param type
     * @param objectData
     * @param fieldName
     * @return
     */
    private String formatValue(String type, ObjectData objectData, String fieldName){
        if (objectData == null) {
            return "";
        }
        // 值为空，直接返回
        if (objectData.get(fieldName) == null) {
            return "";
        }
        String value = "";
        if (Objects.equals(type, "date")) {
            // 日期
            if (objectData.getLong(fieldName) != null) {
                Long longValue = objectData.getLong(fieldName);
                value = DateUtil.longTimeConvertString(longValue);
            }
        } else if (Objects.equals(type, "date_time")) {
            // 日期和时间
            if (objectData.getLong(fieldName) != null) {
                Long longValue = objectData.getLong(fieldName);
                value = DateUtil.format(longValue);
            }
        } else if (Objects.equals(type, "time")){
            // 时间
            if (objectData.getLong(fieldName) != null) {
                Long longValue = objectData.getLong(fieldName);
                value = DateUtil.format5(longValue);
            }
        } else if (Objects.equals(type, "percentile")) {
            // 百分数
            Double dataDouble = objectData.getDouble(fieldName);
            if (dataDouble != null) {
                value = dataDouble.doubleValue() + "%";
            }
        } else {
            value = Objects.toString(objectData.get(fieldName), "");
        }
        return value;
    }

    /**
     * 找出模板内容中所有的${}占位符
     * @param content
     * @return
     */
    private List<String> handleVar(String content){
        List<String> keys = Lists.newArrayList();
        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);
            if (String.valueOf(c).equals("$")) {
                int start = i+1;
                StringBuilder sb = new StringBuilder();
                if (String.valueOf(content.charAt(start)).equals("{")) {
                    while(true) {
                        start++;
                        if (start == content.length()) {
                            break;
                        }
                        if (content.charAt(start) != '}') {
                            sb.append(content.charAt(start));
                        } else {
                            break;
                        }
                    }
                    keys.add(sb.toString());
                }
            }
        }
        return keys;
    }

    /**
     * 5.5营销活动过滤， 过滤指定天数发送过的用户。 仅支持
     * @param days 待过滤的天数，可选1、 3、 5、 7
     * @param phoneObjects 待过滤的手机对象，由于不同发送方法的封装手机类不同，因此这里不指定List的元素类型
     * @param channelType   发送的渠道来源，目前仅支持营销通
     * @return
     */
    private List<OneSMSDetailResult> filterPhoneObjectUsingOneSMSDetailResultType(String ea, Integer days, List<OneSMSDetailResult> phoneObjects, Integer channelType, boolean isCalcQuoto) {
        log.info("MwSendManager.filterPhoneObjectUsingOneSMSDetailResultType ea:{} days:{} phoneObjects:{} channelType:{}", ea, days, phoneObjects, channelType);
        Map<String, OneSMSDetailResult> phone2OneSMSDetailResultMap = phoneObjects.stream().filter(Objects::nonNull).collect(Collectors.toMap(OneSMSDetailResult::getMobile, Function.identity(), (k1, k2)->k1));
        List<String> phones = phoneObjects.stream().filter(Objects::nonNull).map(OneSMSDetailResult::getMobile).collect(Collectors.toList());
        List<String> validPhones;
        // 如果只是计算短信消费额，那么只过滤，不更新
        if (isCalcQuoto) {
            validPhones = enterpriseSpreadRecordManager.getValidSendList(phones, ea, MarketingActivityActionEnum.SEND_NOTE.getSpreadType(), days == null ? 0 : days);
        } else {
            validPhones = enterpriseSpreadRecordManager.filterAndUpsert(phones, MarketingActivityActionEnum.SEND_NOTE.getSpreadType(), ea, days);
        }
        List<OneSMSDetailResult> result = new LinkedList<>();
        validPhones.forEach(validPhone -> result.add(phone2OneSMSDetailResultMap.get(validPhone)));
        return result;
    }

    /**
     * 5.5营销活动过滤， 过滤指定天数发送过的用户。 仅支持
     * @param days 待过滤的天数，可选1、 3、 5、 7
     * @param phoneObjects 待过滤的手机对象，由于不同发送方法的封装手机类不同，因此这里不指定List的元素类型
     * @param channelType   发送的渠道来源，目前仅支持营销通
     * @return
     */
    private List<MobileMarketingUserResult> filterPhoneObjectUsingMobileMarketingUserResultType(String ea, Integer days, List<MobileMarketingUserResult> phoneObjects, Integer channelType) {
        log.info("MwSendManager.filterPhoneObjectUsingMobileMarketingUserResultType ea:{} days:{} phoneObjects:{} channelType:{}", ea, days, phoneObjects, channelType);
        Map<String, MobileMarketingUserResult> phone2MobileMarketingUserResultMap = phoneObjects.stream().filter(Objects::nonNull).collect(Collectors.toMap(MobileMarketingUserResult::getMobile, Function.identity(), (k1, k2)->k1));
        List<String> phones = phoneObjects.stream().filter(Objects::nonNull).map(MobileMarketingUserResult::getMobile).collect(Collectors.toList());
        List<String> validPhones = enterpriseSpreadRecordManager.filterAndUpsert(phones, MarketingActivityActionEnum.SEND_NOTE.getSpreadType(), ea, days);
        List<MobileMarketingUserResult> result = new LinkedList<>();
        validPhones.forEach(validPhone -> result.add(phone2MobileMarketingUserResultMap.get(validPhone)));
        return result;
    }

    /**
     * 5.5营销活动过滤， 过滤指定天数发送过的用户。 仅支持
     * @param days 待过滤的天数，可选1、 3、 5、 7
     * @param phoneObjects 待过滤的手机对象，由于不同发送方法的封装手机类不同，因此这里不指定List的元素类型
     * @param channelType   发送的渠道来源，目前仅支持营销通
     * @return
     */
    private List<PhoneContentResult> filterPhoneObjectUsingPhoneContentResultType(String ea, Integer days, List<PhoneContentResult> phoneObjects, Integer channelType, boolean isCalcQuoto) {
        log.info("MwSendManager.filterPhoneObjectUsingPhoneContentResultType ea:{} days:{} phoneObjects:{} channelType:{}", ea, days, phoneObjects, channelType);
        //如果非营销通的营销活动，则不支持过滤功能，设置过滤天数为null
        if (!ChannelTypeEnum.MARKETING.getType().equals(channelType)) {
            return phoneObjects;
        }
        Integer type = MarketingActivityActionEnum.SEND_NOTE.getSpreadType();
        Map<String, PhoneContentResult> phone2PhoneContentResultMap = phoneObjects.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(PhoneContentResult::getPhone, Function.identity(), (v1, v2) -> v1));
        List<String> phones = phoneObjects.stream().filter(Objects::nonNull).map(PhoneContentResult::getPhone).collect(Collectors.toList());
        //List<String> validPhones = enterpriseSpreadRecordManager.filterAndUpsert(phones, type, ea, days);
        List<String> validPhones;
        // 如果只是计算短信消费额，那么只过滤，不更新
        if (isCalcQuoto) {
            validPhones = enterpriseSpreadRecordManager.getValidSendList(phones, ea, type, days == null ? 0 : days);
        } else {
            validPhones = enterpriseSpreadRecordManager.filterAndUpsert(phones, type, ea, days);
        }
        List<PhoneContentResult> result = new LinkedList<>();
        validPhones.forEach(validPhone -> result.add(phone2PhoneContentResultMap.get(validPhone)));
        return result;
    }

    //通过选择的用户群组创建短信
    // 暂时不支持动态内容
    /*private Result<List<MwSendDetailEntity>> createSendDetailFromUserGroup(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg){
        String template = null;
        if (CollectionUtils.isEmpty(arg.getLongUrls())) {
            template = templateEntity.getContent();
        } else {
            template = arg.getTemplateContentWithMarketingActivityId();
        }
        List<String> userGroupIds = arg.getUserGroupIds();
        List<MwSendDetailEntity> detailEntityList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userGroupIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }

        List<MobileMarketingUserResult> marketingUserResults = null;
        try {
            marketingUserResults = marketingUserGroupManager.listMobileByMarketingUserGroupIds(sendEntity.getEa(), userGroupIds);
        } catch (Exception e) {
            log.error("MwSendManager createSendDetailFromUserGroup marketingUserGroupManager.listMobileByMarketingUserGroupIds, exception:", e);
            return Result.newError(SHErrorCode.SMS_USER_GROUP_EXCEPTION_ERROR);
        }
        if (CollectionUtils.isNotEmpty(marketingUserResults) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
            //过滤days天内发送过短信的手机，防止短信骚扰
            marketingUserResults = filterPhoneObjectUsingMobileMarketingUserResultType(arg.getEa(), arg.getFilterNDaySentUser(), marketingUserResults, arg.getChannelType());
            if (CollectionUtils.isEmpty(marketingUserResults)) {
                return Result.newError(SHErrorCode.EMPTY_MARKETING_ACTIVITY_SEND_USER_AFTER_FILTER);
            }
        }
        if (CollectionUtils.isEmpty(marketingUserResults)) {
            return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
        }
        int totalFee = 0;
        //实际发送人数
        int actualSenderCount = 0;
        //需要发送人数
        int toSenderCount = 0;
        SHErrorCode firstError = null;
        List<String> hasPhones = Lists.newArrayList();
        for (MobileMarketingUserResult marketingUser : marketingUserResults){
            if (StringUtils.isEmpty(marketingUser.getMobile()) || !RexUtil.rexForPhone(marketingUser.getMobile().trim())) {
                if (firstError == null) {
                    firstError = SHErrorCode.SMS_USER_GROUP_PHONE_ERROR;
                }
                continue;
            }
            if (hasPhones.contains(marketingUser.getMobile().trim())) {
                log.info("createSendDetailFromPhones duplicate phone, marketingUser: {}", marketingUser);
                continue;
            }
            toSenderCount += 1;
            MwSendDetailEntity detailEntity = new MwSendDetailEntity();
            detailEntity.setId(UUIDUtil.getUUID());
            detailEntity.setSendId(sendEntity.getId());
            detailEntity.setPhone(marketingUser.getMobile().trim());
            String detailContent = template;
            detailEntity.setContent(detailContent);

            String smsContent = buildSmsContent(signature, detailContent);

            if (smsContent.length() > SmsContants.MW_SMS_CONTENT_WORD_LIMIT) {
                detailEntity.setStatus(MwSendDetailStatusEnum.SEND_FAIL.getStatus());
                detailEntity.setErrStatus(ERROR_999999);
                if (firstError == null) {
                    firstError = SHErrorCode.SMS_WORD_LIMIT_ERROR;
                }
                continue;
            }

            int fee = quotaManager.calcSendSMSCount(smsContent.length());
            detailEntity.setFee(fee);
            totalFee += fee;
            actualSenderCount += 1;
            detailEntity.setStatus(MwSendDetailStatusEnum.NO_SEND.getStatus());
            detailEntityList.add(detailEntity);
            hasPhones.add(marketingUser.getMobile().trim());
        }
        if (CollectionUtils.isEmpty(detailEntityList)) {
            if (firstError != null) {
                return Result.newError(firstError);
            } else {
                return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
            }
        }
        sendEntity.setTotalFee(totalFee);
        sendEntity.setActualSenderCount(actualSenderCount);
        sendEntity.setToSenderCount(toSenderCount);
        return Result.newSuccess(detailEntityList);
    }*/

    /*private Result<List<MwSendDetailEntity>> createSendDetailFromConferenceEnroll(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg){
        List<String> campaignMergeDataIds = arg.getCampaignIds();
        if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }
        List<String> conferenceEnrollIds = campaignMergeDataManager.campaignIdToActivityEnrollId(campaignMergeDataIds);
        List<PhoneContentResult> phoneContentResults = conferenceManager.buildPhoneContentEnrollList(conferenceEnrollIds);
        arg.setPhones(phoneContentResults);
        return createSendDetailFromPhones(sendEntity, signature, templateEntity, arg);
    }*/

    /*private Result<List<MwSendDetailEntity>> createSendDetailFromConferenceInvite(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg, boolean isCalcQuoto){
        if (CollectionUtils.isEmpty(arg.getUserGroupIds())) {
            log.warn("MwSendManager.createSendDetailFromConferenceInvite UserGroupIds is empty arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<ActivityEntity> activityEntityByMarketingEventIdOptional = activityManager.getActivityEntityByMarketingEventId(arg.getEa(), arg.getMarketingEventId());
        if (!activityEntityByMarketingEventIdOptional.isPresent()) {
            log.warn("MwSendManager.createSendDetailFromConferenceInvite optional is empty arg:{}", arg);
            return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_NOT_FOUND);
        }
        // 会议相关参数
        ActivityEntity activityEntity = activityEntityByMarketingEventIdOptional.get();
        String activityUrl = host + "/ec/cml-marketing/release/web/cml-marketing.html?conferenceId=" + activityEntity.getId() + "&marketingEventId=" + activityEntity.getMarketingEventId() + "&byshare=1&_hash=/cml/h5/conference_detail";
        activityUrl = shortUrlManager.createShortUrl(activityUrl).orElse("");
        *//** 计算则使用预估人群手机号数量 *//*
        if (isCalcQuoto) {
            int phoneNum = marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(arg.getEa(), arg.getUserGroupIds());
            if (phoneNum == 0) {
                return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
            }
            int totalFee;
            Map<String, String> paramValueMap = activityEntity.getParamValueMap();
            paramValueMap.put("activity.url", activityUrl);
            String realContent = replaceTemplateParamByMap(templateEntity.getContent(), paramValueMap);
            String smsContent = buildSmsContent(signature, realContent);
            if (smsContent.length() > SmsContants.MW_SMS_CONTENT_WORD_LIMIT) {
                return Result.newError(SHErrorCode.SMS_WORD_LIMIT_ERROR);
            }
            totalFee = quotaManager.calcSendSMSCount(smsContent.length()) * phoneNum;
            sendEntity.setTotalFee(totalFee);
            sendEntity.setActualSenderCount(phoneNum);
            return Result.newSuccess(null);
        }
        Optional<List<PhoneContentResult>> phoneListOptional = getPhoneList(arg.getEa(), arg.getUserGroupIds());
        if (!phoneListOptional.isPresent() || CollectionUtils.isEmpty(phoneListOptional.get())) {
            log.warn("MwSendManager.createSendDetailFromConferenceInvite phoneList is empty phoneListOptional:{} arg:{}", phoneListOptional, arg);
            return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
        }
        List<PhoneContentResult> phoneContentResultList = phoneListOptional.get();
        for (PhoneContentResult element : phoneContentResultList) {
            element.setParamMap(activityEntity.getParamValueMap());
            element.getParamMap().put("activity.url", activityUrl);
        }
        arg.setPhones(phoneContentResultList);
        return createSendDetailFromPhones(sendEntity, signature, templateEntity, arg);
    }*/

    /**
     * 新模板变量参数重新组装，由{a:b,b:c}变成{a:c}
     * @param phoneContentResults
     * @param smsVarArgs
     */
    private void reBuildPhoneContentResults4New(List<PhoneContentResult> phoneContentResults, List<SmsVarArg> smsVarArgs) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(phoneContentResults) || org.apache.commons.collections4.CollectionUtils.isEmpty(smsVarArgs)) {
            log.info("phoneContentResults or smsVarArgs is empty");
            return;
        }
        for (PhoneContentResult phoneContentResult : phoneContentResults) {
            Map<String, String> paramMap = Maps.newHashMap();
            Map<String, String> phoneContentResultParamMap = phoneContentResult.getParamMap();
            for (SmsVarArg smsVarArg : smsVarArgs) {
                String value = "";
                if (Objects.equals(smsVarArg.getValueType(), "0")) {
                    // 固定值
                    value = smsVarArg.getValue();
                } else {
                    // 动态值
                    if (phoneContentResultParamMap != null && phoneContentResultParamMap.containsKey(smsVarArg.getValue())) {
                        value = phoneContentResultParamMap.get(smsVarArg.getValue());
                    }
                }
                if (smsVarArg.isUrlVar()) {
                    // 阿里云的url变量要求申请模板的时候指定域名，所以在模板中已经有了域名部分，这里只要后面那部分
                    value = value.replace(shortLinkDomain + "/", "");
                }
                paramMap.put(smsVarArg.getKey(), value);
            }
            phoneContentResult.setParamMap(paramMap);
        }
        log.info("新模板的phoneContentResults:{}", phoneContentResults);
    }

    /*private Result<List<MwSendDetailEntity>> createSendDetailFromLiveEnroll(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg){
        List<String> campaignMergeDataIds = arg.getCampaignIds();
        if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }

        List<PhoneContentResult> phoneContentResults = liveManager.buildLivePhoneContentList(campaignMergeDataIds);
        arg.setPhones(phoneContentResults);
        return createSendDetailFromPhones(sendEntity, signature, templateEntity, arg);
    }*/

    /*private Result<List<MwSendDetailEntity>> createSendDetailFromMarketingActivity(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg) {
        List<String> campaignMergeDataIds = arg.getCampaignIds();
        if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }
        List<PhoneContentResult> phoneContentResults = Lists.newArrayList();
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignMergeDataIds);
        if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
            ObjectData mktDetail = crmV2Manager.getDetail(arg.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), arg.getMarketingEventId());
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                if (StringUtils.isBlank(campaignMergeDataEntity.getPhone())) {
                    continue;
                }
                // 添加活动成员参数
                PhoneContentResult contentResult = new PhoneContentResult();
                contentResult.setPhone(campaignMergeDataEntity.getPhone());

                String campaignMembersObjId = campaignMergeDataEntity.getCampaignMembersObjId();
                Map<String, String> detail = crmV2Manager.getObjectDataEnTextVal(campaignMergeDataEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMembersObjId);
                if (detail != null) {
                    HashMap<String, String> stringVHashMap = new HashMap<>();
                    stringVHashMap.putAll(campaignMergeDataEntity.getParamValueMap());
                    detail.forEach((k,v)->{
                        if (v != null) {
                            stringVHashMap.put(k, String.valueOf(v));
                        } else {
                            stringVHashMap.put(k, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                        }
                    });

                    // 活动参数替换
                    if (mktDetail != null) {
                        stringVHashMap.put("activity.name", mktDetail.get("name") == null ? I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996) : mktDetail.get("name").toString());
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        if(mktDetail.get("begin_time") != null) {
                            stringVHashMap.put("activity.startTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("begin_time")).longValue())));
                        }
                        if(mktDetail.get("end_time") != null) {
                            stringVHashMap.put("activity.endTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("end_time")).longValue())));
                        }
                    }

                    contentResult.setParamMap(stringVHashMap);
                    phoneContentResults.add(contentResult);
                }
            }
        }
        arg.setPhones(phoneContentResults);
        return createSendDetailFromPhones(sendEntity, signature, templateEntity, arg);
    }*/

    /*private Result<List<MwSendDetailEntity>> createSendDetailFromLiveInvite(MwSmsSendEntity sendEntity, String signature, MwSmsTemplateEntity templateEntity, CreateSendTaskArg arg, boolean isCalcQuoto) {
        if (StringUtils.isEmpty(arg.getTaPath()) && CollectionUtils.isEmpty(arg.getUserGroupIds())) {
            log.warn("MwSendManager.createSendDetailFromLiveInvite taPath is null or UserGroupIds is empty arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<MarketingLiveEntity> marketingLiveEntityByMarketingEventIdOptional = liveManager.getMarketingLiveEntityByMarketingEventId(arg.getEa(), arg.getMarketingEventId());
        if (!marketingLiveEntityByMarketingEventIdOptional.isPresent()) {
            log.warn("MwSendManager.createSendDetailFromLiveInvite optional is empty arg:{}", arg);
            return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_NOT_FOUND);
        }
        MarketingLiveEntity marketingLiveEntity = marketingLiveEntityByMarketingEventIdOptional.get();
        *//** 计算则使用预估人群手机号数量 *//*
        if (isCalcQuoto) {
            int phoneNum = marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(arg.getEa(), arg.getUserGroupIds());
            if (phoneNum == 0) {
                return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
            }
            int totalFee;
            Map<String, String> paramValueMap = marketingLiveEntity.getParamValueMap();
            String realContent = replaceTemplateParamByMap(templateEntity.getContent(), paramValueMap);
            String smsContent = buildSmsContent(signature, realContent);
            if (smsContent.length() > SmsContants.MW_SMS_CONTENT_WORD_LIMIT) {
                return Result.newError(SHErrorCode.SMS_WORD_LIMIT_ERROR);
            }
            totalFee = quotaManager.calcSendSMSCount(smsContent.length()) * phoneNum;
            sendEntity.setTotalFee(totalFee);
            sendEntity.setActualSenderCount(phoneNum);
            return Result.newSuccess(null);
        }
        Optional<List<PhoneContentResult>> phoneListOptional = getPhoneList(arg.getEa(), arg.getUserGroupIds());
        if (!phoneListOptional.isPresent() || CollectionUtils.isEmpty(phoneListOptional.get())) {
            log.warn("MwSendManager.createSendDetailFromConferenceInvite phoneList is empty phoneListOptional:{} arg:{}", phoneListOptional, arg);
            return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
        }
        List<PhoneContentResult> phoneContentResultList = phoneListOptional.get();
        for (PhoneContentResult element : phoneContentResultList) {
            element.setParamMap(marketingLiveEntity.getParamValueMap());
        }
        arg.setPhones(phoneContentResultList);
        return createSendDetailFromPhones(sendEntity, signature, templateEntity, arg);
    }*/

    // 获取计算机名称
    public String getHostName(){
        if (StringUtils.isNotEmpty(hostName)) {
            return hostName;
        }
        try{
            InetAddress ia = InetAddress.getLocalHost();
            hostName = ia.getHostName();//获取计算机名字
        } catch (Exception e) {
            log.error("MwSendManager getHostName failed, exception: {}", e);
            hostName = null;
        }
        return hostName;
    }

    public String buildSendContent(String signature, String template, List<String> params){
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(params)) {
            return stringBuilder.append("【").append(signature).append("】").append(template).toString();
        } else {
            return stringBuilder.append("【").append(signature).append("】").append(replaceTemplateParamWithValue(template, params)).toString();
        }
    }

    public String buildSmsContent(String signature, String detailContent){
        if (StringUtils.isEmpty(signature)) {
            return detailContent;
        }
        StringBuilder stringBuilder = new StringBuilder();
        return stringBuilder.append("【").append(signature).append("】").append(detailContent).toString();

    }

    public String buildSmsContentBySignatureId(String signatureId, String detailContent){
        if (StringUtils.isEmpty(signatureId)) {
            return detailContent;
        }
        MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(signatureId);
        if (signatureEntity == null) {
            return detailContent;
        }
        StringBuilder stringBuilder = new StringBuilder();
        return stringBuilder.append("【").append(signatureEntity.getSvrName()).append("】").append(detailContent).toString();

    }

    public String buildSmsContentById(String signatureId, String templateId){
        if (StringUtils.isEmpty(signatureId) || StringUtils.isEmpty(templateId)) {
            return null;
        }
        MwSmsTemplateEntity templateEntity = templateDao.getTemplateById(templateId);
        MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(signatureId);
        if (templateEntity == null || signatureEntity == null) {
            return null;
        }

        return buildSmsContent(signatureEntity.getSvrName(), smsParamManager.getShowParamNameTemplate(templateEntity.getContent(), templateEntity.getParamDetail()));
    }

    public String replaceTemplateParamWithValue(String template, List<String> params) {
        String resultStr = template;
        String leftBrace = "\\{";
        String rightBrace = "\\}";
        if (CollectionUtils.isNotEmpty(params)) {
            StringBuilder paramStr;
            for (int i = 0; i < params.size(); i++){
                paramStr = new StringBuilder();
                paramStr.append(leftBrace).append(i + 1).append(rightBrace);
                resultStr = resultStr.replaceAll(paramStr.toString(), params.get(i));
            }
        }
        return resultStr;
    }

    public String replaceTemplateParamByMap(String template, Map<String, String> paramMap){
        String resultStr = template;
        String leftBrace = "\\{";
        String rightBrace = "\\}";
        if (paramMap == null || paramMap.size() == 0){
            return resultStr;
        }
        StringBuilder paramStr;
        for (String key : paramMap.keySet()) {
            if (key == null || paramMap.get(key) == null) {
                continue;
            }
            paramStr = new StringBuilder();
            paramStr.append(leftBrace).append(key).append(rightBrace);
            resultStr = resultStr.replaceAll(paramStr.toString(), paramMap.get(key));
        }
        log.info("短信发送->营销通变量替换，替换前内容：{}，替换后内容：{}", template, resultStr);
        return resultStr;
    }

    /**
     * 替换模板内容中的${}变量
     * @param template
     * @param paramMap
     * @return
     */
    public String replaceTemplateParamWith$(String template, Map<String, String> paramMap) {
        if (paramMap == null || paramMap.isEmpty()) {
            return template;
        }

        String resultStr = template;
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key == null || value == null) {
                continue;
            }
            String placeholder = "${" + key + "}";
            resultStr = resultStr.replace(placeholder, value);
        }
        log.info("短信发送->自定义变量替换，替换前内容：{}，替换后内容：{}", template, resultStr);
        return resultStr;
    }

    @Transactional
    public void deleteSend(String smsSendId){
        smsSendDao.deleteSMSSendById(smsSendId);
        smsSendDao.deleteSMSSendDetailById(smsSendId);
    }

    public boolean executeGroupSend(MwSmsSendEntity smsSendEntity) {
        if (smsSendEntity == null) {
            return false;
        }

        if (smsSendEntity.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType())) {
            if (smsSendEntity.getScheduleTime() != null && DateUtil.getTimeDifference(smsSendEntity.getScheduleTime(),
                    new Date(), TimeTypeEnum.MILLIS.getType()) < 0){
                return false;
            }
        }
        if (!smsSendDao.updateSendFlag(smsSendEntity.getId(), 1570602732000L, System.currentTimeMillis())) {
            return false;//被其他进程执行了 所以这里不执行
        }
        if (smsSendEntity.getSendTime() == null){
            smsSendDao.updateSendTimeById(smsSendEntity.getId());
        }

        // 非梦网模板，走短信平台发送逻辑
        if (!smsTemplateManager.isMwTemplate(smsSendEntity.getEa(), smsSendEntity.getTemplateId())) {
            return smsPlatformSend(smsSendEntity);
        }

        MwSmsTemplateEntity templateEntity = templateDao.getTemplateById(smsSendEntity.getTemplateId());
        if (templateEntity == null) {
            return false;
        }
        MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(smsSendEntity.getSignatureId());
        if (signatureEntity == null) {
            return false;
        }

        // 通知短信，且配置了使用fs签名，走fs签名进行发送
        if (Objects.equals(templateEntity.getTplType(), SmsTemplateTypeEnum.NOTICE.getType())
                && org.apache.commons.lang3.StringUtils.isNotBlank(useFsSmsSignatureEas)
                && Arrays.asList(useFsSmsSignatureEas.split(",")).contains(smsSendEntity.getEa())) {
            // 如果是多条发送，走fs签名
            int totalCount = smsSendDao.querySendDetailCountBySendId(smsSendEntity.getId(), MwSendDetailStatusEnum.NO_SEND.getStatus());
            if (totalCount > 1) {
                signatureEntity = signatureDao.getSignatureByEa("fs");
                log.info("企业{}走fs签名进行发送", smsSendEntity.getEa());
            } else {
                // 单条发送，如果是移动手机号，走fs签名
                List<MwSendDetailEntity> mwSendDetailEntities = smsSendDao.querySendDetailBySendId(smsSendEntity.getId(), MwSendDetailStatusEnum.NO_SEND.getStatus());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mwSendDetailEntities)) {
                    MwSendDetailEntity mwSendDetailEntity = mwSendDetailEntities.get(0);
                    if (PhoneNumberCheck.isChinaMobileNumber(mwSendDetailEntity.getPhone())) {
                        signatureEntity = signatureDao.getSignatureByEa("fs");
                        log.info("企业{}走fs签名进行发送，手机号：{}", smsSendEntity.getEa(), mwSendDetailEntity.getPhone());
                    }
                }
            }
        }

        if (templateEntity.getType().equals(MwTemplateTypeEnum.DYNAMIC_CONTENT.getType())) {
            return multiSend(smsSendEntity, signatureEntity, templateEntity);
        } else {
            return batchSend(smsSendEntity, signatureEntity, templateEntity);
        }
    }

    private boolean smsPlatformSend(MwSmsSendEntity smsSendEntity) {
        String ea = smsSendEntity.getEa();
        String templateId = smsSendEntity.getTemplateId();
        // 校验模板状态
        Result<SpRestGetSmsTemplateResult> smsTemplateResult = smsPlatformRestManager.getSmsTemplate(ea, templateId);
        if (smsTemplateResult == null || !smsTemplateResult.isSuccess() || !Objects.equals(smsTemplateResult.getData().getStatus(), SmsTemplateStatusEnum.APPROVED.getStatus())) {
            log.info("send smsTemplate not approved templateId:{}, result:{}", templateId, smsTemplateResult);
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
            smsSendDao.updateSendEntityStatus(smsSendEntity);
            doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            return false;
        }

        List<MwSendDetailEntity> detailEntityList = getSendDetailEntityByPartition(smsSendEntity);
        if (CollectionUtils.isEmpty(detailEntityList)) {
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
            smsSendDao.updateSendEntityStatus(smsSendEntity);
            doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            return false;
        }

        if (updateSendStatusToSending(smsSendEntity)) {
            Lists.partition(detailEntityList, 1000).forEach(detailEntities -> {
                for (MwSendDetailEntity detailEntity : detailEntities) {
                    String phone = detailEntity.getPhone();
                    SpRestSmsSendArg spRestSmsSendArg = new SpRestSmsSendArg();
                    spRestSmsSendArg.setEa(ea);
                    spRestSmsSendArg.setFsUserId(smsSendEntity.getCreatorUserId());
                    spRestSmsSendArg.setTemplateId(templateId);
                    spRestSmsSendArg.setChannelType(smsSendEntity.getChannelType());
                    spRestSmsSendArg.setSendNode(smsSendEntity.getSendNode());
                    spRestSmsSendArg.setReceiver(smsSendEntity.getReceiver());
                    spRestSmsSendArg.setNodeType(smsSendEntity.getNodeType());
                    spRestSmsSendArg.setBusinessType(smsSendEntity.getBusinessType());
                    spRestSmsSendArg.setObjectId(smsSendEntity.getObjectId());
                    SmsPhoneData smsPhoneData = new SmsPhoneData();
                    smsPhoneData.setPhoneList(Lists.newArrayList(phone));
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(detailEntity.getVariables())) {
                        smsPhoneData.setParamMap(GsonUtil.fromJson(detailEntity.getVariables(), Map.class));
                    }
                    Map<String, String> extraMap = Maps.newHashMap();
                    extraMap.put(phone, detailEntity.getId());
                    smsPhoneData.setExtraMap(extraMap);
                    spRestSmsSendArg.setPhoneDataList(Lists.newArrayList(smsPhoneData));
                    smsPlatformRestManager.send(ea, spRestSmsSendArg);
                }
            });
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_SUCCESSFULLY.getStatus());
            smsSendDao.updateSendEntityStatus(smsSendEntity);
            //更新CRM状态
            if (smsSendEntity.getStatus().equals(MwSendStatusEnum.SEND_SUCCESSFULLY.getStatus())) {
                doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FINISHED.getStatus(), smsSendEntity.getId());
            } else if (smsSendEntity.getStatus().equals(MwSendStatusEnum.SEND_FAIL.getStatus())) {
                doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            }
            return true;
        }

        return false;
    }

    private boolean batchSend(MwSmsSendEntity smsSendEntity, MwSmsSignatureEntity signatureEntity, MwSmsTemplateEntity templateEntity) {
        MwAccountEntity accountEntity = mwAccountDao.queryAccount(templateEntity.getSendAccountId());
        List<MwSendDetailEntity> detailEntityList = getSendDetailEntityByPartition(smsSendEntity);
        if (accountEntity == null){
            log.error("sms multiSend failed accountEntity not exist smsSendEntity:{}", smsSendEntity);
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
            smsSendDao.updateSendEntityStatus(smsSendEntity);
            doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            createSmsSendRecordObj(smsSendEntity, detailEntityList);
            return false;
        }

        if (CollectionUtils.isEmpty(detailEntityList)) {
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
            smsSendDao.updateSendEntityStatus(smsSendEntity);
            doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            return false;
        }
        List<SendRequestTask> requestTasks = partitionSendTask(detailEntityList, SmsContants.MW_SMS_BATCH_SEND_LIMIT);
        if (CollectionUtils.isEmpty(requestTasks)) {
            return false;
        }
        if (updateSendStatusToSending(smsSendEntity)) {
            String mwRequestContent = templateEntity.getContent();
            if (!SmsSceneTypeEnum.isNotificationType(templateEntity.getSceneType())) {
                String signature = "【" + signatureEntity.getSvrName() + "】";
                if (!mwRequestContent.startsWith(signature)) {
                    mwRequestContent = signature + mwRequestContent;
                }
            }
            String finalMwRequestContent = mwRequestContent;
            String custid = smsSendEntity.getEa() + "_" + smsSendEntity.getId();
            requestTasks.forEach(sendRequestTask -> {
                SendRequestResult requestResult = mwSmsManager.batchSend(signatureEntity.getSvrType(), getPhoneStr(sendRequestTask.getDetailEntityList()), finalMwRequestContent, custid, null, accountEntity.getMwUser(), accountEntity.getMwPwd(), accountEntity.getMasterIp());
                if (requestResult != null) {
                    sendRequestTask.setRequestResult(requestResult);
                } else {
                    log.info("MwSendManager batchSend requestResult is null, sendRequestTask={}", sendRequestTask);
                }
            });

            return updateSendResult(smsSendEntity, requestTasks);
        }
       return false;
    }

    // 梦网对固定内容单次群发限制1000个号码，动态内容限制100个号码
    List<SendRequestTask> partitionSendTask(List<MwSendDetailEntity> detailEntityList, int limit){
        List<SendRequestTask> requestTasks = Lists.newArrayList();
        List<List<MwSendDetailEntity>> partitions = Lists.partition(detailEntityList, limit);
        if (CollectionUtils.isNotEmpty(partitions)) {
            partitions.forEach(list -> {
                SendRequestTask requestTask = new SendRequestTask();
                requestTask.setDetailEntityList(list);
                requestTasks.add(requestTask);
            });
        }
        return requestTasks;
    }

    private boolean updateSendStatusToSending(MwSmsSendEntity smsSendEntity){
        //发送前我们先设置为发送中状态，避免重复发送
        smsSendEntity.setStatus(MwSendStatusEnum.SEND_ING.getStatus());
        try {
            return smsSendDao.updateSendEntityStatus(smsSendEntity);
        } catch (Exception e){
            log.info("MwSendManager updateSendStatusToSending updateSendEntityStatus failed, smsSendEntity:{}", smsSendEntity);
            return false;
        }
    }

    private String getPhoneStr(List<MwSendDetailEntity> detailEntityList){
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(detailEntityList)) {
            detailEntityList.forEach(detailEntity -> {
                builder.append(detailEntity.getPhone());
                builder.append(",");
            });
        }
        return builder.toString();
    }

    private boolean multiSend(MwSmsSendEntity smsSendEntity, MwSmsSignatureEntity signatureEntity, MwSmsTemplateEntity templateEntity) {
        MwAccountEntity accountEntity = mwAccountDao.queryAccount(templateEntity.getSendAccountId());
        List<MwSendDetailEntity> detailEntityList = getSendDetailEntityByPartition(smsSendEntity);
        if (accountEntity == null){
            log.error("sms multiSend failed accountEntity not exist smsSendEntity:{}", smsSendEntity);
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
            smsSendDao.updateSendEntityStatus(smsSendEntity);
            doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            createSmsSendRecordObj(smsSendEntity, detailEntityList);
            return false;
        }


        if (CollectionUtils.isEmpty(detailEntityList)) {
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
            smsSendDao.updateSendEntityStatus(smsSendEntity);
            doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            return false;
        }

        // 短信埋点，链接拼接营销活动和手机号
        if (Lists.newArrayList(ChannelTypeEnum.MARKETING.getType(), ChannelTypeEnum.MARKETING_FLOW.getType(), ChannelTypeEnum.CONFERENCE_NOTIFICATION.getType())
                .contains(smsSendEntity.getChannelType())) {
            doTrackSmsLink(smsSendEntity.getEa(), detailEntityList);
        }
        List<SendRequestTask> requestTasks = partitionSendTask(detailEntityList, SmsContants.MW_SMS_MULTI_SEND_LIMIT);
        if (CollectionUtils.isEmpty(requestTasks)) {
            return false;
        }

        if (updateSendStatusToSending(smsSendEntity)) {
            String signature = "【" + signatureEntity.getSvrName() + "】";
            requestTasks.forEach(sendRequestTask -> {
                List<MultiMtArg> multiMtList = Lists.newArrayList();
                sendRequestTask.getDetailEntityList().forEach(detailEntity -> {
                    MultiMtArg mtArg = new MultiMtArg();
                    mtArg.setMobile(detailEntity.getPhone());
                    String content = detailEntity.getContent();
                    if (!SmsSceneTypeEnum.isNotificationType(templateEntity.getSceneType()) && !content.startsWith(signature)) {
                        content = signature + content;
                    }
                    mtArg.setContent(content);
                    mtArg.setCustid(smsSendEntity.getEa() + "_" + smsSendEntity.getId());
                    mtArg.setSvrtype(signatureEntity.getSvrType());
                    mtArg.setExdata(detailEntity.getId());
                    multiMtList.add(mtArg);
                });
                SendRequestResult requestResult = mwSmsManager.multiSend(multiMtList, accountEntity.getMwUser(), accountEntity.getMwPwd(), accountEntity.getMasterIp());
                if (requestResult != null) {
                    sendRequestTask.setRequestResult(requestResult);
                } else {
                    log.info("MwSendManager batchSend requestResult is null, sendRequestTask={}", sendRequestTask);
                }
            });

            return updateSendResult(smsSendEntity, requestTasks);
        }

        return false;
    }

    private List<MwSendDetailEntity> getSendDetailEntityByPartition(MwSmsSendEntity smsSendEntity){
        List<MwSendDetailEntity> sendDetailEntityList = Lists.newArrayList();
        int totalCount = smsSendDao.querySendDetailCountBySendId(smsSendEntity.getId(), MwSendDetailStatusEnum.NO_SEND.getStatus());
        if (totalCount == 0){
            return sendDetailEntityList;
        }

        int pageSize = 10000;
        int totalPageCount = totalCount / pageSize;
        if (totalCount % pageSize != 0){
            totalPageCount++;
        }
        log.info("getSendDetailEntityByPartition ea:{} sendId:{} totalCount:{} totalPage:{}", smsSendEntity.getEa(), smsSendEntity.getId(), totalCount, totalPageCount);
        for (int i = 0; i < totalPageCount; i++) {
            int currentPage = i + 1;
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(currentPage, pageSize);
            List<MwSendDetailEntity> currentPageData = smsSendDao.pageQuerySendDetailBySendId(smsSendEntity.getId(), MwSendDetailStatusEnum.NO_SEND.getStatus(), page);
            if (CollectionUtils.isNotEmpty(currentPageData)){
                sendDetailEntityList.addAll(currentPageData);
            }
        }

        return sendDetailEntityList;
    }

    private void doTrackSmsLink(String ea, List<MwSendDetailEntity> detailEntityList) {
        Map<String, String> shortUrl2LongUrlMap = shortUrlManager.getShortUrl2LongUrlMapV2(detailEntityList.get(0).getContent());
        log.info("doTrackSmsLink ea: {} content: {} shortUrl2LongUrlMap: {}", ea, detailEntityList.get(0).getContent(), shortUrl2LongUrlMap);
        if (MapUtils.isEmpty(shortUrl2LongUrlMap)) {
            return ;
        }
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE.getType(), detailEntityList.get(0).getSendId());
        String marketingActivityId = "";
        if (marketingActivityExternalConfig != null) {
            marketingActivityId = marketingActivityExternalConfig.getMarketingActivityId();
        }
        this.batchDoTrackContent(ea, marketingActivityId, shortUrl2LongUrlMap, detailEntityList);
    }

    /**
     * shortUrl2LongUrlMap:
     * newLongUrl2ShortUrl:
     * newLongUrl2NewShortUrl:
     *
     * @param ea
     * @param marketingActivityId
     * @param shortUrl2LongUrlMap
     * @param detailEntityList
     */
    private void batchDoTrackContent(String ea, String marketingActivityId, Map<String, String> shortUrl2LongUrlMap, List<MwSendDetailEntity> detailEntityList) {
        Map<String, String> newLongUrl2ShortUrl = new HashMap<>();
        for (MwSendDetailEntity entity : detailEntityList) {
            String originContent = entity.getContent();
            String newContent = doTrackContent(marketingActivityId, entity.getPhone(), shortUrl2LongUrlMap, originContent, ea, newLongUrl2ShortUrl, entity.getSendId());
            log.info("替换短信链接,ea: {} marketingActivityId: {} originContent: {} newContent: {}", ea, marketingActivityId, originContent, newContent);
            entity.setContent(newContent);
        }
        Map<String, String> newLongUrl2NewShortUrl = new HashMap<>();
        Set<String> newLongUrlList = newLongUrl2ShortUrl.keySet();
        // 太多了会报错 先降低 宁愿慢点
        List<List<String>> partition = Lists.partition(new ArrayList<>(newLongUrlList), 300);
        for (List<String> newLongUrls : partition) {
            BatchCreateShortUrlsArg arg = new BatchCreateShortUrlsArg();
            arg.setUrls(newLongUrls);
            //BatchShortUrlResult batchCreateShortUrl = shortUrlManager.batchCreateShortUrl(arg);
            BatchShortUrlResult batchCreateShortUrl = shortUrlManager.batchCreateShortUrlV2(arg);
            if (batchCreateShortUrl != null && batchCreateShortUrl.getShortUrlMapping() != null) {
                //newLongUrl2NewShortUrl = batchCreateShortUrl.getShortUrlMapping();
                newLongUrl2NewShortUrl.putAll(batchCreateShortUrl.getShortUrlMapping());
            }
        }
        for (MwSendDetailEntity entity : detailEntityList) {
            String newContent = entity.getContent();
            log.info("替换短信链接成新短链,ea: {} marketingActivityId: {}, content: {}", ea, marketingActivityId, newContent);
            for (Map.Entry<String, String> newLongUrl2NewShortUrlEntry : newLongUrl2NewShortUrl.entrySet()) {
                String newLongUrl = newLongUrl2NewShortUrlEntry.getKey();
                String newShortUrl = newLongUrl2NewShortUrlEntry.getValue();
                String shortUrl = newLongUrl2ShortUrl.get(newLongUrl);
                if (StringUtils.isNotEmpty(newShortUrl) && newLongUrl.contains(entity.getPhone())) {
                    newContent = newContent.replace(shortUrl, newShortUrl);
                }
//                log.info("替换短信链接成新短链,ea: {} marketingActivityId: {}, newLongUrl: {} newShortUrl: {} shortUrl: {} newContent: {}", ea, marketingActivityId, newLongUrl, newShortUrl, shortUrl, newContent);
            }
            log.info("替换短信链接成新短链,ea: {} marketingActivityId: {}, newContent: {}", ea, marketingActivityId, newContent);
            entity.setContent(newContent);
        }
    }

    private String doTrackContent(String marketingActivityId, String phone, Map<String, String> shortUrl2LongUrlMap,
                                  String content, String ea, Map<String, String> newLongUrl2ShortUrl, String sendId) {
        String result = content;
        StringBuilder builder = new StringBuilder();
        Map<String, String> wxUrlMap = new HashMap<>();
        HashSet<String> longUrls = new HashSet<>();
        Map<String, String> fsUrlMap = new HashMap<>();
        String marketingEventId = null;
        MwSmsSendMarketingEventRelationEntity relationEntity = mwSmsSendMarketingEventRelationDao.getEntityBySendId(ea, sendId);
        if (relationEntity != null) {
            marketingEventId = relationEntity.getMarketingEventId();
        }
        for (Map.Entry<String, String> shortUrl2LongUrlEntry : shortUrl2LongUrlMap.entrySet()) {
            builder.delete(0, builder.length());
            String shortUrl = shortUrl2LongUrlEntry.getKey();
            String longUrl = shortUrl2LongUrlEntry.getValue();
            String newLongUrl;
            if (StringUtils.isNotEmpty(longUrl) && longUrl.contains("?")) {
                newLongUrl = builder.append(longUrl).append("&marketingActivityId=").append(marketingActivityId).append("&phone=").append(phone).toString();
            } else {
                newLongUrl = builder.append(longUrl).append("?marketingActivityId=").append(marketingActivityId).append("&phone=").append(phone).toString();
            }
            if (StringUtils.isNotEmpty(marketingEventId)) {
                newLongUrl += "&marketingEventId=" + marketingEventId;
            }
            newLongUrl += "&sceneType=" + MarketingUserActionSceneType.SMS.getSceneType() + "&sceneId=" + sendId;
            if (shortUrl.startsWith(shortLinkDomain) || shortUrl.startsWith(shortLinkDomain.replace("https://", "").replace("http://", ""))) {
                newLongUrl2ShortUrl.put(newLongUrl, shortUrl);
            }
            else if (shortUrl.startsWith("https://wxaurl.cn")) {
                wxUrlMap.put(shortUrl, newLongUrl);
                longUrls.add(newLongUrl);
            }
        }
        if (!wxUrlMap.isEmpty()) {
            String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
            String accessToken = wechatAccountManager.getAccessTokenByWxAppId(wxAppId);
            Map<String, String> generatedUrlMap = shortUrlManager.batchGetGeneratedWxAppUrl(longUrls);
            for (Entry<String, String> entry : wxUrlMap.entrySet()) {
                String longUrl = entry.getValue();
                String newShortUrl = generatedUrlMap.get(longUrl);
                if (StringUtils.isEmpty(newShortUrl)) {
                    String[] urlArray = longUrl.split("[?]");
                    QueryMiniAppForwardUrlArg arg = new QueryMiniAppForwardUrlArg();
                    arg.setPath(urlArray[0]);
                    arg.setQuery(urlArray.length > 1 ? urlArray[1] : "");
                    arg.setIsExpire(true);
                    arg.setExpireType(1);
                    arg.setExpireInterval(31);
                    newShortUrl = customizeMiniAuthorizeManager.createMiniAppForwardUrl(arg, accessToken).getUrlLink();
                    generatedUrlMap.put(longUrl, newShortUrl);
                }
                result = result.replace(entry.getKey(), newShortUrl);
            }
        }
        return result;
    }

    private boolean updateSendResult(MwSmsSendEntity smsSendEntity, List<SendRequestTask> requestTasks) {
        if (CollectionUtils.isEmpty(requestTasks)) {
            log.info("MwSendManager updateSendResult requestTasks is null");
            return false;
        }

        int successCnt = 0;
        int failCnt = 0;
        int rebackFee = 0;
        List<MwSendDetailEntity> detailEntityList = Lists.newArrayList();
        for (SendRequestTask  requestTask : requestTasks) {
            if (requestTask.getRequestResult() == null) {
                if (CollectionUtils.isNotEmpty(requestTask.getDetailEntityList())) {
                    for (MwSendDetailEntity detailEntity : requestTask.getDetailEntityList()) {
                        failCnt++;
                        detailEntity.setStatus(MwSendDetailStatusEnum.SEND_FAIL.getStatus());
                        detailEntity.setErrStatus(MwSmsManager.ERROR_310099);
                        rebackFee += detailEntity.getFee();
                        detailEntityList.add(detailEntity);
                    }
                }
                continue;
            }

            if (requestTask.getRequestResult().getResult() == 0){
                smsSendEntity.setResultCode(requestTask.getRequestResult().getResult());
                smsSendEntity.setMsgid(requestTask.getRequestResult().getMsgid());
                smsSendEntity.setCustid(requestTask.getRequestResult().getCustid());

                if (CollectionUtils.isNotEmpty(requestTask.getDetailEntityList())) {
                    for (MwSendDetailEntity detailEntity : requestTask.getDetailEntityList()) {
                        successCnt++;
                        detailEntity.setStatus(MwSendDetailStatusEnum.SEND_SUCCESSFUL.getStatus());
                        detailEntityList.add(detailEntity);
                    }
                }
            } else {
                failCnt++;
                smsSendEntity.setResultCode(requestTask.getRequestResult().getResult());
                smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                if (CollectionUtils.isNotEmpty(requestTask.getDetailEntityList())) {
                    for (MwSendDetailEntity detailEntity : requestTask.getDetailEntityList()) {
                        failCnt++;
                        detailEntity.setStatus(MwSendDetailStatusEnum.SEND_FAIL.getStatus());
                        detailEntity.setErrStatus(requestTask.getRequestResult().getResult());
                        rebackFee += detailEntity.getFee();
                        detailEntityList.add(detailEntity);
                    }
                }

            }
        }
        if (successCnt > 0 && failCnt > 0) {
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_SUCCESSFULLY.getStatus());
        } else if (successCnt > 0) {
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_SUCCESSFULLY.getStatus());
        } else {
            smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
        }
        if (failCnt > 0) {
            smsSendEntity.setActualSenderCount(smsSendEntity.getActualSenderCount() - failCnt);
        }

        if (rebackFee > 0) {
            smsSendEntity.setTotalFee(smsSendEntity.getTotalFee() - rebackFee);
        }

        try {
            smsSendDao.updateSendResult(smsSendEntity);
            //更新CRM状态
            if (smsSendEntity.getStatus().equals(MwSendStatusEnum.SEND_SUCCESSFULLY.getStatus())) {
                doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FINISHED.getStatus(), smsSendEntity.getId());
            }
            else if (smsSendEntity.getStatus().equals(MwSendStatusEnum.SEND_FAIL.getStatus())) {
                doSendGroupSmsStatusMq(smsSendEntity.getEa(), SendStatusEnum.FAIL.getStatus(), smsSendEntity.getId());
            }
            // 根据签名的企业ea退回配额
            MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(smsSendEntity.getSignatureId());
            quotaDAO.increaseQuotaLeftCount(signatureEntity.getEa(), rebackFee);
            detailEntityList.forEach(sendDetailEntity -> {
                smsSendDao.updateSendDetailStatus(sendDetailEntity, MwSendDetailStatusEnum.NO_SEND.getStatus());
                // 收到短信的动态放在定时任务里面处理
//                if(MwSendDetailStatusEnum.SEND_SUCCESSFUL.getStatus().equals(sendDetailEntity.getStatus())){
//                    mwSmsManager.generateSmsReceivedMQ(sendDetailEntity.getSendId(), sendDetailEntity.getPhone());
//                }
            });
            createSmsSendRecordObj(smsSendEntity, detailEntityList);

        } catch (Exception e) {
            log.info("MwSendManager updateSendResult db exception: {}", e.toString());
            return false;
        }
        if (smsSendEntity.getStatus().equals(MwSendStatusEnum.SEND_FAIL.getStatus())){
            return false;
        }
        return true;
    }

    // 发送的时候需要扣减费用
    @Transactional
    public boolean reduceQuotaAndSend(MwSmsSendEntity smsSendEntity, boolean isVerifyCodeSms) {
        try {
            //先从当前企业配额中扣除
            boolean reduceResult = quotaManager.reduceQuotaLeftCount(smsSendEntity.getEa(), smsSendEntity.getTotalFee());
            if (!reduceResult) {
                log.warn("MwSendManager.reduceQuotaAndSend from send ea, the left is not enough, reduce error. smsSendEntity:{}", smsSendEntity);
                if (isVerifyCodeSms) {
                    // 验证码短信，支持使用纷享签名的企业发送验证码
                    MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(smsSendEntity.getSignatureId());
                    reduceResult = quotaManager.reduceQuotaLeftCount(signatureEntity.getEa(), smsSendEntity.getTotalFee());
                }
            }
            if (reduceResult) {
                if (smsSendEntity.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType())) {
                    smsSendEntity.setStatus(MwSendStatusEnum.SCHEDULE_SENDING.getStatus());
                } else {
                    smsSendEntity.setStatus(MwSendStatusEnum.READY.getStatus());
                }
                smsSendDao.updateSendEntityStatus(smsSendEntity);
                return true;
            } else {
                log.warn("MwSendManager.reduceQuotaAndSend the left is not enough, reduce error. smsSendEntity:{}", smsSendEntity);
                smsSendEntity.setStatus(MwSendStatusEnum.SEND_FAIL.getStatus());
                smsSendDao.updateSendEntityStatus(smsSendEntity);
            }
        } catch (Exception e) {
            log.info("MwSendManager reduceQuotaWhenSend failed, smsSendEntity : {},exception : {}", smsSendEntity, e);
            return false;
        }
        return false;
    }

    public boolean getRpt(int limit){
        List<MwAccountEntity> accountEntityList = mwAccountDao.queryAccountByStatus(0);
        if (CollectionUtils.isEmpty(accountEntityList)) {
            return false;
        }
        for (MwAccountEntity accountEntity : accountEntityList) {
            try {
                ReportResults reportResults = mwSmsManager.getRpt(limit, accountEntity.getMwUser(), accountEntity.getMwPwd(), accountEntity.getMasterIp());
                if (reportResults == null) {
                    continue;
                }
                if (reportResults.getResult() != 0){
                    log.error("pull mw sms report failed", reportResults);
                    continue;
                }
                if (CollectionUtils.isEmpty(reportResults.getRpts())){
                    continue;
                }

                Set<String> foneshareSmsEaSet = new HashSet<>();
                List<ReportResult> foneshareSmsReportList = Lists.newArrayList();
                Map<String, List<ReportResult>> cloudEaReportMap = new HashMap<>();
                for (ReportResult reportResult : reportResults.getRpts()){
                    if (StringUtils.isEmpty(reportResult.getCustid()) || StringUtils.isEmpty(reportResult.getMobile())){
                        continue;
                    }
                    if (!reportResult.getCustid().contains("_")){
                        foneshareSmsReportList.add(reportResult);
                        continue;
                    }
                    String[] regs = reportResult.getCustid().split("_");
                    String ea = regs[0];
                    String custid = regs[1];
                    if (foneshareSmsEaSet.contains(ea)){
                        foneshareSmsReportList.add(reportResult);
                    }else if (cloudEaReportMap.keySet() != null && cloudEaReportMap.keySet().contains(ea)){
                        cloudEaReportMap.get(ea).add(reportResult);
                    }else {
                        MwSmsSendEntity smsSendEntity = smsSendDao.getSMSSendById(custid);
                        if (smsSendEntity != null){
                            foneshareSmsReportList.add(reportResult);
                            foneshareSmsEaSet.add(ea);
                        }else {
                            if (cloudEaReportMap.get(ea) == null){
                                cloudEaReportMap.put(ea, Lists.newArrayList());
                            }
                            cloudEaReportMap.get(ea).add(reportResult);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(foneshareSmsReportList)){
                    foneshareSmsReportList.forEach(reportResult -> {
                        if (reportResult.getCustid().contains("_")) {
                            String[] regs = reportResult.getCustid().split("_");
                            reportResult.setCustid(regs[1]);
                        }
                    });
                    handleLocalSmsReport(foneshareSmsReportList);
                }
                if (MapUtils.isNotEmpty(cloudEaReportMap)){
                    for (Map.Entry<String, List<ReportResult>> entry : cloudEaReportMap.entrySet()){
                        entry.getValue().forEach(reportResult -> {
                            if (reportResult.getCustid().contains("_")) {
                                String[] regs = reportResult.getCustid().split("_");
                                reportResult.setCustid(regs[1]);
                            }
                        });
                    }
                    sendCloudSmsReport(cloudEaReportMap);
                }
            } catch (Exception e) {
                log.info("MwSendManager getRpt exception, accountEntity : {},exception : {}", accountEntity, e.fillInStackTrace());
            }
        }

        return true;
    }

    private void sendCloudSmsReport(Map<String, List<ReportResult>> cloudEaReportMap){
        String cloudUrl = centerHost + "/marketing/wxThirdCloudInner/handleCloudSmsReportData";
        Map<String, String> header = new HashMap<>();
        for (Map.Entry<String, List<ReportResult>> entry : cloudEaReportMap.entrySet()){
            String ea = entry.getKey();
            List<ReportResult> reportResults = entry.getValue();
            CloudSmsReportArg arg = new CloudSmsReportArg();
            arg.setReportResultList(reportResults);
            //跳转对应云
            try {
                Integer ei = eieaConverter.enterpriseAccountToId(ea);
                if (ei == null) {
                    log.info("sendCloudSmsReport ea to ei return null ea:{}", ea);
                    continue;
                }
                header.put("x-fs-ei", String.valueOf(ei));
                RequestBody requestBody = RequestBody.create(null, GsonUtil.toJson(arg));
                httpManager.executePostByOkHttpClientWithRequestBodyAndHeader(requestBody, cloudUrl, new TypeToken<Result>() {
                }, header);
            }catch (Exception e){
                log.info("sendCloudSmsReport send cloud exception cloudEaReportMap:{} e:", cloudEaReportMap, e);
            }
        }
    }
    public void handleLocalSmsReport(List<ReportResult> reportResults){
        if (CollectionUtils.isEmpty(reportResults)){
            return;
        }

        Map<String, MwSmsSendEntity> smsSendEntityMap = Maps.newHashMap();
        Map<String, MwSendDetailEntity> detailEntityMap = Maps.newHashMap();
        Map<String, List<ReportResult>> reportMap = Maps.newHashMap();
        reportResults.forEach(reportResult -> {
            if (StringUtils.isNotEmpty(reportResult.getCustid()) && StringUtils.isNotEmpty(reportResult.getMobile())) {
                MwSmsSendEntity smsSendEntity = smsSendEntityMap.get(reportResult.getCustid());
                if (smsSendEntity == null) {
                    smsSendEntity = smsSendDao.getSMSSendById(reportResult.getCustid());// 这里的自定义custid 就是发送的时候填进去的sendId
                    if (smsSendEntity != null) {
                        smsSendEntityMap.put(smsSendEntity.getId(), smsSendEntity);
                    }
                }
                if (smsSendEntity != null) {
                    MwSendDetailEntity detailEntity = smsSendDao.querySendDetailBySendIdAndPhone(smsSendEntity.getId(), reportResult.getMobile());
                    if (detailEntity != null) {
                        detailEntityMap.put(detailEntity.getId(), detailEntity);
                        if (reportMap.containsKey(detailEntity.getId())) {
                            reportMap.get(detailEntity.getId()).add(reportResult);
                        } else {
                            List<ReportResult> results = Lists.newArrayList();
                            results.add(reportResult);
                            reportMap.put(detailEntity.getId(), results);
                        }
                    }
                }
            }
        });

        computeRptResult(smsSendEntityMap, detailEntityMap, reportMap);
    }

    //计算返回结果
    private void computeRptResult(Map<String, MwSmsSendEntity> smsSendEntityMap, Map<String, MwSendDetailEntity> detailEntityMap, Map<String, List<ReportResult>> reportMap) {
        detailEntityMap.values().forEach(sendDetailEntity -> {
            computeDetailRptResult(sendDetailEntity, reportMap.get(sendDetailEntity.getId()));
            if (sendDetailEntity.getStatus().equals(MwSendDetailStatusEnum.SEND_FAIL.getStatus())) {
                MwSmsSendEntity sendEntity = smsSendEntityMap.get(sendDetailEntity.getSendId());
                if (sendEntity != null && sendDetailEntity.getFailCount() != null && sendDetailEntity.getFailCount() > 0) {
                    //sendEntity.setTotalFee(sendEntity.getTotalFee() - sendDetailEntity.getFee());
                    sendEntity.setRebackFee(sendEntity.getRebackFee() + sendDetailEntity.getFee());
                    //sendEntity.setActualSenderCount(sendEntity.getActualSenderCount() - 1);
                    sendEntity.setSenderReduce(sendEntity.getSenderReduce() + 1);
                }
            }
        });
        Map<String, List<MwSendDetailEntity>> eaToErrorSendDetailMap = new HashMap<>();
        detailEntityMap.values().forEach(sendDetailEntity -> {
            try {
                if (sendDetailEntity.getFailCount() != null) {
                    smsSendDao.updateSendDetailStatus(sendDetailEntity, null);
                    if(MwSendDetailStatusEnum.SEND_SUCCESSFUL.getStatus().equals(sendDetailEntity.getStatus())){
                        mwSmsManager.generateSmsReceivedMQ(sendDetailEntity.getSendId(), sendDetailEntity.getPhone());
                    } else {
                        MwSmsSendEntity mwSmsSendEntity = smsSendEntityMap.get(sendDetailEntity.getSendId());
                        if (mwSmsSendEntity != null) {
                            List<MwSendDetailEntity> list = eaToErrorSendDetailMap.computeIfAbsent(mwSmsSendEntity.getEa(), k -> Lists.newArrayList());
                            list.add(sendDetailEntity);
                        }
                    }
                }
            } catch (Exception e) {
                log.info("MwSendManager computeRptResult updateSendDetailStatus exception: {}", e.toString());
            }
        });

        smsSendEntityMap.values().forEach(smsSendEntity -> {
            try {
                smsSendDao.updateSendResultByPullRpt(smsSendEntity);
                // 根据签名的企业ea退回配额
                MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(smsSendEntity.getSignatureId());
                quotaDAO.increaseQuotaLeftCount(signatureEntity.getEa(), smsSendEntity.getRebackFee());
            } catch (Exception e) {
                log.info("MwSendManager computeRptResult updateSendResult or increaseQuotaLeftCount exception: {}", e.toString());
            }
        });
        // 发送失败，清理掉营销短信发送记录
        try {
            if (MapUtils.isNotEmpty(eaToErrorSendDetailMap)) {
                eaToErrorSendDetailMap.forEach((ea, mwSendDetailEntities) -> {
                    // 分批处理
                    Lists.partition(mwSendDetailEntities, 2000).forEach(subEntities -> {
                        List<String> phones = subEntities.stream().map(MwSendDetailEntity::getPhone).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(phones)) {
                            enterpriseSpreadRecordManager.deleteByEaAndPhones(ea, MarketingActivityActionEnum.SEND_NOTE.getSpreadType(), phones);
                        }
                    });
                });
            }
        } catch (Exception e) {
            log.info("清理掉营销短信发送记录异常");
        }
        sendSmsSendErrorToMQ(eaToErrorSendDetailMap);
    }

    // 将发送失败的短信写入到延迟队列中，将短信发送记录对象的状态更新成失败
    public void sendSmsSendErrorToMQ(Map<String, List<MwSendDetailEntity>> eaToErrorSendDetailMap) {
        if (MapUtils.isEmpty(eaToErrorSendDetailMap)) {
            return;
        }
        eaToErrorSendDetailMap.forEach((k, v) -> {
            MwSendDetailUpdateMqArg mwSendDetailUpdateMqArg = new MwSendDetailUpdateMqArg();
            mwSendDetailUpdateMqArg.setEa(k);
            mwSendDetailUpdateMqArg.setSendDetailEntityList(v);
            delayQueueSender.sendByObj(k, mwSendDetailUpdateMqArg, DelayQueueTagConstants.SMS_RECORD_UPDATE, RocketMqDelayLevelConstants.TEN_MINUTE);
        });
    }

    public void updateSmsSendRecordObjStatusToFail(MwSendDetailUpdateMqArg mwSendDetailUpdateMqArg) {
        String ea = mwSendDetailUpdateMqArg.getEa();
        if (CollectionUtils.isEmpty(mwSendDetailUpdateMqArg.getSendDetailEntityList())){
            return;
        }
        MwSmsSendEntity mwSmsSendEntity = smsSendDao.getSMSSendById(mwSendDetailUpdateMqArg.getSendDetailEntityList().get(0).getSendId());
        if (mwSmsSendEntity == null) {
            return;
        }
        MwSmsSignatureEntity mwSmsSignatureEntity = signatureDao.getSignatureById(mwSmsSendEntity.getSignatureId());
        if (mwSmsSignatureEntity == null || !StringUtils.isEquals(mwSmsSignatureEntity.getEa(), ea)) {
            return;
        }
        try {
            List<MwSendDetailEntity>  errorSendDetailList = mwSendDetailUpdateMqArg.getSendDetailEntityList();
            List<String> sendDetailIdList = errorSendDetailList.stream().map(MwSendDetailEntity::getId).collect(Collectors.toList());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, sendDetailIdList.size());
            paasQueryArg.addFilter("send_detail_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), sendDetailIdList);
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.SMS_SEND_RECORD_OBJ.getName());
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, SuperUserConstants.USER_ID, findByQueryV3Arg);
            if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getDataList())) {
                List<ObjectData> objectDataList = pageResult.getDataList();
                Map<String, MwSendDetailEntity> mwSendDetailEntityMap = errorSendDetailList.stream().collect(Collectors.toMap(MwSendDetailEntity::getId, e -> e));
                for (ObjectData objectData : objectDataList) {
                    String sendDetailId = objectData.getString("send_detail_id");
                    if (StringUtils.isBlank(sendDetailId)) {
                        continue;
                    }
                    String sendStatus = objectData.getString("send_status");
                    if (StringUtils.isNotEmpty(sendStatus) && sendStatus.equals(SmsRecordSendStatusEnum.FAILURE.getCode())) {
                        continue;
                    }
                    MwSendDetailEntity mwSendDetailEntity = mwSendDetailEntityMap.get(sendDetailId);
                    String errorCode = mwSendDetailEntity.getErrCode();
                    String failureReason = SmsRecordFailureReasonEnum.getByErrorCode(errorCode).getReason();
                    ObjectData updateData = new ObjectData();
                    updateData.put("send_status", SmsRecordSendStatusEnum.FAILURE.getCode());
                    updateData.put("failure_reason", failureReason);
                    updateData.put("error_code", mwSendDetailEntity.getErrCode());
                    updateData.put("_id", objectData.getId());
                    ActionEditArg arg = new ActionEditArg();
                    arg.setObjectData(updateData);
                    int tenantId = eIEAConverter.enterpriseAccountToId(ea);
                    metadataActionService.edit(new HeaderObj(tenantId, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.SMS_SEND_RECORD_OBJ.getName(), true, true, arg);
                }
                log.info("sendDetailIdList.size {}, smsSendRecordSize: {}", errorSendDetailList.size(), objectDataList.size());
            }
        } catch (Exception e) {
            log.error("更新短信记录对象错误, ea:[{}]", ea, e);
        }
    }
    private void createSmsSendRecordObj(MwSmsSendEntity smsSendEntity, List<MwSendDetailEntity> detailEntityList) {
        if (CollectionUtils.isEmpty(detailEntityList)) {
            return;
        }
        //其他企业使用纷享签名发送短信，不写入短信记录对象
        MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(smsSendEntity.getSignatureId());
        if (!StringUtils.isEquals(signatureEntity.getEa(), smsSendEntity.getEa())) {
            return;
        }

        /**SOP短信发送记录使用到*/
        MwSmsSendMarketingEventRelationEntity eventRelationEntity = mwSmsSendMarketingEventRelationDao.getEntityBySendId(smsSendEntity.getEa(), smsSendEntity.getId());
        String marketingEventId = eventRelationEntity != null ? eventRelationEntity.getMarketingEventId() : null;

        MwSmsTemplateEntity templateEntities = null;
        if (StringUtils.isNotEmpty(smsSendEntity.getTemplateId())) {
            templateEntities = templateDao.getTemplateById(smsSendEntity.getTemplateId());
        }
        MwSmsTemplateEntity finalTemplateEntities = templateEntities;
        detailEntityList.forEach(sendDetailEntity -> {
            try {
                SmsRecordObjCreateArg smsRecordObjCreateArg = new SmsRecordObjCreateArg();
                smsRecordObjCreateArg.setEa(smsSendEntity.getEa());
                smsRecordObjCreateArg.setUserId(smsSendEntity.getCreatorUserId());
                smsRecordObjCreateArg.setPhone(sendDetailEntity.getPhone());
                smsRecordObjCreateArg.setSmsContent(sendDetailEntity.getContent());
                smsRecordObjCreateArg.setReceiver(smsSendEntity.getReceiver());
                smsRecordObjCreateArg.setSender(SmsRecordSenderEnum.getBySmsChanelType(smsSendEntity.getChannelType()).getSender());
                smsRecordObjCreateArg.setBusinessType(smsSendEntity.getBusinessType());
                if (StringUtils.isNotEmpty(smsSendEntity.getNodeType())) {
                    smsRecordObjCreateArg.setNodeType(SmsRecordNodeTypeEnum.getByNodeType(smsSendEntity.getNodeType()).getNodeType());
                }
                smsRecordObjCreateArg.setSendDetailId(sendDetailEntity.getId());
                smsRecordObjCreateArg.setSendNode(smsSendEntity.getSendNode());
                SmsRecordSendStatusEnum statusEnum = SmsRecordSendStatusEnum.getBySmsStatus(sendDetailEntity.getStatus());
                if (statusEnum == SmsRecordSendStatusEnum.FAILURE) {
                    smsRecordObjCreateArg.setErrorCode(sendDetailEntity.getErrCode());
                    smsRecordObjCreateArg.setFailureReason(SmsRecordFailureReasonEnum.getByErrorCode(sendDetailEntity.getErrCode()).getReason());
                }
                smsRecordObjCreateArg.setSendTime(System.currentTimeMillis());
                smsRecordObjCreateArg.setSendStatus(statusEnum.getCode());
                if (finalTemplateEntities != null ) {
                    smsRecordObjCreateArg.setSmsTemplate(finalTemplateEntities.getName());
                }
                smsRecordObjCreateArg.setMarketingEventId(marketingEventId);
                smsSendRecordObjManager.createObj(smsRecordObjCreateArg);

            } catch (Exception e) {
                log.info("createSmsSendRecordObj, sendDetail:[{}]", sendDetailEntity, e);
            }
        });
    }

    //计算返回结果
    private void computeDetailRptResult(MwSendDetailEntity detailEntity, List<ReportResult> reportResults){
        if (CollectionUtils.isEmpty(reportResults)) {
            return;
        }
        int failCount = 0;
        boolean isSuccess = false;
        ReportResult reportSuccess = reportResults.get(0);
        for (ReportResult reportResult : reportResults){ //当长短信被分割成多条短信时候，若其中一条成功，则认为成功，所以都失败才算失败
            if (reportResult.getStatus() != 0) {
                failCount++;
            } else {
                if (isSuccess) {
                    continue;
                }
                reportSuccess = reportResult;
                isSuccess = true;
            }
        }

        if (smsSendDao.updateSendDetailErrStatus(detailEntity.getId(), reportSuccess.getStatus())) { //防止启动多个服务时同时更新
            if (isSuccess) {
                detailEntity.setStatus(MwSendDetailStatusEnum.SEND_SUCCESSFUL.getStatus());
                detailEntity.setFailCount(0);
            } else {
                detailEntity.setStatus(MwSendDetailStatusEnum.SEND_FAIL.getStatus());
                detailEntity.setFailCount(failCount);
            }
            detailEntity.setErrStatus(reportSuccess.getStatus());
            detailEntity.setErrCode(reportSuccess.getErrcode());
            detailEntity.setErrDesc(reportSuccess.getErrdesc());
            detailEntity.setExdata(reportSuccess.getExdata());
        }
    }

    public MwSmsSignatureEntity getSignatureOrTrial(String ea, String signatureId, Integer channelType) {
        if (StringUtils.isEmpty(ea) && channelType == null && StringUtils.isEmpty(signatureId)) {
            return null;
        }
        MwSmsSignatureEntity signatureEntity;
        if (StringUtils.isNotEmpty(signatureId)) {
            signatureEntity = signatureDao.getSignatureById(signatureId);
        } else {
            signatureEntity = getSmsSignature(ea, channelType);
            if (signatureEntity == null || !signatureEntity.getStatus().equals(ApplySignatureStatusEnum.APPLY_PASS.getStatus())) {
                SmsTrialEntity smsTrialEntity = smsTrialDao.querySmsTrial(ea); // 短信试用
                if (smsTrialEntity != null) {
                    signatureEntity = signatureDao.getSignatureById(trialSignatureId);
                }
            }
        }
        return signatureEntity;
    }

    private MwSmsSignatureEntity getSmsSignature(String ea, Integer channelType) {
        MwSmsSignatureEntity signatureEntity;
        SmsChannelConfigEntity smsChannelConfigEntity = smsChannelConfigDAO.queryChannelConfig(ea, channelType);
        if (smsChannelConfigEntity != null) {
            signatureEntity = signatureDao.getSignatureById(smsChannelConfigEntity.getSignatureId());
        } else {
            signatureEntity = signatureDao.getSignatureByEa(ea);
        }
        return signatureEntity;
    }

    /**
     * 取消发送任务，通过修改表mw_sms_send的send_flag控制并发
     * 1、修改send_flag的方式判断是否可以取消发送任务
     * 2、返回扣除的短信费用
     * 3、修改表mw_sms_send的发送状态status为取消发送,修改实际发送人数、总共消费短信数量
     * 4、批量修改表mw_sms_detail的发送状态为status为取消发送
     * @param smsSendId
     * @return
     */
    @Transactional
    public boolean cancelSendSms(String smsSendId, String ea) {
        MwSmsSendEntity smsSendEntity = smsSendDao.getSMSSendById(smsSendId);
        if (smsSendEntity == null || !smsSendEntity.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType()) || !smsSendEntity.getStatus().equals(MwSendStatusEnum.SCHEDULE_SENDING.getStatus())) {
            log.info("取消定时任务中，smsSendEntity的状态非定时任务 smsSendEntity:{}", smsSendEntity);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        if (!smsSendDao.updateSendFlag(smsSendId, 1570602732000L, System.currentTimeMillis())) {
            return false;
        }
        if (!quotaManager.increaseQuotaLeftCount(ea, smsSendEntity.getTotalFee())) {
            log.info("取消定时任务中，返回扣除的短信费用失败 smsSendEntity:{}", smsSendEntity);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        smsSendEntity.setStatus(MwSendStatusEnum.CANCEL.getStatus());
        smsSendEntity.setActualSenderCount(0);
        smsSendEntity.setTotalFee(0);
        if (!smsSendDao.updateSendEntity(smsSendEntity)) {
            log.info("取消定时任务中，设置send表数据 smsSendEntity:{}", smsSendEntity);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        // 防止大批量更新明细状态接口超时
        ThreadPoolUtils.execute(()-> smsSendDao.batchUpdateSendDetailStatusBySendId(smsSendId, MwSendDetailStatusEnum.CANCELED.getStatus()), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return true;
    }

    public boolean saveSendIdAndMarketingEventToDB(MwSmsSendEntity mwSmsSendEntity, String ea, GroupSenderArg arg) {
        if (StringUtils.isEmpty(mwSmsSendEntity.getId()) || StringUtils.isEmpty(ea) || arg == null || StringUtils.isEmpty(arg.getMarketingEventId())) {
            return false;
        }
        MwSmsSendMarketingEventRelationEntity entity = new MwSmsSendMarketingEventRelationEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setSendId(mwSmsSendEntity.getId());
        entity.setMarketingEventId(arg.getMarketingEventId());
        ExternalConfig externalConfig = new ExternalConfig();
        MarketingActivityGroupSenderData marketingActivityGroupSenderVO = new MarketingActivityGroupSenderData();
        BeanUtils.copyProperties(arg, marketingActivityGroupSenderVO);
        marketingActivityGroupSenderVO.setSmsSendId(mwSmsSendEntity.getId());
        marketingActivityGroupSenderVO.setTemplateId(mwSmsSendEntity.getTemplateId());
        marketingActivityGroupSenderVO.setScheduleTime(arg.getScheduleTime() != null ? arg.getScheduleTime().getTime() : null);
        marketingActivityGroupSenderVO.setMarketingUserGroupIds(arg.getUserGroupIds());
        marketingActivityGroupSenderVO.setSendRange(arg.getGroupType());
        externalConfig.setMarketingActivityGroupSenderVO(marketingActivityGroupSenderVO);
        entity.setExternalConfig(externalConfig);
        return mwSmsSendMarketingEventRelationDao.addMwSmsSendMarketingEventRelation(entity);
    }

    @Data
    @ToString
    public class SendRequestTask{
        SendRequestResult requestResult;
        List<MwSendDetailEntity> detailEntityList;
    }

    public void queryMwTemplateStatus(){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 3);
        List<MwSmsTemplateEntity> templateEntities = mwSmsTemplateDao.querySyncTemplateEntity(MwTemplateStatusEnum.UNCONFIRMED.getStatus(), calendar.getTime());
        if (CollectionUtils.isEmpty(templateEntities)){
            return;
        }
        List<String> tplids = templateEntities.stream().map(MwSmsTemplateEntity::getTplid).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tplids)){
            return;
        }

        Result<Map<String, TemplateQueryDetail>> result = mwTemplateManager.queryTemplateStatus(tplids);
        if (!result.isSuccess() || result.getData() == null){
            log.info("queryMwTemplateStatus return not success tplids:{}", tplids);
            return;
        }

        Map<String, MwSmsTemplateEntity> updateEntityMap = templateEntities.stream().filter(entity -> StringUtils.isNotEmpty(entity.getParentId())).collect(Collectors.toMap(MwSmsTemplateEntity::getId, entity -> entity, (v1, v2) -> v1));
        for (Map.Entry<String, TemplateQueryDetail> entry : result.getData().entrySet()){
            String templateId = entry.getKey();
            Integer status = entry.getValue().getStatus();
            if (!status.equals(MwTemplateStatusEnum.UNCONFIRMED.getStatus())) {
                // 若审核通过，将编辑前老的内容更新
                if (status.equals(MwTemplateStatusEnum.VALIDITY.getStatus())) {
                    MwSmsTemplateEntity entity = updateEntityMap.get(templateId);
                    if (entity != null) {
                        entity.setId(entity.getParentId());
                        entity.setName(entity.getName());
                        entity.setContent(entity.getContent());
                        mwSmsTemplateDao.updateTemplateNameAndContent(entity);
                    }
                }
                mwSmsTemplateDao.updateTemplateStatusAndReply(status, entry.getValue().getDesc(), templateId);
                try {
                    MwSmsTemplateEntity templateEntity = mwSmsTemplateDao.getTemplateById(templateId);
                    if (templateEntity != null && templateEntity.getTemplateObjId() != null) {
                        String templateObjId = templateEntity.getTemplateObjId();
                        Map<String, Object> updateMap = Maps.newHashMap();
                        updateMap.put("reply", templateEntity.getReply());
                        if (Objects.equals(templateEntity.getStatus(), MwTemplateStatusEnum.VALIDITY.getStatus())) {
                            updateMap.put("template_status", "APPROVED");
                        } else if (Objects.equals(templateEntity.getStatus(), MwTemplateStatusEnum.UNCONFIRMED.getStatus())) {
                            updateMap.put("template_status", "APPROVING");
                        } else {
                            updateMap.put("template_status", "REJECTED");
                        }
                        mwTemplateManager.updateObj(templateEntity.getEa(), templateObjId, updateMap);
                    }
                } catch (Exception e) {
                    log.info("updateObj error:", e);
                }
            }
        }
    }

    /**
     * 定时任务完成,发送通知crm
     */
    private void doSendGroupSmsStatusMq(String ea, Integer status, String taskId) {
        try {
            GroupSmsStatusVO groupSmsStatusVO = new GroupSmsStatusVO();
            groupSmsStatusVO.setEa(ea);
            groupSmsStatusVO.setStatus(status);
            groupSmsStatusVO.setTaskId(taskId);
            groupSmsStatusSender.send(groupSmsStatusVO);
            log.info("doSendGroupSmsStatusMq groupSmsStatusVO,{} ",groupSmsStatusVO);
        } catch (Exception e) {
            log.error("doSendGroupSmsStatusMq error:", e);
        }
    }

    public void fixSmsSendRecordObjSendStatus(String ea) {
        String date = "2023-05-19 23:00:00";
        Date specialTime = DateUtil.parse(date);
        int totalCount = smsSendDao.countBySpecialTime(specialTime);
        int count = 0;
        String lastId = null;
        int pageSize = 2000;
        int sendCount = 0;
        while(count < totalCount) {
            List<MwSmsSendEntity> smsSendEntityList = smsSendDao.scanBySpecialTime(lastId, specialTime, pageSize);
            if (CollectionUtils.isEmpty(smsSendEntityList)) {
                break;
            }
            int size = smsSendEntityList.size();
            count += size;
            lastId = smsSendEntityList.get(size - 1).getId();
            for (MwSmsSendEntity mwSmsSendEntity : smsSendEntityList) {
                sendCount++;
                long beginTime = System.currentTimeMillis();
                Page page = new Page(1, pageSize, true);
                List<MwSendDetailEntity> smsSendDetailEntities = smsSendDao.getSendDetailBySmsSendIdAndStatus2Page(mwSmsSendEntity.getId(), MwSendDetailStatusEnum.SEND_FAIL.getStatus(), page);
                if (CollectionUtils.isEmpty(smsSendDetailEntities)) {
                    log.info("fixSmsSendRecordObjSendStatus sendId:{} {} 没有发送失败记录", mwSmsSendEntity.getId(), sendCount);
                    continue;
                }
                int totalNum = page.getTotalNum();
                MwSendDetailUpdateMqArg mwSendDetailUpdateMqArg = new MwSendDetailUpdateMqArg();
                mwSendDetailUpdateMqArg.setEa(mwSmsSendEntity.getEa());
                mwSendDetailUpdateMqArg.setSendDetailEntityList(smsSendDetailEntities);
                long t1 = System.currentTimeMillis();
                updateSmsSendRecordObjStatusToFail(mwSendDetailUpdateMqArg);
                long t2 = System.currentTimeMillis();
                log.info("fixSmsSendRecordObjSendStatus sendId:{} totalNum: {} page: {} batchSize: {} 耗时: {}ms", mwSmsSendEntity.getId(), totalNum, 1, smsSendDetailEntities.size(), t2 - t1);
                int totalPage = page.getTotalPage();
                for (int i = 2; i <= totalPage; i++) {
                    page = new Page(i, pageSize, false);
                    smsSendDetailEntities = smsSendDao.getSendDetailBySmsSendIdAndStatus2Page(mwSmsSendEntity.getId(), MwSendDetailStatusEnum.SEND_FAIL.getStatus(), page);
                    if (CollectionUtils.isEmpty(smsSendDetailEntities)) {
                        break;
                    }
                    mwSendDetailUpdateMqArg = new MwSendDetailUpdateMqArg();
                    mwSendDetailUpdateMqArg.setEa(mwSmsSendEntity.getEa());
                    mwSendDetailUpdateMqArg.setSendDetailEntityList(smsSendDetailEntities);
                    long t3 = System.currentTimeMillis();
                    updateSmsSendRecordObjStatusToFail(mwSendDetailUpdateMqArg);
                    long t4 = System.currentTimeMillis();
                    log.info("fixSmsSendRecordObjSendStatus sendId:{} totalNum: {} page: {} batchSize: {} 耗时: {}ms", mwSmsSendEntity.getId(), totalNum, i, smsSendDetailEntities.size(), t4 - t3);
                }
                long endTime = System.currentTimeMillis();
                log.info("fixSmsSendRecordObjSendStatus sendId:{} totalNum: {} 第: {} 条主表记录，耗时: {}", mwSmsSendEntity.getId(), totalNum, sendCount, endTime - beginTime);
            }
        }
    }

    /**
     * 校验excel文件
     * @param arg
     * @param templateEntity
     * @return
     */
    public Result validate4Excel(CreateSendTaskArg arg, MwSmsTemplateEntity templateEntity) {
        String taPath = arg.getTaPath();
        try{
            byte[] excelByte = fileV2Manager.downloadAFile(taPath, null);
            if (excelByte == null) {
                return Result.newError(SHErrorCode.SMS_EXCEL_DOWNLOAD_ERROR);
            }
            List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(taPath, excelByte);
            if (CollectionUtils.isEmpty(excelData) || excelData.size() == 1) {
                return Result.newError(SHErrorCode.APPLY_TEMPLATE_NULL_FAILED);
            }

            List<String> columns = ReadExcelUtil.getColumns(excelData);
            /*if (org.apache.commons.collections4.CollectionUtils.isEmpty(columns)) {
                return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
            }*/

            // 检查excel的列名和模板变量是否匹配
            if (templateEntity != null) {
                List<String> vars = TemplateUtil.extractVariables(templateEntity.getContent());
                if (CollectionUtils.isNotEmpty(vars)) {
                    // 判断columns是否全部包含vars
                    if (!columns.containsAll(vars)) {
                        return Result.newError(SHErrorCode.SMS_VARIABLE_ERROR);
                    }
                }
            }
        } catch (Exception e) {
            log.info("MwSendManager validate4Excel failed, exception:{}", e.toString());
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_EXCEPTION_ERROR);
        }
        return Result.newSuccess();
    }

    /**
     * excel导入-手机号对象组装
     * @param arg
     * @return
     */
    private Result<List<PhoneContentResult>> buildPhoneContentFromExcel(CreateSendTaskArg arg) {
        String taPath = arg.getTaPath();
        List<PhoneContentResult> phoneContentResults = Lists.newArrayList();
        try{
            byte[] excelByte = fileV2Manager.downloadAFile(taPath, null);
            if (excelByte == null) {
                return Result.newError(SHErrorCode.SMS_EXCEL_DOWNLOAD_ERROR);
            }
            List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(taPath, excelByte);
            ParamsOfExcelResult excelResult = ReadExcelUtil.readList2ParamAndPhone4New(excelData, arg.isIgnoreErrorPhone());
            if (excelResult.getType().equals(ExcelCheckResultTypeEnum.SUCCESS.getType())) {
                List<OneSMSDetailResult> smsDetails = excelResult.getOneSMSDetailResults();
                if (CollectionUtils.isNotEmpty(smsDetails)) {
                    for (OneSMSDetailResult smsDetail : smsDetails) {
                        PhoneContentResult phoneContentResult = new PhoneContentResult();
                        phoneContentResult.setPhone(smsDetail.getMobile());
                        phoneContentResult.setParamMap(smsDetail.getParamsMap());
                        phoneContentResults.add(phoneContentResult);
                    }
                }
            } else {
                log.warn("buildPhoneContentFromExcel excelResult is not success, excelResult:{}", excelResult);
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.EXCEL_EMPTY.getType())) {
                    return Result.newError(SHErrorCode.APPLY_TEMPLATE_NULL_FAILED);
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.PHONE_ERROR.getType())) {
                    StringBuilder sb = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(excelResult.getErrorList())) {
                        for (String errorPhone : excelResult.getErrorList()) {
                            sb.append(errorPhone).append(",");
                        }
                    }
                    Result result = new Result(SHErrorCode.EXCEL_TEMPLATE_PHONE_ERROR.getErrorCode(), sb.toString());
                    return result;
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.PARAMETER_ERROR.getType())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.TITLE_ERROR.getType())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                }
                if (excelResult.getType().equals(ExcelCheckResultTypeEnum.WRONG_FORMAT.getType())) {
                    return Result.newError(SHErrorCode.EXCEL_PARAM_NUMBER_ERROR);
                }
            }
        } catch (Exception e) {
            log.info("MwSendManager buildPhoneContentFromExcel failed, exception:{}", e.toString());
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_EXCEPTION_ERROR);
        }
        return Result.newSuccess(phoneContentResults);
    }

    /**
     * 组装市场活动自定义字段值map
     * @param ea
     * @param marketingEventId
     * @return
     */
    public Map<String, String> buildMktCustomizeFieldMap(String ea, String marketingEventId) {
        Map<String, String> customizeMktDetail = crmV2Manager.getObjectDataEnTextVal(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (customizeMktDetail != null) {
            HashMap<String, String> stringVHashMap = new HashMap<>();
            customizeMktDetail.forEach((k,v)->{
                if (v != null) {
                    stringVHashMap.put(k, String.valueOf(v));
                } else {
                    stringVHashMap.put(k, "暂无");
                }
            });
            return stringVHashMap;
        }
        return Maps.newHashMap();
    }

    /**
     * 判断模板中是否存在变量
     * @param templateEntity
     * @return
     */
    private boolean isExistsVars(MwSmsTemplateEntity templateEntity) {
        if (templateEntity != null) {
            return TemplateUtil.containsVariables(templateEntity.getContent());
        }
        return false;
    }

    /**
     * 根据模板找出有效变量，适用于多个手机号发送相同内容（例如直播邀约，会议邀约）
     * @param templateEntity
     * @param allParamMap
     * @return
     */
    private Map<String, String> findValidParamsByTpl(MwSmsTemplateEntity templateEntity, Map<String, String> allParamMap) {
        if (templateEntity == null) {
            return Maps.newHashMap();
        }

        if (!isExistsVars(templateEntity)) {
            return Maps.newHashMap();
        }
        // 根据variables取出allParamMap中存在的key
        String content = templateEntity.getContent();
        List<String> variables = TemplateUtil.extractVariables(content);
        Map<String, String> validParams = allParamMap.entrySet().stream().filter(e -> variables.contains(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        log.info("findValidParamsByTpl validParams:{}", validParams);
        return validParams;
    }

    private Map<String, String> findValidParamsByVar(List<String> variables, Map<String, String> allParamMap) {
        if (CollectionUtils.isEmpty(variables)) {
            return Maps.newHashMap();
        }

        // 根据variables取出allParamMap中存在的key
        Map<String, String> validParams = allParamMap.entrySet().stream().filter(e -> variables.contains(e.getKey())).collect(Collectors.toMap(Entry::getKey, Entry::getValue));
        log.info("findValidParamsByVar validParams:{}", validParams);
        return validParams;
    }

    /**
     * 找出有效变量
     * @param smsVarArgs
     * @param allParamMap
     * @return
     */
    private Map<String, String> findValidParams(List<SmsVarArg> smsVarArgs, Map<String, String> allParamMap) {
        if (CollectionUtils.isEmpty(smsVarArgs)) {
            return Maps.newHashMap();
        }

        // 找出所有变量
        List<String> variables = smsVarArgs.stream().filter(s -> s.getValueType().equals("1")).map(smsVarArg -> smsVarArg.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(variables)) {
            return Maps.newHashMap();
        }
        // 根据variables取出allParamMap中存在的key
        Map<String, String> validParams = allParamMap.entrySet().stream().filter(e -> variables.contains(e.getKey())).collect(Collectors.toMap(Entry::getKey, Entry::getValue));
        log.info("findValidParams validParams:{}", validParams);
        return validParams;
    }

    /**
     * 直播推广-手机号对象组装
     * @param arg
     * @return
     */
    private Result<List<PhoneContentResult>> buildPhoneContentFromLiveInvite(CreateSendTaskArg arg) {
        if (CollectionUtils.isEmpty(arg.getUserGroupIds())) {
            log.warn("MwSendManager.buildPhoneContentFromLiveInvite taPath is null or UserGroupIds is empty arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Optional<List<PhoneContentResult>> phoneListOptional = getPhoneList(arg.getEa(), arg.getUserGroupIds());
        if (!phoneListOptional.isPresent() || CollectionUtils.isEmpty(phoneListOptional.get())) {
            log.warn("MwSendManager.buildPhoneContentFromLiveInvite phoneList is empty phoneListOptional:{} arg:{}", phoneListOptional, arg);
            return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
        }
        List<PhoneContentResult> phoneContentResultList = phoneListOptional.get();
        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), arg.getTemplateId());
        // 模板中不存在变量
        if (!isExistsVars(templateEntity)) {
            return Result.newSuccess(phoneContentResultList);
        }

        Optional<MarketingLiveEntity> marketingLiveEntityByMarketingEventIdOptional = liveManager.getMarketingLiveEntityByMarketingEventId(arg.getEa(), arg.getMarketingEventId());
        if (!marketingLiveEntityByMarketingEventIdOptional.isPresent() && marketingLiveEntityByMarketingEventIdOptional.get() != null) {
            log.warn("MwSendManager.buildPhoneContentFromLiveInvite optional is empty arg:{}", arg);
            return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_NOT_FOUND);
        }
        MarketingLiveEntity marketingLiveEntity = marketingLiveEntityByMarketingEventIdOptional.get();
        Map<String, String> mktCustomizeFieldMap = buildMktCustomizeFieldMap(arg.getEa(), arg.getMarketingEventId());

        // 只填充需要的变量
        Map<String, String> allParam = Maps.newHashMap();
        allParam.putAll(marketingLiveEntity.getParamValueMap());
        allParam.putAll(mktCustomizeFieldMap);
        Map<String, String> validParam;
        if (isNewTemplate(templateEntity)) {
            validParam = findValidParams(arg.getSmsVarArgs(), allParam);
        } else {
            validParam = findValidParamsByTpl(templateEntity, allParam);
        }
        for (PhoneContentResult element : phoneContentResultList) {
            element.setParamMap(validParam);
        }
        return Result.newSuccess(phoneContentResultList);
    }

    /**
     * 会议推广-手机号对象组装
     * @param arg
     * @return
     */
    private Result<List<PhoneContentResult>> buildPhoneContentFromConferenceInvite(CreateSendTaskArg arg){
        if (CollectionUtils.isEmpty(arg.getUserGroupIds())) {
            log.warn("MwSendManager.buildPhoneContentFromConferenceInvite UserGroupIds is empty arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Optional<List<PhoneContentResult>> phoneListOptional = getPhoneList(arg.getEa(), arg.getUserGroupIds());
        if (!phoneListOptional.isPresent() || CollectionUtils.isEmpty(phoneListOptional.get())) {
            log.warn("MwSendManager.buildPhoneContentFromConferenceInvite phoneList is empty phoneListOptional:{} arg:{}", phoneListOptional, arg);
            return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
        }

        Optional<ActivityEntity> activityEntityByMarketingEventIdOptional = activityManager.getActivityEntityByMarketingEventId(arg.getEa(), arg.getMarketingEventId());
        if (!activityEntityByMarketingEventIdOptional.isPresent() && activityEntityByMarketingEventIdOptional.get() != null) {
            log.warn("MwSendManager.buildPhoneContentFromConferenceInvite optional is empty arg:{}", arg);
            return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_NOT_FOUND);
        }

        List<PhoneContentResult> phoneContentResultList = phoneListOptional.get();
        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), arg.getTemplateId());
        // 模板中不存在变量
        if (!isExistsVars(templateEntity)) {
            return Result.newSuccess(phoneContentResultList);
        }

        // 会议相关参数
        ActivityEntity activityEntity = activityEntityByMarketingEventIdOptional.get();
        String activityUrl = host + "/ec/cml-marketing/release/web/cml-marketing.html?conferenceId=" + activityEntity.getId() + "&marketingEventId=" + activityEntity.getMarketingEventId() + "&byshare=1&_hash=/cml/h5/conference_detail";
        activityUrl = shortUrlManager.createShortUrl(activityUrl).orElse("");
        Map<String, String> mktCustomizeFieldMap = buildMktCustomizeFieldMap(arg.getEa(), arg.getMarketingEventId());

        // 只填充需要的变量
        Map<String, String> allParam = Maps.newHashMap();
        allParam.putAll(activityEntity.getParamValueMap());
        allParam.putAll(mktCustomizeFieldMap);
        allParam.put("activity.url", activityUrl);
        Map<String, String> validParam;
        if (isNewTemplate(templateEntity)) {
            validParam = findValidParams(arg.getSmsVarArgs(), allParam);
        } else {
            validParam = findValidParamsByTpl(templateEntity, allParam);
        }
        for (PhoneContentResult element : phoneContentResultList) {
            element.setParamMap(validParam);
        }
        return Result.newSuccess(phoneContentResultList);
    }

    /**
     * 活动推广-手机号对象组装
     * @param arg
     * @return
     */
    private Result<List<PhoneContentResult>> buildPhoneContentFromEventInvite(CreateSendTaskArg arg) {
        if (CollectionUtils.isEmpty(arg.getUserGroupIds())) {
            log.warn("MwSendManager.buildPhoneContentFromEventInvite taPath is null or UserGroupIds is empty arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Optional<List<PhoneContentResult>> phoneListOptional = getPhoneList(arg.getEa(), arg.getUserGroupIds());
        if (!phoneListOptional.isPresent() || CollectionUtils.isEmpty(phoneListOptional.get())) {
            log.warn("MwSendManager.buildPhoneContentFromEventInvite phoneList is empty phoneListOptional:{} arg:{}", phoneListOptional, arg);
            return Result.newError(SHErrorCode.SMS_USER_GROUP_NO_PHONE_ERROR);
        }

        List<PhoneContentResult> phoneContentResultList = phoneListOptional.get();
        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), arg.getTemplateId());
        // 模板中不存在变量
        if (!isExistsVars(templateEntity)) {
            return Result.newSuccess(phoneContentResultList);
        }

        // 查询市场活动详情
        ObjectData mktDetail = crmV2Manager.getDetail(arg.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), arg.getMarketingEventId());
        // 查询市场活动自定义字段
        Map<String, String> mktCustomizeFieldMap = buildMktCustomizeFieldMap(arg.getEa(), arg.getMarketingEventId());

        // 只填充需要的变量
        Map<String, String> allParam = Maps.newHashMap();
        allParam.putAll(mktCustomizeFieldMap);
        // 活动参数替换
        if (mktDetail != null) {
            allParam.put("activity.name", mktDetail.getName() == null ? "暂无" : mktDetail.getName());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            if(mktDetail.get("begin_time") != null) {
                allParam.put("activity.startTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("begin_time")).longValue())));
            }
            if(mktDetail.get("end_time") != null) {
                allParam.put("activity.endTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("end_time")).longValue())));
            }
        }
        Map<String, String> validParam;
        if (isNewTemplate(templateEntity)) {
            validParam = findValidParams(arg.getSmsVarArgs(), allParam);
        } else {
            validParam = findValidParamsByTpl(templateEntity, allParam);
        }
        for (PhoneContentResult element : phoneContentResultList) {
            if (StringUtils.isBlank(element.getPhone())) {
                continue;
            }
            element.setParamMap(validParam);
        }
        return Result.newSuccess(phoneContentResultList);
    }

    /**
     * 直播通知-手机号对象组装
     * @param arg
     * @return
     */
    private Result<List<PhoneContentResult>> buildPhoneContentFromLiveEnroll(CreateSendTaskArg arg){
        List<String> campaignMergeDataIds = arg.getCampaignIds();
        if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }

        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), arg.getTemplateId());
        List<String> variables = Lists.newArrayList();
        if (templateEntity != null) {
            variables = TemplateUtil.extractVariables(templateEntity.getContent());
        }

        boolean existsVars = isExistsVars(templateEntity);
        List<PhoneContentResult> phoneContentResults = liveManager.buildLivePhoneContentList(campaignMergeDataIds, existsVars);
        if (!existsVars) {
            return Result.newSuccess(phoneContentResults);
        }

        // 增加市场活动自定义参数
        Map<String, String> mktCustomizeFieldMap = buildMktCustomizeFieldMap(arg.getEa(), arg.getMarketingEventId());
        for (PhoneContentResult element : phoneContentResults) {
            element.getParamMap().putAll(mktCustomizeFieldMap);
            if (isNewTemplate(templateEntity)) {
                element.setParamMap(findValidParams(arg.getSmsVarArgs(), element.getParamMap()));
            } else {
                element.setParamMap(findValidParamsByVar(variables, element.getParamMap()));
            }
        }
        return Result.newSuccess(phoneContentResults);
    }

    /**
     * 会议通知-手机号对象组装
     * @param arg
     * @return
     */
    private Result<List<PhoneContentResult>> buildPhoneContentFromConferenceEnroll(CreateSendTaskArg arg){
        List<String> campaignMergeDataIds = arg.getCampaignIds();
        if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }

        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), arg.getTemplateId());
        List<String> variables = Lists.newArrayList();
        if (templateEntity != null) {
            variables = TemplateUtil.extractVariables(templateEntity.getContent());
        }
        boolean existsVars = isExistsVars(templateEntity);
        List<String> conferenceEnrollIds = campaignMergeDataManager.campaignIdToActivityEnrollId(campaignMergeDataIds);
        List<PhoneContentResult> phoneContentResults = conferenceManager.buildPhoneContentEnrollList(conferenceEnrollIds, existsVars);
        if (!existsVars) {
            return Result.newSuccess(phoneContentResults);
        }

        // 增加市场活动自定义参数
        Map<String, String> mktCustomizeFieldMap = buildMktCustomizeFieldMap(arg.getEa(), arg.getMarketingEventId());
        for (PhoneContentResult element : phoneContentResults) {
            element.getParamMap().putAll(mktCustomizeFieldMap);
            if (isNewTemplate(templateEntity)) {
                element.setParamMap(findValidParams(arg.getSmsVarArgs(), element.getParamMap()));
            } else {
                element.setParamMap(findValidParamsByVar(variables, element.getParamMap()));
            }
        }
        return Result.newSuccess(phoneContentResults);
    }

    /**
     * 活动通知-手机号对象组装
     * @param arg
     * @return
     */
    private Result<List<PhoneContentResult>> buildPhoneContentFromEventEnroll(CreateSendTaskArg arg) {
        List<String> campaignMergeDataIds = arg.getCampaignIds();
        if (CollectionUtils.isEmpty(campaignMergeDataIds)) {
            return Result.newError(SHErrorCode.SMS_DETAIL_CREATE_ERROR);
        }

        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), arg.getTemplateId());
        List<String> variables = Lists.newArrayList();
        if (templateEntity != null) {
            variables = TemplateUtil.extractVariables(templateEntity.getContent());
        }
        boolean existsVars = isExistsVars(templateEntity);
        List<PhoneContentResult> phoneContentResults = Lists.newArrayList();
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignMergeDataIds);
        if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
            ObjectData mktDetail = crmV2Manager.getDetail(arg.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), arg.getMarketingEventId());
            Map<String, String> mktCustomizeFieldMap = buildMktCustomizeFieldMap(arg.getEa(), arg.getMarketingEventId());
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                if (StringUtils.isBlank(campaignMergeDataEntity.getPhone())) {
                    continue;
                }
                // 添加活动成员参数
                PhoneContentResult contentResult = new PhoneContentResult();
                contentResult.setPhone(campaignMergeDataEntity.getPhone());

                // 模板中存在变量，才需要处理变量参数
                if (existsVars) {
                    String campaignMembersObjId = campaignMergeDataEntity.getCampaignMembersObjId();
                    Map<String, String> detail = crmV2Manager.getObjectDataEnTextVal(campaignMergeDataEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMembersObjId);
                    if (detail != null) {
                        HashMap<String, String> stringVHashMap = new HashMap<>();
                        stringVHashMap.putAll(campaignMergeDataEntity.getParamValueMap());
                        detail.forEach((k,v)->{
                            if (v != null) {
                                stringVHashMap.put(k, String.valueOf(v));
                            } else {
                                stringVHashMap.put(k, "暂无");
                            }
                        });

                        // 活动参数替换
                        if (mktDetail != null) {
                            stringVHashMap.put("activity.name", mktDetail.get("name") == null ? "暂无" : mktDetail.get("name").toString());
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            if(mktDetail.get("begin_time") != null) {
                                stringVHashMap.put("activity.startTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("begin_time")).longValue())));
                            }
                            if(mktDetail.get("end_time") != null) {
                                stringVHashMap.put("activity.endTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("end_time")).longValue())));
                            }
                        }
                        // 增加市场活动自定义字段
                        stringVHashMap.putAll(mktCustomizeFieldMap);
                        Map<String, String> validParams;
                        if (isNewTemplate(templateEntity)) {
                            validParams = findValidParams(arg.getSmsVarArgs(), stringVHashMap);
                        } else {
                            validParams = findValidParamsByVar(variables, stringVHashMap);
                        }
                        contentResult.setParamMap(validParams);
                    }
                }
                phoneContentResults.add(contentResult);
            }
        }
        return Result.newSuccess(phoneContentResults);
    }

    public void initDetailTemplateIdAndChannelType(String ea) {
        ThreadPoolUtils.execute(() -> {
            String date = "2019-03-19 02:04:40";
            Date specialTime = DateUtil.parse(date);
            int totalCount = smsSendDao.countBySpecialTime(specialTime);
            int count = 0;
            String lastId = null;
            int pageSize = 2000;
            int updateTotalCount = 0;
            while(count < totalCount) {
                List<MwSmsSendEntity> smsSendEntityList = smsSendDao.scanBySpecialTime(lastId, specialTime, pageSize);
                if (CollectionUtils.isEmpty(smsSendEntityList)) {
                    break;
                }
                int size = smsSendEntityList.size();
                count += size;
                lastId = smsSendEntityList.get(size - 1).getId();
                for (MwSmsSendEntity mwSmsSendEntity : smsSendEntityList) {
                    updateTotalCount++;
                    if (mwSmsSendEntity.getChannelType() == null && StringUtils.isBlank(mwSmsSendEntity.getTemplateId())) {
                        continue;
                    }
                    try {
                        int nullCount = smsSendDao.countTemplateAndChannelTypeIsNull(mwSmsSendEntity.getEa(), mwSmsSendEntity.getId());
                        if (nullCount <= 0) {
                            log.info("ea: {} sendId: {} 已经被初始化过了 主表总数量:{} 已更新数量：{}", mwSmsSendEntity.getEa(), mwSmsSendEntity.getId(), totalCount, updateTotalCount);
                            continue;
                        }
                        long t1 = System.currentTimeMillis();
                        int updateCount = smsSendDao.updateDetailTemplateIdAndChannelType(mwSmsSendEntity.getEa(), mwSmsSendEntity.getId(), mwSmsSendEntity.getTemplateId(), mwSmsSendEntity.getChannelType());
                        log.info("更新耗时: {}, ea: {} sendId: {} 主表总数量:{} 已更新数量：{} 该id更新的数量：{}", System.currentTimeMillis() - t1, mwSmsSendEntity.getEa(), mwSmsSendEntity.getId(), totalCount, updateTotalCount, updateCount);
                    } catch (Exception e) {
                        log.error("initDetailTemplateIdAndChannelType error, data: {}", mwSmsSendEntity, e);
                    }
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }
}