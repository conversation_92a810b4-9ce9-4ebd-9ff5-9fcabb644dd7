package com.facishare.marketing.provider.baidu;

import com.facishare.marketing.provider.baidu.IResultData;
import com.facishare.marketing.provider.baidu.OfflineTimeType;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Created by ranluch on 2019/11/22.
 */
@Data
@ToString
public class GetAccountInfoResultData implements IResultData, Serializable {
    /**
     * 	账户ID
     * */
    @SerializedName("userId")
    private Long userId;

    /**
     * 	推广共享包金额	账户剩余金额减去信用卡透支金额
     （但目前不会提供小于0的数，即使小于0也会返回0）
     * */
    @SerializedName("balance")
    private Double balance;

    /**
     * 	基准资金包金额	PC余额
     * */
    @SerializedName("pcBalance")
    private Double pcBalance;

    /**
     * 	无线优惠资金包金额	移动余额
     * */
    @SerializedName("mobileBalance")
    private Double mobileBalance;

    /**
     * 	账户累积消费
     * 	注：如果使用了全能账户（开通全能账户后，用户名下的所有账户将会共享余额，每个账户的显示的余额为所有账户余额的和，
        cost的计算公式是：
        总消费=总现金+总优惠+总补偿+总转入资金-余额，余额变大导致cost可能为负。），此处余额可能为负
     * */
    @SerializedName("cost")
    private Double cost;

    /**
     * 	账户投资
     * */
    @SerializedName("payment")
    private Double payment;

    /**
     * 	账户预算类型
     * 	0为不设置预算；
        1为日预算；
        2为周预算；
     * */
    @SerializedName("budgetType")
    private Integer budgetType;

    /**
     * 	账户预算	当设置为日预算时，取值范围：
        [50, 10000000]；
        当设置为周预算时，取值范围：[388, 70000000]；
        当不设置预算时，输入任意值均默认为0；
     * */
    @SerializedName("budget")
    private Double budget;

    /**
     * 	推广地域列表
     * */
    @SerializedName("regionTarget")
    private List<Integer> regionTarget;

    /**
     * 	每个元素是合法的ipV4地址，其中最多可以包括
        3个高级IP地址（后两位为）和200个普通IP地址
        （后1位为或者没有*）。
        数组元素个数最大值：203
     * */
    @SerializedName("excludeIp")
    private List<String> excludeIp;

    /**
     * 	账户开放域名列表
     * 	(开放域名是在已有注册域名的基础上，再添加用户可以推广的域名，系统默认用户只能推广注册域名，物料URL需要和注册域名及开放域名的主域一致即可)
     * */
    @SerializedName("openDomains")
    private List<String> openDomains;

    /**
     * 	账户注册域名
     * */
    @SerializedName("regDomain")
    private String regDomain;

    /**
     * 	到达预算下线时段
     * 	数组元素个数限制：
        最近有过下线时段的7个自然日的下线和上线时段（这7个自然日中若某日期距当前已超过30天，则不返回）
        null : 无到达预算下线时段；
        注：时间为date类型，格式示例”Jul 10, 2015 11:00:00 AM”
     * */
    @SerializedName("budgetOfflineTime")
    private List<OfflineTimeType> budgetOfflineTime;

    /**
     * 	返回本周的每日预算值
     * 	如设置为周预算时，该接口显示的是实际分配到每一天的日预算
     * */
    @SerializedName("weeklyBudget")
    private List<Double> weeklyBudget;

    /**
     * 	账户状态（用于一站式平台显示账户状态）
     * 	null: 未设置获取该属性
     1: 开户金未到
     2: 正常生效
     3: 余额为零
     4: 未通过审核
     6: 审核中
     7: 被禁用
     11: 预算不足
     * */
    @SerializedName("userStat")
    private Integer userStat;


    /**
     子链开关	True：开启
     False：关闭
     （默认开启）
     * */
    @SerializedName("isDynamicCreative")
    private Boolean isDynamicCreative = true;

    /**
     标签子链开关	True：开启
     False：关闭
     （默认开启）
     * */
    @SerializedName("isDynamicTagSublink")
    private Boolean isDynamicTagSublink = true;

    /**
     动态标题开关	True：开启
     False：关闭
     （默认开启）
     * */
    @SerializedName("isDynamicTitle")
    private Boolean isDynamicTitle = true;

    /**
     动态标题开关	True：开启
     False：关闭
     （默认开启）
     * */
    @SerializedName("isDynamicHotRedirect")
    private Boolean isDynamicHotRedirect = true;

    /**
     动态创意统计参数
     以”?”或者”#”开头，长度不超过256字节
     * */
    @SerializedName("dynamicCreativeParam")
    private String dynamicCreativeParam;

    /**
     客户权益等级查询接口，如果用户通过此接口请求了客户权益等级，返回结果中会有对应userLevel字段标识客户权益等级。
     其中，1 – 三徽章客户；2 – 二徽章客户；3 – 一徽章客户；4 – 未生效客户。
     有关客户权益的介绍，请参考客户平台权益
     * */
    @SerializedName("userLevel")
    private Integer userLevel;

    /**
     * 信息流账号特有
     * 取值范围：枚举值，列表如下
     * 0 - 信息流资金包
     * 1 - 搜索推广资金包
     * 2 - 代理商信息流资金包
     */
    @SerializedName("balancePackage")
    private Integer balancePackage;

    /**
     * 信息流特有：是否开通feed产品线权限
     * 取值范围：枚举值，列表如下
     * 1 - 已开通
     * 2 - 待开通
     * 3 - 不允许开通（KA客户）
     */
    @SerializedName("uaStatus")
    private Integer uaStatus;

    /**
     * 信息流特有 可投放流量
     * 数组为空时表示无可投放流量。流量类型与推广单元对象的ftypes字段定义保持一致。
     * 取值范围：枚举值，列表如下
     * 1 - 百度APP
     * 2 - 贴吧
     * 4 - 百青藤
     * 8 - 好看视频
     * 64 - 百度小说
     */
    @SerializedName("validFlows")
    private List<Integer> validFlows;

}
