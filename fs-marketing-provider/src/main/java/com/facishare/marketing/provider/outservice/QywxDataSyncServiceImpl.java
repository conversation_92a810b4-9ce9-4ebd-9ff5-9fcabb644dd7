package com.facishare.marketing.provider.outservice;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.arg.WechatExternalUserSyncArg;
import com.facishare.marketing.outapi.arg.WechatGroupSyncArg;
import com.facishare.marketing.outapi.result.WechatGroupResult;
import com.facishare.marketing.outapi.result.WechatWorkExternalUserResult;
import com.facishare.marketing.outapi.service.QywxDataSyncService;
import com.facishare.marketing.provider.innerResult.qywx.CustomerGroupListResult;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatFriendsRecordObjDescribeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupObjDescribeManager;
import com.facishare.marketing.provider.manager.qywx.CustomerGroupManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("qywxDataSyncService")
@Slf4j
public class QywxDataSyncServiceImpl implements QywxDataSyncService {

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;

    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;

    @Autowired
    private CustomerGroupManager customerGroupManager;

    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public Result<WechatWorkExternalUserResult> syncExternalUser(WechatExternalUserSyncArg arg) {
        WechatWorkExternalUserResult wechatWorkExternalUserResult = new WechatWorkExternalUserResult();
        wechatWorkExternalUserResult.setExternalUserId(arg.getExternalUserId());
        String externalUserObjId;
        //1.先查询企微客户对象
        PaasQueryFilterArg paasExternalUserFilterArg = new PaasQueryFilterArg();
        paasExternalUserFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        PaasQueryArg paasExternalUserQueryArg = new PaasQueryArg(0, 1000);
        paasExternalUserQueryArg.addFilter("external_user_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(arg.getExternalUserId()));
        paasExternalUserFilterArg.setQuery(paasExternalUserQueryArg);
        InnerPage<ObjectData> externalUserInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(arg.getEa(), -10000, paasExternalUserFilterArg);
        if (externalUserInnerPage == null || CollectionUtils.isEmpty(externalUserInnerPage.getDataList())) {
            log.warn("externalUserInnerPage is null or empty,start sync WechatWorkExternalUserObj");
            //进行企微客户数据同步
            //ThreadPoolUtils.executeWithTraceContext(()->{
                wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(arg.getEa(), ImmutableList.of(arg.getExternalUserId()), true, null);
            //}, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            externalUserInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(arg.getEa(), -10000, paasExternalUserFilterArg);
            if (externalUserInnerPage == null || CollectionUtils.isEmpty(externalUserInnerPage.getDataList())) {
                log.warn("syncExternalUser sync data is failed,ea:{},qywxUserId:{},externalUserId:{}",arg.getEa(),arg.getQywxUserId(),arg.getExternalUserId());
                wechatWorkExternalUserResult.setWechatFriendsRecordResult(Lists.newArrayList());
                return Result.newSuccess(wechatWorkExternalUserResult);
            }
            externalUserObjId = externalUserInnerPage.getDataList().get(0).getId();
        } else {
            externalUserObjId = externalUserInnerPage.getDataList().get(0).getId();
        }
        //2.查询企微好友记录
        List<ObjectData> dataList = wechatFriendsRecordObjDescribeManager.queryWechatFriendsRecordObj(arg.getEa(), arg.getQywxUserId(), externalUserObjId, 0);
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("wechatFriendsRecordInnerPage is null or empty,start sync WechatFriendsRecordObj, arg: {}", arg);
            //如果该员工没有改外部联系人对应的好友记录，重新触发一次外部联系人同步
            wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(arg.getEa(), ImmutableList.of(arg.getExternalUserId()), true, null);
            //再查询一遍数据
            dataList = wechatFriendsRecordObjDescribeManager.queryWechatFriendsRecordObj(arg.getEa(), arg.getQywxUserId(), externalUserObjId, 0);
        }
        wechatWorkExternalUserResult.setWechatFriendsRecordResult(dataList);
        return Result.newSuccess(wechatWorkExternalUserResult);
    }

    @Override
    public Result<WechatGroupResult> syncGroup(WechatGroupSyncArg arg) {
        WechatGroupResult result = new WechatGroupResult();
        result.setChatId(arg.getChatId());
        ObjectData objectData = wechatGroupObjDescribeManager.getGroupObject(eieaConverter.enterpriseAccountToId(arg.getEa()), arg.getChatId());
        if (objectData == null) {
            //进行企微群和企微群成员数据同步
            CustomerGroupListResult customerGroupListResult = customerGroupManager.queryCustomerListNew(arg.getEa(), Lists.newArrayList(arg.getQywxUserId()), 1000);
            if (customerGroupListResult == null || CollectionUtils.isEmpty(customerGroupListResult.getGroupList())) {
                log.warn(" wechat group faild call queryCustomerList api return error ea:{}", arg.getEa());
                result.setWechatGroupResult(objectData);
                return Result.newSuccess(result);
            }
            Map<String, CustomerGroupListResult.GroupItem> groupResultMap = customerGroupListResult.getGroupList().stream().collect(Collectors.toMap(CustomerGroupListResult.GroupItem::getGroupId, Function.identity()));
            //进行群成员数据同步
            wechatGroupObjDescribeManager.mergeWeChatGroupListToCrmLimited(arg.getEa(),ImmutableList.of(arg.getChatId()),groupResultMap.get(arg.getChatId()).getStatus(),true);
            //再查一遍
            objectData = wechatGroupObjDescribeManager.getGroupObject(eieaConverter.enterpriseAccountToId(arg.getEa()), arg.getChatId());
        }
        result.setWechatGroupResult(objectData);
        return Result.newSuccess(result);
    }
}
