package com.facishare.marketing.provider.service.kis;

import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.service.ActionService;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.typehandlers.value.ActionVO;
import com.facishare.marketing.api.arg.RecordUtmParamArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.qywx.record.MiniAppRecordArg;
import com.facishare.marketing.api.service.kis.KisActionService;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.MD5Util;
import com.facishare.marketing.common.util.StringReplaceUtils;
import com.facishare.marketing.common.util.TextUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.UserDAO;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.cta.CtaDAO;
import com.facishare.marketing.provider.dao.live.*;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationEntity;
import com.facishare.marketing.provider.entity.cta.CtaEntity;
import com.facishare.marketing.provider.entity.live.*;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteTrackEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmMemberRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.innerResult.BaiduAuthUserInfoResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;

import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;

import java.util.*;

import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by ranluch on 2019/3/11.
 */
@Slf4j
@Service("kisActionService")
public class KisActionServiceImpl implements KisActionService {
    @Autowired
    private ActionService actionService;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private ActionManager actionManager;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private OfficialWebsiteManager officialWebsiteManager;

    @Autowired
    private QRPosterDAO qrPosterDAO;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private ObjectTagManager objectTagManager;

    @Autowired
    private com.facishare.mankeep.api.service.ActionService miniAppActionService;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private SpreadTaskManager spreadTaskManager;

    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private MarketingEventCommonSettingManager marketingEventCommonSettingManager;

    @Autowired
    private MarketingActivityManager marketingActivityManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private XiaoetongAccountDAO xiaoetongAccountDAO;

    @Autowired
    private PolyvAccountDAO polyvAccountDAO;

    @Autowired
    private MuduAccountDAO muduAccountDAO;

    @Autowired
    private VHallManager vHallManager;

    @Autowired
    private ChannelsAccountDAO channelsAccountDAO;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    @Autowired
    private IdempotentRecordManager idempotentRecordManager;

    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private UserDAO userDAO;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;
    @Autowired
    private CtaDAO ctaDAO;

    @FilterLog
    @Override
    public Result record(RecordActionArg arg) {
        if (arg == null) {
            log.info("KisActionServiceImpl record arg is null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        recordUtmParam(arg);
        if (arg.getSpreadFsUid() != null && arg.getSpreadFsUid() == 0){
            log.warn("KisActionServiceImpl record failed arg param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        String fingerPrint = arg.getFingerPrint();
        if (!TextUtil.checkIdIsLegal(fingerPrint)) {
            log.warn("埋点接口，访客id不合法, fingerPrint: {}", fingerPrint);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if (!TextUtil.checkIdIsLegal(arg.getObjectId())) {
            log.warn("埋点接口，物料id不合法, objectId: {}", arg.getObjectId());
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        //以物料所属的ea为准
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isNotBlank(ea)) {
            arg.setEa(ea);
        } else {
            ea = arg.getEa();
        }
        try {
            eieaConverter.enterpriseAccountToId(ea);
        } catch (Exception e) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        Map<String, Object> extensionParams = arg.getExtensionParams();
        if (extensionParams == null) {
            extensionParams = new HashMap<>();
        }
        String phone = null;
        String email = null;
        if (arg.getExtensionParams() != null) {
            Object phoneObj = extensionParams.get("phone");
            phone = phoneObj != null ? phoneObj.toString() : null;
            Object emailObj = extensionParams.get("email");
            email = emailObj != null ? emailObj.toString() : null;
        }
        if (StringUtils.isBlank(phone)) {
            phone = arg.getPhone();
        }
        if (StringUtils.isNotBlank(fingerPrint)) {
            try {
                AssociationArg browserUserAssociateArg = new AssociationArg();
                browserUserAssociateArg.setAssociationId(fingerPrint);
                browserUserAssociateArg.setEa(ea);
                browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
                if (StringUtils.isNotBlank(phone)) {
                    browserUserAssociateArg.setPhone(phone);
                }
                if (StringUtils.isNotBlank(email)) {
                    browserUserAssociateArg.setEmail(email);
                }
                browserUserAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
                browserUserAssociateArg.setTriggerAction("record");
                browserUserAssociateArg.setEmail(email);
                String userMarketingAccountId = userMarketingAccountAssociationManager.associate(browserUserAssociateArg).getUserMarketingAccountId();

                if (StringUtils.isNotBlank(email)) {
                    String emailMarketingUserId = null;
                    // 查询营销用户信息
                    Map<String, MailManager.EmailMarketingDetailInfo> emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(Lists.newArrayList(email), ea, SuperUserConstants.USER_ID);
                    if (MapUtils.isNotEmpty(emailMarketingDetailInfoMap)) {
                        MailManager.EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(email);
                        if (emailMarketingDetailInfo != null) {
                            emailMarketingUserId = emailMarketingDetailInfo.getMarketingUserId();
                        }
                    }
                    if (StringUtils.isNotBlank(emailMarketingUserId)) {
                        userMarketingAccountAssociationManager.merge(ea, userMarketingAccountId, emailMarketingUserId, browserUserAssociateArg);
                    }
                }
            } catch (Exception ignor) {
            }
        }
        if (StringUtils.isNotEmpty(arg.getUid())) {
            try {
                AssociationArg associationArg = new AssociationArg();
                associationArg.setEa(ea);
                associationArg.setType(ChannelEnum.MINIAPP.getType());
                associationArg.setAssociationId(arg.getUid());
                if (StringUtils.isNotBlank(phone)) {
                    associationArg.setPhone(phone);
                    associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
                    associationArg.setTriggerAction("record");
                    associationArg.setEmail(email);
                    userMarketingAccountAssociationManager.associate(associationArg);
                }
//                UserEntity userEntity = userDAO.queryByUid(arg.getUid());
//                if (userEntity != null && StringUtils.isNotEmpty(userEntity.getExternalUserId())) {
//                    userMarketingAccountRelationManager.bindMiniappUserAndWxWorkExternalUser(ea, arg.getUid(), userEntity.getExternalUserId(), "record");
//                }
            } catch (Exception ignor) {
            }
        }
        String primaryId = null;
        primaryId = extensionParams.get("primaryId") != null ? extensionParams.get("primaryId").toString() : null;
        if (primaryId == null) {
            primaryId = redisManager.getPrimaryId();
        }
        arg.setExtensionParams(extensionParams);
        // 浏览相关的  一分钟重复浏览之内不上报
        if (checkRepetitionAction(arg)) {
            return Result.newSuccess();
        }
        extensionParams.put("primaryId", primaryId);

        arg.setMarketingActivityId(StringReplaceUtils.errorStrReplaceToNull(arg.getMarketingActivityId()));
        arg.setMarketingEventId(StringReplaceUtils.errorStrReplaceToNull(arg.getMarketingEventId()));

        if(arg.getChannelType() != null && arg.getChannelType() == MarketingUserActionChannelType.H5.getChannelType()){
            Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getFingerPrint()) && !"null".equals(arg.getFingerPrint()), "fingerPrintId can not be null");
        }
        if (arg.getObjectType().equals(ObjectTypeEnum.OFFICIAL_WEBSITE_EVENT_ATTRIBUTES.getType())) {
            String objectId = officialWebsiteManager.getEventAttributesIdByCustomizeId(arg.getEventId(), arg.getAttributesId(), arg.getEa());
            if (StringUtils.isBlank(objectId)) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            arg.setObjectId(objectId);
            if (StringUtils.isNotBlank(arg.getEventDescription())) {
                // 官网支持自定义的时间描述
                extensionParams.put("eventDescription", arg.getEventDescription());
            }
        }
        RecordActionArg recordArg = BeanUtil.copy(arg, RecordActionArg.class);
        recordArg.setEa(ea);
        if (StringUtils.isEmpty(recordArg.getEa())) {
            log.info("KisActionServiceImpl record ea is null, arg={}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Map<String, Object> extensionMap = recordArg.getExtensionParams();
        if (extensionMap == null) {
            extensionMap = new HashMap<>();
            recordArg.setExtensionParams(extensionMap);
        }
        // 官网行为追踪
        if (StringUtils.isNotBlank(arg.getLookUpUrl())) {
            // 查询官网绑定url
            List<OfficialWebsiteTrackEntity> officialWebsiteTrackEntityList = officialWebsiteManager.queryOfficialWebsiteTrackByEa(ea, true, arg.getWebsiteId());
            if (CollectionUtils.isEmpty(officialWebsiteTrackEntityList)) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            recordArg.setSceneId(arg.getWebsiteId());
            sortByUrlAccuracy(officialWebsiteTrackEntityList);
            // 匹配url
            OfficialWebsiteTrackEntity officialWebsiteTrackEntity = officialWebsiteManager.matchTrackInfo(officialWebsiteTrackEntityList, arg.getLookUpUrl());
            if (officialWebsiteTrackEntity == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            recordArg.setObjectId(officialWebsiteTrackEntity.getId());
            // 浏览相关的  一分钟重复浏览之内不上报
            if (recordArg.getActionType() == ActionTypeEnum.LOOK_UP_WEBSITE.getAction() && checkRepetitionAction(recordArg.getEa(), recordArg.getActionType(), recordArg.getObjectType(), recordArg.getObjectId(), getUserMarketingId(recordArg))) {
                return Result.newSuccess();
            }
            extensionParams.put("websiteUrl", arg.getLookUpUrl());
            if (StringUtils.isNotBlank(recordArg.getUtmCampaign())){
                //如果是官网行为追踪，utmCampaign不为空，并且是广告类型的,营销场景设置为广告营销
                PaasQueryFilterArg evenQueryFilterArg = new PaasQueryFilterArg();
                PaasQueryArg evenPaasQueryArg = new PaasQueryArg(0, 1);
                evenPaasQueryArg.addFilter("event_type",  OperatorConstants.EQ, Lists.newArrayList("advertising_marketing"));
                evenPaasQueryArg.addFilter("name",  OperatorConstants.LIKE,Lists.newArrayList(recordArg.getUtmCampaign()));
                evenQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                evenQueryFilterArg.setQuery(evenPaasQueryArg);
                int total = crmV2Manager.countCrmObjectByFilterV3(recordArg.getEa(),-10000,evenQueryFilterArg);
                if (total > 0){
                    recordArg.setMarketingScene(MarketingSceneEnum.AD.getCode());
                }
            }
        }
        if(StringUtils.isNotBlank(arg.getOperateSystem())){
            extensionParams.put("operateSystem", arg.getOperateSystem());
        }
        if(StringUtils.isNotBlank(arg.getBrowser())){
            extensionParams.put("browser", arg.getBrowser());
        }
        if (StringUtils.isEmpty(recordArg.getMarketingEventId()) && StringUtils.isNotEmpty(recordArg.getMarketingActivityId())) {
            recordArg.setMarketingEventId(objectManager.queryMarketingEventId(recordArg.getMarketingActivityId()));
        }

        if (recordArg.getActionType().equals(ActionTypeEnum.FORWARD_QR_POSTER.getAction())) {
            QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(recordArg.getObjectId());
            if (qrPosterEntity == null) {
                log.warn("KisActionServiceImpl record qrPosterEntity is null, arg={}", arg);
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            log.info("KisActionServiceImpl record poster change before arg:{}, qrPosterEntity={}", arg, qrPosterEntity);
            if (!QRPosterForwardTypeEnum.nonMaterialType(qrPosterEntity.getForwardType()) && StringUtils.isNotEmpty(qrPosterEntity.getTargetId())) {
                recordArg.setObjectId(qrPosterEntity.getTargetId());
                recordArg.setActionType(QRPosterForwardTypeEnum.toActionTypeEnum(qrPosterEntity.getForwardType()).getActionType());
                recordArg.setObjectType(QRPosterForwardTypeEnum.toObjectTypeEnum(qrPosterEntity.getForwardType()).getType());
                recordArg.setSceneId(qrPosterEntity.getId());
                recordArg.setSceneType(MarketingUserActionSceneType.QR_POSTER.getSceneType());
            }
        }

        //邀请函增加场景类型
        if (recordArg.getObjectType().equals(ObjectTypeEnum.ACTIVITY_INVITATION.getType())) {
            ConferenceInvitationEntity conferenceInvitationEntity = new ConferenceInvitationEntity();
            conferenceInvitationEntity.setId(recordArg.getObjectId());
            ConferenceInvitationEntity invitationEntity = objectManager.visit(conferenceInvitationEntity);
            if (invitationEntity != null) {
                recordArg.setSceneType(MarketingUserActionSceneType.CONFERENCE_INVITATION.getSceneType());
                recordArg.setSceneId(invitationEntity.getActivityId());
            }
        }
        
        tryTransferToEnrollConferenceEvent(recordArg);
        tryTransferToEnrollLiveEvent(recordArg);
        if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
            extensionMap.put("fromUserMarketingId", arg.getFromUserMarketingId());
            UserMarketingCrmMemberRelationEntity userMarketingCrmMemberRelationEntity = userMarketingAccountRelationManager.getByEaAndUserMarketingId(ea,arg.getFromUserMarketingId());
            if(userMarketingCrmMemberRelationEntity!=null){
                extensionMap.put("spreadMemberId", userMarketingCrmMemberRelationEntity.getCrmMemberObjectId());
            }
        }
        if (StringUtils.isNotBlank(arg.getClient())) {
            extensionMap.put("client", arg.getClient());
        }
        if (StringUtils.isNotBlank(arg.getChannelAccountName())) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, arg.getChannelAccountName());
        }

        if (StringUtils.isNotBlank(arg.getMarketingSourceType())) {
            extensionMap.put("marketingSourceType", arg.getMarketingSourceType());
        }
        if (StringUtils.isNotBlank(arg.getIpAddress())) {
            extensionMap.put("ipAddress", arg.getIpAddress());
        }
        if (StringUtils.isNotBlank(arg.getChannelAccountPlatform())) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, arg.getChannelAccountPlatform());
        }

        String spreadType = marketingActivityManager.getSpreadType(ea, arg.getMarketingActivityId());
        if (spreadType != null) {
            extensionParams.put("spreadType", spreadType);
        }
        // 营销场景
        Integer marketingScene = recordArg.getMarketingScene();
        if (marketingScene == null) {
            marketingScene = marketingEventCommonSettingManager.getMarketingScene(ea, recordArg.getMarketingEventId(), arg.getMarketingActivityId(), arg.getObjectType());
        }
        // 营销场景
        extensionMap.put(RecordActionArg.MARKETING_SCENE_KEY, marketingScene);
        // 推广渠道
        String spreadChannel = arg.getChannelValue();
        if (StringUtils.isBlank(spreadChannel)) {
            spreadChannel = getSpreadChannel(recordArg.getChannelType(), arg.getUserAgent());
        }
        extensionMap.put(RecordActionArg.SPREAD_CHANNEL_KEY, spreadChannel);
        // 如果是浏览微页面 并且市场活动是广告的，那么就actionType改成 访问广告落地页
        updateActionTypeWhenIsAdPage(recordArg);
        fillChannelAccount(arg, marketingScene);
        fillCtaContent(ea, extensionMap);
       /* if (filterAction(recordArg)) {
            log.warn("KisActionServiceImpl.record is look up action oneself recordArg:{}", recordArg);
            return Result.newSuccess();
        }*/
        if (recordArg.getChannelType() != null && recordArg.getChannelType().equals(MarketingUserActionChannelType.MANKEEP.getChannelType()) && StringUtils.isNotEmpty(recordArg.getMarketingActivityId()) && ActionTypeEnum.isForwardActon(recordArg.getActionType())) {
            if (recordArg.getSpreadFsUid() == null) {
                recordArg.setSpreadFsUid(qywxUserManager.convertOldUserId(ea, recordArg.getFsUserId()));
            } else {
                recordArg.setSpreadFsUid(qywxUserManager.convertOldUserId(ea, recordArg.getSpreadFsUid()));
            }
            String uid = qywxUserManager.getUidByFsUserInfo(ea, recordArg.getSpreadFsUid());
            if (uid != null) {
                ActionVO targetActionVO = new ActionVO();
                targetActionVO.setSourceUid(uid);
                targetActionVO.setTargetUid(uid);
                targetActionVO.setForwarderUid(uid);
                targetActionVO.setActionType(recordArg.getActionType());
                targetActionVO.setObjectId(recordArg.getObjectId());
                targetActionVO.setObjectType(recordArg.getObjectType());
                targetActionVO.setActionTime(new Date());
                targetActionVO.setFeedKey(recordArg.getFeedKey());
                targetActionVO.setMarketingActivityId(recordArg.getMarketingActivityId());
                targetActionVO.setSpreadFsUid(recordArg.getSpreadFsUid());
                targetActionVO.setSceneId(recordArg.getSceneId());
                targetActionVO.setSceneType(recordArg.getSceneType());
                targetActionVO.setFsEa(ea);
                targetActionVO.setSourceType(recordArg.getSourceType());
                targetActionVO.setMarketingEventId(recordArg.getMarketingEventId());
                targetActionVO.setExtensionParams(recordArg.getExtensionParams());
                ModelResult<Void> modelResult = actionService.record(targetActionVO);
                if (!modelResult.isSuccess()) {
                    log.info("KisActionServiceImpl record failed, arg={}, modelResult={}", arg, modelResult);
                    return new Result<>(SHErrorCode.SYSTEM_ERROR);
                }
            } else {
                actionManager.sendMarketingActivityActionToMq(ea, recordArg);
            }
        } else {
            actionManager.sendMarketingActivityActionToMq(ea, recordArg);
        }
        // 如果该行为不需要记录访问时间和浏览深度，不要返回该字段
        if (!MarketingUserActionType.isRecordActionTimeAndProportion(recordArg.getActionType())) {
            primaryId = null;
        }
        return Result.newSuccess(Collections.singletonMap("primaryId", primaryId));
    }

    private void fillCtaContent(String ea, Map<String, Object> extensionMap) {
        if(extensionMap != null && extensionMap.containsKey("ctaId")) {
            CtaEntity ctaEntity = ctaDAO.queryCtaDetail(ea, String.valueOf(extensionMap.get("ctaId")));
            if(ctaEntity != null) {
                extensionMap.put("ctaName", ctaEntity.getName());
            }
        }
    }

    private boolean checkRepetitionAction(MiniAppRecordArg recordArg) {
        // 浏览相关的  一分钟重复浏览之内不上报
        if (!MarketingUserActionType.isLookUpAction(recordArg.getActionType()) && recordArg.getActionType() != ActionTypeEnum.PLAY_VIDEO.getAction()) {
            return false;
        }
        // 如果是更新浏览进度和时间的  不限制
        if (recordArg.getExtensionParams().containsKey("primaryId")) {
            return false;
        }
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(recordArg.getFsEa());
        associationArg.setType(ChannelEnum.MINIAPP.getType());
        associationArg.setAssociationId(recordArg.getUid());
        associationArg.setTriggerAction("miniAppRecord");
        associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
        String userMarketingId = associationResult == null ? null : associationResult.getUserMarketingAccountId();
        return checkRepetitionAction(recordArg.getFsEa(), recordArg.getActionType(), recordArg.getObjectType(), recordArg.getObjectId(), userMarketingId);
    }
    private boolean checkRepetitionAction(RecordActionArg recordArg) {
        // 官网的直接放行,在拿到官网真正物料id后再做一次校验
        if (recordArg.getActionType() == ActionTypeEnum.LOOK_UP_WEBSITE.getAction()) {
            return false;
        }
        // 浏览相关的和播放视频  一分钟重复浏览之内不上报
        if (!MarketingUserActionType.isLookUpAction(recordArg.getActionType()) && recordArg.getActionType() != ActionTypeEnum.PLAY_VIDEO.getAction()) {
            return false;
        }
        // 如果是更新浏览进度和时间的  不限制
        Map<String, Object> extensionParams = recordArg.getExtensionParams();
        if (extensionParams.containsKey("primaryId")) {
            return false;
        }
        String userMarketingId = getUserMarketingId(recordArg);
        return checkRepetitionAction(recordArg.getEa(), recordArg.getActionType(), recordArg.getObjectType(), recordArg.getObjectId(), userMarketingId);
    }

    private boolean checkRepetitionAction(String ea, int actionType, Integer objectType, String objectId, String userMarketingId) {

        if (StringUtils.isBlank(userMarketingId)) {
            return false;
        }
        String key = ea + "-" + userMarketingId + "-" + actionType + "-" + objectId;
        key = MD5Util.md5String(key);
        IdempotentRecordEntity idempotentRecordEntity = idempotentRecordManager.getByBusinessId(key);
        boolean isRepetition;
        // 不存在 说明是第一次
        if (idempotentRecordEntity == null) {
            boolean success = idempotentRecordManager.insertRecord(key);
            isRepetition = !success;
        } else {
            long now = System.currentTimeMillis();
            // 当前时间 - 上次时间 > 一分钟了
            if (now - idempotentRecordEntity.getUpdateTime().getTime() > 1000 * 60) {
                boolean updateSuccess = idempotentRecordManager.updateTime(key, idempotentRecordEntity.getUpdateTime(), new Date());
                isRepetition = !updateSuccess;
            } else {
                isRepetition = true;
            }
        }
        if (isRepetition) {
            log.info("一分钟内重复浏览,ea: {} userMarketingId: {} objectType: {} objectId: {} actionType: {}", ea, userMarketingId, objectType, objectId, actionType);
        }
        return isRepetition;
    }

    private String getUserMarketingId(RecordActionArg recordArg) {
        if (recordArg.getChannelType() == null) {
            return null;
        }
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(recordArg.getEa());
        associationArg.setPhone(recordArg.getPhone());
        if (recordArg.getChannelType().equals(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType())) {
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            associationArg.setAssociationId(recordArg.getWxOpenId());
            associationArg.setWxAppId(recordArg.getWxAppId());
        } else if (recordArg.getChannelType().equals(MarketingUserActionChannelType.H5.getChannelType())
                || recordArg.getChannelType().equals(MarketingUserActionChannelType.EMAIL.getChannelType())
                || recordArg.getChannelType().equals(MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType())
                || recordArg.getChannelType().equals(MarketingUserActionChannelType.SMS.getChannelType()) ) {
            associationArg.setAssociationId(recordArg.getFingerPrint());
            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
        } else {
            return null;
        }
        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
        return associationResult == null ? null : associationResult.getUserMarketingAccountId();
    }

    private void fillChannelAccount(RecordActionArg recordArg, Integer marketingScene) {
        if (marketingScene == null) {
            return;
        }
        Map<String, Object> extensionMap = recordArg.getExtensionParams();
        if (marketingScene == MarketingSceneEnum.LIVE.getCode() && StringUtils.isNotBlank(recordArg.getMarketingEventId())) {
            fillLivePlatformAccount(recordArg.getEa(), recordArg.getMarketingEventId(), extensionMap);
        } else if (marketingScene == MarketingSceneEnum.OFFICIAL_WEBSITE.getCode()) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.OFFICIAL_WEBSITE.getPlatform());
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, recordArg.getWebsiteId());
        } else if (recordArg.getChannelType() != null && StringUtils.isNotBlank(recordArg.getWxAppId())
                && recordArg.getChannelType() == MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType()) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.OFFICIAL_ACCOUNT.getPlatform());
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, recordArg.getWxAppId());
        }
    }

    private void fillLivePlatformAccount(String ea, String marketingEventId, Map<String, Object> extensionMap) {
        if (StringUtils.isBlank(marketingEventId)) {
            return;
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
        if (marketingLive == null || marketingLive.getPlatform() == null) {
           return;
        }
        Integer platform = marketingLive.getPlatform();
        if (LivePlatformEnum.XIAOETONG.getType() == platform) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.XIAO_E_TONG_LIVE.getPlatform());
            XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
            if (xiaoetongAccountEntity != null) {
                extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, xiaoetongAccountEntity.getId());
            }
        } else if (LivePlatformEnum.POLYV.getType() == platform) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.POLYV_LIVE.getPlatform());
            PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
            if (polyvAccountEntity != null) {
                extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, polyvAccountEntity.getId());
            }
        } else if (LivePlatformEnum.CHANNELS.getType() == platform) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.CHANNELS_LIVE.getPlatform());
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, marketingLive.getAssociatedAccountId());

        } else if (LivePlatformEnum.MUDU.getType() == platform) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.MU_DU_LIVE.getPlatform());
            MuduAccountEntity muduAccountEntity = muduAccountDAO.getByEa(ea);
            if (muduAccountEntity != null) {
                extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, muduAccountEntity.getId());
            }
        } else if (LivePlatformEnum.VHALL.getType() == platform) {
            extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.WEI_HOU_LIVE.getPlatform());
            ThirdLiveAccountEntity thirdLiveAccountEntity = vHallManager.getThirdAccount(ea);
            if (thirdLiveAccountEntity != null) {
                extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, thirdLiveAccountEntity.getId());
            }
        }
    }

    private void updateActionTypeWhenIsAdPage(MiniAppRecordArg recordArg) {
        if (recordArg.getObjectType() == null || recordArg.getObjectType() != ObjectTypeEnum.HEXAGON_SITE.getType()) {
            return;
        }
        String marketingEventId = recordArg.getMarketingEventId();
        if (StringUtils.isBlank(marketingEventId) || !adCommonManager.checkMarketingEventIsRelatedAd(recordArg.getFsEa(), marketingEventId)) {
            return;
        }
        recordArg.setActionType(MarketingUserActionType.LOOK_UP_AD_LANDING_PAGE.getActionType());
    }

    private void updateActionTypeWhenIsAdPage(RecordActionArg recordArg) {
        if (recordArg.getObjectType() == null || recordArg.getObjectType() != ObjectTypeEnum.HEXAGON_SITE.getType()) {
            return;
        }
        String marketingEventId = recordArg.getMarketingEventId();
        if (StringUtils.isBlank(marketingEventId) || !adCommonManager.checkMarketingEventIsRelatedAd(recordArg.getEa(), marketingEventId)) {
            return;
        }
        recordArg.setActionType(MarketingUserActionType.LOOK_UP_AD_LANDING_PAGE.getActionType());
    }

    private void recordUtmParam(RecordActionArg arg) {
        // 官网过来的 记录utm参数，之前记录utm参数是单独的接口 现在合并到埋点接口来
        if (arg.isFRecord() || arg.getChannelType() == null || MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType() != arg.getChannelType()
                || StringUtils.isBlank(arg.getEa()) || StringUtils.isBlank(arg.getFingerPrint())) {
            return ;
        }
        RecordUtmParamArg recordUtmParamArg = BeanUtil.copy(arg, RecordUtmParamArg.class);
        recordUtmParamArg.setClient(Integer.valueOf(arg.getClient()));
        recordUtmParamArg.setVisitorId(arg.getFingerPrint());
        redisManager.setUtmParam(recordUtmParamArg.getVisitorId(), recordUtmParamArg);
    }

    private String getSpreadChannel(Integer channelType, String userAgent) {
        if (StringUtils.isNotBlank(userAgent) && userAgent.contains("MicroMessenger")) {
            return String.valueOf(SpreadChannelEnum.WECHAT.getCode());
        }
        if (channelType == null) {
            return String.valueOf(SpreadChannelEnum.OTHER.getCode());
        }
        String spreadChannel;
        if (channelType == MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType()) {
            spreadChannel = String.valueOf(SpreadChannelEnum.WECHAT.getCode());
        } else if (channelType == MarketingUserActionChannelType.SMS.getChannelType()) {
            spreadChannel = String.valueOf(SpreadChannelEnum.SMS.getCode());
        } else if (channelType == MarketingUserActionChannelType.EMAIL.getChannelType()) {
            spreadChannel = String.valueOf(SpreadChannelEnum.EMAIL.getCode());
        } else if (channelType == MarketingUserActionChannelType.AD.getChannelType()) {
            spreadChannel = String.valueOf(SpreadChannelEnum.AD.getCode());
        } else {
            spreadChannel = String.valueOf(SpreadChannelEnum.OTHER.getCode());
        }
        return spreadChannel;
    }

    private void sortByUrlAccuracy(List<OfficialWebsiteTrackEntity> officialWebsiteTrackEntityList) {
        Iterator<OfficialWebsiteTrackEntity> iterator = officialWebsiteTrackEntityList.iterator();
        List<OfficialWebsiteTrackEntity> trackUrlHasStar = new ArrayList<>();
        OfficialWebsiteTrackEntity temp = null;
        while (iterator.hasNext()) {
            temp = iterator.next();
            if (null != temp.getTrackUrl() && temp.getTrackUrl().contains("*")) {
                trackUrlHasStar.add(temp);
                iterator.remove();
            }
        }
        if (trackUrlHasStar.size() > 0) {
            Collections.sort(trackUrlHasStar, (t1, t2) -> {
                int t2Start = sort(t2.getTrackUrl());
                int t1Start = sort(t1.getTrackUrl());
                if (t2Start == t1Start) {
                    return t2.getTrackUrl().length() - t1.getTrackUrl().length();
                }
                return t2Start - t1Start;
            });
        }
        officialWebsiteTrackEntityList.addAll(trackUrlHasStar);
    }

    private int sort(String url) {
        int count = 0;
        while (url.contains("*")) {
            url = url.substring(url.indexOf("*") + 1);
            count++;
        }
        return count;
    }


    public boolean filterAction(RecordActionArg recordArg) {
        return recordArg.getActionType() != null && recordArg.getFsUserId() != null && recordArg.getSpreadFsUid() != null && MarketingUserActionType.isLookUpAction(recordArg.getActionType())
            && recordArg.getFsUserId().equals(recordArg.getSpreadFsUid());
    }
    
    private void tryTransferToEnrollConferenceEvent(RecordActionArg recordActionArg){
        if (!Strings.isNullOrEmpty(recordActionArg.getEa()) && MarketingUserActionType.SUBMIT_FORM.getActionType() ==  recordActionArg.getActionType() && recordActionArg.getSceneType() != null && MarketingUserActionSceneType.ACTIVITY_FORM.getSceneType() == recordActionArg.getSceneType() && !Strings.isNullOrEmpty(recordActionArg.getSceneId())){
            ActivityEntity activityEntity = conferenceDAO.getConferenceById(recordActionArg.getSceneId());
            if (activityEntity != null){
                recordActionArg.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                recordActionArg.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                recordActionArg.setObjectId(activityEntity.getId());
                return;
            }
        }
        if (!Strings.isNullOrEmpty(recordActionArg.getEa()) && MarketingUserActionType.SUBMIT_FORM.getActionType() ==  recordActionArg.getActionType() && !Strings.isNullOrEmpty(recordActionArg.getMarketingEventId()) && !Strings.isNullOrEmpty(recordActionArg.getObjectId())){
            ActivityEntity activityEntity = conferenceDAO.getConferenceByEaAndMarketingEventId(recordActionArg.getEa(), recordActionArg.getMarketingEventId());
            if (activityEntity != null){
                boolean isEnrollFormAction = false;
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(recordActionArg.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
                if (customizeFormDataEntity != null && recordActionArg.getObjectId().equals(customizeFormDataEntity.getId())){
                    isEnrollFormAction = true;
                }
                if (recordActionArg.getSceneType() != null && !Strings.isNullOrEmpty(recordActionArg.getSceneId())){
                    if (contentMarketingEventMaterialRelationDAO.checkIsApplyObject(recordActionArg.getEa(), recordActionArg.getMarketingEventId(), recordActionArg.getSceneId()) > 0) {
                        isEnrollFormAction = true;
                    }
                }
                if (isEnrollFormAction){
                    recordActionArg.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                    recordActionArg.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                    recordActionArg.setObjectId(activityEntity.getId());
                }
            }
        }
    }
    
    private void tryTransferToEnrollLiveEvent(RecordActionArg recordActionArg){
        if (!Strings.isNullOrEmpty(recordActionArg.getEa()) && MarketingUserActionType.SUBMIT_FORM.getActionType() ==  recordActionArg.getActionType() && ObjectTypeEnum.CUSTOMIZE_FORM.getType() == recordActionArg.getObjectType() && !Strings.isNullOrEmpty(recordActionArg.getMarketingEventId()) && !Strings.isNullOrEmpty(recordActionArg.getObjectId())){
            MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(recordActionArg.getEa()), recordActionArg.getMarketingEventId());
            if (marketingLive != null){
                if (recordActionArg.getSceneType() != null && !Strings.isNullOrEmpty(recordActionArg.getSceneId())){
                    if (contentMarketingEventMaterialRelationDAO.checkIsApplyObject(recordActionArg.getEa(), recordActionArg.getMarketingEventId(), recordActionArg.getSceneId()) > 0) {
                        recordActionArg.setActionType(MarketingUserActionType.ENROLL_LIVE.getActionType());
                        recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                        recordActionArg.setObjectId(marketingLive.getId());
                    }
                }
            }
        }
    }

    @Override
    public Result miniAppRecord(MiniAppRecordArg arg) {
        String primaryId = null;
        Map<String, Object> extensionParams = arg.getExtensionParams();

        String ea = arg.getFsEa();
        if (StringUtils.isBlank(ea) && StringUtils.isNotBlank(arg.getObjectId()) && arg.getObjectType() != null) {
            ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
            arg.setFsEa(ea);
        }
        if (extensionParams == null) {
            extensionParams = new HashMap<>();
        } else {
            primaryId = extensionParams.get("primaryId") != null ? extensionParams.get("primaryId").toString() : null;
        }
        if (primaryId == null) {
            primaryId = redisManager.getPrimaryId();
        }
        arg.setExtensionParams(extensionParams);
        String phone = null;
        if (arg.getExtensionParams() != null) {
            Object phoneObj = extensionParams.get("phone");
            phone = phoneObj != null ? phoneObj.toString() : null;
        }
        if (StringUtils.isNotEmpty(arg.getUid())) {
            try {
                AssociationArg associationArg = new AssociationArg();
                associationArg.setEa(ea);
                associationArg.setType(ChannelEnum.MINIAPP.getType());
                associationArg.setAssociationId(arg.getUid());
                if(StringUtils.isNotBlank(phone)) {
                    associationArg.setPhone(phone);
                }
                associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
                associationArg.setTriggerAction("miniAppRecord");
                userMarketingAccountAssociationManager.associate(associationArg);
                UserEntity userEntity = userDAO.queryByUid(arg.getUid());
                if (userEntity != null && StringUtils.isNotEmpty(userEntity.getExternalUserId())) {
                    userMarketingAccountRelationManager.bindMiniappUserAndWxWorkExternalUser(ea, arg.getUid(), userEntity.getExternalUserId(), "miniAppRecord");
                }
            } catch (Exception ignor) {

            }
        }

        // 浏览相关的  一分钟重复浏览之内不上报
        if (checkRepetitionAction(arg)) {
            return Result.newSuccess();
        }
        if (!TextUtil.checkIdIsLegal(arg.getObjectId())) {
            log.warn("埋点接口，物料id不合法, objectId: {}", arg.getObjectId());
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        extensionParams.put("primaryId", primaryId);

        ActionVO actionVO = BeanUtil.copy(arg, ActionVO.class);
        actionVO.setSourceUid(arg.getUid());
        actionVO.setFsEa(arg.getFsEa());
        // 查询推广者
        if (arg.getSpreadFsUid() != null) {
            arg.setSpreadFsUid(qywxUserManager.convertOldUserId(ea, arg.getSpreadFsUid()));
        }
        if (StringUtils.isNotBlank(arg.getTargetUid()) && arg.getSpreadFsUid() == null) {
            FSBindEntity fsBindEntity;
            if (StringUtils.isBlank(ea)) {
                fsBindEntity = fsBindManager.queryFSBindByUid(arg.getTargetUid());
                if (fsBindEntity == null) {
                   return Result.newError(SHErrorCode.FSBIND_USER_NOFOUND);
                }
                ea = fsBindEntity.getFsEa();
                actionVO.setFsEa(fsBindEntity.getFsEa());
            } else {
                fsBindEntity = fsBindManager.queryFSBindByUidAndEa(arg.getTargetUid(), ea);
            }
            if (fsBindEntity != null) {
                actionVO.setSpreadFsUid(fsBindEntity.getFsUserId());
            }
        }
        if (StringUtils.isNotBlank(arg.getMarketingActivityId()) && StringUtils.isBlank(arg.getMarketingEventId())) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getMarketingActivityId());
            if (marketingActivityExternalConfigEntity != null && StringUtils.isNotBlank(marketingActivityExternalConfigEntity.getMarketingEventId())) {
                actionVO.setMarketingEventId(marketingActivityExternalConfigEntity.getMarketingEventId());
            }
        }
        if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
            extensionParams.put("fromUserMarketingId", arg.getFromUserMarketingId());
            UserMarketingCrmMemberRelationEntity userMarketingCrmMemberRelationEntity = userMarketingAccountRelationManager.getByEaAndUserMarketingId(ea,arg.getFromUserMarketingId());
            if(userMarketingCrmMemberRelationEntity!=null){
                extensionParams.put("spreadMemberId", userMarketingCrmMemberRelationEntity.getCrmMemberObjectId());
            }
        }

        if (StringUtils.isNotBlank(arg.getClient())) {
            extensionParams.put("client", arg.getClient());
        }

        if (StringUtils.isNotBlank(arg.getChannelAccountName())) {
            extensionParams.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, arg.getChannelAccountName());
        }

        if (StringUtils.isNotBlank(arg.getSpreadType())) {
            extensionParams.put("spreadType", arg.getSpreadType());
        }
        String spreadChannel = arg.getChannelValue();
        if (StringUtils.isBlank(spreadChannel)) {
            spreadChannel = String.valueOf(SpreadChannelEnum.WECHAT.getCode());
        }
        extensionParams.put(RecordActionArg.SPREAD_CHANNEL_KEY, spreadChannel);
        int marketingScene = marketingEventCommonSettingManager.getMarketingScene(ea, arg.getMarketingEventId(), arg.getMarketingActivityId(), arg.getObjectType());
        extensionParams.put(RecordActionArg.MARKETING_SCENE_KEY, marketingScene);
        String spreadType = marketingActivityManager.getSpreadType(ea, arg.getMarketingActivityId());
        if (spreadType != null) {
            extensionParams.put("spreadType", spreadType);
        }
        if (marketingScene == MarketingSceneEnum.LIVE.getCode() && StringUtils.isNotBlank(arg.getMarketingEventId())) {
            fillLivePlatformAccount(ea, arg.getMarketingEventId(), extensionParams);
        }
        // 如果是浏览微页面 并且市场活动是广告的，那么就actionType改成 访问广告落地页
        updateActionTypeWhenIsAdPage(arg);
        fillCtaContent(ea, extensionParams);
        //转换
//        tryTransferMiniAppToEnrollConferenceEvent(arg,actionVO);
//        tryTransferMiniAppToEnrollLiveEvent(arg,actionVO);
        ModelResult modelResult = miniAppActionService.record(actionVO);
        if(arg.getExtensionParams() != null && arg.getExtensionParams().containsKey("ctaId") && StringUtils.isNotBlank(String.valueOf(arg.getExtensionParams().get("ctaId")))) {
            CtaEntity ctaEntity = ctaDAO.queryCtaDetail(ea, String.valueOf(arg.getExtensionParams().get("ctaId")));
            if(ctaEntity != null) {
                arg.getExtensionParams().put("ctaName", ctaEntity.getName());
            }
            RecordActionArg recordActionArg = BeanUtil.copy(arg, RecordActionArg.class);
            if(StringUtils.isNotEmpty(arg.getUid())) {
                recordActionArg.setChannelType(MarketingUserActionChannelType.MANKEEP.getChannelType());
            } else {
                recordActionArg.setChannelType(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType());
            }
            recordActionArg.setWxAppId(arg.getAppId());
            recordActionArg.setWxOpenId(arg.getOpenid());
            recordActionArg.setUid(arg.getUid());
            actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
        }
        if (!modelResult.isSuccess()) {
            log.warn("KisActionServiceImpl.modelResult error arg:{}", arg);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        // 如果该行为不需要记录访问时间和浏览深度，不要返回该字段
        if (!MarketingUserActionType.isRecordActionTimeAndProportion(actionVO.getActionType())) {
            primaryId = null;
        }
        return Result.newSuccess(Collections.singletonMap("primaryId", primaryId));
    }

    private void tryTransferMiniAppToEnrollConferenceEvent(MiniAppRecordArg miniAppRecordArg,ActionVO actionVO){
        String ea = objectManager.getObjectEa(miniAppRecordArg.getObjectId(), miniAppRecordArg.getObjectType());
        if (MarketingUserActionType.SUBMIT_FORM.getActionType() ==  miniAppRecordArg.getActionType() && miniAppRecordArg.getSceneType() != null && MarketingUserActionSceneType.ACTIVITY_FORM.getSceneType() == miniAppRecordArg.getSceneType() && !Strings.isNullOrEmpty(miniAppRecordArg.getSceneId())){
            ActivityEntity activityEntity = conferenceDAO.getConferenceById(miniAppRecordArg.getSceneId());
            if (activityEntity != null){
                actionVO.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                actionVO.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                actionVO.setObjectId(activityEntity.getId());
                return;
            }
        }
        if (!Strings.isNullOrEmpty(ea) && MarketingUserActionType.SUBMIT_FORM.getActionType() ==  miniAppRecordArg.getActionType() && !Strings.isNullOrEmpty(miniAppRecordArg.getMarketingEventId()) && !Strings.isNullOrEmpty(miniAppRecordArg.getObjectId())){
            ActivityEntity activityEntity = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, miniAppRecordArg.getMarketingEventId());
            if (activityEntity != null){
                boolean isEnrollFormAction = false;
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(ea, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
                if (customizeFormDataEntity != null && miniAppRecordArg.getObjectId().equals(customizeFormDataEntity.getId())){
                    isEnrollFormAction = true;
                }
                if (miniAppRecordArg.getSceneType() != null && !Strings.isNullOrEmpty(miniAppRecordArg.getSceneId())){
                    if (contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, miniAppRecordArg.getMarketingEventId(), miniAppRecordArg.getSceneId()) > 0) {
                        isEnrollFormAction = true;
                    }
                }
                if (isEnrollFormAction){
                    actionVO.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                    actionVO.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                    actionVO.setObjectId(activityEntity.getId());
                }
            }
        }
    }

    private void tryTransferMiniAppToEnrollLiveEvent(MiniAppRecordArg miniAppRecordArg,ActionVO actionVO){
        String ea = objectManager.getObjectEa(miniAppRecordArg.getObjectId(), miniAppRecordArg.getObjectType());
        if (!Strings.isNullOrEmpty(ea) && MarketingUserActionType.SUBMIT_FORM.getActionType() ==  miniAppRecordArg.getActionType() && ObjectTypeEnum.CUSTOMIZE_FORM.getType() == miniAppRecordArg.getObjectType() && !Strings.isNullOrEmpty(miniAppRecordArg.getMarketingEventId()) && !Strings.isNullOrEmpty(miniAppRecordArg.getObjectId())){
            MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), miniAppRecordArg.getMarketingEventId());
            if (marketingLive != null){
                if (miniAppRecordArg.getSceneType() != null && !Strings.isNullOrEmpty(miniAppRecordArg.getSceneId())){
                    if (contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, miniAppRecordArg.getMarketingEventId(), miniAppRecordArg.getSceneId()) > 0) {
                        actionVO.setActionType(MarketingUserActionType.ENROLL_LIVE.getActionType());
                        actionVO.setObjectType(ObjectTypeEnum.LIVE.getType());
                        actionVO.setObjectId(marketingLive.getId());
                    }
                }
            }
        }
    }

    @Override
    public Result generateID() {
        return Result.newSuccess(redisManager.getPrimaryId());
    }

    @Override
    public Result<Void> updateLookUpStatus(String uid,Integer fsUserId,String ea,String marketingActivityId) {
        if (fsUserId == null) {
            if (StringUtils.isBlank(uid)) {
                log.warn("KisActionServiceImpl.updateLookUpStatus fsUserId is null and uid is null");
                return Result.newSuccess();
            }
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
            if (fsBindEntity == null) {
                log.warn("KisActionServiceImpl.updateLookUpStatus fsBindEntity is null");
                return Result.newSuccess();
            }
            fsUserId = fsBindEntity.getFsUserId();
        }
        spreadTaskManager.updateSpreadLookUpById(ea,fsUserId,marketingActivityId);
        return Result.newSuccess();
    }
}
