package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.SmsVerCodeSettingEntity;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * @description 短信验证码设置
 */
public interface SmsVerCodeSettingDao {

    @Insert("INSERT INTO sms_vercode_setting " +
            "(id, ea, domestic_provider_id, international_provider_id, create_time, update_time) " +
            "values " +
            "(#{obj.id}, #{obj.ea}, #{obj.domesticProviderId}, #{obj.internationalProviderId}, now(), now()) ")
    int insert(@Param("obj") SmsVerCodeSettingEntity entity);

    @Update("UPDATE sms_vercode_setting SET domestic_provider_id=#{obj.domesticProviderId}, international_provider_id=#{obj.internationalProviderId}, update_time=now() WHERE id=#{obj.id}")
    int update(@Param("obj") SmsVerCodeSettingEntity entity);

    @Select("SELECT * FROM sms_vercode_setting WHERE ea = #{ea} order by update_time desc limit 1")
    SmsVerCodeSettingEntity getByEa(@Param("ea") String ea);
}
