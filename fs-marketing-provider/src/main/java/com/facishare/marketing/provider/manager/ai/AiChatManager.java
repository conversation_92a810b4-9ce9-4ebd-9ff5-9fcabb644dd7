package com.facishare.marketing.provider.manager.ai;

import com.alibaba.fastjson.JSONObject;
import com.facishare.common.UidUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.ai.AiChatRecordResult;
import com.facishare.marketing.api.vo.ai.AiChatRecordCreateVO;
import com.facishare.marketing.api.vo.ai.ChatCompleteVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.sse.RedisMQ;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.remote.FieldDescribeService;
import com.facishare.marketing.provider.remote.arg.AssignRecordArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.sharegpt.manager.AutoGPTChatContext;
import com.facishare.marketing.provider.sharegpt.manager.AutoGPTChatManager;
import com.facishare.marketing.provider.sharegpt.store.chat.manager.AIChatObjManager;
import com.facishare.marketing.provider.sharegpt.store.chat.obj.AIChatRecordObj;
import com.facishare.marketing.provider.sharegpt.store.chat.obj.AIChatSessionObj;
import com.facishare.marketing.provider.sharegpt.utils.ObjectDataUtil;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;

@Slf4j
@Component
public class AiChatManager {
    @Autowired
    private AIChatObjManager aiChatObjManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private AutoGPTChatManager autoGPTChatManager;
    @Autowired
    private MergeJedisCmd jedisCmd;
    @Autowired
    private FieldDescribeService fieldDescribeService;
    @Autowired
    private EIEAConverter eieaConverter;

    private static final Integer SYSTEM_USER = -100;
    private static final String SESSION_LOCK_PREFIX = "MARKETING_AI_CHAT_COMPLETE_LOCK_";
    private static final String CHAT_COMPLETE_RESULT_PREFIX = "MARKETING_AI_CHAT_COMPLETE_RESULT_";
    private static final long CHAT_COMPLETE_RESULT_EXPIRE_TIME = 1000 * 60 * 5;
    private static final long SESSION_LOCK_EXPIRE_TIME = 100L;

    private static final int DEFAULT_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 4;
    public static ExecutorService executor = NamedThreadPool.newFixedThreadPool(DEFAULT_POOL_SIZE, "CHAT_COMPLETE_POOL");

    public AiChatRecordResult getRecord(String ea, Integer fsUserId, String id) {
        AIChatRecordObj recordObj = AIChatRecordObj.wrap(aiChatObjManager.getById(ea, AIChatRecordObj.OBJECT_API_NAME, null, id));
        return ObjectDataUtil.objectDataResultMapping(recordObj);
    }

    /**
     * AI对话:
     * temp = true 表示临时会话
     * recordId != null 表示刷新会话结果
     * 其他: ...
     *
     * @param ea
     * @param userId
     * @param vo
     * @return
     */
    public Result chatCompleteWithSession(String ea, Integer userId, ChatCompleteVO vo) {
        String prompt = vo.getPrompt();
        ChatCompleteVO.Property property = vo.getProperty();
        String sessionId = vo.getSessionId();
        String recordId = vo.getRecordId();
        AIChatRecordObj recordObj = null;
        boolean isEdit = false;
        try {
            if (StringUtils.isNotBlank(recordId)) {
                // 如果recordId不为空, 表示重新生成结果
                recordObj = AIChatRecordObj.wrap(aiChatObjManager.getById(ea, AIChatRecordObj.OBJECT_API_NAME, null, recordId));
                if (recordObj.isEmpty()) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
                sessionId = recordObj.getSessionId();
                isEdit = true;
            } else {
                // 否则, 表示新发起会话
                if (StringUtils.isBlank(prompt) && CollectionUtils.isEmpty(property.getContentList())) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
//                if (StringUtils.isBlank(prompt)) {
//                    prompt = property.getContentList().stream().filter(e-> StringUtils.isNotBlank(e.getText())).findFirst().map(ChatCompleteVO.Attachments::getText).orElse(null);
//                }
                sessionId = getOrCreateSession(ea, userId, vo.getSceneId(), vo.getBizSessionId(), sessionId, prompt);
            }
            // 获取会话锁, 执行中不能进行发起会话
            boolean isLock = retryLock(SESSION_LOCK_PREFIX + sessionId);
            if (!isLock) {
                return Result.newError(SHErrorCode.SERVER_BUSY);
            }
            if (StringUtils.isNotBlank(recordId)) {
                String promptRecordId = recordObj.getPromptRecordId();
                if (StringUtils.isNotBlank(promptRecordId)) {
                    AIChatRecordObj promptRecord = AIChatRecordObj.wrap(aiChatObjManager.getById(ea, AIChatRecordObj.OBJECT_API_NAME, null, promptRecordId));
                    prompt = promptRecord.getContent();
                } else {
                    redisManager.delete(SESSION_LOCK_PREFIX + sessionId);
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
                property = recordObj.getProperty();
            } else {
                recordObj = AIChatRecordObj.wrap(aiChatObjManager.add(ea, userId, AIChatRecordObj.OBJECT_API_NAME, new AIChatRecordObj()
                        .setSessionId(sessionId)
                        .setContent(prompt)
                        .setContentType(AiChatContentType.TEXT.getType())
                        .setCardType(autoGPTChatManager.getCardType(null))
                        .setAgentName(vo.getDefaultHelperName())
                        .setSenderId(userId)
                        .setProperty(property)
                ));
                recordId = recordObj.getId();
            }
        } catch (Exception e) {
            log.warn("AI对象存储异常:", e);
            redisManager.delete(SESSION_LOCK_PREFIX + sessionId);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        AiChatRecordResult record = this.getRecord(ea, userId, recordId);
        //提交请求
        chatCompleteWithRecord(ea, userId, sessionId, recordId, prompt, property, isEdit);

        //返回当前会话记录
        return Result.newSuccess(record);
    }

    private String getOrCreateSession(String ea, Integer userId, String sceneId, String bizSessionId, String sessionId, String prompt) {
        // 若只传了 bizSessionId, 先根据 bizSessionId 查询
        if (StringUtils.isBlank(sessionId) && StringUtils.isNotEmpty(bizSessionId)) {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("biz_session_id", OperatorConstants.EQ, Collections.singletonList(bizSessionId));
            query.addOrderByAsc("last_modified_time", false);
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(AIChatSessionObj.OBJECT_API_NAME);
            AIChatSessionObj sessionObj = AIChatSessionObj.wrap(aiChatObjManager.getByFilter(ea, paasQueryFilterArg));
            sessionId = sessionObj.getId();
        }
        // 若sessionId 为空, 则表示会话不存在需要创建, 存在则更新时间用于展示到最上面
        if (StringUtils.isBlank(sessionId)) {
            String title = UUIDUtil.getUUID();
            if (StringUtils.isNotBlank(prompt)) {
                title = StringUtils.substring(prompt, 0, 32);
            }
            AIChatSessionObj sessionObj = new AIChatSessionObj().setTitle(title).setSenderId(userId);
            if (StringUtils.isNotBlank(sceneId)) {
                sessionObj.setSceneId(sceneId);
            }
            if (StringUtils.isNotBlank(bizSessionId)) {
                sessionObj.setBizSessionId(bizSessionId);
            }
            ObjectData objectData = aiChatObjManager.add(ea, userId, AIChatSessionObj.OBJECT_API_NAME, sessionObj);
            sessionId = objectData.getId();
        } else {
            AIChatSessionObj aiChatSessionObj = new AIChatSessionObj().setId(sessionId).setLastModifiedTime(new Date().getTime());
            aiChatObjManager.edit(ea, AIChatSessionObj.OBJECT_API_NAME, aiChatSessionObj);
        }
        return sessionId;
    }

    /**
     * 异步执行chatComplete
     *
     * @param ea
     * @param fsUserId
     * @param sessionId
     * @param currentRecordId
     * @param prompt
     * @param property
     * @param isEdit
     */
    private void chatCompleteWithRecord(String ea, Integer fsUserId,
                                        String sessionId, String currentRecordId,
                                        String prompt, ChatCompleteVO.Property property, boolean isEdit) {
        String channel = currentRecordId;
        if (property.isStreaming()) {
            RedisMQ.publish(jedisCmd, channel, JSONObject.toJSONString(new AiChatRecordResult()));
        }
        executor.submit(MonitorTaskWrapper.wrap(() -> {
            try {
                String templateId = property.getTemplateId();
                String defaultHelperName = property.getDefaultHelperName();
                List<AIChatRecordObj> chatMemories = getChatHistoryRecord(ea, sessionId, currentRecordId, templateId, defaultHelperName);
                property.setSessionId(sessionId);
                property.setChannel(channel);
                AutoGPTChatContext chatContext = autoGPTChatManager.autoGPTChat(ea, fsUserId, property, chatMemories, prompt);
                // 异常中断
                if (chatContext.isError()) {
                    chatContext.setContentType(AiChatContentType.TEXT.getType());
                    chatContext.setCardType("ERROR_MSG");
                }
                AIChatRecordObj recordObj = new AIChatRecordObj()
                        .setSessionId(sessionId)
                        .setTips(chatContext.getTips())
                        .setContent(chatContext.getContent())
                        .setContentType(chatContext.getContentType())
                        .setCardType(chatContext.getCardType())
                        .setAction(chatContext.getAction())
                        .setActions(chatContext.getActions())
                        .setData(chatContext.getData())
                        .setInstanceId(chatContext.getInstanceId())
                        .setProcess(chatContext.getProcess())
                        .setAgentName(defaultHelperName)
                        .setSenderId(SYSTEM_USER)
                        .setDisplayData(chatContext.getDisplayData())
                        .setActionType(chatContext.getActionType())
                        .setOriginalData(chatContext.getOriginalData())
                        .setPromptRecordId(currentRecordId);
                if (isEdit) {
                    recordObj = AIChatRecordObj.wrap(aiChatObjManager.edit(ea, AIChatRecordObj.OBJECT_API_NAME, recordObj));
                } else {
                    recordObj = AIChatRecordObj.wrap(aiChatObjManager.add(ea, fsUserId, AIChatRecordObj.OBJECT_API_NAME, recordObj));
                }
                if (property.isStreaming()) {
                    AiChatRecordResult aiChatRecordResult = ObjectDataUtil.objectDataResultMapping(recordObj);
                    aiChatRecordResult.setHasFollowUpQuestion(StringUtils.isNotBlank(chatContext.getFollowUp()));

                    String followUpQuestion = autoGPTChatManager.followUpChat(chatContext);
                    if (StringUtils.isNotBlank(followUpQuestion)) {
                        aiChatRecordResult.setFollowUpQuestion(followUpQuestion);
                    }
                    aiChatRecordResult.setFinish(true);
                    RedisMQ.publish(jedisCmd, channel, JSONObject.toJSONString(aiChatRecordResult));
                }
            } catch (Exception e) {
                log.warn("AiChatManager.chatCompleteWithRecord error", e);
                if (property.isStreaming()) {
                    AiChatRecordResult aiChatRecordResult = new AiChatRecordResult();
                    aiChatRecordResult.setFinish(true);
                    aiChatRecordResult.setContent(e.getMessage());
                    RedisMQ.publish(jedisCmd, channel, JSONObject.toJSONString(aiChatRecordResult));
                }
            } finally {
                if (StringUtils.isNotBlank(sessionId)) {
                    redisManager.delete(SESSION_LOCK_PREFIX + sessionId);
                }
            }
        }));
    }

    private List<AIChatRecordObj> getChatHistoryRecord(String ea, String sessionId, String currentRecordId, String templateId, String defaultHelperName) {
        List<AIChatRecordObj> chatMemories = Lists.newArrayList();
        if (StringUtils.isNotEmpty(defaultHelperName) && defaultHelperName.startsWith("Copilot_")) {
            return chatMemories;
        }
        if (StringUtils.isNotEmpty(sessionId) && StringUtils.isNotEmpty(currentRecordId) && StringUtils.isBlank(templateId)) {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("session_id", OperatorConstants.EQ, Collections.singletonList(sessionId));
            query.addFilter("card_type", OperatorConstants.N, Collections.singletonList("ERROR_MSG"));
            if (StringUtils.isNotBlank(currentRecordId)) {
                query.addFilter("_id", OperatorConstants.LT, Collections.singletonList(currentRecordId));
            }
            query.addOrderByAsc("_id", true);
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(AIChatRecordObj.OBJECT_API_NAME);
            List<ObjectData> objectData = aiChatObjManager.listAllByFilter(ea, paasQueryFilterArg);
            List<AIChatRecordObj> aiChatRecordList = AIChatRecordObj.wrap(objectData);
            if (CollectionUtils.isNotEmpty(aiChatRecordList)) {
                for (int i = aiChatRecordList.size() - 1; i >= 0; i--) {
                    AIChatRecordObj aiChatRecord = aiChatRecordList.get(i);
                    if (StringUtils.isEmpty(aiChatRecord.getContent())) {
                        continue;
                    }
                    if (StringUtils.isBlank(aiChatRecord.getAgentName())) {
                        chatMemories.add(aiChatRecord);
                    } else if (StringUtils.equals(aiChatRecord.getAgentName(), defaultHelperName)) {
                        chatMemories.add(aiChatRecord);
                    } else {
                        break;
                    }
                }
            }
            chatMemories.sort(Comparator.comparing(AIChatRecordObj::getCreateTime));
        }
        return chatMemories;
    }


    /**
     * 可重试分布式锁, 重试次数:3
     *
     * @param sessionLockKey
     * @return
     */
    private boolean retryLock(String sessionLockKey) {
        String uid = UidUtil.getUid();
        boolean isLock = redisManager.set(sessionLockKey, uid, Long.valueOf(SESSION_LOCK_EXPIRE_TIME).intValue());
        int lockCount = 3;
        while (!isLock && lockCount > 0) {
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                Thread.interrupted();
                log.warn("AiChatManager.chatCompleteWithRecord retryLock InterruptedException");
            }
            isLock = redisManager.set(sessionLockKey, uid, Long.valueOf(SESSION_LOCK_EXPIRE_TIME).intValue());
            lockCount--;
        }
        return isLock;
    }

    public Result chatCompleteWithoutSession(String ea, Integer fsUserId, ChatCompleteVO vo) {
        String chatCompleteResultId = UUIDUtil.getUUID();
        ChatCompleteVO.Property property = vo.getProperty();
        if (property.isStreaming()) {
            RedisMQ.publish(jedisCmd, chatCompleteResultId, JSONObject.toJSONString(new AiChatRecordResult()));
        }
        //提交请求
        executor.submit(MonitorTaskWrapper.wrap(() -> {
            try {
                property.setChannel(chatCompleteResultId);
                AutoGPTChatContext chatContext = autoGPTChatManager.autoGPTChat(ea, fsUserId, property, null, vo.getPrompt());

                if (chatContext.isError()) {
                    chatContext.setContentType(AiChatContentType.TEXT.getType());
                    chatContext.setCardType("ERROR_MSG");
                }
                AIChatRecordObj recordObj = new AIChatRecordObj()
                        .setTips(chatContext.getTips())
                        .setContent(chatContext.getContent())
                        .setContentType(chatContext.getContentType())
                        .setCardType(chatContext.getCardType())
                        .setAction(chatContext.getAction())
                        .setActions(chatContext.getActions())
                        .setData(chatContext.getData())
                        .setInstanceId(chatContext.getInstanceId())
                        .setProcess(chatContext.getProcess())
                        .setSenderId(SYSTEM_USER)
                        .setDisplayData(chatContext.getDisplayData())
                        .setActionType(chatContext.getActionType())
                        .setOriginalData(chatContext.getOriginalData());
                AiChatRecordResult aiChatRecordResult = ObjectDataUtil.objectDataResultMapping(recordObj);
                String chatCompleteResultKey = CHAT_COMPLETE_RESULT_PREFIX + chatCompleteResultId;
                redisManager.set(chatCompleteResultKey, GsonUtil.toJson(aiChatRecordResult), Long.valueOf(CHAT_COMPLETE_RESULT_EXPIRE_TIME).intValue());
                // 异常中断
                if (chatContext.isError()) {
                    chatContext.setContentType(AiChatContentType.TEXT.getType());
                    chatContext.setCardType("ERROR_MSG");
                }
                if (property.isStreaming()) {
                    aiChatRecordResult.setHasFollowUpQuestion(StringUtils.isNotBlank(chatContext.getFollowUp()));
                    String followUpQuestion = autoGPTChatManager.followUpChat(chatContext);
                    if (StringUtils.isNotBlank(followUpQuestion)) {
                        aiChatRecordResult.setFollowUpQuestion(followUpQuestion);
                    }
                    aiChatRecordResult.setFinish(true);
                    RedisMQ.publish(jedisCmd, chatCompleteResultId, JSONObject.toJSONString(aiChatRecordResult));
                }
            } catch (Exception e) {
                log.warn("AiChatManager.chatCompleteWithRecord error", e);
                if (property.isStreaming()) {
                    AiChatRecordResult aiChatRecordResult = new AiChatRecordResult();
                    aiChatRecordResult.setFinish(true);
                    aiChatRecordResult.setContent(e.getMessage());
                    RedisMQ.publish(jedisCmd, chatCompleteResultId, JSONObject.toJSONString(aiChatRecordResult));
                }
            }
        }));
        return com.facishare.marketing.common.result.Result.newSuccess(chatCompleteResultId);
    }

    public Result getChatCompleteResult(String ea, Integer fsUserId, String id) {
        String chatCompleteResultKey = CHAT_COMPLETE_RESULT_PREFIX + id;
        String chatCompleteResult = redisManager.get(chatCompleteResultKey);
        if (StringUtils.isNotBlank(chatCompleteResult)) {
            try {
                AiChatRecordResult aiChatRecordResult = GsonUtil.fromJson(chatCompleteResult, AiChatRecordResult.class);
                return com.facishare.marketing.common.result.Result.newSuccess(aiChatRecordResult);
            } catch (Exception e) {
            }
        }
        return com.facishare.marketing.common.result.Result.newSuccess();
    }

    public String createRecord(String ea, Integer fsUserId, AiChatRecordCreateVO vo) {
        String sessionId = getOrCreateSession(ea, fsUserId, null, vo.getBizSessionId(), vo.getSessionId(), vo.getContent());
        ObjectData objectData = aiChatObjManager.add(ea, fsUserId, AIChatRecordObj.OBJECT_API_NAME, new AIChatRecordObj()
                .setSessionId(sessionId)
                .setTips(vo.getTips())
                .setContent(vo.getContent())
                .setContentType(vo.getContentType())
                .setCardType(vo.getCardType())
                .setAction(vo.getAction())
                .setActions(vo.getActions())
                .setData(vo.getData())
                .setSenderId(SYSTEM_USER)
                .setDisplayData(vo.getDisplayData())
                .setActionType(vo.getActionType())
                .setOriginalData(vo.getOriginalData())
        );
        return objectData.getId();
    }

    public void assignRecord(String ea) {
        AssignRecordArg arg = new AssignRecordArg(
                "AIChatRecordObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"00000000000000000000000000000006\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
        arg = new AssignRecordArg(
                "AIChatSessionObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"00000000000000000000000000000006\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
        arg = new AssignRecordArg(
                "AIChatShareObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"00000000000000000000000000000006\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
        arg = new AssignRecordArg(
                "AIHelperObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"00000000000000000000000000000006\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
        arg = new AssignRecordArg(
                "AIToolsObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"00000000000000000000000000000006\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
        arg = new AssignRecordArg(
                "AISceneObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"00000000000000000000000000000006\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
        arg = new AssignRecordArg(
                "PromptBookmarkObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"personnelrole\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
        arg = new AssignRecordArg(
                "PromptObj",
                "[{\"default_record\":\"default__c\",\"roleCode\":\"personnelrole\",\"records\":[\"default__c\"]}]"
        );
        try {
            fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        } catch (Exception e) {
            log.warn("assignRecord error apiName:{}", arg.getDescribeApiName());
        }
    }
}
