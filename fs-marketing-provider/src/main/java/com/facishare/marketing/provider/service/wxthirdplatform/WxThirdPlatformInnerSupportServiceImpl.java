package com.facishare.marketing.provider.service.wxthirdplatform;

import com.facishare.marketing.common.dbroute.TenantService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.DBRoutUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.CloudContextUtil;
import com.facishare.marketing.api.arg.CommitCodeAndSubmitAuditArg;
import com.facishare.marketing.api.arg.DispatchRequestArg;
import com.facishare.marketing.api.result.DispatchRequestResult;
import com.facishare.marketing.api.result.QueryWxMiniListResult;
import com.facishare.marketing.api.result.WxCodeTemplateResult;
import com.facishare.marketing.api.result.WxTemplateVersionResult;
import com.facishare.marketing.api.result.WxThirdPlatformDomainResult;
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdCloudInnerService;
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdPlatformInnerSupportService;
import com.facishare.marketing.common.enums.MiniappReleaseStatusEnum;
import com.facishare.marketing.common.enums.WxMiniSearchTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.MiniappReleaseRecordDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatEaBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatThirdPlatformConfigDao;
import com.facishare.marketing.provider.dto.MiniappReleaseRecordEaAndWxAppIdDTO;
import com.facishare.marketing.provider.entity.MiniappReleaseRecordEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatThirdPlatformConfigEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WxMiniAppEntity;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.SettingManager;
import com.facishare.marketing.provider.manager.miniAppSetting.MiniAppSettingManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WxCloudRestManager;
import com.facishare.marketing.provider.service.SettingServiceImpl.ExtAppInfo;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.wechatrestapi.arg.CommitCodeArg;
import com.fxiaoke.wechatrestapi.arg.GetAuditStatusArg;
import com.fxiaoke.wechatrestapi.arg.SubmitAuditArg;
import com.fxiaoke.wechatrestapi.result.GetAuditStatusResult;
import com.fxiaoke.wechatrestapi.result.GetCodeTemplateListResult;
import com.fxiaoke.wechatrestapi.result.GetCodeTemplateListResult.CodeTemplate;
import com.fxiaoke.wechatrestapi.result.ModifyDomainResult;
import com.fxiaoke.wechatrestapi.result.SetWebViewDomainResult;
import com.fxiaoke.wechatrestapi.result.SubmitAuditResult;
import com.fxiaoke.wechatrestapi.result.WechatBaseResult;
import com.fxiaoke.wechatrestapi.service.miniapp.CodeManageService;
import com.fxiaoke.wechatrestapi.service.miniapp.CodeTemplateService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.mongodb.DB;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * Created on 2020-12-14.
 */
@Service("wxThirdPlatformInnerSupportService")
@Slf4j
public class WxThirdPlatformInnerSupportServiceImpl implements WxThirdPlatformInnerSupportService {
	@Autowired
	private EaWechatAccountBindDao eaWechatAccountBindDao;
	@Autowired
	private WechatThirdPlatformConfigDao wechatThirdPlatformConfigDao;
	@Autowired
	private WechatThirdPlatformManager wechatThirdPlatformManager;
	@Autowired
	private WechatAccountConfigDao wechatAccountConfigDao;
	@Autowired
	private WechatAccountManager wechatAccountManager;
	@Autowired
	private HttpManager httpManager;
	@Autowired
	private EIEAConverter eieaConverter;
	@Autowired
	private EnterpriseEditionService enterpriseEditionService;
	@Autowired
	private MiniAppSettingManager miniAppSettingManager;
	@Autowired
	private MiniappReleaseRecordDao miniappReleaseRecordDao;
	@Autowired
	private WxCloudRestManager wxCloudRestManager;
	@Autowired
	private SettingManager settingManager;
	@Autowired
	private CodeTemplateService codeTemplateService;
	@Autowired
	private CodeManageService codeManageService;
	@Autowired
	private RedisManager redisManager;
	@Autowired
	private WxThirdCloudInnerService wxThirdCloudInnerService;
	@Autowired
	private OpenAppAdminService openAppAdminService;
	@Autowired
	private WechatEaBindDao wechatEaBindDao;
	@ReloadableProperty("host")
	private String host;
	@ReloadableProperty("marketing_appid")
	private String marketingAppId;

	private static final String TO_REPLACE_WX_COMPONENT_APP_ID = "${wxComponentAppId}";
	private static final String TO_REPLACE_WX_COMPONENT_ACCESS_TOKEN = "${wxComponentAccessToken}";
	private static final String TO_REPLACE_WX_APP_ACCESS_TOKEN = "${wxAppAccessToken}";
    @Autowired
    private TenantService tenantService;

	@Override
	public Result<String> getWxAppIdByFSPlatformIdAndEa(String fsPlatformId, String ea) {
		Preconditions.checkArgument(!Strings.isNullOrEmpty(fsPlatformId));
		Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
		String wxAppId = eaWechatAccountBindDao.getWxAppIdByEa(ea, fsPlatformId);
		if (com.google.common.base.Strings.isNullOrEmpty(wxAppId)){
			return Result.newError(SHErrorCode.EA_NOT_BIND_TO_MINIAPP);
		}
		return Result.newSuccess(wxAppId);
	}

	@Override
	public Result<String> getEaByFSPlatformIdAndWxAppId(String fsPlatformId, String wxAppId) {
		Preconditions.checkArgument(!Strings.isNullOrEmpty(fsPlatformId));
		Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
		List<String> eas = Lists.newArrayList();
		tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
			List<String> eaList = eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(fsPlatformId, wxAppId, dbRouteEa);
			eas.addAll(eaList);
		});

		if (eas.isEmpty()){
			return Result.newError(SHErrorCode.EA_NOT_BIND_TO_MINIAPP);
		}
		if (eas.size() > 1){
			throw new IllegalStateException("One miniapp bind to many ea, wxAppId" + wxAppId);
		}
		return Result.newSuccess(eas.get(0));
	}

	@Override
	public Result<DispatchRequestResult> dispatchRequest(String fsPlatformId, String wxAppId, DispatchRequestArg dispatchRequestArg) {
		Preconditions.checkArgument(!Strings.isNullOrEmpty(fsPlatformId));
		Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
		Preconditions.checkArgument(dispatchRequestArg != null);
		Preconditions.checkArgument("GET".equalsIgnoreCase(dispatchRequestArg.getRequestMethod()) || "POST".equalsIgnoreCase(dispatchRequestArg.getRequestMethod()));
		Preconditions.checkArgument(!Strings.isNullOrEmpty(dispatchRequestArg.getRequestUrl()));
		Preconditions.checkArgument("GET".equalsIgnoreCase(dispatchRequestArg.getRequestMethod()) || !Strings.isNullOrEmpty(dispatchRequestArg.getRequestBody()));
		WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = getWechatThirdPlatformConfig(fsPlatformId);
		if (wechatThirdPlatformConfig == null) {
			return Result.newError(SHErrorCode.PARAMS_ERROR);
		}
		if (wechatEaBindDao.getEaByAppId(wxAppId, wechatThirdPlatformConfig.getEa()) == null){
			return Result.newError(SHErrorCode.WX_APP_ID_NOT_AUTH_TO_PLATFORM);
		}

		String requestUrl = replaceProperties(fsPlatformId, wxAppId, wechatThirdPlatformConfig, dispatchRequestArg.getRequestUrl());
		String responseBody;
		if ("GET".equalsIgnoreCase(dispatchRequestArg.getRequestMethod())){
			responseBody = httpManager.executeGetHttpReturnString(requestUrl);
		} else {
			String requestBody = replaceProperties(fsPlatformId, wxAppId, wechatThirdPlatformConfig, dispatchRequestArg.getRequestBody());
			responseBody = httpManager.executePostByPlainBodyHttpReturnString(requestBody, requestUrl);
		}

		return Result.newSuccess(new DispatchRequestResult(responseBody));
	}

	private WechatThirdPlatformConfigEntity getWechatThirdPlatformConfig(String fsPlatformId) {
		for(String dbRouteEa : tenantService.getAllDbRouteEas()) {
			WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(fsPlatformId, dbRouteEa);
			if(wechatThirdPlatformConfig != null){
				return wechatThirdPlatformConfig;
			}
		}
		return null;
	}

	@Override
	public Result<List<QueryWxMiniListResult>> getWxMiniAppList(String fsPlatformId, String keywordType, String keyword) {
		Preconditions.checkArgument(!Strings.isNullOrEmpty(fsPlatformId), I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_193));
		if (!Strings.isNullOrEmpty(keyword)) {
			Preconditions.checkArgument(!Strings.isNullOrEmpty(keywordType) && WxMiniSearchTypeEnum.isValid(keywordType), I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_195));
		}
		List<QueryWxMiniListResult> resultList = Lists.newArrayList();
		for(String dbRouteEa : tenantService.getAllDbRouteEas()) {
			List<WxMiniAppEntity> wxMiniAppEntities = wechatAccountConfigDao.listWxMiniApp(dbRouteEa, fsPlatformId, keywordType, keyword);
			Map<String, String> appIdAndToken = batchGetAppIdTokenMap(wxMiniAppEntities);

			List<String> eaList = wxMiniAppEntities.stream().map(WxMiniAppEntity::getEa).collect(Collectors.toList());
			Map<String, Integer> eaToEiMap = eieaConverter.enterpriseAccountToId(eaList);
			BatchGetEnterpriseDataArg batchGetEnterpriseDataArg = new BatchGetEnterpriseDataArg();
			batchGetEnterpriseDataArg.setEnterpriseAccounts(Lists.newArrayList(eaToEiMap.keySet()));
			batchGetEnterpriseDataArg.setEnterpriseIds(Lists.newArrayList(eaToEiMap.values()));
			BatchGetEnterpriseDataResult batchGetEnterpriseDataResult = enterpriseEditionService.batchGetEnterpriseData(batchGetEnterpriseDataArg);
			Map<String, EnterpriseData> enterpriseDataMap = new HashMap<>();
			if (batchGetEnterpriseDataResult != null && CollectionUtils.isNotEmpty(batchGetEnterpriseDataResult.getEnterpriseDatas())) {
				Map<String, EnterpriseData> tempMap = batchGetEnterpriseDataResult.getEnterpriseDatas().stream().collect(Collectors.toMap(EnterpriseData::getEnterpriseAccount, e -> e, (v1, v2) -> v1));
				enterpriseDataMap.putAll(tempMap);
			}
			List<QueryWxMiniListResult> list = wxMiniAppEntities.stream().map(wxMiniAppEntity -> {
				QueryWxMiniListResult result = new QueryWxMiniListResult();
				BeanUtils.copyProperties(wxMiniAppEntity, result);
				// 公司名称
				String ea = wxMiniAppEntity.getEa();
				EnterpriseData enterpriseData = enterpriseDataMap.get(ea);
				String companyName = enterpriseData != null ? enterpriseData.getEnterpriseName() : "";
				result.setCompanyName(companyName);
				return result;
			}).collect(Collectors.toList());
			// 累计用户数
			Map<String, Long> visitTotalMap = miniAppSettingManager.batchGetVisitTotal(appIdAndToken);
			// 发布状态（失败原因）
			Set<String> appIdSet = appIdAndToken.keySet();
			boolean hasSet = !appIdSet.isEmpty();
			Map<String, Map<String, Object>> endReasonMap;
			if (hasSet) {
				endReasonMap = miniappReleaseRecordDao.batchGetEndReason(dbRouteEa, appIdSet);
				CountDownLatch countDownLatch = new CountDownLatch(list.size());
				for (QueryWxMiniListResult result : list) {
					ThreadPoolUtils.execute(() -> {
						String wxAppId = result.getWxAppId();
						result.setUserCount(visitTotalMap.get(wxAppId));
						Map<String, Object> map = endReasonMap.get(wxAppId);
						if (map != null) {
							Integer status = (Integer) map.get("status");
							String endReason = (String) map.get("end_reason");
							if (status == MiniappReleaseStatusEnum.AUDITING.getStatus()) {
								GetAuditStatusArg getAuditStatusArg = new GetAuditStatusArg();
								getAuditStatusArg.setAuditId((String) map.get("audit_id"));
								GetAuditStatusResult getAuditStatusResult = codeManageService.getAuditStatus(wechatAccountManager.getAccessTokenByWxAppId(result.getEa(), wxAppId), getAuditStatusArg);
								if (getAuditStatusResult.isSuccess()) {
									if (getAuditStatusResult.getStatus() == GetAuditStatusResult.AuditStatus.SUCCESS) {
										miniappReleaseRecordDao.auditSuccess(result.getEa(), wxAppId, new Date());
										status = 4;
										endReason = getAuditStatusResult.getReason();
									}
									if (getAuditStatusResult.getStatus() == GetAuditStatusResult.AuditStatus.REJECTED || getAuditStatusResult.getStatus() == GetAuditStatusResult.AuditStatus.CANCEL) {
										miniappReleaseRecordDao.auditFail(dbRouteEa, wxAppId, getAuditStatusResult.getReason(), GsonUtil.toJson(strToList(getAuditStatusResult.getScreenShot(), "|")), new Date());
										status = 5;
										endReason = getAuditStatusResult.getReason();
									}
								}
							}
							if (StringUtils.isNotEmpty(endReason)) {
								result.setReply(endReason);
							} else {
								result.setReply(MiniappReleaseStatusEnum.getNameByStatus(status));
							}
						}
						countDownLatch.countDown();
					}, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
				}
				try {
					countDownLatch.await(6, TimeUnit.SECONDS);
				} catch (InterruptedException e) {
					log.warn("com.facishare.marketing.provider.service.wxthirdplatform.WxThirdPlatformInnerSupportServiceImpl#getWxMiniAppList.await error:", e);
				}
			}
			resultList.addAll(list);
		}
		return Result.newSuccess(resultList);
	}

	private Map<String, String> batchGetAppIdTokenMap(List<WxMiniAppEntity> wxMiniAppEntityList){
		Map<String, String> appIdTokenMap = new HashMap<>();
		if (CollectionUtils.isEmpty(wxMiniAppEntityList)){
			return appIdTokenMap;
		}

		CountDownLatch countDownLatch = new CountDownLatch(wxMiniAppEntityList.size());
		for (WxMiniAppEntity wxMiniAppEntity : wxMiniAppEntityList){
			ThreadPoolUtils.execute(() -> {
				try {
					String token = getValidAccessToken(wxMiniAppEntity);
					appIdTokenMap.put(wxMiniAppEntity.getWxAppId(), token);
				}finally {
					countDownLatch.countDown();
				}

			}, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
		}
		try {
			countDownLatch.await(12, TimeUnit.SECONDS);
		} catch (InterruptedException e) {
			log.warn("batchGetAppIdTokenMap error:", e);
		}

		return appIdTokenMap;
	}

	@Override
	public Result<String> batchCommitCodeAndSubmitAudit(List<String> appIds, String platformId) {
		if (StringUtils.isEmpty(platformId) || appIds == null || appIds.isEmpty()) {
			return Result.newError(SHErrorCode.PARAMS_ERROR, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_6));
		}
		StringBuffer errorMsg = new StringBuffer();
		Set<String> appIdSet = new HashSet<>(appIds);
		tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
			Map<String, WechatAccountConfigEntity> wechatAccountConfigMap = wechatAccountManager.batchGetWechatAccountConfig(appIdSet, dbRouteEa);
			Map<String, Map<String, Integer>> latestReleaseStatusMap = miniappReleaseRecordDao.batchGetStatus(dbRouteEa, appIdSet);
			WxThirdPlatformDomainResult platformDomain = wxCloudRestManager.getDomainByPlatformId(platformId);
			String thirdPlatformAccessToken = wechatThirdPlatformManager.getThirdPlatformAccessToken(platformId);
			GetCodeTemplateListResult getCodeTemplateListResult = codeTemplateService.getTemplateList(thirdPlatformAccessToken);
			Map<String, Object> extTopMap = new HashMap<>(3);
			extTopMap.put("extEnable", true);

			Optional<CodeTemplate> latestCodeTemplate = getCodeTemplateListResult.getLatestCodeTemplate();
			if (!latestCodeTemplate.isPresent()) {
				log.warn("batchCommitCodeAndSubmitAudit error, dbRoute: {}, msg: {}", dbRouteEa, I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_317));
				errorMsg.append(SHErrorCode.SYSTEM_ERROR).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_317));
				return;
			}
			CodeTemplate codeTemplate = latestCodeTemplate.get();
			String codeTemplateId = codeTemplate.getTemplateId();
			String codeVersion = codeTemplate.getUserVersion();
			String codeDescription = codeTemplate.getUserDescription();

			CountDownLatch countDownLatch = new CountDownLatch(appIdSet.size());
			for (String appId : appIdSet) {
				ThreadPoolUtils.execute(() -> {
					String lockKey = dbRouteEa + appId + RedisManager.WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO;
					if (!redisManager.lock(lockKey, 180)) {
						errorMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_329));
						countDownLatch.countDown();
						return;
					}
					try {
						WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigMap.get(appId);
						if (codeVersion.equals(wechatAccountConfig.getCurrentCodeVersion())) {
							errorMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_336));
							return;
						}
						if (latestReleaseStatusMap.get(appId) != null && MiniappReleaseStatusEnum.listAllPendingStatus().contains(latestReleaseStatusMap.get(appId).get("status"))) {
							errorMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_340));
							return;
						}

						String accessToken = wechatAccountConfig.getAccessToken();
						ModifyDomainResult modifyDomainResult = settingManager.modifyMiniAppDomain(platformDomain, accessToken);
						if (!modifyDomainResult.isSuccess()) {
							miniappReleaseRecordDao.commitCodeFail(dbRouteEa, appId, codeTemplateId, codeVersion, codeDescription, modifyDomainResult.getErrMsg());
							errorMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_348)).append(modifyDomainResult.getErrMsg()).append("</br>");
							return;
						}


						SetWebViewDomainResult setWebViewDomainResult = settingManager.setWebviewDomain(platformDomain.getWebViewDomain(), accessToken);
						if (!setWebViewDomainResult.isSuccess()) {
							miniappReleaseRecordDao.commitCodeFail(dbRouteEa, appId, codeTemplateId, codeVersion, codeDescription, setWebViewDomainResult.getErrMsg());
							errorMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_356)).append(setWebViewDomainResult.getErrMsg()).append("</br>");
							return;
						}

						//设置隐私
						EaWechatAccountBindEntity eaWechatAccountBindEntity = eaWechatAccountBindDao.getCustomizedByPlatformIdAndWxAppId(dbRouteEa, platformId, appId);
						if (eaWechatAccountBindEntity != null){
							BaseResult<List<Integer>> br = openAppAdminService.getAppAdminIds(eaWechatAccountBindEntity.getEa(), marketingAppId);
							if (br.isSuccess() && CollectionUtils.isNotEmpty(br.getResult())){
								settingManager.setPrivacySetting(eaWechatAccountBindEntity.getEa(), br.getResult().get(0), accessToken);
							}
						}

						CommitCodeArg commitCodeArg = new CommitCodeArg();
						commitCodeArg.setTemplateId(codeTemplateId);
						commitCodeArg.setUserVersion(codeVersion);
						commitCodeArg.setUserDescription(codeDescription);
						extTopMap.put("extAppid", wechatAccountConfig.getWxAppId());
						extTopMap.put("ext", new ExtAppInfo(wechatAccountConfig.getWxAppId(), wechatAccountConfig.getNickName(), wechatAccountConfig.getHeadImg(), host, eaWechatAccountBindEntity.getEa()));
						commitCodeArg.setExtJson(GsonUtil.toJson(extTopMap));
						WechatBaseResult commitCodeResult = codeManageService.commit(accessToken, commitCodeArg);
						if (!commitCodeResult.isSuccess()) {
							miniappReleaseRecordDao.commitCodeFail(dbRouteEa , appId, codeTemplateId, codeVersion, codeDescription, commitCodeResult.getErrMsg());
							errorMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_379)).append(commitCodeResult.getErrMsg()).append("</br>");
							return;
						}
					} catch (Exception e) {
						log.warn("批量更新微信小程序" + appId + "出错：", e);
						errorMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_384));
						String[] errMsg = e.toString().split("\n");
						if (errMsg.length > 0) {
							errorMsg.append(errMsg[0]);
						}
						errorMsg.append("</br>");
					} finally {
						redisManager.unLock(lockKey);
						countDownLatch.countDown();
					}
				}, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
			}
			try {
				countDownLatch.await(6, TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				log.warn("com.facishare.marketing.provider.service.wxthirdplatform.WxThirdPlatformInnerSupportServiceImpl#batchCommitCodeAndSubmitAudit.await error:", e);
			}

		//延迟提交代码审核
		ThreadPoolUtils.execute(() -> {
			for (String appId : appIdSet){
				String key = "marketing_miniapp_" + appId;
				long expireTime = 300;
				boolean isLock = redisManager.lock(appId, expireTime);
				if (!isLock) {
					log.info("batchCommitCodeAndSubmitAudit app in handling appId:{}", appId);
					continue;
				}
				CommitCodeAndSubmitAuditArg arg = new CommitCodeAndSubmitAuditArg();
				arg.setCodeDescription(codeDescription);
				arg.setCodeTemplateId(codeTemplateId);
				arg.setCodeVersion(codeVersion);
				arg.setPlatformId(platformId);
				arg.setWxAppId(appId);
				String accessToken = wechatAccountManager.getAccessTokenByWxAppId(appId);
				try {
					miniappReleaseRecordDao.submitAuditSuccess(dbRouteEa, appId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), "0");
					settingManager.submitAuditDelay(dbRouteEa, arg, accessToken, appId, true);
				} catch (InterruptedException e) {
					log.info("batchCommitCodeAndSubmitAudit submitAuditDelay failed appId:{} e:", appId, e);
				}finally {
					redisManager.unLock(key);
				}
			}
		}, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

		});


		if (errorMsg.length() < 1) {
			return Result.newSuccess(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_431));
		}
		return Result.newSuccess(errorMsg.append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_433)).toString());
	}

		@Override
		public Result<String> batchReleaseCode(List<String> appIds, String platformId) {
			if (StringUtils.isEmpty(platformId) || appIds == null || appIds.isEmpty()) {
				return Result.newError(SHErrorCode.PARAMS_ERROR, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_6));
			}
			StringBuffer errMsg = new StringBuffer();
			HashSet<String> appIdSet = new HashSet<>(appIds);
			tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
				Map<String, String> appIdAndAccessToken = wechatAccountManager.batchGetAccessToken(appIdSet,dbRouteEa);
				Map<String, Map<String, Integer>> latestReleaseStatusMap = miniappReleaseRecordDao.batchGetStatus(dbRouteEa, appIdSet);
				List<MiniappReleaseRecordEaAndWxAppIdDTO> eaAndWxAppIdDTOS = miniappReleaseRecordDao.batchGetEaAndWxAppId(dbRouteEa, appIdSet);
				Map<String, String> eaAndWxAppIdMap = eaAndWxAppIdDTOS.stream().collect(Collectors.toMap(MiniappReleaseRecordEaAndWxAppIdDTO::getWxAppId, MiniappReleaseRecordEaAndWxAppIdDTO::getEa, (v1, v2) -> v1));
				CountDownLatch countDownLatch = new CountDownLatch(appIds.size());
				for (String appId : appIds) {
					ThreadPoolUtils.execute(() -> {
						String lockKey = dbRouteEa + appId + RedisManager.WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO;
						if (!redisManager.lock(lockKey, 180)) {
							errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_329));
							countDownLatch.countDown();
							return;
						}
						try {
							Map<String, Integer> statusMap = latestReleaseStatusMap.get(appId);
							if (statusMap == null) {
								errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_457));
								return;
							}
							if (statusMap.get("status") == null || !statusMap.get("status").equals(MiniappReleaseStatusEnum.AUDIT_SUCCESS.getStatus())) {
								errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_461));
								return;
							}
							WechatBaseResult releaseCodeResult = codeManageService.release(appIdAndAccessToken.get(appId), new Object());
							if (releaseCodeResult.isSuccess() || 85052 == releaseCodeResult.getErrCode()) {
								miniappReleaseRecordDao.relaseSuccess(eaAndWxAppIdMap.get(appId), appId, "success");
								MiniappReleaseRecordEntity latestReleaseSuccessRecord = miniappReleaseRecordDao.getLatestReleaseSuccessRecord(eaAndWxAppIdMap.get(appId), appId);
								wechatAccountConfigDao.releaseVersion(latestReleaseSuccessRecord.getEa(), latestReleaseSuccessRecord.getWxAppId(), latestReleaseSuccessRecord.getCodeVersion(), latestReleaseSuccessRecord.getCodeDescription());
							} else {
								errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_470)).append(releaseCodeResult.getErrMsg()).append("</br>");
							}
						} catch (Exception e) {
							log.warn("批量发布微信小程序" + appId + "出错：", e);
							errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_474));
							String[] errorMsg = e.toString().split("\n");
							if (errorMsg.length > 0) {
								errMsg.append(errorMsg[0]);
							}
							errMsg.append("</br>");
						} finally {
							redisManager.unLock(lockKey);
							countDownLatch.countDown();
						}
					}, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
				}
				try {
					countDownLatch.await(6, TimeUnit.SECONDS);
				} catch (InterruptedException e) {
					log.warn("com.facishare.marketing.provider.service.wxthirdplatform.WxThirdPlatformInnerSupportServiceImpl#batchReleaseCode.await error:", e);
				}
			});
			if (errMsg.length() < 1) {
				return Result.newSuccess(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_492));
			}
			return Result.newSuccess(errMsg.append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_433)).toString());
		}

		@Override
		public Result<WxCodeTemplateResult> getWxMiniAppNewVersion(String platformId) {
			Preconditions.checkArgument(StringUtils.isNotEmpty(platformId), I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_499));
			String thirdPlatformAccessToken = wechatThirdPlatformManager.getThirdPlatformAccessToken(platformId);
			Preconditions.checkArgument(StringUtils.isNotEmpty(thirdPlatformAccessToken), I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_501));
			Optional<CodeTemplate> latestCodeTemplate = codeTemplateService.getTemplateList(thirdPlatformAccessToken).getLatestCodeTemplate();
			if (!latestCodeTemplate.isPresent()) {
				return Result.newError(SHErrorCode.SYSTEM_ERROR);
			}
			CodeTemplate codeTemplate = latestCodeTemplate.get();
			return Result.newSuccess(new WxCodeTemplateResult(codeTemplate.getTemplateId(), codeTemplate.getUserVersion(), codeTemplate.getUserDescription(), codeTemplate.getCreateTime()));
		}

		@Override
		public Result<String> batchUndoCodeAudit(List<String> appIds, String platformId) {
			if (StringUtils.isEmpty(platformId) || appIds == null || appIds.isEmpty()) {
				return Result.newError(SHErrorCode.PARAMS_ERROR, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_6));
			}
			StringBuffer errMsg = new StringBuffer();
			HashSet<String> appIdSet = new HashSet<>(appIds);
			tenantService.getAllDbRouteEas().forEach(dbRouteEa -> {
				Map<String, String> appIdAndAccessToken = wechatAccountManager.batchGetAccessToken(appIdSet,dbRouteEa);
				Map<String, Map<String, Integer>> latestReleaseStatusMap = miniappReleaseRecordDao.batchGetStatus(dbRouteEa, appIdSet);
				CountDownLatch countDownLatch = new CountDownLatch(appIds.size());
				for (String appId : appIds) {
					ThreadPoolUtils.execute(() -> {
						try {
							undoCodeAudit(appId, errMsg, appIdAndAccessToken, latestReleaseStatusMap,dbRouteEa);
						} catch (Exception e) {
							log.warn("批量撤回微信小程序" + appId + "审核出错：", e);
						}finally {
							countDownLatch.countDown();
						}
					}, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
				}
				try {
					countDownLatch.await(6, TimeUnit.SECONDS);
				} catch (InterruptedException e) {
					log.warn("com.facishare.marketing.provider.service.wxthirdplatform.batchUndoCodeAudit#batchReleaseCode.await error:", e);
				}
			});
			if (errMsg.length() < 1) {
				return Result.newSuccess(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_537));
			}
			return Result.newSuccess(errMsg.append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_539)).toString());
		}

		public boolean undoCodeAudit(String appId, StringBuffer errMsg, Map<String, String> appIdAndAccessToken, Map<String, Map<String, Integer>> latestReleaseStatusMap,String ea){
			String lockKey = appId + RedisManager.WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO;
			if (!redisManager.lock(lockKey, 180)) {
				errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_329));
				return false;
			}
			try {
				Map<String, Integer> statusMap = latestReleaseStatusMap.get(appId);
				if (statusMap == null) {
					errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_457));
					return false;
				}
				if (statusMap.get("status") == null || !statusMap.get("status").equals(MiniappReleaseStatusEnum.AUDITING.getStatus())) {
					errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_555));
					return false;
				}
				String resultStr = httpManager.executeGetHttpReturnString("https://api.weixin.qq.com/wxa/undocodeaudit?access_token=" + appIdAndAccessToken.get(appId));
				if (StringUtils.isEmpty(resultStr)) {
					errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_560));
					return false;
				}
				JSONObject undoResult = JSON.parseObject(resultStr);
				Integer errcode = (Integer) undoResult.get("errcode");
				if (errcode == null || errcode != 0) {
					errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_566)).append(undoResult.get("errmsg")).append("</br>");
					return false;
				}
				 miniappReleaseRecordDao.undoAudit(ea, statusMap.get("id"), appId);
			} catch (Exception e) {
				log.warn("批量撤回小程序审核" + appId + "出错：", e);
				errMsg.append(appId).append(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_572));
				String[] errorMsg = e.toString().split("\n");
				if (errorMsg.length > 0) {
					errMsg.append(errorMsg[0]);
				}
				errMsg.append("</br>");
			} finally {
				redisManager.unLock(appId + RedisManager.WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO);
			}
			return true;
		}

		@Override
		public Result<String> updateShowVersion(String showCodeTemplateId, String showCodeVersion, String showCodeDescription, String platformId) {
			if (StringUtils.isEmpty(showCodeTemplateId) || StringUtils.isEmpty(showCodeVersion) || StringUtils.isEmpty(platformId)) {
				return Result.newError(SHErrorCode.PARAMS_ERROR, SHErrorCode.PARAMS_ERROR.getErrorMessage());
			}
			if ("--".equals(showCodeTemplateId)) {
				showCodeTemplateId = null;
				showCodeVersion = null;
				showCodeDescription = null;
			}
			for(String dbRouteEa : tenantService.getAllDbRouteEas()) {
				int rest = wechatThirdPlatformConfigDao.updateShowVersion(showCodeTemplateId, showCodeVersion, showCodeDescription, platformId,dbRouteEa);
				if (rest != 1) {
					return Result.newError(SHErrorCode.OPERATE_DB_FAIL, SHErrorCode.OPERATE_DB_FAIL.getErrorMessage());
				}
			}
			return Result.newSuccess(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_598));
		}

		@Override
		public Result<List<WxTemplateVersionResult>> getVersionList(String platformId) {
			if (StringUtils.isEmpty(platformId)) {
				return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_604), new ArrayList<>());
			}
			Result<String> accessTokenResult = wxThirdCloudInnerService.getComponentAccessTokenByPlatformId(platformId);
			if (accessTokenResult == null || !accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
				return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_608), new ArrayList<>());
			}
			GetCodeTemplateListResult templateListResult = codeTemplateService.getTemplateList(accessTokenResult.getData());
			if (templateListResult == null || !templateListResult.isSuccess()) {
				return Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_612), new ArrayList<>());
			}
			WechatThirdPlatformConfigEntity wechatThirdPlatform = wechatThirdPlatformManager.getWechatThirdPlatformConfig(platformId);
			List<CodeTemplate> templateList = templateListResult.getTemplateList();
			Collections.reverse(templateList);
			List<WxTemplateVersionResult> results = new ArrayList<>(templateList.size() + 1);
			WxTemplateVersionResult useNewVersion = new WxTemplateVersionResult();
			useNewVersion.setShowCodeTemplateId("--");
			useNewVersion.setShowCodeVersion(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_620));
			useNewVersion.setShowCodeDescription(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_621));
			if (StringUtils.isEmpty(wechatThirdPlatform.getShowCodeTemplateId())) {
				useNewVersion.setChecked(true);
			}
			results.add(useNewVersion);
			results.addAll(templateList.stream().map(template -> {
				WxTemplateVersionResult result = new WxTemplateVersionResult();
				result.setShowCodeTemplateId(template.getTemplateId());
				result.setShowCodeVersion(template.getUserVersion());
				result.setShowCodeDescription(template.getUserDescription());
				result.setCreateTime(new Date(template.getCreateTime() * 1000));
				if (template.getTemplateId().equals(wechatThirdPlatform.getShowCodeTemplateId())) {
					result.setChecked(true);
				}
				return result;
			}).collect(Collectors.toList()));
			return Result.newSuccess(results);
		}

		private String replaceProperties(String fsPlatformId, String wxAppId, WechatThirdPlatformConfigEntity wechatThirdPlatformConfig, String requestUrl) {
			if (Strings.isNullOrEmpty(requestUrl)){
				return requestUrl;
			}
			if (requestUrl.contains(TO_REPLACE_WX_COMPONENT_APP_ID)){
				requestUrl = requestUrl.replace(TO_REPLACE_WX_COMPONENT_APP_ID, wechatThirdPlatformConfig.getComponentAppId());
			}
			if (requestUrl.contains(TO_REPLACE_WX_COMPONENT_ACCESS_TOKEN)){
				requestUrl = requestUrl.replace(TO_REPLACE_WX_COMPONENT_ACCESS_TOKEN, wechatThirdPlatformManager.getThirdPlatformAccessToken(fsPlatformId));
			}
			if (requestUrl.contains(TO_REPLACE_WX_APP_ACCESS_TOKEN)){
				requestUrl = requestUrl.replace(TO_REPLACE_WX_APP_ACCESS_TOKEN, wechatAccountManager.getAccessTokenByWxAppId(wechatThirdPlatformConfig.getEa(), wxAppId));
			}
			return requestUrl;
		}

		private String getValidAccessToken(WxMiniAppEntity wxMiniAppEntity) {
			if(wxMiniAppEntity.isAccessTokenInvalid()){
				return wechatAccountManager.doGetAndUpdateAccessTokenFromWechat(wechatAccountConfigDao.getByWxAppId(wxMiniAppEntity.getEa(), wxMiniAppEntity.getWxAppId()));
			}
			if(wxMiniAppEntity.isAccessTokenNeedRefresh()){
				ThreadPoolUtils.execute(() -> wechatAccountManager.doGetAndUpdateAccessTokenFromWechat(wechatAccountConfigDao.getByWxAppId(wxMiniAppEntity.getEa(), wxMiniAppEntity.getWxAppId())), ThreadPoolTypeEnums.LIGHT_BUSINESS);
			}
			return wxMiniAppEntity.getAccessToken();
		}

		private static List<String> strToList(String str, String separator){
			if(Strings.isNullOrEmpty(str)){
				return new ArrayList<>(0);
			}
			return Splitter.on(separator).splitToList(str).stream().filter(Objects::nonNull).map(String::trim).filter(s -> !Strings.isNullOrEmpty(s)).collect(Collectors.toList());
		}
}