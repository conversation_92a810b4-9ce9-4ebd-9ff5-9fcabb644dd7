/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.mail;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.mail.*;
import com.facishare.marketing.api.vo.mail.*;
import com.facishare.marketing.audit.log.MarketingAuditLogService;
import com.facishare.marketing.audit.log.model.MarketingAuditLogArg;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.mail.MailConstant;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.conference.ConferenceParamEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LiveParamEnum;
import com.facishare.marketing.common.enums.mail.*;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.MailSendExtraData;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.ReplaceUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.ActivityEnrollDataDAO;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.mail.*;
import com.facishare.marketing.provider.dto.MarketingUserWithEmail;
import com.facishare.marketing.provider.dto.conference.ConferenceEnrollBaseInfoDTO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.mail.*;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.innerArg.AddTaskScheduleArg;
import com.facishare.marketing.provider.innerArg.mail.SendEmailArg;
import com.facishare.marketing.provider.innerResult.mail.SendEmailResult;
import com.facishare.marketing.provider.innerResult.qywx.Tag;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.EnterpriseSpreadRecordManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.MarketingUserGroupManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.EmailSendRecordDetailObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.mail.arg.*;
import com.facishare.marketing.provider.manager.mail.result.*;
import com.facishare.marketing.provider.manager.mail.result.AddApiUserResp.AddApiUserResult;
import com.facishare.marketing.provider.manager.mail.result.AddMailDomainResp.DomainInfo;
import com.facishare.marketing.provider.manager.mail.result.CheckMailDomainResp.CheckMailResult;
import com.facishare.marketing.provider.manager.mail.result.QueryOpenAndClickListResp.OpenAndClickInfo;
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.taskSchedule.TaskScheduleManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.util.MarketingJobUtil;
import com.facishare.marketing.provider.util.ReadExcelUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.warehouse.api.util.JsonUtils;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ContactFieldContants;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.limit.GuavaLimiter;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager.CRM_OBJ_QUERY_RATE_LIMIT_KEY;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by zhengh on 2020/6/2.
 */
@Component(value = "emailManager")
@Slf4j
public class MailManager {
    @Autowired
    private MailLabelDAO mailLabelDAO;
    @Autowired
    private MailAccountDAO mailAccountDAO;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private MailSendTaskResultDAO mailSendTaskResultDAO;
    @Autowired
    private MailTemplateDAO mailTemplateDAO;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private MailSendReplyDAO mailSendReplyDAO;
    @Autowired
    private MailAddressListDAO mailAddressListDAO;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private MailSendExtraDataDAO mailSendExtraDataDAO;
    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private EnterpriseSpreadRecordManager enterpriseSpreadRecordManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private MailAddressMapDAO mailAddressMapDAO;
    @Autowired
    private MailSendErrorAddressDAO mailSendErrorAddressDAO;
    @Autowired
    private MailFilterDetailDAO mailFilterDetailDAO;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;
    @Autowired
    private EmailSendRecordDetailObjManager emailSendRecordDetailObjManager;

    @Autowired
    private TaskScheduleManager taskScheduleManager;
    @Autowired
    private MarketingAuditLogService auditLogService;

    private static final String auditLogTopic = "marketing-audit-log";

//    public static final String SINGLE_EMAIL_REGEX = "(?:(?:[A-Za-z0-9\\-_@!#$%&'*+/=?^`{|}~]|(?:\\\\[\\x00-\\xFF]?)|(?:\"[\\x00-\\xFF]*\"))+(?:\\.(?:(?:[A-Za-z0-9\\-_@!#$%&'*+/=?^`{|}~])|(?:\\\\[\\x00-\\xFF]?)|(?:\"[\\x00-\\xFF]*\"))+)*)@(?:(?:[A-Za-z0-9](?:[A-Za-z0-9-]*[A-Za-z0-9])?\\.)+(?:(?:[A-Za-z0-9]*[A-Za-z][A-Za-z0-9]*)(?:[A-Za-z0-9-]*[A-Za-z0-9])?))";

    public static final String SINGLE_EMAIL_REGEX = "^(?=.{6,128}$)([A-Za-z0-9._%+-]{1,96})@([A-Za-z0-9](?:[A-Za-z0-9-]{0,61}[A-Za-z0-9])?(?:\\.[A-Za-z0-9](?:[A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*\\.[A-Za-z]{2,10})$";

    @ReloadableProperty("host")
    private String host;

    @ReloadableProperty("special_ea_mail_account")
    private String specialEaMailAccount;

    @ReloadableProperty("send_email_audit_log_eas")
    private String auditLogEas;

    public Result<String> createGroupSendMsgTask(MailServiceMarketingActivityVO mailServiceMarketingActivityVO, String marketingEventId, String marketingActivityId, String ea, Integer userId) {
        MailSendTaskEntity entity = new MailSendTaskEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setFsUserId(userId);
        entity.setSubject(mailServiceMarketingActivityVO.getTitle());
        entity.setMarketingEventId(marketingEventId);
        if (mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.MARKETING_USER_GROUP.getType()) {
            entity.setMarketingGroupUserIds(GsonUtil.toJson(mailServiceMarketingActivityVO.getMarketingUserGroupIds()));
        } else if (mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.EXCELL.getType()) {
            Optional<List<String>> mailListOpt = readSendEmailFromExcel(ea, mailServiceMarketingActivityVO.getTaPath());
            if (!mailListOpt.isPresent()) {
                log.info("MailManager createGroupSendMsgTask failed parse mail address file failed ");
                return Result.newError(SHErrorCode.MAIL_ADDRESS_EMAIL_ERROR);
            }
            if (CollectionUtils.isNotEmpty(mailListOpt.get())) {
                entity.setToUser(StringUtils.join(mailListOpt.get(), ";"));
            }
        } else if (mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.CONFERENCE_MAIL_LIST.getType() || mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.LIVE_ENROLL_MAIL_LIST.getType() || mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.MARKETING_ACTIVITY.getType()) {
            List<String> campaignIds = mailServiceMarketingActivityVO.getCampaignIds();
            if (CollectionUtils.isEmpty(campaignIds)) {
                log.info("MailManager createGroupSendMsgTask failed campaignIds is null ");
                return Result.newError(SHErrorCode.NO_DATA);
            }
        } else if (mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.TAG.getType()){
            entity.setSendObjectTags(mailServiceMarketingActivityVO.getTagNames());
            entity.setExcludeObjectTags(mailServiceMarketingActivityVO.getExcludeTagNames());
            entity.setTagOperator(mailServiceMarketingActivityVO.getTagOperator());
        }

        if (mailServiceMarketingActivityVO.getType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            entity.setFixTime(new DateTime().plusSeconds(15).getMillis());
        } else {
            entity.setFixTime(mailServiceMarketingActivityVO.getFixedTime());
        }
        // 替换邮件内容
        mailServiceMarketingActivityVO.setContent(ReplaceUtil.replaceMarketingActivityId(mailServiceMarketingActivityVO.getContent(), marketingActivityId));
        mailServiceMarketingActivityVO.setContent(ReplaceUtil.replaceMarketingEventId(mailServiceMarketingActivityVO.getContent(), marketingEventId));
        entity.setHtml(mailServiceMarketingActivityVO.getContent());
        entity.setSendRange(mailServiceMarketingActivityVO.getSendRange());
        entity.setScheduleType(mailServiceMarketingActivityVO.getType());
        if (marketingActivityAuditManager.isNeedAudit(ea)) {
            entity.setScheduleType(2);
        }
        entity.setMailType(mailServiceMarketingActivityVO.getMailType());
        if (mailServiceMarketingActivityVO.getType() == MailScheduleTypeEnum.SCHEDULE_SEND.getType()) {
            entity.setFixTime(mailServiceMarketingActivityVO.getFixedTime());
        }

        if (StringUtils.isNotEmpty(mailServiceMarketingActivityVO.getTemplateId())) {
            entity.setTemplateInvokeName(mailServiceMarketingActivityVO.getTemplateId());
        }
        if (CollectionUtils.isNotEmpty(mailServiceMarketingActivityVO.getSendMailIds())) {
            entity.setSenderIds(GsonUtil.toJson(mailServiceMarketingActivityVO.getSendMailIds()));
        }
        if (CollectionUtils.isNotEmpty(mailServiceMarketingActivityVO.getReplyToIds())) {
            entity.setReplyIds(GsonUtil.toJson(mailServiceMarketingActivityVO.getReplyToIds()));
        }

        //处理附件
        if (CollectionUtils.isNotEmpty(mailServiceMarketingActivityVO.getAttachments())) {
            for (MailServiceMarketingActivityVO.MailAttachment mailAttachment : mailServiceMarketingActivityVO.getAttachments()) {
                if (mailAttachment.getAttachmentPath().startsWith("TA_")) {
                    String path = fileV2Manager.changeAWarehouseTempToPermanentBybusiness(ea, userId, mailAttachment.getExt(), mailAttachment.getAttachmentPath(), "fs-marketing");
                    if (path == null) {
                        return Result.newError(SHErrorCode.MAIL_ATTACHMENT_ERROR);
                    }
                    mailAttachment.setAttachmentPath(path);
                }
            }
            entity.setAttachments(GsonUtil.toJson(mailServiceMarketingActivityVO.getAttachments()));
        }
        entity.setTotalSendCount(0);
        entity.setSendStatus(MailSendStatusEnum.WAIT_FOR_SEND.getStatus());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setFilterNDaySentUser(mailServiceMarketingActivityVO.getFilterNDaySentUser());
        mailSendTaskDAO.insert(entity);

        // 保存额外数据
        ThreadPoolUtils.executeWithTraceContext(() -> {
            saveExtraDataEntity(mailServiceMarketingActivityVO, ea, entity);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);

        return Result.newSuccess(entity.getId());
    }

    private void saveExtraDataEntity(MailServiceMarketingActivityVO mailServiceMarketingActivityVO, String ea, MailSendTaskEntity entity) {
        if (mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.CONFERENCE_MAIL_LIST.getType()) {
            MailSendExtraDataEntity mailSendExtraDataEntity = new MailSendExtraDataEntity();
            MailSendExtraData mailSendExtraData = new MailSendExtraData();
            //mailSendExtraData.setConferenceEnrollMap(mailServiceMarketingActivityVO.getEnrollMailMap());
            mailSendExtraData.setCampaignIds(mailServiceMarketingActivityVO.getCampaignIds());
            mailSendExtraDataEntity.setTaskId(entity.getId());
            mailSendExtraDataEntity.setEa(ea);
            mailSendExtraDataEntity.setDataType(MailSendExtraDataTypeEnum.CONFERENCE_ENROLL.getType());
            mailSendExtraDataEntity.setExtraData(mailSendExtraData);
            mailSendExtraDataDAO.insert(mailSendExtraDataEntity);
        } else if (mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.LIVE_ENROLL_MAIL_LIST.getType()) {
            MailSendExtraDataEntity mailSendExtraDataEntity = new MailSendExtraDataEntity();
            MailSendExtraData mailSendExtraData = new MailSendExtraData();
            //mailSendExtraData.setConferenceEnrollMap(mailServiceMarketingActivityVO.getEnrollMailMap());
            mailSendExtraData.setCampaignIds(mailServiceMarketingActivityVO.getCampaignIds());
            mailSendExtraDataEntity.setTaskId(entity.getId());
            mailSendExtraDataEntity.setEa(ea);
            mailSendExtraDataEntity.setDataType(MailSendExtraDataTypeEnum.LIVE_ENROLL.getType());
            mailSendExtraDataEntity.setExtraData(mailSendExtraData);
            mailSendExtraDataDAO.insert(mailSendExtraDataEntity);
        } else if (mailServiceMarketingActivityVO.getSendRange() == MailSendRangeEnum.MARKETING_ACTIVITY.getType()) {
            MailSendExtraDataEntity mailSendExtraDataEntity = new MailSendExtraDataEntity();
            MailSendExtraData mailSendExtraData = new MailSendExtraData();
            mailSendExtraData.setCampaignIds(mailServiceMarketingActivityVO.getCampaignIds());
            mailSendExtraDataEntity.setTaskId(entity.getId());
            mailSendExtraDataEntity.setEa(ea);
            mailSendExtraDataEntity.setDataType(MailSendExtraDataTypeEnum.MARKETING_ACTIVITY.getType());
            mailSendExtraDataEntity.setExtraData(mailSendExtraData);
            mailSendExtraDataDAO.insert(mailSendExtraDataEntity);
        }
    }

    public Optional<List<String>> readSendEmailFromExcel(String ea, String taPath) {
        try {
            byte[] excelByte = fileV2Manager.downloadAFile(taPath, null);
            if (excelByte == null) {
                log.info("readSendEmailFromExcel download mail tapath failed ea:{} tapath:{}", ea, taPath);
                return Optional.empty();
            }
            List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(taPath, excelByte);
            if (CollectionUtils.isEmpty(excelData)) {
                return Optional.empty();
            }
            List<String> emails = excelData.stream().map(data -> data[0]).collect(Collectors.toList());
            emails = emails.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            /*List<String> validMails = getValidMails(emails);
            if (emails.size() != validMails.size()){
                log.info("readSendEmailFromExcel ea:{} tapath:{} totalMail:{} valid mail:{}", ea, taPath, emails, validMails);
                return Optional.empty();
            }*/
            return CollectionUtils.isNotEmpty(emails) ? Optional.of(emails) : Optional.empty();
        } catch (Exception e) {
            log.info("readSendEmailFromExcel download exception tapath failed ea:{} tapath:{} e:", ea, taPath, e);
            return Optional.empty();
        }
    }

    public List<String> getValidMails(List<String> mails) {

        List<String> vaildMails = Lists.newArrayList();
        for (int i = 0; i < mails.size(); i++) {
            String mail = mails.get(i);
            Pattern p = Pattern.compile(SINGLE_EMAIL_REGEX);
            Matcher m = p.matcher(mail);
            if (!m.matches()) {
                log.info("mail idx: {} value:{} invalid", i, mail);
            } else {
                vaildMails.add(mail);
            }
        }

        return vaildMails;
    }

    public boolean getValidMail(String mail) {
        Pattern p = Pattern.compile(SINGLE_EMAIL_REGEX);
        Matcher m = p.matcher(mail);
        return m.matches();
    }

    /**
     * 新增邮件标签
     *
     * @param apiUser
     * @param apiKey
     * @param ea
     * @param subject
     * @return
     */
    private Optional<Integer> addEmailLabel(String apiUser, String apiKey, String ea, String subject) {
        String url = getSendCloudApiHostUrl() + "label/add";
        subject = filterEmoji(subject);
        RequestBody requestBody = new FormBody.Builder().add("apiUser", apiUser).add("apiKey", apiKey).add("labelName", subject + "-" + System.currentTimeMillis()).build();
        MailBaseResp<AddMailLabelResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<AddMailLabelResp>>() {
        });
        if (!resp.isSuccess() || resp.getInfo() == null) {
            log.info("MailManager.addEmailLabel failed errocode:{} errorMsg:{}", resp.getStatusCode(), resp.getMessage());
            return Optional.empty();
        }

        AddMailLabelResp label = resp.getInfo();
        if (label != null) {
            MailLabelEntity entity = new MailLabelEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setLabelName(label.getData().getLabelName());
            entity.setLabelId(label.getData().getLabelId());
            Date now = new Date(System.currentTimeMillis());
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            mailLabelDAO.insert(entity);
        }

        return Optional.ofNullable(label.getData().getLabelId());
    }

    private String filterEmoji(String source) {
        if (source != null) {
            Pattern emoji = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]", Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                source = emojiMatcher.replaceAll("");
                return source;
            }
            return source;
        }
        return source;
    }

    /**
     * 分页查询邮件标签列表
     *
     * @param vo
     * @return
     */
    public Result<PageResult<PageQueryMailLabelResult>> pageQueryMailLabel(PageQueryMailLabelVO vo) {
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(vo.getEa(), MailApiUserTypeEnum.DEFAULT.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.pageQueryMailLabelList failed mailAccountEntity not exist ea:{}", vo.getEa());
            return Result.newError(SHErrorCode.ENTERPRSE_NOT_BIND_MAIL_ACCOUNT);
        }

        PageResult<PageQueryMailLabelResult> pageResult = new PageResult();
        List<PageQueryMailLabelResult> mailLabelList = Lists.newArrayList();
        pageResult.setResult(mailLabelList);
        pageResult.setTotalCount(0);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());

        Page page = new Page(vo.getPageNum(), vo.getPageSize());
        List<MailLabelEntity> mailLabelEntityList = mailLabelDAO.pageQueryLabel(vo.getEa(), vo.getKeyword(), page);
        if (CollectionUtils.isNotEmpty(mailLabelEntityList)) {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (MailLabelEntity entity : mailLabelEntityList) {
                PageQueryMailLabelResult result = new PageQueryMailLabelResult();
                result.setId(entity.getId());
                result.setLabelId(entity.getLabelId());
                result.setLabelName(entity.getLabelName());
                result.setCreateTime(df.format(entity.getCreateTime()));
                result.setUpdateTime(df.format(entity.getUpdateTime()));
                mailLabelList.add(result);
            }
        }

        return Result.newSuccess(pageResult);
    }

    /**
     * 删除标签
     *
     * @param vo
     * @return
     */
    public Result<Void> deleteMailLabel(DeleteMailLabelVO vo) {
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(vo.getEa(), MailApiUserTypeEnum.DEFAULT.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.deleteMailLabel failed mailAccountEntity not exist ea:{}", vo.getEa());
            return Result.newError(SHErrorCode.ENTERPRSE_NOT_BIND_MAIL_ACCOUNT);
        }
        MailLabelEntity entity = mailLabelDAO.getById(vo.getId());
        if (entity == null) {
            log.warn("MailManager.deleteMailLabel failed mailLabelEntity not exist vo:{}", vo);
            return Result.newError(SHErrorCode.DELETE_MAIL_LABEL_FAILED);
        }

        String url = getSendCloudApiHostUrl() + "label/delete";
        DeleteMailLabelReq req = new DeleteMailLabelReq();
        req.setApiUser(mailAccountEntity.getApiUser());
        req.setApiKey(mailAccountEntity.getApiKey());
        req.setLabelId(entity.getLabelId());
        MailBaseResp<DeleteMailLabelResp> resp = httpManager.executePostHttp(req, url, new TypeToken<MailBaseResp<DeleteMailLabelResp>>() {
        });
        if (!resp.isSuccess() || resp.getInfo() == null) {
            log.info("MailManager.deleteMailLabel failed errocode:{} errorMsg:{}", resp.getStatusCode(), resp.getMessage());
            return Result.newError(SHErrorCode.ADD_MAIL_LABEL_FAILED);
        }
        mailLabelDAO.deleteById(vo.getId());

        return Result.newSuccess();
    }

    /**
     * 修改邮件标签名称
     *
     * @param vo
     * @return
     */
    public Result<Void> updateMailLabel(UpdateMailLabelVO vo) {
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(vo.getEa(), MailApiUserTypeEnum.DEFAULT.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.updateMailLabel failed mailAccountEntity not exist ea:{}", vo.getEa());
            return Result.newError(SHErrorCode.ENTERPRSE_NOT_BIND_MAIL_ACCOUNT);
        }
        MailLabelEntity entity = mailLabelDAO.getById(vo.getId());
        if (entity == null) {
            log.warn("MailManager.updateMailLabel failed mailLabelEntity not exist vo:{}", vo);
            return Result.newError(SHErrorCode.UPDATE_MAIL_LABEL_FAILED);
        }

        String url = getSendCloudApiHostUrl() + "label/update";
        UpdateMailLabelReq req = new UpdateMailLabelReq();
        req.setApiUser(mailAccountEntity.getApiUser());
        req.setApiKey(mailAccountEntity.getApiKey());
        req.setLabelId(entity.getLabelId());
        req.setLabelName(vo.getLabelName());
        MailBaseResp<UpdateMailLabelResp> resp = httpManager.executePostHttp(req, url, new TypeToken<MailBaseResp<UpdateMailLabelResp>>() {
        });
        if (!resp.isSuccess() || resp.getInfo() == null) {
            log.info("MailManager.updateMailLabel failed errocode:{} errorMsg:{}", resp.getStatusCode(), resp.getMessage());
            return Result.newError(SHErrorCode.ADD_MAIL_LABEL_FAILED);
        }

        mailLabelDAO.updateMailLabel(vo.getId(), vo.getLabelName());
        return Result.newSuccess();
    }

    public Optional<RequestBody> buildEmailSendRequestBody(MailSendTaskEntity taskEntity, String apiUser, String apiKey,
                                                           List<String> addressList, Integer labelId, String xsmtpapi, int sendType) {
        // 设置header
        Map<String, String> header = buildSendMailHeaderMap(taskEntity);
        return this.buildEmailSendRequestBody(taskEntity, apiUser, apiKey, addressList, labelId, xsmtpapi, sendType, header);
    }

    /**
     * 构建通过xsmtpapi的方式发送邮件的请求对象
     *
     * @param taskEntity
     * @return
     */
    private Optional<RequestBody> buildEmailSendRequestBody(MailSendTaskEntity taskEntity, String apiUser, String apiKey,
                                                            List<String> addressList, Integer labelId, String xsmtpapi, int sendType, Map<String, String> header) {
        List<String> senderIds = GsonUtil.getGson().fromJson(taskEntity.getSenderIds(), new TypeToken<ArrayList>() {
        }.getType());
        List<String> replyIds = GsonUtil.getGson().fromJson(taskEntity.getReplyIds(), new TypeToken<ArrayList>() {
        }.getType());
        List<MailSendReplyEntity> mailSenderEntityList = mailSendReplyDAO.getByIds(senderIds);
        List<MailSendReplyEntity> mailReplyToEntityList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(replyIds)) {
            mailReplyToEntityList = mailSendReplyDAO.getByIds(replyIds);
        }
        String fromEmail = null;
        String fromName = null;
        if (CollectionUtils.isNotEmpty(mailSenderEntityList)) {
            fromEmail = mailSenderEntityList.get(0).getAddress();
            fromName = mailSenderEntityList.get(0).getName();
        }

        String replyTo = "";
        if (CollectionUtils.isNotEmpty(mailReplyToEntityList)) {
            for (int i = 0; i < mailReplyToEntityList.size() && i < 3; i++) {
                replyTo = replyTo + mailReplyToEntityList.get(i).getAddress() + ";";
            }
            replyTo = replyTo.substring(0, replyTo.length() - 1);
        }

        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        builder.addFormDataPart("apiUser", apiUser);
        builder.addFormDataPart("apiKey", apiKey);
        builder.addFormDataPart("from", fromEmail);
        builder.addFormDataPart("fromName", fromName);
        builder.addFormDataPart("headers", GsonUtil.getGson().toJson(header));
        if (sendType == MailSendTypeEnum.SEND_BY_ADDRESS_LIST.getType()) {
            builder.addFormDataPart("to", StringUtils.join(addressList, ";"));
        } else if (sendType == MailSendTypeEnum.SEND_BY_XSMTPAPI.getType()) {
            builder.addFormDataPart("xsmtpapi", xsmtpapi);
        } else if (sendType == MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType()) {
            builder.addFormDataPart("to", StringUtils.join(addressList, ";"));
        }
        builder.addFormDataPart("subject", taskEntity.getSubject());
        builder.addFormDataPart("html", taskEntity.getHtml());
        if (StringUtils.isNotEmpty(taskEntity.getContentSummary())) {
            builder.addFormDataPart("contentSummary", taskEntity.getContentSummary());
        }
        if (StringUtils.isNotEmpty(taskEntity.getTemplateInvokeName())) {
            builder.addFormDataPart("templateInvokeName", taskEntity.getTemplateInvokeName());
        }
        if (StringUtils.isNotBlank(replyTo)) {
            builder.addFormDataPart("replyTo", replyTo);
        }
        if (labelId != null) {
            builder.addFormDataPart("labelId", labelId.toString());
        }

        if (sendType == MailSendTypeEnum.SEND_BY_ADDRESS_LIST.getType()) {
            builder.addFormDataPart("useAddressList", "true");
        } else {
            builder.addFormDataPart("useAddressList", "false");
        }
        builder.addFormDataPart("respEmailId", "true");

        //邮件附件
        if (StringUtils.isNotEmpty(taskEntity.getAttachments())) {
            List<MailServiceMarketingActivityVO.MailAttachment> attachments = GsonUtil.getGson().fromJson(taskEntity.getAttachments(), new TypeToken<List<MailServiceMarketingActivityVO.MailAttachment>>() {
            }.getType());
            attachments.forEach(attachment -> {
                byte[] fileByte = fileV2Manager.downloadAFile(attachment.getAttachmentPath(), taskEntity.getEa());
                if (fileByte != null) {
                    builder.addFormDataPart("attachments", attachment.getAttachmentName() + "." + attachment.getExt(), RequestBody.create(MediaType.parse("application/octet-stream"), fileByte));
                }
            });
        }

        return Optional.ofNullable(builder.build());
    }

    /**
     * 调度任务发送邮件
     *
     * @return
     */
    public void sendEmailTaskSchedule(String id) {
        try {
            MailSendTaskEntity emailSendTaskEntity = mailSendTaskDAO.getById(id);

            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getMarketingActivityExternalConfigEntity(emailSendTaskEntity.getEa(), emailSendTaskEntity.getId(), AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType());
            if (marketingActivityExternalConfigEntity != null && !marketingActivityAuditManager.checkAudtStatus(emailSendTaskEntity.getEa(), marketingActivityExternalConfigEntity.getMarketingActivityId())) {
                log.info("MailManager.sendEmailTaskSchedule checkAudtStatus not eq normal {}", marketingActivityExternalConfigEntity.getMarketingActivityId());
                return;
            }

            if (marketingActivityRemoteManager.enterpriseStop(emailSendTaskEntity.getEa()) || appVersionManager.getCurrentAppVersion(emailSendTaskEntity.getEa()) == null) {
                log.info("MailManager.sendEmailTaskSchedule failed enterprise stop or license expire ea:{}", emailSendTaskEntity.getEa());
                return;
            }
            if (MarketingJobUtil.isMarketingJobForbidExec(emailSendTaskEntity.getEa())) {
                log.warn("当前时间禁止发送营销消息, email job: {}", emailSendTaskEntity);
                mailSendTaskDAO.updateStatusAndCodeById(MailSendStatusEnum.SEND_FAILED.getStatus(), SHErrorCode.FORBID_SEND_MARKETING_MESSAGE.getErrorCode(), emailSendTaskEntity.getId());
                return;
            }
            ThreadPoolUtils.execute(() -> handlerSendTask(emailSendTaskEntity), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        } catch (Exception e) {
            log.warn("GroupSendMessageManager groupSendMsgSchedule exception:{}", e.fillInStackTrace());
        }
    }

    public void handlerSendTask(MailSendTaskEntity taskEntity) {
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(taskEntity.getEa(), taskEntity.getMailType());
        if (mailAccountEntity == null) {
            log.warn("handlerSendTask failed mailAccountEntity not exist ea:{}", taskEntity.getEa());
            return;
        }

        MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getMarketingActivityExternalConfigEntityWithMarketingEventId(taskEntity.getEa(), taskEntity.getId(), AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType(), taskEntity.getMarketingEventId());
        boolean isNormall = marketingActivityAuditManager.checkAudtStatus(taskEntity.getEa(), externalConfigEntity.getMarketingActivityId());
        if (!isNormall) {
            return;
        }
        Integer updateNum = mailSendTaskDAO.updateStatusByIdAndBeforeStatus(MailSendStatusEnum.SENDING.getStatus(), taskEntity.getId(), MailSendStatusEnum.WAIT_FOR_SEND.getStatus());
        if (updateNum != 1) {
            return;
        }
        try {
            String commonSendUrl = getSendCloudApiHostHttpsUrl() + "mail/send";
            String templateSendUrl = getSendCloudApiHostHttpsUrl() + "mail/sendtemplate";
            String sendUrl = commonSendUrl;
            if (StringUtils.isNotEmpty(taskEntity.getTemplateInvokeName())) {
                sendUrl = templateSendUrl;
            }

            Set<MarketingUserWithEmail> marketingUserWithEmailSet = Sets.newHashSet();
            List<String> tagEmails = null;
            List<String> toUser = null;
            if (taskEntity.getSendRange() == MailSendRangeEnum.MARKETING_USER_GROUP.getType()) {
                List<String> marketingGroupIds = GsonUtil.getGson().fromJson(taskEntity.getMarketingGroupUserIds(), new TypeToken<ArrayList>() {
                }.getType());
                if (CollectionUtils.isEmpty(marketingGroupIds)) {
                    log.info("handlerSendTask failed marketingGroupIds null ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                    updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                    mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                }
                marketingUserWithEmailSet = marketingUserGroupManager.listEmailByMarketingUserGroupIds(taskEntity.getEa(), marketingGroupIds);
                if (CollectionUtils.isEmpty(marketingUserWithEmailSet)) {
                    log.info("handlerSendTask failed marketingUserWithEmailSet null ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                    updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                    mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                }

                sendAuditLog("step-marketingUserWithEmailSet", taskEntity, marketingUserWithEmailSet.stream().map(MarketingUserWithEmail::getEmail).collect(Collectors.toList()));
            } else if (taskEntity.getSendRange() == MailSendRangeEnum.TAG.getType()){
                //通过标签查询的邮箱
                boolean excludeTags = !CollectionUtils.isEmpty(taskEntity.getExcludeObjectTags());
                tagEmails = userMarketingAccountManager.getObjectEmailsByTags(taskEntity.getEa(), taskEntity.getSendObjectTags(), taskEntity.getTagOperator(), excludeTags, taskEntity.getExcludeObjectTags());
                if (CollectionUtils.isEmpty(tagEmails)) {
                    log.info("handlerSendTask failed tag to mail null ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                    updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                    mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                }
            }else {
                if (taskEntity.getSendRange() == MailSendRangeEnum.CONFERENCE_MAIL_LIST.getType()) {
                    // 查询邮件中需要替换内容
                    List<ConferenceParamEnum> needReplaceParam = ConferenceParamEnum.getNeedReplaceByContent(taskEntity.getHtml());
                    if (CollectionUtils.isNotEmpty(needReplaceParam)) {
                        sendConferenceEmail(taskEntity, needReplaceParam);
                        return;
                    } else {
                        sendConferenceEmail(taskEntity, null);
                        return;
                    }
                }
                if (taskEntity.getSendRange() == MailSendRangeEnum.LIVE_ENROLL_MAIL_LIST.getType()) {
                    List<LiveParamEnum> needReplaceParam = LiveParamEnum.getNeedReplaceByContent(taskEntity.getHtml());
                    if (CollectionUtils.isNotEmpty(needReplaceParam)) {
                        sendLiveEnrollEmail(taskEntity, needReplaceParam);
                        return;
                    } else {
                        sendLiveEnrollEmail(taskEntity, null);
                        return;
                    }
                }
                if (taskEntity.getSendRange() == MailSendRangeEnum.MARKETING_ACTIVITY.getType()) {
                    sendMarketingActivityEmail(taskEntity);
                    return;
                }
                if (taskEntity.getToUser() == null) {
                    log.info("handlerSendTask failed toUser null ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                    updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                    mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                }
            }

            //地址列表增加发送成员
            List<String> addressList = Lists.newArrayList();
            Pattern p = Pattern.compile(SINGLE_EMAIL_REGEX);
            int invalidMailCount = 0;
            if (taskEntity.getSendRange() == MailSendRangeEnum.MARKETING_USER_GROUP.getType()) {
                for (MarketingUserWithEmail marketingUserWithEmail : marketingUserWithEmailSet) {
                    if (StringUtils.isNotEmpty(marketingUserWithEmail.getEmail())) {
                        Matcher m = p.matcher(marketingUserWithEmail.getEmail());
                        if (m.matches()) {
                            addressList.add(marketingUserWithEmail.getEmail());
                        }else{
                            invalidMailCount++;
                        }
                    }
                }
            } else if (taskEntity.getSendRange() == MailSendRangeEnum.TAG.getType()){
                //通过标签查询的邮箱
                if (CollectionUtils.isNotEmpty(tagEmails)) {
                    addressList.addAll(tagEmails);
                }
            }else {
                if (StringUtils.isNotEmpty(taskEntity.getToUser())) {
                    Set<String> toUserSet = Arrays.stream(taskEntity.getToUser().split(";")).map(String::trim).collect(Collectors.toSet());
                    for (String email : toUserSet) {
                        Matcher m = p.matcher(email);
                        if (m.matches()) {
                            addressList.add(email);
                        }else {
                            invalidMailCount++;
                        }
                    }
                }
            }
            log.info("handlerSendTask send mail validcount:{} invaidcount:{}", addressList.size(), invalidMailCount);

            if (taskEntity.getMailType().equals(MailApiUserTypeEnum.TRIGGER.getType())) {
                sendMailByXsmtpapi(taskEntity, mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), sendUrl);
            } else {
                // 判断邮件内容中是否有对象参数
                String ea = taskEntity.getEa();

                //所有营销邮箱都需要去重过滤N天发送逻辑
                // 去重地址
                addressList = distinctAndReturnOriginAddressList(addressList);
                addressList = enterpriseSpreadRecordManager.filterList(addressList, MarketingActivityActionEnum.MAIL_SERVICE.getSpreadType(), taskEntity.getEa(), taskEntity.getFilterNDaySentUser());
                // 保存/过滤问题邮件
                addressList = filterSendErrorAddress(taskEntity.getEa(), taskEntity.getId(), addressList);
                if (CollectionUtils.isEmpty(addressList)){
                    log.info("handlerSendTask failed filterd mail null ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                    updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                    mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                }
                // 统一转换为小写对比有差异的放入数据库
                checkSendEmailAddress(addressList);
                log.info("handlerSendTask id:{} send email count:{}", taskEntity.getId(), addressList.size());

                List<String> keyList = findHolders(taskEntity.getHtml());
                if (CollectionUtils.isEmpty(keyList)) {
                    // 走原来的逻辑
//                sendMailByAddressList(taskEntity, mailAccountEntity, sendUrl, list, labeleRespOpt.get(), addressList);
                    taskEntity.setToUser(String.join(";", addressList));
                    sendMailByXsmtpapi(taskEntity, mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), sendUrl);
                    mailSendTaskDAO.updateTotalSendCountById(taskEntity.getId(), addressList.size());
                    sendAuditLog("step-sendMailByXsmtpapi", taskEntity, addressList);
                } else {
                    //创建地址列表
                    String addressId = UUIDUtil.getUUID();
                    Optional<String> addressOpt = addAddressList(addressId, mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), mailAccountEntity.getEa());
                    if (!addressOpt.isPresent()) {
                        updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                        mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                        log.info("handlerSendTask exec task failed create address failed ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                        return;
                    }

//                // 去重地址
//                addressList = distinctAndReturnOriginAddressList(addressList);
//                addressList = enterpriseSpreadRecordManager.filterList(addressList, MarketingActivityActionEnum.MAIL_SERVICE.getSpreadType(), taskEntity.getEa(), taskEntity.getFilterNDaySentUser());
//
//                // 保存/过滤问题邮件
//                addressList = filterSendErrorAddress(taskEntity.getEa(), taskEntity.getId(), addressList);
//                // 统一转换为小写对比有差异的放入数据库
//                checkSendEmailAddress(addressList);
//                log.info("handlerSendTask id:{} send email count:{}", taskEntity.getId(), addressList.size());
//                mailSendTaskDAO.updateTotalSendCountById(taskEntity.getId(), addressList.size());
                    int totalTo = addAddressMember(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), addressOpt.get(), addressList);
                    if (totalTo == 0) {
                        updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                        mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                        log.info("handlerSendTask exec task failed addAddressMember failed ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                        return;
                    }

                    // 修改地址列表地址数量
                    mailAddressListDAO.updateMemberCount(totalTo, addressId);

                    //创建发送标签
                    Optional<Integer> labeleRespOpt = addEmailLabel(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), mailAccountEntity.getEa(), taskEntity.getSubject());
                    if (!labeleRespOpt.isPresent()) {
                        updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                        mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                        log.info("handlerSendTask exec task failed create label failed ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                        return;
                    }

                    log.info("sendMail with object start");
                    // 找出邮箱过滤后需要发送的营销用户
                    List<MarketingUserWithEmail> needHandleDatas = Lists.newArrayList();
                    for (MarketingUserWithEmail marketingUserWithEmail : marketingUserWithEmailSet) {
                        //一个邮箱有多个营销用户时任一取一个营销用户发送邮件，只发送一次
                        if (addressList.contains(marketingUserWithEmail.getEmail())
                                && needHandleDatas.stream().noneMatch(x -> x.getEmail().equals(marketingUserWithEmail.getEmail()))) {
                            needHandleDatas.add(marketingUserWithEmail);
                        }
                    }
                    log.info("sendMail with object marketingUsers:{}", needHandleDatas);
                    mailSendTaskDAO.updateTotalSendCountById(taskEntity.getId(), needHandleDatas.size());

                    if (CollectionUtils.isEmpty(needHandleDatas)) {
                        log.info("没有需要发送的邮件地址");
                        return;
                    }

                    sendAuditLog("step-sendMailByExecutePostHttpByRequestBody", taskEntity, needHandleDatas.stream().map(MarketingUserWithEmail::getEmail).collect(Collectors.toList()));

                    String url = getSendCloudApiHostHttpsUrl() + "mail/send";
                    //原始的html，这是替换参数之前的html
                    String originalContent = taskEntity.getHtml();
                    // 发送邮件
                    for (MarketingUserWithEmail marketingUserWithEmail : needHandleDatas) {
                        String marketingUserId = marketingUserWithEmail.getMarketingUserId();
                        String email = marketingUserWithEmail.getEmail();
                        Map<String, String> contentMap = Maps.newHashMap();//存储变量要替换的值
                        Map<String, UserMarketingAccountData> baseInfosByIds = userMarketingAccountManager.getBaseInfosByIds(ea, -10000, Lists.newArrayList(marketingUserId), InfoStateEnum.DETAIL);
                        UserMarketingAccountData userMarketingAccountData = baseInfosByIds.get(marketingUserId);
                        for (String key : keyList) {
                            String[] fields = key.split("\\.");
                            String apiName = fields[0];
                            String fieldName = fields[1];
                            String objectId = null;
                            if (Objects.equals(apiName, CrmObjectApiNameEnum.CRM_LEAD.getName())) {
                                List<UserMarketingAccountData.CrmObjectIdName> crmLeadInfos = userMarketingAccountData.getCrmLeadInfos();
                                if (CollectionUtils.isNotEmpty(crmLeadInfos)) {
                                    objectId = crmLeadInfos.get(0).getId();
                                }
                            } else if (Objects.equals(apiName, CrmObjectApiNameEnum.CUSTOMER.getName())) {
                                List<UserMarketingAccountData.CrmObjectIdName> crmCustomerInfos = userMarketingAccountData.getCrmCustomerInfos();
                                if (CollectionUtils.isNotEmpty(crmCustomerInfos)) {
                                    objectId = crmCustomerInfos.get(0).getId();
                                }
                            } else if (Objects.equals(apiName, CrmObjectApiNameEnum.CONTACT.getName())) {
                                List<UserMarketingAccountData.CrmObjectIdName> crmContactInfos = userMarketingAccountData.getCrmContactInfos();
                                if (CollectionUtils.isNotEmpty(crmContactInfos)) {
                                    objectId = crmContactInfos.get(0).getId();
                                }
                            } else if (Objects.equals(apiName, CrmObjectApiNameEnum.WECHAT.getName())) {
                                List<UserMarketingAccountData.CrmObjectIdName> crmWxUserInfos = userMarketingAccountData.getCrmWxUserInfos();
                                if (CollectionUtils.isNotEmpty(crmWxUserInfos)) {
                                    objectId = crmWxUserInfos.get(0).getId();
                                }
                            } else if (Objects.equals(apiName, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())) {
                                List<UserMarketingAccountData.CrmObjectIdName> crmWxWorkExternalInfos = userMarketingAccountData.getCrmWxWorkExternalInfos();
                                if (CollectionUtils.isNotEmpty(crmWxWorkExternalInfos)) {
                                    objectId = crmWxWorkExternalInfos.get(0).getId();
                                }
                            }
                            String value = buildValue(ea, apiName, fieldName, fields, objectId);
                            contentMap.put(key, value);
                        }
                        log.info("sendMail with object email:{} contentMap:{}", email, contentMap);
                        // 这里深复制一下原始的字符串，防止替换后的字符串影响下次替换
                        String content = originalContent.substring(0);
                        // 物料链接邮箱号埋点参数
                        content = content.replace("!!email!!", email);
                        // 替换占位符
                        for (String key : keyList) {
                            if (contentMap.containsKey(key)) {
                                content = content.replace("$${" + key + "}", contentMap.get(key));
                            }
                        }
                        log.info("sendMail with object email:{}, content:{}", email, content);
                        taskEntity.setHtml(content);
                        Optional<RequestBody> requestBodyOpt = buildEmailSendRequestBody(taskEntity, mailAccountEntity.getApiUser(),
                                mailAccountEntity.getApiKey(), Lists.newArrayList(email), labeleRespOpt.get(), null, MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
                        if (!requestBodyOpt.isPresent()) {
                            log.warn("MailManager.sendCommonEmail requestBodyOpt error taskEntity:{}", taskEntity);
                            continue;
                        }
                        MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
                        });
                        if (sendResult == null || !sendResult.isSuccess()) {
                            log.warn("MailManager.sendCommonEmail sendResult is null body:{}", requestBodyOpt.get());
                        }
                    }
                    updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FINISHED);
                    mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_SUCCESS.getStatus(), taskEntity.getId());
                    addEmailTaskSchedule(taskEntity.getEa(), taskEntity.getId());
                    MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(taskEntity.getEa());
                    entity.setTaskId(taskEntity.getId());
                    entity.setType(MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
                    Date now = new Date();
                    entity.setCreateTime(now);
                    entity.setUpdateTime(now);
                    mailSendTaskResultDAO.insert(entity);
                    // 发送成功后更新labelId
                    mailSendTaskDAO.setLabelIdById(taskEntity.getId(), labeleRespOpt.get());
                }
            }
        } catch (Exception e) {
            log.error("MailManager.handlerSendTask failed ea:{}, taskId: {} ", taskEntity.getEa(), taskEntity.getId(), e);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
        }
    }

    private List<String> getObjectEmailsByPaasTagFilter(String ea, Integer tenantId, TagNameList tagNames) {


        List<String> selectedFileds = ImmutableList.of("_id", "email");
        List<FilterData> filters = userMarketingAccountManager.buildFilterDataListByTag(ea, "HASANYOF", tagNames, false, null, 0, 0);
        PaasQueryFilterArg paasQueryFilterArg = userMarketingAccountManager.buildPassQueryFilterArg(ea, filters.get(0));
        paasQueryFilterArg.setSelectFields(selectedFileds);

        GuavaLimiter.acquire(CRM_OBJ_QUERY_RATE_LIMIT_KEY, ea);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg);
        if (totalCount <= 0) {
            return null;
        }

        int currentCount = 0;
        String lastId = null;
        List<ObjectData> objectDataList = Lists.newArrayList();
        while (currentCount < totalCount) {
            GuavaLimiter.acquire(CRM_OBJ_QUERY_RATE_LIMIT_KEY, ea);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, lastId, 500);
            if (objectDataInnerPage == null || org.apache.commons.collections4.CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentCount += tempSize;
            lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }

        log.info("objectDataInnerPage data:{}", objectDataList);

        return objectDataList.stream().map(objectData -> objectData.getString("email")).distinct().collect(Collectors.toList());
    }

    private void sendAuditLog(String stepName, MailSendTaskEntity taskEntity, List<String> mailList) {
        try {
            if(StringUtils.isBlank(auditLogEas)) {
                return;
            }
            List<String> grayEas = Arrays.asList(auditLogEas.split(","));
            if(CollectionUtils.isEmpty(grayEas) || !grayEas.contains(taskEntity.getEa())) {
                return;
            }
            for (List<String> partitionList : Lists.partition(mailList, MailConstant.BATCH_SEND_SIZE)) {
                MarketingAuditLogArg auditLogArg = MarketingAuditLogArg.builder().ea(taskEntity.getEa())
                        .bizName("MarketingMailSendJob").objectApiName("MarketingMailTask").actionName("MarketingMailSendJob")
                        .objectIds(taskEntity.getId()).extra(stepName).extra1(String.format("MarketingUserWithEmail count: %s" ,mailList.size()))
                        .extra2(String.join(",", partitionList)).build();
                auditLogService.sendAuditLog(auditLogArg, auditLogTopic);
            }
        } catch (Exception e) {
            log.warn("send email audit log error: {}", e.getMessage());
        }
    }

//    public static void main(String[] args) {
//        String[] emails = {
//                "<EMAIL>-com",
//                "<EMAIL>",
//                "<EMAIL>",
//                "<EMAIL>",
//                "user@example-com",
//                "<EMAIL>",
//                "<EMAIL>",
//                "user@example.m",
//                "<EMAIL>",
//                "<EMAIL>",
//                "<EMAIL>",
//                "<EMAIL>"
//        };
//        List<String> addressList = Lists.newArrayList();
//        int invalidMailCount = 0;
//        Pattern pattern = Pattern.compile(SINGLE_EMAIL_REGEX);
//        for (String email : emails) {
//            Matcher matcher = pattern.matcher(email);
//            if (matcher.matches()) {
//                addressList.add(email);
//            }else {
//                invalidMailCount++;
//            }
//        }
//        System.out.println("addressList:" + addressList+ "invalidMailCount:" + invalidMailCount);
//    }
    private String buildValue(String ea, String apiName, String fieldName, String[] fields, String objectId) {
        String defaultValue = ""; //为空时默认值
        if (fields.length >= 3) {
            defaultValue = fields[2];
        }

        if (StringUtils.isBlank(objectId)) {
            return defaultValue;
        } else {
            Map<String, String> objectDataEnTextVal = crmV2Manager.getObjectDataEnTextValWithNull(ea, -10000, apiName, objectId);
            String value = objectDataEnTextVal.get(fieldName);
            if (StringUtils.isBlank(value)) {
                return defaultValue;
            } else {
                return value;
            }
        }
    }

    /**
     * 找出模板内容中所有的${}占位符
     *
     * @param content
     * @return
     */
    private static List<String> findHolders(String content) {
        List<String> keys = Lists.newArrayList();
        int length = content.length();
        for (int i = 0; i < length; i++) {
            char c = content.charAt(i);
            if (c == '$') {
                if ((i + 1) == length) {
                    continue;
                }
                if (content.charAt(i + 1) != '$') {
                    continue;
                }
                int start = i + 2;
                if (start == length) {
                    continue;
                }
                StringBuilder sb = new StringBuilder();
                if (content.charAt(start) == '{') {
                    while (true) {
                        start++;
                        if (start == content.length()) {
                            break;
                        }
                        if (content.charAt(start) != '}') {
                            sb.append(content.charAt(start));
                        } else {
                            break;
                        }
                    }
                    String tempStr = sb.toString();
                    if (StringUtils.isEmpty(tempStr)) {
                        continue;
                    }
                    String[] fields = tempStr.split("\\.");
                    if (fields.length == 2 || fields.length == 3) {
                        keys.add(sb.toString());
                    }
                }
            }
        }
        log.info("MailManager -> findHolders, result:{}", keys);
        return keys;
    }

    /**
     * 统一替换邮件中的活动成员自定义变量参数
     *
     * @param html
     * @param ea
     * @param id   活动成员id
     */
    public String replaceCustomFields(String html, String ea, String id) {
        // 获取活动成员自定义字段
        Map<String, String> customFields = new HashMap<>();
        List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
            crmCustomerFields.forEach(e -> {
                if (2 == e.getFieldProperty()) {
                    customFields.put(e.getFieldCaption(), e.getFieldName());
                }
            });
        }
        if (!customFields.isEmpty() && StringUtils.isNotBlank(id)) {
            Map<String, String> detail = crmV2Manager.getObjectDataEnTextVal(ea, -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), id);
            if (detail != null) {
                for (String customField : customFields.keySet()) {
                    Object value = detail.get(customFields.get(customField));
                    if (null != value) {
                        html = html.replace("{" + customField + "}", value.toString());
                    } else {
                        html = html.replace("{" + customField + "}", "暂无");
                    }
                }
            }
        }
        return html;
    }

    private void checkSendEmailAddress(String sendEmail) {
        if (StringUtils.isBlank(sendEmail)) {
            return;
        }
        String sendEmailLowerCase = sendEmail.toLowerCase();
        if (!sendEmail.equals(sendEmailLowerCase)) {
            mailAddressMapDAO.insertMailAddressMap(new MailAddressMapEntity(sendEmail, sendEmailLowerCase));
        }
    }

    private void checkSendEmailAddress(List<String> sendEmails) {
        if (CollectionUtils.isEmpty(sendEmails)) {
            return;
        }
        ThreadPoolUtils.execute(() -> {
            for (String sendEmail : sendEmails) {
                checkSendEmailAddress(sendEmail);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    private List<String> filterSendErrorAddress(String ea, String taskId, List<String> addressList) {
        if (CollectionUtils.isEmpty(addressList)) {
            return Lists.newArrayList();
        }
        // 每次处理500条数据
        int batchSize = 500;
        List<String> resultAddress = Lists.newArrayList();

        Lists.partition(addressList, batchSize).forEach(currentAddressList -> {
            // 根据当前批次的邮件地址查询当前批次的问题邮件
            List<MailSendErrorAddressEntity> mailSendErrorAddressEntityList =
                    mailSendErrorAddressDAO.queryAllErrorAddressByEaAndAddressList(ea, currentAddressList);
            if (CollectionUtils.isNotEmpty(mailSendErrorAddressEntityList)) {
                // 当前批次的问题邮件
                List<String> filterAddress = Lists.newArrayList();
                Map<String, MailSendErrorAddressEntity> mailSendErrorAddressEntityMap =
                        mailSendErrorAddressEntityList.stream().collect(Collectors.toMap(MailSendErrorAddressEntity::getAddress, data -> data));
                for (String address : currentAddressList) {
                    if (mailSendErrorAddressEntityMap.containsKey(address)) {
                        // 问题邮件过滤
                        filterAddress.add(address);
                    } else {
                        resultAddress.add(address);
                    }
                }
                // 异步保存当前批次的过滤数据
                ThreadPoolUtils.execute(() -> saveFilteredAddresses(ea, taskId, filterAddress, mailSendErrorAddressEntityMap),
                        ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            } else {
                resultAddress.addAll(currentAddressList);
            }
        });

        return resultAddress;
    }

    /**
     * 批量保存过滤的地址到数据库
     */
    private void saveFilteredAddresses(String ea, String taskId, List<String> filterAddress,
                                       Map<String, MailSendErrorAddressEntity> mailSendErrorAddressEntityMap) {
        if (CollectionUtils.isEmpty(filterAddress)) {
            return;
        }
        List<MailFilterDetailEntity> batchInsertList = Lists.newArrayList();

        for (String address : filterAddress) { // 这里filterAddress最多只有500条，因此无需考虑此处来分批批量插入
            MailSendErrorAddressEntity mailSendErrorAddressEntity = mailSendErrorAddressEntityMap.get(address);
            if (mailSendErrorAddressEntity != null) {
                MailFilterDetailEntity mailFilterDetailEntity = new MailFilterDetailEntity();
                mailFilterDetailEntity.setId(UUIDUtil.getUUID());
                mailFilterDetailEntity.setEa(ea);
                mailFilterDetailEntity.setTaskId(taskId);
                mailFilterDetailEntity.setAddress(address);
                mailFilterDetailEntity.setEventType(mailSendErrorAddressEntity.getEventType());
                mailFilterDetailEntity.setSubStat(mailSendErrorAddressEntity.getSubStat());
                batchInsertList.add(mailFilterDetailEntity);
            }
        }
        // 每次200的大小批量插入剩余数据
        Lists.partition(batchInsertList, 200).forEach(e -> mailFilterDetailDAO.batchInsertMailFilterDetail(e));
    }

    private boolean filterSendErrorAddress(String ea, String taskId, String address) {
        List<MailSendErrorAddressEntity> mailSendErrorAddressEntityList = mailSendErrorAddressDAO.queryAllErrorAddressByEa(ea);
        if (CollectionUtils.isEmpty(mailSendErrorAddressEntityList)) {
            return false;
        }
        for (MailSendErrorAddressEntity mailSendErrorAddressEntity : mailSendErrorAddressEntityList) {
            if (mailSendErrorAddressEntity.getAddress().equals(address)) {
                MailFilterDetailEntity mailFilterDetailEntity = new MailFilterDetailEntity();
                mailFilterDetailEntity.setId(UUIDUtil.getUUID());
                mailFilterDetailEntity.setEa(ea);
                mailFilterDetailEntity.setTaskId(taskId);
                mailFilterDetailEntity.setAddress(address);
                mailFilterDetailEntity.setEventType(mailSendErrorAddressEntity.getEventType());
                mailFilterDetailEntity.setSubStat(mailSendErrorAddressEntity.getSubStat());
                mailFilterDetailDAO.insertMailFilterDetail(mailFilterDetailEntity);
                return true;
            }
        }
        return false;
    }



    public Map<String, String> getSendAddressByLowerCase(List<String> address) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(address)) {
            return result;
        }
        List<MailAddressMapEntity> mailAddressMapEntities = mailAddressMapDAO.queryEmailMapByLowerCase(address);
        if (CollectionUtils.isEmpty(mailAddressMapEntities)) {
            return result;
        }
        result = mailAddressMapEntities.stream().collect(Collectors.toMap(MailAddressMapEntity::getSendEmailAddressLowerCase, MailAddressMapEntity::getSendEmailAddress, (v1, v2) -> v1));
        return result;
    }

    /**
     * 直接发送邮件不保存发送详情(使用地址列表发送)
     */
    public Result<SendEmailResult> sendEmailWithOutSaveDetail(SendEmailArg arg) {
        if (arg.isWrongParam()) {
            log.warn("MailManager.sendEmailWithOutSaveDetail param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String url = getSendCloudApiHostHttpsUrl() + "mail/send";
        List<String> toUser = arg.getMailList();
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(arg.getEa(), arg.getAccountType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.sendEmailWithOutSaveDetail mailAccountEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.API_USER_NOT_FOUND);
        }
        // 若发送数量大于100则使用地址列表
        Boolean sendByAddressList = false;
        if (toUser.size() > 100) {
            String addressId = UUIDUtil.getUUID();
            Optional<String> addressOpt = addAddressList(addressId, mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), mailAccountEntity.getEa());
            if (!addressOpt.isPresent()) {
                log.warn("MailManager.sendEmailWithOutSaveDetail addressOpt error arg:{}", arg);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            int totalTo = addAddressMember(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), addressOpt.get(), arg.getMailList());
            if (totalTo == 0) {
                log.warn("MailManager.sendEmailWithOutSaveDetail totalTo is 0 arg:{}", arg);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            toUser = Lists.newArrayList(addressOpt.get());
            sendByAddressList = true;
        }
        //创建发送标签
        Optional<Integer> labelRespOpt = addEmailLabel(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), mailAccountEntity.getEa(), arg.getTitle());
        if (!labelRespOpt.isPresent()) {
            log.warn("MailManager.sendEmailWithOutSaveDetail labelRespOpt is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        MailSendTaskEntity taskEntity = new MailSendTaskEntity();
        taskEntity.setEa(arg.getEa());
        taskEntity.setSenderIds(GsonUtil.getGson().toJson(arg.getSenderIds()));
        if (CollectionUtils.isNotEmpty(arg.getReplyIds())) {
            taskEntity.setReplyIds(GsonUtil.getGson().toJson(arg.getReplyIds()));
        }
        taskEntity.setSubject(arg.getTitle());
        taskEntity.setHtml(arg.getContent());
        taskEntity.setMailType(arg.getAccountType());
        taskEntity.setAttachments(arg.getAttachments());
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put(MailConstant.EA_HEADERS_KEY, taskEntity.getEa());
        if (StringUtils.isNotEmpty(arg.getTaskId())) {
            headerMap.put(MailConstant.TASK_ID_HEADERS_KEY, arg.getTaskId());
        }
        if (StringUtils.isNotEmpty(arg.getTriggerTaskInstanceId())) {
            headerMap.put(MailConstant.TRIGGER_TASK_INSTANCE_ID_HEADERS_KEY, arg.getTriggerTaskInstanceId());
        }
        Optional<RequestBody> requestBodyOpt = buildEmailSendRequestBody(taskEntity, mailAccountEntity.getApiUser(),
                mailAccountEntity.getApiKey(), toUser, labelRespOpt.get(), null, sendByAddressList ? MailSendTypeEnum.SEND_BY_ADDRESS_LIST.getType() : MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType(), headerMap);
        if (!requestBodyOpt.isPresent()) {
            log.warn("MailManager.sendEmailByAddressList failed to is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
        });
        if (sendResult == null || !sendResult.isSuccess()) {
            log.warn("MailManager.sendEmailWithOutSaveDetail sendResult is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        SendEmailResult result = new SendEmailResult();
        result.setLabelId(labelRespOpt.get());
        result.setTaskEntity(taskEntity);
        return Result.newSuccess(result);
    }

    /**
     * 通过xmstpapi发送邮件(不保存记录)
     *
     * @param arg
     */
    public Result<SendEmailResult> sendEmailWithOutSaveDetailV2(SendEmailArg arg) {
        if (arg.isWrongParam()) {
            log.warn("MailManager.sendEmailWithOutSaveDetailV2 param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String url = getSendCloudApiHostHttpsUrl() + "mail/send";
        List<String> toList = arg.getMailList();
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(arg.getEa(), arg.getAccountType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.sendEmailWithOutSaveDetailV2 mailAccountEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.API_USER_NOT_FOUND);
        }
        String apiUser = mailAccountEntity.getApiUser();
        String apiKey = mailAccountEntity.getApiKey();
        String ea = mailAccountEntity.getEa();

        // 去重地址
        toList = distinctAndReturnOriginAddressList(toList);


        MailSendTaskEntity taskEntity = new MailSendTaskEntity();
        taskEntity.setId(arg.getTaskId());
        taskEntity.setEa(arg.getEa());
        taskEntity.setSenderIds(GsonUtil.getGson().toJson(arg.getSenderIds()));
        if (CollectionUtils.isNotEmpty(arg.getReplyIds())) {
            taskEntity.setReplyIds(GsonUtil.getGson().toJson(arg.getReplyIds()));
        }
        taskEntity.setSubject(arg.getTitle());
        taskEntity.setHtml(arg.getContent().replace("!!email!!", "%email%"));
        taskEntity.setMailType(arg.getAccountType());
        taskEntity.setAttachments(arg.getAttachments());

        //创建发送标签
        Optional<Integer> labeleRespOpt = addEmailLabel(apiUser, apiKey, ea, arg.getTitle());
        if (!labeleRespOpt.isPresent()) {
            log.info("sendMailByXsmtpapiWithoutSave exec failed create label failed ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put(MailConstant.EA_HEADERS_KEY, taskEntity.getEa());
        if (StringUtils.isNotEmpty(arg.getTaskId())) {
            headerMap.put(MailConstant.TASK_ID_HEADERS_KEY, arg.getTaskId());
        }
        if (StringUtils.isNotEmpty(arg.getTriggerTaskInstanceId())) {
            headerMap.put(MailConstant.TRIGGER_TASK_INSTANCE_ID_HEADERS_KEY, arg.getTriggerTaskInstanceId());
        }
        List<String> emailIds = Lists.newArrayList();

        for (List<String> toUserList : Lists.partition(toList, MailConstant.BATCH_SEND_SIZE)) {
            XsmtpapiReq xsmtpapiArg = new XsmtpapiReq();
            xsmtpapiArg.setTo(toUserList);
            HashMap<String, Object> sub = new HashMap<>();
            sub.put("%email%", toUserList);
            xsmtpapiArg.setSub(sub);
            Optional<RequestBody> requestBodyOpt = this.buildEmailSendRequestBody(taskEntity, apiUser, apiKey, null, labeleRespOpt.get(), GsonUtil.getGson().toJson(xsmtpapiArg), MailSendTypeEnum.SEND_BY_XSMTPAPI.getType(), headerMap);
            if (!requestBodyOpt.isPresent()) {
                log.info("sendMailByXsmtpapiWithoutSave build request body failed ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }

            MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
            });
            if (sendResult == null || !sendResult.isSuccess()) {
                log.error("MailManager.sendEmailWithOutSaveDetailV2 failed taskEntityId:{}", taskEntity.getId());
            } else {
                if (sendResult.getInfo() == null || CollectionUtils.isEmpty(sendResult.getInfo().getEmailIdList())) {
                    log.error("EmailManager.sendEmailWithOutSaveDetailV2 return emailIdList null taskEntityId:{}", taskEntity.getId());
                } else {
                    emailIds.addAll(sendResult.getInfo().getEmailIdList());
                }
            }
        }

        if (CollectionUtils.isEmpty(emailIds)) {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        SendEmailResult result = new SendEmailResult();
        result.setLabelId(labeleRespOpt.get());
        result.setTaskEntity(taskEntity);
        result.setMailIdList(emailIds);
        return Result.newSuccess(result);
    }


    public void sendConferenceEmail(MailSendTaskEntity taskEntity, List<ConferenceParamEnum> needReplaceParam) {
        if (taskEntity == null) {
            log.warn("MailManager.sendConferenceEmail error taskEntity is null");
            return;
        }
        // 查询额外信息
        MailSendExtraDataEntity mailSendExtraDataEntity = mailSendExtraDataDAO.getExtraDataByTaskId(taskEntity.getId());
        if (mailSendExtraDataEntity == null || mailSendExtraDataEntity.getExtraData() == null || (MapUtils.isEmpty(mailSendExtraDataEntity.getExtraData().getConferenceEnrollMap()) && CollectionUtils.isEmpty(mailSendExtraDataEntity.getExtraData().getCampaignIds()))) {
            log.warn("MailManager.sendConferenceEmail error mailSendExtraDataEntity is null");
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }
        Map<String, String> enrollMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(mailSendExtraDataEntity.getExtraData().getConferenceEnrollMap())) {
            enrollMap = mailSendExtraDataEntity.getExtraData().getConferenceEnrollMap();
        } else {
            Map<String, String> userMailDataMap = campaignMergeDataManager.getUserEmailByCampaignId(mailSendExtraDataEntity.getEa(), mailSendExtraDataEntity.getExtraData().getCampaignIds());
            if (MapUtils.isEmpty(userMailDataMap)) {
                log.info("MailManager sendConferenceEmail failed userMailDataMap is null ");
            }
            Map<String, String> activityEnrollIdMap = campaignMergeDataManager.campaignIdToActivityEnrollIdMap(Lists.newArrayList(userMailDataMap.keySet()));
            for (Map.Entry<String, String> entry : userMailDataMap.entrySet()) {
                String enrollId = activityEnrollIdMap.get(entry.getKey());
                if (StringUtils.isNotBlank(enrollId)) {
                    enrollMap.put(enrollId, entry.getValue());
                }
            }
        }
        // 查询报名数据
        List<ConferenceEnrollBaseInfoDTO> conferenceEnrollBaseInfoDTOList = activityEnrollDataDAO.queryConferenceEnrollBaseInfoByIds(Lists.newArrayList(enrollMap.keySet()));
        if (CollectionUtils.isEmpty(conferenceEnrollBaseInfoDTOList)) {
            log.warn("MailManager.sendConferenceEmail conferenceEnrollBaseInfoDTOList is empty enrollMap:{}", enrollMap);
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }

        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(taskEntity.getEa(), MailApiUserTypeEnum.BATCH.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.sendConferenceEmail mailAccountEntity is null taskEntity:{}", taskEntity);
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }

        //创建发送标签
        Optional<Integer> labelRespOpt = addEmailLabel(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), mailAccountEntity.getEa(), taskEntity.getSubject());
        if (!labelRespOpt.isPresent()) {
            log.warn("MailManager.sendConferenceEmail labelRespOpt is null taskEntity:{}", taskEntity);
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }

        mailSendTaskDAO.updateTotalSendCountById(taskEntity.getId(), enrollMap.size());

        String url = getSendCloudApiHostHttpsUrl() + "mail/send";
        // 单个发送邮件
        String html = taskEntity.getHtml();
        Set<String> sentEmailSet = Sets.newHashSet();
        for (ConferenceEnrollBaseInfoDTO conferenceEnrollBaseInfoDTO : conferenceEnrollBaseInfoDTOList) {
            conferenceEnrollBaseInfoDTO.setEnrollEmail(enrollMap.get(conferenceEnrollBaseInfoDTO.getActivityEnrollId()));
            if (StringUtils.isBlank(conferenceEnrollBaseInfoDTO.getEnrollEmail()) || sentEmailSet.contains(conferenceEnrollBaseInfoDTO.getEnrollEmail().toLowerCase())) {
                continue;
            }
            filterSendErrorAddress(taskEntity.getEa(), taskEntity.getId(), conferenceEnrollBaseInfoDTO.getEnrollEmail());
            checkSendEmailAddress(conferenceEnrollBaseInfoDTO.getEnrollEmail());
            conferenceEnrollBaseInfoDTO.setStartTime(DateUtil.format(conferenceEnrollBaseInfoDTO.getStartTimeStamp(), "yyyy-MM-dd HH:mm"));
            conferenceEnrollBaseInfoDTO.setEndTime(DateUtil.format(conferenceEnrollBaseInfoDTO.getEndTimeStamp(), "yyyy-MM-dd HH:mm"));
            conferenceEnrollBaseInfoDTO.setActivityUrl(
                    host + "/ec/cml-marketing/release/web/cml-marketing.html?conferenceId=" + conferenceEnrollBaseInfoDTO.getActivityId() + "&marketingEventId=" + conferenceEnrollBaseInfoDTO
                            .getMarketingEventId() + "&byshare=1&_hash=/cml/h5/conference_detail");

            // 物料链接邮箱号埋点参数
            html = taskEntity.getHtml().replace("!!email!!", conferenceEnrollBaseInfoDTO.getEnrollEmail());

            // 替换发送内容
            if (CollectionUtils.isNotEmpty(needReplaceParam)) {
                taskEntity.setHtml(ConferenceParamEnum.replaceContent(taskEntity.getEa(), html, needReplaceParam, conferenceEnrollBaseInfoDTO, host, conferenceEnrollBaseInfoDTO.getCampaignId()));
            } else {
                taskEntity.setHtml(html);
            }
            taskEntity.setHtml(this.replaceCustomFields(taskEntity.getHtml(), taskEntity.getEa(), conferenceEnrollBaseInfoDTO.getCampaignMembersObjId()));
            Optional<RequestBody> requestBodyOpt = buildEmailSendRequestBody(taskEntity, mailAccountEntity.getApiUser(),
                    mailAccountEntity.getApiKey(), Lists.newArrayList(conferenceEnrollBaseInfoDTO.getEnrollEmail()), labelRespOpt.get(), null, MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
            if (!requestBodyOpt.isPresent()) {
                log.warn("MailManager.sendConferenceEmail requestBodyOpt error taskEntity:{}", taskEntity);
                continue;
            }
            MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
            });
            if (sendResult == null || !sendResult.isSuccess()) {
                log.warn("MailManager.sendConferenceEmail sendResult is null conferenceEnrollBaseInfoDTO:{}", conferenceEnrollBaseInfoDTO);
            } else {
                sentEmailSet.add(conferenceEnrollBaseInfoDTO.getEnrollEmail().toLowerCase());
            }
        }

        updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FINISHED);
        mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_SUCCESS.getStatus(), taskEntity.getId());
        addEmailTaskSchedule(taskEntity.getEa(), taskEntity.getId());
        MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(taskEntity.getEa());
        entity.setTaskId(taskEntity.getId());
        entity.setType(MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        mailSendTaskResultDAO.insert(entity);
        // 发送成功后更新labelId
        mailSendTaskDAO.setLabelIdById(taskEntity.getId(), labelRespOpt.get());
        enrollMap.clear();
        conferenceEnrollBaseInfoDTOList.clear();
    }

    public void sendLiveEnrollEmail(MailSendTaskEntity taskEntity, List<LiveParamEnum> needReplaceParam) {
        if (taskEntity == null) {
            log.warn("MailManager.sendLiveEnrollEmail error taskEntity is null");
            return;
        }
        // 查询额外信息
        MailSendExtraDataEntity mailSendExtraDataEntity = mailSendExtraDataDAO.getExtraDataByTaskId(taskEntity.getId());
        if (mailSendExtraDataEntity == null || mailSendExtraDataEntity.getExtraData() == null || CollectionUtils.isEmpty(mailSendExtraDataEntity.getExtraData().getCampaignIds())) {
            log.warn("MailManager.sendLiveEnrollEmail error mailSendExtraDataEntity is null");
            return;
        }
        List<String> campaignIds = mailSendExtraDataEntity.getExtraData().getCampaignIds();
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignIds.get(0));
        if (campaignMergeDataEntity == null) {
            log.warn("MailManager.campaignMergeDataEntity is null");
            return;
        }
        String marketingEventId = campaignMergeDataEntity.getMarketingEventId();
        String ea = campaignMergeDataEntity.getEa();
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
        if (marketingLiveEntity == null) {
            log.warn("MailManager.marketingLiveEntity marketingLiveEntity is null");
            return;
        }
        Map<String, String> userMailDataMap = campaignMergeDataManager.getUserEmailByCampaignId(ea, campaignIds);
        if (MapUtils.isEmpty(userMailDataMap)) {
            log.warn("MailManager.marketingLiveEntity userMailDataMap is null");
            return;
        }

        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(taskEntity.getEa(), MailApiUserTypeEnum.BATCH.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.sendLiveEnrollEmail mailAccountEntity is null taskEntity:{}", taskEntity);
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }

        //创建发送标签
        Optional<Integer> labelRespOpt = addEmailLabel(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), mailAccountEntity.getEa(), taskEntity.getSubject());
        if (!labelRespOpt.isPresent()) {
            log.warn("MailManager.sendLiveEnrollEmail labelRespOpt is null taskEntity:{}", taskEntity);
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }

        mailSendTaskDAO.updateTotalSendCountById(taskEntity.getId(), userMailDataMap.size());

        String url = getSendCloudApiHostHttpsUrl() + "mail/send";
        // 单个发送邮件
        String html = taskEntity.getHtml();
        Set<String> sentEmailSet = Sets.newHashSet();
        for (Map.Entry<String, String> entry : userMailDataMap.entrySet()) {
            if (StringUtils.isBlank(entry.getValue()) || sentEmailSet.contains(entry.getValue().toLowerCase())) {
                continue;
            }

            // 物料链接邮箱号埋点参数
            html = taskEntity.getHtml().replace("!!email!!", entry.getValue());

            // 替换发送内容
            if (CollectionUtils.isNotEmpty(needReplaceParam)) {
                taskEntity.setHtml(LiveParamEnum.replaceContent(html, needReplaceParam, marketingLiveEntity));
            } else {
                taskEntity.setHtml(html);
            }
            filterSendErrorAddress(taskEntity.getEa(), taskEntity.getId(), entry.getValue());
            checkSendEmailAddress(entry.getValue());
            CampaignMergeDataEntity campaignMergeDataById = campaignMergeDataDAO.getCampaignMergeDataById(entry.getKey());
            if (campaignMergeDataById != null) {
                taskEntity.setHtml(this.replaceCustomFields(taskEntity.getHtml(), taskEntity.getEa(), campaignMergeDataById.getCampaignMembersObjId()));
            }
            Optional<RequestBody> requestBodyOpt = buildEmailSendRequestBody(taskEntity, mailAccountEntity.getApiUser(),
                    mailAccountEntity.getApiKey(), Lists.newArrayList(entry.getValue()), labelRespOpt.get(), null, MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
            if (!requestBodyOpt.isPresent()) {
                log.warn("MailManager.sendLiveEnrollEmail requestBodyOpt error taskEntity:{}", taskEntity);
                continue;
            }
            MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
            });
            if (sendResult == null || !sendResult.isSuccess()) {
                log.warn("MailManager.sendLiveEnrollEmail sendResult is null campaignMergeId:{}", entry.getKey());
            }
            sentEmailSet.add(entry.getValue().toLowerCase());
        }

        updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FINISHED);
        mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_SUCCESS.getStatus(), taskEntity.getId());
        addEmailTaskSchedule(taskEntity.getEa(), taskEntity.getId());
        MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(taskEntity.getEa());
        entity.setTaskId(taskEntity.getId());
        entity.setType(MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        mailSendTaskResultDAO.insert(entity);
        // 发送成功后更新labelId
        mailSendTaskDAO.setLabelIdById(taskEntity.getId(), labelRespOpt.get());
        userMailDataMap.clear();
    }

    public void sendMarketingActivityEmail(MailSendTaskEntity taskEntity) {
        if (taskEntity == null) {
            log.warn("MailManager.sendMarketingActivityEmail error taskEntity is null");
            return;
        }
        // 查询额外信息
        MailSendExtraDataEntity mailSendExtraDataEntity = mailSendExtraDataDAO.getExtraDataByTaskId(taskEntity.getId());
        if (mailSendExtraDataEntity == null || mailSendExtraDataEntity.getExtraData() == null || CollectionUtils.isEmpty(mailSendExtraDataEntity.getExtraData().getCampaignIds())) {
            log.warn("MailManager.sendMarketingActivityEmail error mailSendExtraDataEntity is null");
            return;
        }
        List<String> campaignIds = mailSendExtraDataEntity.getExtraData().getCampaignIds();
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignIds.get(0));
        if (campaignMergeDataEntity == null) {
            log.warn("MailManager.sendMarketingActivityEmail campaignMergeDataEntity is null");
            return;
        }
        String ea = campaignMergeDataEntity.getEa();
        Map<String, String> userMailDataMap = campaignMergeDataManager.getUserEmailByCampaignId(ea, campaignIds);
        if (MapUtils.isEmpty(userMailDataMap)) {
            log.warn("MailManager.sendMarketingActivityEmail userMailDataMap is null");
            return;
        }

        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(taskEntity.getEa(), MailApiUserTypeEnum.BATCH.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.sendMarketingActivityEmail mailAccountEntity is null taskEntity:{}", taskEntity);
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }

        //创建发送标签
        Optional<Integer> labelRespOpt = addEmailLabel(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey(), mailAccountEntity.getEa(), taskEntity.getSubject());
        if (!labelRespOpt.isPresent()) {
            log.warn("MailManager.sendMarketingActivityEmail labelRespOpt is null taskEntity:{}", taskEntity);
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }

        mailSendTaskDAO.updateTotalSendCountById(taskEntity.getId(), userMailDataMap.size());

        String url = getSendCloudApiHostHttpsUrl() + "mail/send";
        // 单个发送邮件
        String html = taskEntity.getHtml();

        ObjectData mktDetail = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), taskEntity.getMarketingEventId());
        Set<String> sentEmailSet = Sets.newHashSet();
        for (Map.Entry<String, String> entry : userMailDataMap.entrySet()) {
            if (StringUtils.isBlank(entry.getValue()) || sentEmailSet.contains(entry.getValue().toLowerCase())) {
                continue;
            }
            filterSendErrorAddress(taskEntity.getEa(), taskEntity.getId(), entry.getValue());
            checkSendEmailAddress(entry.getValue());

            // 物料链接邮箱号埋点参数
            html = taskEntity.getHtml().replace("!!email!!", entry.getValue());

            // 活动参数替换
            if (mktDetail != null) {
                if (html.contains("{市场活动名称}")) {
                    html = html.replace("{市场活动名称}", mktDetail.get("name") == null ? "暂无" : mktDetail.get("name").toString());
                }
                if (html.contains("{活动开始时间}")) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    if (mktDetail.get("begin_time") != null) {
                        html = html.replace("{活动开始时间}", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("begin_time")).longValue())));
                    }
                }
                if (html.contains("{活动结束时间}")) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    if (mktDetail.get("end_time") != null) {
                        html = html.replace("{活动结束时间}", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("end_time")).longValue())));
                    }
                }
            }
            taskEntity.setHtml(html);
            CampaignMergeDataEntity campaignMergeDataById = campaignMergeDataDAO.getCampaignMergeDataById(entry.getKey());
            if (campaignMergeDataById != null) {
                taskEntity.setHtml(this.replaceCustomFields(taskEntity.getHtml(), taskEntity.getEa(), campaignMergeDataById.getCampaignMembersObjId()));
            }
            Optional<RequestBody> requestBodyOpt = buildEmailSendRequestBody(taskEntity, mailAccountEntity.getApiUser(),
                    mailAccountEntity.getApiKey(), Lists.newArrayList(entry.getValue()), labelRespOpt.get(), null, MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
            if (!requestBodyOpt.isPresent()) {
                log.warn("MailManager.sendMarketingActivityEmail requestBodyOpt error taskEntity:{}", taskEntity);
                continue;
            }
            MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
            });
            if (sendResult == null || !sendResult.isSuccess()) {
                log.warn("MailManager.sendMarketingActivityEmail sendResult is null campaignMergeId:{}", entry.getKey());
            }
            sentEmailSet.add(entry.getValue().toLowerCase());
        }
        updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FINISHED);
        mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_SUCCESS.getStatus(), taskEntity.getId());
        addEmailTaskSchedule(taskEntity.getEa(), taskEntity.getId());
        MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(taskEntity.getEa());
        entity.setTaskId(taskEntity.getId());
        entity.setType(MailSendTypeEnum.SEND_BY_COMMON_ADDRESS.getType());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        mailSendTaskResultDAO.insert(entity);
        // 发送成功后更新labelId
        mailSendTaskDAO.setLabelIdById(taskEntity.getId(), labelRespOpt.get());
        userMailDataMap.clear();
    }

    /**
     * 通过xmstpapi发送邮件
     *
     * @param taskEntity
     * @param apiUser
     * @param apiKey
     * @param url
     */
    public void sendMailByXsmtpapi(MailSendTaskEntity taskEntity, String apiUser, String apiKey, String url) {
        if (StringUtils.isEmpty(taskEntity.getToUser())) {
            log.info("MailManager.sendMailByXsmtpapi toList is null taskId:{}", taskEntity.getId());
            return;
        }

        List<String> toList = Arrays.asList(taskEntity.getToUser().split(";")).stream().map(s -> (s.trim())).collect(Collectors.toList());
        // 去重地址
        toList = distinctAndReturnOriginAddressList(toList);
        toList = enterpriseSpreadRecordManager.filterList(toList, MarketingActivityActionEnum.MAIL_SERVICE.getSpreadType(), taskEntity.getEa(), taskEntity.getFilterNDaySentUser());

        // 保存/过滤问题邮件
        toList = filterSendErrorAddress(taskEntity.getEa(), taskEntity.getId(), toList);
        // 统一转换为小写对比有差异的放入数据库
        checkSendEmailAddress(toList);
        log.info("handlerSendTask id:{} send email count:{}", taskEntity.getId(), toList.size());
        mailSendTaskDAO.updateTotalSendCountById(taskEntity.getId(), toList.size());

        // 物料链接邮箱号埋点参数
        String html = taskEntity.getHtml().replace("!!email!!", "%email%");
        taskEntity.setHtml(html);

        //创建发送标签
        Optional<Integer> labeleRespOpt = addEmailLabel(apiUser, apiKey, taskEntity.getEa(), taskEntity.getSubject());
        if (!labeleRespOpt.isPresent()) {
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            log.info("handlerSendTask exec task failed create label failed ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
            return;
        }

        List<String> emailIds = Lists.newArrayList();

        for (List<String> toUserList : Lists.partition(toList, MailConstant.BATCH_SEND_SIZE)) {
            XsmtpapiReq xsmtpapiArg = new XsmtpapiReq();
            xsmtpapiArg.setTo(toUserList);
            HashMap<String, Object> sub = new HashMap<>();
            sub.put("%email%", toUserList);
            xsmtpapiArg.setSub(sub);
            Optional<RequestBody> requestBodyOpt = buildEmailSendRequestBody(taskEntity, apiUser, apiKey, null, labeleRespOpt.get(),
                    GsonUtil.getGson().toJson(xsmtpapiArg), MailSendTypeEnum.SEND_BY_XSMTPAPI.getType());
            if (!requestBodyOpt.isPresent()) {
                mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
                log.info("sendMailByXsmtpapi build request body failed ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
                return;
            }

            MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
            });
            if (sendResult == null || !sendResult.isSuccess()) {
                log.error("MailManager.sendEmailByCommonAddress failed taskEntityId:{} toUser:{} sendResult:{}", taskEntity.getId(), toUserList, sendResult);
            } else {
                if (sendResult.getInfo() == null || sendResult.getInfo() == null || CollectionUtils.isEmpty(sendResult.getInfo().getEmailIdList())) {
                    log.error("EmailManager.sendEmailByCommonAddress return emailIdList null taskEntityId:{}", taskEntity.getId());
                } else {
                    emailIds.addAll(sendResult.getInfo().getEmailIdList());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(emailIds)) {
            // 发送成功后更新labelId
            mailSendTaskDAO.setLabelIdById(taskEntity.getId(), labeleRespOpt.get());
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_SUCCESS.getStatus(), taskEntity.getId());
            addEmailTaskSchedule(taskEntity.getEa(), taskEntity.getId());
            //保存发送结果
            ThreadPoolUtils.execute(() -> {
                List<List<String>> partition = Lists.partition(emailIds, 100);
                for (List<String> part : partition) {
                    List<MailSendTaskResultEntity> entities = Lists.newArrayList();
                    for (String mailId : part) {
                        MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
                        entity.setId(UUIDUtil.getUUID());
                        entity.setEa(taskEntity.getEa());
                        entity.setTaskId(taskEntity.getId());
                        entity.setMailId(mailId);
                        String[] mailRex = StringUtils.split(mailId, "$");
                        if (mailRex != null && mailRex.length == 2) {
                            entity.setMail(mailRex[1]);
                        }
                        entity.setType(MailSendTypeEnum.SEND_BY_XSMTPAPI.getType());
                        Date now = new Date();
                        entity.setCreateTime(now);
                        entity.setUpdateTime(now);
                        entities.add(entity);
                    }
                    mailSendTaskResultDAO.batchInsert(entities);
                }
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            // 之后更新已发送邮件
            enterpriseSpreadRecordManager.upsertList(toList, MarketingActivityActionEnum.MAIL_SERVICE.getSpreadType(), taskEntity.getEa());
        } else {
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
        }
    }

    public void addEmailTaskSchedule(String ea, String id) {
        // 邮件发送状态任务
        AddTaskScheduleArg addTaskScheduleArg = new AddTaskScheduleArg();
        addTaskScheduleArg.setEa(ea);
        addTaskScheduleArg.setTaskId(id);
        addTaskScheduleArg.setTaskType(TaskScheduleTypeEnum.MAIL_SEND_STATUS.getType());
        // 只需要要执行前面两天
        addTaskScheduleArg.setStartTime(new Date());
        addTaskScheduleArg.setEndTime(DateUtil.plusDay(new Date(), 2));
        addTaskScheduleArg.setMaxExecutionCount(100);

        List<AddTaskScheduleArg.Config> configList = Lists.newArrayList();
        // 前面10分钟 每5分钟执行一次
        AddTaskScheduleArg.Config firstConfig = new AddTaskScheduleArg.Config();
        firstConfig.setStartOffsetSeconds(0);
        firstConfig.setEndOffsetSeconds(60 * 10);
        firstConfig.setPriority(1);
        firstConfig.setIntervalSeconds(60 * 5);
        configList.add(firstConfig);
        // 后面 10 分钟 到 2 小时，每30分钟执行一次
        AddTaskScheduleArg.Config secondConfig = new AddTaskScheduleArg.Config();
        secondConfig.setStartOffsetSeconds(60 * 10);
        secondConfig.setEndOffsetSeconds(60 * 60 * 2);
        secondConfig.setPriority(2);
        secondConfig.setIntervalSeconds(60 * 30);
        configList.add(secondConfig);

        // 后面 2 小时 到 2 天，每12小时执行一次 环球资源的邮件很多，需要延长时间
        AddTaskScheduleArg.Config thirdConfig = new AddTaskScheduleArg.Config();
        thirdConfig.setStartOffsetSeconds(60 * 60 * 2);
        thirdConfig.setEndOffsetSeconds(60 * 60 * 24 * 2);
        thirdConfig.setPriority(3);
        thirdConfig.setIntervalSeconds(60 * 60 * 12);
        configList.add(thirdConfig);

        addTaskScheduleArg.setConfigList(configList);
        taskScheduleManager.addTaskSchedule(addTaskScheduleArg);
        // 单个邮件统计任务
        addEmailTaskStatisticSchedule(ea, id);
    }

    // 将过去三十天的邮件发送任务写入调度表 只执行一次
    public void refreshOldTaskScheduleConfig(String ea) {
        List<MailSendTaskEntity> mailSendTaskEntityList = mailSendTaskDAO.listAllWithoutBigField();
        if (CollectionUtils.isEmpty(mailSendTaskEntityList)) {
            return;
        }
        mailSendTaskEntityList.forEach(e -> addEmailTaskStatisticSchedule(e.getEa(), e.getId()));
    }

    private void addEmailTaskStatisticSchedule(String ea, String id) {
        AddTaskScheduleArg mailTaskStatisticScheduleArg = new AddTaskScheduleArg();
        mailTaskStatisticScheduleArg.setEa(ea);
        mailTaskStatisticScheduleArg.setTaskId(id);
        mailTaskStatisticScheduleArg.setTaskType(TaskScheduleTypeEnum.MAIL_TASK_STATISTIC.getType());
        // 需要要执行前面30天
        mailTaskStatisticScheduleArg.setStartTime(new Date());
        mailTaskStatisticScheduleArg.setEndTime(DateUtil.plusMonth(new Date(), 1));
        mailTaskStatisticScheduleArg.setMaxExecutionCount(100);

        List<AddTaskScheduleArg.Config> statisticConfigList = Lists.newArrayList();
        // 前面1小时 每10分钟执行一次
        AddTaskScheduleArg.Config statisticFirstConfig = new AddTaskScheduleArg.Config();
        statisticFirstConfig.setStartOffsetSeconds(0);
        statisticFirstConfig.setEndOffsetSeconds(60 * 60);
        statisticFirstConfig.setPriority(1);
        statisticFirstConfig.setIntervalSeconds(60 * 10);
        statisticConfigList.add(statisticFirstConfig);
        // 1小时 —— 6小时 每30分钟一次
        AddTaskScheduleArg.Config statisticSecondConfig = new AddTaskScheduleArg.Config();
        statisticSecondConfig.setStartOffsetSeconds(60 * 60);
        statisticSecondConfig.setEndOffsetSeconds(60 * 60 * 6);
        statisticSecondConfig.setPriority(2);
        statisticSecondConfig.setIntervalSeconds(60 * 30);
        statisticConfigList.add(statisticSecondConfig);
        // 6小时 —— 48小时 每1小时一次
        AddTaskScheduleArg.Config statisticThirdConfig = new AddTaskScheduleArg.Config();
        statisticThirdConfig.setStartOffsetSeconds(60 * 60 * 6);
        statisticThirdConfig.setEndOffsetSeconds(60 * 60 * 48);
        statisticThirdConfig.setPriority(3);
        statisticThirdConfig.setIntervalSeconds(60 * 60);
        statisticConfigList.add(statisticThirdConfig);
        // 48小时 —— 31天 每5小时一次
        AddTaskScheduleArg.Config statisticFourConfig = new AddTaskScheduleArg.Config();
        statisticFourConfig.setStartOffsetSeconds(60 * 60 * 48);
        statisticFourConfig.setEndOffsetSeconds(60 * 60 * 24 * 31);
        statisticFourConfig.setPriority(4);
        statisticFourConfig.setIntervalSeconds(60 * 60);
        statisticConfigList.add(statisticFourConfig);

        mailTaskStatisticScheduleArg.setConfigList(statisticConfigList);
        taskScheduleManager.addTaskSchedule(mailTaskStatisticScheduleArg);
    }

    // 去重地址、并返回原始的地址列表
    private List<String> distinctAndReturnOriginAddressList(List<String> addressList) {
        if (CollectionUtils.isEmpty(addressList)) {
            return addressList;
        }
        List<String> resultList = Lists.newArrayList();
        Set<String> addressSet = Sets.newHashSet();
        for (String address : addressList) {
            if (addressSet.contains(address.toLowerCase())) {
                continue;
            }
            addressSet.add(address.toLowerCase());
            resultList.add(address);
        }
        return resultList;
    }

    /**
     * 通过地址列表发送邮件
     *
     * @param taskEntity
     * @param mailAccountEntity * @param url
     * @return
     */
    public void sendMailByAddressList(MailSendTaskEntity taskEntity, MailAccountEntity mailAccountEntity, String url,
                                      List<String> addressList, Integer labelId, List<String> sendAddressList) {
        Optional<RequestBody> requestBodyOpt = buildEmailSendRequestBody(taskEntity, mailAccountEntity.getApiUser(),
                mailAccountEntity.getApiKey(), addressList, labelId, null, MailSendTypeEnum.SEND_BY_ADDRESS_LIST.getType());
        if (!requestBodyOpt.isPresent()) {
            log.info("MailManager.sendEmailByAddressList failed to is null ea:{} taskId:{}", taskEntity.getEa(), taskEntity.getId());
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            return;
        }
        MailBaseResp<SendMailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBodyOpt.get(), url, new TypeToken<MailBaseResp<SendMailResp>>() {
        });
        if (sendResult == null || !sendResult.isSuccess()) {
            log.error("MailManager.sendEmailByAddressList failed taskEntityId:{}", taskEntity.getId());
            updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
            if (sendResult != null && sendResult.getStatusCode() != null) {
                mailSendTaskDAO.updateStatusAndCodeById(MailSendStatusEnum.SEND_FAILED.getStatus(), sendResult.getStatusCode(), taskEntity.getId());
            } else {
                mailSendTaskDAO.updateStatusById(MailSendStatusEnum.SEND_FAILED.getStatus(), taskEntity.getId());
            }
        } else {
            if (sendResult.getInfo() == null || sendResult.getInfo() == null || CollectionUtils.isEmpty(sendResult.getInfo().getMaillistTaskId())) {
                log.error("EmailManager.sendEmailByAddressList return maillistTaskId null taskEntityId:{}", taskEntity.getId());
                updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FAIL);
                mailSendTaskDAO.updateStatusAndCodeById(MailSendStatusEnum.SEND_FAILED.getStatus(), sendResult.getStatusCode(), taskEntity.getId());
            } else {
                updateMailMarketingStatus(taskEntity.getEa(), taskEntity.getId(), SendStatusEnum.FINISHED);
                mailSendTaskDAO.updateStatusAndCodeById(MailSendStatusEnum.SEND_SUCCESS.getStatus(), sendResult.getStatusCode(), taskEntity.getId());
                addEmailTaskSchedule(taskEntity.getEa(), taskEntity.getId());
                MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(taskEntity.getEa());
                entity.setTaskId(taskEntity.getId());
                entity.setMaillistTaskId(sendResult.getInfo().getMaillistTaskId().get(0));
                entity.setType(MailSendTypeEnum.SEND_BY_ADDRESS_LIST.getType());
                Date now = new Date();
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                mailSendTaskResultDAO.insert(entity);
                // 发送成功后更新labelId
                if (labelId != null) {
                    mailSendTaskDAO.setLabelIdById(taskEntity.getId(), labelId);
                }
                // 之后更新已发送邮件
                enterpriseSpreadRecordManager.upsertList(sendAddressList, MarketingActivityActionEnum.MAIL_SERVICE.getSpreadType(), taskEntity.getEa());
            }
        }
    }

    private Map<String, String> buildSendMailHeaderMap(MailSendTaskEntity taskEntity) {
        Map<String, String> headerMap = Maps.newHashMap();
        if (taskEntity == null || StringUtils.isBlank(taskEntity.getEa()) || StringUtils.isBlank(taskEntity.getId())) {
            return headerMap;
        }
        headerMap.put(MailConstant.EA_HEADERS_KEY, taskEntity.getEa());
        headerMap.put(MailConstant.TASK_ID_HEADERS_KEY, taskEntity.getId());
        return headerMap;
    }

    /**
     * 创建邮件模板
     *
     * @param vo
     * @return
     */
    public Result<String> addMailTemplate(AddTemplateVO vo) {
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(vo.getEa(), MailApiUserTypeEnum.BATCH.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.addMailTemplate failed mailAccountEntity not exist ea:{}name:{}", vo.getEa(), vo.getTemplateName());
            return Result.newError(SHErrorCode.ENTERPRSE_NOT_BIND_MAIL_ACCOUNT);
        }

        int templateCount = mailTemplateDAO.getTotalTemplateCount(vo.getEa());
        if (templateCount >= MailConstant.MAIL_TEMPLATE_MAX_SIZE) {
            log.warn("MailManager.addMailTemplate failed template count over limit ea:{} maxCount:{}", vo.getEa(), templateCount);
            return Result.newError(SHErrorCode.MAIL_TEMPLATE_COUNT_MAX_LIMIT);
        }

        String url = getSendCloudApiHostUrl() + "template/add";
        String id = UUIDUtil.getUUID();
        RequestBody requestBody = new FormBody.Builder()
                .add("apiUser", mailAccountEntity.getApiUser())
                .add("apiKey", mailAccountEntity.getApiKey())
                .add("invokeName", id)
                .add("name", vo.getTemplateName())
                .add("subject", vo.getTemplateName())
                .add("html", vo.getContent())
                .add("templateType", String.valueOf(MailTemplateEnum.BATCH.getType()))
                .build();
        MailBaseResp<AddMailTemplateResp> sendResult = httpManager.executePostHttpByRequestBody(requestBody, url, new TypeToken<MailBaseResp<AddMailTemplateResp>>() {
        });
        if (!sendResult.isSuccess() || sendResult.getInfo() == null) {
            log.info("MailManager.addMailTemplate failed errocode:{} errorMsg:{}", sendResult.getStatusCode(), sendResult.getMessage());
            return Result.newError(SHErrorCode.CREATE_MAIL_TEMPLATE_FAILED);
        }

        MailTemplateEntity templateEntity = new MailTemplateEntity();
        templateEntity.setId(id);
        templateEntity.setName(vo.getTemplateName());
        templateEntity.setEa(vo.getEa());
        templateEntity.setType(MailTemplateEnum.BATCH.getType());
        templateEntity.setFsUserId(vo.getFsUserId());
        templateEntity.setStatus(MailTempalateStatusEnum.NORMAL.getStatus());
        Date now = new Date();
        templateEntity.setCreateTime(now);
        templateEntity.setUpdateTime(now);
        mailTemplateDAO.insert(templateEntity);

        return Result.newSuccess();
    }

    /**
     * 批量查询
     *
     * @param vo
     * @return
     */
    public Result<PageResult<PageQueryMailTemplateResult>> pageQueryMailTemplate(PageQueryTemplateVO vo) {
        PageResult<PageQueryMailTemplateResult> pageResult = new PageResult();
        List<PageQueryMailTemplateResult> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);

        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<MailTemplateEntity> templateResult = mailTemplateDAO.pageList(vo.getEa(), vo.getKeyword(), page);
        if (CollectionUtils.isNotEmpty(templateResult)) {
            for (MailTemplateEntity mailTemplateEntity : templateResult) {
                PageQueryMailTemplateResult template = new PageQueryMailTemplateResult();
                template.setId(mailTemplateEntity.getId());
                template.setFsUserId(mailTemplateEntity.getFsUserId());
                template.setName(mailTemplateEntity.getName());
                template.setTemplateType(mailTemplateEntity.getType());
                template.setCreateTime(mailTemplateEntity.getCreateTime().getTime());
                template.setUpdateTime(mailTemplateEntity.getUpdateTime().getTime());
                resultList.add(template);
            }

            pageResult.setTotalCount(page.getTotalNum());
        }
        return Result.newSuccess(pageResult);
    }

    public Result<PageResult<PageQueryMailTemplateResult>> pageCommonMailTemplate(PageQueryTemplateVO vo) {
        PageResult<PageQueryMailTemplateResult> pageResult = new PageResult();
        List<PageQueryMailTemplateResult> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);

        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<MailCommonTemplateEntity> templateResult = mailTemplateDAO.pageCommonTemplate(vo.getKeyword(), page);
        if (CollectionUtils.isEmpty(templateResult)) {
            return Result.newSuccess(pageResult);
        }
        for (MailCommonTemplateEntity mailCommonTemplateEntity : templateResult) {
            PageQueryMailTemplateResult template = new PageQueryMailTemplateResult();
            template.setId(mailCommonTemplateEntity.getId());
            template.setName(mailCommonTemplateEntity.getName());
            template.setTemplateType(mailCommonTemplateEntity.getType());
            template.setCreateTime(mailCommonTemplateEntity.getCreateTime().getTime());
            resultList.add(template);
        }
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    /**
     * 查询邮件模板详情
     *
     * @param vo
     * @return
     */
    public Result<QueryMailTemplateDetailResult> queryTemplateDetail(QueryTemplateDetailVO vo) {
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(vo.getEa(), MailApiUserTypeEnum.BATCH.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.queryTemplateDetail failed mailAccountEntity not exist ea:{} pageNum:{} templateId:{}", vo.getEa(), vo.getId());
            return Result.newError(SHErrorCode.ENTERPRSE_NOT_BIND_MAIL_ACCOUNT);
        }

        RequestBody requestBody = new FormBody.Builder()
                .add("apiUser", mailAccountEntity.getApiUser())
                .add("apiKey", mailAccountEntity.getApiKey())
                .add("invokeName", vo.getId())
                .build();

        String url = getSendCloudApiHostUrl() + "template/get";
        MailBaseResp<QueryMailTemplateDetailResp> sendResult = httpManager.executePostHttpByRequestBody(requestBody, url, new TypeToken<MailBaseResp<QueryMailTemplateDetailResp>>() {
        });
        if (!sendResult.isSuccess() || sendResult.getInfo() == null || sendResult.getInfo().getData() == null) {
            log.info("MailManager.queryTemplateDetail failed errocode:{} errorMsg:{}", sendResult.getStatusCode(), sendResult.getMessage());
            return Result.newError(SHErrorCode.QUERY_MAIL_TEMPLATE_DETAIL_FAILED);
        }

        QueryMailTemplateDetailResp.TemplateData templateData = sendResult.getInfo().getData();
        QueryMailTemplateDetailResult detailResult = new QueryMailTemplateDetailResult();
        if (templateData != null) {
            detailResult.setName(templateData.getName());
            detailResult.setId(templateData.getInvokeName());
            detailResult.setContentSummary(templateData.getContentSummary());
            detailResult.setHtml(templateData.getHtml());
            detailResult.setCreateTime(templateData.getGmtCreated());
            detailResult.setUpdateTime(templateData.getGmtModified());
            detailResult.setSubject(templateData.getSubject());
        }

        MailTemplateEntity entity = mailTemplateDAO.getDetailById(vo.getId());
        if (entity != null) {
            detailResult.setFsUserId(entity.getFsUserId());
        }
        return Result.newSuccess(detailResult);
    }

    public Result<QueryMailTemplateDetailResult> queryCommonTemplateDetail(QueryTemplateDetailVO vo) {
        MailCommonTemplateEntity mailCommonTemplateEntity = mailTemplateDAO.getCommonDetailById(vo.getId());
        if (mailCommonTemplateEntity == null) {
            log.warn("MailManager.queryCommonTemplateDetail mailCommonTemplateEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        QueryMailTemplateDetailResult detailResult = new QueryMailTemplateDetailResult();
        detailResult.setName(mailCommonTemplateEntity.getName());
        detailResult.setHtml(mailCommonTemplateEntity.getContent());
        detailResult.setSubject(mailCommonTemplateEntity.getName());
        return Result.newSuccess(detailResult);
    }

    /**
     * 删除邮件模板
     *
     * @param vo
     * @return
     */
    public Result<Void> deleteTemplate(DeleteMailTemplateVO vo) {
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(vo.getEa(), MailApiUserTypeEnum.BATCH.getType());
        if (mailAccountEntity == null) {
            log.warn("MailManager.deleteTemplate failed mailAccountEntity not exist ea:{} pageNum:{} templateId:{}", vo.getEa(), vo.getId());
            return Result.newError(SHErrorCode.ENTERPRSE_NOT_BIND_MAIL_ACCOUNT);
        }

        String url = getSendCloudApiHostUrl() + "template/delete";
        RequestBody requestBody = new FormBody.Builder()
                .add("apiUser", mailAccountEntity.getApiUser())
                .add("apiKey", mailAccountEntity.getApiKey())
                .add("invokeName", vo.getId())
                .build();
        MailBaseResp<DeleteMailTemplateResp> resp = httpManager.executePostHttpByRequestBody(requestBody, url, new TypeToken<MailBaseResp<DeleteMailTemplateResp>>() {
        });
        if (!resp.isSuccess() || resp.getInfo() == null) {
            log.info("MailManager.deleteTemplate failed errocode:{} errorMsg:{}", resp.getStatusCode(), resp.getMessage());
            return Result.newError(SHErrorCode.DELETE_MAIL_TEMPLATE_FAILED);
        }

        mailTemplateDAO.deleteTemplateById(vo.getId());
        return Result.newSuccess();
    }

    /**
     * 更新邮件模板
     *
     * @param vo
     * @return
     */
    public UpdateMailTemplateResp updateTemplate(UpdateMailTemplateVO vo) {
        MailAccountEntity mailAccountEntity = getUserAccount(vo.getEa(), MailApiUserTypeEnum.BATCH.getType());
        if (mailAccountEntity == null) {
            return null;
        }
        vo.setApiKey(mailAccountEntity.getApiKey());
        vo.setApiUser(mailAccountEntity.getApiUser());
        FormBody.Builder formBody = new FormBody.Builder().add("apiUser", vo.getApiUser()).add("apiKey", vo.getApiKey()).add("invokeName", vo.getInvokeName()).add("name", vo.getName()).add("html", vo.getHtml());
        RequestBody requestBody = formBody.build();
        String url = getSendCloudApiHostUrl() + "template/update";
        MailBaseResp<UpdateMailTemplateResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<UpdateMailTemplateResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        log.warn("MailManager.queryEmailStatus error vo:{}, resp:{}", vo, resp);
        return null;
    }

    /**
     * ======================
     * Api_User接口
     * ======================
     */

    public MailAccountEntity getUserAccount(String ea, Integer type) {
        if (StringUtils.isBlank(ea)) {
            return null;
        }
        if (type == null) {
            type = MailApiUserTypeEnum.DEFAULT.getType();
        }
        return mailAccountDAO.getAccountByEaAndType(ea, type);
    }


    private void setApiUserInfo(BaseMailVO vo) {
        String apiUser = vo.getApiUser();
        String apiKey = vo.getApiKey();
        if (StringUtils.isBlank(apiUser) || StringUtils.isBlank(apiKey)) {
            MailAccountEntity mailAccountEntity = getUserAccount(vo.getEa(), null);
            if (mailAccountEntity == null) {
                return;
            }
            apiUser = mailAccountEntity.getApiUser();
            apiKey = mailAccountEntity.getApiKey();
        }
        vo.setApiUser(apiUser);
        vo.setApiKey(apiKey);
    }

    /**
     * 获取apiUser列表
     *
     * @return
     */
    public QueryApiUserListResp queryApiUserList(BaseMailVO vo) {
        setApiUserInfo(vo);
        QueryApiUserListReq queryApiUserListReq = new QueryApiUserListReq();
        Map<String, String> param = Maps.newHashMap();
        param.put("apiUser", vo.getApiUser());
        param.put("apiKey", vo.getApiKey());
        if (vo.getEmailType() != null) {
            param.put("emailType", vo.getEmailType() + "");
        }
        if (vo.getCType() != null) {
            param.put("cType", vo.getCType() + "");
        }
        param.put("domainName", vo.getDomain());
        MailBaseResp<QueryApiUserListResp> resp = httpManager
                .executePostHttp(queryApiUserListReq, getSendCloudApiHostUrl() + "apiuser/list?" + httpManager.transformUrlParams(param), new TypeToken<MailBaseResp<QueryApiUserListResp>>() {
                });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        log.warn("MailManager.queryApiUserList error vo:{}, resp:{}", vo, resp);
        return null;
    }

    /**
     * 创建apiUser
     */
    public AddApiUserResult addApiUser(AddApiUserVO vo) {
        setApiUserInfo(vo);
        AddApiUserReq addApiUserReq = new AddApiUserReq();
        Map<String, String> param = Maps.newHashMap();
        param.put("apiUser", vo.getApiUser());
        param.put("apiKey", vo.getApiKey());
        param.put("name", vo.getName());
        param.put("emailType", vo.getEmailType() + "");
        param.put("domainName", vo.getDomain());
        param.put("open", vo.getOpen() + "");
        param.put("click", vo.getClick() + "");
        param.put("unsubscribe", vo.getUnsubscribe() + "");
        MailBaseResp<AddApiUserResp> resp = httpManager
                .executePostHttp(addApiUserReq, getSendCloudApiHostUrl() + "apiuser/add?" + httpManager.transformUrlParams(param), new TypeToken<MailBaseResp<AddApiUserResp>>() {
                });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null && resp.getInfo().getData() != null) {
            return resp.getInfo().getData();
        }
        log.warn("EmailManager.addApiUser error vo:{}, result:{}", resp);
        return null;
    }


    /**
     * ======================
     * 域名接口
     * ======================
     */

    /**
     * 新增发信域名
     *
     * @param vo
     * @return
     */
    public AddMailDomainResp addMailDomain(BaseMailVO vo) {
        setApiUserInfo(vo);
        AddMailDomainReq addMailDomainReq = new AddMailDomainReq();
        Map<String, String> param = Maps.newHashMap();
        param.put("apiUser", vo.getApiUser());
        param.put("apiKey", vo.getApiKey());
        param.put("name", vo.getDomain());
        MailBaseResp<AddMailDomainResp> resp = httpManager.executePostHttp(addMailDomainReq, getSendCloudApiHostUrl() + "domain/add?" + httpManager.transformUrlParams(param), new TypeToken<MailBaseResp<AddMailDomainResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        log.warn("EmailManager.addMailDomain error vo:{}, resp:{}", vo, resp);
        return null;
    }

    /**
     * 校验域名
     *
     * @param vo
     * @return
     */
    public List<CheckMailResult> checkMailDomain(BaseMailVO vo) {
        setApiUserInfo(vo);
        CheckMailDomainReq checkMailDomainReq = new CheckMailDomainReq();
        Map<String, String> param = Maps.newHashMap();
        param.put("apiUser", vo.getApiUser());
        param.put("apiKey", vo.getApiKey());
        param.put("name", vo.getDomain());
        MailBaseResp<CheckMailDomainResp> resp = httpManager.executePostHttp(checkMailDomainReq, getSendCloudApiHostUrl() + "domain/checkConfig?" + httpManager.transformUrlParams(param), new TypeToken<MailBaseResp<CheckMailDomainResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null && CollectionUtils.isNotEmpty(resp.getInfo().getDataList())) {
            return resp.getInfo().getDataList();
        }
        log.warn("EmailManager.checkMailDomain error vo:{}, resp:{}", vo, resp);
        return Lists.newArrayList();
    }

    /**
     * 更新发送域名
     *
     * @param vo
     * @return
     */
    public UpdateMainDomainResp updateMainDomain(UpdateMainDomainVO vo) {
        setApiUserInfo(vo);
        RequestBody requestBody = new FormBody.Builder().add("apiUser", vo.getApiUser()).add("apiKey", vo.getApiKey()).add("name", vo.getOldDomain()).add("newName", vo.getNewDomain()).build();
        String url = getSendCloudApiHostUrl() + "domain/update";
        MailBaseResp<UpdateMainDomainResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<UpdateMainDomainResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        log.warn("EmailManager.updateMainDomain error vo:{}, resp:{}", vo, resp);
        return null;
    }


    /**
     * 获取域名列表
     *
     * @param vo
     * @return
     */
    public List<DomainInfo> queryDomainList(QueryDomainListVO vo) {
        setApiUserInfo(vo);
        QueryDomainListReq queryDomainListReq = new QueryDomainListReq();
        Map<String, String> param = Maps.newHashMap();
        param.put("apiUser", vo.getApiUser());
        param.put("apiKey", vo.getApiKey());
        param.put("name", vo.getDomain());
        if (vo.getType() != null) {
            param.put("type", vo.getType() + "");
        }
        param.put("verify", vo.getVerify());
        MailBaseResp<QueryDomainListResp> resp = httpManager.executePostHttp(queryDomainListReq, getSendCloudApiHostUrl() + "domain/list?" + httpManager.transformUrlParams(param), new TypeToken<MailBaseResp<QueryDomainListResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null && CollectionUtils.isNotEmpty(resp.getInfo().getDataList())) {
            return resp.getInfo().getDataList();
        }
        return Lists.newArrayList();
    }


    /**
     * ======================
     * 投递回应
     * ======================
     */
    @FilterLog
    public QueryEmailStatusResp queryEmailStatus(QueryEmailStatusVO vo) {
        setApiUserInfo(vo);
        RequestBody requestBody;
        //RequestBody requestBody = new FormBody.Builder().add("apiUser", vo.getApiUser()).add("apiKey", vo.getApiKey()).add("labelId", vo.getLabelId() + "").add("startDate", vo.getStartDate()).add("endDate", vo.getEndDate()).add("start", vo.getStart() + "").add("limit", vo.getLimit() + "").add("status", vo.getStatus() + "").add("subStatus", vo.getSubStatus() + "").build();
        FormBody.Builder formBody = new FormBody.Builder().add("apiUser", vo.getApiUser()).add("apiKey", vo.getApiKey()).add("startDate", vo.getStartDate()).add("endDate", vo.getEndDate()).add("start", vo.getStart() + "").add("limit", vo.getLimit() + "");
        if (StringUtils.isNotBlank(vo.getStatus())) {
            formBody.add("status", vo.getStatus());
        }
        if (StringUtils.isNotBlank(vo.getSubStatus())) {
            formBody.add("subStatus", vo.getSubStatus());
        }
        if (StringUtils.isNotBlank(vo.getEmail())) {
            formBody.add("email", vo.getEmail());
        }
        if (vo.getLabelId() != null) {
            formBody.add("labelId", String.valueOf(vo.getLabelId()));
        }
        if (StringUtils.isNotBlank(vo.getEmailIds())) {
            formBody.add("emailIds", vo.getEmailIds());
        }
        requestBody = formBody.build();
        //RequestBody requestBody = httpManager.buildFormBodyByMap(vo, vo.getClass());
        String url = getSendCloudApiHostUrl() + "data/emailStatus";
        MailBaseResp<QueryEmailStatusResp> resp = httpManager.executeSendCloudRequest(() -> httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<QueryEmailStatusResp>>() {}));
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        log.warn("MailManager.queryEmailStatus error vo:{}, resp:{}", vo, resp);
        return null;
    }

    /**
     * 查询点击人数与打开人数
     *
     * @param ea        企业ea
     * @param labelId   标签id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    public MailUserStatisticBO queryOpenAndClickUserNumByLabelId(String ea, String labelId, String startDate, String endDate) {
        MailUserStatisticBO result = new MailUserStatisticBO();
        result.setClickUserNum(0L);
        result.setOpenUserNum(0L);
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(labelId)) {
            return result;
        }
        QueryOpenAndClickListVO queryOpenAndClickListVO = new QueryOpenAndClickListVO();
        queryOpenAndClickListVO.setEa(ea);
        queryOpenAndClickListVO.setStartDate(startDate);
        queryOpenAndClickListVO.setEndDate(endDate);
        queryOpenAndClickListVO.setLabelId(labelId);
        queryOpenAndClickListVO.setStart(0);
        int pageSize =  MailConstant.OPEN_AND_CLICK_LIMIT;
        queryOpenAndClickListVO.setLimit(pageSize);
        MailBaseResp<QueryOpenAndClickListResp> resp = queryOpenAndClickPage(queryOpenAndClickListVO);
        if (resp == null || !resp.isSuccess() || resp.getInfo() == null || CollectionUtils.isEmpty(resp.getInfo().getDataList())) {
            return result;
        }
        Set<String> clickMailSet = Sets.newHashSet();
        Set<String> openMailSet = Sets.newHashSet();
        for (OpenAndClickInfo openAndClickInfo : resp.getInfo().getDataList()) {
            if (openAndClickInfo.getTrackType().equals(MailOpenAndClickTrackTypeEnum.CLICK.getType())) {
                clickMailSet.add(openAndClickInfo.getEmail());
            } else if (openAndClickInfo.getTrackType().equals(MailOpenAndClickTrackTypeEnum.OPEN.getType())) {
                openMailSet.add(openAndClickInfo.getEmail());
            }
        }

        if (resp.getInfo().getTotal() > pageSize) {
            int page = (resp.getInfo().getTotal() + pageSize - 1) / pageSize;
            for (int i = 2; i <= page; i++) {
                queryOpenAndClickListVO.setStart((i - 1) * pageSize);
                resp = queryOpenAndClickPage(queryOpenAndClickListVO);
                if (resp == null || !resp.isSuccess() || resp.getInfo() == null || CollectionUtils.isEmpty(resp.getInfo().getDataList())) {
                    log.info("queryAllOpenAndClickList request is end, arg: {} totalPage: {} currentPage: {}", queryOpenAndClickListVO, page, i);
                    break;
                }
                for (OpenAndClickInfo openAndClickInfo : resp.getInfo().getDataList()) {
                    if (openAndClickInfo.getTrackType().equals(MailOpenAndClickTrackTypeEnum.CLICK.getType())) {
                        clickMailSet.add(openAndClickInfo.getEmail());
                    } else if (openAndClickInfo.getTrackType().equals(MailOpenAndClickTrackTypeEnum.OPEN.getType())) {
                        openMailSet.add(openAndClickInfo.getEmail());
                    }
                }
            }
        }
        result.setClickUserNum((long) clickMailSet.size());
        result.setOpenUserNum((long) openMailSet.size());
        openMailSet.clear();
        clickMailSet.clear();
        return result;
    }


    // 尽量不在调用这个方法，这个可能会查出很多数据
    @FilterLog
    @Deprecated
    public List<OpenAndClickInfo> queryAllOpenAndClickList(QueryOpenAndClickListVO vo) {
        List<OpenAndClickInfo> result = Lists.newArrayList();
        // 先取第一页
        setApiUserInfo(vo);
        vo.setStart(0);
        vo.setLimit(MailConstant.OPEN_AND_CLICK_LIMIT);
        MailBaseResp<QueryOpenAndClickListResp> resp = queryOpenAndClickPage(vo);
        if (resp == null || !resp.isSuccess() || resp.getInfo() == null || CollectionUtils.isEmpty(resp.getInfo().getDataList())) {
            log.warn("queryAllOpenAndClickList result is empty, arg: {} resp: {}", vo, resp);
            return Lists.newArrayList();
        }
        log.info("queryAllOpenAndClickList request is success, arg: {} total: {}", vo, resp.getInfo().getTotal());
        result.addAll(resp.getInfo().getDataList());
        if (resp.getInfo().getTotal() > MailConstant.OPEN_AND_CLICK_LIMIT) {
            int page = (resp.getInfo().getTotal() + MailConstant.OPEN_AND_CLICK_LIMIT - 1) / MailConstant.OPEN_AND_CLICK_LIMIT;
            for (int i = 2; i <= page; i++) {
                vo.setStart((i - 1) * MailConstant.OPEN_AND_CLICK_LIMIT);
                resp = queryOpenAndClickPage(vo);
                if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
                    result.addAll(resp.getInfo().getDataList());
                    log.info("queryAllOpenAndClickList request is success, arg: {} totalPage: {} currentPage: {}", vo, page, i);
                } else {
                    log.info("queryAllOpenAndClickList result is empty, arg: {} totalPage: {} currentPage: {}", vo, page, i);
                    return result;
                }
            }
        }
        return result;
    }

    public MailBaseResp<QueryOpenAndClickListResp> queryOpenAndClickPage(QueryOpenAndClickListVO vo) {
        if (vo.getStart() == null || vo.getLimit() == null) {
            MailBaseResp<QueryOpenAndClickListResp> resp = new MailBaseResp<>();
            resp.setStatusCode(-1);
            resp.setMessage("param error, start or limit is null");
            resp.setResult(false);
            return resp;
        }
        // 先取第一页
        setApiUserInfo(vo);
        FormBody.Builder formBody = new FormBody.Builder().add("apiUser", vo.getApiUser()).add("apiKey", vo.getApiKey()).add("start", vo.getStart() + "").add("limit", vo.getLimit() + "");
        if (StringUtils.isNotBlank(vo.getEmail())) {
            formBody.add("email", vo.getEmail());
        }
        if (StringUtils.isNotBlank(vo.getTrackType())) {
            formBody.add("trackType", vo.getTrackType());
        }
        if (StringUtils.isNotBlank(vo.getStartDate())) {
            formBody.add("startDate", vo.getStartDate());
        }
        if (StringUtils.isNotBlank(vo.getEndDate())) {
            formBody.add("endDate", vo.getEndDate());
        }
        if (vo.getDays() != null) {
            formBody.add("days", String.valueOf(vo.getDays()));
        }
        if (StringUtils.isNotBlank(vo.getLabelId())) {
            formBody.add("labelId", vo.getLabelId());
        }
        RequestBody firstRequestBody = formBody.build();
        String url = getSendCloudApiHostUrl() + "openandclick/list";
        return httpManager.executeSendCloudRequest(() -> httpManager.executePostHttpWithRequestBody(firstRequestBody, url, new TypeToken<MailBaseResp<QueryOpenAndClickListResp>>() {
        }));
    }

    /**
     * ======================
     * 数据统计
     * ======================
     */
    @FilterLog
    public StatDayStatisticsResp statDayStatisticsFromSendCloud(StatDayStatisticsVO vo) {
        /*MailAccountEntity mailAccountEntity = getUserAccount(vo.getEa(), null);
        if (mailAccountEntity == null) {
            return null;
        }
        vo.setApiUser(mailAccountEntity.getApiUser());
        vo.setApiKey(mailAccountEntity.getApiKey());
        RequestBody requestBody = httpManager.buildFormBodyByMap(vo, vo.getClass());*/
        setApiUserInfo(vo);
        FormBody.Builder formBody = new FormBody.Builder().add("apiUser", vo.getApiUser()).add("apiKey", vo.getApiKey())
                .add("startDate", vo.getStartDate()).add("endDate", vo.getEndDate());
        if (StringUtils.isNotBlank(vo.getLabelIdList())) {
            formBody.add("labelIdList", vo.getLabelIdList());
        }
        if (vo.getAggregate() != null) {
            formBody.add("aggregate", vo.getAggregate().toString());
        }
        RequestBody requestBody = formBody.build();
        String url = getSendCloudApiHostUrl() + "statday/list";
        MailBaseResp<StatDayStatisticsResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<StatDayStatisticsResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            if (resp.getInfo().getDataList() != null) {
                String json = JsonUtils.toJson(resp.getInfo().getDataList());
                if (vo.getAggregate() != null && vo.getAggregate() == 1) {
                    StatDayStatisticsResp.StatDayDetail statDayDetail = JsonUtil.fromJson(json, StatDayStatisticsResp.StatDayDetail.class);
                    resp.getInfo().setSingleStatistics(statDayDetail);
                } else {
                    List<StatDayStatisticsResp.StatDayDetail> statDayDetailList = JsonUtil.fromJson(json, new TypeToken<List<StatDayStatisticsResp.StatDayDetail>>() {
                    }.getType());
                    resp.getInfo().setMultiStatistics(statDayDetailList);
                }
            }
            return resp.getInfo();
        }
        log.warn("MailManager.statdayStatistics error vo:{}, resp:{}", vo, resp);
        return null;
    }


    public MailStatisticsResp statDayStatisticsToJob(StatDayStatisticsVO vo) {
        MailStatisticsResp result = new MailStatisticsResp();
        if (vo.getStartTime() == null || vo.getEndTime() == null || StringUtils.isBlank(vo.getLabelIdList())) {
            return null;
        }
        String startTime;
        String endTime;
        startTime = DateUtil.format2(new Date(vo.getStartTime()));
        endTime = DateUtil.format2(new Date(vo.getEndTime()));
        vo.setStartDate(startTime);
        vo.setEndDate(endTime);
        StatDayStatisticsResp statDayStatisticsResp = statDayStatisticsFromSendCloud(vo);
        if (statDayStatisticsResp == null || statDayStatisticsResp.getSingleStatistics() == null) {
            return null;
        }
        StatDayStatisticsResp.StatDayDetail statDayDetail = statDayStatisticsResp.getSingleStatistics();
        long clickNum = statDayDetail.getClickNum() == null ? 0L : statDayDetail.getClickNum();
        long deliveredNum = statDayDetail.getDeliveredNum() == null ? 0L : statDayDetail.getDeliveredNum();
        long openNum = statDayDetail.getOpenNum() == null ? 0L : statDayDetail.getOpenNum();
        // 统计打开人数与点击人数  这里循环去查即可，最多查过去三十天发送的邮件，且这里的数据仅邮件漏斗用到，慢慢统计即可
        List<String> labelIdList = Lists.newArrayList(vo.getLabelIdList().split(";"));
        long clickUserNum = 0L;
        long openUserNum = 0L;
        for (String labelId : labelIdList) {
            MailUserStatisticBO mailUserStatisticBO = queryOpenAndClickUserNumByLabelId(vo.getEa(), labelId, startTime, endTime);
            openUserNum = openUserNum + mailUserStatisticBO.getOpenUserNum();
            clickUserNum = clickUserNum + mailUserStatisticBO.getClickUserNum();
        }
        result.setClickNum(clickNum);
        result.setDeliveredNum(deliveredNum);
        result.setOpenNum(openNum);
        result.setOpenUserNum(openUserNum);
        result.setClickUserNum(clickUserNum);
        return result;
    }


    private Optional<String> addAddressList(String addressId, String apiUser, String apiKey, String ea) {
        String address = addressId + "@maillist.sendcloud.org";
        String url = getSendCloudApiHostUrl() + "addresslist/add?apiUser=" + apiUser + "&apiKey=" + apiKey + "&address=" + address + "&name=" + addressId;
        MailBaseResp<AddAddressListResp> resp = httpManager.executeGetHttp(url, new TypeToken<MailBaseResp<AddAddressListResp>>() {
        });
        if (resp == null || !resp.isSuccess() || resp.getInfo() == null) {
            log.info("MailManager.addAdressList failed  apiUser:{} apiKey:{} errorMsg:{}", apiUser, apiKey, resp);
            return Optional.empty();
        }

        MailAddressListEntity entity = new MailAddressListEntity();
        entity.setId(addressId);
        entity.setEa(ea);
        entity.setAddress(resp.getInfo().getData().getAddress());
        entity.setName(resp.getInfo().getData().getName());
        entity.setDescription(resp.getInfo().getData().getDescription());
        entity.setMemberCount(resp.getInfo().getData().getMemberCount());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        if (mailAddressListDAO.insert(entity) < 0) {
            log.info("MailManager.addAdressList insert entity failed apiUser:{} apiKey:{} entity:{}", apiUser, apiKey, entity);
            return Optional.empty();
        }

        return Optional.ofNullable(resp.getInfo().getData().getAddress());
    }

    private int addAddressMember(String apiUser, String apiKey, String addressName, List<String> addressList) {
        if (CollectionUtils.isEmpty(addressList)) {
            return 0;
        }

        String url = getSendCloudApiHostUrl() + "addressmember/add";
        int totalAdd = 0;
        int invalidAddressCount = 0;
        if (addressList.size() > MailConstant.ADD_ADDRESS_PER_TIME_MAX_SIZE) {
            PageUtil<String> addressListPageUtil = new PageUtil<>(addressList, MailConstant.ADD_ADDRESS_PER_TIME_MAX_SIZE);
            int pageCount = addressListPageUtil.getPageCount();
            for (int i = 1; i <= pageCount; i++) {
                List<String> addressListPage = addressListPageUtil.getPagedList(i);
                String members = StringUtils.join(addressListPage, ";");
                RequestBody requestBody = new FormBody.Builder().add("apiUser", apiUser).add("apiKey", apiKey).add("address", addressName).add("members", members).build();
                MailBaseResp<AddAddressListMemberResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<AddAddressListMemberResp>>() {
                });
                if (resp == null || !resp.isSuccess() || resp.getInfo() == null) {
                    log.info("MailManager.addAddressMember failed  apiUser:{} apiKey:{} errorMsg:{}", apiUser, apiKey, resp);
                } else {
                    totalAdd += resp.getInfo().getCount();
                    if (CollectionUtils.isNotEmpty(resp.getInfo().getInvalidAddressList())) {
                        invalidAddressCount += resp.getInfo().getInvalidAddressList().size();
                    }
                }
            }
        } else {
            String members = StringUtils.join(addressList, ";");
            RequestBody requestBody = new FormBody.Builder().add("apiUser", apiUser).add("apiKey", apiKey).add("address", addressName).add("members", members).build();
            MailBaseResp<AddAddressListMemberResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<AddAddressListMemberResp>>() {
            });
            if (resp == null || !resp.isSuccess() || resp.getInfo() == null) {
                log.info("MailManager.addAddressMember failed  apiUser:{} apiKey:{} errorMsg:{}", apiUser, apiKey, resp);
            } else {
                totalAdd += resp.getInfo().getCount();
                if (CollectionUtils.isNotEmpty(resp.getInfo().getInvalidAddressList())) {
                    invalidAddressCount += resp.getInfo().getInvalidAddressList().size();
                }
            }
        }

        log.info("MailManager.addAddressMember address size:{} valid size:{} invalid size:{}", addressList.size(), totalAdd, invalidAddressCount);
        return totalAdd;
    }

    /**
     * 查询账户详情
     *
     * @param apiUser
     * @param apiKey
     */
    public Result<QueryAccountInfo> queryAccoutInfo(String apiUser, String apiKey) {
        String url = getSendCloudApiHostUrl() + "userinfo/get";
        RequestBody requestBody = new FormBody.Builder().add("apiUser", apiUser).add("apiKey", apiKey).build();

        QueryAccountInfo info = new QueryAccountInfo();
        if (appVersionManager.isInternationalProd()) {
            return getInternationalAccountInfo(apiUser, apiKey, url);
        } else {
            return getInternalAccountInfo(apiUser, apiKey, url);
        }
    }

    private Result<QueryAccountInfo> getInternalAccountInfo(String apiUser, String apiKey, String url) {
        RequestBody requestBody = new FormBody.Builder().add("apiUser", apiUser).add("apiKey", apiKey).build();
        MailBaseResp<AccountInfoResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<AccountInfoResp>>() {
        });
        if (resp == null || !resp.isSuccess() || resp.getInfo() == null) {
            log.info("MailManager.getInternalAccountInfo failed  apiUser:{} apiKey:{}  errorMsg:{}", apiUser, apiKey, resp);
            return Result.newError(SHErrorCode.MAIL_ACCOUNT_ERROR);
        }
        if (resp.getInfo().getAvaliableBalance() == null) {
            resp.getInfo().setAvaliableBalance(0.0);
        }
        if (resp.getInfo().getReputation() == null) {
            resp.getInfo().setReputation(0.0);
        }
        if (resp.getInfo().getQuota() == null) {
            resp.getInfo().setQuota(0);
        }
        if (resp.getInfo().getTodayUsedQuota() == null) {
            resp.getInfo().setTodayUsedQuota(0);
        }
        if (resp.getInfo().getBalance() == null) {
            resp.getInfo().setBalance(0.0);
        }

        QueryAccountInfo info = new QueryAccountInfo();
        info.setAvaliableBalance(resp.getInfo().getAvaliableBalance());
        int toDayAvailableQuota = resp.getInfo().getQuota() - resp.getInfo().getTodayUsedQuota();
        if (toDayAvailableQuota < 0) {
            toDayAvailableQuota = 0;
        }
        // 若余额小于等于0 额度改为0
        if (resp.getInfo().getAvaliableBalance() <= 0) {
            toDayAvailableQuota = 0;
        }
        info.setThisDayAvaliableCount(toDayAvailableQuota);
        DecimalFormat format = new DecimalFormat("#.00");
        String str = format.format(resp.getInfo().getReputation());
        info.setReputation(str + "%");

        return Result.newSuccess(info);
    }

    private Result<QueryAccountInfo> getInternationalAccountInfo(String apiUser, String apiKey, String url) {
        RequestBody requestBody = new FormBody.Builder().add("apiUser", apiUser).add("apiKey", apiKey).build();
        MailBaseResp<InternationalAccountInfoResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<InternationalAccountInfoResp>>() {
        });
        if (resp == null || !resp.isSuccess() || resp.getInfo() == null) {
            log.info("MailManager.queryAccoutInfo failed  apiUser:{} apiKey:{}  errorMsg:{}", apiUser, apiKey, resp);
            return Result.newError(SHErrorCode.MAIL_ACCOUNT_ERROR);
        }

        QueryAccountInfo info = new QueryAccountInfo();
        info.setAvaliableBalance((double) resp.getInfo().getRestNum());
        int toDayAvailableQuota = resp.getInfo().getQuota() - resp.getInfo().getTodayUsedQuota();
        if (toDayAvailableQuota < 0) {
            toDayAvailableQuota = 0;
        }
        // 若余额小于等于0 额度改为0
        if (resp.getInfo().getRestNum() <= 0) {
            toDayAvailableQuota = 0;
        }
        info.setThisDayAvaliableCount(toDayAvailableQuota);
        DecimalFormat format = new DecimalFormat("#.00");
        String str = format.format(resp.getInfo().getReputation());
        info.setReputation(str + "%");

        return Result.newSuccess(info);
    }

    /**
     * 校验是否可发送邮件
     *
     * @param ea
     * @return
     */
    public boolean canSendMail(String ea) {
        if (specialEaMailAccount.contains(ea)) {
            return true;
        }
        MailAccountEntity mailAccountEntity = getUserAccount(ea, MailApiUserTypeEnum.DEFAULT.getType());
        if (mailAccountEntity == null) {
            return false;
        }
        Result<QueryAccountInfo> queryAccountInfo = queryAccoutInfo(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey());
        return queryAccountInfo.isSuccess() && queryAccountInfo.getData().getAvaliableBalance() > 0;
    }

    /**
     * ======================
     * WebHook
     * ======================
     */
    public WebHookResp createWebHook(CreateWebHookVO vo) {
        /*MailAccountEntity mailAccountEntity = getUserAccount(vo.getEa(), null);
        if (mailAccountEntity == null) {
            return null;
        }
        vo.setApiUser(mailAccountEntity.getApiUser());
        vo.setApiKey(mailAccountEntity.getApiKey());
        vo.setUrl(host + "/marketing/email/webhook/callback");
        RequestBody requestBody = httpManager.buildFormBodyByMap(vo, vo.getClass());*/
        setApiUserInfo(vo);
        String hookUrl;
        if (vo.getType().equals(MailApiUserTypeEnum.BATCH.getType())) {
            hookUrl = host + "/marketing/email/webhook/callback/batch/" + vo.getEa() + "/";
        } else if (vo.getType().equals(MailApiUserTypeEnum.TRIGGER.getType())) {
            hookUrl = host + "/marketing/email/webhook/callback/trigger/" + vo.getEa() + "/";
        } else {
            hookUrl = host + "/marketing/email/webhook/callback/default/" + vo.getEa() + "/";
        }
        RequestBody requestBody = new FormBody.Builder().add("apiUser", vo.getApiUser()).add("apiKey", vo.getApiKey()).add("url", hookUrl).add("categoryName", vo.getCategoryName()).add("event", vo.getEvent()).build();
        String url = getSendCloudApiHostUrl() + "webhook/add";
        MailBaseResp<WebHookResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<WebHookResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        return null;
    }

    // 将webHook去掉发送事件
    public void removeWebHookDeliverEvent(String ea) {
        QueryWebHookArg queryWebHookArg = new QueryWebHookArg();
        queryWebHookArg.setEa(ea);
        WebHookResp webHookResp = webHookList(queryWebHookArg);
        if (webHookResp == null || CollectionUtils.isEmpty(webHookResp.getDataList())) {
            log.warn("removeWebHookDeliverEvent webHookResp is null, ea: {}", ea);
            return;
        }
        List<WebHookResp.WebHookDetail> webHookDetailList = webHookResp.getDataList();
        for (WebHookResp.WebHookDetail webHookDetail : webHookDetailList) {
            try {
                Map<String, String> eventTypeMap = webHookDetail.getEventTypeMap();
                if (!eventTypeMap.containsKey("1") && !eventTypeMap.containsKey("4") && !eventTypeMap.containsKey("5")) {
                    continue;
                }
                String oldEvent = String.join(",", eventTypeMap.keySet());
                eventTypeMap.remove("1");
                eventTypeMap.remove("18");
                eventTypeMap.remove("4");
                eventTypeMap.remove("5");
                String newEvent = String.join(",", eventTypeMap.keySet());
                UpdateWebHookArg arg = new UpdateWebHookArg();
                arg.setEa(ea);
                arg.setCategoryName(webHookDetail.getCategoryName());
                arg.setEvent(oldEvent);
                arg.setUrl(webHookDetail.getWebhookUrl());
                arg.setNewEvent(newEvent);
                UpdateWebHookResp updateWebHookResp = updateWebHook(arg);
                log.info("removeWebHookDeliverEvent update ea: {} arg: {} result: {}", ea, arg, updateWebHookResp);
            } catch (Exception e) {
                log.error("removeWebHookDeliverEvent update ea: {} webHookDetail: {} ", ea, webHookDetail, e);
            }
        }
    }

    public WebHookResp webHookList(QueryWebHookArg queryWebHookArg) {
        setApiUserInfo(queryWebHookArg);
        String url = getSendCloudApiHostUrl() + "webhook/list";
        FormBody.Builder builder = new FormBody.Builder().add("apiUser", queryWebHookArg.getApiUser())
                .add("apiKey", queryWebHookArg.getApiKey());
        if (StringUtils.isNotBlank(queryWebHookArg.getCategoryName())) {
            builder.add("categoryName", queryWebHookArg.getCategoryName());
        }
        if (StringUtils.isNotBlank(queryWebHookArg.getEvent())) {
            builder.add("event", queryWebHookArg.getEvent());
        }
        if (StringUtils.isNotBlank(queryWebHookArg.getUrl())) {
            builder.add("url", queryWebHookArg.getUrl());
        }

        RequestBody requestBody = builder.build();
        MailBaseResp<WebHookResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<WebHookResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        return null;
    }

    public UpdateWebHookResp updateWebHook(UpdateWebHookArg updateWebHookArg) {
        if (StringUtils.isBlank(updateWebHookArg.getNewCategoryName()) && StringUtils.isBlank(updateWebHookArg.getNewEvent())
                && StringUtils.isBlank(updateWebHookArg.getNewUrl())) {
            log.warn("updateWebHook param error, arg: {}", updateWebHookArg);
            return null;
        }
        setApiUserInfo(updateWebHookArg);
        String url = getSendCloudApiHostUrl() + "webhook/update";
        FormBody.Builder builder = new FormBody.Builder().add("apiUser", updateWebHookArg.getApiUser())
                .add("apiKey", updateWebHookArg.getApiKey()).add("url", updateWebHookArg.getUrl())
                .add("categoryName", updateWebHookArg.getCategoryName()).add("event", updateWebHookArg.getEvent());
        if (StringUtils.isNotBlank(updateWebHookArg.getNewUrl())) {
            builder.add("newUrl", updateWebHookArg.getNewUrl());
        }
        if (StringUtils.isNotBlank(updateWebHookArg.getNewEvent())) {
            builder.add("newEvent", updateWebHookArg.getNewEvent());
        }
        if (StringUtils.isNotBlank(updateWebHookArg.getNewCategoryName())) {
            builder.add("newCategoryName", updateWebHookArg.getNewCategoryName());
        }
        RequestBody requestBody = builder.build();
        MailBaseResp<UpdateWebHookResp> resp = httpManager.executePostHttpWithRequestBody(requestBody, url, new TypeToken<MailBaseResp<UpdateWebHookResp>>() {
        });
        if (resp != null && resp.isSuccess() && resp.getInfo() != null) {
            return resp.getInfo();
        }
        return null;
    }

    public void buildUserActionResult(Integer realActionType, UserMarketingActionResult userMarketingActionResult) {
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(userMarketingActionResult.getObjectId());
        if (mailSendTaskEntity == null) {
            return;
        }
        if (realActionType.equals(MarketingUserActionType.MAIL_DELIVER.getActionType())) {
            userMarketingActionResult.setObjectName(mailSendTaskEntity.getSubject());
        } else if (realActionType.equals(MarketingUserActionType.MAIL_OPEN.getActionType())) {
            userMarketingActionResult.setObjectName(mailSendTaskEntity.getSubject());
        } else if (realActionType.equals(MarketingUserActionType.MAIL_RESPONSE.getActionType())) {
            userMarketingActionResult.setObjectName(mailSendTaskEntity.getSubject());
        } else if (realActionType.equals(MarketingUserActionType.MAIL_CLICK.getActionType())) {
            userMarketingActionResult.setObjectName(mailSendTaskEntity.getSubject());
            userMarketingActionResult.setObjectLink(userMarketingActionResult.getSceneId());
        } else if (realActionType.equals(MarketingUserActionType.MAIL_UNSUBSCRIBE.getActionType())) {
            userMarketingActionResult.setObjectName(mailSendTaskEntity.getSubject());
            List<String> senderIds = GsonUtil.fromJson(mailSendTaskEntity.getSenderIds(), new TypeToken<List>() {
            }.getType());
            if (CollectionUtils.isNotEmpty(senderIds)) {
                String senderId = senderIds.get(0);
                MailSendReplyEntity mailSendReplyEntity = mailSendReplyDAO.getById(senderId);
                if (mailSendReplyEntity != null) {
                    userMarketingActionResult.setObjectName(mailSendReplyEntity.getAddress());
                }
            }
        } else if (realActionType.equals(MarketingUserActionType.MAIL_REPORT_SPAM.getActionType())) {
            userMarketingActionResult.setObjectName(mailSendTaskEntity.getSubject());
        }
    }

    public List<ListMailMarketingResult.MailMarketingGroupUser> getMarketingGroupUserInfo(String ea, String groupUserIds) {
        List<ListMailMarketingResult.MailMarketingGroupUser> marketingGroupUser = Lists.newArrayList();
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(groupUserIds)) {
            return marketingGroupUser;
        }
        List<String> marketingGroupUserIds = GsonUtil.getGson().fromJson(groupUserIds, new TypeToken<ArrayList>() {
        }.getType());
        if (CollectionUtils.isEmpty(marketingGroupUserIds)) {
            return marketingGroupUser;
        }
        List<MarketingUserGroupEntity> groupEntityList = marketingUserGroupDao.batchGet(ea, marketingGroupUserIds);
        if (CollectionUtils.isEmpty(groupEntityList)) {
            return marketingGroupUser;
        }
        Map<String, MarketingUserGroupEntity> marketingUserEntityMap = groupEntityList.stream().collect(Collectors.toMap(MarketingUserGroupEntity::getId, Function.identity()));
        for (String marketingUserId : marketingGroupUserIds) {
            ListMailMarketingResult.MailMarketingGroupUser groupUser = new ListMailMarketingResult.MailMarketingGroupUser();
            MarketingUserGroupEntity marketingUserGroupEntity = marketingUserEntityMap.get(marketingUserId);
            if (marketingUserGroupEntity != null) {
                groupUser.setMarketingGroupUserId(marketingUserId);
                groupUser.setMarketingGroupUserName(marketingUserEntityMap.get(marketingUserId).getName());
                groupUser.setMarketingGroupUserCount(marketingUserEntityMap.get(marketingUserId).getUserNumber());
                marketingGroupUser.add(groupUser);
            }
        }
        return marketingGroupUser;
    }

    public Map<String, ListMailMarketingResult.MailMarketingGroupUser> getMarketingGroupUserMap(String ea, List<String> groupUserIds) {
        PageUtil pageUtil = new PageUtil(groupUserIds, 1000);
        List<MarketingUserGroupEntity> totalData = Lists.newArrayList();
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<MarketingUserGroupEntity> groupEntityList = marketingUserGroupDao.batchGet(ea, pageUtil.getPagedList(i));
            if (CollectionUtils.isNotEmpty(groupEntityList)) {
                totalData.addAll(groupEntityList);
            }
        }

        Map<String, MarketingUserGroupEntity> marketingUserEntityMap = totalData.stream().collect(Collectors.toMap(MarketingUserGroupEntity::getId, Function.identity()));
        Map<String, ListMailMarketingResult.MailMarketingGroupUser> resultMap = new HashMap<>();
        for (String marketingUserId : groupUserIds) {
            ListMailMarketingResult.MailMarketingGroupUser groupUser = new ListMailMarketingResult.MailMarketingGroupUser();
            MarketingUserGroupEntity marketingUserGroupEntity = marketingUserEntityMap.get(marketingUserId);
            if (marketingUserGroupEntity != null) {
                groupUser.setMarketingGroupUserId(marketingUserId);
                groupUser.setMarketingGroupUserName(marketingUserEntityMap.get(marketingUserId).getName());
                groupUser.setMarketingGroupUserCount(marketingUserEntityMap.get(marketingUserId).getUserNumber());
                resultMap.put(marketingUserId, groupUser);
            }
        }

        return resultMap;
    }

    public Map<String, EmailMarketingDetailInfo> getMarketingActivityUserByEmail(List<String> emails, String ea, Integer fsUserId) {
        // 查询线索
        Map<String, ObjectData> objectMap = Maps.newHashMap();
        Map<String, String> emailUserMap = Maps.newHashMap();
        Map<String, EmailMarketingDetailInfo> result = Maps.newHashMap();
        List<String> selectFields = Lists.newArrayList(CrmCustomerFieldEnum.ID.getFieldName(), CrmCustomerFieldEnum.EMAIL.getFieldName(),
                CrmCustomerFieldEnum.TEL.getFieldName(), CrmLeadFieldEnum.MOBILE.getFieldName(), CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName(),
                ObjectDescribeContants.DESCRIBE_API_NAME, "name");
        PaasQueryFilterArg leadsQueryFilterArg = new PaasQueryFilterArg();
        leadsQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, emails.size());
        paasQueryArg.addFilter(LeadsFieldContants.EMAIL, PaasAndCrmOperatorEnum.IN.getCrmOperator(), emails);
        leadsQueryFilterArg.setQuery(paasQueryArg);
        leadsQueryFilterArg.setSelectFields(selectFields);
        InnerPage<ObjectData> leadsObjList = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, leadsQueryFilterArg, null);
        if (leadsObjList != null && CollectionUtils.isNotEmpty(leadsObjList.getDataList())) {
            for (ObjectData objectData : leadsObjList.getDataList()) {
                String email = objectData.getString(LeadsFieldContants.EMAIL);
                if (StringUtils.isNotBlank(email)) {
                    objectMap.put(email, objectData);
                    emails.remove(email);
                }
            }
        }

        // 查询联系人
        if (CollectionUtils.isNotEmpty(emails)) {
            PaasQueryFilterArg contactQueryFilterArg = new PaasQueryFilterArg();
            contactQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CONTACT.getName());
            paasQueryArg = new PaasQueryArg(0, emails.size());
            paasQueryArg.addFilter(ContactFieldContants.EMAIL, PaasAndCrmOperatorEnum.IN.getCrmOperator(), emails);
            contactQueryFilterArg.setQuery(paasQueryArg);
            contactQueryFilterArg.setSelectFields(selectFields);
            InnerPage<ObjectData> contactList = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, contactQueryFilterArg, null);
            if (contactList != null && CollectionUtils.isNotEmpty(contactList.getDataList())) {
                for (ObjectData objectData : contactList.getDataList()) {
                    String email = objectData.getString(ContactFieldContants.EMAIL);
                    if (StringUtils.isNotBlank(email)) {
                        objectMap.put(email, objectData);
                        emails.remove(email);
                    }
                }
            }
        }


        // 查询客户
        if (CollectionUtils.isNotEmpty(emails)) {
            PaasQueryFilterArg customerQueryFilterArg = new PaasQueryFilterArg();
            customerQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            paasQueryArg = new PaasQueryArg(0, emails.size());
            paasQueryArg.addFilter(AccountFieldContants.EMAIL, PaasAndCrmOperatorEnum.IN.getCrmOperator(), emails);
            customerQueryFilterArg.setQuery(paasQueryArg);
            customerQueryFilterArg.setSelectFields(selectFields);
            InnerPage<ObjectData> customerList = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, customerQueryFilterArg, null);
            if (customerList != null && CollectionUtils.isNotEmpty(customerList.getDataList())) {
                for (ObjectData objectData : customerList.getDataList()) {
                    String email = objectData.getString(AccountFieldContants.EMAIL);
                    if (StringUtils.isNotBlank(email)) {
                        objectMap.put(email, objectData);
                        emails.remove(email);
                    }
                }
            }
        }
        Map<String, List<ObjectData>> mergerMap = new HashMap<>();
        // 查询营销用户
        for (Map.Entry<String, ObjectData> map : objectMap.entrySet()) {
            List<ObjectData> objectDataList = mergerMap.computeIfAbsent(map.getValue().getApiName(), k -> Lists.newArrayList());
            objectDataList.add(map.getValue());
//            List<String> marketingUserId = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, map.getValue().getApiName(), ImmutableList.of(map.getValue()));
//            if (CollectionUtils.isNotEmpty(marketingUserId)) {
//                emailUserMap.put(map.getKey(), marketingUserId.get(0));
//            }
        }
        if (MapUtils.isNotEmpty(mergerMap)) {
            mergerMap.forEach((apiName, objectDataList) -> {
                Map<String, String> idToMarketingUserIdMap = userMarketingAccountManager.associateAncGetObjectIdToMarketingUserIdMap(ea, apiName, objectDataList);
                for (ObjectData objectData : objectDataList) {
                    String email = objectData.getString(AccountFieldContants.EMAIL);
                    String id = idToMarketingUserIdMap.get(objectData.getId());
                    if (StringUtils.isNotBlank(email) && StringUtils.isNotBlank(id)) {
                        emailUserMap.put(email, id);
                    }
                }
            });
        }

        if (MapUtils.isNotEmpty(emailUserMap)) {
            Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                    .getBaseInfosByIdsV2(ea, fsUserId, Lists.newArrayList(emailUserMap.values()), InfoStateEnum.DETAIL);
            if (MapUtils.isNotEmpty(userMarketingAccountDataMap)) {
                for (Map.Entry<String, String> map : emailUserMap.entrySet()) {
                    UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(map.getValue());
                    if (userMarketingAccountData != null) {
                        EmailMarketingDetailInfo emailMarketingDetailInfo = new EmailMarketingDetailInfo();
                        emailMarketingDetailInfo.setMarketingUserId(userMarketingAccountData.getUserMarketingAccountId());
                        emailMarketingDetailInfo.setName(userMarketingAccountData.getName());
                        emailMarketingDetailInfo.setEmail(map.getKey());
                        emailMarketingDetailInfo.setPhone(userMarketingAccountData.getPhone());
                        emailMarketingDetailInfo.setCompanyName(userMarketingAccountData.getCompanyName());
                        result.put(map.getKey(), emailMarketingDetailInfo);
                    }
                }
            }
        }

        emailUserMap.clear();
        objectMap.clear();
        return result;
    }

    /**
     * 判断邮件是否可以删除
     *
     * @param taskId
     * @return
     */
    public boolean checkSendCancelable(String taskId) {
        MailSendTaskEntity taskEntity = mailSendTaskDAO.getById(taskId);
        if (taskEntity == null) {
            return false;
        }

        if (taskEntity.getScheduleType() == MailScheduleTypeEnum.SCHEDULE_SEND.getType()
                && taskEntity.getSendStatus() == MailSendStatusEnum.WAIT_FOR_SEND.getStatus()) {
            return true;
        }

        return false;
    }

    /**
     * 取消定时邮件发送
     *
     * @param taskId
     * @param ea
     * @return
     */
    public Result<Boolean> cancelSend(String taskId, String ea) {
        MailSendTaskEntity taskEntity = mailSendTaskDAO.getById(taskId);
        if (taskEntity == null) {
            Result.newSuccess(false);
        }

        if (taskEntity.getScheduleType() != MailScheduleTypeEnum.SCHEDULE_SEND.getType()
                || taskEntity.getSendStatus() != MailSendStatusEnum.WAIT_FOR_SEND.getStatus()) {
            Result.newSuccess(false);
        }

        mailSendTaskDAO.updateStatusById(MailSendStatusEnum.CANCLE.getStatus(), taskId);
        return Result.newSuccess(true);
    }

    public void updateMailMarketingStatus(String ea, String taskId, SendStatusEnum sendStatusEnum) {
        ThreadPoolUtils.execute(() -> {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getMarketingActivityExternalConfigEntity(ea, taskId, AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType());
            if (marketingActivityExternalConfigEntity == null) {
                return;
            }
            marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityExternalConfigEntity.getMarketingActivityId(), sendStatusEnum.getStatus() + "");
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);

    }

    public String getSendCloudApiHostUrl() {
        //获取国际的sendCloud地址
        if (appVersionManager.isInternationalProd()) {
            return MailConstant.INTERNATIONAL_SEND_CLOUD_API_HOST;
        }
        //获取国内的sendCloud地址--http
        return MailConstant.SEND_CLOUD_API_HOST;
    }

    public String getSendCloudApiHostHttpsUrl() {
        //获取国际的sendCloud地址
        if (appVersionManager.isInternationalProd()) {
            return MailConstant.INTERNATIONAL_SEND_CLOUD_API_HOST;
        }

        //获取国内的sendCloud地址--https
        return MailConstant.SEND_CLOUD_API_HTTPS_HOST;
    }


    @Data
    public static class EmailMarketingDetailInfo implements Serializable {

        private String marketingUserId;

        private String name;

        private String email;

        private String phone;

        private String companyName;
    }
}
