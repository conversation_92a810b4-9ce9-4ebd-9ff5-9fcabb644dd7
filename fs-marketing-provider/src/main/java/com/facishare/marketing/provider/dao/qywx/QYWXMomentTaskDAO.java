package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.QywxGroupSendTaskEntity;
import com.facishare.marketing.provider.entity.qywx.QywxMomentTaskEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * Created  By zhoux 2020/03/03
 **/
public interface QYWXMomentTaskDAO {

    @Insert("INSERT INTO qywx_moment_task (id, ea, marketing_activity_id, user_id_list, moment_type, send_type, send_time, content, image_info, image_num, image_size, link_type, link_pic_path, link_title, link_url, video_name, video_media_id, video_size, create_user_id, create_time, update_time, job_id,status,fix_time,name,current_user_id,tag_id_list) "
            + " VALUES (#{obj.id}, #{obj.ea}, #{obj.marketingActivityId}, #{obj.userIdList}, #{obj.momentType}, #{obj.sendType}, #{obj.sendTime}, #{obj.content}, #{obj.imageInfo}, #{obj.imageNum}, #{obj.imageSize}, #{obj.linkType}, #{obj.linkPicPath}, #{obj.linkTitle}, #{obj.linkUrl}, #{obj.videoName}, #{obj.videoMediaId}, #{obj.videoSize}, #{obj.createUserId}, now(),now(), #{obj.jobId}, #{obj.status}, #{obj.fixTime}, #{obj.name}, #{obj.currentUserId},#{obj.tagIdList})")
    int insertQywxMomentTask(@Param("obj") QywxMomentTaskEntity qywxMomentTaskEntity);

    @Select("SELECT * FROM qywx_moment_task WHERE marketing_activity_id = #{marketingActivityId} and ea=#{ea} ")
    QywxMomentTaskEntity getByMarketingActivityId(@Param("ea") String ea,@Param("marketingActivityId") String marketingActivityId);

    @Select("SELECT * FROM qywx_moment_task WHERE id = #{id} ")
    QywxMomentTaskEntity getById(@Param("ea")String ea, @Param("id") String id);

    @Update("update qywx_moment_task set job_id = #{jobId},errcode = 0,errmsg='ok' where id = #{id}")
    void updateJobIdById(@Param("ea")String ea, @Param("jobId") String jobId, @Param("id") String id);

    @Update("update qywx_moment_task set job_id = #{jobId},status = #{status} where id = #{id}")
    void updateJobIdAndStatusById(@Param("ea")String ea, @Param("jobId") String jobId,@Param("status") int status, @Param("id") String id);

    @Update("update qywx_moment_task set image_size = #{imageSize} where id = #{id}")
    void updateImageSizeById(@Param("ea")String ea, @Param("imageSize") Long imageSize, @Param("id") String id);

    @Update("update qywx_moment_task set video_name = #{videoName},video_media_id =#{mediaId} where id = #{id}")
    void updateVideoSizeAndVideoMediaIdById(@Param("ea")String ea, @Param("videoName") String videoName, @Param("id") String id, @Param("mediaId") String mediaId);

    @Update("update qywx_moment_task set status = #{status} where id = #{id}")
    void updateStatusById(@Param("ea")String ea, @Param("status") int status, @Param("id") String id);

    @Update("update qywx_moment_task set status = #{status} where id = #{id} and status = #{oldStatus}")
    int updateStatusByIdWithOldStatus(@Param("ea")String ea, @Param("status") int status, @Param("oldStatus") int oldStatus, @Param("id") String id);

    @Update("update qywx_moment_task set status = #{status},errcode=#{errcode},errmsg=#{errmsg},update_time =now() where id = #{id}")
    void updateStatusAndErrorInfoById(@Param("ea")String ea, @Param("status") int status, @Param("id") String id, @Param("errcode") int errcode, @Param("errmsg") String errmsg);

    @Update("update qywx_moment_task set status = #{status},errcode=#{errcode},errmsg=#{errmsg},moment_id=#{momentId},update_time =now() where id = #{id}")
    void updateStatusAndErrorInfoAndMomentIdById(@Param("ea")String ea, @Param("status") int status, @Param("id") String id, @Param("errcode") int errcode, @Param("errmsg") String errmsg, @Param("momentId") String momentId);

    //获取最近30天的发送任务
    @Select("SELECT id, ea, moment_id FROM qywx_moment_task WHERE  status=3 AND moment_id is not null AND fix_time>=((extract(epoch FROM now()) - 30 * 24 * 3600) * 1000)")
    List<QywxMomentTaskEntity> getMomentEmployeeResultTask(@Param("ea")String ea);

    //获取最近当天的发送任务
    @Select("SELECT id, ea, moment_id FROM qywx_moment_task WHERE  status=3 AND moment_id is not null AND fix_time>=((extract(epoch FROM now()) - 1 * 24 * 3600) * 1000)")
    List<QywxMomentTaskEntity> getMomentEmployeeResultTaskByThisDay(@Param("ea")String ea);

    //获取最近1天的未完成的朋友圈任务
    @Select("SELECT id,ea,job_id FROM qywx_moment_task WHERE  status = 2  AND  job_id is not null AND fix_time>=((extract(epoch FROM now()) -  24 * 3600) * 1000)")
    List<QywxMomentTaskEntity> getMomentTaskResultTask(@Param("ea")String ea);

    //获取延迟发送的朋友圈任务
    @Select("SELECT * FROM qywx_moment_task WHERE  status = 1 and send_type =2  AND  fix_time  <= (extract(epoch FROM now()) * 1000)")
    List<QywxMomentTaskEntity> getMomentDelayTask(@Param("ea")String ea);

    @Select("SELECT * FROM qywx_moment_task WHERE  status = 1 and send_type =2  AND ea=#{ea} AND  fix_time  <= (extract(epoch FROM now()) * 1000)")
    List<QywxMomentTaskEntity> getMomentDelayTaskByEa(@Param("ea")String ea);

    @Select("<script>"
            + "SELECT * FROM qywx_moment_task WHERE ea=#{ea} AND marketing_activity_id IN\n"
            +   "<foreach collection = 'marketingActivityIds' item = 'marketingActivityId' open = '(' separator = ',' close = ')'>"
            +       "#{marketingActivityId}"
            +   "</foreach>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<QywxMomentTaskEntity> listMomentTaskByMarketingActivityIds( @Param("ea") String ea, @Param("marketingActivityIds")List<String> marketingActivityIds);

    @Select("SELECT COUNT(*) FROM qywx_moment_task WHERE ea=#{ea} AND create_time BETWEEN #{startTime} AND #{endTime}")
    int queryTaskByTimeRange(@Param("ea")String ea, @Param("startTime")Date startTime, @Param("endTime")Date endTime);

    @Update("UPDATE qywx_moment_task SET user_id_list = #{obj.userIdList}, moment_type = #{obj.momentType},  send_time = #{obj.sendTime}, content = #{obj.content}, image_info = #{obj.imageInfo}, image_num = #{obj.imageNum}, image_size = #{obj.imageSize}, link_type = #{obj.linkType}, link_pic_path = #{obj.linkPicPath}, " +
            "link_title = #{obj.linkTitle}, link_url = #{obj.linkUrl}, video_name = #{obj.videoName}, video_media_id = #{obj.videoMediaId}, video_size = #{obj.videoSize}, update_time = now(), fix_time = #{obj.fixTime}, name = #{obj.name},current_user_id = #{obj.currentUserId},  tag_id_list = #{obj.tagIdList}, status = #{obj.status}, send_type = #{obj.sendType}" +
            " WHERE marketing_activity_id = #{obj.marketingActivityId} AND ea = #{obj.ea} and send_type = 2 ")
    int updateQywxMomentTask(@Param("obj") QywxMomentTaskEntity qywxMomentTaskEntity);

    @Update("update qywx_moment_task set send_employee_count = #{sendEmployeeCount},send_customer_count=#{sendCustomerCount},unsend_customer_count=#{unsendCustomerCount},update_time =now() where id = #{id}")
    void updateStatisticDataById(@Param("ea") String ea, @Param("id") String id, @Param("sendEmployeeCount") int sendEmployeeCount, @Param("sendCustomerCount") int sendCustomerCount, @Param("unsendCustomerCount") int unsendCustomerCount);

    @Select("<script> SELECT DISTINCT  ea   FROM qywx_moment_task  </script>")
    List<String> findEaAll(@Param("ea") String ea);

    @Select("SELECT * FROM qywx_moment_task WHERE ea=#{ea}")
    List<QywxMomentTaskEntity> queryMomentTaskByEa(@Param("ea") String ea);

    @Select("<script>" +
            "select a.* from qywx_moment_task a " +
            "left join marketing_activity_external_config b on a.ea =b.ea and a.marketing_activity_id = b.marketing_activity_id " +
            "where a.ea = #{ea} and a.fix_time between #{startDate} and #{endDate} " +
            "<if test=\"marketingEventId != null and marketingEventId != ''\">" +
            "and b.marketing_event_id = #{marketingEventId} " +
            "</if>" +
            "</script>")
    List<QywxMomentTaskEntity> listActivityIdsInDateRangeWithObjectIds(@Param("ea") String ea, @Param("startDate") Long startDate, @Param("endDate") Long endDate, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT id,current_user_id,user_id_list FROM qywx_moment_task WHERE ea = #{ea} and (user_id_list like  CONCAT('%', #{userId}, '%') or current_user_id like  CONCAT('%', #{userId}, '%')) ")
    List<QywxMomentTaskEntity> getAllUserIdByUserId(@Param("ea") String ea, @Param("userId") String userId);

    @Update("<script>"
            +" update qywx_moment_task set "
            + "<if test=\"userIdList != null and userIdList != ''\">"
            + " user_id_list = #{userIdList}, "
            + "</if>"
            + "<if test=\"currentUserId != null and currentUserId != ''\">"
            + " current_user_id = #{currentUserId},"
            + "</if>"
            + " update_time=now()\n"
            + " where id = #{id} and ea = #{ea}"
            + "</script>")
    int updateUserIdField(@Param("ea") String ea, @Param("id") String id, @Param("userIdList") String userIdList, @Param("currentUserId") String currentUserId);

    @Select("SELECT id, tag_id_list FROM qywx_moment_task WHERE ea=#{ea} and tag_id_list is not null")
    List<QywxMomentTaskEntity> queryAllTagIdNotNullByEa(@Param("ea") String ea);

    @Update(" update qywx_moment_task set tag_id_list = #{tagIdList}, update_time = now() where ea = #{ea} and id = #{id}")
    int updateTagIdList(@Param("ea") String ea, @Param("id") String id, @Param("tagIdList") String tagIdList);
}
