package com.facishare.marketing.provider.dao.customizedSpread;

import com.facishare.marketing.provider.entity.customizedSpread.CustomizedSpreadSendTaskEntity;
import com.facishare.marketing.provider.entity.whatsapp.WhatsAppSendTaskEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface CustomizedSpreadSendTaskDAO {

    @Insert("INSERT INTO customized_spread_send_task (id, ea, customized_spread_channel_id, send_type, send_time, send_status, create_time, create_by, update_time, update_by) " +
            "VALUES (#{entity.id}, #{entity.ea}, #{entity.customizedSpreadChannelId}, #{entity.sendType}, #{entity.sendTime}, #{entity.sendStatus}, now(), #{entity.createBy}, now(), #{entity.updateBy})")
    int insert(@Param("entity") CustomizedSpreadSendTaskEntity entity);

    @Update("<script>" +
            "UPDATE customized_spread_send_task "
            + "<set>"
            + "send_status = #{sendStatus},"
            + "update_time = now()"
            + "</set> " +
            "WHERE ea = #{ea} AND id = #{id}" +
            "</script>")
    int updateStatus(@Param("ea") String ea, @Param("id") String id, @Param("sendStatus") String sendStatus);

    @Update("<script>" +
            "UPDATE customized_spread_send_task "
            + "<set>"
            + "send_status = 'SENDING',"
            + "update_time = now()"
            + "</set> " +
            "WHERE ea = #{ea} AND id = #{id} and send_status = 'WAIT'" +
            "</script>")
    int updateSendingStatus(@Param("ea") String ea, @Param("id") String id);

    @Select("SELECT * FROM customized_spread_send_task WHERE ea = #{ea} and id = #{id}")
    CustomizedSpreadSendTaskEntity getById(@Param("ea") String ea, @Param("id") String id);

    @Select("<script>" +
            "SELECT * " +
            "FROM customized_spread_send_task " +
            "WHERE ea = #{ea} AND id IN " +
            "<foreach collection='ids' item='item' open='(' separator=',' close=')'>#{item}</foreach> " +
            "order by create_time desc " +
            "</script>")
    List<CustomizedSpreadSendTaskEntity> getByIds(@Param("ea") String ea, @Param("ids") List<String> ids);

    @Select("<script>" +
            "SELECT * " +
            "FROM customized_spread_send_task " +
            "WHERE ea = #{ea} " +
            "<if test=\"status != null\"> AND status = #{status} </if>" +
            "order by create_time desc " +
            "</script>")
    List<CustomizedSpreadSendTaskEntity> getByEa(@Param("ea") String ea, @Param("status") String status);

    @Delete("DELETE FROM customized_spread_send_task WHERE ea = #{ea} and id = #{id}")
    int deleteById(@Param("ea") String ea, @Param("id") String id);

    @Select("<script> select * from customized_spread_send_task where send_status = 'WAIT' and (send_type = 'IMMEDIATELY' or send_time &lt;= now() ) order by send_time asc limit #{limit} </script>")
    List<CustomizedSpreadSendTaskEntity> getUnSendTask(@Param("limit") int limit);

}
