package com.facishare.marketing.provider.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.change.set.component.handler.module.Filter;
import com.facishare.change.set.listener.ChangeSetListener;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.AddBoardArg;
import com.facishare.marketing.api.arg.AddBoardCardArg;
import com.facishare.marketing.api.arg.AddProductArg;
import com.facishare.marketing.api.arg.CreateObjectDataModel;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.data.BoardTemplateData;
import com.facishare.marketing.api.service.BoardService;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.api.service.ProductService;
import com.facishare.marketing.api.service.conference.ConferenceService;
import com.facishare.marketing.api.vo.conference.CreateOrUpdateConferenceVO;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.TagModelSourceTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.NestedIdList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.manager.ArticleDAOManager;
import com.facishare.marketing.provider.dto.TagWithTagModel;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.manager.BoardManager;
import com.facishare.marketing.provider.manager.DisplayOrderManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.HexagonManager;
import com.facishare.warehouse.api.model.arg.*;
import com.facishare.warehouse.api.service.FileUnityService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.xml.ws.soap.Addressing;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component("industryConfigConsumer")
public class IndustryConfigConsumer extends ChangeSetListener {
    @Autowired
    private ArticleDAOManager articleDAOManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private ProductService productService;
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Autowired
    private ConferenceService conferenceService;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private TagModelDao tagModelDao;
    @Autowired
    private TagModelUserTagRelationDao tagModelUserTagRelationDao;
    @Autowired
    private UserTagDao userTagDao;
    @Autowired
    private BoardCardActivityDao boardCardActivityDao;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private FileLibraryDAO fileLibraryDAO;
    @Autowired
    private BoardDao boardDao;
    @Autowired
    private BoardCardDao boardCardDao;
    @Autowired
    private BoardCardListDao boardCardListDao;
    @Autowired
    private FileUnityService fileUnityService;
    @Autowired
    private BoardManager boardManager;
    @Autowired
    private BoardService boardService;
    @Autowired
    private DisplayOrderManager displayOrderManager;
    @ReloadableProperty("picture.fsEa")
    private String pictureDefaultEa;
    private final String ARTICLE_TABLE_NAME = "article";
    private final String PRODUCT_TABLE_NAME = "product";
    private final String CUSTOMIZE_FORM_DATA_TABLE_NAME = "customize_form_data";
    private final String CONFERENCE_TABLE_NAME = "activity";
    private final String HEXAGON_SITE_TABLE_NAME = "hexagon_site";
    private final String FILE_TABLE_NAME = "file_library";
    private final String TAG_MODEL_TABLE_NAME = "tag_model";
    private final String BOARD_TABEL_NAME = "board";

    public IndustryConfigConsumer() {
        super("FS-MK-SET-ACTION-CONSUMER",  Lists.newArrayList(new String[]{"marketingIndustryTag"}));
    }

    /**
     * 入站时 直接物理修改原始数据 重写该方法
     *
     * @param enterpriseId  出站企业id
     * @param tableName     出站组件的数据库表名
     * @param uniqueKeys    该表格的业务唯一id,upsert的where条件 对应表描述中的uniqueKeys
     * @param data          出站的数据 已替换企业id等数据
     * @param defaultFilter 表的默认过滤条件 对应表描述中的basicFilterCondition
     */
    protected void innerInBoundPart(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter) throws Throwable {
        log.info("innerInBoundPart enterpriseId:{} tableName:{} uniqueKeys:{} data:{} defaultFilter:{}", enterpriseId, tableName, uniqueKeys, data, defaultFilter);
        switch (tableName){
            case ARTICLE_TABLE_NAME: {
                //文章入站
                log.info("start hanle article inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processArticleInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle article inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
            case PRODUCT_TABLE_NAME:{
                //产品入站
                log.info("start hanle product inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processProductInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle product inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
            case CUSTOMIZE_FORM_DATA_TABLE_NAME:{
                //表单入站
                log.info("start hanle customize form inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processCustomizeFormDataInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle customize form inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
            case CONFERENCE_TABLE_NAME:{
                //会议入站
                log.info("start hanle conference inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processConferenceInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle conference inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
            case HEXAGON_SITE_TABLE_NAME:{
                //微页面
                log.info("start hanle hexagon site inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processHexagonInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle hexagon site inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
            case FILE_TABLE_NAME:{
                //文件
                log.info("start hanle file inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processFileInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle file inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
            case TAG_MODEL_TABLE_NAME:{
                //标签
                log.info("start hanle tag inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processTagModelInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle tag inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
            case BOARD_TABEL_NAME:{
                //运营计划
                log.info("start hanle board inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                processBoardInbound(enterpriseId, tableName, uniqueKeys, data, defaultFilter);
                log.info("end hanle board inbound enterpriseId:{} tableName:{}", enterpriseId, tableName);
                break;
            }
        }
    }
    public void processBoardInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter){
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        Integer fsUserId = data.getInteger("creator");
        String id = data.getString("id");

        BoardEntity configBoardEntity = boardDao.getBoardByOnlyBoardId(id);
        BoardTemplateData boardTemplateData = new BoardTemplateData();
        boardTemplateData.setName(configBoardEntity.getName());
        boardTemplateData.setIsTemplate(true);
        boardTemplateData.setDefaultTemplateId(configBoardEntity.getDefaultTemplateId());
        boardTemplateData.setVisibleRage(configBoardEntity.getVisibleRange());
        boardTemplateData.setBoardUserIds(Lists.newArrayList(fsUserId));

        Result<String> boardResult = boardService.addBoard(currentEa, fsUserId, boardTemplateData);
        if (!boardResult.isSuccess()){
            return;
        }
        String boardId = boardResult.getData();
        List<String> boardCardIds = boardCardDao.listBoardCardIdsByBoardId(configBoardEntity.getEa(), configBoardEntity.getId());
        if (CollectionUtils.isEmpty(boardCardIds)){
            return;
        }
        List<BoardCardListEntity> oldBoardCardLists = boardCardListDao.listByBoardId(configBoardEntity.getEa(), id);
        List<BoardCardListEntity> newBoardCardLists = boardCardListDao.listByBoardId(currentEa, boardId);
        if (CollectionUtils.isEmpty(oldBoardCardLists) || CollectionUtils.isEmpty(newBoardCardLists)){
            return;
        }

        Map<String, BoardCardListEntity> oldBoardCardListIdMap = oldBoardCardLists.stream().collect(Collectors.toMap(BoardCardListEntity::getId, Function.identity(), (v1,v2)->v2));
        Map<String, BoardCardListEntity> newBoardCardListNameMap = newBoardCardLists.stream().collect(Collectors.toMap(BoardCardListEntity::getName, Function.identity(), (v1,v2)->v2));
        for (String boardCardId : boardCardIds){
            BoardCardEntity boardCardEntity = boardCardDao.getById(boardCardId, configBoardEntity.getEa());
            AddBoardCardArg boardCardArg = new AddBoardCardArg();
            String oldBoardCardListName = oldBoardCardListIdMap.get(boardCardEntity.getBoardCardListId()).getName();
            boardCardArg.setBoardCardListId(newBoardCardListNameMap.get(oldBoardCardListName).getId());
            boardCardArg.setName(boardCardEntity.getName());
            boardCardArg.setDescription(boardCardEntity.getDescription());
            boardCardArg.setGoalType(boardCardEntity.getGoalType());
            boardCardArg.setType(boardCardEntity.getType());
            boardManager.addBoardCard(currentEa, fsUserId, boardId, boardCardArg, false);
        }
    }

    public void processTagModelInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter) {
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        Integer fsUserId = data.getInteger("creator");
        String id = data.getString("id");
        TagModelEntity tagModelEntity = tagModelDao.getByOnlyId(id);
        if (tagModelEntity == null){
            return;
        }

        //复制标签模型数据
        TagModelEntity newTagModel = new TagModelEntity();
        newTagModel.setId(UUIDUtil.getUUID());
        newTagModel.setCreator(fsUserId);
        newTagModel.setEa(currentEa);
        newTagModel.setDescription(data.getString("description"));
        newTagModel.setType(data.getString("type"));
        newTagModel.setState(data.getInteger("state"));
        newTagModel.setMaxGrade(data.getInteger("max_grade"));
        newTagModel.setParentTagSelectable(data.getBoolean("parent_tag_selectable"));
        newTagModel.setName(data.getString("name"));
        newTagModel.setCreator(fsUserId);
        newTagModel.setRoles("_ALL_");
        newTagModel.setSourceType(TagModelSourceTypeEnum.USER.getValue());
        tagModelDao.insertIgnore(newTagModel);
        displayOrderManager.mergeNestedIdToLast(currentEa, DisplayOrderConstants.TAG_MODEL_DISPLAY_KEY, NestedIdList.buildFromIdList(ImmutableList.of(newTagModel.getId())));
        displayOrderManager.mergeNestedIdToLast(currentEa, DisplayOrderConstants.getTagDisplayKey(newTagModel.getId()), new ArrayList<>(0));

        List<TagWithTagModel> tagWithTagModels = tagModelDao.listAllTagsByTagModelIds(tagModelEntity.getEa(), Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(tagWithTagModels)){
            return;
        }
        //复制标签关系和标签数据
        List<String> tagIds = tagWithTagModels.stream().map(TagWithTagModel::getTagId).collect(Collectors.toList());
        List<UserTagEntity> userTagEntityList = userTagDao.batchGet(tagModelEntity.getEa(), tagIds);
        List<TagModelUserTagRelationEntity> userTagRelationEntityList = tagModelUserTagRelationDao.queryTagModelUserTagRelationByTagMode(tagModelEntity.getEa(), tagModelEntity.getId());
        Map<String, String> userTagIdMap = new HashMap<>();
        for (TagModelUserTagRelationEntity tagRelationEntity : userTagRelationEntityList){
            if (userTagIdMap.get(tagRelationEntity.getUserTagId()) == null){
                userTagIdMap.put(tagRelationEntity.getUserTagId(), UUIDUtil.getUUID());
            }
            if (!tagRelationEntity.getParentTagId().equals("NONE") && userTagIdMap.get(tagRelationEntity.getParentTagId()) == null){
                userTagIdMap.put(tagRelationEntity.getParentTagId(), UUIDUtil.getUUID());
            }
        }
        for (TagModelUserTagRelationEntity tagRelationEntity : userTagRelationEntityList){
            String userTagId = userTagIdMap.get(tagRelationEntity.getUserTagId());
            String parentTagId= tagRelationEntity.getParentTagId();
            if (!parentTagId.equals("NONE")) {
                parentTagId = userTagIdMap.get(tagRelationEntity.getParentTagId());
            }
            tagModelUserTagRelationDao.insertIgnore(currentEa, newTagModel.getId(), parentTagId, userTagId, fsUserId, false);
        }

        for (UserTagEntity tagEntity : userTagEntityList){
            tagEntity.setEa(currentEa);
            tagEntity.setId(userTagIdMap.get(tagEntity.getId()));
            tagEntity.setCreateTime(new Date());
            tagEntity.setUpdateTime(new Date());
            tagEntity.setCreator(fsUserId);
            if (!tagEntity.getParentTagId().equals("NONE")){
                tagEntity.setParentTagId(userTagIdMap.get(tagEntity.getParentTagId()));
            }
            userTagDao.insertTagIgnore(tagEntity.getId(), currentEa, fsUserId, tagEntity.getParentTagId(), tagEntity.getGrade(), tagEntity.getName(), TagModelSourceTypeEnum.USER.getValue());
        }
    }

    public void processFileInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter) {
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        Integer fsUserId = data.getInteger("create_by");
        String id = data.getString("id");
        String name = data.getString("file_name");
        String npath =  data.getString("file_path");
        int fileSize = data.getInteger("file_size");
        String[] regs = StringUtils.split(npath, ".");
        FileEntity fileEntity = fileLibraryDAO.getById(id);
        if (fileEntity == null){
            return;
        }

        List<CopyAFilesToOtherRequest> copyFilesToOtherRequestList = new ArrayList<>();
        CopyAFilesToOtherRequest copyAFilesFromOneEnterpriseToOther = new CopyAFilesToOtherRequest();
        CopyAFileFromOneEnterpriseToOther.AUploadFileArg aUploadFileArg = new CopyAFileFromOneEnterpriseToOther.AUploadFileArg();
        CopyAFileFromOneEnterpriseToOther.ADownloadFileArg aDownloadFileArg = new CopyAFileFromOneEnterpriseToOther.ADownloadFileArg();
        aUploadFileArg.setBusiness("fs-marketing");
        aUploadFileArg.setFileExt(regs[1]);
        com.facishare.warehouse.api.model.arg.User user = new com.facishare.warehouse.api.model.arg.User();
        user.setEnterpriseAccount(fileEntity.getEa());
        user.setEnterpriseAccount(currentEa);
        user.setEmployId(fsUserId);
        aUploadFileArg.setUser(user);
        aDownloadFileArg.setBusiness("fs-marketing");
        aDownloadFileArg.setaPath(npath);
        aDownloadFileArg.setUser(user);
        copyAFilesFromOneEnterpriseToOther.setADownloadFileArg(aDownloadFileArg);
        copyAFilesFromOneEnterpriseToOther.setAUploadFileArg(aUploadFileArg);
        copyFilesToOtherRequestList.add(copyAFilesFromOneEnterpriseToOther);

        CopyAFilesToOtherResponse copyResult = fileUnityService.CopyAFilesFromOneEnterpriseToOther(copyFilesToOtherRequestList);
        if (CollectionUtils.isEmpty(copyResult.getResults())){
            log.info("CopyAFilesFromOneEnterpriseToOther return null", copyResult);
            return;
        }

        String newApath = copyResult.getResults().get(0).getFinalAPath();
        FileEntity newFile = new FileEntity();
        newFile.setId(UUIDUtil.getUUID());
        newFile.setEa(currentEa);
        newFile.setCreateBy(fsUserId);
        newFile.setExt(regs[1]);
        newFile.setFileName(name);
        newFile.setFilePath(newApath);
        newFile.setFileSize((long)fileSize);
        newFile.setType(0);
        fileLibraryDAO.insert(newFile);
    }

    private void processHexagonInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter){
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        String hexagonId = data.getString("id");
        String name = data.getString("name");
        Integer fsUserId = data.getInteger("create_by");
        HexagonCopyArg arg = new HexagonCopyArg();
        arg.setId(hexagonId);
        arg.setName(name);
        arg.setCheckMarketingTemplate(false);
        hexagonManager.hexagonCopySite(currentEa, fsUserId, arg, HexagonManager.COPY_FROM_HEXAGON);
    }

    public void processConferenceInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter){
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        ActivityEntity confActivityEntity = conferenceDAO.getConferenceById(data.getString("id"));
        Integer currentFsUserId = data.getInteger("create_by");
        CreateOrUpdateConferenceVO vo = new CreateOrUpdateConferenceVO();
        vo.setConferenceId(UUIDUtil.getUUID());
        vo.setEa(currentEa);
        vo.setFsUserId(currentFsUserId);
        vo.setTitle(data.getString("title"));
        vo.setStartTime(System.currentTimeMillis());
        vo.setEndTime(System.currentTimeMillis() + 3600 * 2 * 1000);
        vo.setLocation(data.getString("location"));
        CreateObjectDataModel.Arg createObjectDataModel = new CreateObjectDataModel.Arg();
        Map<String, Object> objectData = new HashMap<>();
        createObjectDataModel.setObjectData(objectData);
        objectData.put("begin_time", vo.getStartTime());
        objectData.put("end_time", vo.getEndTime());
        objectData.put("event_type", 3);
        if (StringUtils.isNotEmpty(vo.getLocation())) {
            objectData.put("location", vo.getLocation());
        }
        objectData.put("object_describe_api_name", "MarketingEventObj");
        objectData.put("owner", Lists.newArrayList(vo.getFsUserId() + ""));
        vo.setCreateObjectDataModel(createObjectDataModel);

        byte[] bytes = fileV2Manager.downloadAFile(data.getString("conference_details"), 1000, confActivityEntity.getEa());
        String conferenceDetail = fileV2Manager.uploadToApath(bytes, "html", currentEa);
        vo.setConferenceDetails(conferenceDetail);

        List<PhotoEntity> coverPhotoEntityList = photoDAO.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), Lists.newArrayList(data.getString("id")));
        if (CollectionUtils.isNotEmpty(coverPhotoEntityList)) {
            PhotoEntity photoEntity = coverPhotoEntityList.get(0);
            vo.setCoverImagePath(photoEntity.getPath());
        }

        if (CollectionUtils.isNotEmpty(coverPhotoEntityList)){
            vo.setCoverImagePath(coverPhotoEntityList.get(0).getPath());
        }
        log.info("processConferenceInbound:{}", vo);
        conferenceService.create(vo);
    }

    public void processProductInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter){
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        Integer currentFsUserId = data.getInteger("fs_user_id");
        String productId = data.getString("id");
        ProductEntity productEntity = productDAO.getById(productId);
        if (productEntity == null){
            log.info("IndustryConfigConsumer.processProductInbound product is not exist id:{} data:{}", data.getString("id"), data);
            return;
        }

        List<PhotoEntity>  headPhotoLists = photoDAO.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(),Lists.newArrayList(productId));
        List<PhotoEntity> detailPhotoLists = photoDAO.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(),Lists.newArrayList(productId));
        Map<String, String> headPathMap = null;
        Map<String, String> detailPathMap = null;
        if (CollectionUtils.isNotEmpty(headPhotoLists)){
            List<String> headPhotoPaths = headPhotoLists.stream().map(PhotoEntity::getPath).collect(Collectors.toList());
            headPathMap = createNewTapathByExistPhoto(pictureDefaultEa, currentFsUserId, headPhotoPaths);
        }
        if (CollectionUtils.isNotEmpty(detailPhotoLists)){
            List<String> detailPhotoPaths = detailPhotoLists.stream().map(PhotoEntity::getPath).collect(Collectors.toList());
            detailPathMap = createNewTapathByExistPhoto(pictureDefaultEa, currentFsUserId, detailPhotoPaths);
        }
        if (MapUtils.isEmpty(headPathMap) || MapUtils.isEmpty(detailPathMap)){
            log.info("IndustryConfigConsumer.processProductInbound photo path is null headPathMap:{} detailPathMap:{} ea:{} data:{}", headPathMap, detailPathMap, currentEa, data);
            return;
        }
        AddProductArg arg = new AddProductArg();
        arg.setName(data.getString("name"));
        arg.setPrice(data.getString("price"));
        arg.setDiscountPrice(data.getString("discount_price"));
        arg.setType(data.getInteger("type"));
        arg.setSummary(data.getString("summary"));
        arg.setDetailPics(Lists.newArrayList(detailPathMap.values()));
        arg.setHeadPics(Lists.newArrayList(headPathMap.values()));
        arg.setTryOutEnable(data.getBoolean("try_out_enable"));
        arg.setTryOutButtonValue(data.getString("try_out_button_value"));

        productService.addEnterpriseProduct(currentEa, currentFsUserId, arg);
    }

    private void processArticleInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter){
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        byte[] bytes = fileV2Manager.downloadAFile(data.getString("article_path"), data.getString("fs_ea"));
        if (bytes == null){
            log.info("processArticleInbound downloadAFile failed enterpriseId:{} tableName:{} uniqueKeys:{} data:{} defaultFilter:{}", enterpriseId, tableName, uniqueKeys, data, defaultFilter);
            return;
        }
        String contentPath = fileV2Manager.uploadToApath(bytes, "html", currentEa);
        if (contentPath == null){
            log.info("processArticleInbound uploadAFile failed enterpriseId:{} tableName:{} uniqueKeys:{} data:{} defaultFilter:{}", enterpriseId, tableName, uniqueKeys, data, defaultFilter);
            return;
        }

        String parsedContentPath = data.getString("parsed_content_path");
        if (parsedContentPath != null){
            bytes = fileV2Manager.downloadAFile(parsedContentPath, data.getString("fs_ea"));
            if (bytes != null){
                parsedContentPath = fileV2Manager.uploadToApath(bytes, "json", currentEa);;
            }
        }

        ArticleEntity articleEntity = new ArticleEntity();
        articleEntity.setId(UUIDUtil.getUUID());
        articleEntity.setFsEa(currentEa);
        articleEntity.setUid("");
        articleEntity.setTitle(data.getString("title"));
        articleEntity.setSummary(data.getString("summary"));
        articleEntity.setArticlePath(contentPath);
        articleEntity.setParsedContentPath(parsedContentPath);
        articleEntity.setArticleType(data.getInteger("article_type"));
        articleEntity.setCreator(data.getString("creator"));
        articleEntity.setSource(data.getString("source"));
        articleEntity.setSourceType(data.getInteger("source_type"));
        articleEntity.setStatus(data.getInteger("status"));
        articleEntity.setFsUserId(data.getInteger("fs_user_id"));
        articleEntity.setUrl(data.getString("url"));
        articleEntity.setCreateTime(new Date());
        articleEntity.setLastModifyTime(new Date());
        articleEntity.setPhotoUrl(data.getString("photo_url"));
        articleEntity.setPhotoThumbnailUrl(data.getString("photo_thumbnail_url"));
        articleEntity.setPreArticleContent(data.getString("pre_article_content"));
        articleEntity.setCreateSourceType(data.getInteger("create_source_type"));

        //处理文章封面
        List<PhotoEntity> photoEntityList = photoDAO.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), Lists.newArrayList(data.getString("id")));
        String coverPath = null;
        if (CollectionUtils.isNotEmpty(photoEntityList)){
            PhotoEntity photoEntity = photoEntityList.get(0);
            byte[] photoBytes = fileV2Manager.downloadAFile(photoEntity.getPath(), data.getString("fs_ea"));
            if (photoBytes != null){
                String[] res = StringUtils.split(photoEntity.getPath(), ".");
                coverPath = fileV2Manager.uploadToApath(photoBytes, res[1], currentEa);
                String url = fileV2Manager.getUrlByPath(coverPath,currentEa,false);
                log.info("coverPath:{} res:{}", coverPath, res);
                PhotoEntity copyPhotoEntity = new PhotoEntity();
                copyPhotoEntity.setId(UUIDUtil.getUUID());
                copyPhotoEntity.setTargetId(articleEntity.getId());
                copyPhotoEntity.setPath(coverPath);
                copyPhotoEntity.setUrl(url);
                copyPhotoEntity.setThumbnailUrl(url);
                copyPhotoEntity.setTargetType(PhotoTargetTypeEnum.ARTICLE_COVER.getType());
                copyPhotoEntity.setEa(currentEa);
                photoDAO.addPhoto(copyPhotoEntity);
            }
        }
        articleDAOManager.addArticle(articleEntity);
    }

    private void processCustomizeFormDataInbound(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter){
        String currentEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        Integer currentFsUserId = data.getInteger("create_by");
        String customizeFormDataId = data.getString("id");
        customizeFormDataService.copyCustomizeForm(customizeFormDataId, null, currentEa, currentFsUserId);
    }

    private Map<String, String> createNewTapathByExistPhoto(String ea, Integer fsUserId, List<String> paths ){
        if (CollectionUtils.isEmpty(paths)){
            return null;
        }

        Map<String, String> pathMap = new HashMap();
        for (String path : paths){
            String[] regs = StringUtils.split(path, ".");
            byte[] photoBytes = fileV2Manager.downloadAFile(path, ea);
            String taPath = fileV2Manager.uploadToTApath(photoBytes, regs[1], ea, fsUserId);
            if (taPath == null){
                log.info("IndustryConfigConsumer.createNewTapathByExistPhoto failed taPath == null path:{} ea:{} fsUserId:{}", path, ea, fsUserId);
                return null;
            }
            pathMap.put(path, taPath);
        }

        return pathMap;
    }
    


}
