package com.facishare.marketing.provider.service.wxthirdplatform;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import static com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity.ACCESS_TOKEN_AHEAD_EXPIRED_SECONDS;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.result.WxThirdComponentResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdAuthService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.MiniappIntroductionSiteTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.UrlUtils;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatThirdPlatformConfigEntity;
import com.facishare.marketing.provider.manager.EnterpriseDefaultManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WxCloudRestManager;
import com.facishare.wechat.proxy.outer.model.auth.WechatFunctionOrigin;
import com.fxiaoke.wechatrestapi.arg.GetAuthorizationInfoByAuthCodeArg;
import com.fxiaoke.wechatrestapi.data.GetAuthorizationInfoByAuthCodeResult;
import com.fxiaoke.wechatrestapi.service.WechatAuthRestService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.FormBody.Builder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Slf4j
@Service("wxThirdAuthService")
public class WxThirdAuthServiceImpl implements WxThirdAuthService {
    @Autowired
    @Qualifier("jedisCmd")
    private MergeJedisCmd mergeJedisCmd;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;
    @Autowired
    private WechatAuthRestService wechatAuthRestService;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private WxCloudRestManager wxCloudRestManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseDefaultManager enterpriseDefaultManager;
    @ReloadableProperty("marketing_appid")
    private String appId;
    @ReloadableProperty("defaultIntroductionPageSiteId")
    private String defaultIntroductionPageSiteId;


    /** 形如 https://mp.weixin.qq.com/cgi-bin/componentloginpage?component_appid=%s&pre_auth_code=%s&redirect_uri=.../%s/%s&auth_type=2 */
    // https://mp.weixin.qq.com/cgi-bin/componentloginpage?component_appid={{componentAppId}}&pre_auth_code={{preAuthCode}}&redirect_uri=http%3A%2F%2Fwww.fxiaoke.com%2Fmarketing%2FwxThirdAuth%2FauthRedirect%2F{{platformId}}%2F{{infoKey}}%2F{{ea}}&auth_type=2
//    @ReloadableProperty("wx.third.platform.auth.link")
    @ReloadableProperty("wx.third.platform.auth.newlink")
    private String wxThirdPlatformAuthLink;

    /** 各云生成，回调域名为www.fxiaoke.com */
    @Override
    public Result<String> getWxAuthLink(String platformId, String ea, String successRedirectUrl, String failRedirectUrl) {
        String infoKey = UUIDUtil.getUUID();
        mergeJedisCmd.setex(infoKey, 7200, GsonUtil.toJson(new Info(ea, successRedirectUrl, failRedirectUrl)));
        String preAuthLink = wxThirdPlatformAuthLink;
        preAuthLink = preAuthLink.replace("{{componentAppId}}", wechatThirdPlatformManager.getComponentAppId(platformId));
        preAuthLink = preAuthLink.replace("{{preAuthCode}}", wechatThirdPlatformManager.getPreAuthCode(platformId));
        preAuthLink = preAuthLink.replace("{{platformId}}", platformId);
        preAuthLink = preAuthLink.replace("{{infoKey}}", infoKey);
        preAuthLink = preAuthLink.replace("{{ea}}", ea);
        return Result.newSuccess(preAuthLink);
    }

    /** 各云接收，处理认证业务，返回重定向页URL */
    @Override
    public Result<String> authRedirect(String platformId, String infoKey, String authCode, int expiredSeconds, String ea) {// 根据 ea 获取对应域名
        if (appVersionManager.isVpnDisconnectCloud() || appVersionManager.isCurrentCloud(ea)) {
            return authentication(platformId, infoKey, authCode);
        }else {
            String url = wxCloudRestManager.getCloudDispatchUrl(ea) + "/inner/wxThirdAuth/authentication";
            Map<String, String> header = new HashMap<>();
            header.put("x-fs-ei", String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
            FormBody requestBody = new Builder().add("platformId", platformId).add("infoKey", infoKey).add("authCode", authCode).build();
            // 返回值为跳转地址
            return httpManager.executePostByOkHttpClientWithRequestBodyAndHeader(requestBody, url, new TypeToken<Result<String>>() {
            }, header);
        }
    }

    /** 各云处理认证业务，返回重定向页URL */
    @Override
    public Result<String> authentication(String platformId, String infoKey, String authCode) {
        Map<String, String> returnParams = new HashMap<>(2);
        Info info = GsonUtil.fromJson(mergeJedisCmd.get(infoKey), Info.class);
        try {
            WxThirdComponentResult componentResult = wechatThirdPlatformManager.getComponentAccessTokenAndAppId(platformId);
            GetAuthorizationInfoByAuthCodeArg arg = new GetAuthorizationInfoByAuthCodeArg();
            arg.setAuthorizationCode(authCode);
            arg.setComponentAppId(componentResult.getComponentAppId());
            // 换取接口调用令牌authorizer_access_token
            GetAuthorizationInfoByAuthCodeResult result = wechatAuthRestService.getAuthorizationInfoByAuthCode(componentResult.getComponentAccessToken(), arg);
            if(!result.isSuccess()){
                returnParams.put("errorMsg", result.getErrMsg());
                return Result.newSuccess(UrlUtils.appendParamsToUrl(info.getFailRedirectUrl(), returnParams));
            }
            String ea = info.getEa();
            WechatAccountConfigEntity dbWechatAccountConfig = wechatAccountConfigDao.getByWxAppId(ea, result.getAuthorizationInfo().getAuthorizerAppId());
            if (dbWechatAccountConfig != null){
                if (!platformId.equals(dbWechatAccountConfig.getThirdPlatformId())){
                    returnParams.put("errorMsg", I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_150));
                    return Result.newSuccess(UrlUtils.appendParamsToUrl(info.getFailRedirectUrl(), returnParams));
                }
                List<String> wxAppIdBindEas = eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(platformId, dbWechatAccountConfig.getWxAppId(),ea);
                if (!wxAppIdBindEas.isEmpty() && !wxAppIdBindEas.contains(ea)){
                    returnParams.put("errorMsg", I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_155));
                    return Result.newSuccess(UrlUtils.appendParamsToUrl(info.getFailRedirectUrl(), returnParams));
                }
            }
            AuthorizationInfo authorizationInfo = BeanUtil.copyByGson(result.getAuthorizationInfo(), AuthorizationInfo.class);
            String authorizerAppId = authorizationInfo.getAuthorizerAppId();
            wechatAccountConfigDao.mergeBaseInfo(ea, authorizerAppId, platformId, authorizationInfo.getAuthorizerRefreshToken(), authorizationInfo.getAuthorizerAccessToken(), DateUtil.getUnixTimeByDurationSecondsFromNow(authorizationInfo.getExpireTime() - ACCESS_TOKEN_AHEAD_EXPIRED_SECONDS), GsonUtil.toJson(authorizationInfo.getFunctionList()));
            wechatAccountManager.updateWechatAccountInfo(ea, platformId, authorizerAppId);
            String boundWxAppId = eaWechatAccountBindDao.getWxAppIdByEa(ea, platformId);
            if(Strings.isNullOrEmpty(boundWxAppId)){
                eaWechatAccountBindDao.insert(ea, platformId, authorizerAppId);
                // 调纷享云接口，插入合并表中
                wechatAccountManager.bindWxAppIdAndEa(authorizerAppId, ea);
                setDefaultIntroductionPage(ea);//设置默认的通用介绍页，预设到企业，默认使用通用介绍页面
                return Result.newSuccess(info.getSuccessRedirectUrl());
            }else{
                // 重新授权则什么都不做，返回成功
                if(authorizerAppId.equals(boundWxAppId)){
                    return Result.newSuccess(info.getSuccessRedirectUrl());
                }
                // 这里特殊处理营销通的逻辑，支持将绑定的客脉/客脉Pro小程序强制升级成用户自己的小程序。
                if(MKThirdPlatformConstants.PLATFORM_ID.equals(platformId) && WxAppInfoEnum.isSystemApp(boundWxAppId)){
                    redisManager.setPreBoundWxAppId(ea, boundWxAppId);
                    eaWechatAccountBindDao.deleteByEaAndPlatformId(ea, platformId);
                    eaWechatAccountBindDao.insert(ea, platformId, authorizerAppId);
                    qywxMiniappConfigDAO.updateAppIdCorpIdByEa(authorizerAppId, ea);
                    wechatAccountManager.bindWxAppIdAndEa(authorizerAppId, ea);
                    //设置默认的通用介绍页，预设到企业，默认使用通用介绍页面
                    setDefaultIntroductionPage(ea);
                    return Result.newSuccess(info.getSuccessRedirectUrl());
                }
                returnParams.put("errorMsg", I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_186));
                return Result.newSuccess(UrlUtils.appendParamsToUrl(info.getFailRedirectUrl(), returnParams));
            }
        }catch (Exception e){
            log.warn("error at authRedirect", e);
            returnParams.put("errorMsg", I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8));
            return Result.newSuccess(UrlUtils.appendParamsToUrl(info.getFailRedirectUrl(), returnParams));
        }
    }

    /**
     * 520: 默认的通用介绍页，预设到企业，默认使用通用介绍页面
     * @param ea-
     */
    public Result<Void> setDefaultIntroductionPage(String ea){
        try {
            return enterpriseDefaultManager.setDefaultIntroductionPage(ea);
        } catch (Exception exception){
            log.warn("设置默认通用介绍页出现异常 ea:{}  exception msg:{}", ea, exception.getMessage());
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_219));
        }
    }

    @Getter
    private static class Info{
        public Info(String ea, String successRedirectUrl, String failRedirectUrl) {
            this.ea = ea;
            this.successRedirectUrl = successRedirectUrl;
            this.failRedirectUrl = failRedirectUrl;
        }

        private String ea;
        private String successRedirectUrl;
        private String failRedirectUrl;
    }

    @Getter
    private static class AuthorizationInfo implements Serializable {
        /**
         * 公众号appId
         */
        @SerializedName("authorizer_appid")
        private String authorizerAppId;

        /**
         * 访问微信必须要的token
         */
        @SerializedName("authorizer_access_token")
        private String authorizerAccessToken;

        /**
         * 用于刷新token
         */
        @SerializedName("authorizer_refresh_token")
        private String authorizerRefreshToken;

        /**
         * accessToken 的过期时间
         */
        @SerializedName("expires_in")
        private int expireTime;

        /**
         * 公众号授权的权限列表
         */
        @SerializedName("func_info")
        private List<WechatFunctionOrigin> functionList;
    }
}