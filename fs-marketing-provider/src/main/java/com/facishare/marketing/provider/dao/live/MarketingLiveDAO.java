package com.facishare.marketing.provider.dao.live;

import com.facishare.marketing.provider.dto.MarketingEventIdTypeDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2020/3/20.
 */
public interface MarketingLiveDAO {
    @Insert("INSERT INTO marketing_live(id, corp_id, live_id, marketing_event_id, title, description, start_time, end_time, create_user_id, lecture_name, lecture_password, tags, lecture_url, view_url, " +
            "other_platform_url, status, cover, chat_on, auto_record, max_live_count, record_id, platform, short_view_url, short_lecture_url, show_acitivity_list, xiaoetong_live_id, create_time,update_time, " +
            "sub_event, mudu_parent_id,associated_account_id)\n"
            +" VALUES(#{entity.id}, #{entity.corpId}, #{entity.liveId}, #{entity.marketingEventId}, #{entity.title}, #{entity.description}, #{entity.startTime}, #{entity.endTime}, #{entity.createUserId}, " +
            "#{entity.lectureName}, #{entity.lecturePassword}, #{entity.tags}, #{entity.lectureUrl}, #{entity.viewUrl},  #{entity.otherPlatformUrl}, #{entity.status}, #{entity.cover}, #{entity.chatOn}, " +
            "#{entity.autoRecord}, #{entity.maxLiveCount}, #{entity.recordId},#{entity.platform}, #{entity.shortViewUrl}, #{entity.shortLectureUrl}, #{entity.showAcitivityList}, #{entity.xiaoetongLiveId}, " +
            "#{entity.createTime}, #{entity.updateTime}, #{entity.subEvent}, #{entity.muduParentId}, #{entity.associatedAccountId})")
    int addMarketingLive(@Param("entity")MarketingLiveEntity entity);

    @Select("<script>"
            + "SELECT * FROM marketing_live WHERE id IN(\n"
            + "SELECT id FROM marketing_live  WHERE corp_id=#{corpId} AND platform = 1 AND status!=6\n"
            + "<if test =\"keyword != null\"> AND title LIKE CONCAT ('%', #{keyword}, '%') </if>\n"
            + "<if test=\"statusList != null\">\n"
            +     "AND status IN <foreach collection=\"statusList\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{statusList[${idx}]}</foreach> \n "
            + "</if>"
            + "<if test=\"marketingEventIds != null\">\n"
            + "  AND marketing_event_id IN\n"
            +   "<foreach collection = 'marketingEventIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{marketingEventIds[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            +" UNION\n"
            + "SELECT id FROM marketing_live WHERE corp_id=#{corpId} AND status!=6 AND (platform = 3 OR  platform = 4 OR platform = 6 OR platform = 7 )\n"
            + "<if test =\"keyword != null\"> AND title LIKE CONCAT ('%', #{keyword}, '%') </if>\n"
            + "<if test=\"statusList != null\">\n"
            +     "AND status IN <foreach collection=\"statusList\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{statusList[${idx}]}</foreach> \n "
            + "</if>"
            + "<if test=\"marketingEventIds != null\">\n"
            + "  AND marketing_event_id IN\n"
            +   "<foreach collection = 'marketingEventIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{marketingEventIds[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            +" UNION\n"
            + "SELECT id FROM marketing_live WHERE corp_id=#{corpId} AND status!=6 AND (platform = 2 OR  platform = 5 or  platform is null)\n"
            + "<if test =\"keyword != null\"> AND title LIKE CONCAT ('%', #{keyword}, '%') </if>\n"
            + "<if test=\"marketingEventIds != null\">\n"
            + "  AND marketing_event_id IN\n"
            +   "<foreach collection = 'marketingEventIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{marketingEventIds[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            + "    <if test=\"statusList != null \">\n"
            + "      <trim prefix=\"AND (\" prefixOverrides=\"AND|OR\" suffix=\")\">\n"
            + "        <foreach collection=\"statusList\" index=\"idx\" item=\"state\">\n"
            + "          <if test=\"statusList[idx] == 1\">\n"
            + "            <![CDATA[ OR (start_time < now() AND end_time > now()) ]]>\n"
            + "          </if>\n"
            + "          <if test=\"statusList[idx] == 2\">\n"
            + "            <![CDATA[ OR start_time >= now()]]>\n"
            + "          </if>\n"
            + "          <if test=\"statusList[idx] == 3\">\n"
            + "            <![CDATA[ OR end_time <= now() ]]>\n"
            + "          </if>\n"
            + "          <if test=\"statusList[idx] == 5\">\n"
            + "            <![CDATA[ OR and status = 5 ]]>\n"
            + "          </if>\n"
            + "        </foreach>\n"
            + "      </trim>\n"
            + "     </if>\n"
            + ")\n"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<MarketingLiveEntity> pageLiveEnrollsByEaAndStatus(@Param("keyword")String keyword, @Param("statusList")List<Integer> statusList,
                                                           @Param("corpId")Integer corpId,  @Param("isShowSpread")boolean isShowSpread, @Param("page")Page page,@Param("marketingEventIds")List<String> marketingEventIds);

    @Select("<script>"
            + "SELECT * FROM marketing_live WHERE corp_id=#{corpId} AND marketing_event_id IN\n"
            +   "<foreach collection = 'maketingEventIds' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    List<MarketingLiveEntity> queryMarketingLiveByMarketingEventIds(@Param("corpId")Integer corpId, @Param("maketingEventIds")List<String>maketingEventIds);

    @Select("SELECT * FROM marketing_live WHERE id=#{id}")
    MarketingLiveEntity getById(@Param("id")String id);

    @Select("SELECT * FROM marketing_live WHERE xiaoetong_live_id = #{xiaoetongId} ORDER BY create_time DESC LIMIT 1")
    MarketingLiveEntity getMarketingLiveByXiaoetongId(@Param("xiaoetongId")String xiaoetongId);

    @Deprecated
    @Select("SELECT * FROM marketing_live WHERE marketing_event_id=#{marketingEventId}")
    MarketingLiveEntity getByMarketingEventId(@Param("marketingEventId")String marketingEventId);
    
    @Select("SELECT * FROM marketing_live WHERE corp_id=#{corpId} AND marketing_event_id=#{marketingEventId}")
    MarketingLiveEntity getByCorpIdAndMarketingEventId(@Param("corpId") Integer corpId, @Param("marketingEventId")String marketingEventId);

    @Update("<script>"
          + "UPDATE marketing_live SET title=#{entity.title}, live_id=#{entity.liveId}, description=#{entity.description}, cover=#{entity.cover}, start_time=#{entity.startTime}, end_time=#{entity.endTime}, lecture_name=#{entity.lectureName},\n"
          + "platform=#{entity.platform}, view_url=#{entity.viewUrl}, short_view_url=#{entity.shortViewUrl}, lecture_url=#{entity.lectureUrl}, short_lecture_url=#{entity.shortLectureUrl}, other_platform_url=#{entity.otherPlatformUrl}, lecture_password=#{entity.lecturePassword},\n"
          + "chat_on=#{entity.chatOn}, max_live_count=#{entity.maxLiveCount},\n"
          + "auto_record=#{entity.autoRecord}, tags=#{entity.tags}, show_acitivity_list=#{entity.showAcitivityList}, xiaoetong_live_id=#{entity.xiaoetongLiveId}, associated_account_id=#{entity.associatedAccountId}, update_time=#{entity.updateTime}\n"
          + "WHERE id=#{entity.id}"
          + "</script>")
    int updateMarketingLive(@Param("entity")MarketingLiveEntity entity);

    @Update("<script>"
            + "UPDATE marketing_live SET sub_event = #{entity.subEvent}, mudu_parent_id = #{entity.muduParentId} WHERE id=#{entity.id}"
            + "</script>")
    int updateSubEvent(@Param("entity")MarketingLiveEntity entity);

    @Select("SELECT COUNT(*) FROM marketing_live WHERE corp_id=#{corpId}")
    int getTotalLiveCount(@Param("corpId")Integer corpId);

    @Select("SELECT * FROM marketing_live WHERE live_id=#{liveId} AND corp_id=#{corpId}")
    MarketingLiveEntity getByLiveIdAndCorpId(@Param("liveId")Integer liveId, @Param("corpId")int corpId);

    @Select("SELECT * FROM marketing_live WHERE mudu_parent_id=#{muduParentId} AND corp_id=#{corpId}")
    List<MarketingLiveEntity> getMuduSubEvent(@Param("muduParentId") String muduParentId, @Param("corpId")int corpId);

    @Update("UPDATE marketing_live SET status=#{status}, update_time=now() WHERE live_id=#{liveId}")
    void updateStatusByLiveId(@Param("liveId")Integer liveId, @Param("status")Integer status);

    @Update("UPDATE marketing_live SET status=#{status}, update_time=now() WHERE id=#{id}")
    void updateStatusById(@Param("id")String id, @Param("status")Integer status);

    @Update("UPDATE marketing_live SET marketing_template_id=#{marketingTemplateId}, update_time=now() WHERE id=#{id}")
    void updateHexagonTemplateSiteById(@Param("id")String id, @Param("marketingTemplateId")String marketingTemplateId);

    @Update("UPDATE marketing_live SET status=#{status}, update_time=now() WHERE xiaoetong_live_id=#{xiaoetongLiveId} AND corp_id=#{corpId}")
    void updateXiaoetongLiveStatus(@Param("corpId")int corpId, @Param("xiaoetongLiveId")String xiaoetongLiveId, @Param("status")int status);

    @Update("UPDATE marketing_live SET record_id=#{recordId},update_time=now() WHERE live_id=#{liveId}")
    void updateLiveRecord(@Param("liveId")Integer liveId, @Param("recordId")Integer recordId);

    @Update("UPDATE marketing_live SET form_hexagon_id=#{hexagonId} WHERE id=#{id}")
    void updateFormHexagonById(@Param("id")String id, @Param("hexagonId")String hexagonId);
    
    @Select("SELECT * FROM marketing_live")
    List<MarketingLiveEntity> listAllMarketingLive();

    @Update("UPDATE marketing_live SET title=#{title}, update_time=now() WHERE id=#{id} AND corp_id=#{corpId}")
    int updateTitleById(@Param("id")String id, @Param("corpId")Integer corpId, @Param("title")String title);

    @Update("UPDATE marketing_live SET start_time=#{startTime}, update_time=now() WHERE id=#{id} AND corp_id=#{corpId}")
    int updateStartTimeById(@Param("id")String id,  @Param("corpId")Integer corpId, @Param("startTime")Date startTime);

    @Select("SELECT m1.* FROM marketing_live m1 join marketing_live_statistics m2 ON m1.live_id=m2.live_id WHERE m2.status=3 AND m1.auto_record = 1 AND m1.platform = 1 AND m1.update_time>=#{limitTime} ORDER BY m1.update_time DESC LIMIT 100 OFFSET 0")
    List<MarketingLiveEntity> getForDefaultRecordLiveIds(@Param("limitTime")Date limitTime);

    @Select("SELECT marketing_event_id AS marketingEventId, 'live_marketing' AS event_type, create_time, start_time, end_time, title FROM marketing_live WHERE corp_id=#{corpId} AND status != 6 AND marketing_event_id is not null AND form_hexagon_id is not null ORDER by start_time DESC")
    List<MarketingEventIdTypeDTO> getVisibleLiveMarketingEvent(@Param("corpId")Integer corpId, @Param("page") Page page);

    @Select("SELECT * FROM marketing_live WHERE create_time>=#{limitTime} AND platform in (3, 4, 6, 7) and status!=6")
    List<MarketingLiveEntity> getNeedSyncLive(@Param("limitTime")Date limitTime);

    @Select("SELECT * FROM marketing_live WHERE create_time>=#{limitTime} AND platform = 3 and status!=6")
    List<MarketingLiveEntity> getSyncXiaoetongLive(@Param("limitTime")Date limitTime);

    @Select("SELECT * FROM marketing_live WHERE create_time>=#{limitTime} AND platform = 4 and status!=6")
    List<MarketingLiveEntity> getSyncPolyvLive(@Param("limitTime")Date limitTime);

    @Select("SELECT * FROM marketing_live WHERE create_time>=#{limitTime} AND platform = 6 and status!=6 and sub_event != 1")
    List<MarketingLiveEntity> getSyncMuduLive(@Param("limitTime")Date limitTime);

    @Select("SELECT * FROM marketing_live WHERE create_time>=#{limitTime} AND platform = 7 and status!=6")
    List<MarketingLiveEntity> getSyncVHallLive(@Param("limitTime")Date limitTime);

    @Select("SELECT COUNT(*) FROM marketing_live WHERE corp_id=#{corpId} AND create_time>=#{limitTime} AND platform = 3")
    int getNeedSyncXiaoetongLiveCountByCorpId(@Param("corpId")int corpId, @Param("limitTime")Date limitTime);

    @Select("SELECT marketing_event_id FROM marketing_live WHERE corp_id=#{corpId} AND status != 6")
    List<String> getValidLiveByEa(@Param("corpId")Integer corpId);


    @Select("SELECT COUNT(*) FROM marketing_live WHERE corp_id=#{corpId} AND create_time>=#{limitTime} AND platform = 4")
    int getNeedSyncPolyvLiveCountByCorpId(@Param("corpId")int corpId, @Param("limitTime")Date limitTime);


    @Select("<script>"
            + "SELECT * FROM marketing_live WHERE corp_id=#{corpId}\n"
            + "<if test=\"marketingEventIds != null \">"
            + "  AND marketing_event_id IN\n"
            +   "<foreach collection = 'marketingEventIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{marketingEventIds[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<MarketingLiveEntity> getLiveByEaAndMarketingEventIds(@Param("corpId") int corpId, @Param("marketingEventIds") List<String> marketingEventIds);

    @Select("<script>" + "SELECT * FROM marketing_live \n"
            + "<if test=\"lastId != null \">"
            + "  WHERE id > #{lastId}"
            + "</if>\n"
            + " ORDER BY id asc limit  1000"
            + "</script>")
    List<MarketingLiveEntity> scanAllByLastId(@Param("lastId") String lastId);

    @Select( "SELECT count(*) FROM marketing_live")
    Long countAll();

    @Select("SELECT * FROM marketing_live WHERE corp_id=#{corpId}")
    List<MarketingLiveEntity> getLiveListByCorpId(@Param("corpId")int corpId);

    @Update("<script>"
            + "UPDATE marketing_live\n"
            + "    <set>\n"
            + "      <if test=\"cover != null\">\n"
            + "        cover = #{cover},\n"
            + "      </if>\n"
            + "    </set>\n"
            + "    WHERE id = #{id}"
            + "</script>")
    void updateCoverPath(@Param("id") String id, @Param("cover") String cover);

    @Select("SELECT * FROM marketing_live WHERE  platform = 5")
    List<MarketingLiveEntity> getChannelLive();

    @Update("<script>"
            + "UPDATE marketing_live SET associated_account_id = #{associatedAccountId}, update_time = now() WHERE corp_id = #{corpId} AND platform = 5  "
            + "</script>")
    int updateChannelAccountByIds(@Param("corpId") Integer corpId, @Param("associatedAccountId") String associatedAccountId);

    @Select("<script>"
            + "SELECT distinct(corp_id) FROM marketing_live WHERE 1=1\n"
            + "<if test='startDate != null and endDate != null'>AND create_time between #{startDate} and #{endDate}</if>\n"
            + "</script>")
    List<Integer> getAllCorpId(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("SELECT * FROM marketing_live WHERE marketing_event_id=#{marketingEventId} AND corp_id=#{corpId}")
    MarketingLiveEntity getLiveMarketingDetail(@Param("marketingEventId")String marketingEventId, @Param("corpId")int corpId);

    @Select("<script>"
            + "SELECT * FROM marketing_live WHERE "
            + "<if test='liveIds != null and !liveIds.isEmpty()'>"
            + "live_id IN "
            + "<foreach collection='liveIds' item='item' open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach>"
            + "</if>"
            + "<if test='liveIds != null and !liveIds.isEmpty() and xiaoetongLiveIds != null and !xiaoetongLiveIds.isEmpty()'>"
            + " OR "
            + "</if>"
            + "<if test='xiaoetongLiveIds != null and !xiaoetongLiveIds.isEmpty()'>"
            + "xiaoetong_live_id IN "
            + "<foreach collection='xiaoetongLiveIds' item='item' open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach>"
            + "</if>"
            + "</script>")
    List<MarketingLiveEntity> getByLiveIdsAndXiaoetongLiveIds(@Param("liveIds") Collection<Integer> liveIds, @Param("xiaoetongLiveIds") Collection<String> xiaoetongLiveIds);

    @Select("<script>"
            + "SELECT * FROM marketing_live WHERE marketing_event_id IN "
            + "<foreach collection='marketingEventIds' item='item' open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<MarketingLiveEntity> getByMarketingEventIds(@Param("marketingEventIds") List<String> marketingEventIds);
}
