package com.facishare.marketing.provider.mq.handler.processor;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.audit.log.MarketingAuditLog;
import com.facishare.marketing.audit.log.context.MarketingAuditLogContext;
import com.facishare.marketing.provider.innerArg.qywx.ExternalChatEvent;
import com.facishare.marketing.provider.innerArg.qywx.ExternalContactEvent;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupObjDescribeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatWorkExternalUserObjDescribeManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.mq.handler.audit.log.convert.MessageExtConvert;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OpenQywxChangeMessageProcessor {
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;

    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;

    @Autowired
    private WechatWorkExternalUserObjDescribeManager wechatWorkExternalUserObjDescribeManager;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    private final static String TAG_EXTERNAL_CONTACT = "tag_external_contact";
    private final static String TAG_DELETE_EXTERNAL_CONTACT = "tag_delete_external_contact";
    private final static String TAG_CREATE_EXTERNAL_CHAT = "tag_create_external_chat";
    private final static String TAG_UPDATE_EXTERNAL_CHAT = "tag_update_external_chat";
    private final static String TAG_DISMISS_EXTERNAL_CHAT = "tag_dismiss_external_chat";

    @MarketingAuditLog(bizName = "OpenQywxChangeMessageHandler", entityClass = MessageExt.class, convertClass = MessageExtConvert.class,
            messageId = "#messageId", extra9 = "#extra9", cost9 = "#cost9", ea = "#ea", extra = "#extra")
    public void processMqMessage(MessageExt message) {
        String tag = message.getTags();
        if (StringUtils.isBlank(tag)) {
            return;
        }
        switch (tag) {
            case TAG_EXTERNAL_CONTACT: {
                ExternalContactEvent externalContactEvent = new ExternalContactEvent();
                externalContactEvent.fromProto(message.getBody());
                MarketingAuditLogContext.putVariable("extra", JsonUtil.toJson(externalContactEvent));
                MarketingAuditLogContext.putVariable("ea", externalContactEvent.getFsEa());
                log.info("企微外部联系人创建或者变更事件, msgId: {} msg: {}", message.getMsgId(), JsonUtil.toJson(externalContactEvent));
                if (!qywxManager.isSyncCrmAppCallBackData(externalContactEvent.getFsEa())) {
                    log.info("ea在黑名单中或者营销通为旧应用，ea: {}, msgId: {}", externalContactEvent.getFsEa(), message.getMsgId());
                    return;
                }
                if (marketingActivityRemoteManager.enterpriseStop(externalContactEvent.getFsEa())) {
                    log.info("ea已经过期，ea: {}, msgId: {}", externalContactEvent.getFsEa(), message.getMsgId());
                    return;
                }
                //全网刷描述工具失效，先暂时这么处理
                wechatWorkExternalUserObjDescribeManager.addAllQywxObjectAppSourceField(externalContactEvent.getFsEa());
                wechatWorkExternalUserObjManager.handleScrmExternalUserChangeEvent(externalContactEvent);
                break;
            }
            case TAG_DELETE_EXTERNAL_CONTACT: {
                ExternalContactEvent externalContactEvent = new ExternalContactEvent();
                externalContactEvent.fromProto(message.getBody());
                MarketingAuditLogContext.putVariable("extra", JsonUtil.toJson(externalContactEvent));
                MarketingAuditLogContext.putVariable("ea", externalContactEvent.getFsEa());
                log.info("企微外部联系人删除事件, msgId: {} msg: {}", message.getMsgId(), JsonUtil.toJson(externalContactEvent));
                if (!qywxManager.isSyncCrmAppCallBackData(externalContactEvent.getFsEa())) {
                    log.info("ea在黑名单中或者营销通为旧应用，ea: {}, msgId: {}", externalContactEvent.getFsEa(), message.getMsgId());
                    return;
                }
                if (marketingActivityRemoteManager.enterpriseStop(externalContactEvent.getFsEa())) {
                    log.info("ea已经过期，ea: {}, msgId: {}", externalContactEvent.getFsEa(), message.getMsgId());
                    return;
                }
                //全网刷描述工具失效，先暂时这么处理
                wechatWorkExternalUserObjDescribeManager.addAllQywxObjectAppSourceField(externalContactEvent.getFsEa());
                wechatWorkExternalUserObjManager.handleScrmDeleteExternalUserEvent(externalContactEvent);
                break;
            }
            case TAG_CREATE_EXTERNAL_CHAT:
            case TAG_UPDATE_EXTERNAL_CHAT: {
                ExternalChatEvent externalChatEvent = new ExternalChatEvent();
                externalChatEvent.fromProto(message.getBody());
                MarketingAuditLogContext.putVariable("extra", JsonUtil.toJson(externalChatEvent));
                MarketingAuditLogContext.putVariable("ea", externalChatEvent.getFsEa());
                log.info("企微客户群变更事件, msgId: {} msg: {}", message.getMsgId(), JSON.toJSONString(externalChatEvent));
                if (!qywxManager.isSyncCrmAppCallBackData(externalChatEvent.getFsEa())) {
                    log.info("ea在黑名单中或者营销通为旧应用，ea: {}, msgId: {}", externalChatEvent.getFsEa(), message.getMsgId());
                    return;
                }
                if (marketingActivityRemoteManager.enterpriseStop(externalChatEvent.getFsEa())) {
                    log.info("ea已经过期，ea: {}, msgId: {}", externalChatEvent.getFsEa(), message.getMsgId());
                    return;
                }
                //全网刷描述工具失效，先暂时这么处理
                wechatWorkExternalUserObjDescribeManager.addAllQywxObjectAppSourceField(externalChatEvent.getFsEa());
                wechatGroupObjDescribeManager.handleScrmWechatGroupChangeEvent(externalChatEvent);
                break;
            }
            case TAG_DISMISS_EXTERNAL_CHAT: {
                ExternalChatEvent externalChatEvent = new ExternalChatEvent();
                externalChatEvent.fromProto(message.getBody());
                MarketingAuditLogContext.putVariable("extra", JsonUtil.toJson(externalChatEvent));
                MarketingAuditLogContext.putVariable("ea", externalChatEvent.getFsEa());
                log.info("企微客户群解散事件, msgId: {} msg: {}", message.getMsgId(), JSON.toJSONString(externalChatEvent));
                if (!qywxManager.isSyncCrmAppCallBackData(externalChatEvent.getFsEa())) {
                    log.info("ea在黑名单中或者营销通为旧应用，ea: {}, msgId: {}", externalChatEvent.getFsEa(), message.getMsgId());
                    return;
                }
                if (marketingActivityRemoteManager.enterpriseStop(externalChatEvent.getFsEa())) {
                    log.info("ea已经过期，ea: {}, msgId: {}", externalChatEvent.getFsEa(), message.getMsgId());
                    return;
                }
                //全网刷描述工具失效，先暂时这么处理
                wechatWorkExternalUserObjDescribeManager.addAllQywxObjectAppSourceField(externalChatEvent.getFsEa());
                wechatGroupObjDescribeManager.handleScrmWechatGroupDeleteEvent(externalChatEvent);
                break;
            }
            default:
                break;
        }
    }
}
