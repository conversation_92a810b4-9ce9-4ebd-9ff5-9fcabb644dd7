package com.facishare.marketing.provider.dao.live;

import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2020/3/30.
 */
public interface MarketingLiveStatisticsDAO {
    @Select("SELECT * FROM marketing_live_statistics WHERE live_id=#{liveId} and type = 1")
    MarketingLiveStatistics getByLiveId(@Param("liveId")Integer liveId,@Param("corpId")int corpId);

    @Select("SELECT * FROM marketing_live_statistics WHERE corp_id=#{corpId} AND xiaoetong_live_id=#{xiaoetongLiveId} and (type = 3 or type = 4 or type = 6 or type = 7)")
    List<MarketingLiveStatistics> getByXiaoetongLiveId(@Param("corpId") int corpId, @Param("xiaoetongLiveId") String xiaoetongLiveId);

    @Select("SELECT * FROM marketing_live_statistics WHERE corp_id=#{corpId} AND xiaoetong_live_id=#{xiaoetongLiveId}")
    List<MarketingLiveStatistics> getByXiaoetongLiveIdV2(@Param("corpId") int corpId, @Param("xiaoetongLiveId") String xiaoetongLiveId);

    @Insert("INSERT INTO marketing_live_statistics(id, corp_id, live_id, total_view_users, total_attendee_users, view_times, view_duration, create_time, update_time, status, total_chat_user, chat_times, total_record_users, record_times, type, xiaoetong_live_id)\n"
           +" VALUES(#{entity.id}, #{entity.corpId}, #{entity.liveId}, #{entity.totalViewUsers}, #{entity.totalAttendeeUsers}, #{entity.viewTimes}, #{entity.viewDuration}, now(), now(), #{entity.status}, #{entity.totalChatUser}, #{entity.chatTimes}, #{entity.totalRecordUsers}, #{entity.recordTimes}, #{entity.type}, #{entity.xiaoetongLiveId})")
    void insert(@Param("entity")MarketingLiveStatistics entity);

    @Update("UPDATE marketing_live_statistics SET total_view_users=#{totalViewUsers}, total_attendee_users=#{totalAttendeeUsers}," +
            "view_times=#{viewTimes}, view_duration=#{viewDuration}, update_time=now() WHERE live_id=#{liveId}")
    void updateStatisticsByLiveId(@Param("liveId")int liveId, @Param("totalViewUsers")int totalViewUsers,
                                  @Param("totalAttendeeUsers")int totalAttendeeUsers, @Param("viewTimes")int viewTimes,
                                  @Param("viewDuration")int viewDuration,@Param("corpId")int corpId);
    @Select("<script>" +
            "SELECT l.marketing_event_id as id, sum(coalesce(s.view_times, 0)) as count FROM marketing_live l join marketing_live_statistics s on l.live_id = s.live_id WHERE l.corp_id=#{corpId} AND l.marketing_event_id IN " +
            "<foreach collection='marketingEventIds' open='(' close=')' separator=',' item='item'>#{item}</foreach> " +
            "GROUP BY l.marketing_event_id" +
            "</script>")
    List<DataCount> groupCountPvByMarketingEventIds(@Param("corpId")Integer corpId, @Param("marketingEventIds")Collection<String> marketingEventIds);

    @Select("<script>"
            + "SELECT * FROM marketing_live_statistics WHERE live_id IN\n"
            + "<foreach collection='liveIds' open='(' close=')' separator=',' item='item'>"
            +        "#{item}"
            + "</foreach>"
            + "</script>")
    List<MarketingLiveStatistics> getLiveStatByLiveIds(@Param("liveIds")List<Integer> liveIds,@Param("corpId")int corpId);

    @Select("<script>"
            + "SELECT * FROM marketing_live_statistics WHERE xiaoetong_live_id IN\n"
            + "<foreach collection='xiaoetongLiveIds' open='(' close=')' separator=',' item='item'>"
            +        "#{item}"
            + "</foreach>"
            + "</script>")
    List<MarketingLiveStatistics> getLiveStatByXiaoetongLiveIds(@Param("xiaoetongLiveIds")List<String> xiaoetongLiveIds,@Param("corpId")int corpId);

    @Select("SELECT COUNT(*) FROM marketing_live_statistics WHERE status != 6 AND create_time>=#{limitTime} AND type = 1")
    int getSyncVhallLiveIdCount(@Param("limitTime")Date limitTime,@Param("ea") String ea);

    @Select("SELECT COUNT(*) FROM marketing_live_statistics WHERE status != 6 AND create_time>=#{limitTime} AND type = 3")
    int getSyncXiaoetongLiveIdCount(@Param("limitTime")Date limitTime,@Param("corpId")int corpId);

    @Select("SELECT live_id FROM marketing_live_statistics WHERE status != 6 AND type = 1 AND create_time>=#{limitTime}")
    List<Integer> getPageSyncVhallLiveIds(@Param("limitTime")Date limitTime, @Param("page")Page page,@Param("ea") String ea);

    @Select("SELECT * FROM marketing_live_statistics WHERE status != 6 AND type = 3 AND create_time>=#{limitTime}")
    List<MarketingLiveStatistics> getPageSyncXiaoetongLive(@Param("limitTime")Date limitTime, @Param("page")Page page,@Param("corpId")int corpId);

    @Update("UPDATE marketing_live_statistics SET total_view_users=#{totalViewUsers}, view_times=#{viewTimes}, record_times = #{recordTimes}, update_time=NOW() "
            + "WHERE id=#{id}")
    int updateViewTimesAndViewUsers(@Param("id")String id, @Param("totalViewUsers")int totalViewUsers, @Param("viewTimes") int viewTimes, @Param("recordTimes") int recordTimes,@Param("corpId")int corpId);

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"totalViewUsers != null\">total_view_users=#{totalViewUsers},</if>"
            +    "<if test=\"viewDuration !=null\">view_duration=#{viewDuration},</if>"
            +    "update_time = now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateViewTimesAndDuration(@Param("liveId")Integer liveId, @Param("totalViewUsers")Integer totalViewUsers, @Param("viewDuration")Integer viewDuration,@Param("corpId")int corpId );

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"status != null\">status=#{status},</if>"
            +    "update_time = now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateStatus(@Param("liveId")Integer liveId, @Param("status")Integer status,@Param("corpId")int corpId);

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"status != null\">status=#{status},</if>"
            +    "update_time = now()\n"
            + "</set>"
            + "WHERE xiaoetong_live_id=#{xiaoetongLiveId} AND corp_id=#{corpId}"
            + "</script>")
    int updateXiaoetongLiveStatus(@Param("corpId")int corpId, @Param("xiaoetongLiveId")String xiaoetongLiveId, @Param("status")Integer status);

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"chatUser != null\">total_chat_user=#{chatUser},</if>"
            +    "<if test=\"chatTimes != null\">chat_times=#{chatTimes} + chat_times,</if>"
            +    "update_time = now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateChatUserData(@Param("liveId")Integer liveId, @Param("chatUser")Integer chatUser, @Param("chatTimes")Integer chatTimes,@Param("corpId")int corpId);

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"viewTimes > 0\">view_times=#{viewTimes},</if>"
            +    "<if test=\"recordTimes > 0\">record_times=#{recordTimes},</if>"
            +    "<if test=\"recordUsers > 0\">total_record_users=#{recordUsers},</if>"
            +    "update_time = now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateViewAndRecordTimes(@Param("liveId") Integer liveId, @Param("viewTimes") int viewTimes, @Param("recordTimes") Integer recordTimes, @Param("recordUsers")Integer recordUsers,@Param("corpId")int corpId);

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"recordTimes > 0\">record_times=#{recordTimes},</if>"
            +    "<if test=\"recordUsers > 0\">total_record_users=#{recordUsers},</if>"
            +    "update_time = now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateRecordTimesAndUsers(@Param("liveId") Integer liveId,@Param("recordTimes") Integer recordTimes, @Param("recordUsers")Integer recordUsers,@Param("corpId")int corpId);


    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"viewTimes > 0\">view_times=#{viewTimes},</if>"
            +    "update_time = now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateViewTimes(@Param("liveId") Integer liveId, @Param("viewTimes") int viewTimes,@Param("corpId")int corpId);


    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"lastSyncActionTime != null\">last_sync_action_time=#{lastSyncActionTime},</if>"
            +    "update_time=now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateLastSyncActionTime(@Param("liveId") Integer liveId, @Param("lastSyncActionTime")Date lastSyncActionTime,@Param("corpId")int corpId);

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"startTime != null\">start_time=#{startTime},</if>"
            +    "update_time=now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateLiveStartTime(@Param("liveId") Integer liveId, @Param("startTime")Date startTime,@Param("corpId")int corpId);

    @Update("<script>"
            + "UPDATE marketing_live_statistics\n"
            + "<set>"
            +    "<if test=\"endTime != null\">end_time=#{endTime},</if>"
            +    "update_time=now()\n"
            + "</set>"
            + "WHERE live_id=#{liveId}"
            + "</script>")
    int updateLiveEndTime(@Param("liveId") Integer liveId, @Param("endTime")Date endTime,@Param("corpId")int corpId);


    @Update("UPDATE marketing_live_statistics SET total_view_users=#{totalViewUsers}, view_times=#{viewTimes}," +
            "total_record_users=#{totalRecordUsers}, record_times=#{recordTimes},chat_times=#{chatTimes},total_chat_user=#{totalChatUser}, update_time=now() WHERE id=#{liveId}")
    void updateStatisticsByPolyvId(@Param("liveId") String liveId, @Param("totalViewUsers")int totalViewUsers,
                                  @Param("viewTimes")int viewTimes, @Param("totalRecordUsers")int totalRecordUsers,
                                  @Param("recordTimes")int recordTimes, @Param("totalChatUser")int totalChatUser, @Param("chatTimes")int chatTimes,@Param("corpId")int corpId);

    @Update("<script>" +
            " UPDATE " +
            "  marketing_live_statistics " +
            " SET " +
            "  status = #{entity.status}, " +
            "  total_attendee_users = #{entity.totalAttendeeUsers}, " +
            "  total_view_users = #{entity.totalViewUsers}, " +
            "  total_record_users = #{entity.totalRecordUsers}, " +
            "  view_times = #{entity.viewTimes}, " +
            "  record_times = #{entity.recordTimes}," +
            "  view_duration = #{entity.viewDuration}," +
            "  total_chat_user = #{entity.totalChatUser}," +
            "  chat_times = #{entity.chatTimes}," +
            "  start_time = #{entity.startTime}," +
            "  end_time = #{entity.endTime}," +
            "  per_view_duration = #{entity.perViewDuration}," +
            "  per_record_duration = #{entity.perRecordDuration}," +
            "  update_time = now()" +
            " WHERE " +
            "  id = #{entity.id}"
            + "</script>")
    int update(@Param("entity") MarketingLiveStatistics entity);
}
