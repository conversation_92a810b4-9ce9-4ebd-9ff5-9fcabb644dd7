package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.SignatureEntity;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created by zhengh on 2018/12/20.
 */
public interface SignatureDAO {
    @Select("SELECT * FROM mw_sms_signature WHERE ea=#{ea} and status = #{status}")
    List<SignatureEntity> getMWEntityByEaAndStatus(@Param("ea") String ea, @Param("status") Integer status);
}
