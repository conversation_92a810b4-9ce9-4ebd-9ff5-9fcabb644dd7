/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.sharegpt.model.chat.streaming;

import com.drew.lang.annotations.Nullable;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.model.output.Response;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.internal.sse.RealEventSource;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.facishare.marketing.provider.sharegpt.model.chat.OpenAiHelper.toOpenAiMessages;
import static com.facishare.marketing.provider.sharegpt.model.chat.OpenAiHelper.toTools;
import static dev.langchain4j.data.message.UserMessage.userMessage;
import static dev.langchain4j.internal.Utils.getOrDefault;
import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;

@Slf4j
public class OpenAiStreamingChatModel {

    private final String baseUrl;
    private final OkHttpClient client;
    private final String tenantId;
    private final String user;
    private final String businessName;
    private final String modelName;
    private final Double temperature;
    private final Integer maxTokens;
    private List<String> imageStrings;

    @Builder
    public OpenAiStreamingChatModel(
            String baseUrl,
            String tenantId,
            String user,
            String businessName,
            String modelName,
            Double temperature,
            Integer maxTokens,
            List<String> imageStrings
    ) {
        this.baseUrl = baseUrl;
        this.client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(1, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .build();
        this.tenantId = getOrDefault(tenantId, "88146");
        this.user = getOrDefault(user, "-10000");
        this.businessName = businessName;
        this.modelName = modelName;
        this.temperature = temperature;
        this.maxTokens = maxTokens;
        this.imageStrings = imageStrings;
    }

    public void generate(List<ChatMessage> messages, List<ToolSpecification> toolSpecifications, OpenAiStreamingResponseHandler handler) {
        generate(messages, toolSpecifications, null, handler);
    }

    private void generate(List<ChatMessage> messages,
                          List<ToolSpecification> toolSpecifications,
                          ToolSpecification toolThatMustBeExecuted,
                          OpenAiStreamingResponseHandler handler
    ) {
        OpenAiStreamingChatComplete.Arg arg = new OpenAiStreamingChatComplete.Arg();
        arg.setModel(modelName);
        String language = I18nUtil.getLanguage();
        if (CollectionUtils.isNotEmpty(imageStrings)) {
            arg.setImageStrings(imageStrings);
            arg.setSupportImage(true);
            messages = Lists.newArrayList(userMessage("en".equals(language) ? "explain" : "详细解释"));
        }
        arg.setMessages(toOpenAiMessages(messages));
        if (temperature != null) {
            arg.setTemperature(temperature);
        }
        if (maxTokens != null) {
            arg.setMaxTokens(maxTokens);
        }

        if (toolSpecifications != null && !toolSpecifications.isEmpty()) {
            arg.setTools(toTools(toolSpecifications));
        }
        if (toolThatMustBeExecuted != null) {
            arg.setTool_choice(toolThatMustBeExecuted.name());
        }


        if (toolSpecifications != null && !toolSpecifications.isEmpty()) {
            arg.setTools(toTools(toolSpecifications));
        }
        if (toolThatMustBeExecuted != null) {
            arg.setTool_choice(toolThatMustBeExecuted.name());
        }
        Headers.Builder builder = new Headers.Builder();
        builder.set("x-fs-ei", tenantId).set("x-fs-userInfo", user == null ? "-10000" : user);
        if (StringUtils.isNotEmpty(businessName)) builder.set("x-fs-business-name", businessName);
        builder.set("x-fs-locale", language);
        builder.set("x-fs-trace-id", TraceContext.get().getTraceId());
        Headers headers = builder.build();
        RequestBody body = RequestBody.create(MediaType.parse(APPLICATION_JSON_UTF8_VALUE), GsonUtil.toJson(arg));
        Request request = new Request.Builder()
                .url(baseUrl + "/v1/sse/openai/chatComplete")
                .headers(headers)
                .post(body)
                .build();
        OpenAiStreamingResponseBuilder responseBuilder = new OpenAiStreamingResponseBuilder();
        TraceContext context = TraceContext.get().copyNecessaries();
        EventSourceListener eventSourceListener = new EventSourceListener() {
            @Override
            public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type, String data) {
                if (context != null) {
                    TraceContext._set(context);
                }
                if ("exception".equals(type)) {
                    throw new RuntimeException(data);
                }
                try {
                    responseBuilder.append(data);
                    handle(data, handler);
                } finally {
                    if (context != null) {
                        TraceContext.remove();
                    }
                }
            }

            @Override
            public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t, @Nullable okhttp3.Response response) {
                if (context != null) {
                    TraceContext._set(context);
                }
                try {
                    handler.onError(t);
                } finally {
                    if (context != null) {
                        TraceContext.remove();
                    }
                }
            }

            @Override
            public void onClosed(@NotNull EventSource eventSource) {
                if (context != null) {
                    TraceContext._set(context);
                }
                try {
                    Response<AiMessage> response = responseBuilder.build();
                    handler.onComplete(response);
                } finally {
                    if (context != null) {
                        TraceContext.remove();
                    }
                }
            }
        };
        RealEventSource realEventSource = new RealEventSource(request, eventSourceListener);
        realEventSource.connect(client);
    }

    private static void handle(String partialResponse,
                               OpenAiStreamingResponseHandler handler) {
        if (partialResponse == null || partialResponse.isEmpty()) {
            return;
        }
        OpenAiStreamingChatComplete.Result result = GsonUtil.fromJson(partialResponse, OpenAiStreamingChatComplete.Result.class);
        String content = result.getContent();
        log.info("agent invoke handle content:{}", content);
        if (content != null) {
            handler.onNext(content);
        }
    }

}
