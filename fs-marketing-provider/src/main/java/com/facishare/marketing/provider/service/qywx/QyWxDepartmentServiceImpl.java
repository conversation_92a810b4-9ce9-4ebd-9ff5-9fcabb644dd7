package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.api.arg.qywx.department.QueryQywxDepartmentArg;
import com.facishare.marketing.api.result.qywx.department.QueryQywxDepartmentResult;
import com.facishare.marketing.api.service.qywx.QyWxDepartmentService;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created  By zhoux 2020/04/10
 **/
@Slf4j
@Service("qyWxDepartmentService")
public class QyWxDepartmentServiceImpl implements QyWxDepartmentService {

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Override
    public Result<List<QueryQywxDepartmentResult>> queryQywxDepartment(QueryQywxDepartmentArg arg) {
        List<QueryQywxDepartmentResult> result = Lists.newArrayList();

        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(arg.getFsEa());
        if (agentConfig == null) {
            return Result.newSuccess();
        }
        List<QywxCustomerAppInfoEntity> appInfoEntities =
                qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(),
                        agentConfig.getEa());
        if (agentConfig == null) {
            log.warn("QyWxDepartmentServiceImpl.queryQywxDepartment is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (appInfoEntities.size()!=0 || agentConfig.getSecret() != null) {

            String accessToken = qywxManager.getAccessToken(arg.getFsEa());
            if (StringUtils.isBlank(accessToken)) {
                log.warn("QywxStaffServiceImpl.queryQywxDepartment accessToken is null arg:{}", arg);
                return null;
            }
            // 获取部门信息
            DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
            result = BeanUtil.copy(departmentListResult.getDepartmentList(), QueryQywxDepartmentResult.class);
            // 将部门id加上10000 与 纷享的部门区分
            for (QueryQywxDepartmentResult queryQywxDepartmentResult : result) {
                queryQywxDepartmentResult.setId(queryQywxDepartmentResult.getId() + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
                queryQywxDepartmentResult.setParentId(queryQywxDepartmentResult.getParentId() + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
            }
            //开启权限插件返回权限内可见部门
            if(dataPermissionManager.getNewDataPermissionSetting(arg.getFsEa())){
                List<Integer> accessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(arg.getFsEa(), arg.getFsUserId(),false);
                result = dataPermissionManager.queryAccessibleOrgStructure(result, Sets.newHashSet(accessibleDepartmentIds));
            }
            return Result.newSuccess(result);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<QueryQywxDepartmentResult>> queryMiniAppQywxDepartment(QueryQywxDepartmentArg arg) {
        List<QueryQywxDepartmentResult> result = Lists.newArrayList();
        // 获取accessToken
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(arg.getFsEa());
        if (agentConfig == null) {
            log.warn("QyWxDepartmentServiceImpl.queryQywxDepartment is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        String accessToken = qywxManager.getMiniAppAccessToken(arg.getFsEa());
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxStaffServiceImpl.queryQywxStaff accessToken is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        // 获取部门信息
        DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
        result = BeanUtil.copy(departmentListResult.getDepartmentList(), QueryQywxDepartmentResult.class);
        // 将部门id加上10000 与 纷享的部门区分
        for (QueryQywxDepartmentResult queryQywxDepartmentResult : result) {
            queryQywxDepartmentResult.setId(queryQywxDepartmentResult.getId() + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
            queryQywxDepartmentResult.setParentId(queryQywxDepartmentResult.getParentId() + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<QueryQywxDepartmentResult>> queryAllQywxDepartment(QueryQywxDepartmentArg arg) {
        List<QueryQywxDepartmentResult> result = Lists.newArrayList();

        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(arg.getFsEa());
        if (agentConfig == null) {
            return Result.newSuccess();
        }
        List<QywxCustomerAppInfoEntity> appInfoEntities =
                qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(),
                        agentConfig.getEa());
        if (agentConfig == null) {
            log.warn("QyWxDepartmentServiceImpl.queryQywxDepartment is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (appInfoEntities.size()!=0 || agentConfig.getSecret() != null) {

            String accessToken = qywxManager.getAccessToken(arg.getFsEa());
            if (StringUtils.isBlank(accessToken)) {
                log.warn("QywxStaffServiceImpl.queryQywxDepartment accessToken is null arg:{}", arg);
                return null;
            }
            // 获取部门信息
            DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
            result = BeanUtil.copy(departmentListResult.getDepartmentList(), QueryQywxDepartmentResult.class);
            // 将部门id加上10000 与 纷享的部门区分
            for (QueryQywxDepartmentResult queryQywxDepartmentResult : result) {
                queryQywxDepartmentResult.setId(queryQywxDepartmentResult.getId() + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
                queryQywxDepartmentResult.setParentId(queryQywxDepartmentResult.getParentId() + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
            }
            return Result.newSuccess(result);
        }
        return Result.newSuccess(result);
    }
}


