/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.advertiser;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Sets;
import com.facishare.marketing.api.LeadStageOptionVO;
import com.facishare.marketing.api.result.advertiser.AdBigScreenResult;
import com.facishare.marketing.api.result.advertiser.AdBigScreenSettingSelectableTimeResult;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.api.vo.marketingevent.MarketingEventAnalysisSettingVO;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.advertiser.BigScreenTimeRangeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.MD5Util;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.bo.advertise.AdBigScreenSettingDetailBO;
import com.facishare.marketing.provider.bo.advertise.AdCampaignDataStatisticsBO;
import com.facishare.marketing.provider.bo.advertise.AdLeadNewOpportunityDataBO;
import com.facishare.marketing.provider.bo.advertise.OpportunityStatisticsDataBO;
import com.facishare.marketing.provider.dao.EnterprseInfoDao;
import com.facishare.marketing.provider.dao.advertiser.bigScreen.AdBigScreenSettingDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDataDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDataDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDataDAO;
import com.facishare.marketing.provider.dto.AdLeadDataDTO;
import com.facishare.marketing.provider.entity.EnterpriseInfoEntity;
import com.facishare.marketing.provider.entity.advertiser.AdBigScreenSettingEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.stream.Collectors;

@Component("adBigScreenManager")
@Slf4j
public class AdBigScreenManager {

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private BaiduCampaignDataDAO baiduCampaignDataDAO;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private HeadlinesCampaignDataDAO headlinesCampaignDataDAO;

    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;

    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;

    @Autowired
    private HeadlinesAdDataDAO headlinesAdDataDAO;

    @Autowired
    private TencentAdGroupDataDAO tencentAdGroupDataDAO;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;

    @Resource(name = "adLeadDataManager")
    private AdLeadDataManager adLeadDataManager;

    @Resource(name = "crmMetadataManager")
    private CrmMetadataManager crmMetadataManager;

    @Resource(name = "crmV2Manager")
    private CrmV2Manager crmV2Manager;

    @Autowired
    private AdBigScreenSettingDAO adBigScreenSettingDAO;

    @Autowired
    private EnterprseInfoDao enterprseInfoDao;

    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    @Autowired
    private AdLeadNewOpportunityDataManager adLeadNewOpportunityDataManager;

    private static final int SCALE = 0;
    private static final int RADIO_SCALE = 2;

    public static final String MQL = "MQL";

    public static final String SQL = "SQL";

    public static final String OPP = "OPP";

    private final String PROVINCE_JSON = "{\"hebei\":\"河北省\",\"ningxia\":\"宁夏\",\"guizhou\":\"贵州省\",\"xinjiang\":\"新疆\",\"beijing\":\"北京\",\"fujian\":\"福建省\",\"hainan\":\"海南省\",\"guangdong\":\"广东省\",\"guangxi\":\"广西\",\"heilongjiang\":\"黑龙江省\",\"zhejiang\":\"浙江省\",\"qinghai\":\"青海省\",\"jiangsu\":\"江苏省\",\"henan\":\"河南省\",\"shanxi\":\"山西省\",\"xizang\":\"西藏\",\"yunnan\":\"云南省\",\"liaoning\":\"辽宁省\",\"hunan\":\"湖南省\",\"xianggang\":\"香港\",\"aomen\":\"澳门\",\"anhui\":\"安徽省\",\"jiangxi\":\"江西省\",\"tianjin\":\"天津\",\"hubei\":\"湖北省\",\"chongqing\":\"重庆\",\"gansu\":\"甘肃省\",\"shanxi1\":\"陕西省\",\"taiwan\":\"台湾\",\"china\":\"全国\",\"shandong\":\"山东省\",\"jilin\":\"吉林省\",\"shanghai\":\"上海\",\"sichuan\":\"四川省\",\"neimenggu\":\"内蒙古\"}";

    private final Map<String, String> PROVINCE_MAP = Maps.newHashMap();

    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    // 同比
    public final static String Y_O_Y = "YoY";

    // 环比
    public final static String M_O_M = "MoM";

    // 自定义
    public final static String CUSTOMIZE = "CUSTOMIZE";

    // 字段的值类型——单选
    public final static String SELECT_ONE = "select_one";

    // 字段的值类型——多选
    public final static String SELECT_MANY = "select_many";

    // 字段的值类型——数组
    public final static String FIELD_ARRAY_VALUE = "ARRAY";

    // 广告账户投入产出分析展示维度——广告账号
    public final static String DIMENSION_ACCOUNT = "ACCOUNT";

    // 广告账户投入产出分析展示维度——广告计划
    public final static String DIMENSION_CAMPAIGN = "CAMPAIGN";

    // 广告账户投入产出分析展示维度——广告关键词
    public final static String DIMENSION_KEYWORD = "KEYWORD";

    // 广告账户投入产出分析展示维度的字段名
    public final static String DIMENSION_FIELD_NAME = "showDimension";

    // mql定义字段名
    public final static String MQL_DEFINITION_FIELD_NAME = "mqlDefinition";

    // sql定义字段名
    public final static String SQL_DEFINITION_FIELD_NAME = "sqlDefinition";

    // 广告账户投入产出分析数据top范围字段名
    public final static String TOP_FIELD_NAME = "top";

    public final static String TOP_FIVE_FIELD_VALUE = "5";

    public final static String TOP_TEN_FIELD_VALUE = "10";

    public final static String OPPORTUNITY_WIN_STAGE = "5";

    public final static String OPPORTUNITY_STG_CHANGED_TIME = "stg_changed_time";

    public final static String OPPORTUNITY_CREATE_TIME = "create_time";

    // 获客成本
    private final static String CUSTOMER_ACQUISITION_COST = "customerAcquisitionCost";
    // 广告整体数据概览
    private final static String OVER_VIEW = "overView";
    // 广告获客转化漏斗
    private final static String ACQUISITION_CUSTOMER_CONVERT_FUNNER = "acquisitionCustomerConvertFunnel";
    // 广告投放效果趋势
    private final static String LAUNCH_EFFECT_TREND = "launchEffectTrendList";
    // 广告线索地域分布
    private final static String LEADS_AREA_DISTRIBUTIONS = "leadsArealDistributions";
    // 产生了多少MQL SQL 商机和订单
    private final static String LEAD_STAGE_AND_SALE_SITUATION = "leadStageAndSaleSituation";
    // 广告获客关键词
    private final static String ACQUISITION_CUSTOMER_KEYWORD = "acquisitionCustomerKeywordList";
    // 广告账户投入产出分析
    private final static String ACCOUNT_INPUT_OUTPUT_ANALYSIS = "accountInputOutputAnalysisList";
    // 广告账户获客对比
    private final static String ACCOUNT_ACQUISITION_CUSTOMER_COMPARE = "accountAcquisitionCustomerCompareList";
    // 线索转化周期
    private final static String CONVERT_PERIOD = "convertPeriod";

    private final static String CHINA = "china";

    @PostConstruct
    private void init() {
        PROVINCE_MAP.putAll(JSONObject.parseObject(PROVINCE_JSON, new TypeReference<Map<String, String>>() {
        }));
    }

    public AdBigScreenResult bigScreen(String ea) {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEa(ea, true);
        long bigScreenBeginTime = System.currentTimeMillis();
        StopWatch stopWatch = new StopWatch();
        // 构建时间参数
        AdBigScreenSettingEntity adBigScreenSettingEntity = getBigScreenSetting(ea);
        AdBigScreenSettingDetailBO settingDetailBO = JSONObject.parseObject(adBigScreenSettingEntity.getSetting(), AdBigScreenSettingDetailBO.class);
        TimePeriod timePeriod = buildTimePeriod(settingDetailBO.getTimeSetting());
        // 查询各个广告账户的展现、点击、消费数据
        stopWatch.start();
        List<AdCampaignDataStatisticsBO> campaignDataList = buildAdAccountCampaignDataList(ea, timePeriod, adAccountEntityList);
        stopWatch.stop();
        log.info("buildAdAccountCampaignDataList total cost: {} ms", stopWatch.getLastTaskTimeMillis());
        // 统计所有广告账户的所有展现、点击、消费数据
        stopWatch.start();
        AdCampaignDataStatisticsBO allStatisticsData = buildAllStatisticsData(campaignDataList);
        stopWatch.stop();
        log.info("buildAllStatisticsData total cost: {} ms", stopWatch.getLastTaskTimeMillis());
        // 根据线索创建时间查询所有广告过来的线索
        stopWatch.start();
        List<AdLeadDataDTO> adLeadDataEntityList = adLeadDataManager.getByLeadCreateTime(ea, timePeriod.getBeginTime(), timePeriod.getEndTime());
        stopWatch.stop();
        log.info("getByLeadCreateTime total cost: {} ms", stopWatch.getLastTaskTimeMillis());

        // 上期的广告线索数量
        stopWatch.start();
        List<AdLeadDataDTO> relativeAdLeadDataEntityList = adLeadDataManager.getByLeadCreateTime(ea, timePeriod.getRelativeBeginTime(), timePeriod.getRelativeEndTime());
        stopWatch.stop();
        log.info("getByLeadCreateTime relative total cost: {} ms", stopWatch.getLastTaskTimeMillis());

        Set<String> modulePositionsSet = new HashSet<>(settingDetailBO.getModulePositions());
        AdBigScreenResult adBigScreenResult = new AdBigScreenResult();

        EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(ea);
        adBigScreenResult.setCompanyName(enterpriseInfoEntity == null ? Strings.EMPTY : enterpriseInfoEntity.getFullName());

        TimeRangeResult lastSixMonthTimeRange = getLastSixMonthTimeRange(timePeriod);
        List<AdLeadDataDTO> lastSixMonthAdLeadDataEntityList = adLeadDataManager.getByLeadCreateTime(ea, lastSixMonthTimeRange.getBeginTime(), lastSixMonthTimeRange.getEndTime());

        //填充广告投放趋势 这里面的查询逻辑比较复杂 需要查几次pass,这里的接口异步执行
        TraceContext context = TraceContext.get();
        FutureTask<List<AdBigScreenResult.LaunchEffectTrend>> launchEffectTrendFutureTask = null;
        if (modulePositionsSet.contains(LAUNCH_EFFECT_TREND)) {
            launchEffectTrendFutureTask = new FutureTask<>(() -> {
                TraceContext._set(context);
                StopWatch newStopWatch = new StopWatch();
                newStopWatch.start();
                List<AdCampaignDataStatisticsBO> buildTotalCostGroupByActionDate = buildTotalCostGroupByActionDate(ea, lastSixMonthTimeRange, adAccountEntityList);
                List<AdBigScreenResult.LaunchEffectTrend> result = buildLaunchEffectTrend(ea, lastSixMonthAdLeadDataEntityList, buildTotalCostGroupByActionDate, lastSixMonthTimeRange);
                newStopWatch.stop();
                log.info("buildLaunchEffectTrend total cost: {} ms", newStopWatch.getLastTaskTimeMillis());
                return result;
            });
            Thread thread = new Thread(launchEffectTrendFutureTask);
            thread.start();
        }
        // 产生了多少MQL、SQL、商机和订单
        FutureTask<AdBigScreenResult.LeadStageAndSaleSituation> leadStageAndSaleSituationFutureTask = null;

        leadStageAndSaleSituationFutureTask = new FutureTask<>(() -> {
            TraceContext._set(context);
            StopWatch newStopWatch = new StopWatch();

            newStopWatch.start();
            // 获取时间段内的赢单商机金额 - 商机是赢单的 并且 按照商机的阶段变更时间过滤
            OpportunityStatisticsDataBO winOpportunityStatisticsData = adLeadNewOpportunityDataManager.getDistinctStatisticsDataByStgChangedTime(ea, OPPORTUNITY_WIN_STAGE, timePeriod.getBeginTime().getTime(), timePeriod.getEndTime().getTime());
            newStopWatch.stop();
            log.info("winOpportunityStatisticsData total cost: {} ms", newStopWatch.getLastTaskTimeMillis());

            newStopWatch.start();
            // 获取同比或环比时间段内的赢单商机金额 - 商机是赢单的 并且 按照商机的阶段变更时间过滤
            OpportunityStatisticsDataBO relativeWinOpportunityStatisticsData = adLeadNewOpportunityDataManager.getDistinctStatisticsDataByStgChangedTime(ea, OPPORTUNITY_WIN_STAGE, timePeriod.getRelativeBeginTime().getTime(), timePeriod.getRelativeEndTime().getTime());
            newStopWatch.stop();
            log.info("relativeWinOpportunityStatisticsData total cost: {} ms", newStopWatch.getLastTaskTimeMillis());

            newStopWatch.start();
            // 填充广告概览
            AdBigScreenResult.OverView overView = buildOverView(ea, timePeriod, allStatisticsData, adLeadDataEntityList, relativeAdLeadDataEntityList, winOpportunityStatisticsData, relativeWinOpportunityStatisticsData);
            adBigScreenResult.setOverView(overView);
            newStopWatch.stop();
            log.info("fillOverView total cost: {} ms", newStopWatch.getLastTaskTimeMillis());
            AdBigScreenResult.LeadStageAndSaleSituation result = null;
            if (modulePositionsSet.contains(LEAD_STAGE_AND_SALE_SITUATION)) {
                newStopWatch.start();
                result = buildLeadStageAndSaleSituation(ea, overView, adLeadDataEntityList, timePeriod, winOpportunityStatisticsData);
                newStopWatch.stop();
                log.info("buildLeadStageAndSaleSituation total cost: {} ms", newStopWatch.getLastTaskTimeMillis());
            }
            return result;
        });
        Thread leadStageAndSaleSituationThread = new Thread(leadStageAndSaleSituationFutureTask);
        leadStageAndSaleSituationThread.start();


        stopWatch.start();
        // 用时间段内的线索且已经转换过的线索，去查询商机的数量、赢单商机的数量、赢单商机的阶段变更时间总和
        OpportunityStatisticsDataBO opportunityStatisticsData = buildOpportunityStatisticsData(ea, adLeadDataEntityList, relativeAdLeadDataEntityList);
        stopWatch.stop();
        log.info("opportunityStatisticsData total cost: {} ms", stopWatch.getLastTaskTimeMillis());

        // 填充广告获客转化漏斗  有10个模快 展示9个 广告获客成本、广告投入产出分析依赖这个数据 所以这里直接去计算漏斗吧
        stopWatch.start();
        AdBigScreenResult.AcquisitionCustomerConvertFunnel acquisitionCustomerConvertFunnel = buildAcquisitionCustomerConvertFunnel(opportunityStatisticsData, allStatisticsData, adLeadDataEntityList, settingDetailBO);
        adBigScreenResult.setAcquisitionCustomerConvertFunnel(acquisitionCustomerConvertFunnel);
        stopWatch.stop();
        log.info("buildAcquisitionCustomerConvertFunnel total cost: {} ms", stopWatch.getLastTaskTimeMillis());
        FutureTask<List<AdBigScreenResult.AccountInputOutputAnalysis>> accountInputOutAnalysisFuture = new FutureTask<>(() -> {
            TraceContext._set(context);
            // 填充广告账户投入产出分析
            StopWatch newStopWatch = new StopWatch();
            newStopWatch.start();
            List<AdBigScreenResult.AccountInputOutputAnalysis> accountInputOutputAnalysisList = buildAccountInputOutputAnalysis(ea, settingDetailBO, adAccountEntityList, campaignDataList, adLeadDataEntityList, relativeAdLeadDataEntityList, acquisitionCustomerConvertFunnel, timePeriod, opportunityStatisticsData);
            newStopWatch.stop();
            log.info("buildLeadsArealDistributions total cost: {} ms", newStopWatch.getLastTaskTimeMillis());
            return accountInputOutputAnalysisList;
        });
        Thread thread = new Thread(accountInputOutAnalysisFuture);
        thread.start();

        if (modulePositionsSet.contains(CUSTOMER_ACQUISITION_COST)) {
            // 填充广告获客成本
            StopWatch newStopWatch = new StopWatch();
            newStopWatch.start();
            AdBigScreenResult.CustomerAcquisitionCost customerAcquisitionCost = buildCustomerAcquisitionCost(opportunityStatisticsData, adLeadDataEntityList, allStatisticsData, acquisitionCustomerConvertFunnel, relativeAdLeadDataEntityList, settingDetailBO);
            adBigScreenResult.setCustomerAcquisitionCost(customerAcquisitionCost);
            newStopWatch.stop();
            log.info("buildCustomerAcquisitionCost total cost: {} ms", newStopWatch.getLastTaskTimeMillis());

        }
        // 填充广告获客关键词
        if (modulePositionsSet.contains(ACQUISITION_CUSTOMER_KEYWORD)) {
            stopWatch.start();
            List<AdBigScreenResult.AcquisitionCustomerKeyword> acquisitionCustomerKeywordList = buildAcquisitionCustomerKeyword(ea, adLeadDataEntityList, timePeriod);
            adBigScreenResult.setAcquisitionCustomerKeywordList(acquisitionCustomerKeywordList);
            stopWatch.stop();
            log.info("buildAcquisitionCustomerKeyword total cost: {} ms", stopWatch.getLastTaskTimeMillis());
        }
        // 填充广告线索地域分布
        stopWatch.start();
        List<AdBigScreenResult.LeadsArealDistribution> leadsArealDistributions = buildLeadsArealDistributions(adLeadDataEntityList, settingDetailBO);
        adBigScreenResult.setLeadsArealDistributions(leadsArealDistributions);
        stopWatch.stop();
        log.info("buildLeadsArealDistributions total cost: {} ms", stopWatch.getLastTaskTimeMillis());

        // 填充广告账户获客对比
        if (modulePositionsSet.contains(ACCOUNT_ACQUISITION_CUSTOMER_COMPARE)) {
            stopWatch.start();
            List<AdBigScreenResult.AccountAcquisitionCustomerCompare> accountAcquisitionCustomerCompareList = buildAccountAcquisitionCustomerCompare(lastSixMonthAdLeadDataEntityList, adAccountEntityList, lastSixMonthTimeRange, settingDetailBO);
            adBigScreenResult.setAccountAcquisitionCustomerCompareList(accountAcquisitionCustomerCompareList);
            stopWatch.stop();
            log.info("buildAccountAcquisitionCustomerCompare total cost: {} ms", stopWatch.getLastTaskTimeMillis());
        }

        if (modulePositionsSet.contains(CONVERT_PERIOD)) {
            //填充转化周期
            stopWatch.start();
            AdBigScreenResult.ConvertPeriod convertPeriod = buildConvertPeriod(adLeadDataEntityList, relativeAdLeadDataEntityList, opportunityStatisticsData);
            adBigScreenResult.setConvertPeriod(convertPeriod);
            stopWatch.stop();
            log.info("buildConvertPeriod total cost: {} ms", stopWatch.getLastTaskTimeMillis());
        }
        //最后填充广告投放趋势
        List<AdBigScreenResult.LaunchEffectTrend> launchEffectTrendList = Lists.newArrayList();
        if (modulePositionsSet.contains(LAUNCH_EFFECT_TREND) && launchEffectTrendFutureTask != null) {
            try {
                launchEffectTrendList = launchEffectTrendFutureTask.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("获取广告投放趋势异常, ea: {}", ea, e);
            }
        }
        adBigScreenResult.setLaunchEffectTrendList(launchEffectTrendList);

        try {
            List<AdBigScreenResult.AccountInputOutputAnalysis> accountInputOutputAnalysisResult = accountInputOutAnalysisFuture.get();
            adBigScreenResult.setAccountInputOutputAnalysisList(accountInputOutputAnalysisResult);
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取广告投入产出分析异常, ea: {}", ea, e);
        }

        if (modulePositionsSet.contains(LEAD_STAGE_AND_SALE_SITUATION)) {
            try {
                AdBigScreenResult.LeadStageAndSaleSituation result = leadStageAndSaleSituationFutureTask.get();
                adBigScreenResult.setLeadStageAndSaleSituation(result);
            } catch (InterruptedException | ExecutionException e) {
                log.error("获取广告产生了多少MQL、SQL、商机和订单异常, ea: {} ", ea, e);
            }
        }

        log.info("get big screen total cost: {} ms", System.currentTimeMillis() - bigScreenBeginTime);
        return adBigScreenResult;
    }

    private OpportunityStatisticsDataBO buildOpportunityStatisticsData(String ea, List<AdLeadDataDTO> adLeadDataEntityList, List<AdLeadDataDTO> relativeAdLeadDataEntityList) {
        OpportunityStatisticsDataBO result = new OpportunityStatisticsDataBO();
        List<String> leadIdList = adLeadDataEntityList.stream().filter(e -> e.getTransformTime() != null && e.getTransformTime() > 0).map(AdLeadDataDTO::getLeadId).collect(Collectors.toList());
        List<String> relativeLeadIdList = relativeAdLeadDataEntityList.stream().filter(e -> e.getTransformTime() != null && e.getTransformTime() > 0).map(AdLeadDataDTO::getLeadId).collect(Collectors.toList());

        OpportunityStatisticsDataBO opportunityStatisticsData = getOpportunityStatisticsDataFromDb(ea, leadIdList);
        result.setOpportunityCount(opportunityStatisticsData.getOpportunityCount());
        result.setWinOpportunityCount(opportunityStatisticsData.getWinOpportunityCount());
        result.setLeadIdToWinOpporSet(opportunityStatisticsData.getLeadIdToWinOpporSet());
        result.setWinOpporTotalStgChangedTime(opportunityStatisticsData.getWinOpporTotalStgChangedTime());

        opportunityStatisticsData = getOpportunityStatisticsDataFromDb(ea, relativeLeadIdList);
        result.setRelativeOpportunityCount(opportunityStatisticsData.getOpportunityCount());
        result.setRelativeWinOpportunityCount(opportunityStatisticsData.getWinOpportunityCount());
        result.setRelativeLeadIdToWinOpporSet(opportunityStatisticsData.getLeadIdToWinOpporSet());
        result.setRelativeWinOpporTotalStgChangedTime(opportunityStatisticsData.getWinOpporTotalStgChangedTime());
        return result;
    }

    private AdBigScreenResult.LeadStageAndSaleSituation buildLeadStageAndSaleSituation(String ea, AdBigScreenResult.OverView overView, List<AdLeadDataDTO> adLeadDataEntityList,
                                                                                       TimePeriod timePeriod, OpportunityStatisticsDataBO winOpportunityStatisticsData) {
        AdBigScreenResult.LeadStageAndSaleSituation leadStageAndSaleSituation = new AdBigScreenResult.LeadStageAndSaleSituation();
        int mqlCount = adLeadDataManager.countByChangedToMqlTime(ea, timePeriod.getBeginTime().getTime(), timePeriod.getEndTime().getTime());

        leadStageAndSaleSituation.setMqlCount(BigDecimal.valueOf(mqlCount));
        leadStageAndSaleSituation.setSqlCount(overView.getSqlCount());

        OpportunityStatisticsDataBO opportunityStatisticsData = adLeadNewOpportunityDataManager.getDistinctStatisticsDataByOpportunityCreateTime(ea, null, timePeriod.getBeginTime().getTime(), timePeriod.getEndTime().getTime());
        leadStageAndSaleSituation.setOpportunityCount(opportunityStatisticsData.getOpportunityCount());

        leadStageAndSaleSituation.setWinOpportunityCount(winOpportunityStatisticsData.getOpportunityCount());
        List<String> leadIdList = adLeadDataEntityList.stream().filter(e -> e.getTransformTime() != null && e.getTransformTime() > 0).map(AdLeadDataDTO::getLeadId).collect(Collectors.toList());
        BigDecimal winOpportunityMoney = getWinOpportunityMoney(ea, leadIdList);
        leadStageAndSaleSituation.setBetweenTimeWinOpportunityMoney(winOpportunityMoney);

        leadStageAndSaleSituation.setBeforeTimeWinOpportunityMoney(overView.getWinOpportunityMoney().subtract(winOpportunityMoney));
        return leadStageAndSaleSituation;
    }


    public AdBigScreenSettingEntity getBigScreenSetting(String ea) {

        AdBigScreenSettingEntity adBigScreenSettingEntity = adBigScreenSettingDAO.getByEa(ea);
        if (adBigScreenSettingEntity == null) {
            return getDefaultBigScreenSetting(ea);
        }
        // 如果有新绑定的广告账号 这里默认不显示
        AdBigScreenSettingDetailBO settingDetailBO = JSONObject.parseObject(adBigScreenSettingEntity.getSetting(), AdBigScreenSettingDetailBO.class);
        AdBigScreenSettingDetailBO.Setting accountAcquisitionCustomerCompare = settingDetailBO.getAccountAcquisitionCustomerCompare();
        List<AdBigScreenSettingDetailBO.Field> fieldList = accountAcquisitionCustomerCompare.getFieldList();
        Set<String> alreadySettingAdAccountId = fieldList.stream().map(AdBigScreenSettingDetailBO.Field::getId).collect(Collectors.toSet());
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEa(ea, true);
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            if (alreadySettingAdAccountId.contains(adAccountEntity.getId())) {
                continue;
            }
            AdBigScreenSettingDetailBO.Field field = new AdBigScreenSettingDetailBO.Field();
            field.setName(adAccountEntity.getUsername());
            field.setId(adAccountEntity.getId());
            field.setCanEdit(true);
            field.setHidden(true);
            fieldList.add(field);
        }

        AdBigScreenSettingDetailBO.TimeSetting timeSetting = settingDetailBO.getTimeSetting();
        BigScreenTimeRangeEnum bigScreenTimeRangeEnum;
        if (timeSetting == null) {
            // 为空说明是历史设置 这里直接去之前设置的时间重新取值
            bigScreenTimeRangeEnum = BigScreenTimeRangeEnum.getByName(adBigScreenSettingEntity.getTimeRange());
            timeSetting = new AdBigScreenSettingDetailBO.TimeSetting();
            timeSetting.setCompareTimeType(Y_O_Y);
            timeSetting.setTimeRange(bigScreenTimeRangeEnum.getName());
        } else {
            bigScreenTimeRangeEnum = BigScreenTimeRangeEnum.getByName(timeSetting.getTimeRange());
        }
        if (bigScreenTimeRangeEnum != BigScreenTimeRangeEnum.CUSTOMIZE) {
            // 根据设置的时间范围取时间的值
            TimeRangeResult timeRangeResult = getDateByTimeRangeResultByEnum(bigScreenTimeRangeEnum);
            timeSetting.setBeginTime(timeRangeResult.getBeginTime());
            timeSetting.setEndTime(timeRangeResult.getEndTime());
            timeSetting.setCompareBeginTime(timeRangeResult.getYoYBeginTime());
            timeSetting.setCompareEndTime(timeRangeResult.getYoYEndTime());
        }
        settingDetailBO.setTimeSetting(timeSetting);

        // 兼容旧数据 填充默认的
        if (settingDetailBO.getOverView() == null) {
            settingDetailBO.setOverView(getDefaultOverViewSetting());
        }
        // 兼容旧数据 填充默认的
        if (CollectionUtils.isEmpty(settingDetailBO.getModulePositions())) {
            settingDetailBO.setModulePositions(getDefaultModulePositions());
        }
        // 兼容旧数据 填充默认的
        if (settingDetailBO.getLeadsArealDistributions() == null) {
            settingDetailBO.setLeadsArealDistributions(getDefaultArealDistributionSetting());
        }
        // 账户投入产出分析兼容旧数据
        AdBigScreenSettingDetailBO.Setting accountInputOutputAnalysis = settingDetailBO.getAccountInputOutputAnalysis();
        if (accountInputOutputAnalysis.getFieldList().stream().noneMatch(e -> e.getName().equals(DIMENSION_FIELD_NAME) || e.getName().equals(TOP_FIELD_NAME))) {
            accountInputOutputAnalysis.getFieldList().addAll(getAccountInputOutputAnalysisOtherField());
        }
        // 转化漏斗兼容旧数据
        AdBigScreenSettingDetailBO.Setting acquisitionCustomerConvertFunnel = settingDetailBO.getAcquisitionCustomerConvertFunnel();
        // 转化漏斗的MQL、SQL的定义从市场活动设置中读取
        List<AdBigScreenSettingDetailBO.Field> acquisitionCustomerConvertFunnelFieldList = acquisitionCustomerConvertFunnel.getFieldList().stream().filter(e -> !e.getName().equals(MQL_DEFINITION_FIELD_NAME) && !e.getName().equals(SQL_DEFINITION_FIELD_NAME)).collect(Collectors.toList());
        acquisitionCustomerConvertFunnelFieldList.addAll(getLeadStageDefinitionFromMarketingEventAnalysisSetting(ea));
        acquisitionCustomerConvertFunnel.setFieldList(acquisitionCustomerConvertFunnelFieldList);

        AdBigScreenSettingDetailBO.Setting convertPeriod = settingDetailBO.getConvertPeriod();
        if (convertPeriod == null) {
            settingDetailBO.setConvertPeriod(getDefaultConvertPeriodSetting());
        }
        adBigScreenSettingEntity.setSetting(JSON.toJSONString(settingDetailBO));
        return adBigScreenSettingEntity;
    }

    private static List<String> filterFieldName(Field[] fields) {
        List<String> resultList = Lists.newArrayList();
        for (Field field : fields) {
            AdBigScreenResult.FieldSetting fieldSetting = field.getDeclaredAnnotation(AdBigScreenResult.FieldSetting.class);
            if (fieldSetting == null || fieldSetting.showOnSetting()) {
                resultList.add(field.getName());
            }
        }
        return resultList;
    }

    public AdBigScreenSettingEntity getDefaultBigScreenSetting(String ea) {
        AdBigScreenSettingEntity entity = new AdBigScreenSettingEntity();
        entity.setTitle(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_542));

        AdBigScreenSettingDetailBO adBigScreenSettingDetailBO = new AdBigScreenSettingDetailBO();

        Class clazz = AdBigScreenResult.CustomerAcquisitionCost.class;
        Field[] fields = clazz.getDeclaredFields();
        List<String> fieldNameList = filterFieldName(fields);
        // 广告获客成本设置
        AdBigScreenSettingDetailBO.Setting customerAcquisitionCostSetting = new AdBigScreenSettingDetailBO.Setting();
        customerAcquisitionCostSetting.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_551));
        List<AdBigScreenSettingDetailBO.Field> showFieldList = Lists.newArrayList();
        for (String fieldName : fieldNameList) {
            AdBigScreenSettingDetailBO.Field field = new AdBigScreenSettingDetailBO.Field();
            field.setName(fieldName);
            // 广告获客成本只有商机的两个字段能够编辑
            field.setCanEdit(fieldName.contains("Opportunity"));
            field.setHidden(false);
            showFieldList.add(field);
        }
        customerAcquisitionCostSetting.setFieldList(showFieldList);

        adBigScreenSettingDetailBO.setCustomerAcquisitionCost(customerAcquisitionCostSetting);

        // 广告投放效果趋势设置
        AdBigScreenSettingDetailBO.Setting launchEffectTrendSetting = new AdBigScreenSettingDetailBO.Setting();
        launchEffectTrendSetting.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_567));
        showFieldList = Lists.newArrayList();

        String tempFieldStr = "COST;LEADS;WECHAT_FANS;QYWX_EXTERNAL_CUSTOMER";
        for (String fieldName : tempFieldStr.split(";")) {
            AdBigScreenSettingDetailBO.Field field = new AdBigScreenSettingDetailBO.Field();
            field.setName(fieldName);
            // 广告投放效果趋势只有微信粉丝和企微客户能编辑
            field.setCanEdit(fieldName.equals("WECHAT_FANS") || fieldName.equals("QYWX_EXTERNAL_CUSTOMER"));
            field.setHidden(false);
            showFieldList.add(field);
        }
        launchEffectTrendSetting.setFieldList(showFieldList);
        adBigScreenSettingDetailBO.setLaunchEffectTrend(launchEffectTrendSetting);

        // 广告获客转化漏斗
        AdBigScreenSettingDetailBO.Setting acquisitionCustomerConvertFunnel = new AdBigScreenSettingDetailBO.Setting();
        acquisitionCustomerConvertFunnel.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_584));
        clazz = AdBigScreenResult.AcquisitionCustomerConvertFunnel.class;

        showFieldList = buildSettingFieldList(clazz);
        showFieldList.addAll(getLeadStageDefinitionFromMarketingEventAnalysisSetting(ea));

        acquisitionCustomerConvertFunnel.setFieldList(showFieldList);
        adBigScreenSettingDetailBO.setAcquisitionCustomerConvertFunnel(acquisitionCustomerConvertFunnel);
        // 广告账户获客对比
        AdBigScreenSettingDetailBO.Setting accountAcquisitionCustomerCompareSetting = new AdBigScreenSettingDetailBO.Setting();
        accountAcquisitionCustomerCompareSetting.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_594));
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEa(ea, true);

        showFieldList = Lists.newArrayList();
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            AdBigScreenSettingDetailBO.Field field = new AdBigScreenSettingDetailBO.Field();
            field.setName(adAccountEntity.getUsername());
            field.setId(adAccountEntity.getId());
            field.setCanEdit(true);
            field.setHidden(false);
            showFieldList.add(field);
        }
        accountAcquisitionCustomerCompareSetting.setFieldList(showFieldList);
        adBigScreenSettingDetailBO.setAccountAcquisitionCustomerCompare(accountAcquisitionCustomerCompareSetting);

        // 广告账户投入产出分析
        AdBigScreenSettingDetailBO.Setting accountInputOutputAnalysis = new AdBigScreenSettingDetailBO.Setting();
        accountInputOutputAnalysis.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_611));

        clazz = AdBigScreenResult.AccountInputOutputAnalysis.class;
        showFieldList = buildSettingFieldList(clazz);
        showFieldList.addAll(getAccountInputOutputAnalysisOtherField());

        accountInputOutputAnalysis.setFieldList(showFieldList);
        adBigScreenSettingDetailBO.setAccountInputOutputAnalysis(accountInputOutputAnalysis);

        AdBigScreenSettingDetailBO.Setting overView = getDefaultOverViewSetting();
        adBigScreenSettingDetailBO.setOverView(overView);

        AdBigScreenSettingDetailBO.TimeSetting timeSetting = getDefaultTimeSetting();
        adBigScreenSettingDetailBO.setTimeSetting(timeSetting);

        List<String> positions = getDefaultModulePositions();
        adBigScreenSettingDetailBO.setModulePositions(positions);

        adBigScreenSettingDetailBO.setLeadsArealDistributions(getDefaultArealDistributionSetting());

        adBigScreenSettingDetailBO.setConvertPeriod(getDefaultConvertPeriodSetting());

        entity.setSetting(JSONObject.toJSONString(adBigScreenSettingDetailBO));
        return entity;
    }

    public static AdBigScreenSettingDetailBO.Setting getDefaultConvertPeriodSetting() {
        AdBigScreenSettingDetailBO.Setting convertPeriod = new AdBigScreenSettingDetailBO.Setting();
        convertPeriod.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_639));
        List<AdBigScreenSettingDetailBO.Field> fieldList = buildSettingFieldList(AdBigScreenResult.ConvertPeriod.class);
        convertPeriod.setFieldList(fieldList);
        return convertPeriod;
    }

    private static List<AdBigScreenSettingDetailBO.Field> buildSettingFieldList(Class clazz) {
        List<AdBigScreenSettingDetailBO.Field> resultList = Lists.newArrayList();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            AdBigScreenSettingDetailBO.Field result = new AdBigScreenSettingDetailBO.Field();
            AdBigScreenResult.FieldSetting fieldSetting = field.getDeclaredAnnotation(AdBigScreenResult.FieldSetting.class);
            if (fieldSetting == null || fieldSetting.showOnSetting()) {
                result.setName(field.getName());
                result.setCanEdit(fieldSetting != null && fieldSetting.canEdit());
                result.setHidden(fieldSetting != null && fieldSetting.hideOnScreen());
                resultList.add(result);
            }
        }
        return resultList;
    }

    private List<AdBigScreenSettingDetailBO.Field> getLeadStageDefinitionFromMarketingEventAnalysisSetting(String ea) {
        // 查询市场活动设置下面的MQL和SQL定义
        Result<MarketingEventAnalysisSettingVO> marketingEventAnalysisSettingResult = marketingEventCommonSettingService.getAnalysisSetting(ea);

        MarketingEventAnalysisSettingVO marketingEventAnalysisSetting = marketingEventAnalysisSettingResult.getData();

        List<AdBigScreenSettingDetailBO.Field> resultList = Lists.newArrayList();

        AdBigScreenSettingDetailBO.Field mqlDefinitionField = new AdBigScreenSettingDetailBO.Field();
        mqlDefinitionField.setName(MQL_DEFINITION_FIELD_NAME);
        mqlDefinitionField.setHidden(false);
        mqlDefinitionField.setCanEdit(false);
        mqlDefinitionField.setType(SELECT_MANY);
        List<AdBigScreenSettingDetailBO.FieldValue> mqlDefinitionFieldValueList = Lists.newArrayList();
        mqlDefinitionField.setFieldValueList(mqlDefinitionFieldValueList);
        resultList.add(mqlDefinitionField);

        AdBigScreenSettingDetailBO.Field sqlDefinitionField = new AdBigScreenSettingDetailBO.Field();
        sqlDefinitionField.setName(SQL_DEFINITION_FIELD_NAME);
        sqlDefinitionField.setHidden(false);
        sqlDefinitionField.setCanEdit(false);
        sqlDefinitionField.setType(SELECT_MANY);
        List<AdBigScreenSettingDetailBO.FieldValue> sqlDefinitionFieldValueList = Lists.newArrayList();
        sqlDefinitionField.setFieldValueList(sqlDefinitionFieldValueList);
        resultList.add(sqlDefinitionField);

        for (LeadStageOptionVO stageOption : marketingEventAnalysisSetting.getMqlDefinition()) {
            AdBigScreenSettingDetailBO.FieldValue mqlFieldValue = new AdBigScreenSettingDetailBO.FieldValue();
            mqlFieldValue.setLabel(stageOption.getLabel());
            mqlFieldValue.setValue(stageOption.getValue());
            mqlFieldValue.setSelected(stageOption.getSelected());
            mqlDefinitionFieldValueList.add(mqlFieldValue);
        }

        for (LeadStageOptionVO stageOption : marketingEventAnalysisSetting.getSqlDefinition()) {
            AdBigScreenSettingDetailBO.FieldValue sqlFieldValue = new AdBigScreenSettingDetailBO.FieldValue();
            sqlFieldValue.setLabel(stageOption.getLabel());
            sqlFieldValue.setValue(stageOption.getValue());
            sqlFieldValue.setSelected(stageOption.getSelected());
            sqlDefinitionFieldValueList.add(sqlFieldValue);
        }
        return resultList;
    }

    public List<LeadStageOptionVO> getLeadStageOption(String ea) {
        ObjectDescribe objectDescribe = crmV2Manager.getCrmDescribeDetail(ea, CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (objectDescribe == null) {
            return Lists.newArrayList();
        }
        FieldDescribe fieldDescribe = objectDescribe.getFields().get("leads_stage");
        List<Map<String, Object>> options = (List<Map<String, Object>>) (fieldDescribe.get("options"));
        List<LeadStageOptionVO> leadStageOptionVOList = Lists.newArrayList();
        for (Map<String, Object> option : options) {
            LeadStageOptionVO fieldValue = new LeadStageOptionVO();
            String label = (String) option.get("label");
            String value = (String) option.get("value");
            fieldValue.setLabel(label);
            fieldValue.setValue(value);
            leadStageOptionVOList.add(fieldValue);
        }
        return leadStageOptionVOList;
    }

    private List<AdBigScreenSettingDetailBO.Field> getAccountInputOutputAnalysisOtherField() {
        List<AdBigScreenSettingDetailBO.Field> result = Lists.newArrayList();
        AdBigScreenSettingDetailBO.Field dimensionField = new AdBigScreenSettingDetailBO.Field();
        dimensionField.setName(DIMENSION_FIELD_NAME);
        dimensionField.setCanEdit(false);
        dimensionField.setHidden(false);
        dimensionField.setType(SELECT_ONE);
        List<AdBigScreenSettingDetailBO.FieldValue> fieldValueList = Lists.newArrayList();

        AdBigScreenSettingDetailBO.FieldValue accountValue = new AdBigScreenSettingDetailBO.FieldValue();
        accountValue.setSelected(true);
        accountValue.setValue(DIMENSION_ACCOUNT);
        fieldValueList.add(accountValue);

        AdBigScreenSettingDetailBO.FieldValue keywordValue = new AdBigScreenSettingDetailBO.FieldValue();
        keywordValue.setSelected(false);
        keywordValue.setValue(DIMENSION_KEYWORD);
        fieldValueList.add(keywordValue);

        AdBigScreenSettingDetailBO.FieldValue campaignValue = new AdBigScreenSettingDetailBO.FieldValue();
        campaignValue.setSelected(false);
        campaignValue.setValue(DIMENSION_CAMPAIGN);
        fieldValueList.add(campaignValue);

        dimensionField.setFieldValueList(fieldValueList);
        result.add(dimensionField);

        AdBigScreenSettingDetailBO.Field topField = new AdBigScreenSettingDetailBO.Field();
        topField.setName(TOP_FIELD_NAME);
        topField.setCanEdit(false);
        topField.setHidden(false);
        topField.setType(SELECT_ONE);
        fieldValueList = Lists.newArrayList();

        AdBigScreenSettingDetailBO.FieldValue topFiveValue = new AdBigScreenSettingDetailBO.FieldValue();
        topFiveValue.setSelected(true);
        topFiveValue.setValue(TOP_FIVE_FIELD_VALUE);
        fieldValueList.add(topFiveValue);

        AdBigScreenSettingDetailBO.FieldValue topTenValue = new AdBigScreenSettingDetailBO.FieldValue();
        topTenValue.setSelected(false);
        topTenValue.setValue(TOP_TEN_FIELD_VALUE);
        fieldValueList.add(topTenValue);

        topField.setFieldValueList(fieldValueList);
        result.add(topField);
        return result;
    }

    private AdBigScreenSettingDetailBO.Setting getDefaultArealDistributionSetting() {
        // 线索地域分布设置
        AdBigScreenSettingDetailBO.Setting arealDistribution = new AdBigScreenSettingDetailBO.Setting();
        arealDistribution.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_776));
        List<AdBigScreenSettingDetailBO.Field> fieldList = Lists.newArrayList();

        AdBigScreenSettingDetailBO.Field field = new AdBigScreenSettingDetailBO.Field();
        field.setName(DIMENSION_FIELD_NAME);
        field.setHidden(false);
        field.setCanEdit(false);
        field.setType(SELECT_ONE);
        List<AdBigScreenSettingDetailBO.FieldValue> fieldValueList = Lists.newArrayList();
        AdBigScreenSettingDetailBO.FieldValue chinaField = new AdBigScreenSettingDetailBO.FieldValue();
        chinaField.setValue(CHINA);
        chinaField.setLabel(PROVINCE_MAP.get(CHINA));
        chinaField.setSelected(true);
        fieldValueList.add(chinaField);
        Set<String> ignoreCitySet = Sets.newHashSet();
        ignoreCitySet.add("beijing");
        ignoreCitySet.add("shanghai");
        ignoreCitySet.add("chongqing");
        ignoreCitySet.add("tianjin");
        ignoreCitySet.add("aomen");
        ignoreCitySet.add("xianggang");
        for (Map.Entry<String, String> entry : PROVINCE_MAP.entrySet()) {
            String code = entry.getKey();
            if (code.equals(CHINA) || ignoreCitySet.contains(code)) {
                continue;
            }
            String value = entry.getValue();
            AdBigScreenSettingDetailBO.FieldValue fieldValue = new AdBigScreenSettingDetailBO.FieldValue();
            fieldValue.setValue(code);
            fieldValue.setLabel(value);
            fieldValue.setSelected(false);
            fieldValueList.add(fieldValue);
        }
        field.setFieldValueList(fieldValueList);
        fieldList.add(field);
        arealDistribution.setFieldList(fieldList);
        return arealDistribution;
    }

    // 获取默认的模块位置
    private List<String> getDefaultModulePositions() {
        return Lists.newArrayList("customerAcquisitionCost", "overView", "acquisitionCustomerConvertFunnel",
                "launchEffectTrendList", "leadsArealDistributions", "leadStageAndSaleSituation",
                "acquisitionCustomerKeywordList", "accountInputOutputAnalysisList", "accountAcquisitionCustomerCompareList");
    }

    // 获取默认时间设置
    private AdBigScreenSettingDetailBO.TimeSetting getDefaultTimeSetting() {
        AdBigScreenSettingDetailBO.TimeSetting timeSetting = new AdBigScreenSettingDetailBO.TimeSetting();
        timeSetting.setTimeRange(BigScreenTimeRangeEnum.THIS_SEASON.getName());
        timeSetting.setCompareTimeType(Y_O_Y);
        TimeRangeResult timeRangeResult = getDateByTimeRangeResultByEnum(BigScreenTimeRangeEnum.THIS_SEASON);
        timeSetting.setBeginTime(timeRangeResult.getBeginTime());
        timeSetting.setEndTime(timeRangeResult.getEndTime());
        timeSetting.setCompareBeginTime(timeRangeResult.getYoYBeginTime());
        timeSetting.setCompareEndTime(timeRangeResult.getYoYEndTime());
        return timeSetting;
    }

    // 获取默认概览设置
    public static AdBigScreenSettingDetailBO.Setting getDefaultOverViewSetting() {
        AdBigScreenSettingDetailBO.Setting overView = new AdBigScreenSettingDetailBO.Setting();
        overView.setName(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_838));
        Class clazz = AdBigScreenResult.OverView.class;
        List<AdBigScreenSettingDetailBO.Field> showFieldList = buildSettingFieldList(clazz);
        overView.setFieldList(showFieldList);
        return overView;
    }

    private AdBigScreenResult.ConvertPeriod buildConvertPeriod(List<AdLeadDataDTO> adLeadDataEntityList, List<AdLeadDataDTO> relativeAdLeadDataEntityList, OpportunityStatisticsDataBO opportunityStatisticsData) {
        AdBigScreenResult.ConvertPeriod convertPeriod = new AdBigScreenResult.ConvertPeriod();
        List<AdLeadDataDTO> hasMqlPeriodLeadList = adLeadDataEntityList.stream().filter(e -> e.getChangedToMqlPeriod() != null && e.getChangedToMqlPeriod() > 0).collect(Collectors.toList());
        convertPeriod.setAvgLeadToMQL(BigDecimal.ZERO);
        convertPeriod.setRelativeLeadToMQL(BigDecimal.ZERO);
        convertPeriod.setAvgLeadMQLToSQL(BigDecimal.ZERO);
        convertPeriod.setRelativeLeadMQLToSQL(BigDecimal.ZERO);
        int convertPeriodScale = 4;
        // 一天的毫秒数
        int dayMills = 1000 * 60 * 60 * 24;
        // 线索转MQL周期
        int hasMqlPeriodLeadCount = hasMqlPeriodLeadList.size();
        if (hasMqlPeriodLeadCount > 0) {
            // 总的转MQL周期 单位为天的毫秒数
            long totalMqlPeriod = hasMqlPeriodLeadList.stream().mapToLong(AdLeadDataDTO::getChangedToMqlPeriod).sum();
            // 线索转MQL平均周期
            BigDecimal avgMqlPeriodMs = BigDecimal.valueOf(totalMqlPeriod).divide(BigDecimal.valueOf(hasMqlPeriodLeadCount), convertPeriodScale, RoundingMode.HALF_UP);
            // 将毫秒转化为天
            BigDecimal avgMqlPeriodDay = avgMqlPeriodMs.divide(BigDecimal.valueOf(dayMills), convertPeriodScale, RoundingMode.HALF_UP);
            if (avgMqlPeriodDay.compareTo(BigDecimal.ONE) > 0) {
                avgMqlPeriodDay = avgMqlPeriodDay.setScale(SCALE, RoundingMode.HALF_UP);
            }
            convertPeriod.setAvgLeadToMQL(avgMqlPeriodDay);
        }
        // 计算环比线索转MQL周期
        List<AdLeadDataDTO> relativeHasMqlPeriodLeadList = relativeAdLeadDataEntityList.stream().filter(e -> e.getChangedToMqlPeriod() != null && e.getChangedToMqlPeriod() > 0).collect(Collectors.toList());
        int relativeHasMqlPeriodLeadCount = relativeHasMqlPeriodLeadList.size();
        if (relativeHasMqlPeriodLeadCount > 0) {
            long relativeTotalMqlPeriod = relativeHasMqlPeriodLeadList.stream().mapToLong(AdLeadDataDTO::getChangedToMqlPeriod).sum();
            // 环比线索抓MQL平均周期  单位为天的毫秒数
            BigDecimal relativeAvgMqlPeriodMs = BigDecimal.valueOf(relativeTotalMqlPeriod).divide(BigDecimal.valueOf(relativeHasMqlPeriodLeadCount), convertPeriodScale, RoundingMode.HALF_UP);
            // 将毫秒转化为天
            BigDecimal relativeAvgMqlPeriodDay = relativeAvgMqlPeriodMs.divide(BigDecimal.valueOf(dayMills), convertPeriodScale, RoundingMode.HALF_UP);
            if (relativeAvgMqlPeriodDay.compareTo(BigDecimal.ONE) > 0) {
                relativeAvgMqlPeriodDay = relativeAvgMqlPeriodDay.setScale(SCALE, RoundingMode.HALF_UP);
            }
            convertPeriod.setRelativeLeadToMQL(relativeAvgMqlPeriodDay);
        }
        // MQL转SQL周期 sum(转换时间-MQL时间) / leadCount
        List<AdLeadDataDTO> transferAndHasMqlTimeLeadList = adLeadDataEntityList.stream().filter(e -> e.getTransformTime() != null && e.getTransformTime() > 0 && e.getChangedToMqlTime() != null && e.getChangedToMqlTime() > 0).collect(Collectors.toList());
        int transferAndHasMqlTimeLeadCount = transferAndHasMqlTimeLeadList.size();
        if (transferAndHasMqlTimeLeadCount > 0) {
            // 单位为天的毫秒数
            long totalMqlToSqlTime = transferAndHasMqlTimeLeadList.stream().mapToLong(e -> e.getTransformTime() - e.getChangedToMqlTime()).sum();
            BigDecimal avgMqlToSqlTimeMs = BigDecimal.valueOf(totalMqlToSqlTime).divide(BigDecimal.valueOf(transferAndHasMqlTimeLeadCount), convertPeriodScale, RoundingMode.HALF_UP);
            // 将毫秒转化为天
            BigDecimal avgMqlToSqlTimeDay = avgMqlToSqlTimeMs.divide(BigDecimal.valueOf(dayMills), convertPeriodScale, RoundingMode.HALF_UP);
            if (avgMqlToSqlTimeDay.compareTo(BigDecimal.ONE) > 0 || avgMqlToSqlTimeDay.compareTo(BigDecimal.ZERO) < 0) {
                avgMqlToSqlTimeDay = avgMqlToSqlTimeDay.setScale(SCALE, RoundingMode.HALF_UP);
            }
            convertPeriod.setAvgLeadMQLToSQL(avgMqlToSqlTimeDay);
        }
        // 计算环比MQL转SQL周期

        List<AdLeadDataDTO> relativeTransferAndHasMqlTimeLeadList = relativeAdLeadDataEntityList.stream().filter(e -> e.getTransformTime() != null && e.getTransformTime() > 0 && e.getChangedToMqlTime() != null && e.getChangedToMqlTime() > 0).collect(Collectors.toList());
        int relativeTransferAndHasMqlTimeLeadCount = relativeTransferAndHasMqlTimeLeadList.size();
        if (relativeTransferAndHasMqlTimeLeadCount > 0) {
            // 单位为天的毫秒数
            long totalMqlToSqlTime = relativeTransferAndHasMqlTimeLeadList.stream().mapToLong(e -> e.getTransformTime() - e.getChangedToMqlTime()).sum();
            BigDecimal avgMqlToSqlTimeMs = BigDecimal.valueOf(totalMqlToSqlTime).divide(BigDecimal.valueOf(relativeTransferAndHasMqlTimeLeadCount), convertPeriodScale, RoundingMode.HALF_UP);
            // 将毫秒转化为天
            BigDecimal avgMqlToSqlTimeDay = avgMqlToSqlTimeMs.divide(BigDecimal.valueOf(dayMills), convertPeriodScale, RoundingMode.HALF_UP);
            if (avgMqlToSqlTimeDay.compareTo(BigDecimal.ONE) >= 0 || avgMqlToSqlTimeDay.compareTo(BigDecimal.ZERO) < 0) {
                avgMqlToSqlTimeDay = avgMqlToSqlTimeDay.setScale(SCALE, RoundingMode.HALF_UP);
            }
            convertPeriod.setRelativeLeadMQLToSQL(avgMqlToSqlTimeDay);
        }
        // SQL转赢单商机周期 = 商机阶段状态等于赢单时的阶段变更时间 - 源线索转化时间）均值
        // 单位为天的毫秒数
        BigDecimal avgSQLToWinOpportunityMs = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(opportunityStatisticsData.getLeadIdToWinOpporSet())) {
            BigDecimal totalTransferTime = adLeadDataEntityList.stream().filter(e -> e.getTransformTime() != null && opportunityStatisticsData.getLeadIdToWinOpporSet().contains(e.getLeadId()))
                    .map(e -> BigDecimal.valueOf(e.getTransformTime())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalStageChangedTime = opportunityStatisticsData.getWinOpporTotalStgChangedTime();
            BigDecimal sub = totalStageChangedTime.subtract(totalTransferTime);
            avgSQLToWinOpportunityMs = sub.divide(BigDecimal.valueOf(opportunityStatisticsData.getLeadIdToWinOpporSet().size()), convertPeriodScale, RoundingMode.HALF_UP);
        }
        // 将毫秒转化为天
        BigDecimal avgSQLToWinOpportunityDay = avgSQLToWinOpportunityMs.divide(BigDecimal.valueOf(dayMills), convertPeriodScale, RoundingMode.HALF_UP);
        if (avgSQLToWinOpportunityDay.compareTo(BigDecimal.ONE) > 0 || avgSQLToWinOpportunityDay.compareTo(BigDecimal.ZERO) < 0) {
            avgSQLToWinOpportunityDay = avgSQLToWinOpportunityDay.setScale(SCALE, RoundingMode.HALF_UP);
        }
        convertPeriod.setAvgSQLToWinOpportunity(avgSQLToWinOpportunityDay);
        // 计算环比 SQL转赢单商机周期
        // 单位为天的毫秒数
        BigDecimal relativeAvgSQLToWinOpportunityMs = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(opportunityStatisticsData.getRelativeLeadIdToWinOpporSet())) {
            BigDecimal relativeTotalTransferTime = relativeAdLeadDataEntityList.stream().filter(e -> e.getTransformTime() != null && opportunityStatisticsData.getRelativeLeadIdToWinOpporSet().contains(e.getLeadId()))
                    .map(e -> BigDecimal.valueOf(e.getTransformTime())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal relativeTotalStageChangedTime = opportunityStatisticsData.getRelativeWinOpporTotalStgChangedTime();
            BigDecimal sub = relativeTotalStageChangedTime.subtract(relativeTotalTransferTime);
            relativeAvgSQLToWinOpportunityMs = sub.divide(BigDecimal.valueOf(opportunityStatisticsData.getRelativeLeadIdToWinOpporSet().size()), convertPeriodScale, RoundingMode.HALF_UP);
        }
        BigDecimal relativeAvgSQLToWinOpportunity = relativeAvgSQLToWinOpportunityMs.divide(BigDecimal.valueOf(dayMills), convertPeriodScale, RoundingMode.HALF_UP);
        if (relativeAvgSQLToWinOpportunity.compareTo(BigDecimal.ONE) > 0 || relativeAvgSQLToWinOpportunity.compareTo(BigDecimal.ZERO) < 0) {
            relativeAvgSQLToWinOpportunity = relativeAvgSQLToWinOpportunity.setScale(SCALE, RoundingMode.HALF_UP);
        }
        convertPeriod.setRelativeSQLToWinOpportunity(relativeAvgSQLToWinOpportunity);

        BigDecimal leadToWinOpportunity = convertPeriod.getAvgLeadToMQL().add(convertPeriod.getAvgLeadMQLToSQL()).add(convertPeriod.getAvgSQLToWinOpportunity());
        convertPeriod.setLeadToWinOpportunity(leadToWinOpportunity.setScale(SCALE, RoundingMode.HALF_UP));

        BigDecimal relativeLeadToWinOpportunity = convertPeriod.getRelativeLeadToMQL().add(convertPeriod.getRelativeLeadMQLToSQL()).add(convertPeriod.getRelativeSQLToWinOpportunity());
        convertPeriod.setRelativeLeadToWinOpportunity(relativeLeadToWinOpportunity.setScale(SCALE, RoundingMode.HALF_UP));
        return convertPeriod;
    }

    private List<AdBigScreenResult.LaunchEffectTrend> buildLaunchEffectTrend(String ea, List<AdLeadDataDTO> adLeadDataEntityList, List<AdCampaignDataStatisticsBO> campaignDataList, TimeRangeResult timeRangeResult) {
        List<AdBigScreenResult.LaunchEffectTrend> result = Lists.newArrayList();
        // 只取过去6个月的数据
        int monthAgo = 5;
        Date endTime = timeRangeResult.getEndTime();
        Date beginTime = timeRangeResult.getBeginTime();
        int endTimeMonth = DateUtil.getMonth(endTime);
        int endTimeYear = DateUtil.getYear(endTime);
        List<Integer> monthList = getLastSixMonthList(endTime);
        // ----- 构造消费的趋势数据开始-----
        AdBigScreenResult.LaunchEffectTrend costTrend = new AdBigScreenResult.LaunchEffectTrend();
        costTrend.setObjName("COST");
        List<AdBigScreenResult.MonthDataTrend> costDataList = Lists.newArrayList();
        costTrend.setDataList(costDataList);
        // 根据月份分组
        Map<Integer, List<AdCampaignDataStatisticsBO>> costMonthDataMap = campaignDataList.stream().filter(e -> e.getActionDate() != null)
                .collect(Collectors.groupingBy(e -> Integer.parseInt(DateUtil.format("MM", e.getActionDate()))));

        for (Integer month : monthList) {
            AdBigScreenResult.MonthDataTrend monthDataTrend = new AdBigScreenResult.MonthDataTrend();
            monthDataTrend.setMonth(month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972));
            monthDataTrend.setCount(BigDecimal.ZERO);
            costDataList.add(monthDataTrend);
            List<AdCampaignDataStatisticsBO> list = costMonthDataMap.get(month);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            if (month == 1 && endTimeMonth - monthAgo != 1) {
                monthDataTrend.setMonth(endTimeYear + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_980) + monthDataTrend.getMonth());
            }
            BigDecimal totalCost = BigDecimal.ZERO;
            for (AdCampaignDataStatisticsBO adCampaignDataStatisticsBO : list) {
                if (adCampaignDataStatisticsBO.getTotalCost() != null) {
                    totalCost = totalCost.add(adCampaignDataStatisticsBO.getTotalCost());
                }
            }
            monthDataTrend.setCount(totalCost.setScale(SCALE, RoundingMode.HALF_UP));
        }
        result.add(costTrend);
        // ----- 构造消费的趋势数据结束-----

        // ----- 构造线索的趋势数据开始-----

        AdBigScreenResult.LaunchEffectTrend leadTrend = new AdBigScreenResult.LaunchEffectTrend();
        leadTrend.setObjName("LEADS");
        List<AdBigScreenResult.MonthDataTrend> leadDataList = Lists.newArrayList();
        leadTrend.setDataList(leadDataList);
        Map<Integer, Long> leadMonthDataMap = adLeadDataEntityList.stream().collect(Collectors.groupingBy(e -> Integer.parseInt(DateUtil.format("MM", e.getLeadCreateTime())), Collectors.counting()));
        for (Integer month : monthList) {
            long leadCount = leadMonthDataMap.getOrDefault(month, 0L);
            AdBigScreenResult.MonthDataTrend monthDataTrend = new AdBigScreenResult.MonthDataTrend();
            monthDataTrend.setMonth(month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972));
            if (month == 1 && endTimeMonth - monthAgo != 1) {
                monthDataTrend.setMonth(endTimeYear + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_980) + monthDataTrend.getMonth());
            }
            monthDataTrend.setCount(BigDecimal.valueOf(leadCount));
            leadDataList.add(monthDataTrend);
        }
        result.add(leadTrend);
        // ----- 构造线索的趋势数据结束-----

        //  ----- 构造微信粉丝和企微客户的趋势数据开始-----

        AdBigScreenSettingEntity adBigScreenSettingEntity = getBigScreenSetting(ea);
        AdBigScreenSettingDetailBO settingDetailBO = JSONObject.parseObject(adBigScreenSettingEntity.getSetting(), AdBigScreenSettingDetailBO.class);
        AdBigScreenSettingDetailBO.Setting launchEffectTrendSetting = settingDetailBO.getLaunchEffectTrend();
        boolean showWechatFan = launchEffectTrendSetting.getFieldList().stream().anyMatch(e -> "WECHAT_FANS".equals(e.getName()) && !e.isHidden());
        boolean showQywxExternal = launchEffectTrendSetting.getFieldList().stream().anyMatch(e -> "QYWX_EXTERNAL_CUSTOMER".equals(e.getName()) && !e.isHidden());

        if (!showWechatFan && !showQywxExternal) {
            return result;
        }

        // 逻辑： 先获取所有广告的市场活动，根据市场活动查询营销通营销推广记录，在根据营销推广记录查询微信粉丝和企微客户对象
        List<String> marketingEventIdList = new ArrayList<>();

        // 根据市场活动查询营销推广记录
        List<String> marketingPromotionIdList = Lists.newArrayList();
        List<String> selectFields = Lists.newArrayList("_id");
        int pageSize = 2000;
        List<List<String>> partitions = Lists.partition(marketingEventIdList, pageSize);
        for (List<String> partition : partitions) {
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName());
            queryFilterArg.setSelectFields(selectFields);
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("marketing_event_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), partition);
            queryFilterArg.setQuery(paasQueryArg);

            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            int totalPage = (totalCount - 1) / pageSize + 1;
            for (int i = 1; i <= totalPage; i++) {
                InnerPage<ObjectData> objectDataPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, i, pageSize);
                if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
                    objectDataPage.getDataList().forEach(e -> marketingPromotionIdList.add(e.getId()));
                }
            }
        }

        // 根据营销推广来源查询微信粉丝和企业微信客户对象
        selectFields = Lists.newArrayList("_id", "create_time");
        partitions = Lists.partition(marketingPromotionIdList, 2000);
        List<ObjectData> wechatFanObjectDataList = Lists.newArrayList();
        List<ObjectData> qywxExternalObjectDataList = Lists.newArrayList();
        for (List<String> partition : partitions) {
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT.getName());
            queryFilterArg.setSelectFields(selectFields);
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("marketing_promotion_source_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), partition);
            paasQueryArg.addFilter("create_time", PaasAndCrmOperatorEnum.GTE.getCrmOperator(), Lists.newArrayList(String.valueOf(beginTime.getTime())));
            paasQueryArg.addFilter("create_time", PaasAndCrmOperatorEnum.LTE.getCrmOperator(), Lists.newArrayList(String.valueOf(endTime.getTime())));
            queryFilterArg.setQuery(paasQueryArg);

            if (showWechatFan) {
                int wechatFanCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
                int totalPage = (wechatFanCount - 1) / pageSize + 1;
                for (int i = 1; i <= totalPage; i++) {
                    InnerPage<ObjectData> objectDataPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, i, pageSize);
                    if (objectDataPage == null || CollectionUtils.isEmpty(objectDataPage.getDataList())) {
                        break;
                    }
                    wechatFanObjectDataList.addAll(objectDataPage.getDataList());
                }
            }

            if (showQywxExternal) {
                queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                int qywxExternalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
                int totalPage = (qywxExternalCount - 1) / pageSize + 1;
                for (int i = 1; i <= totalPage; i++) {
                    InnerPage<ObjectData> objectDataPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, i, pageSize);
                    if (objectDataPage == null || CollectionUtils.isEmpty(objectDataPage.getDataList())) {
                        break;
                    }
                    qywxExternalObjectDataList.addAll(objectDataPage.getDataList());
                }
            }
        }
        // 根据月份分组统计微信粉丝数量
        Map<Integer, Long> wechatFanceMonthTrendData = wechatFanObjectDataList.stream().filter(e -> e.getCreateTime() != null)
                .collect(Collectors.groupingBy(e -> DateUtil.getMonth(DateUtil.fromTimestamp(e.getCreateTime())), Collectors.counting()));
        // 根据月份分组统计微信粉丝数量
        Map<Integer, Long> qywxExternalMonthTrendData = qywxExternalObjectDataList.stream().filter(e -> e.getCreateTime() != null)
                .collect(Collectors.groupingBy(e -> DateUtil.getMonth(DateUtil.fromTimestamp(e.getCreateTime())), Collectors.counting()));

        AdBigScreenResult.LaunchEffectTrend wechatFanTrend = new AdBigScreenResult.LaunchEffectTrend();
        wechatFanTrend.setObjName("WECHAT_FANS");
        List<AdBigScreenResult.MonthDataTrend> wechatFanDataList = Lists.newArrayList();
        wechatFanTrend.setDataList(wechatFanDataList);
        result.add(wechatFanTrend);

        AdBigScreenResult.LaunchEffectTrend qywxExternalTrend = new AdBigScreenResult.LaunchEffectTrend();
        qywxExternalTrend.setObjName("QYWX_EXTERNAL_CUSTOMER");
        List<AdBigScreenResult.MonthDataTrend> qywxExternalDataList = Lists.newArrayList();
        qywxExternalTrend.setDataList(qywxExternalDataList);
        result.add(qywxExternalTrend);

        for (Integer month : monthList) {
            long wechatFanceCount = wechatFanceMonthTrendData.getOrDefault(month, 0L);
            AdBigScreenResult.MonthDataTrend monthDataTrend = new AdBigScreenResult.MonthDataTrend();
            monthDataTrend.setMonth(month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972));
            if (month == 1 && endTimeMonth - monthAgo != 1) {
                monthDataTrend.setMonth(endTimeYear + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_980) + monthDataTrend.getMonth());
            }
            monthDataTrend.setCount(BigDecimal.valueOf(wechatFanceCount));
            wechatFanDataList.add(monthDataTrend);

            long qywxExternalCount = qywxExternalMonthTrendData.getOrDefault(month, 0L);
            AdBigScreenResult.MonthDataTrend qywxExternalMonthDataTrend = new AdBigScreenResult.MonthDataTrend();
            qywxExternalMonthDataTrend.setMonth(month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972));
            if (month == 1 && endTimeMonth - monthAgo != 1) {
                qywxExternalMonthDataTrend.setMonth(endTimeYear + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_980) + qywxExternalMonthDataTrend.getMonth());
            }
            qywxExternalMonthDataTrend.setCount(BigDecimal.valueOf(qywxExternalCount));
            qywxExternalDataList.add(qywxExternalMonthDataTrend);
        }
        //  ----- 构造微信粉丝和企微客户的趋势数据结束-----
        return result;
    }

    private List<AdBigScreenResult.AccountAcquisitionCustomerCompare> buildAccountAcquisitionCustomerCompare(List<AdLeadDataDTO> adLeadDataEntityList, List<AdAccountEntity> adAccountEntityList, TimeRangeResult timeRangeResult, AdBigScreenSettingDetailBO settingDetailBO) {
        // 只取过去6个月的数据
        int monthAgo = 5;
        Date endTime = timeRangeResult.getEndTime();
        int endTimeMonth = DateUtil.getMonth(endTime);
        int endTimeYear = DateUtil.getYear(endTime);
        List<Integer> monthList = getLastSixMonthList(endTime);

        AdBigScreenSettingDetailBO.Setting accountAcquisitionCustomerCompareSetting = settingDetailBO.getAccountAcquisitionCustomerCompare();

        Set<String> adAccountIdSet = accountAcquisitionCustomerCompareSetting.getFieldList().stream().filter(e -> !e.isHidden() && StringUtils.isNotBlank(e.getId()))
                .map(AdBigScreenSettingDetailBO.Field::getId).collect(Collectors.toSet());

        // 只获取配置的广告账户数据
        List<AdLeadDataDTO> filterLeadDataEntityList = adLeadDataEntityList.stream().filter(e -> adAccountIdSet.contains(e.getAdAccountId())).collect(Collectors.toList());
        List<AdAccountEntity> filterAdAccountEntityList = adAccountEntityList.stream().filter(e -> adAccountIdSet.contains(e.getId())).collect(Collectors.toList());

        List<AdBigScreenResult.AccountAcquisitionCustomerCompare> accountAcquisitionCustomerCompareList = Lists.newArrayList();

        // 线索数据按月份分组
        Map<Integer, List<AdLeadDataDTO>> monthDataMap = filterLeadDataEntityList.stream().collect(Collectors.groupingBy(e -> Integer.parseInt(DateUtil.format("MM", e.getLeadCreateTime()))));

        for (Integer month : monthList) {
            AdBigScreenResult.AccountAcquisitionCustomerCompare accountAcquisitionCustomerCompare = new AdBigScreenResult.AccountAcquisitionCustomerCompare();
            List<AdBigScreenResult.AccountDataHistogram> dataList = Lists.newArrayList();
            accountAcquisitionCustomerCompare.setDataList(dataList);
            accountAcquisitionCustomerCompare.setMonth(month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972));
            if (month == 1 && endTimeMonth - monthAgo != 1) {
                accountAcquisitionCustomerCompare.setMonth(endTimeYear + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_980) + accountAcquisitionCustomerCompare.getMonth());
            }
            accountAcquisitionCustomerCompareList.add(accountAcquisitionCustomerCompare);

            List<AdLeadDataDTO> adDataList = monthDataMap.getOrDefault(month, Lists.newArrayList());

            // 计算这个月下面每个账户的线索数量
            Map<String, Long> accountIdToLeadMap = adDataList.stream().collect(Collectors.groupingBy(AdLeadDataDTO::getAdAccountId, Collectors.counting()));
            for (AdAccountEntity adAccountEntity : filterAdAccountEntityList) {
                long leadCount = accountIdToLeadMap.getOrDefault(adAccountEntity.getId(), 0L);
                AdBigScreenResult.AccountDataHistogram accountDataHistogram = new AdBigScreenResult.AccountDataHistogram();
                accountDataHistogram.setAccountName(adAccountEntity.getUsername());
                accountDataHistogram.setCount(BigDecimal.valueOf(leadCount));
                dataList.add(accountDataHistogram);
            }
        }
        return accountAcquisitionCustomerCompareList;
    }

    private List<Integer> getLastSixMonthList(Date endTime) {
        // 只取过去6个月的数据
        List<Integer> monthList = Lists.newArrayList();
        for (int i = 5; i >= 0; i--) {
            monthList.add(DateUtil.getMonth(DateUtil.plusMonth(endTime, -i)));
        }
        return monthList;
    }

    private AdBigScreenResult.AcquisitionCustomerConvertFunnel buildAcquisitionCustomerConvertFunnel(OpportunityStatisticsDataBO opportunityStatisticsData, AdCampaignDataStatisticsBO allStatisticsData,
                                                                                                     List<AdLeadDataDTO> adLeadDataEntityList, AdBigScreenSettingDetailBO settingDetailBO) {
        AdBigScreenResult.AcquisitionCustomerConvertFunnel acquisitionCustomerConvertFunnel = new AdBigScreenResult.AcquisitionCustomerConvertFunnel();
        acquisitionCustomerConvertFunnel.setPv(allStatisticsData.getTotalPv());
        acquisitionCustomerConvertFunnel.setClicks(allStatisticsData.getTotalClick());
        acquisitionCustomerConvertFunnel.setLeadsCount(BigDecimal.valueOf(adLeadDataEntityList.size()));
        // 哪些线索阶段对应成MQL
        Set<String> mqlStageDefiniteSet = getLeadStageToMqlDefiniteSet(settingDetailBO);
        // 哪些线索阶段对应成SQL
        Set<String> sqlStageDefiniteSet = getLeadStageToSqlDefiniteSet(settingDetailBO);
        // mql线索数量
        long mqlCount = adLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && mqlStageDefiniteSet.contains(e.getLeadsStage())).count();
        // sql线索数量
        long sqlCount = adLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && sqlStageDefiniteSet.contains(e.getLeadsStage())).count();
        acquisitionCustomerConvertFunnel.setLeadsMQLCount(BigDecimal.valueOf(mqlCount));
        acquisitionCustomerConvertFunnel.setLeadsSQLCount(BigDecimal.valueOf(sqlCount));
        //计算商机数量和赢单商机数量 这里就不要判断是否展示这两个字段了  广告获客成本要用到这两个字段
        BigDecimal opportunityCount = opportunityStatisticsData.getOpportunityCount();
        BigDecimal winOpportunityCount = opportunityStatisticsData.getWinOpportunityCount();

        acquisitionCustomerConvertFunnel.setOpportunityCount(opportunityCount);
        acquisitionCustomerConvertFunnel.setWinOpportunityCount(winOpportunityCount);
        // 计算 ctr = 点击数/展现数 * 100%
        BigDecimal ctr = BigDecimal.ZERO;
        BigDecimal pv = acquisitionCustomerConvertFunnel.getPv();
        if (pv.compareTo(BigDecimal.ZERO) > 0) {
            ctr = acquisitionCustomerConvertFunnel.getClicks().divide(pv, RADIO_SCALE, RoundingMode.HALF_UP).multiply(HUNDRED);
        }
        acquisitionCustomerConvertFunnel.setCtr(ctr);
        // 转线索率 = 线索数/点击数 * 100%
        BigDecimal leadsCount = acquisitionCustomerConvertFunnel.getLeadsCount();
        BigDecimal convertLeadRate = BigDecimal.ZERO;
        BigDecimal clicks = acquisitionCustomerConvertFunnel.getClicks();
        if (clicks.compareTo(BigDecimal.ZERO) > 0) {
            convertLeadRate = leadsCount.divide(clicks, RADIO_SCALE, RoundingMode.HALF_UP).multiply(HUNDRED);
        }
        acquisitionCustomerConvertFunnel.setConvertLeadRate(convertLeadRate);
        // 转MQL率 = MQL数/线索数 * 100%
        BigDecimal transferMQLRate = BigDecimal.ZERO;
        BigDecimal leadMqlCount = acquisitionCustomerConvertFunnel.getLeadsMQLCount();
        if (leadsCount.compareTo(BigDecimal.ZERO) > 0) {
            transferMQLRate = leadMqlCount.divide(leadsCount, RADIO_SCALE, RoundingMode.HALF_UP).multiply(HUNDRED);
        }
        acquisitionCustomerConvertFunnel.setTransferMQLRate(transferMQLRate);
        // 转SQL率 = SQL数/MQL数 * 100%
        BigDecimal transferSQLRate = BigDecimal.ZERO;
        BigDecimal leadSqlCount = acquisitionCustomerConvertFunnel.getLeadsSQLCount();
        if (leadMqlCount.compareTo(BigDecimal.ZERO) > 0) {
            transferSQLRate = leadSqlCount.divide(leadMqlCount, RADIO_SCALE, RoundingMode.HALF_UP).multiply(HUNDRED);
        }
        acquisitionCustomerConvertFunnel.setTransferSQLRate(transferSQLRate);
        // 转商机率 = 商机数/SQL数 * 100%
        BigDecimal transferOpportunityRate = BigDecimal.ZERO;
        if (leadSqlCount.compareTo(BigDecimal.ZERO) > 0) {
            transferOpportunityRate = opportunityCount.divide(leadSqlCount, RADIO_SCALE, RoundingMode.HALF_UP).multiply(HUNDRED);
        }
        acquisitionCustomerConvertFunnel.setTransferOpportunityRate(transferOpportunityRate);
        // 转赢单商机率 = 赢单商机数 / 商机数 * 100%
        BigDecimal transferWinOpportunityRate = BigDecimal.ZERO;
        if (opportunityCount.compareTo(BigDecimal.ZERO) > 0) {
            transferWinOpportunityRate = winOpportunityCount.divide(opportunityCount, RADIO_SCALE, RoundingMode.HALF_UP).multiply(HUNDRED);
        }
        acquisitionCustomerConvertFunnel.setTransferWinOpportunityRate(transferWinOpportunityRate);
        return acquisitionCustomerConvertFunnel;
    }

    private Set<String> getLeadStageToMqlDefiniteSet(AdBigScreenSettingDetailBO settingDetailBO) {
        // 哪些线索阶段对应成MQL
        Set<String> mqlStageDefiniteSet = Sets.newHashSet();
        AdBigScreenSettingDetailBO.Setting acquisitionCustomerConvertFunnelSetting = settingDetailBO.getAcquisitionCustomerConvertFunnel();
        for (AdBigScreenSettingDetailBO.Field field : acquisitionCustomerConvertFunnelSetting.getFieldList()) {
            if (MQL_DEFINITION_FIELD_NAME.equals(field.getName())) {
                field.getFieldValueList().stream().filter(e -> BooleanUtils.isTrue(e.getSelected())).forEach(e -> mqlStageDefiniteSet.add(e.getValue()));
            }
        }
        return mqlStageDefiniteSet;
    }

    private Set<String> getLeadStageToSqlDefiniteSet(AdBigScreenSettingDetailBO settingDetailBO) {
        // 哪些线索阶段对应成SQL
        Set<String> sqlStageDefiniteSet = Sets.newHashSet();
        AdBigScreenSettingDetailBO.Setting acquisitionCustomerConvertFunnelSetting = settingDetailBO.getAcquisitionCustomerConvertFunnel();
        for (AdBigScreenSettingDetailBO.Field field : acquisitionCustomerConvertFunnelSetting.getFieldList()) {
            if (SQL_DEFINITION_FIELD_NAME.equals(field.getName())) {
                field.getFieldValueList().stream().filter(e -> BooleanUtils.isTrue(e.getSelected())).forEach(e -> sqlStageDefiniteSet.add(e.getValue()));
            }
        }
        return sqlStageDefiniteSet;
    }

    private List<AdBigScreenResult.AccountInputOutputAnalysis> buildAccountInputOutputAnalysis(String ea, AdBigScreenSettingDetailBO settingDetailBO, List<AdAccountEntity> adAccountEntityList, List<AdCampaignDataStatisticsBO> campaignDataList, List<AdLeadDataDTO> adLeadDataEntityList,
                                                                                               List<AdLeadDataDTO> relativeAdLeadDataEntityList, AdBigScreenResult.AcquisitionCustomerConvertFunnel acquisitionCustomerConvertFunnel, TimePeriod timePeriod, OpportunityStatisticsDataBO opportunityStatisticsData) {
        String showDimension = DIMENSION_ACCOUNT;
        int top = 10;
        for (AdBigScreenSettingDetailBO.Field field : settingDetailBO.getAccountInputOutputAnalysis().getFieldList()) {
            String fieldName = field.getName();
            List<AdBigScreenSettingDetailBO.FieldValue> fieldValueList = field.getFieldValueList();
            if (CollectionUtils.isEmpty(fieldValueList)) {
                continue;
            }
            Optional<AdBigScreenSettingDetailBO.FieldValue> optional = fieldValueList.stream().filter(e -> BooleanUtils.isTrue(e.getSelected())).findFirst();
            if (optional.isPresent()) {
                String value = optional.get().getValue();
                if (fieldName.equals(DIMENSION_FIELD_NAME)) {
                    showDimension = value;
                } else if (fieldName.equals(TOP_FIELD_NAME)) {
                    top = Integer.parseInt(value);
                }
            }
        }
        switch (showDimension) {
            case DIMENSION_ACCOUNT:
                List<AdBigScreenResult.AccountInputOutputAnalysis> result = getAccountInputOutputAnalyses(adAccountEntityList, campaignDataList, adLeadDataEntityList, acquisitionCustomerConvertFunnel);
                if (result.size() > top) {
                    result = Lists.newArrayList(result.subList(0, top));
                }
                return result;
            case DIMENSION_CAMPAIGN:
                return getCampaignInputOutputAnalyses(ea, top, timePeriod, adLeadDataEntityList, relativeAdLeadDataEntityList, settingDetailBO, opportunityStatisticsData);
            case DIMENSION_KEYWORD:
                return getKeywordInputOutputAnalyses(ea, top, timePeriod, adLeadDataEntityList, relativeAdLeadDataEntityList, settingDetailBO, opportunityStatisticsData);
        }

        return Lists.newArrayList();
    }

    private List<AdBigScreenResult.AccountInputOutputAnalysis> getKeywordInputOutputAnalyses(String ea, int top, TimePeriod timePeriod, List<AdLeadDataDTO> adLeadDataEntityList,
                                                                                             List<AdLeadDataDTO> relativeAdLeadDataEntityList, AdBigScreenSettingDetailBO settingDetailBO, OpportunityStatisticsDataBO opportunityStatisticsData) {
        List<String> keywordIdList = adLeadDataEntityList.stream().map(AdLeadDataDTO::getKeywordId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return Lists.newArrayList();
        }

        Map<String, Integer> keywordToLeadCountMap = Maps.newHashMap();
        for (AdLeadDataDTO adLeadDataEntity : adLeadDataEntityList) {
            if (StringUtils.isNotBlank(adLeadDataEntity.getKeywordId())) {
                int leadCount = keywordToLeadCountMap.computeIfAbsent(adLeadDataEntity.getKeywordId(), k -> 0);
                keywordToLeadCountMap.put(adLeadDataEntity.getKeywordId(), ++leadCount);
            }
        }
        // 根据线索数量倒叙排序一下
        Map<String, Integer> sortedMap = Maps.newLinkedHashMap();
        keywordToLeadCountMap.entrySet().stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(top).forEachOrdered(e -> sortedMap.put(e.getKey(), e.getValue()));

        keywordIdList = Lists.newArrayList(sortedMap.keySet());
        Map<String, KeywordStatisticsData> keywordStatisticsMap = getKeywordStatisticsMap(ea, keywordIdList, timePeriod.getBeginTime(), timePeriod.getEndTime());
        Map<String, KeywordStatisticsData> relativeKeywordStatisticsMap = getKeywordStatisticsMap(ea, keywordIdList, timePeriod.getRelativeBeginTime(), timePeriod.getRelativeEndTime());

        List<String> sortedKeywordIdList = keywordIdList;
        // 哪些线索阶段对应成MQL
        Set<String> mqlStageDefiniteSet = getLeadStageToMqlDefiniteSet(settingDetailBO);
        // 哪些线索阶段对应成SQL
        Set<String> sqlStageDefiniteSet = getLeadStageToSqlDefiniteSet(settingDetailBO);
        if (CollectionUtils.isEmpty(sortedKeywordIdList)) {
            return Lists.newArrayList();
        }

        List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID,
                CrmObjectApiNameEnum.MARKETING_KEYWORD.getName(), Lists.newArrayList("_id", "name"), keywordIdList);
        Map<String, String> keywordIdToNameMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2) -> v1));

        List<AdBigScreenResult.AccountInputOutputAnalysis> resultList = Lists.newArrayList();
        for (String keywordId : sortedKeywordIdList) {
            KeywordStatisticsData keywordStatisticsData = keywordStatisticsMap.getOrDefault(keywordId, new KeywordStatisticsData());
            KeywordStatisticsData relativeKeywordStatisticsData = relativeKeywordStatisticsMap.getOrDefault(keywordId, new KeywordStatisticsData());
            BigDecimal totalCost = keywordStatisticsData.getTotalCost();
            AdBigScreenResult.AccountInputOutputAnalysis result = new AdBigScreenResult.AccountInputOutputAnalysis();
            result.setDimension(DIMENSION_KEYWORD);
            result.setDimensionValue(keywordIdToNameMap.getOrDefault(keywordId, ""));
            // 消费
            result.setCost(totalCost.setScale(SCALE, RoundingMode.HALF_UP));
            BigDecimal relativeTotalCost = relativeKeywordStatisticsData.getTotalCost();
            result.setRelativeCost(relativeTotalCost);
            // 线索数量
            List<AdLeadDataDTO> adLeadDataDTOList = adLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()) && e.getKeywordId().equals(keywordId))
                    .collect(Collectors.toList());
            List<AdLeadDataDTO> relativeAdLeadDataDTOList = relativeAdLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()) && e.getKeywordId().equals(keywordId))
                    .collect(Collectors.toList());
            result.setLeadsCount(BigDecimal.valueOf(adLeadDataDTOList.size()));
            result.setRelativeLeadsCount(BigDecimal.valueOf(relativeAdLeadDataDTOList.size()));
            // mql线索数量
            long mqlCount = adLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && mqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setLeadsMQLCount(BigDecimal.valueOf(mqlCount));
            long relativeMqlCount = relativeAdLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && mqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setRelativeLeadsMQLCount(BigDecimal.valueOf(relativeMqlCount));
            // sql线索数量
            long sqlCount = adLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && sqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setLeadsSQLCount(BigDecimal.valueOf(sqlCount));
            long relativeSqlCount = relativeAdLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && sqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setRelativeLeadsSQLCount(BigDecimal.valueOf(relativeSqlCount));
            // 商机数量
            BigDecimal opportunityCount = opportunityStatisticsData.getOpportunityCount();
            result.setOpportunityCount(opportunityCount);
            BigDecimal relativeOpportunityCount = opportunityStatisticsData.getRelativeOpportunityCount();
            result.setRelativeOpportunityCount(relativeOpportunityCount);
            // 平均点击单价
            BigDecimal totalClick = keywordStatisticsData.getTotalClick();
            BigDecimal avgClickPrice = totalClick.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(totalClick, SCALE, RoundingMode.HALF_UP);
            result.setAvgClickPrice(avgClickPrice);
            BigDecimal relativeTotalClick = relativeKeywordStatisticsData.getTotalClick() == null ? BigDecimal.ZERO : relativeKeywordStatisticsData.getTotalClick();
            BigDecimal relativeAvgClickPrice = relativeTotalClick.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(relativeTotalClick, SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgClickPrice(relativeAvgClickPrice);
            // 平均线索点击单价
            BigDecimal avgLeadAcquisitionPrice = BigDecimal.ZERO;
            if (result.getLeadsCount().compareTo(BigDecimal.ZERO) != 0) {
                avgLeadAcquisitionPrice = totalCost.divide(result.getLeadsCount(), SCALE, RoundingMode.HALF_UP);
            }
            result.setAvgLeadAcquisitionPrice(avgLeadAcquisitionPrice);
            BigDecimal relativeAvgLeadAcquisitionPrice = BigDecimal.ZERO;
            if (result.getRelativeLeadsCount().compareTo(BigDecimal.ZERO) != 0) {
                relativeAvgLeadAcquisitionPrice = relativeTotalCost.divide(result.getRelativeLeadsCount(), SCALE, RoundingMode.HALF_UP);
            }
            result.setRelativeAvgLeadAcquisitionPrice(relativeAvgLeadAcquisitionPrice);
            // 平均MQL获取单价
            BigDecimal avgMQLAcquisitionPrice = result.getLeadsMQLCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(result.getLeadsMQLCount(), SCALE, RoundingMode.HALF_UP);
            result.setAvgMQLAcquisitionPrice(avgMQLAcquisitionPrice);

            BigDecimal relativeAvgMQLAcquisitionPrice = result.getRelativeLeadsCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(result.getRelativeLeadsCount(), SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgMQLAcquisitionPrice(relativeAvgMQLAcquisitionPrice);
            // 平均SQL获取单价
            BigDecimal avgSQLAcquisitionPrice = result.getLeadsSQLCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(result.getLeadsSQLCount(), SCALE, RoundingMode.HALF_UP);
            result.setAvgSQLAcquisitionPrice(avgSQLAcquisitionPrice);
            BigDecimal relativeAvgSQLAcquisitionPrice = result.getRelativeLeadsSQLCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(result.getRelativeLeadsSQLCount(), SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgSQLAcquisitionPrice(relativeAvgSQLAcquisitionPrice);
            // 平均商机获取单价
            BigDecimal avgOpportunityAcquisitionPrice = result.getOpportunityCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(result.getOpportunityCount(), SCALE, RoundingMode.HALF_UP);
            result.setAvgOpportunityAcquisitionPrice(avgOpportunityAcquisitionPrice);
            BigDecimal relativeAvgOpportunityAcquisitionPrice = result.getRelativeOpportunityCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(result.getRelativeOpportunityCount(), SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgOpportunityAcquisitionPrice(relativeAvgOpportunityAcquisitionPrice);
            resultList.add(result);
        }
        return resultList;
    }

    private List<AdBigScreenResult.AccountInputOutputAnalysis> getCampaignInputOutputAnalyses(String ea, int top, TimePeriod timePeriod, List<AdLeadDataDTO> adLeadDataEntityList,
                                                                                              List<AdLeadDataDTO> relativeAdLeadDataEntityList, AdBigScreenSettingDetailBO settingDetailBO, OpportunityStatisticsDataBO opportunityStatisticsData) {

        Date beginTime = timePeriod.getBeginTime();
        Date endTime = timePeriod.getEndTime();
        Date relativeBeginTime = timePeriod.getRelativeBeginTime();
        Date relativeEndTime = timePeriod.getRelativeEndTime();

        Map<String, Integer> marketingEventIdToLeadCountMap = Maps.newHashMap();
        for (AdLeadDataDTO adLeadDataEntity : adLeadDataEntityList) {
            if (StringUtils.isNotBlank(adLeadDataEntity.getMarketingEventId())) {
                int leadCount = marketingEventIdToLeadCountMap.computeIfAbsent(adLeadDataEntity.getMarketingEventId(), k -> 0);
                marketingEventIdToLeadCountMap.put(adLeadDataEntity.getMarketingEventId(), ++leadCount);
            }
        }
        if (MapUtils.isEmpty(marketingEventIdToLeadCountMap)) {
            return Lists.newArrayList();
        }
        // 根据线索数量倒叙排序一下
        Map<String, Integer> sortedMap = Maps.newLinkedHashMap();
        marketingEventIdToLeadCountMap.entrySet().stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(top).forEachOrdered(e -> sortedMap.put(e.getKey(), e.getValue()));
        // 线索数量 top n 的市场活动id
        List<String> sortMarketingEventIdList = Lists.newArrayList(sortedMap.keySet());
        List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(),
                Lists.newArrayList("_id", "ad_source", "name"), sortMarketingEventIdList);
        // key: 广告平台  value: 市场活动
        Map<String, List<ObjectData>> adSourceMapToDataListMap =
                objectDataList.stream().filter(e -> StringUtils.isNotBlank(e.getString("ad_source"))).collect(Collectors.groupingBy(e -> e.getString("ad_source")));

        Map<String, String> marketingEventIdToNameMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2) -> v1));
        Map<String, AdCampaignDataStatisticsBO> marketingEventIdToStatisticsMap = Maps.newHashMap();
        // 查询百度的展点消
        if (adSourceMapToDataListMap.containsKey(AdSourceEnum.SOURCE_BAIDU.getValue())) {
            List<String> marketingEventIdList = adSourceMapToDataListMap.get(AdSourceEnum.SOURCE_BAIDU.getValue()).stream().map(ObjectData::getId).collect(Collectors.toList());
            List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryByMarketingEventIdList(ea, marketingEventIdList);
            if (CollectionUtils.isNotEmpty(baiduCampaignEntityList)) {
                Map<Long, String> campaignIdToMarketingEventIdMap = Maps.newHashMap();
                List<Long> baiduCampaignIdList = Lists.newArrayList();
                for (BaiduCampaignEntity campaignEntity : baiduCampaignEntityList) {
                    baiduCampaignIdList.add(campaignEntity.getCampaignId());
                    campaignIdToMarketingEventIdMap.put(campaignEntity.getCampaignId(), campaignEntity.getMarketingEventId());
                }
                List<AdCampaignDataStatisticsBO> list = baiduCampaignDataDAO.statisticsCampaignDataGroupByCampaign(ea, beginTime, endTime, relativeBeginTime, relativeEndTime, baiduCampaignIdList);
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(e -> marketingEventIdToStatisticsMap.put(campaignIdToMarketingEventIdMap.get(e.getCampaignId()), e));
                }
            }
        }
        // 查询腾讯的展点消
        if (adSourceMapToDataListMap.containsKey(AdSourceEnum.SOURCE_TENCETN.getValue())) {
            List<String> marketingEventIdList = adSourceMapToDataListMap.get(AdSourceEnum.SOURCE_TENCETN.getValue()).stream().map(ObjectData::getId).collect(Collectors.toList());
            List<TencentAdGroupEntity> adGroupEntityList = tencentAdGroupDAO.queryBySubMarketingEventIdList(ea, marketingEventIdList);
            if (CollectionUtils.isNotEmpty(adGroupEntityList)) {
                Map<Long, String> adGroupIdToMarketingEventIdMap = Maps.newHashMap();
                List<Long> tencentAdGroupIdList = Lists.newArrayList();
                for (TencentAdGroupEntity tencentAdGroupEntity : adGroupEntityList) {
                    tencentAdGroupIdList.add(tencentAdGroupEntity.getAdgroupId());
                    adGroupIdToMarketingEventIdMap.put(tencentAdGroupEntity.getAdgroupId(), tencentAdGroupEntity.getSubMarketingEventId());
                }
                // 腾讯只有广告组的展点消  没有推广计划的 这里做一个计算，根据广告组的站点消算出推广计划的展点消
                List<AdCampaignDataStatisticsBO> list = tencentAdGroupDataDAO.statisticsCampaignDataGroupByCampaign(ea, beginTime, endTime, relativeBeginTime, relativeEndTime, tencentAdGroupIdList);
                list.forEach(e -> marketingEventIdToStatisticsMap.put(adGroupIdToMarketingEventIdMap.get(e.getAdGroupId()), e));
            }
        }
        // 查询头条的展点消
        if (adSourceMapToDataListMap.containsKey(AdSourceEnum.SOURCE_JULIANG.getValue())) {
            List<String> marketingEventIdList = adSourceMapToDataListMap.get(AdSourceEnum.SOURCE_JULIANG.getValue()).stream().map(ObjectData::getId).collect(Collectors.toList());
            List<HeadlinesCampaignEntity> headlinesCampaignEntityList = headlinesCampaignDAO.getByMarketingEventIdList(ea, marketingEventIdList);
            if (CollectionUtils.isNotEmpty(headlinesCampaignEntityList)) {
                Map<Long, String> campaignIdToMarketingEventIdMap = Maps.newHashMap();
                List<Long> campaignIdList = Lists.newArrayList();
                for (HeadlinesCampaignEntity headlinesCampaignEntity : headlinesCampaignEntityList) {
                    campaignIdList.add(headlinesCampaignEntity.getCampaignId());
                    campaignIdToMarketingEventIdMap.put(headlinesCampaignEntity.getCampaignId(), headlinesCampaignEntity.getMarketingEventId());
                }
                List<AdCampaignDataStatisticsBO> list = headlinesCampaignDataDAO.statisticsCampaignDataGroupByCampaign(ea, beginTime, endTime, relativeBeginTime, relativeEndTime, campaignIdList);
                list.forEach(e -> marketingEventIdToStatisticsMap.put(campaignIdToMarketingEventIdMap.get(e.getCampaignId()), e));
            }

            List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryBySubMarketingIdList(ea, marketingEventIdList);
            if (CollectionUtils.isNotEmpty(headlinesAdEntityList)) {
                List<Long> headLinesAdIdList = Lists.newArrayList();
                Map<Long, String> adGroupIdToMarketingEventIdMap = Maps.newHashMap();
                for (HeadlinesAdEntity headlinesAdEntity : headlinesAdEntityList) {
                    headLinesAdIdList.add(headlinesAdEntity.getAdId());
                    adGroupIdToMarketingEventIdMap.put(headlinesAdEntity.getAdId(), headlinesAdEntity.getSubMarketingEventId());
                }
                List<AdCampaignDataStatisticsBO> list = headlinesAdDataDAO.statisticsAdDataGroupByAdIdList(ea, beginTime, endTime, relativeBeginTime, relativeEndTime, headLinesAdIdList);
                list.forEach(e -> marketingEventIdToStatisticsMap.put(adGroupIdToMarketingEventIdMap.get(e.getAdGroupId()), e));
            }
        }

        // 哪些线索阶段对应成MQL
        Set<String> mqlStageDefiniteSet = getLeadStageToMqlDefiniteSet(settingDetailBO);
        // 哪些线索阶段对应成SQL
        Set<String> sqlStageDefiniteSet = getLeadStageToSqlDefiniteSet(settingDetailBO);
        List<AdBigScreenResult.AccountInputOutputAnalysis> resultList = Lists.newArrayList();
        for (String marketingEventId : sortMarketingEventIdList) {
            AdBigScreenResult.AccountInputOutputAnalysis result = new AdBigScreenResult.AccountInputOutputAnalysis();
            result.setDimension(DIMENSION_CAMPAIGN);
            result.setDimensionValue(marketingEventIdToNameMap.get(marketingEventId));
            AdCampaignDataStatisticsBO adCampaignDataStatisticsBO = marketingEventIdToStatisticsMap.getOrDefault(marketingEventId, new AdCampaignDataStatisticsBO());
            // 消费
            BigDecimal totalCost = adCampaignDataStatisticsBO.getTotalCost() == null ? BigDecimal.ZERO : adCampaignDataStatisticsBO.getTotalCost();
            result.setCost(totalCost.setScale(SCALE, RoundingMode.HALF_UP));
            BigDecimal relativeTotalCost = adCampaignDataStatisticsBO.getRelativeTotalCost() == null ? BigDecimal.ZERO : adCampaignDataStatisticsBO.getRelativeTotalCost();
            result.setRelativeCost(relativeTotalCost.setScale(SCALE, RoundingMode.HALF_UP));
            // 线索数量
            List<AdLeadDataDTO> adLeadDataDTOList = adLeadDataEntityList.stream().filter(e -> marketingEventId.equals(e.getMarketingEventId())).collect(Collectors.toList());
            List<AdLeadDataDTO> relativeAdLeadDataDTOList = relativeAdLeadDataEntityList.stream().filter(e -> marketingEventId.equals(e.getMarketingEventId())).collect(Collectors.toList());
            result.setLeadsCount(BigDecimal.valueOf(adLeadDataDTOList.size()));
            result.setRelativeLeadsCount(BigDecimal.valueOf(relativeAdLeadDataDTOList.size()));
            // mql线索数量
            long mqlCount = adLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && mqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setLeadsMQLCount(BigDecimal.valueOf(mqlCount));
            long relativeMqlCount = relativeAdLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && mqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setRelativeLeadsMQLCount(BigDecimal.valueOf(relativeMqlCount));
            // sql线索数量
            long sqlCount = adLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && sqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setLeadsSQLCount(BigDecimal.valueOf(sqlCount));
            long relativeSqlCount = relativeAdLeadDataDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && sqlStageDefiniteSet.contains(e.getLeadsStage())).count();
            result.setRelativeLeadsSQLCount(BigDecimal.valueOf(relativeSqlCount));
            // 商机数量
            BigDecimal opportunityCount = opportunityStatisticsData.getOpportunityCount();
            result.setOpportunityCount(opportunityCount);
            BigDecimal relativeOpportunityCount = opportunityStatisticsData.getRelativeOpportunityCount();
            result.setRelativeOpportunityCount(relativeOpportunityCount);
            // 平均点击单价
            BigDecimal totalClick = adCampaignDataStatisticsBO.getTotalClick() == null ? BigDecimal.ZERO : adCampaignDataStatisticsBO.getTotalClick();
            BigDecimal avgClickPrice = totalClick.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(totalClick, SCALE, RoundingMode.HALF_UP);
            result.setAvgClickPrice(avgClickPrice);
            BigDecimal relativeTotalClick = adCampaignDataStatisticsBO.getRelativeTotalClick() == null ? BigDecimal.ZERO : adCampaignDataStatisticsBO.getRelativeTotalClick();
            BigDecimal relativeAvgClickPrice = relativeTotalClick.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(relativeTotalClick, SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgClickPrice(relativeAvgClickPrice);
            // 平均线索点击单价
            BigDecimal avgLeadAcquisitionPrice = BigDecimal.ZERO;
            if (result.getLeadsCount().compareTo(BigDecimal.ZERO) != 0) {
                avgLeadAcquisitionPrice = totalCost.divide(result.getLeadsCount(), SCALE, RoundingMode.HALF_UP);
            }
            result.setAvgLeadAcquisitionPrice(avgLeadAcquisitionPrice);
            BigDecimal relativeAvgLeadAcquisitionPrice = BigDecimal.ZERO;
            if (result.getRelativeLeadsCount().compareTo(BigDecimal.ZERO) != 0) {
                relativeAvgLeadAcquisitionPrice = relativeTotalCost.divide(result.getRelativeLeadsCount(), SCALE, RoundingMode.HALF_UP);
            }
            result.setRelativeAvgLeadAcquisitionPrice(relativeAvgLeadAcquisitionPrice);
            // 平均MQL获取单价
            BigDecimal avgMQLAcquisitionPrice = result.getLeadsMQLCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(result.getLeadsMQLCount(), SCALE, RoundingMode.HALF_UP);
            result.setAvgMQLAcquisitionPrice(avgMQLAcquisitionPrice);

            BigDecimal relativeAvgMQLAcquisitionPrice = result.getRelativeLeadsCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(result.getRelativeLeadsCount(), SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgMQLAcquisitionPrice(relativeAvgMQLAcquisitionPrice);
            // 平均SQL获取单价
            BigDecimal avgSQLAcquisitionPrice = result.getLeadsSQLCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(result.getLeadsSQLCount(), SCALE, RoundingMode.HALF_UP);
            result.setAvgSQLAcquisitionPrice(avgSQLAcquisitionPrice);
            BigDecimal relativeAvgSQLAcquisitionPrice = result.getRelativeLeadsSQLCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(result.getRelativeLeadsSQLCount(), SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgSQLAcquisitionPrice(relativeAvgSQLAcquisitionPrice);
            // 平均商机获取单价
            BigDecimal avgOpportunityAcquisitionPrice = result.getOpportunityCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(result.getOpportunityCount(), SCALE, RoundingMode.HALF_UP);
            result.setAvgOpportunityAcquisitionPrice(avgOpportunityAcquisitionPrice);
            BigDecimal relativeAvgOpportunityAcquisitionPrice = result.getRelativeOpportunityCount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : relativeTotalCost.divide(result.getRelativeOpportunityCount(), SCALE, RoundingMode.HALF_UP);
            result.setRelativeAvgOpportunityAcquisitionPrice(relativeAvgOpportunityAcquisitionPrice);

            resultList.add(result);
        }
        return resultList;
    }


    private List<AdBigScreenResult.AccountInputOutputAnalysis> getAccountInputOutputAnalyses(List<AdAccountEntity> adAccountEntityList, List<AdCampaignDataStatisticsBO> campaignDataList, List<AdLeadDataDTO> adLeadDataEntityList, AdBigScreenResult.AcquisitionCustomerConvertFunnel acquisitionCustomerConvertFunnel) {
        List<AdBigScreenResult.AccountInputOutputAnalysis> result = Lists.newArrayList();
        // 每个账户的展点消数据
        Map<String, AdCampaignDataStatisticsBO> adAccountIdToCampaignDataMap = campaignDataList.stream().filter(e -> StringUtils.isNotBlank(e.getAdAccountId())).collect(Collectors.toMap(AdCampaignDataStatisticsBO::getAdAccountId, e -> e, (v1, v2) -> v1));
        // 每个账户下面的线索数据
        Map<String, List<AdLeadDataDTO>> adAccountIdToLeadMap = adLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getAdAccountId())).collect(Collectors.groupingBy(AdLeadDataDTO::getAdAccountId));

        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            AdBigScreenResult.AccountInputOutputAnalysis accountInputOutputAnalysis = new AdBigScreenResult.AccountInputOutputAnalysis();
            accountInputOutputAnalysis.setDimensionValue(adAccountEntity.getUsername());
            accountInputOutputAnalysis.setDimension(DIMENSION_ACCOUNT);
            AdCampaignDataStatisticsBO adCampaignDataStatisticsBO = adAccountIdToCampaignDataMap.get(adAccountEntity.getId());
            BigDecimal totalCost = BigDecimal.ZERO;
            BigDecimal totalClick = BigDecimal.ZERO;
            if (adCampaignDataStatisticsBO != null) {
                totalCost = adCampaignDataStatisticsBO.getTotalCost() == null ? BigDecimal.ZERO : adCampaignDataStatisticsBO.getTotalCost();
                totalClick = adCampaignDataStatisticsBO.getTotalClick() == null ? BigDecimal.ZERO : adCampaignDataStatisticsBO.getTotalClick();
            }
            totalCost = totalCost.setScale(SCALE, RoundingMode.HALF_UP);
            totalClick = totalClick.setScale(SCALE, RoundingMode.HALF_UP);
            accountInputOutputAnalysis.setCost(totalCost.setScale(SCALE, RoundingMode.HALF_UP));
            // 平均点击单价
            BigDecimal avgClickPrice = totalClick.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(totalClick, SCALE, RoundingMode.HALF_UP);
            accountInputOutputAnalysis.setAvgClickPrice(avgClickPrice);

            List<AdLeadDataDTO> accountLeadList = adAccountIdToLeadMap.get(adAccountEntity.getId());
            if (CollectionUtils.isEmpty(accountLeadList)) {
                accountInputOutputAnalysis.setLeadsCount(BigDecimal.ZERO);
                accountInputOutputAnalysis.setLeadsMQLCount(BigDecimal.ZERO);
                accountInputOutputAnalysis.setLeadsSQLCount(BigDecimal.ZERO);
                accountInputOutputAnalysis.setOpportunityCount(BigDecimal.ZERO);
                accountInputOutputAnalysis.setAvgLeadAcquisitionPrice(BigDecimal.ZERO);
                accountInputOutputAnalysis.setAvgMQLAcquisitionPrice(BigDecimal.ZERO);
                accountInputOutputAnalysis.setAvgSQLAcquisitionPrice(BigDecimal.ZERO);
                accountInputOutputAnalysis.setAvgOpportunityAcquisitionPrice(BigDecimal.ZERO);
            } else {
                accountInputOutputAnalysis.setLeadsCount(BigDecimal.valueOf(accountLeadList.size()));
                // 线索MQL数量
                BigDecimal mqlCount = acquisitionCustomerConvertFunnel.getLeadsMQLCount();
                // 线索SQL数量
                BigDecimal sqlCount = acquisitionCustomerConvertFunnel.getLeadsSQLCount();

                accountInputOutputAnalysis.setLeadsMQLCount(mqlCount);

                accountInputOutputAnalysis.setLeadsSQLCount(sqlCount);
                // 线索转商机数量
                BigDecimal transferNewOpportunityCount = acquisitionCustomerConvertFunnel.getOpportunityCount();
                accountInputOutputAnalysis.setOpportunityCount(transferNewOpportunityCount);

                // 平均线索获取单价
                BigDecimal avgLeadAcquisitionPrice = totalCost.divide(accountInputOutputAnalysis.getLeadsCount(), SCALE, RoundingMode.HALF_UP);
                accountInputOutputAnalysis.setAvgLeadAcquisitionPrice(avgLeadAcquisitionPrice);
                // 平均MQL获取单价
                BigDecimal avgMQLAcquisitionPrice = mqlCount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(accountInputOutputAnalysis.getLeadsMQLCount(), SCALE, RoundingMode.HALF_UP);
                accountInputOutputAnalysis.setAvgMQLAcquisitionPrice(avgMQLAcquisitionPrice);
                // 平均SQL获取单价
                BigDecimal avgSQLAcquisitionPrice = sqlCount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(accountInputOutputAnalysis.getLeadsSQLCount(), SCALE, RoundingMode.HALF_UP);
                accountInputOutputAnalysis.setAvgSQLAcquisitionPrice(avgSQLAcquisitionPrice);
                // 平均商机获取单价
                BigDecimal avgOpportunityAcquisitionPrice = transferNewOpportunityCount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalCost.divide(accountInputOutputAnalysis.getOpportunityCount(), SCALE, RoundingMode.HALF_UP);
                accountInputOutputAnalysis.setAvgOpportunityAcquisitionPrice(avgOpportunityAcquisitionPrice);
            }
            result.add(accountInputOutputAnalysis);
        }
        result = result.stream().sorted((o1, o2) -> {
            if (o1.getLeadsCount().compareTo(o2.getLeadsCount()) > 0) {
                return -1;
            } else if (o1.getLeadsCount().compareTo(o2.getLeadsCount()) < 0) {
                return 1;
            }
            return 0;
        }).collect(Collectors.toList());
        return result;
    }

    private List<AdBigScreenResult.LeadsArealDistribution> buildLeadsArealDistributions(List<AdLeadDataDTO> adLeadDataEntityList, AdBigScreenSettingDetailBO settingDetailBO) {
        // 获取地域展示的维度
        Map<String, AdBigScreenSettingDetailBO.Field> fieldMap = settingDetailBO.getLeadsArealDistributions().getFieldList().stream().collect(Collectors.toMap(AdBigScreenSettingDetailBO.Field::getName, e -> e, (v1, v2) -> v1));
        AdBigScreenSettingDetailBO.Field field = fieldMap.get(DIMENSION_FIELD_NAME);
        Optional<AdBigScreenSettingDetailBO.FieldValue> fieldValueOptional = field.getFieldValueList().stream().filter(e -> BooleanUtils.isTrue(e.getSelected())).findFirst();
        String settingProvince;
        if (fieldValueOptional.isPresent()) {
            settingProvince = PROVINCE_MAP.get(fieldValueOptional.get().getValue());
        } else {
            settingProvince = CHINA;
        }
        String finalSettingProvince = settingProvince.replace("省", "");
        List<AdBigScreenResult.LeadsArealDistribution> result = Lists.newArrayList();
        String china = I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_1681);
        // 如果是全国的 返回全部省份
        if (china.equals(settingProvince)) {
            Map<String, Long> provinceToLeadCountMap = adLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMobileProvince()))
                    .collect(Collectors.groupingBy(AdLeadDataDTO::getMobileProvince, Collectors.counting()));
            for (String province : PROVINCE_MAP.values()) {
                AdBigScreenResult.LeadsArealDistribution leadsArealDistribution = new AdBigScreenResult.LeadsArealDistribution();
                province = province.replace("省", "");
                leadsArealDistribution.setProvince(province);
                leadsArealDistribution.setCount(BigDecimal.valueOf(provinceToLeadCountMap.getOrDefault(province, 0L)));
                result.add(leadsArealDistribution);
            }
            return result;
        }
        // 设置的是具体的省份  返回省份下面的城市
        Map<String, Long> provinceToLeadCountMap = adLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMobileProvince()) && finalSettingProvince.equals(e.getMobileProvince()))
                .filter(e -> StringUtils.isNotBlank(e.getMobileCity())).collect(Collectors.groupingBy(AdLeadDataDTO::getMobileCity, Collectors.counting()));
        for (Map.Entry<String, Long> entry : provinceToLeadCountMap.entrySet()) {
            String city = entry.getKey();
            Long count = entry.getValue();
            AdBigScreenResult.LeadsArealDistribution leadsArealDistribution = new AdBigScreenResult.LeadsArealDistribution();
            leadsArealDistribution.setCity(city);
            leadsArealDistribution.setCount(BigDecimal.valueOf(count));
            result.add(leadsArealDistribution);
        }
        return result;
    }

    private List<AdBigScreenResult.AcquisitionCustomerKeyword> buildAcquisitionCustomerKeyword(String ea, List<AdLeadDataDTO> adLeadDataEntityList, TimePeriod timePeriod) {
        List<AdBigScreenResult.AcquisitionCustomerKeyword> result = Lists.newArrayList();
        Map<String, Integer> keywordToLeadCountMap = Maps.newHashMap();
        for (AdLeadDataDTO adLeadDataEntity : adLeadDataEntityList) {
            if (StringUtils.isNotBlank(adLeadDataEntity.getKeywordId())) {
                int leadCount = keywordToLeadCountMap.computeIfAbsent(adLeadDataEntity.getKeywordId(), k -> 0);
                keywordToLeadCountMap.put(adLeadDataEntity.getKeywordId(), ++leadCount);
            }
        }
        // 根据线索数量倒叙排序一下
        Map<String, Integer> sortedMap = Maps.newLinkedHashMap();
        keywordToLeadCountMap.entrySet().stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(8).forEachOrdered(e -> sortedMap.put(e.getKey(), e.getValue()));

        List<String> keywordIdList = Lists.newArrayList(sortedMap.keySet());
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return result;
        }
        List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_KEYWORD.getName(), Lists.newArrayList("_id", "name"), keywordIdList);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return result;
        }
        Map<String, String> keywordIdToNameMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2) -> v1));
        Map<String, KeywordStatisticsData> keywordCostMap = getKeywordStatisticsMap(ea, keywordIdList, timePeriod.getBeginTime(), timePeriod.getEndTime());
        for (String keywordId : keywordIdList) {
            AdBigScreenResult.AcquisitionCustomerKeyword acquisitionCustomerKeyword = new AdBigScreenResult.AcquisitionCustomerKeyword();
            acquisitionCustomerKeyword.setCount(BigDecimal.valueOf(sortedMap.getOrDefault(keywordId, 0)));
            acquisitionCustomerKeyword.setKeyword(keywordIdToNameMap.getOrDefault(keywordId, ""));
            KeywordStatisticsData keywordStatisticsData = keywordCostMap.get(keywordId);
            acquisitionCustomerKeyword.setCosts(keywordStatisticsData == null ? BigDecimal.ZERO : keywordStatisticsData.getTotalCost().setScale(SCALE, RoundingMode.HALF_UP));
            result.add(acquisitionCustomerKeyword);
        }
        return result;
    }

    // 查询关键词的消费数量
    public Map<String, KeywordStatisticsData> getKeywordStatisticsMap(String ea, List<String> keywordIdList, Date beginTime, Date endTime) {
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.TERM_SERVING_LINES.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", "advertising_costs", "marketing_keyword_id"));

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("marketing_keyword_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), keywordIdList);
        paasQueryArg.addFilter("launch_date", PaasAndCrmOperatorEnum.GTE.getCrmOperator(), Lists.newArrayList(String.valueOf(beginTime.getTime())));
        paasQueryArg.addFilter("launch_date", PaasAndCrmOperatorEnum.LTE.getCrmOperator(), Lists.newArrayList(String.valueOf(endTime.getTime())));

        queryFilterArg.setQuery(paasQueryArg);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        int count = 0;
        String lastId = null;
        int pageSize = 2000;
        Map<String, KeywordStatisticsData> resultMap = Maps.newHashMap();
        while (count < totalCount) {
            InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
            if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
                break;
            }
            List<ObjectData> termServiceDataList = innerPage.getDataList();
            lastId = termServiceDataList.get(termServiceDataList.size() - 1).getId();
            count += termServiceDataList.size();
            for (ObjectData objectData : termServiceDataList) {
                String keywordId = objectData.getString("marketing_keyword_id");
                BigDecimal cost = objectData.getBigDecimal("advertising_costs");
                BigDecimal totalClick = objectData.getBigDecimal("click_count");

                KeywordStatisticsData keywordStatisticsData = resultMap.computeIfAbsent(keywordId, k -> new KeywordStatisticsData());
                cost = cost == null ? BigDecimal.ZERO : cost;
                if (keywordStatisticsData.getTotalCost() != null) {
                    cost = keywordStatisticsData.getTotalCost().add(cost);
                }
                keywordStatisticsData.setTotalCost(cost);

                totalClick = totalClick == null ? BigDecimal.ZERO : totalClick;
                if (keywordStatisticsData.getTotalClick() != null) {
                    totalClick = keywordStatisticsData.getTotalClick().add(totalClick);
                }
                keywordStatisticsData.setTotalClick(totalClick);
            }
        }
        return resultMap;
    }

    private AdBigScreenResult.CustomerAcquisitionCost buildCustomerAcquisitionCost(OpportunityStatisticsDataBO opportunityStatisticsData, List<AdLeadDataDTO> adLeadDataEntityList, AdCampaignDataStatisticsBO allStatisticsData,
                                                                                   AdBigScreenResult.AcquisitionCustomerConvertFunnel acquisitionCustomerConvertFunnel,
                                                                                   List<AdLeadDataDTO> relativeAdLeadDataEntityList, AdBigScreenSettingDetailBO settingDetailBO) {
        AdBigScreenResult.CustomerAcquisitionCost customerAcquisitionCost = new AdBigScreenResult.CustomerAcquisitionCost();
        // ------------平均点击单价开始------------
        BigDecimal totalCost = allStatisticsData.getTotalCost();
        BigDecimal totalClick = allStatisticsData.getTotalClick();
        // 平均点击单价
        BigDecimal avgClickPrice = BigDecimal.ZERO;
        if (totalClick.compareTo(BigDecimal.ZERO) != 0) {
            avgClickPrice = totalCost.divide(totalClick, SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setAvgClickPrice(avgClickPrice);

        // 计算环比平均点击单价
        BigDecimal relativeTotalCost = allStatisticsData.getRelativeTotalCost();
        BigDecimal relativeTotalClick = allStatisticsData.getRelativeTotalClick();
        // 环比平均点击单价
        BigDecimal relativeAvgClickPrice = BigDecimal.ZERO;
        if (relativeTotalClick.compareTo(BigDecimal.ZERO) != 0) {
            relativeAvgClickPrice = relativeTotalCost.divide(relativeTotalClick, SCALE, RoundingMode.HALF_UP);
        }
        BigDecimal relativeAvgClickPriceRatio = BigDecimal.ZERO;
        // 计算环比平均点击单价百分比
        if (relativeAvgClickPrice.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal value = customerAcquisitionCost.getAvgClickPrice().subtract(relativeAvgClickPrice);
            relativeAvgClickPriceRatio = value.divide(relativeAvgClickPrice, RADIO_SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setRelativeAvgClickPriceRatio(relativeAvgClickPriceRatio.multiply(HUNDRED));
        // ------------平均点击单价结束------------

        // ------------平均线索单价开始------------

        // 平均线索获取单价
        int leadCount = adLeadDataEntityList.size();
        BigDecimal avgLeadAcquisitionPrice = BigDecimal.ZERO;
        if (leadCount > 0) {
            avgLeadAcquisitionPrice = totalCost.divide(BigDecimal.valueOf(leadCount), SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setAvgLeadAcquisitionPrice(avgLeadAcquisitionPrice);
        // 环比平均线索获取单价
        BigDecimal relativeAvgLeadAcquisitionPrice = BigDecimal.ZERO;
        int leadRelativeCount = relativeAdLeadDataEntityList.size();
        if (leadRelativeCount > 0) {
            relativeAvgLeadAcquisitionPrice = relativeTotalCost.divide(BigDecimal.valueOf(leadRelativeCount), SCALE, RoundingMode.HALF_UP);
        }
        BigDecimal relativeAvgLeadAcquisitionPriceRatio = BigDecimal.ZERO;
        if (relativeAvgLeadAcquisitionPrice.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal value = avgLeadAcquisitionPrice.subtract(relativeAvgLeadAcquisitionPrice);
            relativeAvgLeadAcquisitionPriceRatio = value.divide(relativeAvgLeadAcquisitionPrice, RADIO_SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setRelativeAvgLeadAcquisitionPriceRatio(relativeAvgLeadAcquisitionPriceRatio.multiply(HUNDRED));
        // ------------平均线索单价结束------------

        // ------------平均MQL单价开始------------
        // 本期处于MQL的线索数量
        BigDecimal mqlLeadCount = acquisitionCustomerConvertFunnel.getLeadsMQLCount();
        // 环比处于MQL的线索数量
        // 哪些线索阶段对应成MQL
        Set<String> mqlStageDefiniteSet = getLeadStageToMqlDefiniteSet(settingDetailBO);
        long relativeMqlLeadCount = relativeAdLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && mqlStageDefiniteSet.contains(e.getLeadsStage())).count();
        // 本期处于SQL的线索数量
        BigDecimal sqlLeadCount = acquisitionCustomerConvertFunnel.getLeadsSQLCount();
        // 环比处于SQL的线索数量
        Set<String> sqlStageDefiniteSet = getLeadStageToSqlDefiniteSet(settingDetailBO);
        long relativeSqlLeadCount = relativeAdLeadDataEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadsStage()) && sqlStageDefiniteSet.contains(e.getLeadsStage())).count();

        // 平均MQL获取单价
        BigDecimal avgMQLAcquisitionPrice = BigDecimal.ZERO;
        if (mqlLeadCount.compareTo(BigDecimal.ZERO) > 0) {
            avgMQLAcquisitionPrice = totalCost.divide(mqlLeadCount, SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setAvgMQLAcquisitionPrice(avgMQLAcquisitionPrice);
        // 环比平均MQL获取单价百分比
        BigDecimal relativeAvgMQLAcquisitionPriceRatio = BigDecimal.ZERO;
        if (relativeMqlLeadCount > 0) {
            BigDecimal relativeAvgMQLAcquisitionPrice = relativeTotalCost.divide(BigDecimal.valueOf(relativeMqlLeadCount), SCALE, RoundingMode.HALF_UP);
            if (relativeAvgMQLAcquisitionPrice.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal sub = avgMQLAcquisitionPrice.subtract(relativeAvgMQLAcquisitionPrice);
                relativeAvgMQLAcquisitionPriceRatio = sub.divide(relativeAvgMQLAcquisitionPrice, RADIO_SCALE, RoundingMode.HALF_UP);
            }
        }
        customerAcquisitionCost.setRelativeAvgMQLAcquisitionPriceRatio(relativeAvgMQLAcquisitionPriceRatio.multiply(HUNDRED));
        // ------------平均MQL单价结束------------

        // ------------平均SQL单价开始------------

        // 平均SQL获取单价
        BigDecimal avgSQLAcquisitionPrice = BigDecimal.ZERO;
        if (sqlLeadCount.compareTo(BigDecimal.ZERO) > 0) {
            avgSQLAcquisitionPrice = totalCost.divide(sqlLeadCount, SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setAvgSQLAcquisitionPrice(avgSQLAcquisitionPrice);
        // 环比平均SQL获取单价百分比
        BigDecimal relativeAvgSQLAcquisitionPriceRatio = BigDecimal.ZERO;
        if (relativeSqlLeadCount > 0) {
            BigDecimal relativeAvgSQLAcquisitionPrice = relativeTotalCost.divide(BigDecimal.valueOf(relativeSqlLeadCount), SCALE, RoundingMode.HALF_UP);
            if (relativeAvgSQLAcquisitionPrice.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal value = avgSQLAcquisitionPrice.subtract(relativeAvgSQLAcquisitionPrice);
                relativeAvgSQLAcquisitionPriceRatio = value.divide(relativeAvgSQLAcquisitionPrice, RADIO_SCALE, RoundingMode.HALF_UP);
            }
        }
        customerAcquisitionCost.setRelativeAvgSQLAcquisitionPriceRatio(relativeAvgSQLAcquisitionPriceRatio.multiply(HUNDRED));
        // ------------平均SQL单价结束------------

        // ------------平均商机获取单价开始------------
        // 线索转为商机2.0的数量
        BigDecimal newOpportunityCount = acquisitionCustomerConvertFunnel.getOpportunityCount();
        // 环比商机2.0的线索数量
        BigDecimal relativeNewOpportunityCount = opportunityStatisticsData.getRelativeOpportunityCount();
        // 平均商机获取单价
        BigDecimal avgOpportunityAcquisitionPrice = BigDecimal.ZERO;
        if (newOpportunityCount.compareTo(BigDecimal.ZERO) > 0) {
            avgOpportunityAcquisitionPrice = totalCost.divide(newOpportunityCount, SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setAvgOpportunityAcquisitionPrice(avgOpportunityAcquisitionPrice);
        // 环比平均商机获取单价百分比
        BigDecimal relativeAvgOpportunityAcquisitionPriceRatio = BigDecimal.ZERO;
        if (relativeNewOpportunityCount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal relativeAvgOpportunityAcquisitionPrice = relativeTotalCost.divide(relativeNewOpportunityCount, SCALE, RoundingMode.HALF_UP);
            BigDecimal value = avgOpportunityAcquisitionPrice.subtract(relativeAvgOpportunityAcquisitionPrice);
            if (relativeAvgOpportunityAcquisitionPrice.compareTo(BigDecimal.ZERO) != 0) {
                relativeAvgOpportunityAcquisitionPriceRatio = value.divide(relativeAvgOpportunityAcquisitionPrice, RADIO_SCALE, RoundingMode.HALF_UP);
            }
        }
        customerAcquisitionCost.setRelativeAvgOpportunityAcquisitionPriceRatio(relativeAvgOpportunityAcquisitionPriceRatio.multiply(HUNDRED));
        // ------------平均商机获取单价结束------------

        // ------------平均赢单商机获取单价开始------------
        BigDecimal winOpportunityCount = acquisitionCustomerConvertFunnel.getWinOpportunityCount();
        BigDecimal avgWinOpportunityAcquisitionPrice = BigDecimal.ZERO;
        if (winOpportunityCount.compareTo(BigDecimal.ZERO) > 0) {
            avgWinOpportunityAcquisitionPrice = totalCost.divide(winOpportunityCount, SCALE, RoundingMode.HALF_UP);
        }
        customerAcquisitionCost.setAvgWinOpportunityAcquisitionPrice(avgWinOpportunityAcquisitionPrice);

        BigDecimal relativeWinOpportunityCount = opportunityStatisticsData.getRelativeWinOpportunityCount();
        BigDecimal relativeAvgWinOpportunityAcquisitionPriceRatio = BigDecimal.ZERO;
        if (relativeWinOpportunityCount.compareTo(BigDecimal.ZERO) > 0) {
            // 环比赢单商机获取单价
            BigDecimal relativeAvgWinOpportunityAcquisitionPrice = relativeTotalCost.divide(relativeWinOpportunityCount, SCALE, RoundingMode.HALF_UP);

            BigDecimal value = avgWinOpportunityAcquisitionPrice.subtract(relativeAvgWinOpportunityAcquisitionPrice);
            if (relativeAvgWinOpportunityAcquisitionPrice.compareTo(BigDecimal.ZERO) != 0) {
                relativeAvgWinOpportunityAcquisitionPriceRatio = value.divide(relativeAvgWinOpportunityAcquisitionPrice, RADIO_SCALE, RoundingMode.HALF_UP);
            }
        }
        customerAcquisitionCost.setRelativeAvgWinOpportunityAcquisitionPriceRatio(relativeAvgWinOpportunityAcquisitionPriceRatio.multiply(HUNDRED));
        // ------------平均赢单商机获取单价结束------------
        return customerAcquisitionCost;
    }

    private AdBigScreenResult.OverView buildOverView(String ea, TimePeriod timePeriod, AdCampaignDataStatisticsBO allStatisticsData, List<AdLeadDataDTO> adLeadDataEntityList,
                                                     List<AdLeadDataDTO> relativeAdLeadDataEntityList, OpportunityStatisticsDataBO winOpportunityStatisticsData, OpportunityStatisticsDataBO relativeWinOpportunityStatisticsData) {
        AdBigScreenResult.OverView overView = new AdBigScreenResult.OverView();
        overView.setSqlCount(BigDecimal.ZERO);
        overView.setWinOpportunityMoney(BigDecimal.ZERO);
        overView.setCosts(allStatisticsData.getTotalCost());
        if (allStatisticsData.getRelativeTotalCost().compareTo(BigDecimal.ZERO) == 0) {
            overView.setRelativeCostsRatio(BigDecimal.ZERO);
        } else {
            BigDecimal sub = allStatisticsData.getTotalCost().subtract(allStatisticsData.getRelativeTotalCost());
            BigDecimal ratio = sub.divide(allStatisticsData.getRelativeTotalCost(), RADIO_SCALE, RoundingMode.HALF_UP);
            overView.setRelativeCostsRatio(ratio.multiply(HUNDRED));
        }
        int leadCount = adLeadDataEntityList.size();
        overView.setLeadsCount(BigDecimal.valueOf(leadCount));
        int leadRelativeCount = relativeAdLeadDataEntityList.size();
        if (leadRelativeCount <= 0) {
            overView.setRelativeLeadsCountRatio(BigDecimal.ZERO);
        } else {
            int sub = leadCount - leadRelativeCount;
            BigDecimal ratio = BigDecimal.valueOf(sub).divide(BigDecimal.valueOf(leadRelativeCount), RADIO_SCALE, RoundingMode.HALF_UP);
            overView.setRelativeLeadsCountRatio(ratio.multiply(HUNDRED));
        }
        int sqlCount = adLeadDataManager.countByTransformTime(ea, timePeriod.getBeginTime().getTime(), timePeriod.getEndTime().getTime());
        long relativeSqlCount = adLeadDataManager.countByTransformTime(ea, timePeriod.getRelativeBeginTime().getTime(), timePeriod.getRelativeEndTime().getTime());
        overView.setSqlCount(BigDecimal.valueOf(sqlCount));
        if (relativeSqlCount <= 0) {
            overView.setRelativeSqlCountRatio(BigDecimal.ZERO);
        } else {
            long sub = sqlCount - relativeSqlCount;
            BigDecimal ratio = BigDecimal.valueOf(sub).divide(BigDecimal.valueOf(relativeSqlCount), RADIO_SCALE, RoundingMode.HALF_UP);
            overView.setRelativeSqlCountRatio(ratio.multiply(HUNDRED));
        }
        BigDecimal opportunityMoney = winOpportunityStatisticsData.getTotalAmount();
        BigDecimal relativeOpportunityMoney = relativeWinOpportunityStatisticsData.getTotalAmount();
        overView.setWinOpportunityMoney(opportunityMoney);
        if (relativeOpportunityMoney.compareTo(BigDecimal.ZERO) <= 0) {
            overView.setRelativeWinOpportunityMoneyRatio(BigDecimal.ZERO);
        } else {
            BigDecimal sub = opportunityMoney.subtract(relativeOpportunityMoney);
            BigDecimal ratio = sub.divide(relativeOpportunityMoney, RADIO_SCALE, RoundingMode.HALF_UP);
            overView.setRelativeWinOpportunityMoneyRatio(ratio.multiply(HUNDRED));
        }
        return overView;
    }

    private OpportunityStatisticsDataBO getOpportunityStatisticsDataFromDb(String ea, List<String> leadIdList) {
        leadIdList = leadIdList.stream().distinct().collect(Collectors.toList());
        List<List<String>> partitionList = Lists.partition(leadIdList, 1000);
        int winOpportunityCount = 0;
        int opportunityCount = 0;
        long totalStgChangedTime = 0;
        Set<String> leadIdSet = Sets.newHashSet();
        for (List<String> partition : partitionList) {
            List<AdLeadNewOpportunityDataBO> newOpportunityDataBOList = adLeadNewOpportunityDataManager.getDistinctDataByLeadIdList(ea, partition);
            if (CollectionUtils.isEmpty(newOpportunityDataBOList)) {
                continue;
            }
            opportunityCount += newOpportunityDataBOList.size();
            for (AdLeadNewOpportunityDataBO bo : newOpportunityDataBOList) {
                if (StringUtils.isNotBlank(bo.getSalesStage()) && OPPORTUNITY_WIN_STAGE.equals(bo.getSalesStage())) {
                    winOpportunityCount++;
                    Long stgChangedTime = bo.getStgChangedTime();
                    if (stgChangedTime != null) {
                        totalStgChangedTime += stgChangedTime;
                    }
                    leadIdSet.add(bo.getLeadId());
                }
            }
        }
        OpportunityStatisticsDataBO opportunityStatisticsData = new OpportunityStatisticsDataBO();
        opportunityStatisticsData.setOpportunityCount(BigDecimal.valueOf(opportunityCount));
        opportunityStatisticsData.setWinOpportunityCount(BigDecimal.valueOf(winOpportunityCount));
        opportunityStatisticsData.setWinOpporTotalStgChangedTime(BigDecimal.valueOf(totalStgChangedTime));
        opportunityStatisticsData.setLeadIdToWinOpporSet(leadIdSet);
        return opportunityStatisticsData;
    }

    private BigDecimal getWinOpportunityMoney(String ea, List<String> leadIdList) {
       return adLeadNewOpportunityDataManager.getOpportunityMoneyByLeadIdList(ea, leadIdList, OPPORTUNITY_WIN_STAGE);
    }

    private List<AdCampaignDataStatisticsBO> buildAdAccountCampaignDataList(String ea, TimePeriod timePeriod, List<AdAccountEntity> adAccountEntityList) {

        Date beginTime = timePeriod.getBeginTime();
        Date endTime = timePeriod.getEndTime();
        Date relativeBeginTime = timePeriod.getRelativeBeginTime();
        Date relativeEndTime = timePeriod.getRelativeEndTime();

        Set<String> adSourceSet = adAccountEntityList.stream().map(AdAccountEntity::getSource).collect(Collectors.toSet());
        List<AdCampaignDataStatisticsBO> campaignDataList = Lists.newArrayList();
        if (adSourceSet.contains(AdSourceEnum.SOURCE_BAIDU.getSource())) {
            List<AdCampaignDataStatisticsBO> list = baiduCampaignDataDAO.statisticsCampaignData(ea, beginTime, endTime, relativeBeginTime, relativeEndTime);
            if (CollectionUtils.isNotEmpty(list)) {
                campaignDataList.addAll(list);
            }
        }
        if (adSourceSet.contains(AdSourceEnum.SOURCE_TENCETN.getSource())) {
            List<AdCampaignDataStatisticsBO> list = tencentAdGroupDataDAO.statisticsCampaignData(ea, beginTime, endTime, relativeBeginTime, relativeEndTime);
            if (CollectionUtils.isNotEmpty(list)) {
                for (AdCampaignDataStatisticsBO adCampaignDataStatisticsBO : list) {
                    if (adCampaignDataStatisticsBO.getTotalCost() != null) {
                        adCampaignDataStatisticsBO.setTotalCost(adCampaignDataStatisticsBO.getTotalCost().divide(BigDecimal.valueOf(100), RADIO_SCALE, RoundingMode.HALF_UP));
                    }
                    if (adCampaignDataStatisticsBO.getRelativeTotalCost() != null) {
                        adCampaignDataStatisticsBO.setRelativeTotalCost(adCampaignDataStatisticsBO.getRelativeTotalCost().divide(BigDecimal.valueOf(100), RADIO_SCALE, RoundingMode.HALF_UP));
                    }
                }
                campaignDataList.addAll(list);
            }
        }
        if (adSourceSet.contains(AdSourceEnum.SOURCE_JULIANG.getSource())) {
            List<AdCampaignDataStatisticsBO> list = headlinesCampaignDataDAO.statisticsCampaignData(ea, beginTime, endTime, relativeBeginTime, relativeEndTime);
            if (CollectionUtils.isNotEmpty(list)) {
                campaignDataList.addAll(list);
            }
        }
        for (AdCampaignDataStatisticsBO adCampaignDataStatisticsBO : campaignDataList) {
            if (StringUtils.isBlank(adCampaignDataStatisticsBO.getAdAccountId())) {
                adCampaignDataStatisticsBO.setAdAccountId(adCampaignDataStatisticsBO.getRelativeAdAccountId());
            } else if (StringUtils.isBlank(adCampaignDataStatisticsBO.getRelativeAdAccountId())) {
                adCampaignDataStatisticsBO.setRelativeAdAccountId(adCampaignDataStatisticsBO.getAdAccountId());
            }
        }
        return campaignDataList;
    }

    private List<AdCampaignDataStatisticsBO> buildTotalCostGroupByActionDate(String ea, TimeRangeResult timeRangeResult, List<AdAccountEntity> adAccountEntityList) {

        Date beginTime = timeRangeResult.getBeginTime();
        Date endTime = timeRangeResult.getEndTime();

        Set<String> adSourceSet = adAccountEntityList.stream().map(AdAccountEntity::getSource).collect(Collectors.toSet());
        List<AdCampaignDataStatisticsBO> campaignDataList = Lists.newArrayList();
        if (adSourceSet.contains(AdSourceEnum.SOURCE_BAIDU.getSource())) {
            List<AdCampaignDataStatisticsBO> list = baiduCampaignDataDAO.getTotalCostGroupByActionData(ea, beginTime, endTime);
            if (CollectionUtils.isNotEmpty(list)) {
                campaignDataList.addAll(list);
            }
        }
        if (adSourceSet.contains(AdSourceEnum.SOURCE_TENCETN.getSource())) {
            List<AdCampaignDataStatisticsBO> list = tencentAdGroupDataDAO.getTotalCostGroupByActionData(ea, beginTime, endTime);
            if (CollectionUtils.isNotEmpty(list)) {
                campaignDataList.addAll(list);
            }
        }
        if (adSourceSet.contains(AdSourceEnum.SOURCE_JULIANG.getSource())) {
            List<AdCampaignDataStatisticsBO> list = headlinesCampaignDataDAO.getTotalCostGroupByActionData(ea, beginTime, endTime);
            if (CollectionUtils.isNotEmpty(list)) {
                campaignDataList.addAll(list);
            }
        }
        return campaignDataList;
    }


    private AdCampaignDataStatisticsBO buildAllStatisticsData(List<AdCampaignDataStatisticsBO> campaignDataList) {

        AdCampaignDataStatisticsBO dto = new AdCampaignDataStatisticsBO();
        dto.setTotalPv(BigDecimal.ZERO);
        dto.setTotalClick(BigDecimal.ZERO);
        dto.setTotalCost(BigDecimal.ZERO);
        dto.setRelativeTotalPv(BigDecimal.ZERO);
        dto.setRelativeTotalClick(BigDecimal.ZERO);
        dto.setRelativeTotalCost(BigDecimal.ZERO);

        for (AdCampaignDataStatisticsBO adCampaignDataStatisticsBO : campaignDataList) {
            if (adCampaignDataStatisticsBO.getTotalPv() != null) {
                BigDecimal totalPv = dto.getTotalPv().add(adCampaignDataStatisticsBO.getTotalPv());
                dto.setTotalPv(totalPv.setScale(SCALE, RoundingMode.HALF_UP));
            }
            if (adCampaignDataStatisticsBO.getTotalClick() != null) {
                BigDecimal totalClick = dto.getTotalClick().add(adCampaignDataStatisticsBO.getTotalClick());
                dto.setTotalClick(totalClick.setScale(SCALE, RoundingMode.HALF_UP));
            }
            if (adCampaignDataStatisticsBO.getTotalCost() != null) {
                BigDecimal totalCost = dto.getTotalCost().add(adCampaignDataStatisticsBO.getTotalCost());
                dto.setTotalCost(totalCost.setScale(SCALE, RoundingMode.HALF_UP));
            }
            if (adCampaignDataStatisticsBO.getRelativeTotalPv() != null) {
                BigDecimal relativeTotalPv = dto.getRelativeTotalPv().add(adCampaignDataStatisticsBO.getRelativeTotalPv());
                adCampaignDataStatisticsBO.setRelativeTotalPv(relativeTotalPv.setScale(SCALE, RoundingMode.HALF_UP));
            }
            if (adCampaignDataStatisticsBO.getRelativeTotalCost() != null) {
                BigDecimal relativeTotalCost = dto.getRelativeTotalCost().add(adCampaignDataStatisticsBO.getRelativeTotalCost());
                dto.setRelativeTotalCost(relativeTotalCost.setScale(SCALE, RoundingMode.HALF_UP));
            }
            if (adCampaignDataStatisticsBO.getRelativeTotalClick() != null) {
                BigDecimal relativeTotalClick = dto.getRelativeTotalClick().add(adCampaignDataStatisticsBO.getRelativeTotalClick());
                dto.setRelativeTotalClick(relativeTotalClick.setScale(SCALE, RoundingMode.HALF_UP));
            }
        }
        return dto;
    }

    public static TimePeriod buildTimePeriod(AdBigScreenSettingDetailBO.TimeSetting timeSetting) {
        TimePeriod timePeriod = new TimePeriod();
        BigScreenTimeRangeEnum bigScreenTimeRangeEnum = BigScreenTimeRangeEnum.getByName(timeSetting.getTimeRange());
        if (bigScreenTimeRangeEnum == BigScreenTimeRangeEnum.CUSTOMIZE) {
            timePeriod.setBeginTime(timeSetting.getBeginTime());
            timePeriod.setEndTime(timeSetting.getEndTime());
            timePeriod.setRelativeBeginTime(timeSetting.getCompareBeginTime());
            timePeriod.setRelativeEndTime(timeSetting.getCompareEndTime());
            return timePeriod;
        }
        TimeRangeResult timeRangeResult = getDateByTimeRangeResultByEnum(bigScreenTimeRangeEnum);
        timePeriod.setBeginTime(timeRangeResult.getBeginTime());
        timePeriod.setEndTime(timeRangeResult.getEndTime());
        if (Y_O_Y.equals(timeSetting.getCompareTimeType())) {
            timePeriod.setRelativeBeginTime(timeRangeResult.getYoYBeginTime());
            timePeriod.setRelativeEndTime(timeRangeResult.getYoYEndTime());
        } else {
            timePeriod.setRelativeBeginTime(timeRangeResult.getMomBeginTime());
            timePeriod.setRelativeEndTime(timeRangeResult.getMomEndTime());
        }
        return timePeriod;
    }

    public static TimeRangeResult getLastSixMonthTimeRange(TimePeriod timePeriod) {
        TimeRangeResult timeRangeResult = new TimeRangeResult();
        timeRangeResult.setBeginTime(DateUtil.getMonthStartTime(DateUtil.plusMonth(timePeriod.getEndTime(), -5)));
        timeRangeResult.setEndTime(timePeriod.getEndTime());
        return timeRangeResult;
    }

    public static TimeRangeResult getDateByTimeRangeResultByEnum(BigScreenTimeRangeEnum timeRangeEnum) {
        TimeRangeResult timeRangeResult = new TimeRangeResult();

        Date now = new Date();

        if (timeRangeEnum == BigScreenTimeRangeEnum.THIS_SEASON) {
            Date thisSeasonBeginDate = DateUtil.getThisSeasonBeginDate();
            timeRangeResult.setBeginTime(thisSeasonBeginDate);
            timeRangeResult.setEndTime(now);
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusMonth(timeRangeResult.getBeginTime(), -3));
            timeRangeResult.setMomEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getMomBeginTime(), 3), 1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getYoYBeginTime(), 3), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.LAST_SEASON) {
            Date thisSeasonBeginDate = DateUtil.getThisSeasonBeginDate();
            timeRangeResult.setBeginTime(DateUtil.plusMonth(thisSeasonBeginDate, -3));
            timeRangeResult.setEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getBeginTime(), 3), 1));
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusMonth(timeRangeResult.getBeginTime(), -3));
            timeRangeResult.setMomEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getEndTime(), -3), 1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getYoYBeginTime(), 3), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.THIS_YEAR) {
            timeRangeResult.setBeginTime(DateUtil.getStartDateCurrentYear());
            timeRangeResult.setEndTime(now);
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setMomEndTime(DateUtil.plusYear(DateUtil.getEndDateCurrentYear(), -1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusYear(timeRangeResult.getYoYBeginTime(), -1), 1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusYear(timeRangeResult.getYoYBeginTime(), 1), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.LAST_YEAR) {
            timeRangeResult.setBeginTime(DateUtil.plusYear(DateUtil.getStartDateCurrentYear(), -1));
            timeRangeResult.setEndTime(DateUtil.plusYear(DateUtil.getEndDateCurrentYear(), -1));
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setMomEndTime(DateUtil.plusYear(timeRangeResult.getEndTime(), -1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusYear(timeRangeResult.getYoYBeginTime(), 1), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.LAST_HALF_YEAR) {
            timeRangeResult.setBeginTime(DateUtil.getStartDateCurrentYear());
            Date endTime = DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getBeginTime(), 6), 1);
            if (now.before(endTime)) {
                endTime = now;
            }
            timeRangeResult.setEndTime(endTime);
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusMonth(timeRangeResult.getBeginTime(), -6));
            timeRangeResult.setMomEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getMomBeginTime(), 6), 1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getYoYBeginTime(), 6), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.NEXT_HALF_YEAR) {
            timeRangeResult.setBeginTime(DateUtil.plusMonth(DateUtil.getStartDateCurrentYear(), 6));
            Date endTime = DateUtil.getEndDateCurrentYear();
            if (now.before(endTime)) {
                endTime = now;
            }
            timeRangeResult.setEndTime(endTime);
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.getStartDateCurrentYear());
            timeRangeResult.setMomEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getMomBeginTime(), 6), 1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getYoYBeginTime(), 6), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.THIS_MONTH) {
            timeRangeResult.setBeginTime(DateUtil.getMonthStartTime(now));
            timeRangeResult.setEndTime(now);
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusMonth(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setMomEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getMomBeginTime(), 1), 1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getYoYBeginTime(), 1), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.LAST_MONTH) {
            timeRangeResult.setBeginTime(DateUtil.plusMonth(DateUtil.getMonthStartTime(now), -1));
            timeRangeResult.setEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getBeginTime(), 1), 1));
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusMonth(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setMomEndTime(DateUtil.plusMonth(timeRangeResult.getEndTime(), -1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusMonth(timeRangeResult.getYoYBeginTime(), 1), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.THIS_WEEK) {
            timeRangeResult.setBeginTime(DateUtil.getThisWeekBeginDate());
            timeRangeResult.setEndTime(now);
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusWeeks(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setMomEndTime(DateUtil.minusSecond(DateUtil.plusWeeks(timeRangeResult.getMomBeginTime(), 1), 1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusDay(timeRangeResult.getYoYBeginTime(), 7), 1));
        } else if (timeRangeEnum == BigScreenTimeRangeEnum.LAST_WEEK) {
            timeRangeResult.setBeginTime(DateUtil.plusWeeks(DateUtil.getThisWeekBeginDate(), -1));
            timeRangeResult.setEndTime(DateUtil.minusSecond(DateUtil.plusWeeks(timeRangeResult.getBeginTime(), 1), 1));
            // 环比时间
            timeRangeResult.setMomBeginTime(DateUtil.plusWeeks(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setMomEndTime(DateUtil.plusWeeks(timeRangeResult.getEndTime(), -1));
            // 同比时间
            timeRangeResult.setYoYBeginTime(DateUtil.plusYear(timeRangeResult.getBeginTime(), -1));
            timeRangeResult.setYoYEndTime(DateUtil.minusSecond(DateUtil.plusWeeks(timeRangeResult.getYoYBeginTime(), 1), 1));
        }
        return timeRangeResult;
    }

    public void updateSetting(AdBigScreenSettingEntity adBigScreenSettingEntity) {
        AdBigScreenSettingEntity existSetting = adBigScreenSettingDAO.getByEa(adBigScreenSettingEntity.getEa());
        if (existSetting == null) {
            adBigScreenSettingEntity.setId(UUIDUtil.getUUID());
            adBigScreenSettingDAO.batchInsert(Lists.newArrayList(adBigScreenSettingEntity));
            return;
        }
        adBigScreenSettingEntity.setId(existSetting.getId());
        adBigScreenSettingDAO.update(adBigScreenSettingEntity);
    }

    public List<AdBigScreenSettingEntity> getAllSetting() {
      return adBigScreenSettingDAO.getAllSetting();
    }

    public AdBigScreenSettingEntity getSettingFromDB(String ea) {
        return adBigScreenSettingDAO.getSetting(ea);
    }

    public List<AdBigScreenSettingSelectableTimeResult> getSelectableTimeList() {
        List<AdBigScreenSettingSelectableTimeResult> list = Lists.newArrayList();
        for (BigScreenTimeRangeEnum value : BigScreenTimeRangeEnum.values()) {
            AdBigScreenSettingSelectableTimeResult selectableTimeResult = new AdBigScreenSettingSelectableTimeResult();
            if (value == BigScreenTimeRangeEnum.CUSTOMIZE) {
                continue;
            }
            TimeRangeResult timeRangeResult = getDateByTimeRangeResultByEnum(value);
            selectableTimeResult.setTimeRange(value.getName());
            selectableTimeResult.setBeginTime(timeRangeResult.getBeginTime());
            selectableTimeResult.setEndTime(timeRangeResult.getEndTime());
            selectableTimeResult.setYoYBeginTime(timeRangeResult.getYoYBeginTime());
            selectableTimeResult.setYoYEndTime(timeRangeResult.getYoYEndTime());
            selectableTimeResult.setMomBeginTime(timeRangeResult.getMomBeginTime());
            selectableTimeResult.setMomEndTime(timeRangeResult.getMomEndTime());
            list.add(selectableTimeResult);
        }
        return list;
    }

    @Data
    public static class TimePeriod implements Serializable {
        private Date beginTime;
        private Date endTime;
        private Date relativeBeginTime;
        private Date relativeEndTime;
    }

    @Data
    public static class TimeRangeResult implements Serializable {

        // 开始时间
        private Date beginTime;

        // 结束时间
        private Date endTime;

        // 同比开始时间
        private Date yoYBeginTime;

        // 同比结束时间
        private Date yoYEndTime;

        // 环比开始时间
        private Date momBeginTime;

        // 环比结束时间
        private Date momEndTime;
    }

    @Data
    public static class KeywordStatisticsData implements Serializable {
        // 消费
        private BigDecimal totalCost = BigDecimal.ZERO;
        // 点击
        private BigDecimal totalClick = BigDecimal.ZERO;
    }
}