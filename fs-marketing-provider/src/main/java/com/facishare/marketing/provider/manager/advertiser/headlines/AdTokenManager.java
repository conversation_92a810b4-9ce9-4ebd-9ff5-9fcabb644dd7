package com.facishare.marketing.provider.manager.advertiser.headlines;

import com.facishare.marketing.common.enums.advertiser.tencent.TencentGrantTypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.provider.advertiser.adAccount.GetTencentAdAccessTokenData;
import com.facishare.marketing.provider.advertiser.adAccount.TencentAdResult;
import com.facishare.marketing.provider.advertiser.headlines.GetAuthInfoResultData;
import com.facishare.marketing.provider.advertiser.headlines.HeadlinesRequestResult;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.innerResult.BaiduAccessTokenResult;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.baidu.AccountApiManager;
import jetbrick.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by wangzhenyi on 2021/8/11 4:09 下午
 */
@Slf4j
@Component
public class AdTokenManager {
    @Autowired
    private AccountApiManager accountApiManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private AdAccountManager adAccountManager;


    public String getAccessToken(AdAccountEntity adAccountEntity) {
        AdSourceEnum adSourceEnum = AdSourceEnum.getBySource(adAccountEntity.getSource());
        if (adSourceEnum == null) {
            log.error("AdTokenManager.getAccessToken adSourceEnum null adAccountEntity:{}", adAccountEntity);
            return null;
        }
        // 兼容旧数据，头条授权改造后，authAccountId会有值，没有就是之前的老授权
        String authoriseUserId = adAccountEntity.getAuthAccountId() == null ? String.valueOf(adAccountEntity.getAccountId()) : String.valueOf(adAccountEntity.getAuthAccountId());
        String accessToken = redisManager.getAdvertiseAccessToken(adAccountEntity.getEa(), authoriseUserId, adSourceEnum.getValue());
        if (StringUtils.isEmpty(accessToken)) {
            log.info("get redis access token is null!!!");
            accessToken = refreshAccessToken(adAccountEntity);
        }
        return accessToken;
    }

    public String refreshAccessToken(AdAccountEntity adAccountEntity) {
        String refreshToken = adAccountEntity.getRefreshToken();
        HeadlinesRequestResult<GetAuthInfoResultData> authInfo = accountApiManager.refreshAccessToken(refreshToken);
        if (Objects.isNull(authInfo)) {
            return null;
        }
        if (authInfo.isSuccess()) {
            GetAuthInfoResultData authInfoResultData = authInfo.getData();
            String newRefreshToken = authInfoResultData.getRefresh_token();
            String newAccessToken = authInfoResultData.getAccess_token();
            Long expiresIn = authInfoResultData.getExpires_in() == null ? 86400L : authInfoResultData.getExpires_in();
            if (StringUtils.isEmpty(newRefreshToken) || StringUtils.isEmpty(newAccessToken)) {
                return null;
            }
            log.info("newAccessToken:{}, newRefreshToken:{}", newAccessToken, newRefreshToken);
            AdSourceEnum adSourceEnum = AdSourceEnum.getBySource(adAccountEntity.getSource());
            if (adSourceEnum == null) {
                log.error("AdTokenManager.getAccessToken adSourceEnum null adAccountEntity:{}", adAccountEntity);
                return null;
            }
            // 头条的授权改造后，authAccountId都不为空
            if (adAccountEntity.getAuthAccountId() != null) {
                redisManager.setAdvertiseAccessTokenWithExpiredTime(adAccountEntity.getEa(), String.valueOf(adAccountEntity.getAuthAccountId()), adSourceEnum.getValue(), newAccessToken, expiresIn);
                adAccountManager.updateTokenByAuthAccountId(adAccountEntity.getEa(), adAccountEntity.getAuthAccountId(), newRefreshToken, newAccessToken);
                return newAccessToken;
            }
            redisManager.setAdvertiseAccessTokenWithExpiredTime(adAccountEntity.getEa(), String.valueOf(adAccountEntity.getAccountId()), adSourceEnum.getValue(), newAccessToken, expiresIn);
            // refresh_token存本地DB
            boolean dbResult = adAccountManager.updateRefreshTokenByAccountId(newRefreshToken, newAccessToken, adAccountEntity.getAccountId());
            if (!dbResult) {
                return null;
            }
            return newAccessToken;
        }
        return null;
    }

    /**
     * 刷新accessToken
     *
     * @param adAccountEntity 用户信息
     * @return newAccessToken       新的accessToken
     */
    public Optional<String> getTencentAccessToken(AdAccountEntity adAccountEntity) {
        String accessToken = redisManager.getAdvertiseAccessToken(adAccountEntity.getEa(), String.valueOf(adAccountEntity.getAuthAccountId()), AdSourceEnum.SOURCE_TENCETN.getValue());
        if (StringUtils.isNotEmpty(accessToken)) {
            return Optional.of(accessToken);
        }
        String refreshToken = adAccountEntity.getRefreshToken();
        TencentAdResult<GetTencentAdAccessTokenData> tencentAdTokenResult = accountApiManager.getTencentAdToken(null, refreshToken, TencentGrantTypeEnum.REFRESH_TOKEN.getValue());
        if (tencentAdTokenResult == null || tencentAdTokenResult.getData() == null || !tencentAdTokenResult.isSuccess()) {
            log.info("AdTokenManager.getTencentAccessToken fail, tencentAdTokenResult:{}", tencentAdTokenResult);
            return Optional.empty();
        }
        String newAccessToken = tencentAdTokenResult.getData().getAccess_token();
        String newRefreshToken = tencentAdTokenResult.getData().getRefresh_token();
        redisManager.setAdvertiseAccessToken(adAccountEntity.getEa(), String.valueOf(adAccountEntity.getAuthAccountId()), AdSourceEnum.SOURCE_TENCETN.getValue(), newAccessToken);
        boolean res = adAccountManager.updateRefreshTokenById(newRefreshToken, newAccessToken, adAccountEntity.getId());
        if (res) {
            return Optional.of(newAccessToken);
        }
        return Optional.empty();
    }

    public BaiduAccessTokenResult getBaiduAccessTokenByAuthCode(String authCode, Long userId, String secretKey, String appId) {
        return accountApiManager.getBaiduAccessTokenByAuthCode(authCode, userId, secretKey, appId);
    }

    public String getBaiduAccessToken(AdAccountEntity adAccountEntity) {
        Long accountId = adAccountEntity.getAuthAccountId() == null ? adAccountEntity.getAccountId() : adAccountEntity.getAuthAccountId();
        String accessToken = redisManager.getAdvertiseAccessToken(adAccountEntity.getEa(), String.valueOf(accountId), AdSourceEnum.SOURCE_BAIDU.getValue());
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }
        String refreshToken = adAccountEntity.getRefreshToken();
        if (StringUtils.isBlank(refreshToken)) {
            return null;
        }
        BaiduAccessTokenResult tokenResult = accountApiManager.getBaiduAccessToken(refreshToken, accountId);
        if (tokenResult == null || tokenResult.getCode() != 0) {
            log.warn("AdTokenManager.getBaiduAccessToken fail, tokenResult:{}", tokenResult);
            return null;
        }
        String newAccessToken = tokenResult.getData().getAccessToken();
        String newRefreshToken = tokenResult.getData().getRefreshToken();
        Long expiresIn = tokenResult.getData().getExpiresIn() == null ? 86400L : tokenResult.getData().getExpiresIn();
        if (adAccountEntity.getAuthAccountId() != null) {
            redisManager.setAdvertiseAccessTokenWithExpiredTime(adAccountEntity.getEa(), String.valueOf(adAccountEntity.getAuthAccountId()), AdSourceEnum.SOURCE_BAIDU.getValue(), newAccessToken, expiresIn);
            adAccountManager.updateTokenByAuthAccountId(adAccountEntity.getEa(), adAccountEntity.getAuthAccountId(), newRefreshToken, newAccessToken);
            return newAccessToken;
        }
        redisManager.setAdvertiseAccessTokenWithExpiredTime(adAccountEntity.getEa(), String.valueOf(accountId), AdSourceEnum.SOURCE_BAIDU.getValue(), newAccessToken, expiresIn);
        boolean res = adAccountManager.updateRefreshTokenById(newRefreshToken, newAccessToken, adAccountEntity.getId());
        if (res) {
            return newAccessToken;
        }
        return null;
    }
}
