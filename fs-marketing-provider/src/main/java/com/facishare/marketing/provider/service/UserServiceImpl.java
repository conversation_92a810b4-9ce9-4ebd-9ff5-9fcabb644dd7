package com.facishare.marketing.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.EmployeeMsgResult;
import com.facishare.marketing.api.result.GetAllUserResult;
import com.facishare.marketing.api.result.MarketingWxServiceResult;
import com.facishare.marketing.api.result.UserIdentityResult;
import com.facishare.marketing.api.result.marketingplugin.MarketingPluginOpenResult;
import com.facishare.marketing.api.result.marketingplugin.MarketingPluginResult;
import com.facishare.marketing.api.service.OutLinkService;
import com.facishare.marketing.api.service.UserService;
import com.facishare.marketing.api.service.distribution.OperatorService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.api.service.marketingplugin.CouponTemplateService;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GrayUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.UserRoleDao;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.innerData.LicenseExpiredData;
import com.facishare.marketing.provider.innerResult.ding.DingStaffInfoResult;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.MarketingPluginConfigManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetAllDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.result.GetAllDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetAllEmployeesArg;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.paas.license.arg.QueryExpireTimeArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("userService")
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private OpenAppAdminService openAppAdminService;
    @Autowired
    private OperatorService operatorService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;
    @Autowired
    private DingManager dingManager;
    @Value("${marketing_appid}")
    private String appId;
    @Value("${home.domain}")
    private String homeDomain;
    @ReloadableProperty("marketingFlowGrayEas")
    private String marketingFlowGrayEas;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private WechatAccountManager wechatAccountManager;

    @ReloadableProperty("gray.third.platform.bind.enterprise")
    private String grayThirdPlatformBindEnterprise;
    @Autowired
    private UserRoleDao userRoleDao;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;
    @Autowired
    private CouponTemplateService couponTemplateService;
    @Autowired
    private DingAuthService dingAuthService;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private WeChatServiceMarketingActivityService weChatServiceMarketingActivityService;
    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;
    @Autowired
    private OutLinkService outLinkService;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @ReloadableProperty("scrm_statistic_gray_list")
    private String scrmStatisticGrayList;

    @ReloadableProperty("fx_email_spread_eas")
    private String fxEmailSpreadEas;

    @Override
    public Result<GetAllUserResult> getAllUsers(String ea, Integer fsUserId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);

        List<Integer> adminList = fsAddressBookManager.getEnterpriseAdminList(ea, fsUserId);
        if (CollectionUtils.isEmpty(adminList)) {
            return new Result<>(SHErrorCode.NOT_APP_MANAGER);
        }

        Integer adminId = adminList.get(0);

        GetAllUserResult result = new GetAllUserResult();

        GetAllEmployeesArg allEmployeesArg = new GetAllEmployeesArg();
        allEmployeesArg.setRunStatus(RunStatus.ACTIVE);
        allEmployeesArg.setCurrentEmployeeId(adminId);
        allEmployeesArg.setEnterpriseId(ei);

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByEa(ea);
        if (null == fsEmployeeMsgMap) {
            result.setUserDetailResultList(Lists.newArrayList());
        } else {

            List<GetAllUserResult.UserDetailResult> userDetailResults = Lists.newArrayList();
            for (Integer key : fsEmployeeMsgMap.keySet()) {
                FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(key);
                GetAllUserResult.UserDetailResult userDetailResult = new GetAllUserResult.UserDetailResult();

                userDetailResult.setId(fsEmployeeMsg.getEmployeeId());
                userDetailResult.setName(fsEmployeeMsg.getName());
                userDetailResult.setProfileImage(fsEmployeeMsg.getProfileImage());
                userDetailResult.setNameSpell(fsEmployeeMsg.getNameSpell());

                userDetailResults.add(userDetailResult);
            }

            result.setUserDetailResultList(userDetailResults);
        }

        GetAllDepartmentArg getAllDepartmentArg = new GetAllDepartmentArg();
        getAllDepartmentArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        getAllDepartmentArg.setCurrentEmployeeId(adminId);
        getAllDepartmentArg.setEnterpriseId(ei);

        GetAllDepartmentResult allDepartmentResult = departmentService.getAllDepartment(getAllDepartmentArg);
        if (allDepartmentResult == null || CollectionUtils.isEmpty(allDepartmentResult.getDepartmentList())) {
            result.setDepartmentDetailResultList(Lists.newArrayList());
        } else {
            List<Department> departmentList = allDepartmentResult.getDepartmentList();
            List<GetAllUserResult.DepartmentDetailResult> departmentDetailResults = Lists.newArrayList();

            departmentList.forEach(department -> {
                GetAllUserResult.DepartmentDetailResult departmentDetailResult = new GetAllUserResult.DepartmentDetailResult();
                departmentDetailResult.setId(department.getDepartmentId());
                departmentDetailResult.setName(department.getName());
                departmentDetailResult.setNameSpell(department.getNameSpell());
                departmentDetailResult.setParentDepartmentId(department.getParentDepartmentId());
                departmentDetailResult.setNameOrder(department.getNameOrder());
                departmentDetailResult.setDepartmentOrder(department.getDepartmentOrder());

                departmentDetailResults.add(departmentDetailResult);
            });

            result.setDepartmentDetailResultList(departmentDetailResults);
        }

        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<EmployeeMsgResult> getEmployeeMsg(String ea, Integer fsUserId) {
        FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(ea, fsUserId);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        EmployeeMsgResult result = new EmployeeMsgResult();
        result.setEa(ea);
        result.setEi(ei);
        result.setHomeDomain(homeDomain);
        result.setBindOfficialAccountOpen(false);
        if (fsEmployeeMsg != null) {
            result.setName(fsEmployeeMsg.getName());
            result.setAvatar(fsEmployeeMsg.getProfileImage());
        }
        Result<UserIdentityResult> userIdentityResultResult = getUserIdentity(ea, fsUserId);
        if (userIdentityResultResult == null || !userIdentityResultResult.isSuccess()) {
            log.info("call getUserIdentity return null ea:{}  fsUserId:{}", ea, fsUserId);
            return Result.newError(SHErrorCode.EMPLOYEE_GET_FAILED);
        }

        if (userIdentityResultResult.getData() != null) {
            result.setIsAppAdmin(userIdentityResultResult.getData().getIsAppAdmin());
            result.setIsOperator(userIdentityResultResult.getData().getIsOperator());
        }
        result.setIsKisVersion(appVersionManager.isKisVersion(ea));

        GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
        enterpriseDataArg.setEnterpriseAccount(ea);
        enterpriseDataArg.setEnterpriseId(ei);
        GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
        if (enterpriseDataResult != null && enterpriseDataResult.getEnterpriseData() != null) {
            result.setEnterpriseName(enterpriseDataResult.getEnterpriseData().getEnterpriseName());
        }
        result.setVersion(appVersionManager.getCurrentAppVersion(ea));
        if (VersionEnum.isDingDingVersion(result.getVersion())){
           com.facishare.open.ding.common.result.Result<List<DingMappingEmployeeResult>> dingMapEmployeeResult = dingAuthService.batchGetDingEmps(ea, Lists.newArrayList(fsUserId));
           if (dingMapEmployeeResult.isSuccess() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(dingMapEmployeeResult.getData())) {
               String dingAccessToken = dingManager.getAuthAccessToken(ea);
               DingStaffInfoResult dingStaffInfoResult = dingManager.getStaffByDepartmentId(dingAccessToken, dingMapEmployeeResult.getData().get(0).getDingEmployeeId());
               result.setDingManager(dingStaffInfoResult == null ? false : dingStaffInfoResult.getResult().getAdmin());
           }
            com.facishare.open.ding.common.result.Result<String> corpIdResult = dingAuthService.EAtoDingCorpId(ea);
           if (corpIdResult.isSuccess()) {
               result.setDingCorpId(corpIdResult.getData());
           }
        }

        try {
            result.setMarketingFlowOpen(GrayUtil.checkInGrayEa(ea, marketingFlowGrayEas));
        } catch (Exception e) {
            log.error("Error at set marketing flow open", e);
            result.setMarketingFlowOpen(false);
        }

        result.setOpenQywxMiniApp(false);
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity != null) {
            result.setOpenQywxMiniApp(true);
        }
        //统一处理营销插件
        marketingPluginConfigManager.handlePluginConfig(ea, result);
        //单独处理纷享优惠券插件
        marketingPluginConfigManager.handleCouponConfig(ea,result);
        result.setOutQrCodeActive(outLinkService.getActiveStatus(ea));
        // 处理直播相关插件
        handleLiveConfig(ea, result);
        // 处理license相关
        handleLicense(ea, result);
        //是否开启优惠券入口
        Boolean flag = false;
        Result<Boolean> couponEaListResult = couponTemplateService.queryEnterCouponEaList(ea);
        if (couponEaListResult.isSuccess() && couponEaListResult.getData() != null) {
            flag = couponEaListResult.getData();
        }
        result.setShowCouponEnabled(flag);
        if (!StringUtils.equals(result.getVersion(), VersionEnum.DING_FREE_APP.getVersion())) {
            result.setRoleIds(userRoleDao.listByEmployeeId(ea, fsUserId));
        }

        //版本过期日期
        result.setExpireTime(queryLicenseExpired(ea, result.getVersion()));
        result.setEnableWxThirdPlatformBind(true);
        result.setEnterpriseLibraryEnabled(appVersionManager.isEnterpriseLibraryEnabled(ea));
        result.setMemberOpened(memberManager.isOpenMember(ea));

        //是否绑定微信公众号
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        result.setBindQywxOpen(qywxCorpAgentConfigEntity != null);

        //是否绑定企业微信
        Result<List<MarketingWxServiceResult>> listResult = weChatServiceMarketingActivityService.listMarketingWxServiceInfo(ea, fsUserId, false, false);
        if (listResult != null && listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
            List<String> wxAppIdList = listResult.getData().stream().map(MarketingWxServiceResult::getWxAppId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            result.setBindOfficialAccountOpen(CollectionUtils.isNotEmpty(wxAppIdList));
        }
        //临时: 是否开启企微群发/朋友圈统计
        if (StringUtils.isNotBlank(scrmStatisticGrayList) &&
                (Lists.newArrayList(scrmStatisticGrayList.split(",")).contains("*") || Lists.newArrayList(scrmStatisticGrayList.split(",")).contains(ea))) {
            result.setIsEnabledScrmStatistic(true);
        }

        // 是否开启纷享邮箱推广
        if (StringUtils.isNotBlank(fxEmailSpreadEas) && (Arrays.asList(fxEmailSpreadEas.split(",")).contains(ea))) {
            result.setFxEmailSpreadEnabled(true);
        }

        //是否为vpn断网环境
        result.setIsVpnDisconnectCloud(appVersionManager.isVpnDisconnectCloud());
        return Result.newSuccess(result);
    }

    private void handleLicense(String ea, EmployeeMsgResult result) {
        result.setIsPurchaseAdLicense(adCommonManager.isPurchaseAdLicense(ea));
    }

    /**
     * 直播相关插件处理
     * @param ea
     * @param result
     */
    private void handleLiveConfig(String ea, EmployeeMsgResult result){
        List<Integer> types = Lists.newArrayList(MarketingPluginTypeEnum.XIAOETONG_LIVE.getType(),
                MarketingPluginTypeEnum.VHALL_LIVE.getType(),
                MarketingPluginTypeEnum.CHANNELS_LIVE.getType(),
                MarketingPluginTypeEnum.POLYV_LIVE.getType(),
                MarketingPluginTypeEnum.MUDU_LIVE.getType());
        List<MarketingPluginConfigEntity> entities = marketingPluginConfigManager.bulkGetByTypes(ea, types);
        Map<Integer, Boolean> map = entities.stream().collect(Collectors.toMap(MarketingPluginConfigEntity::getPluginType, MarketingPluginConfigEntity::getStatus));

        List<EmployeeMsgResult.LivePluginConfig> configs = types.stream().map(type -> {
            EmployeeMsgResult.LivePluginConfig config = new EmployeeMsgResult.LivePluginConfig();
            config.setType(type);
            config.setStatus(false); // 插件默认关闭
            if (map.containsKey(type)) {
                config.setStatus(map.get(type));
            }
            return config;
        }).collect(Collectors.toList());
        Map<Integer, EmployeeMsgResult.LivePluginConfig> configMap = configs.stream().collect(Collectors.toMap(EmployeeMsgResult.LivePluginConfig::getType, Function.identity()));
        result.setLivePluginConfigs(configMap);
    }

    @Override
    public Result<UserIdentityResult> getUserIdentity(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea)) {
            log.warn("ea is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        };

        if (fsUserId == null) {
            log.warn("fsUserId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        boolean isAppAdmin = false;
        boolean isOperator = false;

        String userIdString = "E." + ea + "." + fsUserId;
        BaseResult<Boolean> baseResult = openAppAdminService.isAppAdmin(userIdString, appId);
        if (!baseResult.isSuccess()) {
            log.error("operatorService.isOperator failed, ea={}, userId={}", ea, fsUserId);
        }
        if (BooleanUtils.isTrue(baseResult.getResult())) {
            isAppAdmin = true;
        }
        
        Result<Boolean> operatorResult = operatorService.isOperator(ea, fsUserId);
        if (!operatorResult.isSuccess()) {
            log.error("operatorService.isOperator failed, ea={}, userId={}", ea, fsUserId);
        }
        if (BooleanUtils.isTrue(operatorResult.getData())) {
            isOperator = true;
        }

        UserIdentityResult result = new UserIdentityResult();
        result.setIsAppAdmin(isAppAdmin);
        result.setIsOperator(isOperator);

        return Result.newSuccess(result);
    }

    private Long queryLicenseExpired(String ea, String license){
        if (ea == null || license == null){
            log.info("queryLicenseExpired return null ea:{} license:{}", ea, license);
            return null;
        }

        QueryExpireTimeArg arg = new QueryExpireTimeArg();
        LicenseContext context = new LicenseContext();
        arg.setContext(context);
        context.setAppId("CRM");
        context.setTenantId(String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
        context.setUserId("1000");
        arg.setProductType("3");
        arg.setProductCodes(Lists.newArrayList(license));
        com.facishare.paas.license.common.Result result = licenseClient.queryLicenseExpired(arg);
        log.info("marketing license ea:{} license:{} expire info:{}", ea, license, result);
        if (result.getErrCode() == 0 && result.getResult() != null){
            List<LicenseExpiredData> licenseExpiredDataList = GsonUtil.getGson().fromJson(result.getResult().toString(), new TypeToken<List<LicenseExpiredData> >(){}.getType());
            if (CollectionUtils.isNotEmpty(licenseExpiredDataList)){
               return licenseExpiredDataList.get(0).getExpireTime();
            }
        }

        return null;
    }

    /**
     * 根据EA获取EI
     * @param ea
     * @return
     */
    @Override
    public Result<Integer> getEIByEA(String ea) {
        try {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            return Result.newSuccess(ei);
        } catch (Exception e) {
            log.warn("parse ea to ei failed, ea:{} exception:", ea, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<MarketingPluginResult> queryNewVersionPlugin(String ea, Integer fsUserId) {
        MarketingPluginResult result = new MarketingPluginResult();
        String currentAppVersion = appVersionManager.getCurrentAppVersion(ea);
        List<MarketingPluginResult.PluginConfig> pluginConfigs = marketingPluginConfigManager.handleNewVersionPlugin(ea,currentAppVersion);
        result.setPlugins(pluginConfigs);
        return Result.newSuccess(result);
    }

    @Override
    public Result<MarketingPluginOpenResult> queryPluginEnableOpen(String ea, Integer fsUserId,List<Integer> types) {
        MarketingPluginOpenResult result = new MarketingPluginOpenResult();
        String currentAppVersion = appVersionManager.getCurrentAppVersion(ea);
        List<MarketingPluginOpenResult.PluginOpenConfig> pluginOpenConfigs = marketingPluginConfigManager.handleNewVersionPluginEnableOpen(ea,currentAppVersion,types);
        result.setOpenConfigs(pluginOpenConfigs);
        return Result.newSuccess(result);
    }
}
