package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.dto.FormBindObjectStatisticsDTO;
import com.facishare.marketing.provider.dto.kis.BranchSearchFormBindObjectStatisticsDTO;
import com.facishare.marketing.provider.entity.CustomizeFormDataObjectEntity;
import com.github.mybatis.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2019/04/08
 **/
public interface CustomizeFormDataObjectDAO {

    @Insert("INSERT INTO customize_form_data_object(id, ea, form_id, object_id, object_type, create_by, create_time, update_by, update_time, form_style_type, form_button_name, button_style) VALUES (#{obj.id}, #{obj.ea}, #{obj.formId}, #{obj.objectId}, #{obj.objectType}, #{obj.createBy},now(),#{obj.updateBy}, now(),#{obj.formStyleType}, #{obj.formButtonName}, #{obj.buttonStyle, typeHandler=ButtonStyleTypeHandler});")
    int insertCustomizeFormDataObject(@Param("obj") CustomizeFormDataObjectEntity customizeFormDataObjectEntity);


    @Delete("DELETE FROM customize_form_data_object WHERE ea = #{ea} AND form_id = #{formId} AND  object_id = #{objectId} AND object_type = #{objectType}")
    int deleteCustomizeFormDataObject(@Param("ea") String ea, @Param("formId") String formId, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Delete("<script>"
            + "DELETE FROM customize_form_data_object WHERE ea = #{ea} AND object_type = #{objectType} "
            + "AND form_id IN\n"
            + "<foreach collection = 'formIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + " AND  object_id IN\n"
            + "<foreach collection = 'objectIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "</script>")
    int deleteCustomizeFormDataObjectBatch(@Param("ea") String ea, @Param("formIdList") List<String> formIdList, @Param("objectIdList") List<String> objectIdList, @Param("objectType") Integer objectType);

    @Select("SELECT * FROM customize_form_data_object WHERE form_id = #{formId}")
    List<CustomizeFormDataObjectEntity> queryCustomizeFormDataObjectByFormId(@Param("formId") String formId,@Param("ea")String ea);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_object WHERE form_id IN\n"
            + "<foreach collection = 'formIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "</script>")
    List<CustomizeFormDataObjectEntity> queryCustomizeFormDataObjectByFormIdList(@Param("formIdList") List<String> formIdList,@Param("ea")String ea);

    @Select("SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND form_id = #{formId} AND object_id = #{objectId} AND object_type = #{objectType}")
    CustomizeFormDataObjectEntity accurateAccessCustomizeFormDataObject(@Param("ea") String ea, @Param("formId") String formId, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND form_id = #{formId} AND object_type = #{objectType} order by create_time desc")
    List<CustomizeFormDataObjectEntity> getByFormIdAndObjectType(@Param("ea") String ea, @Param("formId") String formId, @Param("objectType") Integer objectType);

    @Select("SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND object_id = #{objectId} AND object_type = #{objectType} ORDER BY create_time DESC LIMIT 1")
    CustomizeFormDataObjectEntity getObjectBindingForm(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND object_type = #{objectType} AND object_id IN\n"
            +   "<foreach collection = 'objectIdList' item = 'id' open = '(' separator = ',' close = ')'>"
            +       "#{id}"
            +   "</foreach>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataObjectEntity> getObjectBindingFormByObjectIdList(@Param("ea") String ea, @Param("objectIdList") List<String> objectIdList, @Param("objectType") Integer objectType);

    @Select("SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND object_id = #{objectId} AND object_type = #{objectType}")
    List<CustomizeFormDataObjectEntity> getObjectBindingFormList(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType);


    @Select("<script>"
        + "SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND object_id = #{objectId} AND object_type = #{objectType} ORDER BY create_time DESC"
        + "</script>")
    List<CustomizeFormDataObjectEntity> getObjectBindingFormListByPage(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType, @Param("page") Page page);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND object_id = #{objectId} AND object_type = #{objectType} ORDER BY create_time DESC"
        + "</script>")
    List<CustomizeFormDataObjectEntity> getObjectBindingFormListByPageWithoutPage(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_object WHERE ea = #{ea} AND object_id IN "
        + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<CustomizeFormDataObjectEntity> getObjectBindingFormByObjectIds(@Param("ea") String ea, @Param("objectIds") List<String> objectIds);

    @Delete("DELETE FROM customize_form_data_object WHERE form_id = #{formId}")
    boolean deleteCustomizeFormDataObjectByFormId(@Param("formId") String formId,@Param("ea")String ea);


    @Delete("DELETE FROM customize_form_data_object WHERE id = #{id}")
    boolean deleteCustomizeFormDataObjectById(@Param("id") String id,@Param("ea")String ea);

    @Select("SELECT object_type,count(1) AS object_count \n"
        + "FROM customize_form_data_object\n"
        + "WHERE form_id = #{formId}\n"
        + "GROUP BY object_type")
    List<FormBindObjectStatisticsDTO> bindObjectStatistics(@Param("formId") String formId,@Param("ea")String ea);


    @Select("<script>"
        + "SELECT form_id,array_to_json(array_agg(T)) AS statistics_result FROM \n"
        + "(\n"
        + "SELECT form_id, object_type,count(1) AS object_count \n"
        + "FROM customize_form_data_object\n"
        + "WHERE form_id IN \n"
        + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        +"</foreach>"
        + "GROUP BY form_id,object_type\n"
        + ") AS T\n"
        + "GROUP BY form_id"
        + "</script>")
    List<BranchSearchFormBindObjectStatisticsDTO> branchSearchBindObjectStatistics(@Param("formIds") List<String> formIds,@Param("ea")String ea);

    @Update("update customize_form_data_object set form_style_type = #{formStyleType}, form_button_name = #{formButtonName}, update_by = #{fsUserId}, update_time = now() WHERE id = #{id}")
    int updateFormStyleType(@Param("id") String id, @Param("fsUserId") Integer fsUserId, @Param("formStyleType") Integer formStyleType,  @Param("formButtonName") String formButtonName,@Param("ea")String ea);

    @Select("SELECT object_id FROM customize_form_data_object WHERE ea=#{ea} AND object_type=#{objectType}")
    List<String> getObjectIdsByEaAndObjectType(@Param("ea")String ea, @Param("objectType")Integer objectType);

    @Update("UPDATE customize_form_data_user SET object_id = #{setObjId}, object_type = #{setObjType} WHERE object_id = #{originalObjId} AND object_type = #{originalObjType} AND form_id = #{formId}")
    void updateCustomizeFormDataUserObject(@Param("originalObjType") Integer originalObjType, @Param("originalObjId") String originalObjId,
        @Param("setObjType") Integer setObjType, @Param("setObjId") String setObjId, @Param("formId") String formId,@Param("ea")String ea);
}
