package com.facishare.marketing.provider.advertiser.headlines.ad;

import com.facishare.marketing.common.enums.advertiser.headlines.DouYinFlowType;
import com.facishare.marketing.provider.advertiser.headlines.campaign.LocalCluePageInfo;
import com.facishare.marketing.provider.baidu.IResultData;
import com.google.gson.annotations.SerializedName;
import jetbrick.util.StringUtils;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @IgnoreI18nFile
 */
@Data
@ToString
public class GetHeadlinesLocalClueResult implements IResultData, Serializable {

    /**
     * 分页信息
     */
    @SerializedName("page_info")
    private LocalCluePageInfo pageInfo;

    /**
     * 本地推线索列表
     */
    @SerializedName("list")
    private List<HeadlinesLocalClueResult> headlinesLocalClueResultList;

    @Data
    public static class HeadlinesLocalClueResult implements Serializable{
        /**
         * 线索ID
         */
        @SerializedName("clue_id")
        private String clueId;

        /**
         * 广告主ID
         */
        @SerializedName("local_account_id")
        private String localAccountId;

        /**
         * 广告主名
         */
        @SerializedName("advertiser_name")
        private String advertiserName;

        /**
         * 广告ID
         */
        @SerializedName("promotion_id")
        private Long promotionId;

        /**
         * 广告名称
         */
        @SerializedName("promotion_name")
        private String promotionName;

        /**
         * 内容ID
         */
        @SerializedName("content_id")
        private String contentId;

        /**
         * 线索工具ID
         */
        @SerializedName("tool_id")
        private String toolId;

        /**
         * 线索创建时间，如：2020-04-29 00:00:00
         */
        @SerializedName("create_time_detail")
        private String createTimeDetail;

        /**
         * 线索修改时间，如：2020-04-29 00:00:00
         */
        @SerializedName("modify_time")
        private String modifyTime;

        /**
         * 姓名
         */
        @SerializedName("name")
        private String name;

        /**
         * 客户留资手机号。当团购订单退款后，不可获取明文手机号。 默认值: ""
         */
        @SerializedName("telephone")
        private String telephone;

        /**
         * 性别 可选值:
         *
         * UNKNOWN 未知
         * MALE 男
         * FEMALE 女
         * 默认值: UNKNOWN
         */
        @SerializedName("gender")
        private String gender;

        /**
         * 年龄
         */
        @SerializedName("age")
        private Integer age;

        /**
         * 用户填写省份
         */
        @SerializedName("province_name")
        private String provinceName;

        /**
         * 用户所在省份
         *
         */
        @SerializedName("auto_province_name")
        private String autoProvinceName;

        /**
         * 用户填写城市
         */
        @SerializedName("city_name")
        private String cityName;

        /**
         * 用户所在城市
         *
         */
        @SerializedName("auto_city_name")
        private String autoCityName;

        /**
         * 用户填写区县
         */
        @SerializedName("county_name")
        private String countyName;

        /**
         * 详细地址
         */
        @SerializedName("address")
        private String address;

        /**
         * 商家备注
         */
        @SerializedName("remark")
        private String remark;

        /**
         * 商家表单自定义的字段信息，及其他线索相关信息
         */
        @SerializedName("remark_dict")
        private String remarkDict;

        /**
         * 流量类型 可选值:
         *
         * NATURE 自然流量
         * AD 广告流量
         */
        @SerializedName("flow_type")
        private String flowType;

        /**
         * 互动场景 可选值:
         *
         * SHORT_VIDEO 短视频
         * LIVE_VIDEO 直播
         * HOME_PAGE 企业主页
         * IM_MESSAGE 消息列表
         * GROUPON_ORDER 团购tab
         * ALIEN_CARD 异形卡
         * OTHERS 其他
         */
        @SerializedName("action_type")
        private String actionType;

        /**
         * 留资页面 可选值:
         *
         * POI POI页
         * PRODUCT_DETAIL 商详页
         * OTHER 其他
         */
        @SerializedName("leads_page")
        private String leadsPage;

        /**
         * 留资组件 可选值:
         *
         * FORM 表单提交
         * CONSULT 在线咨询
         * SMARTPHONE 智能电话
         * GROUP_BUYING 团购留资
         */
        @SerializedName("clue_type")
        private String clueType;

        /**
         * 跟进账户类型 可选值:
         *
         * HEAD 总部
         * REGION 区域
         * SINGLE 门店
         */
        @SerializedName("follow_life_account_type")
        private String followLifeAccountType;

        /**
         * 跟进账户ID
         */
        @SerializedName("follow_life_account_id")
        private String followLifeAccountId;

        /**
         * 跟进账户名称
         */
        @SerializedName("follow_life_account_name")
        private String followLifeAccountName;

        /**
         * 订单ID
         */
        @SerializedName("order_id")
        private Long orderId;

        /**
         * 线索阶段 可选值:
         *
         * 0 新线索
         * 1 有意向
         * 2 成交
         * 3 无效
         * 6 已加微信
         * 7 待再次沟通
         * 204 到店
         */
        @SerializedName("effective_state")
        private Integer effectiveState;

        /**
         * 线索阶段名称 可选值:
         *
         * NOT_MARKED 新线索
         * NEED_CALLBACK 有意向
         * CONVERTED 成交
         * INVALID 无效
         * ADD_WEIXIN 已加微信
         * WAIT_CONNECT 待再次沟通
         * ARRIVE_STORE 到店
         */
        @SerializedName("effective_state_name")
        private String effectiveStateName;

        /**
         * 所属人姓名
         */
        @SerializedName("clue_owner_name")
        private String clueOwnerName;

        /**
         * 线索通话状态 可选值:
         *
         * NOT_CALLED 待联系
         * NOT_ANSWERED 未接通
         * SHORT_ANSWERED 已接通
         * ANSWERED 有效沟通
         * DEEP_ANSWERED 深度沟通
         */
        @SerializedName("follow_state_name")
        private String followStateName;

        /**
         * 线索被打上的系统标签，是一个标签项的数组
         */
        @SerializedName("system_tags")
        private List<String> systemTags;

        /**
         * 线索被打上的人工标签，是一个标签项的数组，包括自定义标签和行业标签
         */
        @SerializedName("tags")
        private List<String> tags;

        /**
         * 分配状态 可选值:
         *
         * NOT_ASSIGN 待分配
         * ASSIGNED 已分配
         */
        @SerializedName("allocation_status")
        private String allocationStatus;

        /**
         * 当前线索对应广告的请求id
         */
        @SerializedName("req_id")
        private String reqId;

        /**
         * 意向门店名称
         */
        @SerializedName("intention_life_account_name")
        private String intentionLifeAccountName;

        /**
         * 意向门店id
         */
        @SerializedName("intention_poi_id")
        private String intentionPoiId;

        /**
         * 接待抖音号
         */
        @SerializedName("staff_aweme_id")
        private String staffAwemeId;

        /**
         * 接待抖音昵称
         */
        @SerializedName("staff_nickname")
        private String staffNickname;

        /**
         * 内容创作者抖音号
         */
        @SerializedName("author_aweme_id")
        private String authorAwemeId;

        /**
         * 内容创作者昵称
         */
        @SerializedName("author_nickname")
        private String authorNickname;

        /**
         * 内容创作者角色，包括"商家","职人","达人","星图达人"
         */
        @SerializedName("author_role")
        private String authorRole;

        /**
         * 跟进门店id
         */
        @SerializedName("follow_poi_id")
        private String followPoiId;

        /**
         * 标题id
         */
        @SerializedName("title_id")
        private Long titleId;

        /**
         * 视频id
         */
        @SerializedName("video_id")
        private Long videoId;

        /**
         * 图文id
         */
        @SerializedName("carousel_id")
        private Long carouselId;

        /**
         * 来源职人号抖音号
         */
        @SerializedName("source_craftsman_douyin_id")
        private String sourceCraftsmanDouyinId;

        /**
         * 来源职人号昵称
         */
        @SerializedName("source_craftsman_nickname")
        private String sourceCraftsmanNickname;

        /**
         * 是否私信线索
         */
        @SerializedName("is_private_clue")
        private String isPrivateClue;

        /**
         * 流量入口
         */
        @SerializedName("flow_entrance")
        private String flowEntrance;

        private Map<String, Object> utmDataMap; //解析落地页utm相关字段

        /**
         * 根据配置的映射字段获取对应的字段值
         * @param mankeepFieldName 外部映射字段名称
         * @return 字段值
         */
        public Object getFieldValueByName(String mankeepFieldName) {
            if (StringUtils.isBlank(mankeepFieldName)) {
                return null;
            }
            if(mankeepFieldName.equals("clue_id")) {
                return this.clueId;
            }
            if (mankeepFieldName.equals("local_account_id")) {
                return this.localAccountId;
            }
            if (mankeepFieldName.equals("advertiser_name")) {
                return this.advertiserName;
            }
            if (mankeepFieldName.equals("promotion_id")) {
                return this.promotionId;
            }
            if (mankeepFieldName.equals("promotion_name")) {
                return this.promotionName;
            }
            if (mankeepFieldName.equals("content_id")) {
                return this.contentId;
            }
            if (mankeepFieldName.equals("tool_id")) {
                return this.toolId;
            }
            if (mankeepFieldName.equals("create_time_detail")) {
                return this.createTimeDetail;
            }
            if (mankeepFieldName.equals("modify_time")) {
                return this.modifyTime;
            }
            if (mankeepFieldName.equals("name")) {
                return this.name;
            }
            if (mankeepFieldName.equals("telephone")) {
                return this.telephone;
            }
            if (mankeepFieldName.equals("gender")) {
                switch (this.gender) {
                    case "MALE":
                        return "男";
                    case "FEMALE":
                        return "女";
                    default:
                        return "未知";
                }
            }
            if (mankeepFieldName.equals("age")) {
                return this.age;
            }
            if (mankeepFieldName.equals("province_name")) {
                return this.provinceName;
            }
            if (mankeepFieldName.equals("auto_province_name")) {
                return this.autoProvinceName;
            }
            if (mankeepFieldName.equals("city_name")) {
                return this.cityName;
            }
            if (mankeepFieldName.equals("auto_city_name")) {
                return this.autoCityName;
            }
            if (mankeepFieldName.equals("county_name")) {
                return this.countyName;
            }
            if (mankeepFieldName.equals("address")) {
                return this.address;
            }
            if (mankeepFieldName.equals("remark")) {
                return this.remark;
            }
            if (mankeepFieldName.equals("remark_dict")) {
                return this.remarkDict;
            }
            if ("flow_type".equals(mankeepFieldName)) {
                switch (this.flowType) {
                    case "AD":
                        return "广告流量";
                    case "NATURAL":
                        return "自然流量";
                    default:
                        return null;
                }
            }
            if (mankeepFieldName.equals("action_type")) {
                return this.actionType;
            }
            if (mankeepFieldName.equals("leads_page")) {
                return this.leadsPage;
            }
            if (mankeepFieldName.equals("clue_type")) {
                return this.clueType;
            }
            if (mankeepFieldName.equals("follow_life_account_type")) {
                return this.followLifeAccountType;
            }
            if (mankeepFieldName.equals("follow_life_account_id")) {
                return this.followLifeAccountId;
            }
            if (mankeepFieldName.equals("follow_life_account_name")) {
                return this.followLifeAccountName;
            }
            if (mankeepFieldName.equals("order_id")) {
                return this.orderId;
            }
            if (mankeepFieldName.equals("effective_state")) {
                return this.effectiveState;
            }
            if (mankeepFieldName.equals("effective_state_name")) {
                return this.effectiveStateName;
            }
            if (mankeepFieldName.equals("clue_owner_name")) {
                return this.clueOwnerName;
            }
            if (mankeepFieldName.equals("follow_state_name")) {
                return this.followStateName;
            }
            if (mankeepFieldName.equals("system_tags")) {
                return this.systemTags;
            }
            if (mankeepFieldName.equals("tags")) {
                return this.tags;
            }
            if (mankeepFieldName.equals("allocation_status")) {
                return this.allocationStatus;
            }
            if (mankeepFieldName.equals("req_id")) {
                return this.reqId;
            }
            if (mankeepFieldName.equals("intention_life_account_name")) {
                return this.intentionLifeAccountName;
            }
            if (mankeepFieldName.equals("intention_poi_id")) {
                return this.intentionPoiId;
            }
            if (mankeepFieldName.equals("staff_aweme_id")) {
                return this.staffAwemeId;
            }
            if (mankeepFieldName.equals("staff_nickname")) {
                return this.staffNickname;
            }
            if (mankeepFieldName.equals("author_aweme_id")) {
                return this.authorAwemeId;
            }
            if (mankeepFieldName.equals("author_nickname")) {
                return this.authorNickname;
            }
            if (mankeepFieldName.equals("author_role")) {
                return this.authorRole;
            }
            if (mankeepFieldName.equals("follow_poi_id")) {
                return this.followPoiId;
            }
            if (mankeepFieldName.equals("title_id")) {
                return this.titleId;
            }
            if (mankeepFieldName.equals("video_id")) {
                return this.videoId;
            }
            if (mankeepFieldName.equals("carousel_id")) {
                return this.carouselId;
            }
            if (mankeepFieldName.equals("source_craftsman_douyin_id")) {
                return this.sourceCraftsmanDouyinId;
            }
            if (mankeepFieldName.equals("source_craftsman_nickname")) {
                return this.sourceCraftsmanNickname;
            }
            if (mankeepFieldName.equals("is_private_clue")) {
                return this.isPrivateClue;
            }
            if (mankeepFieldName.equals("flow_entrance")) {
                return this.flowEntrance;
            }
            return null;
        }
    }

}
