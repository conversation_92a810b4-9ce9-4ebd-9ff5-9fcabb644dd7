package com.facishare.marketing.provider.manager;

import com.beust.jcommander.internal.Sets;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;

import com.facishare.marketing.api.arg.qywx.miniapp.QueryDingMiniAppDepartmentArg;
import com.facishare.marketing.api.arg.qywx.miniapp.QueryDingMiniAppStaffArg;
import com.facishare.marketing.api.result.fsBind.GetAddressBookSettingResult;
import com.facishare.marketing.api.result.qywx.miniapp.DingMiniAppDepartmentResult;
import com.facishare.marketing.api.result.qywx.miniapp.DingMiniAppStaffResult;
import com.facishare.marketing.api.service.DingMiniAppDepartmentService;
import com.facishare.marketing.api.service.DingMiniAppStaffService;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.FsAddressBookSettingTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.dao.FsAddressBookSettingDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.FsAddressBookSettingEntity;
import com.facishare.marketing.provider.entity.UserRoleEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerResult.MemberVirtualUserInfo;
import com.facishare.marketing.provider.innerResult.PartnerVirtualUserInfo;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdMapByDepartmentId;
import com.facishare.organization.api.model.employee.arg.*;
import com.facishare.organization.api.model.employee.result.*;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.privilege.api.OutUserPrivilegeRestService;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseEnvironment;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.enterpriserelation2.arg.BatchGetEaByOuterTenantIdArg;
import com.fxiaoke.enterpriserelation2.arg.BatchGetEnterpriseCardsArg;
import com.fxiaoke.enterpriserelation2.arg.ListDownstreamEmployeesByDownstreamOuterTenantIdsArg;
import com.fxiaoke.enterpriserelation2.arg.ListEmployeesByOutTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.arg.ListPublicEmployeeInfosArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.BatchGetEaByOuterTenantIdResult;
import com.fxiaoke.enterpriserelation2.result.EmployeeCardResult;
import com.fxiaoke.enterpriserelation2.result.EmployeeCardSimpleResult;
import com.fxiaoke.enterpriserelation2.result.EnterpriseCardVO;
import com.fxiaoke.enterpriserelation2.result.ListDownstreamEmployeesByDownstreamOuterTenantIdsResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseCardService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.fxiaoke.paasauthrestapi.arg.FindTenantGroupDataByGroupIdArg;
import com.fxiaoke.paasauthrestapi.common.data.PaasTenantGroupContextData;
import com.fxiaoke.paasauthrestapi.result.TenantGroupDataResult;
import com.fxiaoke.paasauthrestapi.service.PaasTenantGroupService;
import com.google.common.collect.Lists;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.organization.adapter.api.permission.enums.role.SystemRoleEnum;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.facishare.organization.adapter.api.permission.model.GetEmployeeIdsByRoleCodeAndAppId.Argument;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Service
public class FsAddressBookManager {

    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private FsAddressBookSettingDAO fsAddressBookSettingDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private DingAuthService dingAuthService;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingMiniAppStaffService dingMiniAppStaffService;
    @Autowired
    private DingMiniAppDepartmentService dingMiniAppDepartmentService;
    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private PublicEmployeeService publicEmployeeService;
    @Autowired
    private PaasTenantGroupService paasTenantGroupService;

    @Value("${partner.appid}")
    private String partnerAppId;
    @Autowired
    private EnterpriseCardService enterpriseCardService;
    @Autowired
    private OutUserPrivilegeRestService outUserPrivilegeRestService;

    @Autowired
    private FxiaokeAccountService fxiaokeAccountService;

    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    @Autowired
    private UserRelationManager userRelationManager;

    /**
     * 固定使用纷享部门搜索的企业ea
     */
    private List<String> needFsCircleIdEa = Lists.newArrayList("sbtjt888");


    public boolean mustUseFxiaokeAddressBook(String ea) {
        FsAddressBookSettingEntity fsAddressBookSettingEntity = fsAddressBookSettingDAO.getFsAddressBookSettingByEa(ea);
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        return (fsAddressBookSettingEntity != null && fsAddressBookSettingEntity.getType().equals(FsAddressBookSettingTypeEnum.FXIAOKE.getType())) || isOpen;
    }

    public String getEmployeeNameById(String ea, Integer fsUserId){
        if (fsUserId == -10000){
            return I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193);
        }
        FSEmployeeMsg employeeMsg = this.getEmployeeInfo(ea, fsUserId);
        if (employeeMsg != null){
            return employeeMsg.getName();
        }
        return "";
    }

    public List<Integer> getEmployeeIdsByAllCircleIds(String ea, List<Integer> circleIds) {
        List<Integer> allUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(circleIds)) {
            if (circleIds.contains(Constant.WHOLE_COMPANY_ID)) {
                List<Integer> employeeIdList = getEmployeeIdsByEa(ea);
                allUserIds.addAll(employeeIdList);
            } else {
                List<Integer> userIdsByDepartmentId = getEmployeeIdsByCircleIds(ea, circleIds);
                allUserIds.addAll(userIdsByDepartmentId);
            }
        }
        return allUserIds;
    }

    public GetAddressBookSettingResult getAddressBookSetting(String ea) {
        GetAddressBookSettingResult result = new GetAddressBookSettingResult();
        //先查询设置表
        FsAddressBookSettingEntity fsAddressBookSettingEntity = fsAddressBookSettingDAO.getFsAddressBookSettingByEa(ea);
        // 查询是否绑定企业微信
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        result.setOpenQywx(qywxCorpAgentConfigEntity != null && qywxCorpAgentConfigEntity.getConfirmStatus() == 1);

        //云环境
        GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
        enterpriseDataArg.setEnterpriseAccount(ea);
        enterpriseDataArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
        Integer env = enterpriseDataResult.getEnterpriseData().getEnv();
        if (env != null && env == EnterpriseEnvironment.ALI.getEnv()){
            result.setOpenDingDing(true);
        }

        //9.0需求,若开启数据隔离则全局使用纷享通讯录
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        if (isOpen) {
            result.setType(FsAddressBookSettingTypeEnum.FXIAOKE.getType());
        }else if (fsAddressBookSettingEntity != null){
            result.setType(fsAddressBookSettingEntity.getType());
        }else if (qywxCorpAgentConfigEntity != null && qywxCorpAgentConfigEntity.getConfirmStatus() == 1){
            result.setType(FsAddressBookSettingTypeEnum.QYWX.getType());
        }else {
            //默认通讯录 如果是钉钉云，默认用钉钉通讯录，兼容钉钉云的早期企业
            if (env == EnterpriseEnvironment.ALI.getEnv()) {
                result.setType(FsAddressBookSettingTypeEnum.DINGDING.getType());
            }else {
                result.setType(FsAddressBookSettingTypeEnum.FXIAOKE.getType());
            }
        }

        return result;
    }

    public List<Integer> getEmployeeIdsByCircleIds(String ea, List<Integer> circleIds) {
        List<Integer> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(circleIds)) {
            return result;
        }

        //钉钉通讯录
        if (dingManager.isDingAddressbook(ea)) {
            Set<Integer> dingUserSet = dingManager.getEmployeeIdStatic(ea, null, circleIds);
            if (CollectionUtils.isEmpty(dingUserSet)) {
                return result;
            }
            result.addAll(dingUserSet);
            return result;
        }

        circleIds = circleIds.stream().filter(data -> !data.equals(Constant.WHOLE_COMPANY_ID)).collect(Collectors.toList());
        List<Integer> fsCircleIds = Lists.newArrayList();
        List<Integer> qywxCircleIds = Lists.newArrayList();
        if (needFsCircleIdEa.contains(ea)) {
            fsCircleIds = circleIds;
        } else {
            // 区分企业微信部门与微信部门
            fsCircleIds = circleIds.stream().filter(data -> !QywxUserConstants.isQywxDepartment(data)).collect(Collectors.toList());
            qywxCircleIds = circleIds.stream().filter(QywxUserConstants::isQywxDepartment).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(qywxCircleIds)) {
            for (int i = 0; i < qywxCircleIds.size(); i++) {
                qywxCircleIds.set(i, qywxCircleIds.get(i) - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
            }
            result.addAll(qywxUserManager.getFsUserIdByQywxDepartment(ea, qywxCircleIds));
        }
        if (CollectionUtils.isNotEmpty(fsCircleIds)) {
            BatchGetEmployeeIdMapByDepartmentId.Arg arg = new BatchGetEmployeeIdMapByDepartmentId.Arg();
            arg.setDepartmentIds(circleIds);
            arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            arg.setRunStatus(RunStatus.ACTIVE);
            arg.setIncludeLowDepartment(true);
            BatchGetEmployeeIdMapByDepartmentId.Result employeeIdResult = employeeProviderService.batchGetEmployeeIdMapByDepartmentId(arg);
            if (employeeIdResult != null && employeeIdResult.getEmployeeMap() != null){
                for (Map.Entry<Integer, List<Integer>> entry : employeeIdResult.getEmployeeMap().entrySet()){
                    if (CollectionUtils.isNotEmpty(entry.getValue())){
                        result.addAll(entry.getValue());
                    }
                }
            }
        }

        return result.stream().distinct().collect(Collectors.toList());
    }

    public FSEmployeeMsg getEmployeeInfo(String ea, Integer fsUserId) {
        //添加钉钉逻辑
        if (dingManager.isDingAddressbook(ea)){
            return dingManager.getStaffDetail(ea,fsUserId);
        }
        if (QywxUserConstants.isMemberVirtualUserId(fsUserId) || QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
            UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, fsUserId);
            if (userRelationEntity == null) {
                return null;
            }
            FSEmployeeMsg fsEmployeeMsg = new FSEmployeeMsg();
            fsEmployeeMsg.setEmployeeId(fsUserId);
            fsEmployeeMsg.setName(userRelationEntity.getUserName());
            fsEmployeeMsg.setMobile(userRelationEntity.getMobile());
            fsEmployeeMsg.setEmail(userRelationEntity.getEmail());
            return fsEmployeeMsg;
        }
        boolean useFxiaokeAddressBook = mustUseFxiaokeAddressBook(ea);
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (QywxUserConstants.isVirtualUserId(fsUserId) || (qywxMiniappConfigEntity != null && !useFxiaokeAddressBook)) {
            return qywxUserManager.getQywxInfoByEaAndUserId(ea, fsUserId);
        }
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEmployeeIds(Collections.singletonList(fsUserId));
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ACTIVE);
        batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        Map<Integer, FSEmployeeMsg> map = createFSEmployeeMsg(ea, batchGetEmployeeDtoResult.getEmployeeDtos());
        return map.get(fsUserId);
    }

    public Integer getFsUserIdByPhone(String ea, String phone) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(phone)) {
            return null;
        }
        GetEmployeesDtoByEnterpriseAndMobileArg arg = new GetEmployeesDtoByEnterpriseAndMobileArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setMobile(phone);
        GetEmployeesDtoByEnterpriseAndMobileResult getEmployeesDtoByEnterpriseAndMobileResult = employeeProviderService.getEmployeeByEnterpriseAndMobile(arg);
        if (getEmployeesDtoByEnterpriseAndMobileResult != null && getEmployeesDtoByEnterpriseAndMobileResult.getEmployeeDto() != null) {
            return getEmployeesDtoByEnterpriseAndMobileResult.getEmployeeDto().getEmployeeId();
        }
        return null;
    }

    public List<EmployeeDto> getFsUserIdByPhoneList(String ea, List<String> mobileList, boolean includeInvalidUser) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(mobileList)) {
            return Lists.newArrayList();
        }
        BatchGetEmployeesDtoByEnterpriseAndMobileArg arg = new BatchGetEmployeesDtoByEnterpriseAndMobileArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setMobiles(mobileList);
        BatchGetEmployeesDtoByEnterpriseAndMobileResult batchGetEmployeesDtoByEnterpriseAndMobileResult = employeeProviderService.batchGetEmployeeByEnterpriseAndMobile(arg);
        if (batchGetEmployeesDtoByEnterpriseAndMobileResult != null && CollectionUtils.isNotEmpty(batchGetEmployeesDtoByEnterpriseAndMobileResult.getEmployeeDto())) {
            if (includeInvalidUser) {
                return batchGetEmployeesDtoByEnterpriseAndMobileResult.getEmployeeDto();
            }
            return batchGetEmployeesDtoByEnterpriseAndMobileResult.getEmployeeDto().stream().filter(employeeDto -> employeeDto.getStatus() == EmployeeEntityStatus.NORMAL).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public List<Integer> getEmployeeIdsByEa(String ea) {
        boolean useFxiaokeAddressBook = mustUseFxiaokeAddressBook(ea);
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity != null && !useFxiaokeAddressBook) {
            List<String> qywxUserId = qywxManager.queryAllStaffId(ea);
            Map<String, Integer> userMap = qywxUserManager.getFsUserIdByQyWxInfo(ea, qywxUserId, true, true);
            return MapUtils.isNotEmpty(userMap) ? Lists.newArrayList(userMap.values()) : Lists.newArrayList();
        }

        GetAllEmployeeIdsArg arg = new GetAllEmployeeIdsArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setRunStatus(RunStatus.ACTIVE);
        GetAllEmployeeIdsResult getAllEmployeesDtoResult = employeeProviderService.getAllEmployeeIds(arg);

        return getAllEmployeesDtoResult.getEmployeeIds();
    }

    public Map<Integer, FSEmployeeMsg> getEmployeeInfoByEa(String ea) {
        boolean useFxiaokeAddressBook = mustUseFxiaokeAddressBook(ea);
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity != null && !useFxiaokeAddressBook) {
            List<Integer> userIds = qywxVirtualFsUserManager.queryQyVirtualIdByEa(ea);
            if (CollectionUtils.isEmpty(userIds)) {
                userIds = Lists.newArrayList();
            }
            // 还需要找到纷享员工id
            GetAllEmployeeIdsArg getAllEmployeeIdsArg = new GetAllEmployeeIdsArg();
            getAllEmployeeIdsArg.setRunStatus(RunStatus.ALL);
            getAllEmployeeIdsArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            GetAllEmployeeIdsResult getAllEmployeeIdsResult = employeeProviderService.getAllEmployeeIds(getAllEmployeeIdsArg);
            if (getAllEmployeeIdsResult != null && CollectionUtils.isNotEmpty(getAllEmployeeIdsResult.getEmployeeIds())) {
                userIds.addAll(getAllEmployeeIdsResult.getEmployeeIds());
            }
            userIds = userIds.stream().distinct().collect(Collectors.toList());
            return getEmployeeInfoByUserIds(ea, userIds, true);
        }
        GetAllEmployeesDtoArg getAllEmployeesDtoArg = new GetAllEmployeesDtoArg();
        getAllEmployeesDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        getAllEmployeesDtoArg.setRunStatus(RunStatus.ALL);
        GetAllEmployeesDtoResult getAllEmployeesDtoResult = employeeProviderService.getAllEmployees(getAllEmployeesDtoArg);
        return createFSEmployeeMsg(ea, getAllEmployeesDtoResult.getEmployeeDtoList());
    }

    public Map<Integer, FSEmployeeMsg> getFsEmployeeInfoByUserIds(String ea, List<Integer> fsUserIds, boolean needAllData) {
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMapResult = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(fsUserIds)) {
            return fsEmployeeMsgMapResult;
        }
        fsUserIds = fsUserIds.stream().distinct().collect(Collectors.toList());
        List<Integer> fsUserList = fsUserIds.stream().filter(data -> !QywxUserConstants.isVirtualUserId(data)).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> virtualUserList = fsUserIds.stream().filter(QywxUserConstants::isVirtualUserId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fsUserList)) {
            BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
            batchGetEmployeeDtoArg.setEmployeeIds(fsUserIds);
            batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            batchGetEmployeeDtoArg.setRunStatus(needAllData ? RunStatus.ALL : RunStatus.ACTIVE);
            BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
            fsEmployeeMsgMapResult.putAll(createFSEmployeeMsg(ea, batchGetEmployeeDtoResult.getEmployeeDtos()));
        }
        if (CollectionUtils.isNotEmpty(virtualUserList)) {
            fsEmployeeMsgMapResult.putAll(qywxUserManager.getQywxInfoByEaAndUserId(ea, virtualUserList));
        }
        return fsEmployeeMsgMapResult;
    }

    public Map<Integer, FSEmployeeMsg> getEmployeeInfoByUserIds(String ea, List<Integer> fsUserIds, boolean needAllData) {
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMapResult = Maps.newHashMap();
        boolean useFxiaokeAddressBook = mustUseFxiaokeAddressBook(ea);
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(fsUserIds)) {
            return fsEmployeeMsgMapResult;
        }
        fsUserIds = fsUserIds.stream().distinct().collect(Collectors.toList());
        List<Integer> fsUserList = fsUserIds.stream().filter(data -> !QywxUserConstants.isVirtualUserId(data)).filter(Objects::nonNull).collect(Collectors.toList());
        // 类型为员工/伙伴的会员
        Set<Integer> userRelationUserIdSet = Sets.newHashSet();

        Set<Integer> virtualUserSet = Sets.newHashSet();
        for (Integer fsUserId : fsUserIds) {
            if (QywxUserConstants.isMemberVirtualUserId(fsUserId) || QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
                userRelationUserIdSet.add(fsUserId);
            } else if (QywxUserConstants.isVirtualUserId(fsUserId)) {
                virtualUserSet.add(fsUserId);
            }
        }
        List<Integer> virtualUserList = Lists.newArrayList(virtualUserSet);

        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByFsUserIdList(ea, Lists.newArrayList(userRelationUserIdSet));
        userRelationEntityList.forEach(userRelationEntity -> {
            FSEmployeeMsg fsEmployeeMsg = new FSEmployeeMsg();
            fsEmployeeMsg.setEmployeeId(userRelationEntity.getFsUserId());
            fsEmployeeMsg.setName(userRelationEntity.getUserName());
            fsEmployeeMsg.setMobile(userRelationEntity.getMobile());
            fsEmployeeMsg.setEmail(userRelationEntity.getEmail());
            fsEmployeeMsgMapResult.put(userRelationEntity.getFsUserId(), fsEmployeeMsg);
        });
        // 若开通企业微信员工信息取名片信息
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity != null && !useFxiaokeAddressBook) {
            fsEmployeeMsgMapResult.putAll(qywxUserManager.getQywxInfoByEaAndUserId(ea, fsUserIds));
            return fsEmployeeMsgMapResult;
        }
        //若开通了钉钉,则获取钉钉通讯录信息
        if (dingManager.isDingAddressbook(ea)){
            fsEmployeeMsgMapResult.putAll(getDingInfoByEaAndUserId(ea,fsUserIds));
            return fsEmployeeMsgMapResult;
        }
        //纷享通讯录
        if (CollectionUtils.isNotEmpty(fsUserList)) {
            BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
            batchGetEmployeeDtoArg.setEmployeeIds(fsUserIds);
            batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            batchGetEmployeeDtoArg.setRunStatus(needAllData ? RunStatus.ALL : RunStatus.ACTIVE);
            BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
            fsEmployeeMsgMapResult.putAll(createFSEmployeeMsg(ea, batchGetEmployeeDtoResult.getEmployeeDtos()));
        }
        if (CollectionUtils.isNotEmpty(virtualUserList)) {
            fsEmployeeMsgMapResult.putAll(qywxUserManager.getQywxInfoByEaAndUserId(ea, virtualUserList));
        }
        return fsEmployeeMsgMapResult;
    }

    public Map<Integer,FSEmployeeMsg> getDingInfoByEaAndUserId(String ea, List<Integer> fsUserIds) {
        if (CollectionUtils.isEmpty(fsUserIds)) {
            return Maps.newHashMap();
        }
        Map<Integer, FSEmployeeMsg> employeeMsgMap = Maps.newHashMap();
        Map<Integer, DingMiniAppDepartmentResult> departmentMap;
        QueryDingMiniAppDepartmentArg deptArg = new QueryDingMiniAppDepartmentArg();
        deptArg.setFsEa(ea);
        com.facishare.marketing.common.result.Result<List<DingMiniAppDepartmentResult>> miniAppDepartmentResult = dingMiniAppDepartmentService.queryDingMiniAppDepartment(deptArg);
        if (!miniAppDepartmentResult.isSuccess() || CollectionUtils.isEmpty(miniAppDepartmentResult.getData())){
            departmentMap = null;
        } else {
            departmentMap = miniAppDepartmentResult.getData().stream().collect(Collectors.toMap(DingMiniAppDepartmentResult::getDeptId, data -> data, (v1, v2) -> v1));
        }
        //List<Integer> deptIds = miniAppDepartmentResult.getData().stream().map(DingMiniAppDepartmentResult::getDeptId).collect(Collectors.toList());
        QueryDingMiniAppStaffArg arg = new QueryDingMiniAppStaffArg();
        arg.setFsEa(ea);
        com.facishare.marketing.common.result.Result<List<DingMiniAppStaffResult>> staffResult = dingMiniAppStaffService.queryDingMiniAppStaff(arg);
        if (!staffResult.isSuccess() || org.apache.commons.collections.CollectionUtils.isEmpty(staffResult.getData())){
            return employeeMsgMap;
        }
        List<DingMiniAppStaffResult> staffResultData = staffResult.getData();
        List<QywxVirtualFsUserEntity> fsUserEntityList = qywxVirtualFsUserManager.queryQyUserByVirtualInfos(ea, fsUserIds);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(fsUserEntityList)){
            return employeeMsgMap;
        }
        Map<Integer, DingMiniAppDepartmentResult> departmentResultMap = departmentMap;
        Map<String, DingMiniAppStaffResult> staffResultMap = staffResultData.stream().collect(Collectors.toMap(DingMiniAppStaffResult::getUserid, Function.identity(),(key1, key2)->key2));
        fsUserEntityList.stream().filter(qywxVirtualFsUserEntity -> staffResultMap.containsKey(qywxVirtualFsUserEntity.getQyUserId())).forEach(qywxVirtualFsUserEntity -> {
            FSEmployeeMsg fsEmployeeMsg = new FSEmployeeMsg();
            fsEmployeeMsg.setEmployeeId(qywxVirtualFsUserEntity.getUserId());
            fsEmployeeMsg.setName(staffResultMap.get(qywxVirtualFsUserEntity.getQyUserId()).getName());
            fsEmployeeMsg.setProfileImage(staffResultMap.get(qywxVirtualFsUserEntity.getQyUserId()).getAvatar());
            List<Integer> departmentList = staffResultMap.get(qywxVirtualFsUserEntity.getQyUserId()).getDeptIdList();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(departmentList) && departmentResultMap != null) {
                if (departmentList.get(0) != 1){
                    fsEmployeeMsg.setDepartment(departmentResultMap.get(departmentList.get(0)).getName());
                } else if (departmentList.size() > 1) {
                    fsEmployeeMsg.setDepartment(departmentResultMap.get(departmentList.get(departmentList.size()-1)).getName());
                }
            }
            employeeMsgMap.put(qywxVirtualFsUserEntity.getUserId(), fsEmployeeMsg);
        });
        return employeeMsgMap;
    }

    /**
     * 获取纷享账号详情（无企业微信判断）
     * @param ea
     * @param fsUserIds
     * @return
     */
    public Map<Integer, FSEmployeeMsg> getEmployeeInfoByUserIdsWithoutQywx(String ea, List<Integer> fsUserIds, boolean needAllData) {
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMapResult = Maps.newHashMap();
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEmployeeIds(fsUserIds);
        batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        batchGetEmployeeDtoArg.setRunStatus(needAllData ? RunStatus.ALL : RunStatus.ACTIVE);
        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        fsEmployeeMsgMapResult.putAll(createFSEmployeeMsg(ea, batchGetEmployeeDtoResult.getEmployeeDtos()));
        return fsEmployeeMsgMapResult;
    }

    public List<Integer> getEnterpriseAdminList(String ea, Integer operatorUserId) {
        Argument argument = new Argument();
        argument.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        argument.setCurrentEmployeeId(operatorUserId);
        argument.setAppId(SystemRoleEnum.SYSTEM_MANAGEMENT.getAppId());
        argument.setRoleCode(SystemRoleEnum.SYSTEM_MANAGEMENT.getRoleCode());
        return permissionService.getEmployeeIdsByRoleCodeAndAppId(argument).getEmployeeIds();
    }

    private Map<Integer, FSEmployeeMsg> createFSEmployeeMsg(String ea, List<EmployeeDto> employeeDtoList) {
        Map<Integer, FSEmployeeMsg> map = employeeDtoList.stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, e -> {
            FSEmployeeMsg fsEmployeeMsg = BeanUtil.copy(e, FSEmployeeMsg.class);
            return fsEmployeeMsg;
        }, (v1, v2) -> v1));

        List<Integer> allCircleIds = map.values().stream().filter(Objects::nonNull).map(FSEmployeeMsg::getMainDepartmentId).filter(Objects::nonNull).collect(Collectors.toList());
        BatchGetDepartmentDtoArg batchGetDepartmentDtoArg = new BatchGetDepartmentDtoArg();
        batchGetDepartmentDtoArg.setDepartmentIds(allCircleIds);
        batchGetDepartmentDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        BatchGetDepartmentDtoResult batchGetDepartmentDtoResult = departmentProviderService.batchGetDepartmentDto(batchGetDepartmentDtoArg);

        Map<Integer, String> circleMap = batchGetDepartmentDtoResult.getDepartments().stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getName));
        map.forEach((k, v) -> {
            if (v != null && circleMap.get(v.getMainDepartmentId()) != null) {
                v.setDepartment(circleMap.get(v.getMainDepartmentId()));
            }
        });

        return map;
    }

    /**
     * 查询纷享用户信息
     * @param ea
     * @param userId
     * @return
     */
    public EmployeeDto getFxEmployeeByUserId(String ea, Integer userId) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
            getEmployeeDtoArg.setEnterpriseId(ei);
            getEmployeeDtoArg.setEmployeeId(userId);
            GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
            if (employeeDto != null) {
                return employeeDto.getEmployeeDto();
            }
        } catch (Exception e) {
            log.warn("FsAddressBookManager -> getFxEmployeeByUserId error, ea:{}, userId:{}", ea, userId, e);
        }
        return null;
    }


    public boolean isActiveStatus(String ea, Integer userId) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
            getEmployeeDtoArg.setEnterpriseId(ei);
            getEmployeeDtoArg.setEmployeeId(userId);
            GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
            if (employeeDto != null && employeeDto.getEmployeeDto() != null && employeeDto.getEmployeeDto().getStatus().getValue() == EmployeeEntityStatus.NORMAL.getValue()) {
                return true;
            }
        } catch (Exception e) {
            log.warn("FsAddressBookManager -> getFxEmployeeByUserId error, ea:{}, userId:{}", ea, userId, e);
        }

        return false;
    }



    //    private Map<Integer, FSEmployeeMsg> createFSEmployeeMsgV2(String ea, List<EmployeeDto> employeeDtoList) {
//        log.info("FsAddressBookManager -> createFSEmployeeMsg, employeeDtoList:{}", employeeDtoList);
//        Map<Integer, FSEmployeeMsg> map = employeeDtoList.stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, e -> {
//            FSEmployeeMsg fsEmployeeMsg = BeanUtil.copy(e, FSEmployeeMsg.class);
//            return fsEmployeeMsg;
//        }, (v1, v2) -> v1));
//        List<Integer> allCircleIds = map.values().stream().filter(Objects::nonNull).map(FSEmployeeMsg::getMainDepartmentId).filter(Objects::nonNull).collect(Collectors.toList());
//
//
//        BatchGetUpperDepartmentDtoMap.Arg dtosArg = new BatchGetUpperDepartmentDtoMap.Arg();
//        dtosArg.setDepartmentIds(allCircleIds);
//        dtosArg.setRecursive(false);
//        dtosArg.setSelf(true);
//        dtosArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
//        BatchGetUpperDepartmentDtoMap.Result dtoMap = departmentProviderService.batchGetUpperDepartmentDtoMap(dtosArg);
//        Map<Integer, String> nameMap = dtoMap.getDepartmentDtoMap().entrySet().stream().collect(Collectors.toMap(o -> o.getKey(), v -> v.getValue().getName(), (k1, k2) -> k1));
//        Map<Integer, List<Integer>> upperDepartmentIdMap = dtoMap.getUpperDepartmentIdMap();
//        map.forEach((k, v) -> {
//            Integer mainDepartmentId = v.getMainDepartmentId();
//            if (mainDepartmentId != null && upperDepartmentIdMap.containsKey(mainDepartmentId)) {
//                List<String> nameList = upperDepartmentIdMap.get(v.getMainDepartmentId()).stream().filter(o -> o != 999999).map(u -> nameMap.get(u)).collect(Collectors.toList());
//                v.setDepartment(String.join(">", nameList));
//            }
//        });
//
//        return map;
//    }
    public Map<String, Integer> getEmployeeByNames(String ea, List<String> names) {
        Map<String, Integer> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(names)) {
            return resultMap;
        }
        GetEmployeeByNamesArg arg = new GetEmployeeByNamesArg();
        arg.setNames(names);
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        GetEmployeeByNamesResult employeeByNames = employeeProviderService.getEmployeeByNames(arg);
        if (employeeByNames == null || CollectionUtils.isEmpty(employeeByNames.getEmployeeDtos())) {
            return resultMap;
        }
        employeeByNames.getEmployeeDtos().forEach(employeeDto -> {
            resultMap.put(employeeDto.getName(), employeeDto.getEmployeeId());
        });
        return resultMap;
    }


    /**
     * 获取下游企业id
     * @param tenantIDList
     * @param tenantGroupIdList
     * @param ea
     * @param fsUserId
     * @param
     * @return
     */
    public List<String> getOutTenantId(List<String> tenantIDList,List<String> tenantGroupIdList, String ea, Integer fsUserId) {
        Set<String> tenantIDSet = new HashSet<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantGroupIdList)) {
            //通过企业组查询企业id
            com.fxiaoke.paasauthrestapi.common.data.HeaderObj groupHeadObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
            FindTenantGroupDataByGroupIdArg findTenantGroupDataByGroupIdArg = new FindTenantGroupDataByGroupIdArg();
            PaasTenantGroupContextData paasTenantGroupContextData = new PaasTenantGroupContextData();
            paasTenantGroupContextData.setTenantId("1");
            paasTenantGroupContextData.setUserId(fsUserId == null ? "-10000" : fsUserId.toString());
            findTenantGroupDataByGroupIdArg.setContext(paasTenantGroupContextData);
            com.fxiaoke.paasauthrestapi.common.result.Result<List<TenantGroupDataResult>> tenantGroupDataByGroupId;
            for (String gpID : tenantGroupIdList) {
                findTenantGroupDataByGroupIdArg.setGroupId(gpID);
                tenantGroupDataByGroupId = paasTenantGroupService.findTenantGroupDataByGroupId(groupHeadObj, findTenantGroupDataByGroupIdArg);
                if (null != tenantGroupDataByGroupId && tenantGroupDataByGroupId.getErrCode() == 0 && "成功".equals(tenantGroupDataByGroupId.getErrMessage()) && tenantGroupDataByGroupId.getResult().size() > 0) {
                    tenantIDSet.addAll(tenantGroupDataByGroupId.getResult().stream().map(r -> r.getOutTenantId()).collect(Collectors.toSet()));
                }
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantIDList)) {
            tenantIDSet.addAll(tenantIDList);
        }
        return new ArrayList<>(tenantIDSet);
    }

    /**
     * 获取下游企业名称
     * @param complanyIds
     * @return
     */

    public Map<String, String> getCompanyIdToCompanyMap(List<String> complanyIds) {
        Map<String, String> res = new HashMap<>();
        if (null == complanyIds || complanyIds.size() == 0) {
            return res;
        }
        List<Long> outerTenantIdList = complanyIds.stream().map(sp -> Long.valueOf(sp)).collect(Collectors.toList());
        HeaderObj headerMap = HeaderObj.newInstance(99);
        headerMap.setAppId(partnerAppId);
        BatchGetEnterpriseCardsArg batchGetEnterpriseCardsArg = new BatchGetEnterpriseCardsArg();
        batchGetEnterpriseCardsArg.setOuterTenantIds(outerTenantIdList);
        RestResult<List<EnterpriseCardVO>> enterpriseCards = enterpriseCardService.batchGetEnterpriseCards(headerMap, batchGetEnterpriseCardsArg);
        if (enterpriseCards.isSuccess() && null != enterpriseCards.getData()) {
            for (EnterpriseCardVO temp : enterpriseCards.getData()) {
                res.put(String.valueOf(temp.getOuterTenantId()), temp.getEnterpriseName());
            }
        }
        return res;
    }

    public Map<Integer,String> getOutUserInfoByUpstreamAndUserId(String upstreamEa, List<Integer> fsUserIdList){
        Map<Integer,String> res = new HashMap<>();
        if(CollectionUtils.isEmpty(fsUserIdList)){
            return res;
        }
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByFsUserIdList(upstreamEa, fsUserIdList);
        if (CollectionUtils.isEmpty(userRelationEntityList)) {
            return res;
        }
        userRelationEntityList.forEach(e -> res.put(e.getFsUserId(), e.getUserName()));
        return res;
    }


    /**
     * 获取所有对接人的id
     * @param
     * @param ea
     * @param fsUserId
     * @param outUserIdToOutTannetIdMap
     * @return
     */
    public List<Integer> getDockUserId(List<String> tenantIDList,List<String> tenantGroupIdList, String ea, Integer fsUserId, Map<Integer, String> outUserIdToOutTannetIdMap) {
        Set<String> tenantIDSet = new HashSet<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantGroupIdList)) {
            //通过企业组查询企业id
            com.fxiaoke.paasauthrestapi.common.data.HeaderObj groupHeadObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
            FindTenantGroupDataByGroupIdArg findTenantGroupDataByGroupIdArg = new FindTenantGroupDataByGroupIdArg();
            PaasTenantGroupContextData paasTenantGroupContextData = new PaasTenantGroupContextData();
            paasTenantGroupContextData.setTenantId(eieaConverter.enterpriseAccountToId(ea)+"");
            paasTenantGroupContextData.setUserId(fsUserId == null ? "-10000" : fsUserId.toString());
            findTenantGroupDataByGroupIdArg.setContext(paasTenantGroupContextData);
            com.fxiaoke.paasauthrestapi.common.result.Result<List<TenantGroupDataResult>> tenantGroupDataByGroupId;
            for (String gpID : tenantGroupIdList) {
                findTenantGroupDataByGroupIdArg.setGroupId(gpID);
                tenantGroupDataByGroupId = paasTenantGroupService.findTenantGroupDataByGroupId(groupHeadObj, findTenantGroupDataByGroupIdArg);
                if (null != tenantGroupDataByGroupId && tenantGroupDataByGroupId.getErrCode() == 0 && tenantGroupDataByGroupId.getResult().size() > 0) {
                    tenantIDSet.addAll(tenantGroupDataByGroupId.getResult().stream().map(r -> r.getOutTenantId()).collect(Collectors.toSet()));
                }
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantIDList)) {
            tenantIDSet.addAll(tenantIDList);
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(tenantIDSet)) {
            return Lists.newArrayList();
        }
        //获取有角色的对接人id 现接口不支持,后续加
//        Set<String> roleOutUserIdSet = new HashSet<>();
//        for (String tenantId : tenantIDSet) {
//            PrivilegeContext.PrivilegeContextBuilder builder = PrivilegeContext.builder();
//            PrivilegeContext context = builder.appId("CRM").outIdentityType("1").tenantId(eieaConverter.enterpriseAccountToId(ea)).outerTenantId(tenantId).operatorId(-10000).build();
//            GetUsersByRoleCodes.Argument arg = new GetUsersByRoleCodes.Argument();
//            arg.setRoleCodes(Arrays.asList("er_enterprise"));
//            GetUsersByRoleCodes.Result usersByRoleCodes = outUserPrivilegeRestService.getUsersByRoleCodes(context, arg);
//            if (null != usersByRoleCodes && null != usersByRoleCodes.getUserRoleList() && usersByRoleCodes.getUserRoleList().size() > 0) {
//                usersByRoleCodes.getUserRoleList().forEach(u -> roleOutUserIdSet.add(u.getUserId()));
//            }
//        }
        List<Long> groupIDList = tenantIDSet.stream().map(id -> Long.valueOf(id)).collect(Collectors.toList());
        //通过企业id查询对接人的id
        /*com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
        headerObj.setAppId(partnerAppId);
        ListDownstreamEmployeesByDownstreamOuterTenantIdsArg listUserArg = new ListDownstreamEmployeesByDownstreamOuterTenantIdsArg();
        listUserArg.setUpstreamEa(ea);
        listUserArg.setDownstreamOuterTenantIds(groupIDList);
//        根据下游外部企业账号获取对接人id
        RestResult<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> listUserResult = publicEmployeeService.listDownstreamEmployeesByDownstreamOuterTenantIds(headerObj, listUserArg);*/
        Result<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> listUserResult = listDownstreamEmployeesByOuterTenantIds(ea, groupIDList);
        List<Integer> userList = new ArrayList<>();
        if (listUserResult.isSuccess() && null != listUserResult.getData()) {
            for (ListDownstreamEmployeesByDownstreamOuterTenantIdsResult temp : listUserResult.getData()) {
                outUserIdToOutTannetIdMap.put(temp.getOuterUid().intValue(), null == temp.getOuterTenantId() ? "" : temp.getOuterTenantId().toString());
                userList.add(temp.getOuterUid().intValue());
            }
        }
        return userList;
    }

    /**
     * 查询下游互联用户
     * @param upstreamEA
     * @param outerTenantIds
     * @return
     */
    public Result<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> listDownstreamEmployeesByOuterTenantIds(String upstreamEA, List<Long> outerTenantIds){
        int pageSize = 1000; //每次查询条数
        int maxRecord = 10000; //最多取10000条
        int pageTotal = maxRecord / pageSize;
        ArrayList<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult> list = Lists.newArrayList();
        for (int i = 0; i < pageTotal; i++) {
            com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(upstreamEA));
            headerObj.setAppId(partnerAppId);
            ListDownstreamEmployeesByDownstreamOuterTenantIdsArg listUserArg = new ListDownstreamEmployeesByDownstreamOuterTenantIdsArg();
            listUserArg.setUpstreamEa(upstreamEA);
            listUserArg.setDownstreamOuterTenantIds(outerTenantIds);
            listUserArg.setOffset(i * pageSize);
            listUserArg.setLimit(pageSize);
            RestResult<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> listUserResult = publicEmployeeService.listDownstreamEmployeesByDownstreamOuterTenantIds(headerObj, listUserArg);
            if (!listUserResult.isSuccess()) {
                log.info("publicEmployeeService -> listDownstreamEmployeesByDownstreamOuterTenantIds error, result:{}", listUserResult);
                return Result.newError(listUserResult.getErrCode(), listUserResult.getErrMsg());
            }

            if (listUserResult.getData() == null) {
                continue;
            }

            List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult> data = listUserResult.getData();
            list.addAll(data);
            int size = data.size();
            if (size < pageSize) { // 说明数据已经取完了
                break;
            }
        }

        log.info("FsAddressBookManager -> listDownstreamEmployeesByOuterTenantIds success upstreamEA:{}, outerTenantIds:{}, result:{}", upstreamEA, outerTenantIds, list);
        return Result.newSuccess(list);
    }


    /**
     * 获取优惠券所有对接人的id
     * @param
     * @param ea
     * @param fsUserId
     * @param outUserIdToOutTannetIdMap
     * @return
     */
    public List<Integer> getCouponDockUserId(List<String> tenantIDList,List<String> tenantGroupIdList, String ea, Integer fsUserId, Map<Integer, String> outUserIdToOutTannetIdMap) {
        Set<String> tenantIDSet = new HashSet<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantGroupIdList)) {
            //通过企业组查询企业id
            com.fxiaoke.paasauthrestapi.common.data.HeaderObj groupHeadObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
            FindTenantGroupDataByGroupIdArg findTenantGroupDataByGroupIdArg = new FindTenantGroupDataByGroupIdArg();
            PaasTenantGroupContextData paasTenantGroupContextData = new PaasTenantGroupContextData();
            paasTenantGroupContextData.setTenantId(eieaConverter.enterpriseAccountToId(ea)+"");
            paasTenantGroupContextData.setUserId(fsUserId == null ? "-10000" : fsUserId.toString());
            findTenantGroupDataByGroupIdArg.setContext(paasTenantGroupContextData);
            com.fxiaoke.paasauthrestapi.common.result.Result<List<TenantGroupDataResult>> tenantGroupDataByGroupId;
            for (String gpID : tenantGroupIdList) {
                findTenantGroupDataByGroupIdArg.setGroupId(gpID);
                tenantGroupDataByGroupId = paasTenantGroupService.findTenantGroupDataByGroupId(groupHeadObj, findTenantGroupDataByGroupIdArg);
                if (null != tenantGroupDataByGroupId && tenantGroupDataByGroupId.getErrCode() == 0 && tenantGroupDataByGroupId.getResult().size() > 0) {
                    tenantIDSet.addAll(tenantGroupDataByGroupId.getResult().stream().map(r -> r.getOutTenantId()).collect(Collectors.toSet()));
                }
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tenantIDList)) {
            tenantIDSet.addAll(tenantIDList);
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(tenantIDSet)) {
            return Lists.newArrayList();
        }
        List<Long> outTernatIds = new ArrayList<>();
        tenantIDList.forEach(ternatId ->{
            outTernatIds.add(Long.parseLong(ternatId));
        });
        //获取下游企业的ea
        Map<Long,String> outerTenantId2EaMap = new HashMap<>();
        BatchGetEaByOuterTenantIdArg batchTenantIdArg = new BatchGetEaByOuterTenantIdArg();
        batchTenantIdArg.setOuterTenantIds(outTernatIds);
        RestResult<BatchGetEaByOuterTenantIdResult> outerTenantIdResultRestResult = fxiaokeAccountService.batchGetEaByOuterTenantId(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea)), batchTenantIdArg);
        if (outerTenantIdResultRestResult.isSuccess() &&  null != outerTenantIdResultRestResult.getData()) {
            outerTenantId2EaMap = outerTenantIdResultRestResult.getData().getOuterTenantId2EaMap();
        }
        //获取下游开通了优惠券的企业
        Set<String> roleOutUserIdSet = new HashSet<>();
        for (String tenantId : tenantIDSet) {
            if (!outerTenantId2EaMap.containsKey(Long.valueOf(tenantId))) {
                continue;
            }
            int ei = eieaConverter.enterpriseAccountToId(outerTenantId2EaMap.get(Long.valueOf(tenantId)));
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new com.fxiaoke.crmrestapi.common.data.HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
            if (getDescribeResultResult.isSuccess()){
                roleOutUserIdSet.add(tenantId);
            }
        }
        List<Long> groupIDList = roleOutUserIdSet.stream().map(id -> Long.valueOf(id)).collect(Collectors.toList());
        /*//通过企业id查询对接人的id
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
        headerObj.setAppId(partnerAppId);
        ListDownstreamEmployeesByDownstreamOuterTenantIdsArg listUserArg = new ListDownstreamEmployeesByDownstreamOuterTenantIdsArg();
        listUserArg.setUpstreamEa(ea);
        listUserArg.setDownstreamOuterTenantIds(groupIDList);
//        根据下游外部企业账号获取对接人id
        RestResult<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> listUserResult = publicEmployeeService.listDownstreamEmployeesByDownstreamOuterTenantIds(headerObj, listUserArg);*/
        Result<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> listUserResult = listDownstreamEmployeesByOuterTenantIds(ea, groupIDList);
        List<Integer> userList = new ArrayList<>();
        if (listUserResult.isSuccess() && null != listUserResult.getData()) {
            for (ListDownstreamEmployeesByDownstreamOuterTenantIdsResult temp : listUserResult.getData()) {
                outUserIdToOutTannetIdMap.put(temp.getOuterUid().intValue(), null == temp.getOuterTenantId() ? "" : temp.getOuterTenantId().toString());
                userList.add(temp.getOuterUid().intValue());
            }
        }
        return userList;
    }

   public Map<Integer,String> getOutUserNameByUpstreamEaAndOutTenantId(String upstreamEa,String outTenantId){
       HeaderObj var1 = HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(upstreamEa));
       ListEmployeesByOutTenantIdOutArg listEmployeesByOutTenantIdOutArg = new ListEmployeesByOutTenantIdOutArg();
       listEmployeesByOutTenantIdOutArg.setOuterTenantId(Long.parseLong(outTenantId));
       RestResult<List<EmployeeCardSimpleResult>> listRestResult = publicEmployeeService.listEmployeesByOutTenantId(var1,listEmployeesByOutTenantIdOutArg);
       return null;
    }

    // 将N_path图片转为可展示的url地址
    public void buildNPathProfileImage2Url(FSEmployeeMsg fsEmployeeMsg, String ea) {
        if (fsEmployeeMsg != null && fsEmployeeMsg.getProfileImage() != null && fsEmployeeMsg.getProfileImage().startsWith(Constant.N_WAREHOUSE_TYPE)) {
            String photoUrl = fileV2Manager.getUrlByPath(fsEmployeeMsg.getProfileImage(), ea, false);
            fsEmployeeMsg.setProfileImage(photoUrl);
        }
    }

    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class FSEmployeeMsg extends EmployeeDto {
        private String department;
    }

    public List<Integer> getFsUserIdByDepartmentIdList(String ea, List<Integer> departmentIdList, boolean includeStopAndDeleted, MainDepartment mainDepartment) {
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return Lists.newArrayList();
        }
        boolean isAllDepartment = departmentIdList.stream().anyMatch(e -> e.equals(AuthManager.defaultAllDepartment));
        int ei = eieaConverter.enterpriseAccountToId(ea);
        if (isAllDepartment) {
            com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg getAllEmployeeIdsArg = new com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg();
            getAllEmployeeIdsArg.setEnterpriseId(ei);
            getAllEmployeeIdsArg.setRunStatus(includeStopAndDeleted ? com.facishare.organization.api.model.RunStatus.ALL : com.facishare.organization.api.model.RunStatus.ACTIVE);
            com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult getAllEmployeesDtoResult = employeeProviderService.getAllEmployeeIds(getAllEmployeeIdsArg);
            return getAllEmployeesDtoResult.getEmployeeIds() == null ? Lists.newArrayList() : getAllEmployeesDtoResult.getEmployeeIds();
        }
        BatchGetEmployeesDtoByDepartmentIdArg departmentIdArg = new BatchGetEmployeesDtoByDepartmentIdArg();
        departmentIdArg.setDepartmentIds(departmentIdList);
        departmentIdArg.setIncludeLowDepartment(true);
        departmentIdArg.setRunStatus(includeStopAndDeleted ? RunStatus.ALL : RunStatus.ACTIVE);
        departmentIdArg.setEnterpriseId(ei);
        departmentIdArg.setMainDepartment(mainDepartment);
        BatchGetEmployeesDtoByDepartmentIdResult result = employeeProviderService.batchGetEmployeesByDepartmentId(departmentIdArg);
        log.info("getFsUserIdByDepartmentIdList ,arg: {} result: {}", departmentIdArg, result);
        if (result == null || CollectionUtils.isEmpty(result.getEmployeeDtos())) {
            return Lists.newArrayList();
        }
        return result.getEmployeeDtos().stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
    }

    public List<EmployeeDto> batchGetFsEmployeeDto(String ea, List<Integer> fsUserIdList, boolean needAllData) {
        if (CollectionUtils.isEmpty(fsUserIdList)) {
            return Lists.newArrayList();
        }
        fsUserIdList = fsUserIdList.stream().filter(QywxUserConstants::isFsUserId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fsUserIdList)) {
            return Lists.newArrayList();
        }
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        batchGetEmployeeDtoArg.setRunStatus(needAllData ? RunStatus.ALL : RunStatus.ACTIVE);
        List<EmployeeDto> employeeDtoList = Lists.newArrayList();
        List<List<Integer>> partitionList = Lists.partition(Lists.newArrayList(fsUserIdList), 200);
        for (List<Integer> partition : partitionList) {
            batchGetEmployeeDtoArg.setEmployeeIds(partition);
            BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
            if (batchGetEmployeeDtoResult != null && CollectionUtils.isNotEmpty(batchGetEmployeeDtoResult.getEmployeeDtos())) {
                employeeDtoList.addAll(batchGetEmployeeDtoResult.getEmployeeDtos());
            }
        }
        return employeeDtoList;
    }

}