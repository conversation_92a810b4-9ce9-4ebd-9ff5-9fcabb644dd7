package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.campaignMergeData.AddCampaignMemberByCrmDataArg;
import com.facishare.marketing.api.arg.campaignMergeData.DeleteEnrollDataArg;
import com.facishare.marketing.api.arg.campaignMergeData.QueryEnrollDataArg;
import com.facishare.marketing.api.result.EnrollDataCountResult;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.campaignMergeData.QueryEnrollDataResult;
import com.facishare.marketing.api.service.CampaignMergeDataService;
import com.facishare.marketing.api.util.XssfExcelUtil;
import com.facishare.marketing.common.enums.SaveCrmStatusEnum;
import com.facishare.marketing.common.enums.SystemPromotionChannelEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataSourceTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dto.campaignMergeData.BaseCampaignDTO;
import com.facishare.marketing.provider.dto.campaignMergeData.PageCampaignLiveDTO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.dto.campaignMergeData.PageCampaignParticipantsDTO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager.WxUserData;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2021/03/22
 **/
@Slf4j
@Service("campaignMergeDataService")
public class CampaignMergeDataServiceImpl implements CampaignMergeDataService {

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private MemberManager memberManager;

    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private PushSessionManager pushSessionManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private CrmV2Manager crmV2Manager;

    @Override
    public Result<PageResult<QueryEnrollDataResult>> queryEnrollData(QueryEnrollDataArg arg) {
        if (arg.getPageNum() == null || arg.getPageSize() == null) {
            log.warn("CampaignMergeDataServiceImpl.queryEnrollData param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<QueryEnrollDataResult> pageResult = new PageResult<>();
        List<QueryEnrollDataResult> resultList = Lists.newArrayList();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(resultList);
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);

        List<String> campaignObjIds = crmV2Manager.getObjIdsByRuleGroupJson(arg.getEa(), arg.getRuleGroupJson());

        List<BaseCampaignDTO> campaignDTOList = campaignMergeDataDAO.pageCampaignEnrollData(arg.getEa(), arg.getMarketingEventId(), arg.getKeyword(), arg.getChannelValue(), arg.getSaveCrmStatus(), arg.getPayStatus(), campaignObjIds, page);
        if (CollectionUtils.isEmpty(campaignDTOList)) {
            log.warn("CampaignMergeDataServiceImpl.queryEnrollData campaignDTOList is null arg:{}", arg);
            return Result.newSuccess(pageResult);
        }

        Integer totalCount = campaignMergeDataDAO.countCampaignEnrollData(arg.getEa(), arg.getMarketingEventId(), arg.getKeyword(), arg.getChannelValue(), arg.getSaveCrmStatus(), arg.getPayStatus());
        pageResult.setTotalCount(totalCount);

        // 查询负责人
        List<String> campaignMembersObjIds = campaignDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).map(
            BaseCampaignDTO::getCampaignMembersObjId).collect(
            Collectors.toList());
        List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(arg.getEa(), arg.getMarketingEventId(), campaignMembersObjIds);
        Map<String, String> ownerNameMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(arg.getEa(), objectDataList);
        // 查询姓名（兼容表单无名称，对象有名称场景）
        Map<String, ObjectData> objectDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectDataList)){
            objectDataMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, data -> data, (v1, v2) -> v1));
        }
        Map<Integer, FSEmployeeMsg> allFSEmployeeMsgMap = Maps.newHashMap();
        allFSEmployeeMsgMap.putAll(fsAddressBookManager
            .getEmployeeInfoByUserIds(arg.getEa(), campaignDTOList.stream().filter(data -> data.getSpreadFsUid() != null).map(BaseCampaignDTO::getSpreadFsUid).collect(Collectors.toList()), true));
        Map<Integer, String> outUserNameMap = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(arg.getEa(), campaignDTOList.stream().filter(data -> data.getSpreadFsUid() != null).map(BaseCampaignDTO::getSpreadFsUid).collect(Collectors.toList()));
        // 拼装物料数据
        Map<String, String> objectNameMap = Maps.newHashMap();
        for (BaseCampaignDTO baseCampaignDTO : campaignDTOList) {
            String objectKey = baseCampaignDTO.getEnrollSourceObjectId() + "#" + baseCampaignDTO.getEnrollSourceObjectType();
            if (StringUtils.isBlank(objectNameMap.get(objectKey))) {
                String objectName = objectManager.getObjectName(baseCampaignDTO.getEnrollSourceObjectId(), baseCampaignDTO.getEnrollSourceObjectType());
                if (StringUtils.isNotBlank(objectName)) {
                    objectNameMap.put(objectKey, objectName);
                }
            }
        }
        List<String> campaignIds = campaignDTOList.stream().map(BaseCampaignDTO::getId).collect(Collectors.toList());
        // 查询最新的会员数据
        Map<String, String> accessibleMemberMap = memberManager.getLatestAccessibleMemberIdByCampaignIds(campaignIds);
        // 查询微信用户
        Map<String, List<String>> wxAppOpenIdMap = Maps.newHashMap();
        Map<String, WxUserData> allWxUserData = Maps.newHashMap();
        for (BaseCampaignDTO baseCampaignDTO : campaignDTOList) {
            if (StringUtils.isBlank(baseCampaignDTO.getWxAppId()) || StringUtils.isBlank(baseCampaignDTO.getOpenId())) {
                continue;
            }
            wxAppOpenIdMap.computeIfAbsent(baseCampaignDTO.getWxAppId(), data -> Lists.newArrayList()).add(baseCampaignDTO.getOpenId());
        }
        for (Map.Entry<String, List<String>> entry : wxAppOpenIdMap.entrySet()) {
            allWxUserData.putAll(campaignMergeDataManager.queryWxUserInfo(arg.getEa(), entry.getKey(), entry.getValue()));
        }
        Map<String, String> customerMap = new HashMap<>();
        Map<String, String> employeeMap = new HashMap<>();
        List<String> outTenantIdList = campaignDTOList.stream().map(BaseCampaignDTO::getOutTenantId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outTenantIdList.isEmpty()) {
            List<ObjectData> customerListByDestOuterTenantIds = customizeFormDataManager.getCustomerListByEnterpriserelationIds(arg.getEa(), outTenantIdList);
            customerListByDestOuterTenantIds.forEach(e -> customerMap.put(String.valueOf(e.get("enterpriserelation_id")), String.valueOf(e.get("name"))));
        }
        List<String> outUserIdList = campaignDTOList.stream().map(BaseCampaignDTO::getOutUid).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outUserIdList.isEmpty()) {
            List<ObjectData> employeeListByDestOuterTenantIds = customizeFormDataManager.getEmployeeListByOuterUidIds(arg.getEa(), outUserIdList);
            employeeListByDestOuterTenantIds.forEach(e -> employeeMap.put(String.valueOf(e.get("outer_uid")), String.valueOf(e.get("name"))));
        }
        Map<Integer, List<String>> crmObjectTypeToIdMap = Maps.newHashMap();
        for (BaseCampaignDTO baseCampaignDTO : campaignDTOList) {
            if (baseCampaignDTO.getBindCrmObjectType() != null && StringUtils.isNotBlank(baseCampaignDTO.getBindCrmObjectId())) {
                List<String> crmObjectIdList = crmObjectTypeToIdMap.computeIfAbsent(baseCampaignDTO.getBindCrmObjectType(), k -> Lists.newArrayList());
                crmObjectIdList.add(baseCampaignDTO.getBindCrmObjectId());
            }
        }
        Map<String, String> crmObjectIdToNameMap = conferenceManager.getEnrollNameByObjectType(arg.getEa(), crmObjectTypeToIdMap);
        for (BaseCampaignDTO baseCampaignDTO : campaignDTOList) {
            QueryEnrollDataResult queryEnrollDataResult = new QueryEnrollDataResult();
            queryEnrollDataResult.setId(baseCampaignDTO.getId());
            String userName = baseCampaignDTO.getName();
            if (StringUtils.isBlank(userName) && StringUtils.isNotBlank(baseCampaignDTO.getCampaignMembersObjId())) {
                ObjectData userObjData = objectDataMap.get(baseCampaignDTO.getCampaignMembersObjId());
                userName = userObjData != null ? userObjData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName()) : null;
            }

            //同步广告线索产生的活动成员，广告线索列表要展示为广告渠道
            if (baseCampaignDTO.getChannelValue() == null && baseCampaignDTO.getSourceType().equals(CampaignMergeDataSourceTypeEnum.AD_SYNC.getType())){
                baseCampaignDTO.setChannelValue(SystemPromotionChannelEnum.AD.getValue());
            }

            queryEnrollDataResult.setName(UnicodeFormatter.decodeUnicodeString(userName));
            if (StringUtils.isNotBlank(baseCampaignDTO.getCampaignMembersObjId())) {
                queryEnrollDataResult.setBindCrmObjectId(baseCampaignDTO.getBindCrmObjectId());
                queryEnrollDataResult.setSaveCrmStatus(SaveCrmStatusEnum.SUCCESS.getValue());
                if (baseCampaignDTO.getAddCampaignMember() != null && baseCampaignDTO.getAddCampaignMember()) {
                    queryEnrollDataResult.setSaveCrmStatus(SaveCrmStatusEnum.LINKED.getValue());
                }
                if (baseCampaignDTO.getSaveCrmStatus() != null) {
                    queryEnrollDataResult.setSaveCrmStatus(baseCampaignDTO.getSaveCrmStatus());
                }
                queryEnrollDataResult.setBindCrmObjectType(baseCampaignDTO.getBindCrmObjectType());
                String ownerName = ownerNameMap.get(baseCampaignDTO.getCampaignMembersObjId());
                queryEnrollDataResult.setOwnerName(ownerName);
            } else {
                queryEnrollDataResult.setSaveCrmStatus(baseCampaignDTO.getSaveCrmStatus());
                queryEnrollDataResult.setSaveCrmErrorMessage(baseCampaignDTO.getSaveCrmErrorMessage());
            }
            queryEnrollDataResult.setChannelValue(baseCampaignDTO.getChannelValue());
            queryEnrollDataResult.setCampaignMembersObjId(baseCampaignDTO.getCampaignMembersObjId());
            // 推广人
            if (StringUtils.isNotBlank(baseCampaignDTO.getOutTenantId()) && StringUtils.isNotBlank(baseCampaignDTO.getOutUid())) {
                String outTenant = customerMap.get(baseCampaignDTO.getOutTenantId());
                String outUser = employeeMap.get(baseCampaignDTO.getOutUid());
                if (StringUtils.isNotBlank(outTenant)
                        && StringUtils.isNotBlank(outUser)) {
                    queryEnrollDataResult.setSpreadUserName(outTenant + "-" + outUser);
                }
            } else if (baseCampaignDTO.getSpreadFsUid() != null) {
                FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(baseCampaignDTO.getSpreadFsUid());
                queryEnrollDataResult.setSpreadUserName(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
                if (StringUtils.isBlank(queryEnrollDataResult.getSpreadUserName())) {
                    queryEnrollDataResult.setSpreadUserName(outUserNameMap.get(baseCampaignDTO.getSpreadFsUid()));
                }
            }
            //根据业务数据来源来获取关联名称
            String relationDataName = queryEnrollDataResult.getName();
            if (StringUtils.isNotBlank(baseCampaignDTO.getBindCrmObjectId()) && baseCampaignDTO.getBindCrmObjectType() != null) {
//                String objectName = conferenceManager.getEnrollNameByObjectType(arg.getEa(),baseCampaignDTO.getBindCrmObjectType(),baseCampaignDTO.getBindCrmObjectId());
                String objectName = crmObjectIdToNameMap.get(baseCampaignDTO.getBindCrmObjectId());
                if (objectName != null) {
                    relationDataName = objectName;
                }
            }
            queryEnrollDataResult.setRelationDataName(relationDataName);
            queryEnrollDataResult.setMemberId(accessibleMemberMap.get(baseCampaignDTO.getId()));
            queryEnrollDataResult.setEnrollSourceName(objectNameMap.get(baseCampaignDTO.getEnrollSourceObjectId() + "#" + baseCampaignDTO.getEnrollSourceObjectType()));
            queryEnrollDataResult.setFormDataUserId(baseCampaignDTO.getFormDataUserId());
            if (StringUtils.isNotBlank(baseCampaignDTO.getWxAppId()) && StringUtils.isNotBlank(baseCampaignDTO.getOpenId())) {
                WxUserData wxUserData = allWxUserData.get(baseCampaignDTO.getOpenId());
                if (wxUserData != null) {
                    queryEnrollDataResult.setWxUserName(wxUserData.getName());
                    queryEnrollDataResult.setWxUserAvatar(wxUserData.getUserAvatar());
                }
            }
            BigDecimal divide = new BigDecimal(baseCampaignDTO.getTotalAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            queryEnrollDataResult.setTotalAmount(divide);
            resultList.add(queryEnrollDataResult);
        }
        Map<String, Integer> campaignIdToPayOrderCountMap = campaignMergeDataManager.countPayOrderNumber(arg.getEa(), campaignIds);
        pageResult.getResult().forEach(r -> {
            r.setPayOrderCount(campaignIdToPayOrderCountMap.get(r.getId()) != null ? campaignIdToPayOrderCountMap.get(r.getId()) : 0);
        });
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<ExportEnrollsDataResult> exportEnrollData(QueryEnrollDataArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            ExportEnrollsDataResult result = new ExportEnrollsDataResult();
            try {
                result.setFileName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_270));
                List<String> titleList = Lists.newArrayList();
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_797));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_275));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_276));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1071));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1073));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1074));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1075));
                titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1076));
                result.setTitleList(titleList);
                List<BaseCampaignDTO> campaignDTOList = campaignMergeDataDAO.queryCampaignEnrollData(arg.getEa(), arg.getMarketingEventId(), arg.getKeyword(), arg.getChannelValue(), arg.getSaveCrmStatus());
                if (CollectionUtils.isNotEmpty(campaignDTOList)) {
                    List<List<Object>> participantsList = Lists.newArrayList();
                    List<String> campaignMembersObjIds = campaignDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).map(
                            BaseCampaignDTO::getCampaignMembersObjId).collect(
                            Collectors.toList());
                    List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(arg.getEa(), arg.getMarketingEventId(), campaignMembersObjIds);
                    Map<String, String> ownerNameMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(arg.getEa(), objectDataList);
                    // 查询姓名（兼容表单无名称，对象有名称场景）
                    Map<String, ObjectData> objectDataMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(objectDataList)){
                        objectDataMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, data -> data, (v1, v2) -> v1));
                    }
                    Map<Integer, FSEmployeeMsg> allFSEmployeeMsgMap = Maps.newHashMap();
                    allFSEmployeeMsgMap.putAll(fsAddressBookManager
                            .getEmployeeInfoByUserIds(arg.getEa(), campaignDTOList.stream().filter(data -> data.getSpreadFsUid() != null).map(BaseCampaignDTO::getSpreadFsUid).collect(Collectors.toList()),
                                    true));
                    Map<String, String> customerMap = new HashMap<>();
                    Map<String, String> employeeMap = new HashMap<>();
                    List<String> outTenantIdList = campaignDTOList.stream().map(BaseCampaignDTO::getOutTenantId).filter(Objects::nonNull).collect(Collectors.toList());
                    if (!outTenantIdList.isEmpty()) {
                        List<ObjectData> customerListByDestOuterTenantIds = customizeFormDataManager.getCustomerListByEnterpriserelationIds(arg.getEa(), outTenantIdList);
                        customerListByDestOuterTenantIds.forEach(e -> customerMap.put(String.valueOf(e.get("enterpriserelation_id")), String.valueOf(e.get("name"))));
                    }
                    List<String> outUserIdList = campaignDTOList.stream().map(BaseCampaignDTO::getOutUid).filter(Objects::nonNull).collect(Collectors.toList());
                    if (!outUserIdList.isEmpty()) {
                        List<ObjectData> employeeListByDestOuterTenantIds = customizeFormDataManager.getEmployeeListByOuterUidIds(arg.getEa(), outUserIdList);
                        employeeListByDestOuterTenantIds.forEach(e -> employeeMap.put(String.valueOf(e.get("outer_uid")), String.valueOf(e.get("name"))));
                    }
//                    Map<Integer, String> outUserNameMap = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(arg.getEa(), campaignDTOList.stream().filter(data -> data.getSpreadFsUid() != null).map(BaseCampaignDTO::getSpreadFsUid).collect(Collectors.toList()));
                    // 拼装物料数据
                    Map<String, String> objectNameMap = Maps.newHashMap();
                    for (BaseCampaignDTO baseCampaignDTO : campaignDTOList) {
                        String objectKey = baseCampaignDTO.getEnrollSourceObjectId() + "#" + baseCampaignDTO.getEnrollSourceObjectType();
                        if (StringUtils.isBlank(objectNameMap.get(objectKey))) {
                            String objectName = objectManager.getObjectName(baseCampaignDTO.getEnrollSourceObjectId(), baseCampaignDTO.getEnrollSourceObjectType());
                            if (StringUtils.isNotBlank(objectName)) {
                                objectNameMap.put(objectKey, objectName);
                            }
                        }
                    }
                    Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(arg.getEa());
                    for (BaseCampaignDTO baseCampaignDTO : campaignDTOList) {
                        List<Object> enrollInfos = Lists.newArrayList();
                        String userName = baseCampaignDTO.getName();
                        if (StringUtils.isBlank(userName) && StringUtils.isNotBlank(baseCampaignDTO.getCampaignMembersObjId())) {
                            ObjectData userObjData = objectDataMap.get(baseCampaignDTO.getCampaignMembersObjId());
                            userName = userObjData != null ? userObjData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName()) : null;
                        }
                        enrollInfos.add(UnicodeFormatter.decodeUnicodeString(userName));
                        //手机号
                        String enrollPhone = null;
                        if (null != baseCampaignDTO.getSubmitContent()) {
                            if (StringUtils.isNotBlank(baseCampaignDTO.getSubmitContent().getPhone())) {
                                enrollPhone = baseCampaignDTO.getSubmitContent().getPhone();
                            } else {
                                enrollPhone = baseCampaignDTO.getPhone();
                            }
                        } else {
                            enrollPhone = baseCampaignDTO.getPhone();
                        }
                        enrollInfos.add(enrollPhone);
                        String email = null;
                        String companyName = null;
                        String position = null;
                        if (null != baseCampaignDTO.getSubmitContent()) {
                            email = baseCampaignDTO.getSubmitContent().getEmail();
                            companyName = baseCampaignDTO.getSubmitContent().getCompanyName();
                            position = baseCampaignDTO.getSubmitContent().getPosition();
                        }
                        //邮箱
                        enrollInfos.add(email);
                        //公司名称
                        enrollInfos.add(companyName);
                        //职务
                        enrollInfos.add(position);
                        String memberType = null;
                        if (baseCampaignDTO.getBindCrmObjectType() != null) {
                            CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(baseCampaignDTO.getBindCrmObjectType());
                            memberType = campaignMergeDataObjectTypeEnum.getDesc();
                        }
                        enrollInfos.add(memberType);
                        // 推广人
//                        if (baseCampaignDTO.getSpreadFsUid() != null) {
//                            FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(baseCampaignDTO.getSpreadFsUid());
//                            if (null != fsEmployeeMsg) {
//                                enrollInfos.add(fsEmployeeMsg.getName());
//                            } else if (null != outUserNameMap && outUserNameMap.containsKey(baseCampaignDTO.getSpreadFsUid())) {
//                                enrollInfos.add(outUserNameMap.get(baseCampaignDTO.getSpreadFsUid()));
//                            } else {
//                                enrollInfos.add(null);
//                            }
//                        } else {
//                            enrollInfos.add(null);
//                        }
                        if (StringUtils.isNotBlank(baseCampaignDTO.getOutTenantId()) && StringUtils.isNotBlank(baseCampaignDTO.getOutUid())) {
                            String outTenant = customerMap.get(baseCampaignDTO.getOutTenantId());
                            String outUser = employeeMap.get(baseCampaignDTO.getOutUid());
                            if (StringUtils.isNotBlank(outTenant)
                                    && StringUtils.isNotBlank(outUser)) {
                                enrollInfos.add(outTenant + "-" + outUser);
                            } else {
                                enrollInfos.add(null);
                            }
                        } else if (baseCampaignDTO.getSpreadFsUid() != null) {
                            FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(baseCampaignDTO.getSpreadFsUid());
                            enrollInfos.add(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
                        } else {
                            enrollInfos.add(null);
                        }
                        // 负责人
                        if (StringUtils.isNotBlank(baseCampaignDTO.getCampaignMembersObjId())) {
                            enrollInfos.add(ownerNameMap.get(baseCampaignDTO.getCampaignMembersObjId()));
                        } else {
                            enrollInfos.add(null);
                        }
                        // 来源推广内容
                        enrollInfos.add(objectNameMap.get(baseCampaignDTO.getEnrollSourceObjectId() + "#" + baseCampaignDTO.getEnrollSourceObjectType()));
                        // 来源渠道
                        //同步广告线索产生的活动成员，广告线索列表要展示为广告渠道
                        if (baseCampaignDTO.getSourceType() != null && baseCampaignDTO.getSourceType().equals(CampaignMergeDataSourceTypeEnum.AD_SYNC.getType())) {
                            enrollInfos.add(SystemPromotionChannelEnum.AD.getLabel());
                        } else {
                            //如果是正常的表单提交,则获取来源渠道
                            if (baseCampaignDTO.getSourceType() != null) {
                                enrollInfos.add(spreadChannelManager.getChannelLabelByChannelValue(arg.getEa(), channelValueMap, baseCampaignDTO.getChannelValue()));
                            }
                        }
                        participantsList.add(enrollInfos);
                    }
                    result.setEnrollInfoList(participantsList);
                } else {
                    result.setEnrollInfoList(Lists.newArrayList());
                }

                XssfExcelUtil exportResult = new XssfExcelUtil(result, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_418));
                XSSFWorkbook xssfWorkbook = exportResult.buildXSSFWorkbook();
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, exportResult.getSheetName(),arg.getEa(), arg.getUserId()) ;

            } catch (Exception e) {
                log.warn("CampaignMergeDataServiceImpl.exportEnrollData error e:", e);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<EnrollDataCountResult> countUnSaveEnrollData(String ea, String marketingEventId) {
        List<Integer> saveCrmStatus = new ArrayList<>();
        saveCrmStatus.add(99);
        Integer totalCount = campaignMergeDataDAO.countCampaignEnrollData(ea, marketingEventId, null, null, saveCrmStatus, null);
        EnrollDataCountResult result = new EnrollDataCountResult();
        result.setUnSaveCrmCount(totalCount);
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> addCampaignMemberByCrmData(String ea, Integer fsUserId, AddCampaignMemberByCrmDataArg arg) {
        String objectApiName = CampaignMergeDataObjectTypeEnum.getApiNameByType(arg.getCrmObjectType());
        if (objectApiName == null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

         Optional<CampaignMergeDataEntity> optionalMergeDataEntity = buildCampaignMemberObjData(ea, arg.getCrmObjectType(), arg.getCrmObjectId(), arg.getMarketingEventId(), CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ);
        if (!optionalMergeDataEntity.isPresent()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        CampaignMergeDataEntity campaignMergeDataEntity = optionalMergeDataEntity.get();
        Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity);
        String campaignMemberId = crmV2Manager.addCampaignMembersObjByLock(ea, dataMap, arg.getCrmObjectType(), arg.getCrmObjectId(), arg.getMarketingEventId());
        if (campaignMemberId != null) {
            campaignMergeDataEntity.setCampaignMembersObjId(campaignMemberId);
            campaignMergeDataManager.addCampaignDataOnlyUnLock(optionalMergeDataEntity.get());
        }
        return Result.newSuccess(campaignMemberId);
    }

    public Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity
            campaignMergeDataEntity) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());

        return dataMap;
    }

    private Optional<CampaignMergeDataEntity> buildCampaignMemberObjData(String ea, Integer bindCrmObjectType, String bindCrmObjectId, String marketingEventId, CampaignMergeDataSourceTypeEnum source) {
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
        if (campaignMergeDataObjectTypeEnum == null) {
            return Optional.empty();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        } catch (Exception e) {
            log.warn("BaiduCampaignService.buildCampaignMemberObjData error e:{}", e);
        }
        if (objectData == null) {
            return Optional.empty();
        }

        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        String phone = campaignMergeDataManager.getPhoneByObject(objectData);
        String name = objectData.getName();
        String campaignId = UUIDUtil.getUUID();
        campaignMergeDataEntity.setId(campaignId);
        campaignMergeDataEntity.setEa(ea);
        campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        campaignMergeDataEntity.setBindCrmObjectId(bindCrmObjectId);
        campaignMergeDataEntity.setBindCrmObjectType(bindCrmObjectType);
        campaignMergeDataEntity.setName(name);
        campaignMergeDataEntity.setPhone(phone);
        campaignMergeDataEntity.setCreateTime(new Date());
        campaignMergeDataEntity.setUpdateTime(new Date());
        campaignMergeDataEntity.setSourceType(source.getType());

        return Optional.of(campaignMergeDataEntity);
    }

    @Override
    public Result<Void> deleteEnrollData(DeleteEnrollDataArg arg) {
        ThreadPoolUtils.executeWithNewThread("deleteEnrollData-" + arg.getEa(), () -> {
            List<String> campaignIds = arg.getCampaignIds();
            for (String campaignId : campaignIds) {
                try {
                    campaignMergeDataManager.delete(campaignId, arg.getEa(), arg.getFsUserId());
                } catch (Exception e) {
                    log.error("CampaignMergeDataServiceImpl.deleteEnrollData error campaignId: {} e:", campaignId, e);
                }
            }
        });
        return Result.newSuccess();
    }
}