package com.facishare.marketing.provider.manager.qywx;

import com.facishare.marketing.api.vo.function.UpdateQywxExternalUserRemarkVO;
import com.facishare.marketing.api.service.TraceabilityRelationService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.api.arg.GetQywxSuiteTokenArg;
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeTagResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryGroupOwnerListResult;
import com.facishare.marketing.api.result.qywx.miniapp.CustomerAppInfoResult;
import com.facishare.marketing.api.result.qywx.miniapp.GetPhoneNumberByCodeResult;
import com.facishare.marketing.api.vo.qywx.QywxAttachmentsVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.QYWXApiConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.TimeTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.*;
import com.facishare.marketing.common.enums.user.UserTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.PhotoDAO;
import com.facishare.marketing.provider.dao.TagModelDao;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.dao.wx.WxPendingRelationDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpEaMappingEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerTemplateInfoEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.api.arg.RelationConfig;
import com.facishare.marketing.provider.entity.wx.WxPendingRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerArg.qywx.*;
import com.facishare.marketing.provider.innerData.qywx.QywxEmployeeToCrmArg;
import com.facishare.marketing.provider.innerData.qywx.QywxScheduleContainer;
import com.facishare.marketing.provider.innerResult.qywx.*;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult.StaffInfo;
import com.facishare.marketing.provider.innerResult.qywx.QueryTagListResult.TagDetail;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdMapByDepartmentId;
import com.facishare.organization.api.model.employee.EmployeeOption;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeOptionArg;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeOptionResult;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Created by ranluch on 2020/1/6.
 */
@Component
@Slf4j
public class QywxManager {

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private TagModelDao tagModelDao;
    @Autowired
    private DisplayOrderManager displayOrderManager;
    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;
    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private QywxCustomerTemplateInfoDAO qywxCustomerTemplateInfoDAO;
    @Autowired
    private QywxCorpEaMappingDAO qywxCorpEaMappingDAO;
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private WxPendingRelationDAO wxPendingRelationDAO;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private CustomerGroupManager customerGroupManager;
    @Autowired
    private EnterpriseEditionManager enterpriseEditionManager;
    @Autowired
    private WechatWorkExternalUserObjDescribeManager wechatWorkExternalUserObjDescribeManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;
    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;
    @Autowired
    private WechatGroupUserObjDescribeManager wechatGroupUserObjDescribeManager;
    @Autowired
    private WechatAccountGroupStatisticsObjManager wechatAccountGroupStatisticsObjManager;
    @Autowired
    private WechatAccountStatisticsObjManager wechatAccountStatisticsObjManager;
    @Autowired
    private OtherObjectDescribeManager otherObjectDescribeManager;
    @Autowired
    @Qualifier("wxWorkTagSynchronizationManager")
    private OuterTagSynchronizationManager wxWorkTagSynchronizationManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private CrmPaasOrgDataManager crmPaasOrgDataManager;

    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @Autowired
    private QywxMiniappConfigDAO miniappConfigDAO;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Value("${qywx.group.message.default.cover}")
    private String groupMessageDefaultCoverPath;

    @Autowired
    private UserManager userManager;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private PhotoDAO photoDAO;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private UserRelationManager userRelationManager;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private TraceabilityRelationService traceabilityRelationService;

    @ReloadableProperty("center.host")
    private String centerHost;
    @ReloadableProperty("host")
    private String host;

    @Autowired
    private QywxManager qywxManager;

    private Gson gson = GsonUtil.getGson();

    /*@ReloadableProperty("qywx.template.id.list")
    private String qywxTemplateIdList;*/

    @ReloadableProperty("sync.scrm.black.list")
    private String syncScrmBlackList;

    @ReloadableProperty("qywx.ea.agentId.config")
    private String qywxEaAgentIdConfig;

    @Autowired
    private QywxActivatedAccountManager qywxActivatedAccountManager;


    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 获取Token，此方法为历史方法，新开发的功能不建议使用这个方法，用下面ea那个
     * @param corpId
     * @param agentId
     * @param secret
     * @return
     */
    public String getAccessToken(String corpId, String agentId, String secret) {

        // TODO: 2022/3/7 判断有没有授权信息 有的话 用getAgentAccessToken（）  没有 用getAccessToken的逻辑

        if (Strings.isNullOrEmpty(corpId)) {
            return null;
        }
        String accessToken=null;
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentBycorpId(corpId);
        List<QywxCustomerAppInfoEntity>  qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(corpId,agentConfig.getEa());
        if (qywxCustomerAppInfoEntity.size()!=0) {
            accessToken = getAgentAccessToken(corpId, qywxCustomerAppInfoEntity.get(0).getSuitId(), qywxCustomerAppInfoEntity.get(0).getAuthCode());
        }  else {
            accessToken = redisManager.getQywxAccessToken(corpId, agentId);
            if (Strings.isNullOrEmpty(accessToken)) {
                if (agentConfig.getSecret()==null) {
                    return null;
                }
                String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpId + "&corpsecret=" + secret;
                QywxGetTokenResult tokenResult = httpManager.executeGetHttp(url, new TypeToken<QywxGetTokenResult>() {
                });
                if (tokenResult.getErrCode() == 0) {
                    accessToken = tokenResult.getAccessToken();
                    redisManager.setQywxAccessToken(corpId, agentId, accessToken, tokenResult.getExpiresTime());
                } else {
                    log.info("QywxManager getAccessToken executeGetHttp result is error, tokenResult={}, corpId={}, agentId={}", tokenResult, corpId, agentId);
                    return null;
                }

            }
        }

        return accessToken;
    }

    /**
     * 根据ea获取Token
     * @param ea
     * @return
     */
    public String getAccessToken(String ea) {
        if (StringUtils.isBlank(ea)) {
            return null;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return null;
        }
        return getAccessToken(agentConfig);
    }

    public String getAccessToken(QywxCorpAgentConfigEntity agentConfig) {
        if (agentConfig == null) {
            return null;
        }

        String accessToken = null;
        String corpId = agentConfig.getCorpid();
        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(corpId, agentConfig.getEa());
        if (qywxCustomerAppInfoEntity.size() != 0) {
            accessToken = getAgentAccessToken(corpId, qywxCustomerAppInfoEntity.get(0).getSuitId(), qywxCustomerAppInfoEntity.get(0).getAuthCode());
        }  else {
            String agentId = agentConfig.getAgentid();
            String secret = agentConfig.getSecret();
            accessToken = redisManager.getQywxAccessToken(corpId, agentId);
            if (Strings.isNullOrEmpty(accessToken)) {
                if (agentConfig.getSecret()==null) {
                    return null;
                }
                String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpId + "&corpsecret=" + secret;
                QywxGetTokenResult tokenResult = httpManager.executeGetHttp(url, new TypeToken<QywxGetTokenResult>() {
                });
                if (tokenResult.getErrCode() == 0) {
                    accessToken = tokenResult.getAccessToken();
                    redisManager.setQywxAccessToken(corpId, agentId, accessToken, tokenResult.getExpiresTime());
                } else {
                    log.info("QywxManager getAccessTokenByEa executeGetHttp result is error, tokenResult={}, corpId={}, agentId={}", tokenResult, corpId, agentId);
                    return null;
                }
            }
        }

        return accessToken;
    }

    private String getMiniAppAccessToken(String corpId, String agentId, String secret) {
        if (Strings.isNullOrEmpty(corpId) || Strings.isNullOrEmpty(secret) || Strings.isNullOrEmpty(agentId)) {
            return null;
        }
        String accessToken = redisManager.getQywxAccessToken(corpId, agentId);
        if (Strings.isNullOrEmpty(accessToken)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpId + "&corpsecret=" + secret;
            QywxGetTokenResult tokenResult = httpManager.executeGetHttp(url, new TypeToken<QywxGetTokenResult>() {});
            log.info("QywxManager.getMiniAppAccessToken from qywx api, result:{}", tokenResult);
            if (tokenResult.getErrCode() == 0) {
                accessToken = tokenResult.getAccessToken();
                redisManager.setQywxAccessToken(corpId, agentId, accessToken, tokenResult.getExpiresTime());
                // 放入redis,用于识别该token是自建应用token
                redisManager.setQywxSelfAppAccessToken(accessToken, tokenResult.getExpiresTime());
            } else {
                return null;
            }
        } else {
            log.info("QywxManager.getMiniAppAccessToken from redis cache, token:{}", accessToken);
        }

        return accessToken;
    }

    /**
     * 兼容自建应用转代开发模式，获取Token
     * @param ea
     * @return
     */
    public String getMiniAppAccessToken(String ea) {
        if (StringUtils.isBlank(ea)) {
            return null;
        }
        // 判断新旧企业
        QywxCorpAgentConfigEntity agentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfigEntity == null) {
            return null;
        }
        if (isNewInstallAgentApp(ea)) {
            // 新企业
            QywxCustomerAppInfoEntity customerAppInfoEntity = qywxCustomerAppInfoDAO.selectByEa(ea, QywxSuiteTypeEnum.MINIAPP.getValue());
            if(customerAppInfoEntity == null){
                return null;
            }
            return getAgentAccessToken(customerAppInfoEntity.getCorpId(), customerAppInfoEntity.getSuitId(), customerAppInfoEntity.getAuthCode());
        } else {
            return getMiniAppAccessTokenOld(ea);
        }
    }


    /**
     * 获取自建应用Token，根据ea
     * @param ea
     * @return
     */
    private String getMiniAppAccessTokenOld(String ea) {
        if (Strings.isNullOrEmpty(ea)) {
            return null;
        }

        // 查询获取Token需要的参数
        QywxMiniappConfigEntity miniappConfigEntity = getMiniappConfigEntityByEa(ea);
        if (Objects.isNull(miniappConfigEntity)) {
            return null;
        }

        String corpId = miniappConfigEntity.getCorpid();
        String agentId = miniappConfigEntity.getAgentid();
        String secret = miniappConfigEntity.getSecret();

        return getMiniAppAccessToken(corpId, agentId, secret);
    }

    //保存suitticket
    public void saveOrUpdateSuitTicket(String suitId, String suitTicket) {
        if (StringUtils.isBlank(suitTicket)) {
            return;
        }
        QywxCustomerTemplateInfoEntity qywxCustomerTemplateInfoEntity = qywxCustomerTemplateInfoDAO.selectOne(suitId);
        if (null != qywxCustomerTemplateInfoEntity) {
            qywxCustomerTemplateInfoDAO.updateSuitTicket(suitTicket, suitId);
        }
    }

    /**
     * 企业微信客户授权操作，转发到其他云的请求不会进到这里，这里处理的都是企微回调的一手请求，包括纷享云和vpn断网云的回调
     * @param msgSignature
     * @param timestamp
     * @param nonce
     * @param encryptXmlBody
     * @param authcode
     * @param eventSuitId
     * @return
     */
    public Result<String> qywxAuthorize(String msgSignature, String timestamp, String nonce,
                              String encryptXmlBody, String authcode, String eventSuitId){

        log.info("--------qywxAuthorize----------> {}", eventSuitId);
        //获取永久授权码
        String suitAccessToken = getSuitAccessToken(eventSuitId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token=" + suitAccessToken;
        Map<String, String> params = new HashMap<>();
        params.put("auth_code", authcode);
        QywxCustomerAuthorizationResult res = httpManager.executePostHttp(params, url, new TypeToken<QywxCustomerAuthorizationResult>() {});
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(res.getState()));
        String corpId = res.getAuthCorpInfo().getCorpid();
        log.info("QywxManager.qywxAuthorize ea:{}", ea);

        // 更新多云映射表
        QywxCorpEaMappingEntity eaMappingEntity = qywxCorpEaMappingDAO.queryMappingByEa(ea);
        if (Objects.nonNull(eaMappingEntity)) {
            qywxCorpEaMappingDAO.updateCorpIdById(eaMappingEntity.getId(), corpId);
        }

        //判断环境
        if (appVersionManager.isCurrentCloud(ea)) {
            // 本云
            saveQywxAgentPermanentCode(res.getPermanentCode(), eventSuitId, corpId,res.getAuthInfo().getAgent().get(0).getAgentId() + "", ea);
            return Result.newSuccess("success");
        } else {
            // 其他云
            String callBackURL = centerHost + "/marketing/wxThirdCloudInner/handleQywxAppAgentCallback";
            Map<String, String> header = new HashMap<>();
            header.put("x-fs-ei", String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
            header.put("Content-Type", "multipart/form-data");
            FormBody requestBody = new FormBody.Builder()
                .add("encryptXmlBody", encryptXmlBody)
                .add("timestamp", timestamp)
                .add("nonce", nonce)
                .add("msg_signature", msgSignature)
                .add("autoCode", res.getPermanentCode())
                .add("corpId", corpId)
                .add("agentId", res.getAuthInfo().getAgent().get(0).getAgentId() + "")
                .add("suitId", eventSuitId)
                .add("ea", ea).build();
            httpManager.executePostByOkHttpClientWithRequestBodyAndHeader(requestBody, callBackURL, new TypeToken<String>() {}, header);
        }
        return Result.newSuccess("success");
    }

    //企业微信取消授权
    public Result<String> qywxCancelAuthEvent(String msgSignature, String timestamp, String nonce,
                                        String encryptXmlBody,String eventSuitId,String corpId){

        log.info("--------qywxCancelAuthEvent----------> {}", corpId);
        //判断是不是分享云
        if (appVersionManager.isFxCloud()) {
            QywxCorpEaMappingEntity mappingEntity =
                    qywxCorpEaMappingDAO.queryMappingByCorpId(corpId);
            String ea = mappingEntity.getEa();
            if(appVersionManager.isCurrentCloud(ea)){
                //删除授权信息
                redisManager.deleteQywxAccessToken(corpId, eventSuitId);
                redisManager.delQywxSuitAccessToken(eventSuitId);
                delAuthInfo(corpId, eventSuitId); //发送绑定信息到集成平台
                syncQywxBindInfo(ea, corpId, eventSuitId, "1");
                return Result.newSuccess("success");
            }else{
                //跳转对应云
                String callBackURL = centerHost + "/marketing/wxThirdCloudInner" + "/handleQywxAppAgentCallback";
                Map<String, String> header = new HashMap<>();
                header.put("x-fs-ei", String.valueOf(eieaConverter.enterpriseAccountToId(mappingEntity.getEa())));
                header.put("Content-Type", "multipart/form-data");
                FormBody requestBody =
                        new FormBody.Builder().add("encryptXmlBody", encryptXmlBody).add(
                                        "timestamp", timestamp).add("nonce", nonce).add("msg_signature", msgSignature)
                                .add("autoCode","").add("corpId",corpId).add(
                                        "agentId", "").add(
                                        "suitId", eventSuitId).build();
                httpManager.executePostByOkHttpClientWithRequestBodyAndHeader(requestBody, callBackURL, new TypeToken<String>() {}, header);
            }
            return Result.newSuccess("success");
        } else {
            //删除授权信息
            redisManager.deleteQywxAccessToken(corpId, eventSuitId);
            redisManager.delQywxSuitAccessToken(eventSuitId);
            delAuthInfo(corpId, eventSuitId);
            QywxCorpEaMappingEntity mappingEntity = qywxCorpEaMappingDAO.queryMappingByCorpId(corpId);
            if (mappingEntity != null){
                String ea = mappingEntity.getEa();
                syncQywxBindInfo(ea, corpId, eventSuitId, "1");
            }
            return Result.newSuccess("success");
        }
    }

    /**
     * 该API用于获取第三方应用凭证（suite_access_token）。
     * https://developer.work.weixin.qq.com/document/path/90600
     * @param suitId
     * @return
     */
    public String getSuitAccessToken(String suitId) {
        // vpn断网专属云的模板，直接在当前云获取token
        if (QywxSuiteEnum.isVpnDisconnect(suitId)) {
            return getSuitAccessToken4FxCloud(suitId);
        } else {
            // 判断当前云环境
            if (appVersionManager.isFxCloud()) {
                return getSuitAccessToken4FxCloud(suitId);
            } else {
                return getSuitAccessToken4OtherCloud(suitId);
            }
        }
    }

    public String getSuitAccessToken4FxCloud(String suitId) {
        String qywxSuitAccessToken = redisManager.getQywxSuitAccessToken(suitId);
        if (StringUtils.isNotBlank(qywxSuitAccessToken)) {
            return qywxSuitAccessToken;
        }
        QywxCustomerTemplateInfoEntity qywxCustomerTemplateInfoEntity = qywxCustomerTemplateInfoDAO.selectOne(suitId);
        if (null == qywxCustomerTemplateInfoEntity) {
            return null;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token";
        Map<String, String> params = new HashMap<>();
        params.put("suite_id", suitId);
        params.put("suite_secret", qywxCustomerTemplateInfoEntity.getSecret());
        params.put("suite_ticket", qywxCustomerTemplateInfoEntity.getSuitTicket());
        QywxSuitAccessTokenResult tokenResult = httpManager.executePostHttp(params, url, new TypeToken<QywxSuitAccessTokenResult>() {
        });
        if (null == tokenResult.getErrCode()) {
            qywxSuitAccessToken = tokenResult.getSuitAccessToken();
            redisManager.setQywxSuitAccessToken(suitId, qywxSuitAccessToken);
            return qywxSuitAccessToken;
        } else {
            log.info("QywxManager getSuitAccessToken executePostHttpWithRequestBody result is error, tokenResult={}, suitId={},", tokenResult, suitId);
            return null;
        }
    }

    public String getSuitAccessToken4OtherCloud(String suitId) {
        // 回源到纷享云查询
        String url = centerHost + "/inner/wxThirdCloudInner/getQywxSuiteToken";
        GetQywxSuiteTokenArg arg = new GetQywxSuiteTokenArg();
        arg.setSuiteId(suitId);
        Result<String> result = httpManager.executePostByOkHttpClient(arg, url, new TypeToken<Result<String>>(){});
        if (result.isSuccess()) {
            return result.getData();
        }
        return null;
    }
    public void delAuthInfo(String authCorpId, String suitId) {
        qywxCustomerAppInfoDAO.delete(suitId, authCorpId);
    }

    //保存永久授权码
    public void savePermanentCode(String tempAuthCode, String suitId) {
        try {
            Thread.sleep(1000);
        } catch (Exception e) {
            log.warn("exception:",  e);
        }
        String suitAccessToken = getSuitAccessToken(suitId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token=" + suitAccessToken;
        Map<String, String> params = new HashMap<>();
        params.put("auth_code", tempAuthCode);
        QywxCustomerAuthorizationResult res = httpManager.executePostHttp(params, url, new TypeToken<QywxCustomerAuthorizationResult>() {
        });
        log.info("--------savePermanentCode----------> {}", res);
        if (null != res && null == res.getErrcode() && StringUtils.isNotBlank(res.getPermanentCode())) {
            QywxCustomerAppInfoEntity queryRes = qywxCustomerAppInfoDAO.selectOne(suitId, res.getAuthCorpInfo().getCorpid());
            QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentBycorpId(res.getAuthCorpInfo().getCorpid());
            String ea = "";
            if (null == qywxCorpAgentConfigEntity || StringUtils.isEmpty(qywxCorpAgentConfigEntity.getEa())) {
                ea = UUIDUtil.getUUID();
            } else {
                ea = qywxCorpAgentConfigEntity.getEa();
            }
            if (null == queryRes) {
                QywxCustomerAppInfoEntity entity = new QywxCustomerAppInfoEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setSuitId(suitId);
                entity.setEa(ea);
                entity.setCorpId(res.getAuthCorpInfo().getCorpid());
                entity.setAuthCode(res.getPermanentCode());
                entity.setAgentId(res.getAuthInfo().getAgent().get(0).getAgentId() + "");
                qywxCustomerAppInfoDAO.insert(entity);
            } else {
                qywxCustomerAppInfoDAO.update(res.getPermanentCode(), res.getAuthInfo().getAgent().get(0).getAgentId() + "", res.getAuthCorpInfo().getCorpid(), suitId, ea);
            }

        }
    }

    /**
     * 保存代开发应用永久授权码
     * @param authCode
     * @param suitId
     * @param corpId
     * @param agentId
     * @param ea
     */
    public void saveQywxAgentPermanentCode(String authCode, String suitId, String corpId, String agentId, String ea) {
        log.info("--------saveQywxAgentPermanentCode----------> {}", authCode);
        saveQywxCorpAgent(ea, corpId, suitId);
        saveQywxMiniAppConfig(ea, corpId, eaWechatAccountBindDao.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID), suitId);

        QywxCustomerTemplateInfoEntity templateInfoEntity = qywxCustomerTemplateInfoDAO.selectOne(suitId);
        QywxCustomerAppInfoEntity queryRes = qywxCustomerAppInfoDAO.selectOne(suitId, corpId);
        if (null == queryRes) {
            QywxCustomerAppInfoEntity entity = new QywxCustomerAppInfoEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setSuitId(suitId);
            entity.setEa(ea);
            entity.setCorpId(corpId);
            entity.setAuthCode(authCode);
            entity.setAgentId(agentId);
            if (Objects.nonNull(templateInfoEntity)) {
                entity.setSuiteType(templateInfoEntity.getSuiteType());
            }
            qywxCustomerAppInfoDAO.insert(entity);

            //发送绑定信息到集成平台
            syncQywxBindInfo(ea, corpId, suitId, "0");
        } else {
            qywxCustomerAppInfoDAO.update(authCode, agentId, corpId, suitId, ea);
        }
    }

    public void syncQywxBindInfo(String ea, String corpId, String suitId, String status) {
        /*
        AyncQywxBindInfoArg arg = new AyncQywxBindInfoArg();
        arg.setBindInfos(Lists.newArrayList());
        AyncQywxBindInfoArg.BindInfo info = new AyncQywxBindInfoArg.BindInfo();
        info.setFsEa(ea);
        info.setOutEa(corpId);
        info.setBusinessType("QYWX_YXT");  //集成平台分配的业务类型
        info.setAppId(suitId);
        info.setStatus(status);  //status: 0 （正常） 1 （停用）
        arg.getBindInfos().add(info);

        qyweixinAccountBindManager.syncQywxBindInfo(arg);
     */
    }

    /**
     * 企微授权回调保存qywx_corp_agent_config
     * @param ea
     * @param suitId
     * @param corpId
     */
    private void saveQywxCorpAgent(String ea, String corpId, String suitId){
        // 根据ea查询有无记录
        // 代开发小程序回调，不处理后续业务逻辑
        if (QywxSuiteEnum.isMiniApp(suitId)) {
            return;
        }
        QywxCorpAgentConfigEntity corpAgentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        if (Objects.isNull(corpAgentConfigEntity)) {
            // 新增
            // 1 插入qywx_corp_agent_config
            QywxCorpAgentConfigEntity entity = new QywxCorpAgentConfigEntity();
            String id = UUIDUtil.getUUID();
            entity.setId(id);
            entity.setEa(ea);
            entity.setCorpid(corpId);
            entity.setAgentid("");
            entity.setSecret("");
            entity.setAppName("");
            entity.setSelfAppToken("");
            entity.setSelfAppEncodingAesKey("");
            entity.setCustomerContactSecret("");
            Date now = new Date();
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setCustomerAuthUrl(geneCustomerAuthUrl(ea));
            entity.setIsEncrypt(1);
            entity.setOriginCorpId("");
            agentConfigDAO.addAccount(entity);
            //删除旧的企微标签
            List<String> tagIds = tagModelDao.getQywxModelTagByEa(ea);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(tagIds)){
                tagModelDao.deleteTagModels(tagIds);
                for (String tagId : tagIds) {
                    displayOrderManager.removeFirstNestedId(ea, DisplayOrderConstants.TAG_IN_MODEL_PREFIX_KEY, tagId);
                }
            }
            doUpgradeMankeepToMankeepPro(ea);
            ThreadPoolUtils.execute(() -> initObjs(ea, corpId), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        } else {
            // 更新
            agentConfigDAO.updateCorpIdByEa(corpId, ea);
        }
    }

    /**
     * 企微授权回调保存qywx_miniapp_config
     * @param ea
     * @param corpId
     * @param wxAppId
     * @return
     */
    private void saveQywxMiniAppConfig(String ea, String corpId, String wxAppId, String suitId) {
        // 代开发小程序回调，不处理后续业务逻辑
        if (QywxSuiteEnum.isMiniApp(suitId)) {
            return;
        }
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wxAppId);
        if (miniappConfigEntity != null) {
            qywxMiniappConfigDAO.updateCorpIdByEa(corpId, ea);
            if (!Objects.equals(wxAppId, miniappConfigEntity.getAppid())) {
                qywxMiniappConfigDAO.updateAppIdCorpIdByEa(wxAppId, ea);
            }
        } else {
            QywxMiniappConfigEntity newMiniAppConfigEntity = new QywxMiniappConfigEntity();
            newMiniAppConfigEntity.setId(UUIDUtil.getUUID());
            newMiniAppConfigEntity.setEa(ea);
            newMiniAppConfigEntity.setSecret("-");
            newMiniAppConfigEntity.setAgentid("-");
            newMiniAppConfigEntity.setCorpid(corpId);
            newMiniAppConfigEntity.setAppid(wxAppId);
            Date now = new Date();
            newMiniAppConfigEntity.setCreateTime(now);
            newMiniAppConfigEntity.setUpdateTime(now);
            qywxMiniappConfigDAO.insert(newMiniAppConfigEntity);
        }
    }

    private void doUpgradeMankeepToMankeepPro(String ea) {
        String wxAppId = wechatAccountManager.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID).orElse(null);
        if (WxAppInfoEnum.isMankeepPro(wxAppId)) {
            return;
        }
        if (WxAppInfoEnum.isMankeep(wxAppId)) {
            eaWechatAccountBindDao.delete(ea, MKThirdPlatformConstants.PLATFORM_ID, wxAppId);
            eaWechatAccountBindDao.insert(ea, MKThirdPlatformConstants.PLATFORM_ID,
                    WxAppInfoEnum.MankeepPro.getAppId());
        }
    }

    private void initObjs(String ea, String corpid) {
        //企微客户对象
        wechatWorkExternalUserObjDescribeManager.getOrCreateWxWorkExternalUserObjDescribe(ea);
        userRoleManager.initWechatWorkExternalUserObjPrivilege(ea);
        //企微添加客户记录对象
        wechatFriendsRecordObjDescribeManager.getOrCreateWechatFriendsRecordObjDescribe(ea);
        userRoleManager.initWechatFriendsRecordObjPrivilege(ea);

        //企微客户群和客户群成员对象
        wechatGroupObjDescribeManager.getOrCreateWechatGroupObjDescribe(ea);
        userRoleManager.initWechatGroupObjPrivilege(ea);

        wechatGroupUserObjDescribeManager.getOrCreateWechatGroupUserObjDescribe(ea);
        userRoleManager.initWechatGroupUserObjPrivilege(ea);

        // 企微客户与客户群统计数据对象
        wechatAccountGroupStatisticsObjManager.getOrCreateObjDescribe(ea);
        userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.WECHAT_ACCOUNT_GROUP_STATISTICS_OBJ.getName());
        wechatAccountStatisticsObjManager.getOrCreateObjDescribe(ea);
        userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.WECHAT_ACCOUNT_STATISTICS_OBJ.getName());

        wechatFriendsRecordObjDescribeManager.tryUpdateCustomFieldLabel(ea);
        wechatFriendsRecordObjDescribeManager.tryUpdateTenantScene(ea);
        wechatGroupObjDescribeManager.tryUpdateCustomFieldLabel(ea);
        wechatGroupUserObjDescribeManager.tryUpdateCustomFieldLabel(ea);
        wechatWorkExternalUserObjDescribeManager.tryUpdateCustomFieldLabel(ea);
        otherObjectDescribeManager.tryAddExternalUserId(ea, CrmObjectApiNameEnum.CRM_LEAD.getName());
        otherObjectDescribeManager.tryAddExternalUserId(ea, CrmObjectApiNameEnum.CUSTOMER.getName());
        otherObjectDescribeManager.tryAddExternalUserId(ea, CrmObjectApiNameEnum.CONTACT.getName());
        //新增企微管理员角色并设置权限
        userRoleManager.initQywxAccountMarketingConfig(ea);
        ThreadPoolUtils.execute(() -> wechatWorkExternalUserObjManager.initEnterpriseData(ea), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        ThreadPoolUtils.execute(() -> wechatGroupObjDescribeManager.initData(ea), ThreadPoolTypeEnums.QYWX_ADDRESS_BOOK);
        if (corpid != null) {
            ThreadPoolUtils.execute(() -> wxWorkTagSynchronizationManager.syncToTagModel(ea, corpid), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
        ThreadPoolUtils.execute(() -> {
            wechatFriendsRecordObjDescribeManager.getOrCreateWechatFriendsRecordObjDescribe(ea);
            wechatFriendsRecordObjDescribeManager.syncExternalcontactData(ea);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    /**
     * 查询当前企业的企微绑定状态
     * @param ea
     * @return
     */
    public QywxBindStatusEnum getQywxBindStatus(String ea){
        // 检查agent表
        QywxCorpAgentConfigEntity agentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfigEntity != null) {
            // 检查授权记录表
            List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfigEntity.getCorpid(), ea);
            if (CollectionUtils.isNotEmpty(qywxCustomerAppInfoEntities)) {
                if (agentConfigEntity.getConfirmStatus() == 1) {
                    return QywxBindStatusEnum.FINISHED;
                }
                String agentId = qywxCustomerAppInfoEntities.get(0).getAgentId();
                // 检查企微可见范围设置
                boolean visible = checkQywxVisible(ea, agentId);
                if (visible) {
                    return QywxBindStatusEnum.AUTHED_PUBLISHED;
                } else {
                    return QywxBindStatusEnum.AUTHED_NOT_PUBLISH;
                }
            } else {
                return QywxBindStatusEnum.NOT_AUTH;
            }
        } else {
            return QywxBindStatusEnum.NOT_AUTH;
        }
    }

    /**
     * 校验企微微信是否设置可见范围和企微客户权限
     * @param ea
     * @return
     */
    public boolean checkQywxVisible(String ea, String agentId){
        String accessToken = getAccessToken(ea);
        // 获取应用信息
        QueryQywxAppDetailResult appDetailResult = queryQywxAppDetailResult(accessToken, agentId);
        if (appDetailResult == null) {
            return false;
        }
        // 如果未设置可见范围，直接返回false
        QueryQywxAppDetailResult.AllowUser allowUser = appDetailResult.getAllowUser();
        QueryQywxAppDetailResult.AllowParty allowParty = appDetailResult.getAllowParty();
        QueryQywxAppDetailResult.AllowTag allowTag = appDetailResult.getAllowTag();
        if ((allowUser == null || CollectionUtils.isEmpty(allowUser.getAppUsers())) && (allowParty == null || CollectionUtils.isEmpty(allowParty.getPartyids()))
                && (allowTag == null || CollectionUtils.isEmpty(allowTag.getTagIds()))) {
            return false;
        }
        // 校验企微客户权限
        ExternalConcactFollowUsersResult externalConcactFollowUsersResult = queryExternalConcactFollowUsers(accessToken);
        return externalConcactFollowUsersResult != null && externalConcactFollowUsersResult.isSuccess() && CollectionUtils.isNotEmpty(externalConcactFollowUsersResult.getFollowUsers());
    }


    public ExternalConcactFollowUsersResult queryExternalConcactFollowUsers(String accessToken){
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_follow_user_list?access_token=" + accessToken;
        return httpManager.executeGetHttp(url, new TypeToken<ExternalConcactFollowUsersResult>(){});
    }

    /**
     * 代开发获取token
     *
     * @param authCorpId
     * @param suitId
     * @param authCode
     * @return
     */
    public String getAgentAccessToken(String authCorpId, String suitId, String authCode) {
        if (Strings.isNullOrEmpty(authCorpId) || Strings.isNullOrEmpty(suitId)) {
            return null;
        }
        String accessToken = redisManager.getQywxAccessToken(authCorpId, suitId);
        if (Strings.isNullOrEmpty(accessToken)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + authCorpId + "&corpsecret=" + authCode;
            QywxGetTokenResult tokenResult = httpManager.executeGetHttp(url, new TypeToken<QywxGetTokenResult>() {
            });
            if (tokenResult.getErrCode() == 0) {
                accessToken = tokenResult.getAccessToken();
                redisManager.setQywxAccessToken(authCorpId, suitId, accessToken, tokenResult.getExpiresTime() - 600);
            } else {
                log.info("QywxManager getAgentAccessToken executeGetHttp result is error, tokenResult={}, corpId={}, authCode={}", tokenResult, authCorpId, authCode);
                return null;
            }
        }

        return accessToken;
    }

    public String getCorpJsApiTicket(String corpId, String accessToken, String agentConfigId) {
        if (Strings.isNullOrEmpty(corpId) || Strings.isNullOrEmpty(accessToken)) {
            return null;
        }
        String ticket = redisManager.getCorpJsapiTicket(corpId);
        if (Strings.isNullOrEmpty(ticket)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=" + accessToken;
            JsApiTicketResult ticketResult = httpManager.executeGetHttp(url, new TypeToken<JsApiTicketResult>() {
            });
            log.info("QywxManager getCorpJsApiTicket executeGetHttp, ticketResult={}, corpId={}, accessToken={}", ticketResult, corpId, accessToken);
            if (ticketResult == null) {
                return null;
            }
            if (ticketResult.getErrCode() == 0) {
                ticket = ticketResult.getTicket();
                redisManager.setCorpJsapiTicket(corpId, ticket, ticketResult.getExpiresTime());
            } else {
                String newAccessToken = reGetAccessToken(ticketResult.getErrCode(), agentConfigId);
                if (StringUtils.isNotBlank(newAccessToken)) {
                    String newUrl = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=" + newAccessToken;
                    JsApiTicketResult newTicketResult = httpManager.executeGetHttp(newUrl, new TypeToken<JsApiTicketResult>() {
                    });
                    if (newTicketResult != null && newTicketResult.getErrCode() == 0) {
                        ticket = newTicketResult.getTicket();
                        redisManager.setCorpJsapiTicket(corpId, ticket, newTicketResult.getExpiresTime());
                    }
                }
            }
        }
        return ticket;
    }

    public String getCustomerCorpJsApiTicket(String corpId, String accessToken, String suitId) {
        if (Strings.isNullOrEmpty(corpId) || Strings.isNullOrEmpty(accessToken)) {
            return null;
        }
        String ticket = redisManager.getCorpJsapiTicket(corpId + "_" + suitId);
        if (Strings.isNullOrEmpty(ticket)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=" + accessToken;
            JsApiTicketResult ticketResult = httpManager.executeGetHttp(url, new TypeToken<JsApiTicketResult>() {
            });
            log.info("QywxManager getCustomerCorpJsApiTicket executeGetHttp, ticketResult={}, corpId={}, accessToken={}", ticketResult, corpId, accessToken);
            if (ticketResult == null) {
                return null;
            }
            if (ticketResult.getErrCode() == 0) {
                ticket = ticketResult.getTicket();
                redisManager.setCorpJsapiTicket(corpId + "_" + suitId, ticket, ticketResult.getExpiresTime());
            }
        }
        return ticket;
    }

    public String getAgentJsApiTicket(String corpId, String agentId, String accessToken, String agentConfigId) {
//        if (Strings.isNullOrEmpty(agentId) || Strings.isNullOrEmpty(accessToken)) {
//            return null;
//        }
        if (Strings.isNullOrEmpty(accessToken)) {
            return null;
        }
        String ticket = redisManager.getAgentJsapiTicket(corpId, agentId);
        if (Strings.isNullOrEmpty(ticket)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token=" + accessToken + "&type=agent_config";
            JsApiTicketResult ticketResult = httpManager.executeGetHttp(url, new TypeToken<JsApiTicketResult>() {
            });
            log.info("QywxManager getAgentJsApiTicket executeGetHttp, ticketResult={}, corpId={}, agentId={}, accessToken={}", ticketResult, corpId, agentId, accessToken);
            if (ticketResult == null) {
                return null;
            }
            if (ticketResult.getErrCode() == 0) {
                ticket = ticketResult.getTicket();
                redisManager.setAgentJsapiTicket(corpId, agentId, ticket, ticketResult.getExpiresTime());
            } else {
                String newAccessToken = reGetAccessToken(ticketResult.getErrCode(), agentConfigId);
                if (StringUtils.isNotBlank(newAccessToken)) {
                    String newUrl = "https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token=" + newAccessToken + "&type=agent_config";
                    JsApiTicketResult newTicketResult = httpManager.executeGetHttp(newUrl, new TypeToken<JsApiTicketResult>() {
                    });
                    if (newTicketResult != null && newTicketResult.getErrCode() == 0) {
                        ticket = newTicketResult.getTicket();
                        redisManager.setAgentJsapiTicket(corpId, agentId, ticket, newTicketResult.getExpiresTime());
                    }
                }
            }
        }
        return ticket;
    }

    public String getCustomerAgentJsApiTicket(String corpId, String agentId, String accessToken, String suitId) {
        if (Strings.isNullOrEmpty(agentId) || Strings.isNullOrEmpty(accessToken)) {
            return null;
        }
        String ticket = redisManager.getAgentJsapiTicket(corpId + "_" + suitId, agentId);
        if (Strings.isNullOrEmpty(ticket)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token=" + accessToken + "&type=agent_config";
            JsApiTicketResult ticketResult = httpManager.executeGetHttp(url, new TypeToken<JsApiTicketResult>() {
            });
            log.info("QywxManager getCustomerAgentJsApiTicket executeGetHttp, ticketResult={}, corpId={}, agentId={}, accessToken={}", ticketResult, corpId, agentId, accessToken);
            if (ticketResult == null) {
                return null;
            }
            if (ticketResult.getErrCode() == 0) {
                ticket = ticketResult.getTicket();
                redisManager.setAgentJsapiTicket(corpId + "_" + suitId, agentId, ticket, ticketResult.getExpiresTime());
            }
        }
        return ticket;
    }

    public String getJsApiTicketSignature(String ticket, String noncestr, Long timestamp, String url) {
        if (StringUtils.isAnyEmpty(ticket, noncestr, url) || timestamp == null) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        builder.append("jsapi_ticket=")
                .append(ticket)
                .append("&")
                .append("noncestr=")
                .append(noncestr)
                .append("&")
                .append("timestamp=")
                .append(timestamp)
                .append("&")
                .append("url=")
                .append(url);
        return DigestUtils.sha1Hex(builder.toString());
    }

    public GetUserInfoResult getUserInfoByCode(String code, String accessToken, String agentConfigId) {
        if (Strings.isNullOrEmpty(code) || Strings.isNullOrEmpty(accessToken)) {
            return null;
        }

        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + accessToken + "&code=" + code;
        GetUserInfoResult userInfo = httpManager.executeGetHttp(url, new TypeToken<GetUserInfoResult>() {
        });
        log.info("QywxManager getUserInfoByCode executeGetHttp, userInfo={}, code={}, accessToken={}", userInfo, code, accessToken);
        if (userInfo.getErrCode() == 0) {
            return userInfo;
        } else {
            String newAccessToken = reGetAccessToken(userInfo.getErrCode(), agentConfigId);
            if (StringUtils.isNotBlank(newAccessToken)) {
                String newUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + newAccessToken + "&code=" + code;
                GetUserInfoResult newUserInfo = httpManager.executeGetHttp(newUrl, new TypeToken<GetUserInfoResult>() {
                });
                if (newUserInfo.getErrCode() == 0) {
                    return newUserInfo;
                }
            }
        }
        return null;
    }

    public String getStaffNameByUserId(String ea, String userId) {
        if (Strings.isNullOrEmpty(userId)) {
            return null;
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig != null) {
            String accessToken = this.getAccessToken(ea);
            StaffDetailResult staffDetailResult = this.getStaffDetail(ea, userId, accessToken, true);
            if (staffDetailResult != null) {
                return staffDetailResult.getName();
            }
        }
        return null;
    }

    /**
     * 获取企业微信员工信息
     *
     * @param ea           纷享ea
     * @param userId       企业微信员工ea
     * @param accessToken  调用接口accessToken
     * @param getFromCache 调用接口accessToken
     * @return
     */
    public StaffDetailResult getStaffDetail(String ea, String userId, String accessToken, Boolean getFromCache) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(accessToken) || StringUtils.isBlank(ea)) {
            return null;
        }
        StaffDetailResult staffDetailResult = null;
        if (getFromCache) {
            // 先从缓存查找
            staffDetailResult = redisManager.getQywxStaffDetail(ea, userId);
            if (staffDetailResult != null) {
                return staffDetailResult;
            }
        }
        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("userid", userId);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "user/get?" + httpManager.transformUrlParams(param);
        if (checkQywxMiniAppAccessToken(accessToken)) {
            staffDetailResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<StaffDetailResult>() {});
        } else {
            staffDetailResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<StaffDetailResult>() {});
        }
        if (staffDetailResult.isSuccess()) {
            redisManager.setQywxStaffDetail(ea, staffDetailResult);
        } else {
            return null;
        }
        return staffDetailResult;
    }

    /**
     * 获取企业微信客户列表
     */
    public List<String> getQyWxustomerCList(String accessToken, String userId) {
        if (StringUtils.isBlank(accessToken) || StringUtils.isBlank(userId)) {
            log.warn("QywxManager.getQyWxustomerCList accessToken or userId is null accessToken:{}, userId:{}", accessToken, userId);
            return Lists.newArrayList();
        }

        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("userid", userId);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/list?" + httpManager.transformUrlParams(param);
        GetExternalContactListResult getExternalcontactListResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<GetExternalContactListResult>() {
        });
        if (getExternalcontactListResult.getErrCode().equals(QYWXApiConstants.SUCCESS_CODE)) {
            return getExternalcontactListResult.getExternalUserid() == null ? new ArrayList<>(0) : getExternalcontactListResult.getExternalUserid();
        } else {
            log.warn("QywxManager.getQyWxustomerCList error getExternalcontactListResult:{}", getExternalcontactListResult);
            return new ArrayList<>(0);
        }
    }

    /**
     * 根据企业微信员工id批量获取客户userid
     *
     * @param ea
     * @param userIds
     * @return
     */
    public Set<String> batchGetQywxCustomerExternalUserId(String ea, List<String> userIds) {
        if (StringUtils.isEmpty(ea) || CollectionUtils.isEmpty(userIds)) {
            log.warn("QywxManager.batchGetQywxCustomerExternalUserId accessToken or userIds is null ea:{}, userId:{}", ea, userIds);
            return null;
        }

        Set<String> externlContactUserIdSet = new HashSet<>();
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/batch/get_by_user?access_token=" + qywxManager.getAccessToken(ea);
        BatchGetQywxCustomerArg arg = new BatchGetQywxCustomerArg();
        arg.setLimit(100);
        arg.setUserIds(userIds);
        BatchGetQywxCustomerResult externalContactListResult = httpManager.executePostHttpWithRetry(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
        });
        if (externalContactListResult == null || !externalContactListResult.isSuccess()) {
            log.info("QywxManager.batchGetQywxCustomerExternalUserId error externalcontactListResult:{}", externalContactListResult);
            return null;
        }
        if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
            return null;
        }

        for (BatchGetQywxCustomerResult.ExternalContact externalConcatObj : externalContactListResult.getExternalContactDetailList()) {
            externlContactUserIdSet.add(externalConcatObj.getExternalContactDetail().getExternalUserId());
        }
        //数据未取完
        boolean isRateLimit = false;
        boolean isSystemBusy = false;
        int tryCount = 0;
        String nextCursor = externalContactListResult.getNextCursor();
        while (tryCount <= 10 && StringUtils.isNotBlank(nextCursor)) {
            if (tryCount == 10) {
                log.info("QywxManager.batchGetQywxCustomerExternalUserId tryCount=10 ea:{} userIds:{}", ea, userIds);
            }
            if (!StringUtils.isNotEmpty(nextCursor)) break;
            url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/batch/get_by_user?access_token=" + qywxManager.getAccessToken(ea);
            arg.setCursor(nextCursor);
            try {
                if (isRateLimit) {
                    tryCount++;
                    Thread.sleep(tryCount * 500L);
                }
                if (isSystemBusy) {
                    tryCount++;
                    Thread.sleep(tryCount * 3000L);
                }
            } catch (InterruptedException e) {
            }
            externalContactListResult = httpManager.executePostHttpWithRetry(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
            });
            if (externalContactListResult == null || !externalContactListResult.isSuccess()) {
                if (externalContactListResult != null && externalContactListResult.getErrcode() == 45033) {
                    isRateLimit = true;
                    continue;
                }
                if (externalContactListResult != null && externalContactListResult.getErrcode() == -1) {
                    isSystemBusy = true;
                    continue;
                }
                log.info("QywxManager.batchGetQywxCustomerExternalUserId error externalcontactListResult:{}", externalContactListResult);
                break;
            }

            if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
                break;
            }
            for (BatchGetQywxCustomerResult.ExternalContact externalConcatObj : externalContactListResult.getExternalContactDetailList()) {
                isRateLimit= false;
                isSystemBusy= false;
                tryCount = 0;
                nextCursor = externalContactListResult.getNextCursor();
                externlContactUserIdSet.add(externalConcatObj.getExternalContactDetail().getExternalUserId());
            }
        }

        return externlContactUserIdSet;
    }

    /**
     * 根据企业微信员工id批量获取客户userid并消费
     *
     * @param ea
     * @param userIds
     * @return
     */
    public void batchGetQywxCustomerExternalUserIdV2(String ea, List<String> userIds, Consumer<Set<String>> consumer) {
        if (StringUtils.isEmpty(ea) || CollectionUtils.isEmpty(userIds)) {
            log.warn("QywxManager.batchGetQywxCustomerExternalUserIdV2 accessToken or userIds is null ea:{}, userId:{}", ea, userIds);
            return;
        }

        Set<String> externlContactUserIdSet = new HashSet<>();
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/batch/get_by_user?access_token=" + qywxManager.getAccessToken(ea);
        BatchGetQywxCustomerArg arg = new BatchGetQywxCustomerArg();
        arg.setLimit(100);
        arg.setUserIds(userIds);
        BatchGetQywxCustomerResult externalContactListResult = httpManager.executePostHttpWithRetry(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
        });
        if (externalContactListResult == null || !externalContactListResult.isSuccess()) {
            log.info("QywxManager.batchGetQywxCustomerExternalUserIdV2 error externalcontactListResult:{}", externalContactListResult);
            return;
        }
        if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
            return;
        }

        for (BatchGetQywxCustomerResult.ExternalContact externalConcatObj : externalContactListResult.getExternalContactDetailList()) {
            externlContactUserIdSet.add(externalConcatObj.getExternalContactDetail().getExternalUserId());
        }
        consumer.accept(externlContactUserIdSet);
        //数据未取完
        boolean isRateLimit = false;
        boolean isSystemBusy = false;
        int tryCount = 0;
        String nextCursor = externalContactListResult.getNextCursor();
        while (tryCount <= 10 && StringUtils.isNotBlank(nextCursor)) {
            if (tryCount == 10) {
                log.info("QywxManager.batchGetQywxCustomerExternalUserIdV2 tryCount=10 ea:{} userIds:{}", ea, userIds);
            }
            if (!StringUtils.isNotEmpty(nextCursor)) break;
            url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/batch/get_by_user?access_token=" + qywxManager.getAccessToken(ea);
            arg.setCursor(nextCursor);
            try {
                if (isRateLimit) {
                    tryCount++;
                    Thread.sleep(tryCount * 500L);
                }
                if (isSystemBusy) {
                    tryCount++;
                    Thread.sleep(tryCount * 3000L);
                }
            } catch (InterruptedException e) {
            }
            externalContactListResult = httpManager.executePostHttpWithRetry(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
            });
            if (externalContactListResult == null || !externalContactListResult.isSuccess()) {
                if (externalContactListResult != null && externalContactListResult.getErrcode() == 45033) {
                    isRateLimit = true;
                    continue;
                }
                if (externalContactListResult != null && externalContactListResult.getErrcode() == -1) {
                    isSystemBusy = true;
                    continue;
                }
                log.info("QywxManager.batchGetQywxCustomerExternalUserIdV2 error externalcontactListResult:{}", externalContactListResult);
                break;
            }

            if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
                break;
            }
            Set<String> batchExternlContactUserIdSet = new HashSet<>();
            for (BatchGetQywxCustomerResult.ExternalContact externalConcatObj : externalContactListResult.getExternalContactDetailList()) {
                isRateLimit= false;
                isSystemBusy= false;
                tryCount = 0;
                nextCursor = externalContactListResult.getNextCursor();
                batchExternlContactUserIdSet.add(externalConcatObj.getExternalContactDetail().getExternalUserId());
            }
            consumer.accept(batchExternlContactUserIdSet);
        }
    }

    /**
     * 获取客户详情
     */
    public List<GetExternalContactDetailResult> getExternalContactDetail(String ea, String accessToken, List<String> externalUserIdList) {
        if (StringUtils.isBlank(accessToken) || CollectionUtils.isEmpty(externalUserIdList)) {
            log.warn("QywxManager.getExternalContactDetail accessToken or externalUserIdList null accessToken:{}, externalUserIdList:{}", accessToken, externalUserIdList);
            return Lists.newArrayList();
        }
        List<GetExternalContactDetailResult> getExternalContactDetailResultList = Lists.newArrayList();
        for (String externalUserId : externalUserIdList) {
            Map<String, String> param = Maps.newHashMap();
            param.put("access_token", accessToken);
            param.put("external_userid", externalUserId);
            String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/get?" + httpManager.transformUrlParams(param);
            GetExternalContactDetailResult getExternalContactDetailResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<GetExternalContactDetailResult>() {});
            log.info("QywxManager -> getExternalContactDetail result:{}", getExternalContactDetailResult);

            // unionId不存在时，需要通过下面的方式重新获取，存在则不需要
            /*if (getExternalContactDetailResult.getErrCode() == 0 && StringUtils.isBlank(getExternalContactDetailResult.getExternalContact().getUnionId())) {
                // 将代开发应用获取的外部联系人id转化为自建应用的外部联系人id
                String newExternalUserId = getSelfAppExternalUserIdByProviderExternalUserId(ea, externalUserId);
                // 调用自建应用接口获取客户详情，并将获取到的unionId填充到原客户详情对象中
                GetExternalContactDetailResult getExternalContactDetailResult4Self = getExternalContactDetailBySelf(ea, newExternalUserId);
                if (Objects.nonNull(getExternalContactDetailResult4Self) && getExternalContactDetailResult4Self.getErrCode() == 0) {
                    log.info("QywxManager -> getExternalContactDetailBySelf unionId:{}", getExternalContactDetailResult4Self.getExternalContact().getUnionId());
                    getExternalContactDetailResult.getExternalContact().setUnionId(getExternalContactDetailResult4Self.getExternalContact().getUnionId());
                }
            }*/

            getExternalContactDetailResultList.add(getExternalContactDetailResult);
        }
        return getExternalContactDetailResultList;
    }

    /**
     * 根据ea查询代开发应用配置信息
     * @param ea
     * @return
     */
    public QywxCustomerAppInfoEntity getCustomerAppInfoEntityByEa(String ea){
        if (StringUtils.isBlank(ea)) {
            return null;
        }
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        String corpId = qywxCorpAgentConfigEntity.getCorpid();
        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(corpId, ea);
        if (CollectionUtils.isNotEmpty(qywxCustomerAppInfoEntities)) {
            return qywxCustomerAppInfoEntities.get(0);
        }
        return null;
    }

    /**
     * 根据ea查询自建应用配置信息
     * @param ea
     * @return
     */
    private QywxMiniappConfigEntity getMiniappConfigEntityByEa(String ea) {
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (miniappConfigEntity == null) {
            return null;
        }
        return miniappConfigEntity;
    }

    /**
     * 查询企业下所有员工详情
     *
     * @param accessToken     调用接口使用的accessToken
     * @param simpleList      是否获取简易列表 简易：只有name，userId，department
     * @param needRefreshData 是否需要刷新数据
     * @return 企业微信员工信息
     */
    @FilterLog
    public List<DepartmentStaffResult.StaffInfo> queryAllStaff(String ea, String accessToken, boolean simpleList, boolean needRefreshData) {
        // 先从DB查询
        List<DepartmentStaffResult.StaffInfo> staffInfoList = queryAllStaffFormDB(ea);
        if (CollectionUtils.isNotEmpty(staffInfoList) && !needRefreshData) {
            return staffInfoList;
        }
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.queryStaff accessToken or userId is null accessToken:{}", accessToken);
            return staffInfoList;
        }
        staffInfoList = Lists.newArrayList();
        // 获取企业所有部门
        DepartmentListResult departmentListResult = queryDepartment(accessToken);
        if (departmentListResult != null && CollectionUtils.isNotEmpty(departmentListResult.getDepartmentList())) {
            // 获取部门下员工
            List<Integer> departmentIds = departmentListResult.getDepartmentList().stream().map(Department::getId).collect(Collectors.toList());
            staffInfoList = getStaffByDepartmentId(accessToken, departmentIds, false, simpleList);
        }
        // 查询企业标签
        QueryTagListResult queryTagListResult = queryTagList(accessToken);
        if (queryTagListResult != null && CollectionUtils.isNotEmpty(queryTagListResult.getTagDetailList())) {
            // 获取标签下员工数据
            List<Long> tagIds = queryTagListResult.getTagDetailList().stream().map(TagDetail::getTagId).collect(Collectors.toList());
            // 标签下人员接口没有详情需要异步批量获取
            List<DepartmentStaffResult.StaffInfo> tagStaffInfoList = getStaffByTagId(accessToken, tagIds);
            staffInfoList.addAll(mergeTagAndDepartmentStaffInfo(ea, accessToken, staffInfoList, tagStaffInfoList));
        }
        // 单个员工同步功能
        // 获取agent_id
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        List<QywxCustomerAppInfoEntity> appInfoEntityList = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), agentConfig.getEa());
        String agentId = appInfoEntityList.get(0).getAgentId();
        // 获取应用信息
        QueryQywxAppDetailResult appDetailResult = queryQywxAppDetailResult(accessToken, agentId);
        if (appDetailResult != null && appDetailResult.getAllowUser() != null && CollectionUtils.isNotEmpty(appDetailResult.getAllowUser().getAppUsers())) {
            // 获取所有单个员工
            List<String> userIds = appDetailResult.getAllowUser().getAppUsers().stream().map(QueryQywxAppDetailResult.AppUser::getUserId).collect(Collectors.toList());
            staffInfoList.addAll(getStaffInfoByUserIds(ea, accessToken, userIds));
        }

//        staffInfoList = staffInfoList.stream().filter(distinctByKey(StaffInfo::getUserId)).sorted(Comparator.comparing(StaffInfo::getName)).collect(Collectors.toList());
        staffInfoList = staffInfoList.stream().filter(distinctByKey(StaffInfo::getUserId)).sorted(Comparator.comparing(StaffInfo::getUserId)).collect(Collectors.toList());
        // 异步插入/更新数据
        List<DepartmentStaffResult.StaffInfo> finalStaffInfoList = staffInfoList;
        ThreadPoolUtils.execute(() -> upsertAddressBook(ea, finalStaffInfoList), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        log.info("get qywx user size:{}", CollectionUtils.isNotEmpty(staffInfoList) ? staffInfoList.size() : 0);
        return staffInfoList;
    }

    /**
     * 根据员工id列表调用企微接口查询企微员工信息
     * @param ea 企业标识
     * @param accessToken 调用企业凭证
     * @param userIds 员工id列表
     * @return 员工详细信息列表
     */
    private List<DepartmentStaffResult.StaffInfo> getStaffInfoByUserIds(String ea, String accessToken, List<String> userIds) {
        List<DepartmentStaffResult.StaffInfo> resultList = Lists.newCopyOnWriteArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(userIds.size());
        for (String userId : userIds) {
            ThreadPoolUtils.execute(() -> {
                try {
                    // 调用企微接口，根据员工id获取单个员工信息
                    StaffDetailResult staffDetailResult = getStaffDetail(ea, userId, accessToken, false);
                    if (staffDetailResult != null) {
                        DepartmentStaffResult.StaffInfo staffInfo = BeanUtil.copy(staffDetailResult, DepartmentStaffResult.StaffInfo.class);
                        resultList.add(staffInfo);
                    }
                } catch (Exception e) {
                    log.error("QywxManager.getStaffInfoByUserIds getStaffDetail error e:", e);
                } finally {
                    countDownLatch.countDown();
                }
            }, ThreadPoolTypeEnums.QYWX_ADDRESS_BOOK);
        }
        try {
            countDownLatch.await(30L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("QywxManager.getStaffInfoByUserIds.await error e:", e);
        }
        return resultList;
    }

    @FilterLog
    public List<DepartmentStaffResult.StaffInfo> queryAllStaffFormDB(String ea) {
        List<DepartmentStaffResult.StaffInfo> staffInfoList = Lists.newArrayList();
        List<QyWxAddressBookEntity> qyWxAddressBookEntityList = qywxAddressBookManager.queryByEa(ea);
        if (CollectionUtils.isEmpty(qyWxAddressBookEntityList)) {
            return staffInfoList;
        }
        for (QyWxAddressBookEntity qyWxAddressBookEntity : qyWxAddressBookEntityList) {
            DepartmentStaffResult.StaffInfo staffInfo = new DepartmentStaffResult.StaffInfo();
            staffInfo.setUserId(qyWxAddressBookEntity.getUserId());
            staffInfo.setName(qyWxAddressBookEntity.getName());
            staffInfo.setDepartment(StringUtils.isNotBlank(qyWxAddressBookEntity.getDepartment()) ? GsonUtil.getGson().fromJson(qyWxAddressBookEntity.getDepartment(), new TypeToken<List<Integer>>() {
            }.getType()) : Lists.newArrayList());
            staffInfo.setOrder(StringUtils.isNotBlank(qyWxAddressBookEntity.getOrder()) ? GsonUtil.getGson().fromJson(qyWxAddressBookEntity.getOrder(), new TypeToken<List<Long>>() {
            }.getType()) : Lists.newArrayList());
            staffInfo.setPosition(qyWxAddressBookEntity.getPosition());
            staffInfo.setMobile(qyWxAddressBookEntity.getMobile());
            staffInfo.setGender(qyWxAddressBookEntity.getGender());
            staffInfo.setAvatar(qyWxAddressBookEntity.getAvatar());
            staffInfo.setStatus(qyWxAddressBookEntity.getStatus());
            staffInfoList.add(staffInfo);
        }
        return staffInfoList;
    }

    /**
     * 更新或插入企业微信通讯录信息
     *
     * @param ea 企业微信企业ID
     * @param staffInfos 员工信息列表
     */
    private void upsertAddressBook(String ea, List<DepartmentStaffResult.StaffInfo> staffInfos) {
        // 检查输入参数是否为空，如果为空则直接返回
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(staffInfos)) {
            return;
        }

        // 初始化需要删除的数据列表
        List<QyWxAddressBookEntity> needDeleteData = Lists.newArrayList();

        // 查询出需要删除的数据
        List<QyWxAddressBookEntity> qyWxAddressBookEntityList = qyWxAddressBookDAO.queryByEa(ea);
        if (CollectionUtils.isNotEmpty(qyWxAddressBookEntityList)) {
            // 删除多余数据
            needDeleteData = qyWxAddressBookEntityList.stream()
                .filter(data -> staffInfos.stream().noneMatch(staffInfo -> staffInfo.getUserId().equals(data.getUserId())))
                .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(needDeleteData)) {
                // 分页处理删除操作
                PageUtil<QyWxAddressBookEntity> pageUtil = new PageUtil<>(needDeleteData, 30);
                for (int i = 1; i <= pageUtil.getPageCount(); i++) {
                    List<String> deleteIdList = Lists.newArrayList();
                    List<String> deleteUserIdList = Lists.newArrayList();
                    for (QyWxAddressBookEntity qyWxAddressBookEntity : pageUtil.getPagedList(i)) {
                        deleteIdList.add(qyWxAddressBookEntity.getId());
                        deleteUserIdList.add(qyWxAddressBookEntity.getUserId());
                    }
                    // 执行删除操作
                    qyWxAddressBookDAO.deleteQyWxAddressBookEntity(deleteIdList);
                    // 解绑用户关系
                    userRelationManager.unBindOuterUserRelation(ea, deleteUserIdList, UserTypeEnum.QYWX);
                }
            }
        }

        // 处理通过标签获取的企微员工数据，如果没有department的员工，查询员工信息，获取department
        List<DepartmentStaffResult.StaffInfo> staffInfoList = staffInfos.stream()
            .filter(staffInfo -> CollectionUtils.isEmpty(staffInfo.getDepartment()))
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(staffInfoList)){
            for(DepartmentStaffResult.StaffInfo staffInfo : staffInfos){
                String accessToken = getAccessToken(ea);
                StaffDetailResult staffDetailResult = this.getStaffDetail(ea, staffInfo.getUserId(), accessToken, false);
                if (staffDetailResult != null){
                    // 更新员工信息
                    staffInfo.setDepartment(staffDetailResult.getDepartment());
                    staffInfo.setOrder(staffDetailResult.getOrder());
                    staffInfo.setMobile(staffDetailResult.getMobile());
                    staffInfo.setGender(staffDetailResult.getGender());
                    staffInfo.setAvatar(staffDetailResult.getAvatar());
                    staffInfo.setPosition(staffDetailResult.getPosition());
                    staffInfo.setStatus(staffDetailResult.getStatus());
                }
            }
        }

        // 遍历员工信息，插入或更新到通讯录
        for (DepartmentStaffResult.StaffInfo staffInfo : staffInfos) {
            try {
                QyWxAddressBookEntity qyWxAddressBookEntity = new QyWxAddressBookEntity();
                qyWxAddressBookEntity.setId(UUIDUtil.getUUID());
                qyWxAddressBookEntity.setEa(ea);
                qyWxAddressBookEntity.setUserId(staffInfo.getUserId());
                qyWxAddressBookEntity.setName(staffInfo.getName());
                qyWxAddressBookEntity.setDepartment(CollectionUtils.isEmpty(staffInfo.getDepartment()) ? null : GsonUtil.getGson().toJson(staffInfo.getDepartment()));
                qyWxAddressBookEntity.setOrder(CollectionUtils.isEmpty(staffInfo.getOrder()) ? null : GsonUtil.getGson().toJson(staffInfo.getOrder()));
                qyWxAddressBookEntity.setPosition(staffInfo.getPosition());
                qyWxAddressBookEntity.setMobile(staffInfo.getMobile());
                qyWxAddressBookEntity.setGender(staffInfo.getGender());
                qyWxAddressBookEntity.setAvatar(staffInfo.getAvatar());
                qyWxAddressBookEntity.setStatus(staffInfo.getStatus());

                // 插入或更新通讯录信息
                qyWxAddressBookDAO.upsertQyWxAddressBookEntity(qyWxAddressBookEntity);
                // 绑定用户关系
                userRelationManager.bindOuterQyUserRelation(ea, null, staffInfo.getUserId(), UserTypeEnum.QYWX);
            } catch (Exception e) {
                log.warn("QywxManager.upsertAddressBook error e:", e);
            }
        }

        // 写入对象处理
        List<QywxEmployeeToCrmArg> qywxEmployeeToCrmArgs = staffInfos.stream()
            .map(staffInfo -> {
                QywxEmployeeToCrmArg qywxEmployeeToCrmArg = BeanUtil.copy(staffInfo, QywxEmployeeToCrmArg.class);
                qywxEmployeeToCrmArg.setDepartment(qywxEmployeeManager.integerToString(staffInfo.getDepartment()));
                qywxEmployeeToCrmArg.setOrder(qywxEmployeeManager.longToString(staffInfo.getOrder()));
                return qywxEmployeeToCrmArg;
            }).collect(Collectors.toList());

        // 更新CRM系统中的员工信息
        qywxEmployeeManager.updateAll(ea, qywxEmployeeToCrmArgs);
    }

    private List<DepartmentStaffResult.StaffInfo> mergeTagAndDepartmentStaffInfo(String ea, String accessToken, List<DepartmentStaffResult.StaffInfo> staffInfoList,
                                                                                 List<DepartmentStaffResult.StaffInfo> tagInfoList) {
        List<String> needSearchUserIds = tagInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(staffInfoList)) {
            // 去除部门已有数据
            needSearchUserIds = tagInfoList.stream().filter(data -> staffInfoList.stream().noneMatch(staffInfo -> staffInfo.getUserId().equals(data.getUserId()))).map(StaffInfo::getUserId)
                    .collect(Collectors.toList());
        }
        // 批量并行拉取数据
        List<DepartmentStaffResult.StaffInfo> tempData = Lists.newCopyOnWriteArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(needSearchUserIds.size());
        for (String userId : needSearchUserIds) {
            ThreadPoolUtils.execute(() -> {
                try {
                    // 查询人员详情
                    StaffDetailResult staffDetailResult = getStaffDetail(ea, userId, accessToken, false);
                    if (staffDetailResult != null) {
                        DepartmentStaffResult.StaffInfo staffInfo = BeanUtil.copy(staffDetailResult, DepartmentStaffResult.StaffInfo.class);
                        tempData.add(staffInfo);
                    }
                } catch (Exception e) {
                    log.warn("QywxManager.mergeTagAndDepartmentStaffInfo getStaffDetail error e:{}", e);
                } finally {
                    countDownLatch.countDown();
                }
            }, ThreadPoolTypeEnums.QYWX_ADDRESS_BOOK);
        }
        try {
            countDownLatch.await(30L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("QywxManager.getStaffByTagId.await error e:{}", e);
        }
        return tempData;
    }

    /**
     * 获取全部员工id
     */
    public List<String> queryAllStaffId(String ea) {
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        List<String> userIds = Lists.newArrayList();
        // 先从DB查询
        List<DepartmentStaffResult.StaffInfo> staffInfoList = queryAllStaffFormDB(ea);
        if (CollectionUtils.isNotEmpty(staffInfoList)) {
            return staffInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
        }
        if (agentConfig == null) {
            log.warn("QywxManager.queryAllStaffId is null ea:{}", ea);
            return userIds;
        }
        String accessToken = getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.queryAllStaffId accessToken is null ea:{}", ea);
            return userIds;
        }
        staffInfoList = Lists.newArrayList();
        // 获取企业所有部门
        DepartmentListResult departmentListResult = queryDepartment(accessToken);
        if (departmentListResult != null && CollectionUtils.isNotEmpty(departmentListResult.getDepartmentList())) {
            // 获取部门下员工
            List<Integer> departmentIds = departmentListResult.getDepartmentList().stream().map(Department::getId).collect(Collectors.toList());
            staffInfoList = getStaffByDepartmentId(accessToken, departmentIds, false, false);
        }
        // 查询企业标签
        QueryTagListResult queryTagListResult = queryTagList(accessToken);
        if (queryTagListResult != null && CollectionUtils.isNotEmpty(queryTagListResult.getTagDetailList())) {
            // 获取标签下员工数据
            List<Long> tagIds = queryTagListResult.getTagDetailList().stream().map(TagDetail::getTagId).collect(Collectors.toList());
            // 标签下人员接口没有详情需要异步批量获取
            List<DepartmentStaffResult.StaffInfo> tagStaffInfoList = getStaffByTagId(accessToken, tagIds);
            staffInfoList.addAll(mergeTagAndDepartmentStaffInfo(ea, accessToken, staffInfoList, tagStaffInfoList));
        }
        staffInfoList = staffInfoList.stream().filter(distinctByKey(StaffInfo::getUserId)).collect(Collectors.toList());
        // 异步插入/更新数据
        List<DepartmentStaffResult.StaffInfo> finalStaffInfoList = staffInfoList;
        ThreadPoolUtils.execute(() -> upsertAddressBook(ea, finalStaffInfoList), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return staffInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
    }

    /**
     * 获取部门
     */
    public DepartmentListResult queryDepartment(String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.queryDepartment accessToken is null accessToken:{}", accessToken);
            return null;
        }

        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "department/list?" + httpManager.transformUrlParams(param);
        DepartmentListResult departmentListResult;
        if (checkQywxMiniAppAccessToken(accessToken)) {
            departmentListResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentListResult>() {});
        } else {
            departmentListResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentListResult>() {});
        }
        if (departmentListResult == null || !departmentListResult.isSuccess()) {
            log.warn("QywxManager.queryDepartment error departmentListResult:{}", departmentListResult);
            return null;
        }
        return departmentListResult;
    }

    public SingleDepartmentResult querySingleDepartment(String accessToken, Integer id) {
        if (StringUtils.isBlank(accessToken) || id == null) {
            log.warn("QywxManager.querySingleDepartment accessToken or id is null accessToken:{}", accessToken);
            return null;
        }

        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("id", String.valueOf(id));
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "department/get?" + httpManager.transformUrlParams(param);
        SingleDepartmentResult singleDepartmentResult;
        if (checkQywxMiniAppAccessToken(accessToken)) {
            singleDepartmentResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<SingleDepartmentResult>() {});
        } else {
            singleDepartmentResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<SingleDepartmentResult>() {});
        }
        if (singleDepartmentResult == null || !singleDepartmentResult.isSuccess()) {
            log.warn("QywxManager.queryDepartment error singleDepartmentResult:{}", singleDepartmentResult);
            return null;
        }
        return singleDepartmentResult;
    }


    /**
     * 获取应用信息
     * @param accessToken 调用接口凭证
     * @param agentId 应用id
     * @return
     */
    public QueryQywxAppDetailResult queryQywxAppDetailResult(String accessToken, String agentId) {
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.queryQywxAppDetailResult accessToken is null accessToken:{}", accessToken);
            return null;
        }

        // 构建调用微信“获取应用”接口参数
        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("agentid", agentId);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "agent/get?" + httpManager.transformUrlParams(param);
        QueryQywxAppDetailResult appDetailResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<QueryQywxAppDetailResult>() {});
        if (appDetailResult == null || !appDetailResult.isSuccess()) {
            log.warn("QywxManager.queryQywxAppDetailResult error appDetailResult:{}", appDetailResult);
            return null;
        }
        return appDetailResult;
    }

    public DepartmentSimpleResult querySimpleDepartment(String accessToken,Integer departmentId) {
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.querySimpleDepartment accessToken is null accessToken:{}", accessToken);
            return null;
        }

        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("id",departmentId);
        String url = httpManager.transformUrlParamsV2(QYWXApiConstants.QYWX_RESET_API_HOST + "department/simplelist",param);
        DepartmentSimpleResult departmentSimpleResult;
        if (checkQywxMiniAppAccessToken(accessToken)) {
            departmentSimpleResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentSimpleResult>() {});
        } else {
            departmentSimpleResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentSimpleResult>() {});
        }
        if (departmentSimpleResult == null || !departmentSimpleResult.isSuccess()) {
            log.warn("QywxManager.querySimpleDepartment error departmentSimpleResult:{}", departmentSimpleResult);
            return null;
        }
        return departmentSimpleResult;
    }

    private String handleDepartmentName(Map<Integer,Department> departmentMap, Integer departmentId, StringBuilder name){
        Department department = departmentMap.get(departmentId);
        if (department != null) {
            if (department.getParentId() != 1) { // 父部门id。根部门为1
                handleDepartmentName(departmentMap, department.getParentId(), name);
            }
            name.append(department.getName());
            name.append(">");
        }
        return name.toString();
    }

    /**
     * 组装部门全名称
     * @param departmentMap
     * @param departmentId
     * @return
     */
    @FilterLog
    public String buildDepartmentFullName(Map<Integer,Department> departmentMap, Integer departmentId){
        String fullName = handleDepartmentName(departmentMap, departmentId, new StringBuilder());
        if (fullName.endsWith(">")) {
            fullName = fullName.substring(0, fullName.length() - 1);
        }
        return fullName;
    }


    /*public static void main(String[] args) {
        Department department = new Department();
        department.setName(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMANAGER_1485));
        department.setId(2);
        department.setParentId(1);

        Department department2 = new Department();
        department2.setName(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMANAGER_1490));
        department2.setId(22);
        department2.setParentId(2);

        Department department3 = new Department();
        department3.setName(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMANAGER_1495));
        department3.setId(222);
        department3.setParentId(22);

        List<Department> departmentList = Lists.newArrayList(department, department2, department3);
        Map<Integer, Department> departmentMap = departmentList.stream().collect(Collectors.toMap(Department::getId, Function.identity(), (v1, v2) -> v1));

        String name = buildDepartmentName(departmentMap, 222, new StringBuilder());
        if (name.endsWith("-")) {
            name = name.substring(0, name.length() - 1);
        }
        System.out.println(name);
    }*/

    /**
     * 判断是否是自建应用token
     * @param accessToken
     * @return
     */
    public boolean checkQywxMiniAppAccessToken(String accessToken){
        String qywxMiniAppAccessToken = redisManager.getQywxSelfAppAccessToken(accessToken);
        if (StringUtils.isNotBlank(qywxMiniAppAccessToken)) {
            return true;
        }
        return false;
    }

    /**
     * 获取标签列表
     *
     * @param accessToken
     * @return
     */
    public QueryTagListResult queryTagList(String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.queryTagList accessToken is null accessToken:{}", accessToken);
            return null;
        }
        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "tag/list?" + httpManager.transformUrlParams(param);
        QueryTagListResult queryTagListResult;
        // 判断token是否是小程序token
        if (checkQywxMiniAppAccessToken(accessToken)) {
            queryTagListResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<QueryTagListResult>() {});
        } else {
            queryTagListResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<QueryTagListResult>() {});
        }
        if (queryTagListResult == null || !queryTagListResult.isSuccess()) {
            log.warn("QywxManager.queryTagList error queryTagListResult:{}", queryTagListResult);
            return null;
        }
        return queryTagListResult;
    }

    public String getAppLogo(String ea) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (null == agentConfig) {
            return null;
        }
        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), ea);
        QywxCustomerAppInfoEntity authAppInfo = null;
        if (!CollectionUtil.isEmpty(qywxCustomerAppInfoEntities)) {
            authAppInfo = qywxCustomerAppInfoEntities.get(0);
        } else {
            return null;
        }
        String agentAccessToken = getAgentAccessToken(authAppInfo.getCorpId(), authAppInfo.getSuitId(), authAppInfo.getAuthCode());
        String url = "https://qyapi.weixin.qq.com/cgi-bin/agent/get?access_token=" + agentAccessToken + "&agentid=" + qywxCustomerAppInfoEntities.get(0).getAgentId();
        if (org.apache.commons.lang.StringUtils.isBlank(agentAccessToken)) {
            return null;
        }
        QueryQywxAppDetailResult res = httpManager.executeGetHttp(url, new TypeToken<QueryQywxAppDetailResult>() {
        });
        if (null != res && 0 == res.getErrcode()) {
            return res.getSquareLogoUrl();
        }
        return null;
    }

    public List<DepartmentStaffResult.StaffInfo> getStaffByTagId(String accessToken, List<Long> tagIds) {
        List<DepartmentStaffResult.StaffInfo> staffInfoList = Lists.newCopyOnWriteArrayList();
        if (StringUtils.isBlank(accessToken) || CollectionUtils.isEmpty(tagIds)) {
            log.warn("QywxManager.getStaffByTagId accessToken is null or tagIdsIs null");
            return staffInfoList;
        }
        CountDownLatch countDownLatch = new CountDownLatch(tagIds.size());
        for (Long tagId : tagIds) {
            ThreadPoolUtils.execute(() -> {
                try {
                    // 查询标签下人员
                    DepartmentStaffResult departmentStaffResult = getStaffByTagId(accessToken, tagId);
                    if (departmentStaffResult != null) {
                        staffInfoList.addAll(departmentStaffResult.getStaffList());
                    }
                } catch (Exception e) {
                    log.warn("QywxManager.getStaffByTagId getStaffByDepartmentId error e:{}", e);
                } finally {
                    countDownLatch.countDown();
                }
            }, ThreadPoolTypeEnums.QYWX_ADDRESS_BOOK);
        }
        try {
            countDownLatch.await(30L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("QywxManager.getStaffByTagId.await error e:{}", e);
        }
        return staffInfoList.stream().filter(distinctByKey(StaffInfo::getUserId)).collect(Collectors.toList());
    }

    public DepartmentStaffResult getStaffByTagId(String accessToken, Long tagId) {
        if (StringUtils.isBlank(accessToken) || tagId == null) {
            log.warn("QywxManager.DepartmentStaffResult accessToken or tagId is null accessToken:{}, tagId:{}", accessToken, tagId);
            return null;
        }
        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("tagid", tagId + "");
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "tag/get?" + httpManager.transformUrlParams(param);
        DepartmentStaffResult departmentStaffResult;
        if (checkQywxMiniAppAccessToken(accessToken)) {
            departmentStaffResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentStaffResult>() {});
        } else {
            departmentStaffResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentStaffResult>() {});
        }
        if (departmentStaffResult == null || !departmentStaffResult.isSuccess()) {
            log.warn("QywxManager.getStaffByTagId error departmentStaffResult:{}", departmentStaffResult);
            return null;
        }
        return departmentStaffResult;
    }

    /**
     * 根据部门获取员工信息
     */
    public DepartmentStaffResult getStaffByDepartmentId(String accessToken, Integer departmentId, boolean fetchChild, boolean simpleList) {
        if (StringUtils.isBlank(accessToken) || departmentId == null) {
            log.warn("QywxManager.getStaffByDepartmentId accessToken or departmentId is null accessToken:{}, departmentId:{}", accessToken, departmentId);
            return null;
        }
        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("department_id", departmentId + "");
        param.put("fetch_child", (fetchChild ? 1 : 0) + "");
        String url = null;
        if (simpleList) {
            url = QYWXApiConstants.QYWX_RESET_API_HOST + "user/simplelist?" + httpManager.transformUrlParams(param);
        } else {
            url = QYWXApiConstants.QYWX_RESET_API_HOST + "user/list?" + httpManager.transformUrlParams(param);
        }
        DepartmentStaffResult departmentStaffResult;
        // 判断token是否是小程序token
        if (checkQywxMiniAppAccessToken(accessToken)) {
            departmentStaffResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentStaffResult>() {});
        } else {
            departmentStaffResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<DepartmentStaffResult>() {});
        }
        if (departmentStaffResult == null || !departmentStaffResult.isSuccess()) {
            log.warn("QywxManager.getStaffByDepartmentId error departmentStaffResult:{}", departmentStaffResult);
            return null;
        }
        return departmentStaffResult;
    }

    public List<DepartmentStaffResult.StaffInfo> getStaffByDepartmentId(String accessToken, List<Integer> departmentIds, boolean fetchChild, boolean simpleList){
        return getStaffByDepartmentId(accessToken, departmentIds, fetchChild, simpleList, null);
    }

    /**
     * 根据部门获取员工信息
     * 使用这个：com.facishare.marketing.provider.manager.qywx.QywxUserManager#getQywxUserIdByQywxDepartmentV2()
     */
    @Deprecated
    public List<DepartmentStaffResult.StaffInfo> getStaffByDepartmentId(String accessToken, List<Integer> departmentIds, boolean fetchChild, boolean simpleList, String ea) {
        List<DepartmentStaffResult.StaffInfo> staffInfoList = Lists.newCopyOnWriteArrayList();
        if (StringUtils.isBlank(accessToken) || CollectionUtils.isEmpty(departmentIds)) {
            return staffInfoList;
        }
        CountDownLatch countDownLatch = new CountDownLatch(departmentIds.size());
        ExecutorService departmentExecutorService = NamedThreadPool.newFixedThreadPool(5, "get_department_staff");
        for (Integer departmentId : departmentIds) {
            departmentExecutorService.submit(() -> {
                try {
                    // 查询部门人员
                    DepartmentStaffResult departmentStaffResult;
                    if (StringUtils.isBlank(ea)) {
                        departmentStaffResult = getStaffByDepartmentId(accessToken, departmentId, fetchChild, simpleList);
                    } else {
                        departmentStaffResult = getStaffByDepartmentIdFormDB(accessToken, departmentId, fetchChild, ea);
                    }
                    if (departmentStaffResult != null) {
                        staffInfoList.addAll(departmentStaffResult.getStaffList());
                    }
                } catch (Exception e) {
                    log.warn("QywxManager.getStaffByDepartmentId getStaffByDepartmentId error e:{}", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.warn("QywxManager.getStaffByDepartmentId getStaffByDepartmentId.await error e:{}", e);
        } finally {
            if (!departmentExecutorService.isShutdown()) {
                departmentExecutorService.shutdown();
            }
        }
        return staffInfoList.stream().filter(distinctByKey(StaffInfo::getUserId)).collect(Collectors.toList());
    }

    private DepartmentStaffResult getStaffByDepartmentIdFormDB(String accessToken, Integer departmentId, boolean fetchChild, String ea) {
        DepartmentStaffResult departmentStaffResult = new DepartmentStaffResult();
        List<StaffInfo> staffInfoList = Lists.newArrayList();
        departmentStaffResult.setStaffList(staffInfoList);
        List<Integer> departmentIds = Lists.newArrayList();
        if (fetchChild) {
            DepartmentSimpleResult departmentSimpleResult = this.querySimpleDepartment(accessToken, departmentId);
            if (departmentSimpleResult != null && CollectionUtils.isNotEmpty(departmentSimpleResult.getDepartmentList())) {
                List<Integer> departmentLists = departmentSimpleResult.getDepartmentList().stream().map(DepartmentSimpleResult.DepartmentSimple::getId).collect(Collectors.toList());
                departmentIds.addAll(departmentLists);
            }
        } else {
            departmentIds.add(departmentId);
        }
        List<String> collect = departmentIds.stream().map(String::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            List<QyWxAddressBookEntity> qyWxAddressBookEntities = qyWxAddressBookDAO.queryStaffByEaAnddepartmentIds(ea, collect);
            if (CollectionUtils.isNotEmpty(qyWxAddressBookEntities)) {
                qyWxAddressBookEntities.forEach(entity -> {
                    StaffInfo staffInfo = BeanUtil.copy(entity, StaffInfo.class);
                    staffInfo.setDepartment(gson.fromJson(entity.getDepartment(), new TypeToken<List<Integer>>() {}.getType()));
                    staffInfo.setOrder(gson.fromJson(entity.getOrder(), new TypeToken<List<Long>>() {}.getType()));
                    staffInfoList.add(staffInfo);
                });
            }
        }
        return departmentStaffResult;
    }


    /**
     * 查询员工信息
     * 1.查DB，存在直接返回    2.DB没有用corpId和mobile调用企业微信接口
     * 3.要是企业微信没有提供corpId+手机号的接口查员工信息 就起一个线程执行queryAllStaff()
     *
     * @param ea            企业账号
     * @param mobile        员工手机号
     * @param accessToken   用户凭证
     * @return              员工信息
     */
    public List<DepartmentStaffResult.StaffInfo> queryStaffByEaAndMobile(String ea, String mobile, String accessToken) {
        List<StaffInfo> staffInfoList = new ArrayList<>();
        List<QyWxAddressBookEntity> qyWxAddressBookEntityList = qywxAddressBookManager.queryStaffByEaAndMobile(ea, Lists.newArrayList(mobile));
        if (CollectionUtils.isNotEmpty(qyWxAddressBookEntityList)) {
            staffInfoList = convertToStaffInfoList(qyWxAddressBookEntityList);
            return staffInfoList;
        }
        // 用corpId和mobile调企业微信接口获取员工信息
        String userId = getUserIdByMobile(mobile, accessToken);
        StaffDetailResult staffDetail = getStaffDetail(ea, userId, accessToken, true);
        if (staffDetail != null) {
            StaffInfo staffInfo = BeanUtil.copy(staffDetail, StaffInfo.class);
            staffInfoList.add(staffInfo);
            return staffInfoList;
        }
        return staffInfoList;
    }

    private List<StaffInfo> convertToStaffInfoList(List<QyWxAddressBookEntity> qyWxAddressBookEntityList) {
        List<DepartmentStaffResult.StaffInfo> staffInfoList = new ArrayList<>();
        for (QyWxAddressBookEntity qyWxAddressBookEntity : qyWxAddressBookEntityList) {
            DepartmentStaffResult.StaffInfo staffInfo = new DepartmentStaffResult.StaffInfo();
            staffInfo.setUserId(qyWxAddressBookEntity.getUserId());
            staffInfo.setName(qyWxAddressBookEntity.getName());
            staffInfo.setDepartment(StringUtils.isNotBlank(qyWxAddressBookEntity.getDepartment()) ? GsonUtil.getGson().fromJson(qyWxAddressBookEntity.getDepartment(), new TypeToken<List<Integer>>() {
            }.getType()) : Lists.newArrayList());
            staffInfo.setOrder(StringUtils.isNotBlank(qyWxAddressBookEntity.getOrder()) ? GsonUtil.getGson().fromJson(qyWxAddressBookEntity.getOrder(), new TypeToken<List<Long>>() {
            }.getType()) : Lists.newArrayList());
            staffInfo.setPosition(qyWxAddressBookEntity.getPosition());
            staffInfo.setMobile(qyWxAddressBookEntity.getMobile());
            staffInfo.setGender(qyWxAddressBookEntity.getGender());
            staffInfo.setAvatar(qyWxAddressBookEntity.getAvatar());
            staffInfo.setStatus(qyWxAddressBookEntity.getStatus());
            staffInfoList.add(staffInfo);
        }
        return staffInfoList;
    }

    public String getUserIdByMobile(String mobile, String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.getUserIdByMobile accessToken is null accessToken:{}", accessToken);
            return null;
        }
        Map<String, String> param = Maps.newHashMap();
        param.put("mobile", mobile);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "user/getuserid?access_token=" + accessToken;
        GetUserIdByMobileResult userIdResult = httpManager.executePostHttp(param, url, new TypeToken<GetUserIdByMobileResult>() {
        });
        if (userIdResult == null || !userIdResult.isSuccess()) {
            log.warn("QywxManager.getUserIdByMobile error accessToken:{}, param:{}, userIdResult:{}", accessToken, param, userIdResult);
            return null;
        }
        return userIdResult.getUserid();
    }


    /**
     * 创建企业微信日程
     */
    public void createQywxSchedule(String accessToken, QywxScheduleContainer qywxScheduleContainer) {
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.createQywxSchedule accessToken is null accessToken:{}", accessToken);
            return;
        }
        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "oa/schedule/add?" + httpManager.transformUrlParams(param);
        BaseResult baseQyWxApiResult = httpManager.executePostHttp(qywxScheduleContainer, url, new TypeToken<BaseResult>() {
        });
        if (baseQyWxApiResult == null || !baseQyWxApiResult.isSuccess()) {
            log.warn("QywxManager.createQywxSchedule error accessToken:{}, qywxScheduleContainer:{}, baseQyWxApiResult:{}", accessToken, qywxScheduleContainer, baseQyWxApiResult);
        }
    }

    public String setContactMeConfig(String accessToken, ContactMeConfigArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_contact_way?access_token=" + accessToken;
        ContactMeConfigResult contactMeConfigResult = httpManager.executePostHttp(arg, url, new TypeToken<ContactMeConfigResult>() {
        });
        if (contactMeConfigResult == null || !contactMeConfigResult.isSuccess() || StringUtils.isBlank(contactMeConfigResult.getConfigId())) {
            log.info(" QywxManager.setContactMeConfig error accessToken:{} arg:{} result:{} ", accessToken, arg, contactMeConfigResult);
            return null;
        }

        return contactMeConfigResult.getConfigId();
    }

    public boolean updateContactMeConfig(String accessToken, UpdateContactMeConfigArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/update_contact_way?access_token=" + accessToken;
        UpdateContactMeConfigResult updateContactMeConfigResult = httpManager.executePostHttp(arg, url, new TypeToken<UpdateContactMeConfigResult>() {
        });
        if (updateContactMeConfigResult == null || !updateContactMeConfigResult.isSuccess()) {
            log.info(" QywxManager.updateContactMeConfig error accessToken:{} arg:{} result:{} ", accessToken, arg, updateContactMeConfigResult);
            return false;
        }

        return true;
    }

    public GetContactMeResult getContanctMe(String accessToken, String configId) {
        GetContactMeArg arg = new GetContactMeArg(configId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_contact_way?access_token=" + accessToken;
        GetContactMeResult getContactMeResult = httpManager.executePostHttp(arg, url, new TypeToken<GetContactMeResult>() {
        });
        if (getContactMeResult == null || !getContactMeResult.isSuccess()) {
            log.info(" QywxManager.setContactMeConfig error accessToken:{} arg:{} result:{} ", accessToken, arg, getContactMeResult);
            return null;
        }

        return getContactMeResult;
    }
    
    public boolean detelteContactMe(String accessToken, String configId) {
        GetContactMeArg arg = new GetContactMeArg(configId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/del_contact_way?access_token=" + accessToken;

        BaseResult delContactMeResult = httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {
        });
        if (delContactMeResult == null || !delContactMeResult.isSuccess()) {
            log.error(" QywxManager.detelteContactMe error accessToken:{} arg:{} result:{} ", accessToken, arg, delContactMeResult);
            return false;
        }

        return true;
    }

    public boolean updateCustomerRemark(String accessToken, QywxEmployeeUpdateArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/remark?access_token=" + accessToken;
        BaseResult result = httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {
        });
        return result.isSuccess();
    }

    public List<String> getFollowUserList(String accessToken) {
        Preconditions.checkArgument(accessToken != null);
        Map<String, String> params = new HashMap<>(1);
        params.put("access_token", accessToken);
        GetFollowUserListResult getFollowUserListResult = httpManager
                .executeGetHttpWithRetry(QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/get_follow_user_list?" + httpManager.transformUrlParams(params), new TypeToken<GetFollowUserListResult>() {
                });
        if (getFollowUserListResult == null || getFollowUserListResult.getErrCode() != 0) {
            log.warn("externalcontact/get_follow_user_list failed, accessToken:{} result:{}", accessToken, getFollowUserListResult);
            throw new IllegalStateException();
        }
        return getFollowUserListResult.getFollowUser() == null ? new ArrayList<>(0) : getFollowUserListResult.getFollowUser();
    }

    //access token过期，重新获取
    public String reGetAccessToken(Integer errCode, QywxCorpAgentConfigEntity entity) {
        if (errCode != 42001) {
            return null;
        }
        if (entity == null) {
            return null;
        }
        return getAccessToken(entity);
    }

    public String reGetAccessToken(Integer errCode, String key) {
        if (errCode != 42001) {
            return null;
        }
        if (StringUtils.isBlank(key)) {
            return null;
        }
        QywxCorpAgentConfigEntity configEntity = agentConfigDAO.queryAgentById(key);
        if (configEntity != null) {
            return reGetAccessToken(errCode, configEntity);
        }
        return null;
    }

    public Optional<String> corpIdToFsEa(String corpId) {
        Result<String> result = qyweixinAccountBindManager.outEaToFsEa(corpId);
        if (result.isSuccess() && !Strings.isNullOrEmpty(result.getData())) {
            return Optional.of(result.getData());
        }
        return Optional.empty();
    }

    public SpreadQywxMiniappMessageResult sendSpreadQywxMiniappMessage(String accessToken, QywxSpreadMiniAppMessageArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + accessToken;
        SpreadQywxMiniappMessageResult spreadQywxMiniappMessageResult = httpManager.executePostHttpWithRetry(arg, url, new TypeToken<SpreadQywxMiniappMessageResult>() {});
        return spreadQywxMiniappMessageResult;
    }

    public SpreadQywxMiniappMessageResult sendAgentMessage(String accessToken, QywxAgentMessageArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + accessToken;
        SpreadQywxMiniappMessageResult spreadQywxMiniappMessageResult = httpManager.executePostHttp(arg, url, new TypeToken<SpreadQywxMiniappMessageResult>() {
        });
        return spreadQywxMiniappMessageResult;
    }

    public SpreadQywxMiniappMessageResult sendQywxApplicationMessage(String accessToken, QywxApplicationMessageArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + accessToken;
        SpreadQywxMiniappMessageResult spreadQywxMiniappMessageResult = httpManager.executePostHttp(arg, url, new TypeToken<SpreadQywxMiniappMessageResult>() {
        });
        return spreadQywxMiniappMessageResult;
    }

    /**
     * 通过外部corpId
     */
    public Optional<String> getFsEaByQyWxCorpId(String corpId, String appId) {
        // 查询配置表
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = getAgentConfigEntityByAppId(corpId, appId);
        if (qywxCorpAgentConfigEntity != null) {
            return Optional.of(qywxCorpAgentConfigEntity.getEa());
        }
        // 多企微的先去绑定完成，不应该走到下面的代码
        Result<String> result = qyweixinAccountBindManager.outEaToFsEa(corpId);
        if (result.isSuccess() && !Strings.isNullOrEmpty(result.getData())) {
            return Optional.of(result.getData());
        }
        return Optional.empty();

    }

    /**
     * 通过外部corpId
     */
    public Optional<String> getFsEaByQyWxCorpId2(String corpId, String suiteId) {
        // 查询配置表
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = getAgentConfigEntityBySuiteId(corpId, suiteId);
        if (qywxCorpAgentConfigEntity != null) {
            return Optional.of(qywxCorpAgentConfigEntity.getEa());
        }
        // 多企微的先去绑定完成，不应该走到下面的代码
        Result<String> result = qyweixinAccountBindManager.outEaToFsEa(corpId);
        if (result.isSuccess() && !Strings.isNullOrEmpty(result.getData())) {
            return Optional.of(result.getData());
        }
        return Optional.empty();

    }

    /**
     * 根据suiteId查询QywxCorpAgentConfigEntity，如果是企微多租户的情况，一定要传suiteId
     * @param corpId
     * @param suiteId
     * @return
     */
    public QywxCorpAgentConfigEntity getAgentConfigEntityBySuiteId(String corpId, String suiteId){
        List<QywxCorpAgentConfigEntity> qywxCorpAgentConfigEntities = agentConfigDAO.listAgentByCorpId(corpId);
        if (qywxCorpAgentConfigEntities.size() == 1) {
            // 企微绑定了单租户
            return qywxCorpAgentConfigEntities.get(0);
        } else if (qywxCorpAgentConfigEntities.size() > 1) {
            // 企微绑定了多租户，根据suiteId找到ea
            if (StringUtils.isNotBlank(suiteId)) {
                QywxCustomerAppInfoEntity qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectOne(suiteId, corpId);
                if (qywxCustomerAppInfoEntity != null) {
                    return agentConfigDAO.queryAgentByEa(qywxCustomerAppInfoEntity.getEa());
                }
                return null;
            }
        }
        return null;
    }

    /**
     * 根据appId查询QywxCorpAgentConfigEntity，如果是企微多租户的情况，一定要传不同的appId
     * @param corpId
     * @param appId
     * @return
     */
    public QywxCorpAgentConfigEntity getAgentConfigEntityByAppId(String corpId, String appId){
        List<QywxCorpAgentConfigEntity> qywxCorpAgentConfigEntities = agentConfigDAO.listAgentByCorpId(corpId);
        if (qywxCorpAgentConfigEntities.size() == 1) {
            // 企微绑定了单租户
            return qywxCorpAgentConfigEntities.get(0);
        } else if (qywxCorpAgentConfigEntities.size() > 1) {
            // 企微绑定了多租户，根据appId找到ea
            if (StringUtils.isNotBlank(appId)) {
                List<QywxMiniappConfigEntity> miniappConfigEntities = qywxMiniappConfigDAO.getByCorpidAndAppId(corpId, appId);
                if (CollectionUtils.isNotEmpty(miniappConfigEntities)) {
                    return agentConfigDAO.queryAgentByEa(miniappConfigEntities.get(0).getEa());
                }
            }
        }
        return null;
    }

    //判断ea是否已经绑定Fs
    public Optional<String> getEaByQyWxMapping(String corpId) {
        // 查询映射表
        QywxCorpEaMappingEntity mappingEntity = qywxCorpEaMappingDAO.queryMappingByCorpId(corpId);
        if (mappingEntity != null) {
            return Optional.of(mappingEntity.getEa());
        }

        return Optional.empty();
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    public GetCorpTagListResult getCorpTagList(String ea, GetCorpTagListArg arg) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = getAccessToken(ea);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_corp_tag_list?access_token=" + accessToken;
        return httpManager.executePostHttp(arg, url, new TypeToken<GetCorpTagListResult>() {
        });
    }

    public AddCorpTagResult addCorpTag(String ea, AddCorpTagArg arg) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null || agentConfig.getCustomerContactSecret() == null) {
            throw new IllegalStateException();
        }
        String accessToken = getAccessToken(agentConfig.getCorpid(), "CUSTOMER_CONTACT_SECRET",
                agentConfig.getCustomerContactSecret());
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_corp_tag?access_token=" + accessToken;
        return httpManager.executePostHttp(arg, url, new TypeToken<AddCorpTagResult>() {
        });
    }

    public boolean editCorpTag(String ea, EditCorpTagArg arg) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null || agentConfig.getCustomerContactSecret() == null) {
            throw new IllegalStateException();
        }
        String accessToken = getAccessToken(agentConfig.getCorpid(), "CUSTOMER_CONTACT_SECRET",
                agentConfig.getCustomerContactSecret());
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/edit_corp_tag?access_token=" + accessToken;
        BaseResult baseResult = httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {
        });
        return baseResult.isSuccess();
    }

    public boolean deleteCorpTag(String ea, DeleteCorpTagArg arg) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null || agentConfig.getCustomerContactSecret() == null) {
            throw new IllegalStateException();
        }
        String accessToken = getAccessToken(agentConfig.getCorpid(), "CUSTOMER_CONTACT_SECRET",
                agentConfig.getSecret());
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/del_corp_tag?access_token=" + accessToken;
        BaseResult baseResult = httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {
        });
        return baseResult.isSuccess();
    }

    public boolean markTag(String ea, String externalUserId, List<String> toAddTagIds, List<String> toDeleteTagIds) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = getAccessToken(ea);
        List<GetExternalContactDetailResult> results = this.getExternalContactDetail(ea, accessToken, ImmutableList.of(externalUserId));
        if (results != null && !results.isEmpty() && results.get(0).getFollowUserList() != null) {
            for (GetExternalContactDetailResult.FollowUser followUser : results.get(0).getFollowUserList()) {
                String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/mark_tag?access_token=" + accessToken;
                Map<String, Object> arg = new HashMap<>(4);
                arg.put("userid", followUser.getUserId());
                arg.put("external_userid", externalUserId);
                arg.put("add_tag", toAddTagIds);
                arg.put("remove_tag", toDeleteTagIds);
                httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {
                });
            }
        }
        return true;
    }

    public boolean sendWelcomeMessage(String ea, SendCustomerWelcomeMessageArg arg) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = getAccessToken(ea);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/send_welcome_msg?access_token=" + accessToken;
        SendCustomerWelcomeMessageResult result = httpManager.executePostHttp(arg, url, new TypeToken<SendCustomerWelcomeMessageResult>() {
        });
        if (result == null) {
            return false;
        }

        return result.isSuccess();
    }

    public boolean sendNewWelcomeMessage(String ea, SendWelcomeMessageNewArg arg){
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = getAccessToken(ea);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/send_welcome_msg?access_token="+accessToken;
        SendCustomerWelcomeMessageResult result =  httpManager.executePostHttp(arg, url, new TypeToken<SendCustomerWelcomeMessageResult>() {});
        if (result == null){
            return false;
        }

        return result.isSuccess();
    }

    /**
     * 查询当前企业绑定状况
     *
     * @param ea
     * @return
     */
    public FsEnterpriseBindTypeEnum fsEnterpriseBindType(String ea) {
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        if (WxAppInfoEnum.isMankeep(appId)) {
            //如果企业还是绑定的客脉，切换到客脉pro
            eaWechatAccountBindDao.updateAppIdByPlatform(ea,  WxAppInfoEnum.MankeepPro.getAppId(), "YXT");
        }
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, appId);
        if (qywxMiniappConfigEntity == null) {
            return FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO;
        } else {
            return FsEnterpriseBindTypeEnum.BIND_QYWX_AND_MINIAPPPRO;
        }
    }

    public Optional<String> getExternalUsrIdByUnionId(String accessToken, String unionId, String openId) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/idconvert/unionid_to_external_userid?access_token=" + accessToken;
        Map<String, Object> arg = new HashMap<>(3);
        arg.put("unionid", unionId);
        arg.put("openid", openId);
        GetExternalUserIdByUnionIdResult getExternalUserIdResult = httpManager.executePostHttp(arg, url, new TypeToken<GetExternalUserIdByUnionIdResult>() {});
        if (!getExternalUserIdResult.isSuccess()) {
            return Optional.empty();
        }

        return Optional.of(getExternalUserIdResult.getExternalUserId());
    }

    public Result<GetPhoneNumberByCodeResult> queryPhoneNumberByCode(String appid, String code) {
        String token = wechatAccountManager.getAccessTokenByWxAppId(appid);
        String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + token;
        HashMap<String, Object> map = new HashMap<>();
        map.put("code", code);
        GetPhoneNumberByCodeResult result = httpManager.executePostHttp(map, url, new TypeToken<GetPhoneNumberByCodeResult>() {
        });
        if (result == null) {
            return Result.newSuccess();
        }
        return Result.newSuccess(result);
    }


    /**过滤未激活的企业微信用户*/
    public List<String> fliterUnActiveUser(String ea,List<String> userIds) {
        List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryEaAndUserId(ea, userIds);
        if (CollectionUtils.isNotEmpty(qyWxAddressBookEntities)) {
            return qyWxAddressBookEntities.stream().filter(r -> r.getStatus() == 1).map(QyWxAddressBookEntity::getUserId).collect(Collectors.toList());
        }
        return userIds;
    }

    /**
     * 获取服务商Token
     * @param corpId 服务商的corpid
     * @param secret 服务商的secret，在服务商管理后台可见
     * @return
     */
    public String getProviderAccessToken(String corpId, String secret) {
        if (Strings.isNullOrEmpty(corpId) || Strings.isNullOrEmpty(secret)) {
            return null;
        }

        String accessToken = redisManager.getQywxProviderAccessToken(corpId);
        if (Strings.isNullOrEmpty(accessToken)) {
            String url = QYWXApiConstants.QYWX_GET_PROVIDER_TOKEN_URL;
            Map<String, String> params = new HashMap<>();
            params.put("corpid", corpId);
            params.put("provider_secret", secret);
            QywxApiProviderAccessTokenResult tokenResult = httpManager.executePostHttp(params, url, new TypeToken<QywxApiProviderAccessTokenResult>() {});
            log.info("QywxManager -> getProviderAccessToken arg:{}, result:{}", params, tokenResult);
            if (Objects.isNull(tokenResult.getErrcode()) || tokenResult.getErrcode() == 0) {
                accessToken = tokenResult.getAccessToken();
                redisManager.setQywxProviderAccessToken(corpId, accessToken, tokenResult.getExpiresTime());
                log.info("QywxManager -> getProviderAccessToken from qywx api, token:{}", accessToken);
            }
        } else {
            log.info("QywxManager -> getProviderAccessToken from redis cache, token:{}", accessToken);
        }

        return accessToken;
    }

    /**
     * 判断当前环境是否为VPN断网云
     * @return
     */
    public boolean isVpnDisconnectCloud() {
        return appVersionManager.isHsykCloud();
    }

    /**
     * 生成带参授权链接
     * @param ea
     * @return
     */
    public String geneCustomerAuthUrl(String ea) {
        if (Strings.isNullOrEmpty(ea)) {
            return null;
        }
        String qrcodeUrl = redisManager.getQywxCustomerAuthUrl(ea);
        if (Strings.isNullOrEmpty(qrcodeUrl)) {
            String providerAccessToken = getProviderAccessToken(QYWXApiConstants.QYWX_PROVIDER_CORPID, QYWXApiConstants.QYWX_PROVIDER_SECRET);
            if (StringUtils.isBlank(providerAccessToken)) {
                return null;
            }

            List<String> templateIds = Lists.newArrayList();
            if (host.contains("ceshi112")) {
                templateIds.add(QywxSuiteEnum.CESHI112_ASSISTANT.getSuiteId());
                templateIds.add(QywxSuiteEnum.CESHI112_MINIAPP.getSuiteId());
            } else {
                templateIds.add(QywxSuiteEnum.ONLINE_ASSISTANT.getSuiteId());
                templateIds.add(QywxSuiteEnum.ONLINE_MINIAPP.getSuiteId());
            }

            String url = QYWXApiConstants.GET_CUSTOMER_AUTH_URL + providerAccessToken;
            Map<String, Object> params = new HashMap<>();
            params.put("state", eieaConverter.enterpriseAccountToId(ea) + "");
            params.put("templateid_list", templateIds);
            QywxApiCustomerAuthUrlResult customerAuthUrlResult = httpManager.executePostHttp(params, url, new TypeToken<QywxApiCustomerAuthUrlResult>() {});
            log.info("QywxManager -> geneCustomerAuthUrl arg:{}, result:{}", params, customerAuthUrlResult);
            if (Objects.isNull(customerAuthUrlResult.getErrcode()) || customerAuthUrlResult.getErrcode() == 0) {
                qrcodeUrl = customerAuthUrlResult.getQrcodeUrl();
                redisManager.setQywxCustomerAuthUrl(ea, qrcodeUrl, customerAuthUrlResult.getExpiresTime());
                //本地更新
                agentConfigDAO.updateCustomerAuthUrlByEa(qrcodeUrl,ea);
                log.info("QywxManager -> geneCustomerAuthUrl from qywx api, qrcodeUrl:{}", qrcodeUrl);
            }
        }else {
            log.info("QywxManager -> geneCustomerAuthUrl from redis cache, qrcodeUrl:{}", qrcodeUrl);
        }
        return qrcodeUrl;
    }

    public Result<CustomerAppInfoResult> getCorpIdAndSuitId(String ea) {
        QywxCustomerAppInfoEntity customerAppInfoEntity = getCustomerAppInfoEntityByEa(ea);
        if (customerAppInfoEntity == null) {
            return Result.newSuccess();
        }
        CustomerAppInfoResult customerAppInfoResult = BeanUtil.copy(customerAppInfoEntity,CustomerAppInfoResult.class);
        return Result.newSuccess(customerAppInfoResult);
    }

    public Result<CustomerAppInfoResult> queryAgentIdByCorpIdAndSuitId(String corpId, String suitId) {
        QywxCustomerAppInfoEntity qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectOne(suitId, corpId);
        if (qywxCustomerAppInfoEntity == null) {
            return Result.newSuccess();
        }
        CustomerAppInfoResult customerAppInfoResult = BeanUtil.copy(qywxCustomerAppInfoEntity,CustomerAppInfoResult.class);
        return Result.newSuccess(customerAppInfoResult);
    }

    public void dealExternalUserIdByUnionId(String uid,String openId, String wxUnionId, String finalEa) {
        String accessToken = this.getAccessToken(finalEa);
        if (StringUtils.isBlank(accessToken)) {
            return;
        }
        if (StringUtils.isBlank(openId) || StringUtils.isBlank(wxUnionId)) {
            return;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/idconvert/unionid_to_external_userid?access_token=" + accessToken;
        GetExternalUserIdArg arg = new GetExternalUserIdArg();
        arg.setOpenId(openId);
        arg.setUnionId(wxUnionId);
        GetExternalUserIdByUnionIdResult getExternalUserIdByUnionIdResult = httpManager.executePostHttp(arg,url,new TypeToken<GetExternalUserIdByUnionIdResult>(){});
        log.info("dealExternalUserIdByUnionId result:{}", GsonUtil.getGson().toJson(getExternalUserIdByUnionIdResult));
        if (getExternalUserIdByUnionIdResult != null && getExternalUserIdByUnionIdResult.getErrcode() == 0) {
            //1.如果能获取到externalUserId
            if (StringUtils.isNotBlank(getExternalUserIdByUnionIdResult.getExternalUserId())){
                //1.更新企业微信客户对象unionId
                wechatWorkExternalUserObjManager.updateUnionIdExternalUserObj(finalEa,getExternalUserIdByUnionIdResult.getExternalUserId(),wxUnionId);
                //2.关联企微客户/公众号营销用户
                AssociationArg associateArg = new AssociationArg();
                associateArg.setUnionId(wxUnionId);
                associateArg.setType(ChannelEnum.MINIAPP.getType());
                associateArg.setEa(finalEa);
                associateArg.setAssociationId(uid);
                associateArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
                associateArg.setTriggerAction("pLogin");
                userMarketingAccountAssociationManager.associate(associateArg);
            } else if (StringUtils.isNotBlank(getExternalUserIdByUnionIdResult.getPendingId())) {
                //1.先查询是否已经存在数据
                WxPendingRelationEntity wxPendingRelationEntity = wxPendingRelationDAO.selectByEaAndUnionId(finalEa,wxUnionId);
                if (wxPendingRelationEntity != null) {
                    //比较当前时间和关联数据的创建时间是否大于90天
                    if (DateUtil.getTimeDifference(new Date(),wxPendingRelationEntity.getUpdateTime(), TimeTypeEnum.DAY.getType()) > 90) {
                        //重新更新pending
                        wxPendingRelationDAO.updatePending(wxPendingRelationEntity.getId(),getExternalUserIdByUnionIdResult.getPendingId());
                    }
                } else {
                    //2.写入关联表,等添加企微客户时,进行关联
                    WxPendingRelationEntity pendingEntity = new WxPendingRelationEntity();
                    pendingEntity.setId(UUIDUtil.getUUID());
                    pendingEntity.setEa(finalEa);
                    pendingEntity.setUid(uid);
                    pendingEntity.setUnionId(wxUnionId);
                    pendingEntity.setPendingId(getExternalUserIdByUnionIdResult.getPendingId());
                    wxPendingRelationDAO.insert(pendingEntity);
                }

            }
        }
    }

    public String getWxUnionIdByExternalUserId(String ea, String externalUserId) {
        String accessToken = this.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            return null;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/idconvert/batch/external_userid_to_pending_id?access_token="+ accessToken;
        GetPendingArg arg = new GetPendingArg();
        arg.setExternalUserId(ImmutableList.of(externalUserId));
        GetPendingResult getPendingResult = httpManager.executePostHttp(arg,url,new TypeToken<GetPendingResult>(){});
        log.info("getWxUnionIdByExternalUserId result:{}", GsonUtil.getGson().toJson(getPendingResult));
        if (getPendingResult != null && getPendingResult.getErrcode() == 0) {
            List<GetPendingResult.PendingResult> result = getPendingResult.getResult();
            if (CollectionUtils.isNotEmpty(result)) {
                GetPendingResult.PendingResult pendingResult = result.get(0);
                WxPendingRelationEntity wxPendingRelationEntity = wxPendingRelationDAO.selectByEaAndPendingId(ea, pendingResult.getPendingId());
                if (wxPendingRelationEntity != null) {
                    return wxPendingRelationEntity.getUnionId();
                }
            }
        }
        return null;
    }

    public void dealCrmWxUserUnionId(String openId, String ea, String wxUnionId) {
        String accessToken = this.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            return;
        }
        if (StringUtils.isBlank(openId) || StringUtils.isBlank(wxUnionId)) {
            return;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/idconvert/unionid_to_external_userid?access_token=" + accessToken;
        GetExternalUserIdArg arg = new GetExternalUserIdArg();
        arg.setOpenId(openId);
        arg.setUnionId(wxUnionId);
        GetExternalUserIdByUnionIdResult getExternalUserIdByUnionIdResult = httpManager.executePostHttp(arg,url,new TypeToken<GetExternalUserIdByUnionIdResult>(){});
        log.info("dealCrmWxUserUnionId result:{}", GsonUtil.getGson().toJson(getExternalUserIdByUnionIdResult));
        if (getExternalUserIdByUnionIdResult != null && getExternalUserIdByUnionIdResult.getErrcode() == 0) {
            //1.如果能获取到externalUserId
            if (StringUtils.isNotBlank(getExternalUserIdByUnionIdResult.getExternalUserId())){
                //1.更新企业微信客户对象unionId
                wechatWorkExternalUserObjManager.updateUnionIdExternalUserObj(ea,getExternalUserIdByUnionIdResult.getExternalUserId(),wxUnionId);
            } else if (StringUtils.isNotBlank(getExternalUserIdByUnionIdResult.getPendingId())) {
                //1.先查询是否已经存在数据
                WxPendingRelationEntity wxPendingRelationEntity = wxPendingRelationDAO.selectByEaAndUnionId(ea,wxUnionId);
                if (wxPendingRelationEntity != null) {
                    //比较当前时间和关联数据的创建时间是否大于90天
                    if (DateUtil.getTimeDifference(new Date(),wxPendingRelationEntity.getUpdateTime(), TimeTypeEnum.DAY.getType()) > 90) {
                        //重新更新pending
                        wxPendingRelationDAO.updatePending(wxPendingRelationEntity.getId(),getExternalUserIdByUnionIdResult.getPendingId());
                    }
                } else {
                    //2.写入关联表,等添加企微客户时,进行关联
                    WxPendingRelationEntity pendingEntity = new WxPendingRelationEntity();
                    pendingEntity.setId(UUIDUtil.getUUID());
                    pendingEntity.setEa(ea);
                    pendingEntity.setUnionId(wxUnionId);
                    pendingEntity.setPendingId(getExternalUserIdByUnionIdResult.getPendingId());
                    wxPendingRelationDAO.insert(pendingEntity);
                }

            }
        }
    }

    public String setQywxGroupContactMeConfig(String accessToken, QywxGroupContactMeConfigArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/add_join_way?access_token=" + accessToken;
        ContactMeConfigResult contactMeConfigResult = httpManager.executePostHttp(arg, url, new TypeToken<ContactMeConfigResult>() {
        });
        if (contactMeConfigResult == null || !contactMeConfigResult.isSuccess() || StringUtils.isBlank(contactMeConfigResult.getConfigId())) {
            log.info(" QywxManager.setQywxGroupContactMeConfig error accessToken:{} arg:{} result:{} ", accessToken, arg, contactMeConfigResult);
            return null;
        }

        return contactMeConfigResult.getConfigId();
    }

    public boolean updateQywxGroupContactMeConfig(String accessToken, UpdateQywxGroupContactMeConfigArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/update_join_way?access_token=" + accessToken;
        UpdateContactMeConfigResult updateContactMeConfigResult = httpManager.executePostHttp(arg, url, new TypeToken<UpdateContactMeConfigResult>() {
        });
        if (updateContactMeConfigResult == null || !updateContactMeConfigResult.isSuccess()) {
            log.info(" QywxManager.updateQywxGroupContactMeConfig error accessToken:{} arg:{} result:{} ", accessToken, arg, updateContactMeConfigResult);
            return false;
        }

        return true;
    }

    public GetQywxGroupContactMeResult getQywxGroupContactMe(String accessToken, String configId) {
        GetContactMeArg arg = new GetContactMeArg(configId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get_join_way?access_token=" + accessToken;
        GetQywxGroupContactMeResult getQywxGroupContactMeResult = httpManager.executePostHttp(arg, url, new TypeToken<GetQywxGroupContactMeResult>() {
        });
        if (getQywxGroupContactMeResult == null || !getQywxGroupContactMeResult.isSuccess()) {
            log.info(" QywxManager.getQywxGroupContanctMe error accessToken:{} arg:{} result:{} ", accessToken, arg, getQywxGroupContactMeResult);
            return null;
        }

        return getQywxGroupContactMeResult;
    }

    public boolean deleteQywxGroupContactMe(String accessToken, String configId) {
        GetContactMeArg arg = new GetContactMeArg(configId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/del_join_way?access_token=" + accessToken;

        BaseResult delContactMeResult = httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {
        });
        if (delContactMeResult == null || !delContactMeResult.isSuccess()) {
            log.error(" QywxManager.deleteQywxGroupContactMe error accessToken:{} arg:{} result:{} ", accessToken, arg, delContactMeResult);
            return false;
        }

        return true;
    }

    /**
     * 根据企业微信员工id批量获取客户跟进信息
     *
     * @param accessToken
     * @param userIds
     * @return
     */
    public List<BatchGetQywxCustomerResult.SingleFollowInfoDetail> batchGetQywxCustomerFllowInfo(String accessToken, List<String> userIds) {
        if (StringUtils.isEmpty(accessToken) || CollectionUtils.isEmpty(userIds)) {
            log.warn("QywxManager.batchGetQywxCustomerFllowInfo accessToken or userIds is null accessToken:{}, userId:{}", accessToken, userIds);
            return null;
        }
        List<BatchGetQywxCustomerResult.SingleFollowInfoDetail> followInfoDetails = Lists.newArrayList();
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/batch/get_by_user?access_token=" + accessToken;
        BatchGetQywxCustomerArg arg = new BatchGetQywxCustomerArg();
        arg.setLimit(100);
        arg.setUserIds(userIds);
        BatchGetQywxCustomerResult externalContactListResult = httpManager.executePostHttp(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
        });
        if (!externalContactListResult.isSuccess()) {
            log.info("QywxManager.batchGetQywxCustomerFllowInfo error externalcontactListResult:{}", externalContactListResult);
            return null;
        }
        if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
            return null;
        }

        for (BatchGetQywxCustomerResult.ExternalContact externalConcatObj : externalContactListResult.getExternalContactDetailList()) {
            followInfoDetails.add(externalConcatObj.getFollowInfoDetail());
        }
        //数据未取完
        while (StringUtils.isNotEmpty(externalContactListResult.getNextCursor())) {
            arg.setCursor(externalContactListResult.getNextCursor());
            externalContactListResult = httpManager.executePostHttp(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
            });
            if (!externalContactListResult.isSuccess()) {
                log.info("QywxManager.batchGetQywxCustomerFllowInfo error externalcontactListResult:{}", externalContactListResult);
                break;
            }

            if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
                break;
            }
            for (BatchGetQywxCustomerResult.ExternalContact externalConcatObj : externalContactListResult.getExternalContactDetailList()) {
                followInfoDetails.add(externalConcatObj.getFollowInfoDetail());
            }
        }

        return followInfoDetails;
    }

    public List<BatchGetQywxCustomerResult.ExternalContact> batchGetQywxCustomerInfo(String accessToken, List<String> userIds) {
        if (StringUtils.isEmpty(accessToken) || CollectionUtils.isEmpty(userIds)) {
            log.warn("QywxManager.batchGetQywxCustomerFllowInfo accessToken or userIds is null accessToken:{}, userId:{}", accessToken, userIds);
            return null;
        }
        List<BatchGetQywxCustomerResult.ExternalContact> customerResults = Lists.newArrayList();
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/batch/get_by_user?access_token=" + accessToken;
        List<List<String>> partition = Lists.partition(userIds, 100);
        for (List<String> userParts : partition) {
            BatchGetQywxCustomerArg arg = new BatchGetQywxCustomerArg();
            arg.setLimit(100);
            arg.setUserIds(userParts);
            BatchGetQywxCustomerResult externalContactListResult = httpManager.executePostHttp(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
            });
            if (!externalContactListResult.isSuccess()) {
                log.info("QywxManager.batchGetQywxCustomerFllowInfo error externalcontactListResult:{}", externalContactListResult);
                return null;
            }
            if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
                return null;
            }
            customerResults.addAll(externalContactListResult.getExternalContactDetailList());
            //数据未取完
            while (StringUtils.isNotEmpty(externalContactListResult.getNextCursor())) {
                arg.setCursor(externalContactListResult.getNextCursor());
                externalContactListResult = httpManager.executePostHttp(arg, url, new TypeToken<BatchGetQywxCustomerResult>() {
                });
                if (!externalContactListResult.isSuccess()) {
                    log.info("QywxManager.batchGetQywxCustomerFllowInfo error externalcontactListResult:{}", externalContactListResult);
                    break;
                }

                if (CollectionUtils.isEmpty(externalContactListResult.getExternalContactDetailList())) {
                    break;
                }
                customerResults.addAll(externalContactListResult.getExternalContactDetailList());
            }
        }
        return customerResults;
    }

    public List<EmployeeTagResult> queryEmployeeInfo(String accessToken) {
        List<EmployeeTagResult> tagResults = Lists.newArrayList();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/tag/list?access_token="+accessToken;
        GetEmployeeTagResult getEmployeeTagResult = httpManager.executeGetHttp(url, new TypeToken<GetEmployeeTagResult>() {
        });
        if (getEmployeeTagResult == null || !getEmployeeTagResult.isSuccess()) {
            log.info("QywxManager.queryEmployeeInfo error getEmployeeTagResult:{}", getEmployeeTagResult);
            return tagResults;
        }
        if (CollectionUtils.isEmpty(getEmployeeTagResult.getTagList())) {
            return tagResults;
        }
        return getEmployeeTagResult.getTagList();
    }

    /**
     * 根据企微员工标签批量获取企微员工userId
     * @param tagIds
     * @return
     */
    public List<String> batchGetEmployeeByTags(String ea, List<Integer> tagIds) {
        List<String> userIds = Lists.newArrayList();
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.info("batchGetEmployeeByTags failed agentConfig=null ea:{}", ea);
            return userIds;
        }
        String accessToken = this.getAccessToken(ea);
        List<Integer> departmentIds = Lists.newArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(tagIds.size());
        for (Integer tagId : tagIds) {
            executorService.submit(() -> {
                try {
                    String url = "https://qyapi.weixin.qq.com/cgi-bin/tag/get?access_token=" + accessToken + "&tagid=" + tagId;
                    GetEmployeeUserResult getEmployeeUserResult = httpManager.executeGetHttpWithRetry(url, new TypeToken<GetEmployeeUserResult>() {
                    });
                    if (getEmployeeUserResult != null && getEmployeeUserResult.isSuccess()) {
                        if (CollectionUtils.isNotEmpty(getEmployeeUserResult.getUserList())) {
                            userIds.addAll(getEmployeeUserResult.getUserList().stream().map(GetEmployeeUserResult.GetTagUser::getUserId).collect(Collectors.toSet()));
                        }
                        if (CollectionUtils.isNotEmpty(getEmployeeUserResult.getPartyList())) {
                            departmentIds.addAll(getEmployeeUserResult.getPartyList());
                        }
                    }
                } catch (Exception e) {
                    log.warn("QywxManager.getStaffByDepartmentId getStaffByDepartmentId error e:{}", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.warn("QywxManager.batchGetEmployeeByTags await error e:{}", e);
        }
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            userIds.addAll(qywxUserManager.getQywxUserIdByQywxDepartment(ea, departmentIds));
        }
        return userIds;
    }

    /**
     * 处理企业微信员工选择器,员工id
     * @param userIdList
     * @param departmentIds
     * @param tagIds
     * @return
     */
    public List<String> handleQywxEmployeeUserId(String ea,List<String> userIdList,List<Integer> departmentIds,List<Integer> tagIds) {
        List<String> userIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(userIdList) && userIdList.size() == 1 && "-999999".equals(userIdList.get(0))) {
            //查询所有员工
            userIds = this.queryAllStaffId(ea);
        } else {
            //指定员工
            Set<String> qywxUserIdsSet = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(userIdList)){
                qywxUserIdsSet.addAll(userIdList);
            }
            //指定员工标签
            if (CollectionUtils.isNotEmpty(tagIds)) {
                List<String> employeeIds = this.batchGetEmployeeByTags(ea, tagIds);
                qywxUserIdsSet.addAll(employeeIds);
            }
            //指定员工部门
            if (CollectionUtils.isNotEmpty(departmentIds)) {
                List<Integer> qywxCircleIds = new ArrayList<>();
                for (int i = 0; i < departmentIds.size(); i++) {
                    qywxCircleIds.add(i, departmentIds.get(i) - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
                }
                List<String> qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartmentFormDB(ea, qywxCircleIds);
                qywxUserIdsSet.addAll(qywxUserIds);
            }
            userIds.addAll(qywxUserIdsSet);
        }
        return userIds;
    }

    /**
     * 根据纷享通讯录获取当前账号有权限的企微员工ID,仅用于开启数据隔离企业
     *
     * @param ea
     * @param fsUserId
     * @param fsUserIds
     * @param fsDepartmentIds
     * @return
     */
    public List<String> handleQywxEmployeeUserIdWithOpenDataPermission(String ea, Integer fsUserId, List<String> fsUserIds, List<Integer> fsDepartmentIds) {
        List<String> qywxUserIdsList = Lists.newArrayList();
        List<Integer> fsUserIdList = Lists.newArrayList();
        fsUserIdList.addAll(handleEmployeeUserIdWithOpenDataPermission(ea, fsUserId, fsUserIds, fsDepartmentIds));
        if (CollectionUtils.isNotEmpty(fsUserIdList)) {
            List<List<Integer>> partition = Lists.partition(fsUserIdList, 1000);
            for (List<Integer> part : partition) {
                List<String> qywxUserIds = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfos(ea, part);
                if (CollectionUtils.isNotEmpty(qywxUserIds)) {
                    qywxUserIdsList.addAll(qywxUserIds);
                }
            }
        }
        return qywxUserIdsList;
    }

    public List<Integer> handleEmployeeUserIdWithOpenDataPermission(String ea, Integer fsUserId, List<String> fsUserIds, List<Integer> fsDepartmentIds) {
        return this.handleEmployeeUserIdWithOpenDataPermission(ea, fsUserId, fsUserIds, fsDepartmentIds, null, null,true);
    }

    /** isQywx 判断是否是朋友圈,群发调用过来的,需要额外判断另外的配置*/
    public List<Integer> handleEmployeeUserIdWithOpenDataPermission(String ea, Integer fsUserId, List<String> fsUserIds, List<Integer> fsDepartmentIds, List<String> roleCodes, List<String> userGroupIds,Boolean isQywx) {
        List<Integer> fsUserIdList = Lists.newArrayList();
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        boolean isQywxContactSetting = fsBindManager.isQywxContactFsSetting(ea);
        List<Integer> currentAccountAccessibleFsUserIds = Lists.newArrayList();
        if (isOpen) {
            //currentAccountAccessibleFsUserIds = this.getCurrentAccountAccessibleFsUserIds(ea, fsUserId);
            GetAllEmployeeOptionArg arg = new GetAllEmployeeOptionArg();
            arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            GetAllEmployeeOptionResult allEmployeeOption = employeeProviderService.getAllEmployeeOption(arg);
            if (allEmployeeOption != null && CollectionUtils.isNotEmpty(allEmployeeOption.getEmployeeOptions())) {
                currentAccountAccessibleFsUserIds = allEmployeeOption.getEmployeeOptions().stream().filter(e -> Objects.equals(e.getStatus(),EmployeeEntityStatus.NORMAL.getValue())).map(EmployeeOption::getId).collect(Collectors.toList());
            }
            fsUserIdList = getFilterFsUserIds(ea, fsUserIds, fsDepartmentIds, roleCodes, userGroupIds, fsUserIdList, currentAccountAccessibleFsUserIds);
        } else {
            if (isQywx && isQywxContactSetting) {

                GetAllEmployeeOptionArg arg = new GetAllEmployeeOptionArg();
                arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
                GetAllEmployeeOptionResult allEmployeeOption = employeeProviderService.getAllEmployeeOption(arg);
                if (allEmployeeOption != null && CollectionUtils.isNotEmpty(allEmployeeOption.getEmployeeOptions())) {
                    currentAccountAccessibleFsUserIds = allEmployeeOption.getEmployeeOptions().stream().filter(e -> Objects.equals(e.getStatus(),EmployeeEntityStatus.NORMAL.getValue())).map(EmployeeOption::getId).collect(Collectors.toList());
                }
                fsUserIdList = getFilterFsUserIds(ea, fsUserIds, fsDepartmentIds, roleCodes, userGroupIds, fsUserIdList, currentAccountAccessibleFsUserIds);
            }
        }
        return fsUserIdList;
    }

    private List<Integer> getFilterFsUserIds(String ea, List<String> fsUserIds, List<Integer> fsDepartmentIds, List<String> roleCodes, List<String> userGroupIds, List<Integer> fsUserIdList, List<Integer> currentAccountAccessibleFsUserIds) {
        if (CollectionUtils.isNotEmpty(fsUserIds) && fsUserIds.size() == 1 && "-999999".equals(fsUserIds.get(0))) {
            //查询所有员工
            fsUserIdList.addAll(currentAccountAccessibleFsUserIds);
        } else if (CollectionUtils.isNotEmpty(fsDepartmentIds) && fsDepartmentIds.contains(Constant.WHOLE_COMPANY_ID)) {
            fsUserIdList.addAll(currentAccountAccessibleFsUserIds);
        } else {
            //指定员工
            if (CollectionUtils.isNotEmpty(fsUserIds)) {
                fsUserIdList.addAll(fsUserIds.stream().filter(StringUtils::isNumeric).map(Integer::valueOf).collect(Collectors.toList()));
            }
            //指定员工部门
            if (CollectionUtils.isNotEmpty(fsDepartmentIds)) {
                BatchGetEmployeeIdMapByDepartmentId.Arg arg = new BatchGetEmployeeIdMapByDepartmentId.Arg();
                arg.setDepartmentIds(fsDepartmentIds);
                arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
                arg.setRunStatus(RunStatus.ACTIVE);
                arg.setIncludeLowDepartment(true);
                BatchGetEmployeeIdMapByDepartmentId.Result employeeIdResult = employeeProviderService.batchGetEmployeeIdMapByDepartmentId(arg);
                if (employeeIdResult != null && employeeIdResult.getEmployeeMap() != null) {
                    for (Map.Entry<Integer, List<Integer>> entry : employeeIdResult.getEmployeeMap().entrySet()) {
                        if (CollectionUtils.isNotEmpty(entry.getValue())) {
                            fsUserIdList.addAll(entry.getValue());
                        }
                    }
                }
            }
            // 指定角色
            if (CollectionUtils.isNotEmpty(roleCodes)) {
                List<Integer> employeeIds = userRoleManager.getEmployeeIdsByRoles(ea, roleCodes);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(employeeIds)) {
                    fsUserIdList.addAll(employeeIds);
                }
            }
            // 指定用户组
            if (CollectionUtils.isNotEmpty(userGroupIds)) {
                List<Integer> userIdsByGroups = crmPaasOrgDataManager.queryUserIdsByUserGroups(ea, userGroupIds);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userIdsByGroups)) {
                    fsUserIdList.addAll(userIdsByGroups);
                }
            }
            fsUserIdList = Lists.newArrayList(CollectionUtils.intersection(fsUserIdList, currentAccountAccessibleFsUserIds));
        }
        return fsUserIdList;
    }

    public List<Integer> getCurrentAccountAccessibleFsUserIds(String ea, Integer fsUserId) {
        List<Integer> accessibleDepartmentsFsUserIds = Lists.newArrayList();
        List<Integer> accessibleDepartments = dataPermissionManager.getDataPermission(ea, fsUserId);
        if (CollectionUtils.isNotEmpty(accessibleDepartments)) {
            BatchGetEmployeeIdMapByDepartmentId.Arg arg = new BatchGetEmployeeIdMapByDepartmentId.Arg();
            arg.setDepartmentIds(accessibleDepartments);
            arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            arg.setRunStatus(RunStatus.ACTIVE);
            arg.setIncludeLowDepartment(true);
            BatchGetEmployeeIdMapByDepartmentId.Result employeeIdResult = employeeProviderService.batchGetEmployeeIdMapByDepartmentId(arg);
            if (employeeIdResult != null && employeeIdResult.getEmployeeMap() != null) {
                for (Map.Entry<Integer, List<Integer>> entry : employeeIdResult.getEmployeeMap().entrySet()) {
                    if (CollectionUtils.isNotEmpty(entry.getValue())) {
                        accessibleDepartmentsFsUserIds.addAll(entry.getValue());
                    }
                }
            }
        }
        return accessibleDepartmentsFsUserIds;
    }

    /**
     * 处理企业微信群主选择器
     *
     * @param ea
     * @param ownerList
     * @param departmentIds
     * @param tagIds
     * @return
     */
    public List<String> handleQywxOwnerUserId(String ea, Integer fsUserId, List<String> ownerList, List<Integer> departmentIds, List<Integer> tagIds) {
        List<String> ownerUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ownerList)) {
            ownerUserIds.addAll(ownerList);
        }
//        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        CountDownLatch countDownLatch = new CountDownLatch(2);
        ThreadPoolUtils.executeWithTraceContext(() -> {
            try {
//                if (isOpen) {
//                    ownerUserIds.addAll(this.handleQywxEmployeeUserIdWithOpenDataPermission(ea, fsUserId, ownerList, departmentIds) == null ? Lists.newArrayList() : this.handleQywxEmployeeUserIdWithOpenDataPermission(ea, fsUserId, ownerList, departmentIds));
//                } else {
                    ownerUserIds.addAll(this.handleQywxEmployeeUserId(ea, null, departmentIds, tagIds) == null ? Lists.newArrayList() : this.handleQywxEmployeeUserId(ea, ownerList, departmentIds, tagIds));
//                }
            } catch (Exception e) {
                log.warn("QywxManager handleQywxOwnerUserId handleQywxOwnerUserId error", e);
            } finally {
                countDownLatch.countDown();
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        List<QueryGroupOwnerListResult> queryGroupOwnerListResults = Lists.newArrayList();
        ThreadPoolUtils.executeWithTraceContext(() -> {
            try {
                queryGroupOwnerListResults.addAll(customerGroupManager.queryGroupOwnerList(ea, fsUserId) == null ? Lists.newArrayList() : customerGroupManager.queryGroupOwnerList(ea, fsUserId));
            } catch (Exception e) {
                log.warn("QywxManager handleQywxOwnerUserId queryGroupOwnerList error", e);
            } finally {
                countDownLatch.countDown();
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        try {
            countDownLatch.await(4L, TimeUnit.SECONDS);
        } catch (Exception e) {
            return ownerList;
        }
        if (CollectionUtils.isNotEmpty(queryGroupOwnerListResults) && CollectionUtils.isNotEmpty(ownerUserIds)) {
            List<String> queryGroupOwnerList = queryGroupOwnerListResults.stream().map(QueryGroupOwnerListResult::getGroupOwnerUserId).collect(Collectors.toList());
            Collection<String> intersection = CollectionUtils.intersection(queryGroupOwnerList, ownerUserIds);
            if (CollectionUtils.isNotEmpty(intersection)) {
                return intersection.stream().distinct().collect(Collectors.toList());
            }
        }
        return ownerList;
    }

    public String handleQywxOwnerUseName(String ea, List<String> ownerList, List<Integer> departmentIds, List<Integer> tagIds) {
        List<String> ownerUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ownerList)) {
            ownerUserIds.addAll(ownerList);
        }
        CountDownLatch countDownLatch = new CountDownLatch(2);
        ThreadPoolUtils.executeWithTraceContext(() -> {
            try {
                ownerUserIds.addAll(this.handleQywxEmployeeUserId(ea, null, departmentIds, tagIds) == null ? Lists.newArrayList() : this.handleQywxEmployeeUserId(ea, ownerList, departmentIds, tagIds));
            } catch (Exception e) {
                log.warn("QywxManager handleQywxOwnerUseName handleQywxOwnerUserId error", e);
            } finally {
                countDownLatch.countDown();
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        List<QueryGroupOwnerListResult> queryGroupOwnerListResults = Lists.newArrayList();
        ThreadPoolUtils.executeWithTraceContext(() -> {
            try {
                queryGroupOwnerListResults.addAll(customerGroupManager.queryGroupOwnerList(ea, null) == null ? Lists.newArrayList() : customerGroupManager.queryGroupOwnerList(ea, null));
            } catch (Exception e) {
                log.warn("QywxManager handleQywxOwnerUseName queryGroupOwnerList error", e);
            } finally {
                countDownLatch.countDown();
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        try {
            countDownLatch.await(4L, TimeUnit.SECONDS);
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
        if (CollectionUtils.isNotEmpty(queryGroupOwnerListResults) && CollectionUtils.isNotEmpty(ownerUserIds)) {
            List<String> queryGroupOwnerList = queryGroupOwnerListResults.stream().map(QueryGroupOwnerListResult::getGroupOwnerUserId).collect(Collectors.toList());
            Collection<String> intersection = CollectionUtils.intersection(queryGroupOwnerList, ownerUserIds);
            if (CollectionUtils.isNotEmpty(intersection)) {
                List<String> ownerUseName = Lists.newArrayList();
                queryGroupOwnerListResults.forEach(e -> {
                    if (intersection.contains(e.getGroupOwnerUserId())) {
                        ownerUseName.add(e.getGroupOwnerUserName());
                    }
                });
                return ownerUseName.stream().distinct().collect(Collectors.joining("、"));
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 当前企业是否已连接企业微信
     * @param ea
     * @return
     */
    public boolean isApplyQywx(String ea){
        QywxCorpAgentConfigEntity agentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        QywxCustomerAppInfoEntity qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectByEa(ea, QywxSuiteTypeEnum.ASSISTANT.getValue());
        if (Objects.nonNull(agentConfigEntity) && Objects.nonNull(qywxCustomerAppInfoEntity)) {
            return true;
        }
        return false;
    }

    // 获取企业的帐号列表
    public ListActivatedAccountResult listActivatedAccount(String corpId, String nextCursor) {
        String providerAccessToken = getProviderAccessToken(QYWXApiConstants.QYWX_PROVIDER_CORPID, QYWXApiConstants.QYWX_PROVIDER_SECRET);
        if (StringUtils.isBlank(providerAccessToken)) {
            return null;
        }
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "license/list_actived_account?provider_access_token=" + providerAccessToken;
        ListActivatedAccountArg arg = new ListActivatedAccountArg();
        arg.setCorpId(corpId);
        arg.setLimit(1000);
        arg.setCursor(nextCursor);
        return httpManager.executePostHttp(arg, url, new TypeToken<ListActivatedAccountResult>() {
        });
    }

    public List<SendWelcomeMessageNewArg.Attachment>  builtNewAttachmentsArg(String ea, String accessToken, QywxAttachmentsRelationEntity attachmentsRelation, String qywxCorpid, String userID, RelationConfig config){
        List<QywxAttachmentsVO> vo = JSON.parseArray(attachmentsRelation.getAttachments(), QywxAttachmentsVO.class);
        if(CollectionUtils.isEmpty(vo)){
            return null;
        }
        String aggregateKey = null;
        if(Objects.nonNull(config)){
            Result<String> stringResult = traceabilityRelationService.getAggregateKey(ea, config);
            if(stringResult.isSuccess()){
                aggregateKey = stringResult.getData();
            }
        }
        List<SendWelcomeMessageNewArg.Attachment> attachments = Lists.newArrayList();
        QywxMiniappConfigEntity miniappConfigEntity = miniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        //标记排序
        ConcurrentMap<String, QywxAttachmentsVO> voMap = Maps.newConcurrentMap();
        for (int i = 0; i < vo.size(); i++) {
            voMap.put(String.valueOf(i),vo.get(i));
        }

        ConcurrentMap <String,SendWelcomeMessageNewArg.Attachment> resultAttachmentMap = Maps.newConcurrentMap();
        TraceContext context = TraceContext.get();
        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(5,"marketing_qywx_attachments_size");
        CountDownLatch countDownLatch = new CountDownLatch(voMap.size());
        for (String s : voMap.keySet()) {
            QywxAttachmentsVO attachmentsVO = voMap.get(s);
            String finalAggregateKey = aggregateKey;
            executorService.execute(() -> {
                SendWelcomeMessageNewArg.Attachment attachment = null;
                try {
                    if (context != null ) {
                        TraceContext._set(context);
                    }
                    if ((QywxAttachmentTypeEnum.MINIPROGRAM.getType() == attachmentsVO.getAttachmentType()||QywxAttachmentTypeEnum.OUT_MINIPROGRAM.getType() == attachmentsVO.getAttachmentType() )
                            && attachmentsVO.getMiniprogram()!=null) {
                        attachment = new SendWelcomeMessageNewArg.Attachment();
                        SendWelcomeMessageNewArg.Attachment.Miniprogram miniprogram = new SendWelcomeMessageNewArg.Attachment.Miniprogram();
                        if (MiniProgramTypeEnum.CONTENT.getType() == attachmentsVO.getMiniprogram().getMiniProgramType()) {
                            //内容链接
                            byte[] bytes = new byte[0];
                            bytes = attachmentsVO.getMiniprogram().getTitle().getBytes("utf-8");

                            if (bytes.length > 64) {
                                String substr = cutStringByU8(attachmentsVO.getMiniprogram().getTitle(), 64);
                                miniprogram.setTitle(substr);
                            } else {
                                miniprogram.setTitle( attachmentsVO.getMiniprogram().getTitle());
                            }
                            String url = attachmentsVO.getMiniprogram().getPage();
                            url = ReplaceUtil.replaceWxAppId(url,"");
                            url = ReplaceUtil.replaceMarketingActivityId(url,"");
                            url = ReplaceUtil.replaceMarketingEventId(url,"");
                            if(StringUtils.isNotBlank(finalAggregateKey)){
                                url = url + "&aggregateKey="+ finalAggregateKey;
                            }
                            miniprogram.setPage(url);
                            if (StringUtils.isNotBlank(attachmentsVO.getMiniprogram().getAppId())) {
                                miniprogram.setAppId(attachmentsVO.getMiniprogram().getAppId());
                            } else {
                                miniprogram.setAppId(miniappConfigEntity.getAppid());
                            }
                            String imagePath=null;
                            String objectId = attachmentsVO.getMiniprogram().getMaterialId();
                            if(attachmentsVO.getMiniprogram().getObjectType()==26){
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(objectId);
                                objectId = homePage.getId();
                            } else if (attachmentsVO.getMiniprogram().getObjectType() == 13) {
                                ActivityEntity activity = activityDAO.getById(objectId);
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activity.getActivityDetailSiteId());
                                objectId = homePage.getId();
                            }
                            List<PhotoEntity> photoEntities = photoDAO.queryPhotosByTypesAndTargetId(Lists.newArrayList(43, 46, 49), objectId);
                            if (CollectionUtils.isNotEmpty(photoEntities)){
                                imagePath = photoEntities.get(0).getThumbnailUrl();
                            }else {
                                imagePath = attachmentsVO.getMiniprogram().getPicPath();
                            }
                            if (StringUtils.isBlank(imagePath)) {
                                imagePath = groupMessageDefaultCoverPath;
                            }
                            String imageMediaId = redisManager.getQywxMediaId(ea+"_"+imagePath);
                            if (imageMediaId == null) {
                                byte[] data = null;
                                if (imagePath.startsWith("C_")) {
                                    data = fileV2Manager.getByteDataByUrl(fileV2Manager.getUrlByPath(ea, imagePath));
                                }else if(imagePath.startsWith("http")){
                                    data = fileV2Manager.getByteDataByUrl(imagePath);
                                } else {
                                    data = fileV2Manager.downloadAFile(imagePath, ea);
                                }
                                UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, imagePath, "image");
                                if (mediaResult == null || mediaResult.getErrcode() != 0){
                                    log.warn("qywxManager builtNewAttachmentsArg uploadFile imagePath failed ea:{} imagePath:{} ,QywxAttachmentType:MINIPROGRAM, data.length:{}, result:{}", ea, imagePath,data.length, mediaResult);
                                    return;
                                }
                                imageMediaId = mediaResult.getMediaId();
                                redisManager.setQywxMediaId(ea+"_"+imagePath, mediaResult.getMediaId());
                            }
                            miniprogram.setPicMediaId(imageMediaId);
                        } else if (MiniProgramTypeEnum.OUTLINK.getType() == attachmentsVO.getMiniprogram().getMiniProgramType()) {
                            //外部链接
                            miniprogram.setAppId(attachmentsVO.getMiniprogram().getAppId());
                            byte[] bytes = new byte[0];
                            bytes = attachmentsVO.getMiniprogram().getTitle().getBytes("utf-8");
                            if (bytes.length > 64) {
                                String substr = cutStringByU8(attachmentsVO.getMiniprogram().getTitle(), 64);
                                miniprogram.setTitle(substr);
                            } else {
                                miniprogram.setTitle( attachmentsVO.getMiniprogram().getTitle());
                            }
                            String url = attachmentsVO.getMiniprogram().getPage();
                            if(StringUtils.isNotBlank(finalAggregateKey)){
                                url = url + "&aggregateKey="+ finalAggregateKey;
                            }
                            miniprogram.setPage(url);
                            String imagePath = attachmentsVO.getMiniprogram().getPicPath();
                            if (StringUtils.isBlank(imagePath)) {
                                imagePath = groupMessageDefaultCoverPath;
                            }
                            String imageMediaId = redisManager.getQywxMediaId(ea+"_"+imagePath);
                            if (imageMediaId == null) {
                                byte[] data = null;
                                if (imagePath.startsWith("C_")) {
                                    data = fileV2Manager.getByteDataByUrl(fileV2Manager.getUrlByPath(ea, imagePath));
                                } else {
                                    data = fileV2Manager.downloadAFile(imagePath, ea);
                                }
                                UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, imagePath, "image");
                                if (mediaResult == null || mediaResult.getErrcode() != 0){
                                    log.warn("qywxManager builtNewAttachmentsArg uploadFile imagePath failed ea:{} imagePath:{} ,QywxAttachmentType:OUT_MINIPROGRAM, data.length:{}, result:{}", ea, imagePath,data.length, mediaResult);
                                    return;
                                }
                                imageMediaId = mediaResult.getMediaId();
                                redisManager.setQywxMediaId(ea+"_"+imagePath, mediaResult.getMediaId());
                            }
                            miniprogram.setPicMediaId(imageMediaId);
                            miniprogram.setAppendMktParam(attachmentsVO.getMiniprogram().isAppendMktParam());
                        }else {
                            if(StringUtils.isNotBlank(qywxCorpid) && StringUtils.isNotBlank(userID)){
                                //员工名片
                                //获取员工名片地址
                                UserEntity userEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(qywxCorpid, userID, miniappConfigEntity.getAppid());
                                if (userEntity == null) {
                                    log.warn("未查询到员工信息 userId：{}",userID);
                                    return;
                                }
                                //获取名片详情
                                CardEntity cardEntity = cardDAO.queryCardInfoByUid(userEntity.getUid());
                                if (cardEntity == null) {
                                    log.warn("未查询到员工名片信息 uid：{}",userEntity.getUid());
                                    return;
                                }
                                List<PhotoEntity> photoEntities = photoDAO.listByTargetIdsAndTargetType(userEntity.getUid(), PhotoTargetTypeEnum.MINI_COVER_CARD_SHARE.getType());
                                if (CollectionUtils.isEmpty(photoEntities)) {
                                    log.warn("未查询到员工名片封面,uid:{}",userEntity.getUid());
                                    return;
                                }
                                PhotoEntity photoEntity = photoEntities.get(0);
                                log.info("获取的名片封面信息: photo:{}",GsonUtil.getGson().toJson(photoEntity));
                                String cardMediaId = redisManager.getQywxMediaId(ea+"_"+photoEntity.getPath());
                                if (cardMediaId == null) {
                                    byte[] data = null;
                                    if (photoEntity.getPath().startsWith("C_")) {
                                        data = fileV2Manager.getByteDataByUrl(fileV2Manager.getUrlByPath(ea, photoEntity.getPath()));
                                    } else {
                                        data = fileV2Manager.downloadAFile(photoEntity.getPath(), ea);
                                    }
                                    UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, photoEntity.getPath(), "image");
                                    if (mediaResult == null || mediaResult.getErrcode() != 0){
                                        log.warn("qywxManager builtNewAttachmentsArg  updateFile CardPath failed ea:{} id:{} ,QywxAttachmentType:card ,data.length:{}, result:{}", ea, attachmentsRelation.getTargetId(),data.length, mediaResult);
                                        return;
                                    }
                                    cardMediaId = mediaResult.getMediaId();
                                    redisManager.setQywxMediaId(ea+"_"+photoEntity.getPath(), mediaResult.getMediaId());
                                }
                                if(attachmentsVO.getMiniprogram().getPage().endsWith("?")){
                                    miniprogram.setPage(attachmentsVO.getMiniprogram().getPage()+"objectId="+cardEntity.getUid());
                                }else {
                                    miniprogram.setPage(attachmentsVO.getMiniprogram().getPage()+"&objectId="+cardEntity.getUid());
                                }
                                String url = miniprogram.getPage();
                                if(StringUtils.isNotBlank(finalAggregateKey)){
                                    url = url + "&aggregateKey="+ finalAggregateKey;
                                }
                                miniprogram.setPage(url);
                                miniprogram.setPicMediaId(cardMediaId);
                                miniprogram.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXCONTACTMANAGER_497));
                                miniprogram.setAppId(miniappConfigEntity.getAppid());
                            }
                        }
                        attachment.setMsgtype("miniprogram");
                        attachment.setMiniprogram(miniprogram);
                    } else if ((QywxAttachmentTypeEnum.H5.getType() == attachmentsVO.getAttachmentType()||QywxAttachmentTypeEnum.OUT_H5.getType() == attachmentsVO.getAttachmentType())
                            && attachmentsVO.getLink()!=null) {
                        attachment = new SendWelcomeMessageNewArg.Attachment();
                        SendWelcomeMessageNewArg.Attachment.Link link = new SendWelcomeMessageNewArg.Attachment.Link();
                        link.setTitle(attachmentsVO.getLink().getTitle());

                        String url = attachmentsVO.getLink().getUrl();
                        url = ReplaceUtil.replaceWxAppId(url,"");
                        url = ReplaceUtil.replaceMarketingActivityId(url,"");
                        url = ReplaceUtil.replaceMarketingEventId(url,"");

                        String picUrl = attachmentsVO.getLink().getPicUrl();
                        if(Objects.equals(QywxAttachmentTypeEnum.H5.getType(), attachmentsVO.getAttachmentType())){
                            String objectId = attachmentsVO.getLink().getMaterialId();
                            if(attachmentsVO.getLink().getObjectType()==26){
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(objectId);
                                objectId = homePage.getId();
                            } else if (attachmentsVO.getLink().getObjectType() == 13) {
                                ActivityEntity activity = activityDAO.getById(objectId);
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activity.getActivityDetailSiteId());
                                objectId = homePage.getId();
                            }
                            List<PhotoEntity> photoEntities = photoDAO.queryPhotosByTypesAndTargetId(Lists.newArrayList(44, 47, 50), objectId);
                            if (CollectionUtils.isNotEmpty(photoEntities)){
                                picUrl = photoEntities.get(0).getThumbnailUrl();
                            }
                            if(StringUtils.isNotBlank(finalAggregateKey)){
                                url = url + "&aggregateKey="+ finalAggregateKey;
                            }
                        }else {
                            if(StringUtils.isNotBlank(finalAggregateKey) && attachmentsVO.getLink().isAppendMktParam()){
                                url = url + "&aggregateKey="+ finalAggregateKey;
                            }
                        }
                        link.setPicUrl(picUrl);
                        if (StringUtils.isNotBlank(attachmentsVO.getLink().getDesc())) {
                            byte[] bytes = attachmentsVO.getLink().getDesc().getBytes("utf-8");
                            if (bytes.length > 512) {
                                String substr = cutStringByU8(attachmentsVO.getLink().getDesc(), 512);
                                link.setDesc(substr);
                            } else {
                                link.setDesc(attachmentsVO.getLink().getDesc());
                            }
                        }
//                        String url = attachmentsVO.getLink().getUrl();
                        link.setUrl(url);
                        link.setAppendMktParam(attachmentsVO.getLink().isAppendMktParam());
                        attachment.setMsgtype("link");
                        attachment.setLink(link);
                    } else if (QywxAttachmentTypeEnum.IMAGE.getType() == attachmentsVO.getAttachmentType() && attachmentsVO.getImage()!=null) {
                        attachment = new SendWelcomeMessageNewArg.Attachment();
                        SendWelcomeMessageNewArg.Attachment.Image image = new SendWelcomeMessageNewArg.Attachment.Image();
                        String imagePath = attachmentsVO.getImage().getImagePath();
                        String imageMediaId = redisManager.getQywxMediaId(imagePath);
                        if (imageMediaId == null) {
                            byte[] data = null;
                            if (imagePath.startsWith("C_")) {
                                data = fileV2Manager.getByteDataByUrl(fileV2Manager.getUrlByPath(ea, imagePath));
                            } else {
                                data = fileV2Manager.downloadFileByUrl(imagePath, ea);
                            }
                            UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, imagePath, "image");
                            if (mediaResult == null || mediaResult.getErrcode() != 0){
                                log.warn("qywxManager builtNewAttachmentsArg uploadFile imagePath failed ea:{}, imagePath:{}, QywxAttachmentType:IMAGE, data.length:{}, result:{},", ea, imagePath, data.length,mediaResult);
                                return;
                            }
                            imageMediaId = mediaResult.getMediaId();
                            redisManager.setQywxMediaId(imagePath, mediaResult.getMediaId());
                        }
                        image.setMediaId(imageMediaId);
                        attachment.setMsgtype("image");
                        attachment.setImage(image);
                    } else if (QywxAttachmentTypeEnum.VIDEO.getType() == attachmentsVO.getAttachmentType() && attachmentsVO.getVideo()!=null) {
                        attachment = new SendWelcomeMessageNewArg.Attachment();
                        SendWelcomeMessageNewArg.Attachment.Link link = new SendWelcomeMessageNewArg.Attachment.Link();
                        link.setTitle(attachmentsVO.getVideo().getVideoName());
                        String url = attachmentsVO.getVideo().getVideoUrl();
                        url = ReplaceUtil.replaceWxAppId(url,"");
                        url = ReplaceUtil.replaceMarketingActivityId(url,"");
                        url = ReplaceUtil.replaceMarketingEventId(url,"");
                        link.setUrl(url);
                        link.setPicUrl(QywxFileTypeLogoEnum.MOV.getLogo());
                        attachment.setMsgtype("link");
                        attachment.setLink(link);
                    } else if (QywxAttachmentTypeEnum.FILE.getType() == attachmentsVO.getAttachmentType() && attachmentsVO.getFile()!=null) {
                        attachment = new SendWelcomeMessageNewArg.Attachment();
                        SendWelcomeMessageNewArg.Attachment.Link link = new SendWelcomeMessageNewArg.Attachment.Link();
                        link.setTitle(attachmentsVO.getFile().getFileName());
                        link.setDesc(attachmentsVO.getFile().getFileName());
                        String url = attachmentsVO.getFile().getFileUrl();
                        url = ReplaceUtil.replaceWxAppId(url,"");
                        url = ReplaceUtil.replaceMarketingActivityId(url,"");
                        url = ReplaceUtil.replaceMarketingEventId(url,"");
                        link.setUrl(url);
                        link.setPicUrl(QywxFileTypeLogoEnum.getLogoByType(attachmentsVO.getFile().getExt()));
                        attachment.setMsgtype("link");
                        attachment.setLink(link);
                    }
                    if (attachment != null) {
                        resultAttachmentMap.put(s,attachment);
                    }
                } catch (Exception e) {
                    log.warn("QywxManager.builtNewAttachmentsArg error e:{}", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(18L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("QywxManager.builtNewAttachmentsArg.await error e:{}", e);
        }finally {
            if (!executorService.isShutdown()) {
                executorService.shutdown();
            }
        }
        //还原顺序
        attachments = resultAttachmentMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue).collect(Collectors.toList());
        return attachments;
    }

    //按照utf-8截取字符串
    private String cutStringByU8(String str, int len) throws IOException {
        byte[] buf = str.getBytes("utf-8");
        int count = 0;
        for (int x = len - 1; x >= 0; x--) {
            if (buf[x] < 0) {
                count++;
            } else {
                break;
            }
        }
        if (count % 3 == 0) {
            return new String(buf, 0, len, "utf-8");
        } else if (count % 3 == 1) {
            return new String(buf, 0, len - 1, "utf-8");
        } else {
            return new String(buf, 0, len - 2, "utf-8");
        }
    }

    public boolean isSyncCrmAppCallBackData(String ea) {
        List<String> eas = GsonUtil.getGson().fromJson(syncScrmBlackList, ArrayList.class);
        return !(eas.contains(ea) || eas.contains("*")) && isNewInstallAgentApp(ea);
    }

    public Map<String, String> convertToNewExternalUserId(String ea, List<String> externalUserIdList) {
        if (CollectionUtils.isEmpty(externalUserIdList)) {
            return Maps.newHashMap();
        }
        String accessToken = getAccessToken(ea);

        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.convertToNewExternalUserId accessToken is null ea:{}", ea);
            return Maps.newHashMap();
        }

        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/get_new_external_userid?access_token=" + accessToken;
        ExternalUserIdConvertArg arg = new ExternalUserIdConvertArg();
        arg.setExternalUseridList(externalUserIdList);
        ExternalUserIdConvertResult result = httpManager.executePostHttp(arg, url, new TypeToken<ExternalUserIdConvertResult>() {});
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getItems())) {
            return Maps.newHashMap();
        }
        return result.getItems().stream().collect(Collectors.toMap(ExternalUserIdConvertResult.Item::getExternalUserId, ExternalUserIdConvertResult.Item::getNewExternalUserId, (v1, v2) -> v1));
    }

    public String convertToNewCorpId(String ea, String corpId) {
        if (StringUtils.isBlank(corpId)) {
            return null;
        }
        String providerAccessToken = getProviderAccessToken(QYWXApiConstants.QYWX_PROVIDER_CORPID, QYWXApiConstants.QYWX_PROVIDER_SECRET);
        if (StringUtils.isBlank(providerAccessToken)) {
            log.warn("QywxManager.convertToNewCorpId accessToken is null ea:{}", ea);
            return null;
        }
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "service/corpid_to_opencorpid?provider_access_token=" + providerAccessToken;
        Map<String, String> arg = Maps.newHashMap();
        arg.put("corpid", corpId);
        CorpIdConvertResult result = httpManager.executePostHttp(arg, url, new TypeToken<CorpIdConvertResult>() {});
        if (result == null || !result.isSuccess() || StringUtils.isBlank(result.getOpenCorpId())) {
            return null;
        }
        return result.getOpenCorpId();
    }

    public Map<String, String> convertToNewTagId(String ea, List<String> tagIdList) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return Maps.newHashMap();
        }
        String accessToken = getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.convertToNewTagId accessToken is null ea:{}", ea);
            return Maps.newHashMap();
        }
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "idconvert/external_tagid?access_token=" + accessToken;
        Map<String, List<String>> arg = Maps.newHashMap();
        arg.put("external_tagid_list", tagIdList);
        TagIdConvertResult result = httpManager.executePostHttp(arg, url, new TypeToken<TagIdConvertResult>() {});
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getItems())) {
            return Maps.newHashMap();
        }
        return result.getItems().stream().collect(Collectors.toMap(TagIdConvertResult.Item::getExternalTagId, TagIdConvertResult.Item::getOpenExternalTagId, (v1, v2) -> v1));
    }

    public Result<Void> finishOpenIdMigration(String ea) {
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMANAGER_3238));
        }
        // agentId不维护了  要手动配置
        JSONObject jsonObject = JSONObject.parseObject(qywxEaAgentIdConfig);
        Integer agentId = jsonObject.getInteger(ea);
        if (agentId == null && StringUtils.isNotBlank(qywxCorpAgentConfigEntity.getAgentid())) {
            agentId = Integer.parseInt(qywxCorpAgentConfigEntity.getAgentid());
        }
        if (agentId == null) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMANAGER_3244));
        }
        String providerAccessToken = getProviderAccessToken(QYWXApiConstants.QYWX_PROVIDER_CORPID, QYWXApiConstants.QYWX_PROVIDER_SECRET);
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "service/finish_openid_migration?provider_access_token=" + providerAccessToken;
        Map<String, Object> arg = Maps.newHashMap();
        arg.put("corpid", qywxCorpAgentConfigEntity.getCorpid());
        arg.put("agentid", agentId);
        arg.put("openid_type", Lists.newArrayList(1, 3));
        BaseResult result = httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {});
        log.info("finishOpenIdMigration ea: {} arg: {} result: {}", ea, arg, result);
        return Result.newError(result.getErrcode(), result.getErrmsg());
    }

    // 是否是新安装的应用，新安装的应用企微的数据都是密文，旧的应用企微的数据都是明文
    public boolean isNewInstallAgentApp(String ea) {
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null) {
            return true;
        }
        // 此时间后的认为是新安装的
        String compareDate = "2022-07-18 00:00:00";
        Date date = DateUtil.parse(compareDate);
        return qywxCorpAgentConfigEntity.getCreateTime().compareTo(date) > 0;
    }

    public Map<Integer, String> getAllDepartmentName(String ea) {
        Map<Integer, String> departmentMap = Maps.newHashMap();
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = null;
        if (agentConfig != null) {
            accessToken = getAccessToken(ea);
            DepartmentListResult departmentListResult = queryDepartment(accessToken);
            if (departmentListResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(departmentListResult.getDepartmentList())) {
                Map<Integer, String> tempMap = departmentListResult.getDepartmentList().stream().collect(Collectors.toMap(Department::getId, Department::getName, (v1, v2) -> v1));
                departmentMap.putAll(tempMap);
            }
        }
        return departmentMap;
    }

    public String syncExternalUserRemark(String ea, UpdateQywxExternalUserRemarkVO remarkVO) {
        String accessToken = getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxManager.syncExternalUserRemark accessToken is null ea:{}", ea);
            return "failed";
        }
        String url = QYWXApiConstants.QYWX_RESET_API_HOST + "externalcontact/remark?access_token=" + accessToken;
        Map<String, Object> arg = Maps.newHashMap();
        arg.put("userid", remarkVO.getUserId());
        arg.put("external_userid", remarkVO.getExternalUserId());

        arg.put("remark", remarkVO.getRemark());
        arg.put("description", remarkVO.getDescription());
        arg.put("remark_company", remarkVO.getRemarkCompany());
        arg.put("remark_mobiles", remarkVO.getRemarkMobiles());
        BaseResult result = httpManager.executePostHttp(arg, url, new TypeToken<BaseResult>() {});
        if (result == null){
            return "failed";
        }
        return result.getErrmsg();
    }
}