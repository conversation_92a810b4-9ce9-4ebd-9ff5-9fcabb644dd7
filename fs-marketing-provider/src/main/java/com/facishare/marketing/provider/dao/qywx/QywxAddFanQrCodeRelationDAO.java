package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.dto.qywx.TemplateBindQrConfigIdDTO;
import com.facishare.marketing.provider.entity.qywx.QywxQrCodeBrowseUserRelationEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

public interface QywxAddFanQrCodeRelationDAO {
    @Insert("INSERT INTO qywx_qr_code_browse_user_relation(id, ea, browse_user_id, qywx_qr_code_id, config_id, qr_code, state, status, create_time, update_time) VALUES(#{obj.id}, #{obj.ea}, #{obj.browseUserId}, #{obj.qywxQrCodeId}, #{obj.configId}, #{obj.qrCode}, #{obj.state}, #{obj.status}, now(), now()) ON CONFLICT DO NOTHING")
    int insert(@Param("obj")QywxQrCodeBrowseUserRelationEntity obj);

    @Select("SELECT * FROM qywx_qr_code_browse_user_relation WHERE ea=#{ea} AND browse_user_id=#{browseUserId} AND qywx_qr_code_id=#{qywxQrCodeId} AND status=0 limit 1")
    QywxQrCodeBrowseUserRelationEntity queryByBrowseIdAndQrCodeId(@Param("ea")String ea, @Param("browseUserId")String browseUserId, @Param("qywxQrCodeId")String qywxQrCodeId);

    @Update("UPDATE qywx_qr_code_browse_user_relation SET status=1, update_time=now() WHERE ea=#{ea} AND qywx_qr_code_id=#{qywxQrCodeId}")
    int deleteByQrCodeId(@Param("ea")String ea, @Param("qywxQrCodeId")String qywxQrCodeId);

    @Select("SELECT * FROM qywx_qr_code_browse_user_relation WHERE ea=#{ea} AND state=#{state} limit 1")
    QywxQrCodeBrowseUserRelationEntity queryByState(@Param("ea")String ea, @Param("state")String state);

    @Select("SELECT ea, config_id AS configId, qywx_qr_code_id AS qrCodeId FROM qywx_qr_code_browse_user_relation WHERE create_time<#{deletePointDate} AND status=0")
    List<TemplateBindQrConfigIdDTO> getTemplateBindQrConfigIdsByCheckTime(@Param("ea")String ea, @Param("deletePointDate") Date deletePointDate);
}
