/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.marketingplugin;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dto.shanshan.ShanShanTokenDto;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.fxiaoke.crmrestapi.arg.FindLayoutArg;
import com.fxiaoke.crmrestapi.arg.UpdateLayoutArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.LayoutDescribe;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.FindLayoutResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.crmrestapi.service.ObjectLayoutService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("marketingUserPluginService")
public class MarketingUserPluginService extends MarketingPluginBaseService {

    @Autowired
    private ObjectLayoutService objectLayoutService;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private EIEAConverter eieaConverter;
    @ReloadableProperty("PaasMetadataUrl")
    private String paasMetadataUrl;
    @Autowired
    private HttpManager httpManager;

    @Override
    public Result checkEnable(String ea) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), "AccountObj");
        if (!describe.isSuccess() || describe.getData() == null || describe.getData().getDescribe() == null) {
            return Result.newError(SHErrorCode.NOT_OPEN_ENTERPRISE_INFO_OBJ);
        }
        //添加客户对象layout
        addMarketingUserLayout(ea,"AccountObj","AccountObj_layout_generate_by_UDObjectServer__c");
        //添加企业库对象layout
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> enterpriseInfoDescribe = objectDescribeService.getDescribe(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), "EnterpriseInfoObj");
        if (enterpriseInfoDescribe.isSuccess() && enterpriseInfoDescribe.getData() != null) {
            addMarketingUserLayout(ea,"EnterpriseInfoObj","EnterpriseInfoObj_layout_generate_by_UDObjectServer__c");
        }
        return Result.newSuccess();
    }


    public void addMarketingUserLayout(String ea,String describeApiName,String layoutApiName) {
        //查询Layout
        FindLayoutArg arg = new FindLayoutArg();
        arg.setObjectDescribeApiName(describeApiName);
        arg.setApiName(layoutApiName);
        InnerResult<FindLayoutResult> layout = getLayout(ea, arg);
        FindLayoutResult result = layout.getResult();
        LayoutDescribe getLayout = result.getLayout();
        List<LinkedTreeMap<String,Object>> componentsList = (List<LinkedTreeMap<String,Object>>) getLayout.get("components");
        for (LinkedTreeMap<String,Object> it : componentsList) {
            if (Objects.equals(it.get("type"),"tabs")) {
                List<LinkedTreeMap<String,Object>> tabsMap = (List<LinkedTreeMap<String,Object>>) it.get("tabs");
                LinkedTreeMap<String,Object> map = new LinkedTreeMap<>();
                map.put("api_name","marketing_user_account");
                map.put("header","营销用户");
                tabsMap.add(map);
                List<List<String>> components = (List<List<String>>) it.get("components");
                List<String> userMarketing = new ArrayList<>();
                userMarketing.add("marketing_user_account");
                components.add(userMarketing);
                break;
            }
        }
        LinkedTreeMap<String,Object> componentUserMap = new LinkedTreeMap<>();
        componentUserMap.put("field_section",new ArrayList<>());
        componentUserMap.put("buttons",new ArrayList<>());
        componentUserMap.put("api_name","marketing_user_account");
        componentUserMap.put("related_list_name","");
        componentUserMap.put("header","营销用户");
        componentUserMap.put("style", new HashMap<String, String>() {{
            put("height", "500px");
        }});
        componentUserMap.put("type","marketing_user_account");
        componentUserMap.put("moreUrl", "");
        componentUserMap.put("isSticky", false);
        componentsList.add(componentUserMap);
        //更新layout
        UpdateLayoutArg updateArg = new UpdateLayoutArg();
        updateArg.setLayout_data(GsonUtil.toJson(getLayout));
        objectLayoutService.updateLayout(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), updateArg);
    }

    private InnerResult<FindLayoutResult> getLayout(String ea, FindLayoutArg arg) {
        String callBackURL =  paasMetadataUrl + "/v1/inner/object/layout/service/findLayout";
        Map<String, String> header = new HashMap<>();
        header.put("x-fs-ei", String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
        header.put("X-fs-Employee-Id", "-10000");
        header.put("x-fs-userInfo", "-10000");
        header.put("client_info", "rest-api");

        RequestBody requestBody = RequestBody.create(
                JSON.toJSONString(arg),
                MediaType.parse("application/json; charset=UTF-8")
        );
        Gson gsonNumber = new GsonBuilder()
                .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
                .create();
        InnerResult<FindLayoutResult> layout = httpManager.executePostByOkHttpClientWithRequestBodyAndHeaderV2(requestBody, callBackURL, new TypeToken<InnerResult<FindLayoutResult>>() {}, header,gsonNumber);
        return layout;
    }

    public Result checkUnable(String ea) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), "AccountObj");
        if (!describe.isSuccess() || describe.getData() == null || describe.getData().getDescribe() == null) {
            return Result.newError(SHErrorCode.NOT_OPEN_ENTERPRISE_INFO_OBJ);
        }
        //删除客户对象的layout
        deleteMarketingUserLayout(ea,"AccountObj","AccountObj_layout_generate_by_UDObjectServer__c");
        //删除企业库对象的layout
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> enterpriseInfoDescribe = objectDescribeService.getDescribe(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), "EnterpriseInfoObj");
        if (enterpriseInfoDescribe.isSuccess() && enterpriseInfoDescribe.getData() != null) {
            deleteMarketingUserLayout(ea,"EnterpriseInfoObj","EnterpriseInfoObj_layout_generate_by_UDObjectServer__c");
        }
        return Result.newSuccess();
    }

    private void deleteMarketingUserLayout(String ea,String describeApiName,String layoutApiName) {
        FindLayoutArg arg = new FindLayoutArg();
        arg.setObjectDescribeApiName(describeApiName);
        arg.setApiName(layoutApiName);
        InnerResult<FindLayoutResult> layout = getLayout(ea, arg);
        FindLayoutResult result = layout.getResult();
        LayoutDescribe getLayout = result.getLayout();
        List<LinkedTreeMap<String,Object>> componentsList = (List<LinkedTreeMap<String,Object>>) getLayout.get("components");
        Iterator<LinkedTreeMap<String,Object>> iterator = componentsList.iterator();
        while (iterator.hasNext()) {
            LinkedTreeMap<String,Object> it = iterator.next();
            if (Objects.equals(it.get("type"),"tabs")) {
                List<LinkedTreeMap<String,Object>> tabsMap = (List<LinkedTreeMap<String,Object>>) it.get("tabs");
                Iterator<LinkedTreeMap<String,Object>> treeMapIterator = tabsMap.iterator();
                while (treeMapIterator.hasNext()) {
                    LinkedTreeMap<String,Object> next = treeMapIterator.next();
                    if (Objects.equals(next.get("api_name"),"marketing_user_account")) {
                        treeMapIterator.remove();
                    }
                }
                List<List<String>> components = (List<List<String>>) it.get("components");
                Iterator<List<String>> listIterator = components.iterator();
                while (listIterator.hasNext()) {
                    List<String> stringList = listIterator.next();
                    if (stringList.size() == 1 && Objects.equals(stringList.get(0),"marketing_user_account")) {
                        listIterator.remove();
                    }
                }

            }
            if (Objects.equals(it.get("api_name"),"marketing_user_account")) {
                iterator.remove();
            }
        }
        //更新layout
        UpdateLayoutArg updateArg = new UpdateLayoutArg();
        updateArg.setLayout_data(GsonUtil.toJson(getLayout));
        objectLayoutService.updateLayout(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), updateArg);
    }
}
