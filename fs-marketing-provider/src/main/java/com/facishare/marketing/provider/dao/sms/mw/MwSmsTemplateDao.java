package com.facishare.marketing.provider.dao.sms.mw;

import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * Created by ranluch on 2019/2/18.
 */
public interface MwSmsTemplateDao {
    @Insert("INSERT INTO mw_sms_template(id, ea, type, name, content, seq_num, remark, status, creator_user_id, creator_name, scene_type, param_detail, channel_type, send_account_id, parent_id, " +
            "tpl_type, relation_api_name, sms_content_param, tpl_version, source, template_obj_id" +
        ") VALUES(" +
        "        #{obj.id},\n" +
        "        #{obj.ea},\n" +
        "        #{obj.type},\n" +
        "        #{obj.name},\n" +
        "        #{obj.content},\n" +
        "        #{obj.seqNum},\n" +
        "        #{obj.remark},\n" +
        "        #{obj.status},\n" +
        "        #{obj.creatorUserId},\n" +
        "        #{obj.creatorName},\n" +
        "        #{obj.sceneType},\n" +
        "        #{obj.paramDetail},\n" +
        "        #{obj.channelType}, \n" +
        "        #{obj.sendAccountId}, \n" +
        "        #{obj.parentId}, \n" +
        "        #{obj.tplType}, \n" +
        "        #{obj.relationApiName}, \n" +
        "        #{obj.smsContentParam}, \n" +
        "        #{obj.tplVersion}, \n" +
        "        #{obj.source}, \n" +
        "        #{obj.templateObjId} \n" +
        "        )")
    boolean addMwTemplate(@Param("obj") MwSmsTemplateEntity templateEntity);

    @Select("SELECT * FROM mw_sms_template WHERE id = #{id}")
    MwSmsTemplateEntity getTemplateById(@Param("id") String id, @Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE template_obj_id = #{templateObjId} and ea = #{ea}")
    MwSmsTemplateEntity getTemplateByObjId(@Param("templateObjId") String templateObjId, @Param("ea") String ea);

    @Select("SELECT ea FROM mw_sms_template WHERE source = 1 and status = 0 and ea != '0' group by ea")
    List<String> getEaBySource(@Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE source = 1 and status = 0 and (ea = '0' or ea = #{ea}) and template_obj_id is null order by create_time desc")
    List<MwSmsTemplateEntity> queryTemplateBySource(@Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE source = 1 and status = 0 and ea = #{ea} and template_obj_id is null order by create_time desc")
    List<MwSmsTemplateEntity> queryCustomizeTemplateBySource(@Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE source = 1 and status = 0 and ea = '0' and template_obj_id is null order by create_time desc")
    List<MwSmsTemplateEntity> queryPreSetTemplateBySource(@Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE source = 1 and status = 0 and ea = '0' and name = #{name} order by create_time desc limit 1")
    MwSmsTemplateEntity queryPreSetTemplateByName(@Param("name") String name, @Param("ea") String ea);

    @Update("UPDATE mw_sms_template SET template_obj_id = #{templateObjId} WHERE id = #{id}")
    int updateTemplateObjId(@Param("templateObjId") String templateObjId, @Param("id") String id, @Param("ea") String ea);

    @Update("update mw_sms_template set \"source\" = 1 where channel_type in (1,3,4,99) and \"source\" is null")
    int refreshSource(@Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE id = #{id} AND status != 3 AND status != 7")
    MwSmsTemplateEntity getVisibleTemplateById(@Param("id") String id, @Param("ea") String ea);

    @Update("UPDATE mw_sms_template SET status = 3 WHERE id = #{id} and ea = #{ea}")
    boolean deleteTemplateById(@Param("id") String id, @Param("ea") String ea);

    @Update("UPDATE mw_sms_template SET status = 3 WHERE ea = #{ea}")
    void deleteTemplateByEa(@Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE ea=#{ea} AND status != 3 AND scene_type == 1 AND seq_num>#{seqNum} ORDER BY seq_num ASC LIMIT 1")
    MwSmsTemplateEntity getForwardSwapTemplate(@Param("ea")String ea, @Param("seqNum")Integer seqNum);

    @Select("SELECT * FROM mw_sms_template WHERE ea=#{ea} AND status != 3 AND scene_type == 1 AND seq_num<#{seqNum} ORDER BY seq_num DESC LIMIT 1")
    MwSmsTemplateEntity getBackwardSwapTemplate(@Param("ea")String ea, @Param("seqNum")Integer seqNum);

    @Update("UPDATE mw_sms_template set seq_num = #{seqNum}, update_time = now() where id = #{id}")
    void updateSeqNumById(@Param("id") String id, @Param("seqNum") Integer seqNum, @Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE ea=#{ea} ORDER BY seq_num DESC LIMIT 1")
    MwSmsTemplateEntity getMaxSeqNumTemplate(@Param("ea")String ea);

    // 组合
    @Select("<script>"
        + " SELECT * FROM mw_sms_template\n"
        + " WHERE (ea = #{ea} or ea = '0') \n"
        + " <if test=\"name != null and name != ''\">\n"
        + "  AND name LIKE  CONCAT('%',#{name},'%')\n"
        + " </if>\n"
        + " <if test=\"status != null\">\n"
        + "  AND status = #{status}\n"
        + " </if>\n"
        + " <if test=\"tplType != null\">\n"
        + "  AND tpl_type = #{tplType}\n"
        + " </if>\n"
        + " <if test=\"sceneType != null\">\n"
        + "  AND ( scene_type = #{sceneType} OR scene_type = #{extraSceneType} ) \n"
        + " </if>\n"
        + "  AND status != 3 \n"
        + "  AND id != '40210d2fbb1640e1882aa698c2708f0c' \n"
        + "  AND (channel_type = 1 or channel_type = 3 or channel_type = 4 or channel_type is null) \n"
        + " ORDER BY create_time desc "
        + "</script>")
    List<MwSmsTemplateEntity> queryTemplateByEa(@Param("name") String name, @Param("ea")String ea, @Param("status") Integer status, @Param("sceneType") Integer sceneType, @Param("extraSceneType") Integer extraSceneType, @Param("tplType") Integer tplType, @Param("page") Page page);

    @Select("<script>"
        + "SELECT * FROM mw_sms_template\n"
        + " WHERE ea = #{ea}\n"
        + " <if test=\"tplType != null\">\n"
        + "  AND tpl_type = #{tplType}\n"
        + " </if>\n"
        + " <if test=\"channelType != null\">\n"
        + "  AND channel_type = #{channelType}\n"
        + " </if>\n"
        + " <if test=\"channelType == null\">\n"
        + "  AND channel_type is not null\n"
        + " </if>\n"
        + " <if test=\"name != null and name != '' \">\n"
        + "  AND name LIKE  CONCAT('%',#{name},'%')\n"
        + " </if>\n"
        + "  AND status = 0 and source = 1 \n"
        + " ORDER BY create_time desc "
        + "</script>")
    List<MwSmsTemplateEntity> listSmsTemplateForChannel(@Param("name") String name, @Param("ea")String ea, @Param("channelType")Integer channelType,  @Param("tplType") Integer tplType, @Param("page") Page page);

    @Update("<script>"
        + "UPDATE mw_sms_template \n"
        + "SET \n"
        + "type = #{obj.type}, \n"
        + "name = #{obj.name}, \n"
        + "content = #{obj.content}, \n"
        + "remark = #{obj.remark}, \n"
        + "status = #{obj.status}, \n"
        + "scene_type = #{obj.sceneType}, \n"
        + "param_detail = #{obj.paramDetail}, \n"
        + "channel_type = #{obj.channelType}, \n"
        + "update_time = now()\n"
        + "WHERE id = #{obj.id} and ea != '0'"
        + "</script>")
    boolean updateSmsTemplate(@Param("obj") MwSmsTemplateEntity templateEntity);

    @Update("<script>"
        + "UPDATE mw_sms_template \n"
        + "SET \n"
        + "name = #{obj.name}, \n"
        + "content = #{obj.content}, \n"
        + "update_time = now()\n"
        + "WHERE id = #{obj.id} and ea != '0'"
        + "</script>")
    boolean updateTemplateNameAndContent(@Param("obj") MwSmsTemplateEntity templateEntity);



    @Update("UPDATE mw_sms_template SET status=#{status}, update_time=now() WHERE id=#{id}")
    boolean updateTemplateStatusById(@Param("status")Integer status, @Param("id")String id, @Param("ea")String ea);

    @Update("UPDATE mw_sms_template SET status=#{status}, reply=#{reply}, update_time=now() WHERE id=#{id}")
    boolean updateTemplateStatusAndReply(@Param("status") Integer status, @Param("reply") String reply, @Param("id") String id, @Param("ea") String ea);

    @Update("UPDATE mw_sms_template SET reply=#{reply}, update_time=now() WHERE id=#{id}")
    boolean updateTemplateReplyById(@Param("reply") String reply, @Param("id") String id, @Param("ea") String ea);

    @Select("SELECT * FROM mw_sms_template WHERE status=#{status} AND create_time > #{limitTime}")
    List<MwSmsTemplateEntity> querySyncTemplateEntity(@Param("status")Integer status, @Param("limitTime")Date limitTime, @Param("ea")String ea);


    @Update("UPDATE mw_sms_template SET tplid=#{tplid} WHERE id=#{id}")
    void setTemplateTplid(@Param("id")String id, @Param("tplid")String tplid, @Param("ea")String ea);

    /** 5.8以后有多个预设模板，废除 */
    @Deprecated
    @Select("SELECT * from mw_sms_template where ea = '0' and scene_type = #{sceneType} order by create_time asc limit 1")
    MwSmsTemplateEntity getPresetTemplate(@Param("sceneType") Integer sceneType, @Param("ea") String ea);

    @Select("SELECT * from mw_sms_template where ea = '0' AND send_account_id = #{sendAccountId}")
    List<MwSmsTemplateEntity> getPresetTemplates(@Param("sendAccountId") String sendAccountId, @Param("ea") String ea);

    @Select("SELECT * from mw_sms_template where content like concat(#{content},'%') and status = 0 limit 1")
    MwSmsTemplateEntity getPresetTemplatesByContent(@Param("content") String content, @Param("ea") String ea);


    @Select("<script> SELECT id, name FROM mw_sms_template WHERE id in <foreach collection = 'ids' item = 'id' open = '(' separator = ',' close = ')'> #{id} </foreach> </script>")
    List<MwSmsTemplateEntity> getTemplateByNameIdList(@Param("ids") List<String> ids, @Param("ea") String ea);

    @Select("<script> " +
                "select * from mw_sms_template where ea = #{ea} and status = 0 and channel_type = 99 " +
                "<if test = 'apiName != null'>" +
                    " and relation_api_name = #{apiName}" +
                "</if>" +
                "<if test = 'filter != null'>" +
                    " AND name LIKE CONCAT('%',#{filter},'%') " +
                "</if>" +
                " ORDER BY create_time desc " +
            "</script>")
    List<MwSmsTemplateEntity> listSmsTemplate(@Param("ea") String ea, @Param("apiName") String apiName, @Param("filter") String filter);

    @Select("<script>"
            + " SELECT * FROM mw_sms_template\n"
            + " WHERE (ea = #{ea} or ea = '0') \n"
            + " <if test=\"name != null and name != ''\">\n"
            + "  AND name LIKE  CONCAT('%',#{name},'%')\n"
            + " </if>\n"
            + " <if test=\"status != null\">\n"
            + "  AND status = #{status}\n"
            + " </if>\n"
            + "<if test=\"sceneTypeList != null and sceneTypeList.size != 0\">"
            + " AND scene_type IN "
            + "<foreach collection = 'sceneTypeList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{sceneTypeList[${index}]}"
            + "</foreach>"
            + "</if>"
            + "  AND status != 3 \n"
            + "  AND id != '40210d2fbb1640e1882aa698c2708f0c' \n"
            + "  AND (channel_type = 1 or channel_type = 3 or channel_type = 4 or channel_type is null) \n"
            + " ORDER BY create_time desc "
            + "</script>")
    List<MwSmsTemplateEntity> queryTemplateBySceneType(@Param("name") String name, @Param("ea")String ea, @Param("status") Integer status,
                                                @Param("sceneTypeList") List<Integer> sceneTypeList,@Param("page") Page page);
}
