package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.provider.entity.mail.MailDomainEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2020/07/02
 **/
public interface MailDomainDAO {

    @Insert("INSERT INTO mail_domail(id, ea, name, status, create_time, update_time) VALUES(#{entity.id},\n"
        + " #{entity.ea}, #{entity.name}, #{entity.status}, now(), now())")
    void insert(@Param("entity") MailDomainEntity entity);

    @Select(" SELECT * FROM mail_domail WHERE ea = #{ea} ORDER BY create_time ASC LIMIT 1")
    MailDomainEntity getUniqueByEa(@Param("ea") String ea);

    @Update("<script>"
        + " UPDATE mail_domail" +
        "        <set>\n" +
        "            <if test=\"name != null\">\n" +
        "                \"name\" = #{name},\n" +
        "            </if>\n" +
        "            <if test=\"status != null\">\n" +
        "                \"status\" = #{status},\n" +
        "            </if>\n" +
        "        update_time = now()"+
        "        </set>\n" +
        "        WHERE id = #{id}" +
        "</script>")
    void updateDomainInfo(@Param("id") String id, @Param("name") String name, @Param("status") Integer status,@Param("ea") String ea);

}
