package com.facishare.marketing.provider.manager.kis;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.OrderResult;
import com.facishare.marketing.api.result.marketingplugin.CheckLicenseListResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.EnvironmentUtil;
import com.facishare.marketing.provider.manager.LicenseManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.open.webhook.accountbind.service.AccountBindService;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseEnvironment;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.webhook.api.dto.AddCrmOrderDto;
import com.facishare.webhook.api.model.CrmOrderDetailInfo;
import com.facishare.webhook.api.model.CrmOrderProductInfo;
import com.facishare.webhook.api.service.VersionRegisterService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;

import java.util.*;

import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/02/22
 **/
@Slf4j
@Service
public class AppVersionManager {

    private List<String> kisVersionList = Lists.newArrayList("kiscloud", "kis", "jdy");
    @Autowired
    private AccountBindService accountBindService;
    @Autowired
    private LicenseManager licenseManager;
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private VersionRegisterService versionRegisterService;

    @Value("${host}")
    private String host;
    @Value("${home.domain}")
    private String homeDomain;

    @Value("${enterprise.environment}")
    private String enterpriseEnvironment;

    @Value("${marketing.vpn.disconnect.enviroment}")
    private String isVpnDisconnectCloud;

    @Value("${marketing.integration.product.id}")
    private String marketingIntegrationAppProductId;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private HttpManager httpManager;

    @ReloadableProperty("clue.addorder")
    private String addorderUrl;
    private String VERSION_APP_ID = "CRM";
    private final List<String>  marketingLicenseCodes = Lists.newArrayList();

    // 100年
    private static final long EXPIRE_TIME = 1000L * 60 * 60 * 24 * 365 * 100;

    public boolean isContainMarketingLicense(String moduleCode){
        if (marketingLicenseCodes.size() == 0){
            for (VersionEnum versionEnum : VersionEnum.values()){
                if (versionEnum.getPriority() <= VersionEnum.DING_FREE_APP.getPriority()) {
                    marketingLicenseCodes.add(versionEnum.getVersion());
                }
            }
        }
        return marketingLicenseCodes.contains(moduleCode);
    }

    /**
     * 获取当前app用户版本
     */
    @FilterLog
    public String getCurrentAppVersion(String ea) {
        List<String> versionList = getAllMarketingVersions(ea);
        return getHighestPriorityVersion(versionList);
    }

    public String getVersionByModuleCodes(Collection<String> moduleCodes){
        if (CollectionUtils.isEmpty(moduleCodes)){
            return null;
        }
        List<String> versionList = Lists.newArrayList();
        for (String moduleCode : moduleCodes){
            if (isContainMarketingLicense(moduleCode)){
                versionList.add(moduleCode);
            }
        }
        return getHighestPriorityVersion(versionList);
    }

    /**
     * 获取所有的营销通版本
     *
     * @param ea                   企业账号
     * @return versionList         该企业所有的营销通版本信息
     */
    public List<String> getAllMarketingVersions(String ea) {
        if (StringUtils.isEmpty(ea)) {
            return new ArrayList<String>();
        }
        List<String> versionList = Lists.newArrayList();
        List<ProductVersionPojo> list = licenseManager.listProductVersion(ea, VERSION_APP_ID);
        for (ProductVersionPojo productVersionPojo : list) {
            String currentVersion = productVersionPojo.getCurrentVersion().trim();
            Integer versionPriority = VersionEnum.fromVersion(currentVersion);
            if (null != versionPriority && versionPriority <= VersionEnum.DING_FREE_APP.getPriority() && !versionList.contains(currentVersion)){
                versionList.add(currentVersion);
            }
        }

        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (ModuleInfoPojo moduleResult : moduleResults) {
            String currentVersion = moduleResult.getModuleCode().trim();
            Integer versionPriority = VersionEnum.fromVersion(currentVersion);
            if (null != versionPriority && versionPriority <= VersionEnum.DING_FREE_APP.getPriority() && !versionList.contains(currentVersion)){
                versionList.add(currentVersion);
            }
        }
        return versionList;
    }

    public Set<String> getAllVersion(String ea){
        List<ProductVersionPojo> list = licenseManager.listProductVersion(ea, VERSION_APP_ID);
        Set<String> versions = new HashSet<>();

        list.forEach(productVersionPojo ->{
            String currentVersion = productVersionPojo.getCurrentVersion().trim();
            versions.add(currentVersion);
        });

        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        moduleResults.forEach(moduleInfoPojo ->{
            String currentVersion = moduleInfoPojo.getModuleCode().trim();
            versions.add(currentVersion);
        });

        return versions;
    }

    public Boolean getDingVersion(String ea) {
        Boolean isDingVersion = false;
        if (ea == null) {
            return null;
        }

        List<ProductVersionPojo> list = licenseManager.listProductVersion(ea, VERSION_APP_ID);
        List<String> versionList = Lists.newArrayList();
        for (ProductVersionPojo productVersionPojo : list) {
            String currentVersion = productVersionPojo.getCurrentVersion().trim();
            if (null != VersionEnum.fromVersion(currentVersion) && !versionList.contains(currentVersion)){
                versionList.add(currentVersion);
            }
        }

        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (ModuleInfoPojo moduleResult : moduleResults) {
            String currentVersion = moduleResult.getModuleCode().trim();
            if (null != VersionEnum.fromVersion(currentVersion) && !versionList.contains(currentVersion)){
                versionList.add(currentVersion);
            }
        }
        //获取最高优先级版本
        String highestPriorityVersion = getHighestPriorityVersion(versionList);
        if (VersionEnum.STAN_DINGTALK_30_APP.getVersion().equals(highestPriorityVersion) || VersionEnum.STAN_DINGTALK_100_APP.getVersion().equals(highestPriorityVersion)
                || VersionEnum.STAN_DINGTALK_500_APP.getVersion().equals(highestPriorityVersion) || VersionEnum.PRO_DINGTALK_30_APP.getVersion().equals(highestPriorityVersion)
                || VersionEnum.PRO_DINGTALK_100_APP.getVersion().equals(highestPriorityVersion) || VersionEnum.PRO_DINGTALK_500_APP.getVersion().equals(highestPriorityVersion)
                || VersionEnum.DING_FREE_APP.getVersion().equals(highestPriorityVersion)) {
            isDingVersion = true;
        }

        if (!isDingVersion){
            GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
            enterpriseDataArg.setEnterpriseAccount(ea);
            enterpriseDataArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
            Integer env = enterpriseDataResult.getEnterpriseData().getEnv();
            if (env == EnterpriseEnvironment.ALI.getEnv()) {
                isDingVersion = true;
            }
        }
        return isDingVersion;
    }

    /**
     * 按优先级排序
     * @param versionList   版本list
     * @return              list中的最高优先级version
     */
    public static String getHighestPriorityVersion(List<String> versionList) {
        if (CollectionUtils.isEmpty(versionList)) {
            return null;
        }

        List<Integer> priorityList = Lists.newArrayList();
        for (String version : versionList) {
            priorityList.add(VersionEnum.fromVersion(version));
        }
        Integer[] array = priorityList.toArray(new Integer[0]);
        Arrays.sort(array);
        return VersionEnum.fromPriority(array[0]);
    }

    public boolean isEnableWxThirdPlatformBind(String ea) {
        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (ModuleInfoPojo moduleResult : moduleResults) {
            String currentVersion = moduleResult.getModuleCode().trim();
            if (currentVersion.equals(VersionEnum.THIRD_PLATFORM_BIND.getVersion())) {
                return true;
            }
        }
        return false;
    }

    public boolean isEnterpriseLibraryEnabled(String ea) {
        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (ModuleInfoPojo moduleResult : moduleResults) {
            String currentVersion = moduleResult.getModuleCode().trim();
            if (currentVersion.equals(VersionEnum.ENTERPRISE_LIBRARY_APP.getVersion())) {
                return true;
            }
        }
        return false;
    }

    /**
     * KIS 灰度企业(在KIS版未上线前暂用)
     */
    public boolean isKisVersion(String ea) {
        /*List<String> kisVersionEa = GsonUtil.getGson().fromJson(KIS_VERSION_EA, ArrayList.class);
        return StringUtils.isNotBlank(ea) && CollectionUtils.isNotEmpty(kisVersionEa) && kisVersionEa.stream().anyMatch(data -> data.equals(ea));*/

        if (StringUtils.isBlank(ea)) {
            return false;
        }

        if (EnvironmentUtil.isOuterCloud(homeDomain)){
            return false;
        }

        List<String> sourceByEa = accountBindService.getSourceByEa(ea);
        log.info("isKisVersion.getSourceByEa ea:{} source:{}", ea, sourceByEa);
        boolean isKis = false;
        if (CollectionUtils.isNotEmpty(sourceByEa)) {
            for (String version : sourceByEa){
                if (kisVersionList.contains(version)){
                    isKis = true;
                    break;
                }
            }
        }

        return isKis;
    }

    public Boolean checkChuangketieOrder(String ea) {
        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (ModuleInfoPojo moduleResult : moduleResults) {
            String currentVersion = moduleResult.getModuleCode().trim();
            if (currentVersion.equals(VersionEnum.CHUANG_KE_TIE_APP.getVersion())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否购买了营销一体化产品
     * @param ea
     * @return
     */
    public Boolean checkSpecialVersionOrders(String ea, VersionEnum versionEnum){
        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (ModuleInfoPojo moduleResult : moduleResults) {
            String currentVersion = moduleResult.getModuleCode().trim();
            if (currentVersion.equals(versionEnum.getVersion())) {
                return true;
            }
        }
        return false;
    }

    public Boolean checkSpecialVersionOrders(String ea, String moduleCode){
        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (ModuleInfoPojo moduleResult : moduleResults) {
            String currentVersion = moduleResult.getModuleCode().trim();
            if (currentVersion.equals(moduleCode)) {
                return true;
            }
        }
        return false;
    }

    public List<CheckLicenseListResult.LicenseConfig> checkLicenseConfigs(String ea, List<String> moduleCodes){
        List<CheckLicenseListResult.LicenseConfig> result = Lists.newArrayList();
        List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, VERSION_APP_ID);
        for (String moduleCode : moduleCodes) {
            CheckLicenseListResult.LicenseConfig licenseConfig = new CheckLicenseListResult.LicenseConfig();
            licenseConfig.setModuleCode(moduleCode);
            licenseConfig.setStatus(false);
            for (ModuleInfoPojo moduleResult : moduleResults) {
                String currentVersion = moduleResult.getModuleCode().trim();
                if (Objects.equals(currentVersion, moduleCode)) {
                    licenseConfig.setStatus(true);
                    break;
                }
            }
            result.add(licenseConfig);
        }
        return result;
    }

    /**
     * 判断企业是否为当前云的企业，纷享云会收到其他云的MQ
     * @param ea
     * @return
     */
    public boolean isCurrentCloud(String ea){
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(ea);
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        if (result == null || result.getEnterpriseData() == null || result.getEnterpriseData().getEnv() == null){
            log.error("isCurrentCloud result is null or env is null, ea:{} result:{}", ea, result);
            return false;
        }

        String env = String.valueOf(result.getEnterpriseData().getEnv());
        return  StringUtils.equals(env, enterpriseEnvironment) ? true : false;
    }

    public String getCurrentCloudEnv(String ea) {
        return enterpriseEnvironment;
    }

    /**
     * 判断当前环境是否是纷享云环境
     * @return
     */
    public boolean isFxCloud(){
        String env = String.valueOf(EnterpriseEnvironment.FXIAOKE.getEnv());
        if (StringUtils.equals(env, enterpriseEnvironment)) {
            return true;
        }
        return false;
    }

    /**
     * 判断当前环境是否为亚马逊云
     * @return
     */
    @FilterLog
    public boolean isAwsCloud(){
        String env = String.valueOf(EnterpriseEnvironment.AWS.getEnv());
        if (StringUtils.equals(env, enterpriseEnvironment)) {
            return true;
        }
        return false;
    }

    /**
     * 判断当前环境是否为何氏眼科云
     * @return
     */
    public boolean isHsykCloud(){
        String env = "16";
        if (StringUtils.equals(env, enterpriseEnvironment)) {
            return true;
        }
        return false;
    }

    // 下单营销一体化
    public boolean addMarketingIntegrationAppOrder(String ea) {
        try {
            if (checkSpecialVersionOrders(ea, VersionEnum.MARKETING_CRM_INTEGRATING)) {
                log.info("账号:[{}]已经开通营销一体化", ea);
                return false;
            }
            AddCrmOrderDto.Argument cmrOrderArgument = new AddCrmOrderDto.Argument();

            CrmOrderDetailInfo crmOrderDetailInfo = new CrmOrderDetailInfo();
            crmOrderDetailInfo.setEnterpriseAccount(ea);
            crmOrderDetailInfo.setOrderTime(System.currentTimeMillis());
            //1-标准（购买），2-试用，3-赠送
            crmOrderDetailInfo.setOrderTpye(1);

            cmrOrderArgument.setCrmOrderDetailInfo(crmOrderDetailInfo);

            CrmOrderProductInfo crmOrderProductInfo = new CrmOrderProductInfo();
            crmOrderProductInfo.setOrderAmount("0");
            crmOrderProductInfo.setQuantity(1);
            crmOrderProductInfo.setAllResourceCount(10000);
            crmOrderProductInfo.setBeginTime(System.currentTimeMillis());
            crmOrderProductInfo.setEndTime(System.currentTimeMillis() + EXPIRE_TIME);
            crmOrderProductInfo.setProductId(marketingIntegrationAppProductId);

            cmrOrderArgument.setCrmOrderProductInfo(crmOrderProductInfo);

//            AddCrmOrderDto.Result result = versionRegisterService.addCrmOrder(cmrOrderArgument);
            OrderResult result = getOrderResult(cmrOrderArgument);
            log.info("营销一体化下单,arg:[{}], result:[{}]", cmrOrderArgument, result);
            return true;
        } catch (Exception e) {
            log.error("营销一体化下单失败:", e);
            return false;
        }

    }

    public OrderResult getOrderResult(AddCrmOrderDto.Argument orderArg) {
        if(Objects.isNull(orderArg)){
            return null;
        }
        String url  = addorderUrl +"/versionRegisterService/addCrmOrder";
        OrderResult result = httpManager.executePostHttp(orderArg, url, new TypeToken<OrderResult>() {});
        return result;
    }

    @FilterLog
    public boolean isInternationalProd(){
        String hwsPublicProdEnterpriseEnviroment = "9";  //亚马逊云的环境id=9  variables_endpoint中定义了enterprise_environment
      //  String kscEnvironment = "4"; //华为云香港环境id=4
        if (enterpriseEnvironment.equals(hwsPublicProdEnterpriseEnviroment)) {
            return true;
        }

        return false;
    }

    public boolean isVpnDisconnectCloud(){
        String isVpnDisconnectCloudFlag = "YES";  //YES:是vpn断网云，NO:不是vpn断网云
        if (StringUtils.isBlank(isVpnDisconnectCloud) || !StringUtils.equals(isVpnDisconnectCloudFlag, isVpnDisconnectCloud)) {
            return false;
        }

        return true;
    }
}
