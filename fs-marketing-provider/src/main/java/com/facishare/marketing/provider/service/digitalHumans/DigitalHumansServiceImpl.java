package com.facishare.marketing.provider.service.digitalHumans;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.digitalHumans.CommitSampleArg;
import com.facishare.marketing.api.arg.digitalHumans.DigitalHumansArg;
import com.facishare.marketing.api.arg.digitalHumans.FilePathArg.AliOSSAuthInfo;
import com.facishare.marketing.api.arg.digitalHumans.GrayArg;
import com.facishare.marketing.api.arg.digitalHumans.SignatureArg;
import com.facishare.marketing.api.result.AliOSSAuthInfoResult;
import com.facishare.marketing.api.result.DigitalHumansResult;
import com.facishare.marketing.api.service.digitalHumans.DigitalHumansService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.DBRoutUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.FSBindDAO;
import com.facishare.marketing.provider.dao.digitalHumans.DigitalHumansDAO;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.digitalHumans.DigitalHumansEntity;
import com.facishare.marketing.common.enums.digitalHumans.DigitalHumansStatusEnum;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dto.digitalHumans.QualityAndTringCheckData;
import com.facishare.marketing.provider.dto.digitalHumans.StartTrainingData;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.digitalHumans.DigitalHumansManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.enums.digitalHumans.DigitalHumansStatusEnum.*;

@Slf4j
@Service("digitalHumansService")
public class DigitalHumansServiceImpl implements DigitalHumansService {

    @Autowired
    private DigitalHumansManager digitalHumansManager;

    @Autowired
    private DigitalHumansDAO digitalHumansDAO;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private FSBindDAO fsBindDAO;

    @ReloadableProperty("digital_humans_gray")
    private String digitalHumansGraySetting;
    @Autowired
    private FsBindManager fsBindManager;

    //提交样本接口
    @Override
    public Result<String> commitSample(CommitSampleArg arg) {
        if(!digitalHumansManager.getDigitalHumansSetting(arg.getEa())){
            return Result.newSuccess();
        }
        //获取视频地址
        String trainingVideoUrl = digitalHumansManager.getAliOssFileUrl(arg.getTrainingVideoUrl(), arg.getEa());
        String authVideoUrl = digitalHumansManager.getAliOssFileUrl(arg.getAuthVideoUrl(), arg.getEa());
        if(StringUtils.isBlank(trainingVideoUrl) || StringUtils.isBlank(authVideoUrl)){
            return Result.newSuccess();
        }
        HashMap<String, String> map = Maps.newHashMap();
        map.put("vhModelType", arg.getVhModelType());
        map.put("trainingVideoUrl", trainingVideoUrl);
        map.put("audioText", arg.getAudioText());
        map.put("authVideoUrl", authVideoUrl);
        String bizId = digitalHumansManager.submitZeroSampleTask(map,arg.getEa());
        if (StringUtils.isBlank(bizId)) {
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        //插入数字人数据表
        DigitalHumansEntity humans = new DigitalHumansEntity();
        humans.setId(UUIDUtil.getUUID());
        humans.setStatus(QUALITY_CHECK_IN_PROGRESS.getStatus());
        humans.setFsUserId(arg.getUserId());
        humans.setEa(arg.getEa());
        humans.setAuthVideoUrl(authVideoUrl);
        humans.setTrainingVideoUrl(trainingVideoUrl);
        humans.setAudioText(arg.getAudioText());
        humans.setBizId(bizId);
        humans.setVhModelType(arg.getVhModelType());
        humans.setName(arg.getName());
        digitalHumansDAO.insertOrUpdate(humans);
        return Result.newSuccess(bizId);
    }

    @Override
    public Result<String> getSignature(SignatureArg arg){
        FSBindEntity fsBindEntity = fsBindManager.getFSBindByUid(arg.getCardUid());
        if(fsBindEntity == null){
            return Result.newSuccess();
        }
        if(!digitalHumansManager.getDigitalHumansSetting(fsBindEntity.getFsEa())){
            return Result.newSuccess();
        }
        return Result.newSuccess(digitalHumansManager.getSignature(fsBindEntity.getFsEa()));
    }

    @Override
    public Result<DigitalHumansResult> getUserDigitalHumans(DigitalHumansArg arg) {
        FSBindEntity fsBindEntity = fsBindManager.getFSBindByUid(arg.getCardUid());
        if(fsBindEntity == null){
            return Result.newSuccess();
        }
        DigitalHumansEntity humans = digitalHumansDAO.getByEaAndFsUserId(fsBindEntity.getFsEa(), fsBindEntity.getFsUserId());
        if (humans == null) {
            return Result.newSuccess();
        }
        DigitalHumansResult result = new DigitalHumansResult();
        result.setBizId(humans.getUserBizId());
        result.setStatus(humans.getStatus());
        result.setMessage(humans.getMessage());
        return Result.newSuccess(result);
    }

    @Override
    public Result<AliOSSAuthInfoResult> queryAuthInfo(AliOSSAuthInfo arg) {
        if(!digitalHumansManager.getDigitalHumansSetting(arg.getEa())){
            return Result.newSuccess();
        }
        String filePath = digitalHumansManager.createFilePath(arg);
        if (StringUtils.isBlank(filePath)) {
            return Result.newSuccess();
        }
        AliOSSAuthInfoResult aliOssFileHost = digitalHumansManager.getAliOssFileHost(filePath, arg.getEa());
        return Result.newSuccess(aliOssFileHost);
    }

    @Override
    public void RefreshDigitalHumans(String ea) {
        ThreadPoolUtils.execute(() -> doRefresh(ea), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    @Override
    public Result<Boolean> checkUserPermission(DigitalHumansArg arg) {
        FSBindEntity fsBindEntity = fsBindManager.getFSBindByUid(arg.getCardUid());
        if(fsBindEntity == null){
            return Result.newSuccess(false);
        }
        if(!digitalHumansManager.getDigitalHumansSetting(fsBindEntity.getFsEa())){
            return Result.newSuccess(false);
        }
        List<GrayArg> grayArgs = JSON.parseArray(digitalHumansGraySetting, GrayArg.class);
        if(CollectionUtils.isNotEmpty(grayArgs)){
            Map<String, List<Integer>> listMap = grayArgs.stream().collect(Collectors.toMap(GrayArg::getEa, GrayArg::getFsUserIds, (k1, k2) -> k1));
            if(listMap.get(fsBindEntity.getFsEa()) == null || !listMap.get(fsBindEntity.getFsEa()).contains(fsBindEntity.getFsUserId())){
                return Result.newSuccess(false);
            }
        }
        return Result.newSuccess(true);
    }

    public void doRefresh(String ea) {
        List<DigitalHumansEntity> digitalHumansEntities = digitalHumansDAO.queryByEaAndStatusList(ea, DigitalHumansStatusEnum.listNeedRefreshStatus());
        for (DigitalHumansEntity digitalHuman : digitalHumansEntities) {
            try {
                switch (Objects.requireNonNull(valueOfStatus(digitalHuman.getStatus()))) {

                    case QUALITY_CHECK_IN_PROGRESS:
                        parseQualityCheckResult(digitalHuman);
                        break;

                    case TRAINING_IN_PROGRESS:
                        parseTrainingResult(digitalHuman);
                        break;

                    default:
                        break;
                }
            } catch (Exception e) {
                log.warn("fail RefreshDigitalHumansStatusJob message exception! args:{}", digitalHuman.getBizId(), e);
            }
        }
    }

    /**
     * 质检中轮询
     * @param digitalHuman
     */
    private void parseQualityCheckResult(DigitalHumansEntity digitalHuman) {
        QualityAndTringCheckData qualityAndTringCheckData = digitalHumansManager.queryQualityCheckResult(digitalHuman.getBizId(),digitalHuman.getEa());
        if(Objects.isNull(qualityAndTringCheckData) || qualityAndTringCheckData.getStatus() == null){
            return;
        }
        String status = valueOfQualityCheck(qualityAndTringCheckData.getStatus()).getStatus();
        if(StringUtils.isBlank(status)){
            return;
        }
        //更新质检结果
        String message = qualityAndTringCheckData.getMessage();
        if(!status.equals(QUALITY_CHECK_FAILED.getStatus()) && qualityAndTringCheckData.getProgress() != null){
            message = String.valueOf(qualityAndTringCheckData.getProgress());
        }
        digitalHumansDAO.updateStatusAndUserBizIdByEaAndBizId(digitalHuman.getEa(), digitalHuman.getBizId(), status, message,null);
        //提交训练
        if(status.equals(QUALITY_CHECK_COMPLETED.getStatus())){
            HashMap<String, String> map = Maps.newHashMap();
            map.put("bizId", digitalHuman.getBizId());
            map.put("name", digitalHuman.getName());
            StartTrainingData training = digitalHumansManager.startTraining(map, digitalHuman.getEa());
            String trainingStatus = Objects.isNull(training) || training.getData() == null ? TRAINING_FAILED.getStatus() : TRAINING_IN_PROGRESS.getStatus();
            String trainingMessage = Objects.isNull(training) ? "" : Objects.isNull(training.getData()) ? training.getMessage() : String.valueOf(training.getData().getEstimateMinutes());
            digitalHumansDAO.updateStatusAndUserBizIdByEaAndBizId(digitalHuman.getEa(), digitalHuman.getBizId(), trainingStatus,trainingMessage,null );
        }
    }

    /**
     * 训练中轮询
     * @param digitalHuman
     */

    private void parseTrainingResult(DigitalHumansEntity digitalHuman) {
        QualityAndTringCheckData qualityAndTringCheckData = digitalHumansManager.queryTrainingResult(digitalHuman.getBizId(),digitalHuman.getEa());
        if(Objects.isNull(qualityAndTringCheckData) || qualityAndTringCheckData.getStatus() == null){
            return;
        }
        String status = valueOfTraining(qualityAndTringCheckData.getStatus()).getStatus();
        if(StringUtils.isBlank(status)){
            return;
        }
        //更新训练结果
        String message = qualityAndTringCheckData.getMessage();
        if(!status.equals(TRAINING_FAILED.getStatus()) && qualityAndTringCheckData.getProgress() != null){
            message = String.valueOf(qualityAndTringCheckData.getProgress());
        }
        digitalHumansDAO.updateStatusAndUserBizIdByEaAndBizId(digitalHuman.getEa(), digitalHuman.getBizId(), status, message,qualityAndTringCheckData.getBizId());
        //上线入驻
        if(status.equals(TRAINING_COMPLETED.getStatus())){
            Boolean b = digitalHumansManager.finishTraining(digitalHuman.getBizId(),digitalHuman.getEa());
            String trainingStatus = BooleanUtils.isTrue(b) ? CHECK_COMPLETED.getStatus() : CHECK_FAILED.getStatus();
            digitalHumansDAO.updateStatusByEaAndBizId(digitalHuman.getEa(), digitalHuman.getBizId(), trainingStatus, null);
        }
    }

}
