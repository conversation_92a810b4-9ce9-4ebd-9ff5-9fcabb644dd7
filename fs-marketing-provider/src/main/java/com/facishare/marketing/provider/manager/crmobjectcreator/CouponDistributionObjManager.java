/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.dao.marketingplugin.SendDealerCouponRecordDAO;
import com.facishare.marketing.provider.dao.marketingplugin.WeChatCouponDAO;
import com.facishare.marketing.provider.entity.marketingplugin.SendDealerCouponRecordEntity;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.facishare.marketing.provider.manager.ObjectServiceManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.FieldDescribeService;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.util.ObjDescribeUtil;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionBulkDeleteResult;
import com.fxiaoke.crmrestapi.result.ActionBulkInvalidResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CouponDistributionObjManager extends AbstractObjManager {

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MarketingPluginConfigDAO  marketingPluginConfigDAO;

    @Autowired
    private SendDealerCouponRecordDAO sendDealerCouponRecordDAO;

    @Autowired
    private WeChatCouponDAO weChatCouponDAO;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ObjectServiceManager objectServiceManager;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Autowired
    private FieldDescribeService fieldDescribeService;

    @Override
    public String getApiName() {
        return CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName();
    }

    @Override
    public String getJsonData() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/coupon_distribution_json_data.json");
    }

    @Override
    public String getJsonLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/coupon_distribution_json_layout.json");
    }

    @Override
    public String getJsonListLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/coupon_distribution_json_list_layout.json");
    }

    public String createCouponDistributionObj(String ea, Map<String,Object> params){
        Result<ActionAddResult> addResult = crmV2Manager.addObjectData(ea, CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(), -10000, params);
        if (addResult.isSuccess() && addResult.getData() != null) {
            return addResult.getData().getObjectData().getId();
        }
        return null;
    }

    public Map<String,Object> createDataMapByEntity(WechatCouponEntity couponEntity, SendDealerCouponRecordEntity recordEntity,Boolean isPublic){
        Map<String,Object> dataMap = Maps.newHashMap();
        dataMap.put("coupon_id",couponEntity.getCouponId());
        dataMap.put("down_stream_ea",recordEntity.getDownStreamEa());
        dataMap.put("down_stream_tenant_id",recordEntity.getDownStreamTenantId());
        dataMap.put("dealer_count",recordEntity.getDealerCount());
        dataMap.put("add_coupon_activity",recordEntity.getAddCouponActivity());
        dataMap.put("down_stream_name",recordEntity.getDownStreamName());
        if (isPublic) {
            int ei = eieaConverter.enterpriseAccountToId(couponEntity.getEa());
            dataMap.put("create_enterprise",Lists.newArrayList(String.valueOf(ei)));
        }
        return dataMap;
    }

    public void updateCouponDistributionObjStatus(String ea, String couponId, String erOuterTenantId) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setSelectFields(Lists.newArrayList("_id"));
        filterArg.setObjectAPIName(getApiName());
        PaasQueryArg query = new PaasQueryArg(0,1);
        query.addFilter("coupon_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList(couponId));
        query.addFilter("down_stream_tenant_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList(erOuterTenantId));
        filterArg.setQuery(query);
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, -10000, filterArg, null, 1);
        if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            ObjectData objectData = objectDataInnerPage.getDataList().get(0);
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("_id",objectData.getId());
            dataMap.put("add_coupon_activity",1); //已参与状态
            crmV2Manager.editObjectData(ea,getApiName(),dataMap);
        }
    }

    public void handleCouponDistributionObjOldEnterprise(String ea){
        List<String> eas = marketingPluginConfigDAO.queryEaByPluginType(MarketingPluginTypeEnum.SUYUAN_COUPON.getType(),ea);
        for (String s : eas) {
            this.getOrCreateObjDescribe(s);
            log.info("handleCouponDistributionObjOldEnterprise,ea:{}",s);
        }
    }

    public void handleCouponDistributionObj(String ea){
        this.getOrCreateObjDescribe(ea);
        log.info("handleCouponDistributionObj,ea:{}",ea);
    }

    /**
     * 处理之前的下发记录数据
     * @param ea
     */
    public void handleCouponDistributionData(String ea) {
        Date limitTime = DateUtil.parse("2023-08-25 23:00:00");
        List<SendDealerCouponRecordEntity> dealerCouponRecordEntities = sendDealerCouponRecordDAO.queryListByCreateTime(limitTime, ea);
        if (CollectionUtils.isEmpty(dealerCouponRecordEntities)) {
            log.info("handleCouponDistributionData is not data");
        }
        Map<String, WechatCouponEntity> couponEntityMap = Maps.newHashMap();
        Set<String> couponIds = dealerCouponRecordEntities.stream().map(SendDealerCouponRecordEntity::getCouponId).collect(Collectors.toSet());
        List<WechatCouponEntity> wechatCouponEntities = weChatCouponDAO.getByIds(new ArrayList<>(couponIds), ea);
        if (CollectionUtils.isNotEmpty(wechatCouponEntities)) {
           couponEntityMap = wechatCouponEntities.stream().collect(Collectors.toMap(WechatCouponEntity::getId, o -> o));
        }
        for (SendDealerCouponRecordEntity dealerCouponRecordEntity : dealerCouponRecordEntities) {
            WechatCouponEntity wechatCouponEntity = couponEntityMap.get(dealerCouponRecordEntity.getCouponId());
            Map<String, Object> dataMap = this.createDataMapByEntity(wechatCouponEntity,dealerCouponRecordEntity,true);
            this.createCouponDistributionObj(wechatCouponEntity.getEa(),dataMap);
        }
    }



    public void tryUpdateFieldDescribe(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        ObjectDescribe objectDescribe = getCouponDistributionObjDescribe(ea);
        if (objectDescribe == null) {
            log.warn("Ea:{} have not CouponDistributionObj", ea);
            return;
        }
        //down_stream_name
        if (!objectDescribe.getFields().containsKey("down_stream_name")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"CouponDistributionObj\",\"default_is_expression\":false,\"is_index\":true,\"is_active\":true,\"pattern\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"下游企业名称\",\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"api_name\":\"down_stream_name\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"api_name\":0,\"is_unique\":0,\"default_value\":0,\"label\":0,\"help_text\":0}},\"help_text\":\"\",\"max_length\":500,\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"down_stream_name\",\"label\":\"默认布局\",\"is_default\":true}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }

    }

    public ObjectDescribe getCouponDistributionObjDescribe(String fsEa) {
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        try {
            Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName());
            if (getDescribeResultResult.isSuccess()) {
                return getDescribeResultResult.getData().getDescribe();
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    public void deleteCouponDistributionObj(String ea, String objectId) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName());
        filterArg.setSelectFields(Lists.newArrayList("down_stream_ea","_id","create_time","add_coupon_activity"));
        PaasQueryArg query = new PaasQueryArg(0,1);
        query.addFilter("coupon_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList(objectId));
        filterArg.setQuery(query);
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, filterArg);
        if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
            log.warn("deleteCouponDistributionObj objectDataInnerPage is null or empty,ea:{},objectId:{}",ea,objectId);
            return;
        }
        List<ObjectData> dataList = objectDataInnerPage.getDataList();
        //按照 down_stream_ea 进行分组
        Map<String, List<ObjectData>> groupMap = dataList.stream().collect(Collectors.groupingBy(o -> o.getString("down_stream_ea")));
        //遍历分组, 将同一down_stream_ea的记录过滤掉创建时间最早的那条,其他数据进行删除
        for (Map.Entry<String, List<ObjectData>> entry : groupMap.entrySet()) {
            List<ObjectData> objectDataList = entry.getValue();
            if (objectDataList.size() > 1) {
                //如果同时存在add_coupon_activity = 0 和 add_coupon_activity = 1 的的数据, 则只删除 add_coupon_activity = 0 的数据
                List<ObjectData> notAddCouponActivityList = objectDataList.stream().filter(o -> o.getInt("add_coupon_activity") == 0).collect(Collectors.toList());
                List<ObjectData> addCouponActivityList = objectDataList.stream().filter(o -> o.getInt("add_coupon_activity") == 1).collect(Collectors.toList());
                if (!notAddCouponActivityList.isEmpty() && !addCouponActivityList.isEmpty()) {
                    List<String> ids = notAddCouponActivityList.stream().map(ObjectData::getId).collect(Collectors.toList());
                    Result<ActionBulkInvalidResult> actionBulkInvalidResultResult = crmV2Manager.bulkInvalidWithResult(ea, -10000, CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(), ids);
                    if (actionBulkInvalidResultResult.isSuccess()) {
                        Result<ActionBulkDeleteResult> actionBulkDeleteResultResult = crmV2Manager.bulkDelete(ea, CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(), ids);
                        log.info("deleteCouponDistributionObj notAddCouponActivityList,ea:{},objectIds:{},result:{}",ea,ids,actionBulkDeleteResultResult);
                    }
                } else {
                    Optional<ObjectData> objectDataOptional = objectDataList.stream().min(Comparator.comparing(ObjectData::getCreateTime));
                    if (objectDataOptional.isPresent()) {
                        ObjectData minData = objectDataOptional.get();
                        List<String> ids = objectDataList.stream().map(ObjectData::getId).filter(id -> !id.equals(minData.getId())).collect(Collectors.toList());
                        Result<ActionBulkInvalidResult> actionBulkInvalidResultResult = crmV2Manager.bulkInvalidWithResult(ea, -10000, CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(), ids);
                        if (actionBulkInvalidResultResult.isSuccess()) {
                            Result<ActionBulkDeleteResult> actionBulkDeleteResultResult = crmV2Manager.bulkDelete(ea, CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(), Lists.newArrayList(ids));
                            log.info("deleteCouponDistributionObj,ea:{},objectIds:{},result:{}",ea,ids,actionBulkDeleteResultResult);
                        }
                    } else {
                        log.warn("deleteCouponDistributionObj objectDataOptional is not present,ea:{},objectDataList:{}",ea, GsonUtil.toJson(objectDataList));
                    }
                }
            }
        }
    }
}
