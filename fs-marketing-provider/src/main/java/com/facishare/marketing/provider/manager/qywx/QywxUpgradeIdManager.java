package com.facishare.marketing.provider.manager.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.TagModelSceneConstants;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.dao.user.UserRelationDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.qywx.*;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.innerData.qywx.ChangeContactEventMsg;
import com.facishare.marketing.provider.innerData.qywx.ChangeExternalChatEventMsg;
import com.facishare.marketing.provider.innerData.qywx.ChangeExternalContactEventMsg;
import com.facishare.marketing.provider.innerData.qywx.SubscribeEventMsg;
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Wheres;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class QywxUpgradeIdManager {

    // 这是之前营销动态升级的表，现在已经不用了，这次升级企微的ID，复用这张表吧，反正也就跑几次
    @Autowired
    private UserMarketingActionStatisticUpgradeRecordDao userMarketingActionStatisticUpgradeRecordDao;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private QywxIdUpgradeRecordDAO qywxIdUpgradeRecordDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;

    @Autowired
    private QywxGroupSendResultDAO qywxGroupSendResultDAO;

    @Autowired
    private QywxGroupSendTaskDAO qywxGroupSendTaskDAO;

    @Autowired
    private QYWXMomentTaskDAO qywxMomentTaskDAO;

    @Autowired
    private UserManager userManager;

    @Autowired
    private QywxTaskDao qywxTaskDao;

    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;

    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private QywxWelcomeMsgDAO qywxWelcomeMsgDAO;

    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;

    @Autowired
    private QYWXMomentSendResultDaO qywxMomentSendResultDaO;

    @Autowired
    private TriggerSnapshotDao triggerSnapshotDao;

    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private QywxGroupSendGroupResultDAO qywxGroupSendGroupResultDAO;

    @Autowired
    private QywxIdUpgradingCallbackTempDataDAO qywxIdUpgradingCallbackTempDataDAO;

    @Autowired
    private QywxEventCallBackManager qywxEventCallBackManager;

    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private QywxCorpEaMappingDAO qywxCorpEaMappingDAO;

    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private TagModelDao tagModelDao;

    @Autowired
    private UserRelationDAO userRelationDao;

    private static final String EXTERNAL_USER_ID_TYPE = "external_user_id";

    private static final String EMPLOYEE_ID_TYPE = "employee_id";

    private static final String CORP_ID_TYPE = "corp_id";

    private static final String TAG_ID_TYPE = "tag_id";

    private static final String EXTERNAL_USER_OBJ_DATA_SOURCE = "WechatWorkExternalUserObj";

    private static final String QYWX_USER_OBJ_DATA_SOURCE = "WechatEmployeeObj";

    private static final String QYWX_MOMENT_TASK_SOURCE = "qywx_moment_task";

    private static final String SUCCESS = "success";
    private static final String FAIL = "fail";
    private static final String UPGRADING = "upgrading";

    public static final int UPGRADE_FAIL_STATUS = -1;
    public static final int UPGRADE_UPGRADING_STATUS = 0;
    public static final int UPGRADE_SUCCESS_STATUS = 1;

    private static final String noticeUpgradeSuccessKey = "marketing-qywx-upgrade-%s";

    @ReloadableProperty("qywx_id_upgrade_handle_moment")
    private String qywxIdUpgradeHandleMoment;



    //不要加事务了 @Transactional(rollbackFor = Exception.class)
    public boolean upgradeId(String ea) {
        String[] arr = ea.split(",");
        boolean handleMomentResult = true;
        if (arr.length > 1) {
            ea = arr[0];
            handleMomentResult = Boolean.parseBoolean(arr[1]);
        }
        if (qywxManager.isNewInstallAgentApp(ea)) {
            return false;
        }
        log.info("开始升级企业微信ID，ea:{}", ea);
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null) {
            log.error("升级企业微信ID失败，找不到企业微信配置，ea:{}", ea);
            return false;
        }
        UserMarketingActionStatisticUpgradeRecord upgradeRecord = userMarketingActionStatisticUpgradeRecordDao.getByEa(ea);
        if (upgradeRecord == null) {
            //插入升级记录 状态为升级中
            upgradeRecord = new UserMarketingActionStatisticUpgradeRecord();
            upgradeRecord.setId(UUIDUtil.getUUID());
            upgradeRecord.setLastSuccessIdCreateTime(null);
            upgradeRecord.setLastSuccessId(null);
            upgradeRecord.setStatus(UPGRADE_UPGRADING_STATUS);
            upgradeRecord.setEa(ea);
            upgradeRecord.setErrorMsg(null);
            userMarketingActionStatisticUpgradeRecordDao.insertIgnore(upgradeRecord);
        } else if (UPGRADE_SUCCESS_STATUS == upgradeRecord.getStatus()) {
            log.info("upgradeId ea:[{}]已经升级成功", ea);
            return false;
        } else if (UPGRADE_UPGRADING_STATUS == upgradeRecord.getStatus()) {
            log.info("upgradeId ea:[{}]升级中", ea);
            return false;
        } else {
            userMarketingActionStatisticUpgradeRecordDao.updateStatus(ea, UPGRADE_UPGRADING_STATUS);
        }
        // 1. 先升级外部联系人
        upgradeExternalUserObj(ea, handleMomentResult);
        // 2. 升级userId
        upgradeEmployeeId(ea, handleMomentResult);
        // 3. 升级tagId
        upgradeTagId(ea);
        // 4. 升级corpId
        upgradeCorpId(ea);
        // 5. 先通知企微升级成功
        boolean isNoticeSuccess = noticeQywxUpgradeSuccess(ea);
        if (!isNoticeSuccess) {
            log.error("通知企微升级成功失败，ea:{}", ea);
            return false;
        }
        afterNoticeSuccess(ea);
        return true;
    }

    public void afterNoticeSuccess(String ea) {
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        // 将这个企业的agentConfig的createTime更新为当前时间，证明这个企业不是明文企业
        qywxCorpAgentConfigDAO.updateInfoAfterUpgradeSuccess(ea, qywxCorpAgentConfigEntity.getId());
        long successTime = System.currentTimeMillis();
        String key = String.format(noticeUpgradeSuccessKey, ea);
        // 把通知企微成功时间记录到redis 主要是用于消费升级期间数据用的，在此时间之前，用转换，之后 不用转换
        redisManager.set(key, 60 * 60 * 24 * 7, String.valueOf(successTime));
        // 6. 开始消费升级期间的回调事件
        consumerCallBackData(ea);
        userMarketingActionStatisticUpgradeRecordDao.updateStatus(ea, UPGRADE_SUCCESS_STATUS);
    }

    public void upgradeCorpId(String ea) {
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        String oldCorpId = qywxCorpAgentConfigEntity.getCorpid();
        String newCorpId = qywxManager.convertToNewCorpId(ea, qywxCorpAgentConfigEntity.getCorpid());
        if (StringUtils.isBlank(newCorpId)) {
            log.error("升级企业微信ID失败，找不到新的企业微信ID，ea:{}", ea);
            return;
        }
        String tableName = "qywx_corp_agent_config";
        List<QywxIdUpgradeRecordEntity> qywxIdUpgradeRecordEntityList = qywxIdUpgradeRecordDAO.getByDataIdListAndQywxIdTypeAndStatus(ea, CORP_ID_TYPE, tableName, Lists.newArrayList(qywxCorpAgentConfigEntity.getId()), SUCCESS);
        if (CollectionUtils.isNotEmpty(qywxIdUpgradeRecordEntityList)) {
            log.error("升级企业微信ID已经升级成功，ea:{} data: {}", ea, qywxCorpAgentConfigEntity);
            String oldQywxId = qywxIdUpgradeRecordEntityList.get(0).getOldQywxId();
            String newQywxId = qywxIdUpgradeRecordEntityList.get(0).getNewQywxId();
            replaceOldCorpIdToNewCorpId(ea, oldQywxId, newQywxId);
            return;
        }
        // 1. 处理 qywx_corp_agent_config
        String id = qywxCorpAgentConfigEntity.getId();
        try {
            int updateCount = qywxCorpAgentConfigDAO.updateCorpIdByEa(newCorpId, ea);
            if (updateCount > 0) {
                saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                        newCorpId, SUCCESS, CORP_ID_TYPE, oldCorpId, newCorpId, null);
                replaceOldCorpIdToNewCorpId(ea, oldCorpId, newCorpId);
            } else {
                saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                        newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
            }
        } catch (Exception e) {
            log.error("upgradeId update qywx_corp_agent_config error, ea:{}, id:{} newCorpId: {}", ea, id, newCorpId, e);
            saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                    newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
        }

        try {
            tableName = "tag_model";
            TagModelEntity tagModelEntity = tagModelDao.getByEaAndSceneId(ea, TagModelSceneConstants.getWxWorkSceneId(oldCorpId));
            if (tagModelEntity != null) {
                int updateCount = tagModelDao.updateSceneId(ea, tagModelEntity.getId(), TagModelSceneConstants.getWxWorkSceneId(newCorpId));
                if (updateCount > 0) {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, SUCCESS, CORP_ID_TYPE, oldCorpId, newCorpId, null);
                    replaceOldCorpIdToNewCorpId(ea, oldCorpId, newCorpId);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                }
            }
        } catch (Exception e) {
            log.error("upgradeId update tag_model error, ea:{}, id:{} newCorpId: {}", ea, id, newCorpId, e);
            saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                    newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
        }
    }

    public void replaceOldCorpIdToNewCorpId(String ea, String oldCorpId, String newCorpId) {
        String tableName = null;
        String id = null;
        // 2. qywx_miniapp_config
        List<QywxMiniappConfigEntity> miniappConfigEntityList = qywxMiniappConfigDAO.getByCorpIdAndEa(oldCorpId, ea);
        if (CollectionUtils.isNotEmpty(miniappConfigEntityList)) {
            tableName = "qywx_miniapp_config";
            for (QywxMiniappConfigEntity qywxMiniappConfigEntity : miniappConfigEntityList) {
                id = qywxMiniappConfigEntity.getId();
                try {
                    int updateCount = qywxMiniappConfigDAO.updateCorpIdById(ea, id, newCorpId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, SUCCESS, CORP_ID_TYPE, oldCorpId, newCorpId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_miniapp_config error, ea:{}, id:{} newCorpId: {}", ea, id, newCorpId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 3. user
        List<UserEntity> userList = userManager.queryUsersByCorpId(oldCorpId);
        if (CollectionUtils.isNotEmpty(userList)) {
            tableName = "user";
            for (UserEntity userEntity : userList) {
                id = userEntity.getUid();
                try {
                    int updateCount = userManager.updateCorpId(id, newCorpId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, SUCCESS, CORP_ID_TYPE, oldCorpId, newCorpId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update user error, ea:{}, id:{} newCorpId: {}", ea, id, newCorpId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 4. qywx_corp_ea_mapping
        tableName = "qywx_corp_ea_mapping";
        QywxCorpEaMappingEntity qywxCorpEaMappingEntity = qywxCorpEaMappingDAO.queryMappingByCorpIdAndEa(oldCorpId, ea);
        if (qywxCorpEaMappingEntity != null) {
            try {
                id = qywxCorpEaMappingEntity.getId();
                int updateCount = qywxCorpEaMappingDAO.updateCorpIdById(id, newCorpId);
                if (updateCount > 0) {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, SUCCESS, CORP_ID_TYPE, oldCorpId, newCorpId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                }
            } catch (Exception e) {
                log.error("upgradeId update qywx_corp_ea_mapping error, ea:{}, id:{} newCorpId: {}", ea, id, newCorpId, e);
                saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                        newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
            }
        }
        // 5. qywx_customer_app_info
        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntityList = qywxCustomerAppInfoDAO.selectAllByCorpIdAndEa(oldCorpId, ea);
        if (CollectionUtils.isNotEmpty(qywxCustomerAppInfoEntityList)) {
            tableName = "qywx_customer_app_info";
            for (QywxCustomerAppInfoEntity qywxCustomerAppInfoEntity : qywxCustomerAppInfoEntityList) {
                id = qywxCustomerAppInfoEntity.getId();
                try {
                    int updateCount = qywxCustomerAppInfoDAO.updateCorpIdById(id, newCorpId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, SUCCESS, CORP_ID_TYPE, oldCorpId, newCorpId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_customer_app_info error, ea:{}, id:{} newCorpId: {}", ea, id, newCorpId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 6. qywx_virtual_fs_user
        List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.queryByEaAndCorpId(ea, oldCorpId);
        tableName = "qywx_virtual_fs_user";
        if (CollectionUtils.isNotEmpty(qywxVirtualFsUserEntityList)) {
            for (QywxVirtualFsUserEntity qywxVirtualFsUserEntity : qywxVirtualFsUserEntityList) {
                id = qywxVirtualFsUserEntity.getId();
                try {
                    int updateCount = qywxVirtualFsUserManager.updateCorpIdById(ea, id, newCorpId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, SUCCESS, CORP_ID_TYPE, oldCorpId, newCorpId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                                newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_virtual_fs_user error, ea:{}, id:{} newCorpId: {}", ea, id, newCorpId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, oldCorpId,
                            newCorpId, FAIL, CORP_ID_TYPE, oldCorpId, newCorpId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
    }

    public boolean noticeQywxUpgradeSuccess(String ea) {
        com.facishare.marketing.common.result.Result<Void> result = qywxManager.finishOpenIdMigration(ea);
        log.info("通知企微升级结果 ea:{} result: {}", ea, result);
        return result.isSuccess();
    }

    public void consumerCallBackData(String ea) {
        int pageSize = 500;
        Date lastCreateTime = null;
        List<QywxIdUpgradingCallbackTempDataEntity> callbackTempDataEntityList = qywxIdUpgradingCallbackTempDataDAO.getByStatusAndCreateTime(ea, QywxIdUpgradingCallbackTempDataEntity.UN_CONSUMED_STATUS, lastCreateTime, pageSize);
        String key = String.format(noticeUpgradeSuccessKey, ea);
        String successTimeValue = redisManager.get(key);
        long successTime = StringUtils.isBlank(successTimeValue) ? 0L : Long.parseLong(successTimeValue);
        while (CollectionUtils.isNotEmpty(callbackTempDataEntityList)) {
            lastCreateTime = callbackTempDataEntityList.get(callbackTempDataEntityList.size() - 1).getCreateTime();
            for (QywxIdUpgradingCallbackTempDataEntity entity : callbackTempDataEntityList) {
                try {
                    if (QywxIdUpgradingCallbackTempDataEntity.EXTERNAL_CONTACT_DATA_TYPE.equals(entity.getDataType())) {
                        ChangeExternalContactEventMsg eventMsg = JsonUtil.fromJson(entity.getData(), ChangeExternalContactEventMsg.class);
                        if (entity.getCreateTime().getTime() < successTime) {
                            Map<String, String> oldExternalUserIdToNewIdMap = qywxManager.convertToNewExternalUserId(ea, Lists.newArrayList(eventMsg.getExternalUserID()));
                            String newExternalUserId = oldExternalUserIdToNewIdMap.get(eventMsg.getExternalUserID());
                            if (StringUtils.isBlank(newExternalUserId)) {
                                log.info("upgradeId 找不到新的外部联系人ID，ea:{}, data:{}", ea, entity);
                                continue;
                            }
                            eventMsg.setExternalUserID(newExternalUserId);
                        }
                        qywxEventCallBackManager.handleExternalUserEventCallback(ea, eventMsg);
                    } else if (QywxIdUpgradingCallbackTempDataEntity.WECHAT_GROUP_DATA_TYPE.equals(entity.getDataType())) {
                        ChangeExternalChatEventMsg eventMsg = JsonUtil.fromJson(entity.getData(), ChangeExternalChatEventMsg.class);
                        qywxEventCallBackManager.postPrcoessWeChatGroupEvent(ea, eventMsg);
                    } else if (QywxIdUpgradingCallbackTempDataEntity.WECHAT_ADDRESS_BOOK_DATA_TYPE.equals(entity.getDataType())) {
                        ChangeContactEventMsg eventMsg = JsonUtil.fromJson(entity.getData(), ChangeContactEventMsg.class);
                        if (entity.getCreateTime().getTime() < successTime) {
                            Map<String, String> oldEmployeeIdToNewUserIdMap = qywxUserManager.getUserIdToOpenUserIdMap(ea, Lists.newArrayList(eventMsg.getUserID()));
                            String newEmployeeId = oldEmployeeIdToNewUserIdMap == null ? null : oldEmployeeIdToNewUserIdMap.get(eventMsg.getUserID());
                            if (StringUtils.isBlank(newEmployeeId)) {
                                log.info("upgradeId 找不到新的企业微信员工ID，ea:{}, data:{}", ea, entity);
                                continue;
                            }
                            eventMsg.setUserID(newEmployeeId);
                            QywxCorpAgentConfigEntity corpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
                            eventMsg.setToUserName(corpAgentConfigEntity.getCorpid());
                        }
                        // 发送mq
                        qywxEventCallBackManager.sendContactChangeToMq(ea, eventMsg);
                        // 存入对象
                        qywxEmployeeManager.handleContactChangeEvent(ea, eventMsg);
                    } else if (QywxIdUpgradingCallbackTempDataEntity.WECHAT_VISIBLE_DATA_TYPE.equals(entity.getDataType())) {
                        SubscribeEventMsg eventMsg = JsonUtil.fromJson(entity.getData(), SubscribeEventMsg.class);
                        if (entity.getCreateTime().getTime() < successTime) {
                            Map<String, String> oldEmployeeIdToNewUserIdMap = qywxUserManager.getUserIdToOpenUserIdMap(ea, Lists.newArrayList(eventMsg.getFromUserName()));
                            String newEmployeeId = oldEmployeeIdToNewUserIdMap == null ? null : oldEmployeeIdToNewUserIdMap.get(eventMsg.getFromUserName());
                            if (StringUtils.isBlank(newEmployeeId)) {
                                log.info("upgradeId 可见范围 找不到新的企业微信员工ID，ea:{}, data:{}", ea, entity);
                                continue;
                            }
                            eventMsg.setFromUserName(newEmployeeId);
                            String newCorpId = qywxManager.convertToNewCorpId(ea, eventMsg.getToUserName());
                            eventMsg.setToUserName(newCorpId);
                        }
                        qywxEmployeeManager.handleSubscribeEvent(ea, eventMsg);
                    }
                    qywxIdUpgradingCallbackTempDataDAO.updateStatus(ea, entity.getId(), QywxIdUpgradingCallbackTempDataEntity.CONSUMED_STATUS);
                } catch (Exception e) {
                    log.error("消费回调事件失败，ea:{}, data:{}", ea, entity, e);
                    qywxIdUpgradingCallbackTempDataDAO.updateStatus(ea, entity.getId(), QywxIdUpgradingCallbackTempDataEntity.CONSUMED_FAIL_STATUS);
                }
            }
            callbackTempDataEntityList = qywxIdUpgradingCallbackTempDataDAO.getByStatusAndCreateTime(ea, QywxIdUpgradingCallbackTempDataEntity.UN_CONSUMED_STATUS, lastCreateTime, pageSize);
        }
    }

    public void upgradeTagId(String ea) {
        List<QywxMomentTaskEntity> qywxMomentTaskEntityList = qywxMomentTaskDAO.queryAllTagIdNotNullByEa(ea);
        if (CollectionUtils.isEmpty(qywxMomentTaskEntityList)) {
            return;
        }
        List<String> momentTaskIdList = qywxMomentTaskEntityList.stream().map(QywxMomentTaskEntity::getId).collect(Collectors.toList());
        List<QywxIdUpgradeRecordEntity> qywxIdUpgradeRecordEntityList = qywxIdUpgradeRecordDAO.getByDataIdListAndQywxIdTypeAndStatus(ea, TAG_ID_TYPE, QYWX_MOMENT_TASK_SOURCE, momentTaskIdList, SUCCESS);
        if (CollectionUtils.isNotEmpty(qywxIdUpgradeRecordEntityList)) {
            Set<String> alreadyUpdateObjectId = qywxIdUpgradeRecordEntityList.stream().map(QywxIdUpgradeRecordEntity::getDataId).collect(Collectors.toSet());
            qywxMomentTaskEntityList = qywxMomentTaskEntityList.stream().filter(e -> !alreadyUpdateObjectId.contains(e.getId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(qywxMomentTaskEntityList)) {
            return;
        }
        Set<String> tagIdSet = Sets.newHashSet();
        for (QywxMomentTaskEntity qywxMomentTaskEntity : qywxMomentTaskEntityList) {
            String tagIdListStr = qywxMomentTaskEntity.getTagIdList();
            List<String> tagIdList = JSONObject.parseArray(tagIdListStr, String.class);
            tagIdSet.addAll(tagIdList);
        }

        Map<String, String> oldTagIdToNewTagIdMap = qywxManager.convertToNewTagId(ea, Lists.newArrayList(tagIdSet));

        String tableName = "qywx_moment_task";
        for (QywxMomentTaskEntity qywxMomentTaskEntity : qywxMomentTaskEntityList) {
            String tagIdListStr = qywxMomentTaskEntity.getTagIdList();
            List<String> tagIdList = JSONObject.parseArray(tagIdListStr, String.class);
            List<String> newTagIdList = Lists.newArrayList();
            tagIdList.forEach(e -> {
                if (oldTagIdToNewTagIdMap.containsKey(e)) {
                    newTagIdList.add(oldTagIdToNewTagIdMap.get(e));
                }
            });
            if (CollectionUtils.isEmpty(newTagIdList)) {
                continue;
            }
            String id = qywxMomentTaskEntity.getId();
            String beforeUpdateJsonStr = JsonUtil.toJson(qywxMomentTaskEntity);
            String newTagIdListStr = JsonUtil.toJson(newTagIdList);
            qywxMomentTaskEntity.setTagIdList(newTagIdListStr);
            String afterJsonData = JsonUtil.toJson(qywxMomentTaskEntity);
            try {
                int updateCount = qywxMomentTaskDAO.updateTagIdList(ea, id, newTagIdListStr);
                if (updateCount > 0) {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, TAG_ID_TYPE, null, null, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, TAG_ID_TYPE, null, null, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                }
            } catch (Exception e) {
                log.error("upgradeId update qywx_moment_task tagId error, ea:{},id:{} ", ea, id, e);
                saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                        afterJsonData, FAIL, TAG_ID_TYPE, null, null, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
            }
        }
    }

    public void upgradeEmployeeId(String ea, boolean handleMomentResult) {
        int pageSize = 500;
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", "cipher_user_id", "name"));
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, pageSize);
        paasQueryArg.addFilter("app_scope", OperatorConstants.IN, Lists.newArrayList("Marketing"));
        queryFilterArg.setQuery(paasQueryArg);
        int qywxEmployeerCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        int handleQywxEmployeeCount = 0;
        String lastId = null;
        while (qywxEmployeerCount > handleQywxEmployeeCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
            if (CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
            handleQywxEmployeeCount += objectDataList.size();
            // 查询企微员工对象是否已经升级过了
            List<String> employeeObjectIdList = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
            List<QywxIdUpgradeRecordEntity> qywxIdUpgradeRecordEntityList = qywxIdUpgradeRecordDAO.getByDataIdListAndQywxIdTypeAndStatus(ea, EMPLOYEE_ID_TYPE, QYWX_USER_OBJ_DATA_SOURCE, employeeObjectIdList, SUCCESS);
            List<QywxIdUpgradeRecordEntity> successList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(qywxIdUpgradeRecordEntityList)) {
                Set<String> alreadyUpdateObjectId = Sets.newHashSet();
                for (QywxIdUpgradeRecordEntity qywxIdUpgradeRecordEntity : qywxIdUpgradeRecordEntityList) {
                    alreadyUpdateObjectId.add(qywxIdUpgradeRecordEntity.getDataId());
                    successList.add(qywxIdUpgradeRecordEntity);
                }
                objectDataList = objectDataList.stream().filter(e -> !alreadyUpdateObjectId.contains(e.getId())).collect(Collectors.toList());
            }
            // 对于已经升级成功的企微员工,重新执行本地表中更新员工ID的操作
            updateByUpgradeRecord(ea, successList);
            if (CollectionUtils.isEmpty(objectDataList)) {
                continue;
            }
            // 对象上的UserId
            List<String> employeeIdList = objectDataList.stream().filter(e -> e.getString("cipher_user_id") != null).map(e -> e.getString("cipher_user_id")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(employeeIdList)) {
                continue;
            }
            Map<String, String> oldEmployeeIdToNewUserIdMap = qywxUserManager.getUserIdToOpenUserIdMap(ea, employeeIdList);
            for (ObjectData objectData : objectDataList) {
                String employeeId = objectData.getString("cipher_user_id");
                if (StringUtils.isBlank(employeeId)) {
                    continue;
                }
                String newEmployeeId = oldEmployeeIdToNewUserIdMap.get(employeeId);
                String beforeUpdateJsonStr = JsonUtil.toJson(objectData);
                if (StringUtils.isBlank(newEmployeeId)) {
                    saveQywxIdUpgradeRecordEntity(ea, objectData.getId(), QYWX_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                            null, FAIL, EMPLOYEE_ID_TYPE, employeeId, null, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_571));
                    continue;
                }
                String id = objectData.getString("_id");
                ObjectData forUpdateObjectData = new ObjectData();
                forUpdateObjectData.put("_id", id);
                forUpdateObjectData.put("cipher_user_id", newEmployeeId);
                String afterJsonData = JsonUtil.toJson(forUpdateObjectData);
                try {
                    Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName(), forUpdateObjectData, false, false);
                    if (updateResult.isSuccess()) {
                        saveQywxIdUpgradeRecordEntity(ea, id, QYWX_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                        // 更新对象成功后，更新本地表的的员工ID
                        replaceOldEmployeeIdToNewEmployeeId(ea, employeeId, newEmployeeId, handleMomentResult);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, QYWX_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, updateResult.getMessage());
                    }
                } catch (Exception e) {
                    log.error("upgradeId EMPLOYEE obj error, ea:{}, objectData:{} newEmployee: {}", ea, objectData, newEmployeeId, e);
                    String errorMsg = e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage();
                    saveQywxIdUpgradeRecordEntity(ea, id, QYWX_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, errorMsg);
                }
            }
        }
    }

    public void upgradeExternalUserObj(String ea, boolean handleMomentResult) {
        int pageSize = 500;
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", "external_user_id", "name"));
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, pageSize);
        paasQueryArg.addFilter("app_scope", OperatorConstants.IN, Lists.newArrayList("Marketing"));
        queryFilterArg.setQuery(paasQueryArg);

        int externalUserCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        int handleExternalUserCount = 0;
        String lastId = null;
        while (externalUserCount > handleExternalUserCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
            if (CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
            handleExternalUserCount += objectDataList.size();
            // 查询外部联系人对象是否已经升级过了
            List<String> externalObjectIdList = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
            List<QywxIdUpgradeRecordEntity> qywxIdUpgradeRecordEntityList = qywxIdUpgradeRecordDAO.getByDataIdListAndQywxIdTypeAndStatus(ea, EXTERNAL_USER_ID_TYPE, EXTERNAL_USER_OBJ_DATA_SOURCE, externalObjectIdList, SUCCESS);
            List<QywxIdUpgradeRecordEntity> successList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(qywxIdUpgradeRecordEntityList)) {
                Set<String> alreadyUpdateObjectId = Sets.newHashSet();
                for (QywxIdUpgradeRecordEntity qywxIdUpgradeRecordEntity : qywxIdUpgradeRecordEntityList) {
                    alreadyUpdateObjectId.add(qywxIdUpgradeRecordEntity.getDataId());
                    successList.add(qywxIdUpgradeRecordEntity);
                }
                objectDataList = objectDataList.stream().filter(e -> !alreadyUpdateObjectId.contains(e.getId())).collect(Collectors.toList());
            }
            // 对于已经升级成功的外部联系人,重新执行本地表中更新外部联系人ID
            updateByUpgradeRecord(ea, successList);
            if (CollectionUtils.isEmpty(objectDataList)) {
                continue;
            }
            // 对象上的外部联系人ID
            List<String> externalUserIdList = objectDataList.stream().filter(e -> e.getString("external_user_id") != null).map(e -> e.getString("external_user_id")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(externalUserIdList)) {
                continue;
            }
            Map<String, String> oldExternalUserIdToNewIdMap = qywxManager.convertToNewExternalUserId(ea, externalUserIdList);
            for (ObjectData objectData : objectDataList) {
                String externalUserId = objectData.getString("external_user_id");
                if (StringUtils.isBlank(externalUserId)) {
                    continue;
                }
                String newExternalUserId = oldExternalUserIdToNewIdMap.get(externalUserId);
                String beforeUpdateJsonStr = JsonUtil.toJson(objectData);
                if (StringUtils.isBlank(newExternalUserId)) {
                    saveQywxIdUpgradeRecordEntity(ea, objectData.getId(), EXTERNAL_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                            null, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, null, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_652));
                    continue;
                }
                String id = objectData.getString("_id");
                ObjectData forUpdateObjectData = new ObjectData();
                forUpdateObjectData.put("_id", id);
                forUpdateObjectData.put("external_user_id", newExternalUserId);
                String afterJsonData = JsonUtil.toJson(forUpdateObjectData);
                try {
                    Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), forUpdateObjectData, false, false);
                    if (updateResult.isSuccess()) {
                        saveQywxIdUpgradeRecordEntity(ea, objectData.getId(), EXTERNAL_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                        // 更新对象成功后，更新本地表的外部联系人ID
                        replaceOldExternalUserIdToNewExternalUserId(ea, externalUserId, newExternalUserId, handleMomentResult);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, objectData.getId(), EXTERNAL_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, updateResult.getMessage());
                    }
                } catch (Exception e) {
                    log.error("upgradeId external user obj error, ea:{}, objectData:{} newExternalUserId: {}", ea, objectData, newExternalUserId, e);
                    String errorMsg = e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage();
                    saveQywxIdUpgradeRecordEntity(ea, objectData.getId(), EXTERNAL_USER_OBJ_DATA_SOURCE, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, errorMsg);
                }
            }
        }
    }

    public void replaceOldEmployeeIdToNewEmployeeId(String ea, String employeeId, String newEmployeeId, boolean handleMomentResult) {
        // 将新员工更新到群对象
        updateNewEmployeeToGroupObj(ea, employeeId, newEmployeeId);
        // 将新员工更新到群成员对象
        updateNewEmployeeToGroupUserObj(ea, employeeId, newEmployeeId);
        // 将新员工更新到好友对象
        updateNewEmployeeToFriendRecordObj(ea, employeeId, newEmployeeId);
        // 1. 更新 qywx_group_send_task 表
        List<QywxGroupSendTaskEntity> qywxGroupSendTaskEntityList = qywxGroupSendTaskDAO.getAllEmployIdByEmployId(ea, employeeId);
        if (CollectionUtils.isNotEmpty(qywxGroupSendTaskEntityList)) {
            String tableName = "qywx_group_send_task";
            for (QywxGroupSendTaskEntity qywxGroupSendTaskEntity : qywxGroupSendTaskEntityList) {
                String id = qywxGroupSendTaskEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxGroupSendTaskEntity);
                String groupSenderIds = qywxGroupSendTaskEntity.getGroupMsgSenderIds();
                String newGroupSenderIds = null;
                if (StringUtils.isNotBlank(groupSenderIds)) {
                    List<String> groupSenderIdList = JSONObject.parseArray(groupSenderIds, String.class);
                    Set<String> groupSenderIdSet = Sets.newHashSet(groupSenderIdList);
                    boolean isRemove = groupSenderIdSet.remove(employeeId);
                    if (isRemove) {
                        groupSenderIdSet.add(newEmployeeId);
                        newGroupSenderIds = JsonUtil.toJson(groupSenderIdSet);
                        qywxGroupSendTaskEntity.setGroupMsgSenderIds(newGroupSenderIds);
                    }
                }
                String userId = qywxGroupSendTaskEntity.getUserId();
                String newUserId = null;
                if (StringUtils.isNotBlank(userId)) {
                    List<String> userIdList = JSONObject.parseArray(userId, String.class);
                    Set<String> userIdSet = Sets.newHashSet(userIdList);
                    boolean isRemove = userIdSet.remove(employeeId);
                    if (isRemove) {
                        userIdSet.add(newEmployeeId);
                        newUserId = JsonUtil.toJson(userIdSet);
                        qywxGroupSendTaskEntity.setUserId(newUserId);
                    }
                }
                String sender = qywxGroupSendTaskEntity.getSender();
                String newSender = null;
                if (StringUtils.isNotBlank(sender) && sender.equals(employeeId)) {
                    qywxGroupSendTaskEntity.setSender(newEmployeeId);
                    newSender = newEmployeeId;
                }
                String afterJsonData = JsonUtil.toJson(qywxGroupSendTaskEntity);
                if (newGroupSenderIds == null && newUserId == null && newSender == null) {
                    continue;
                }
                try {
                    int updateCount = qywxGroupSendTaskDAO.updateEmployeeField(ea, id, newGroupSenderIds, newUserId, newSender);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_group_send_task error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 2. 更新 qywx_moment_task 表
        List<QywxMomentTaskEntity> qywxMomentTaskEntitityList = qywxMomentTaskDAO.getAllUserIdByUserId(ea, employeeId);
        if (CollectionUtils.isNotEmpty(qywxMomentTaskEntitityList)) {
            String tableName = "qywx_moment_task";
            for (QywxMomentTaskEntity qywxMomentTaskEntity : qywxMomentTaskEntitityList) {
                String id = qywxMomentTaskEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxMomentTaskEntity);
                String currentUserId = qywxMomentTaskEntity.getCurrentUserId();
                String newCurrentUsrId = null;
                if (StringUtils.isNotBlank(currentUserId)) {
                    List<String> currentUserIdList = JSONObject.parseArray(currentUserId, String.class);
                    Set<String> currentUserIdSet = Sets.newHashSet(currentUserIdList);
                    boolean isRemove = currentUserIdSet.remove(employeeId);
                    if (isRemove) {
                        currentUserIdSet.add(newEmployeeId);
                        newCurrentUsrId = JsonUtil.toJson(currentUserIdSet);
                        qywxMomentTaskEntity.setCurrentUserId(newCurrentUsrId);
                    }
                }
                String userIdListStr = qywxMomentTaskEntity.getUserIdList();
                String newUserIdList = null;
                if (StringUtils.isNotBlank(userIdListStr)) {
                    List<String> userIdLit = JSONObject.parseArray(userIdListStr, String.class);
                    Set<String> userIdSet = Sets.newHashSet(userIdLit);
                    boolean isRemove = userIdSet.remove(employeeId);
                    if (isRemove) {
                        userIdSet.add(newEmployeeId);
                        newUserIdList = JsonUtil.toJson(userIdSet);
                        qywxMomentTaskEntity.setUserIdList(newUserIdList);
                    }
                }
                if (newUserIdList == null && newCurrentUsrId == null) {
                    continue;
                }
                String afterJsonData = JsonUtil.toJson(qywxMomentTaskEntity);
                try {
                    int updateCount = qywxMomentTaskDAO.updateUserIdField(ea, id, newUserIdList, newCurrentUsrId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_group_send_task error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 3.更新 trigger_task_instance 表
        List<TriggerTaskInstanceEntity> triggerTaskInstanceEntityList = triggerTaskInstanceDao.getByOwnerUserId(ea, employeeId);
        if (CollectionUtils.isNotEmpty(triggerTaskInstanceEntityList)) {
            String tableName = "trigger_task_instance";
            for (TriggerTaskInstanceEntity triggerTaskInstanceEntity : triggerTaskInstanceEntityList) {
                String id = triggerTaskInstanceEntity.getId();
                TriggerTaskInstanceEntity newEntity = new TriggerTaskInstanceEntity();
                newEntity.setId(id);
                newEntity.setOwnerUserId(triggerTaskInstanceEntity.getOwnerUserId());
                String beforeUpdateJsonStr = JsonUtil.toJson(newEntity);
                newEntity.setOwnerUserId(newEmployeeId);
                String afterJsonData = JsonUtil.toJson(newEntity);
                try {
                    int updateCount = triggerTaskInstanceDao.updateOwnerUserId(ea, id, newEmployeeId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update trigger_task_instance error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 4. 更新 qywx_add_fan_qr_code
        List<QywxAddFanQrCodeEntity> qywxAddFanQrCodeEntityList = qywxAddFanQrCodeDAO.getUserIdListByUserId(ea, employeeId);
        if (CollectionUtils.isNotEmpty(qywxAddFanQrCodeEntityList)) {
            String tableName = "qywx_add_fan_qr_code";
            for (QywxAddFanQrCodeEntity qywxAddFanQrCodeEntity : qywxAddFanQrCodeEntityList) {
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxAddFanQrCodeEntity);
                String id = qywxAddFanQrCodeEntity.getId();
                String userId = qywxAddFanQrCodeEntity.getUserId();
                boolean userIdIsRemove = false;
                String newUserId = null;
                if (StringUtils.isNotBlank(userId)) {
                    List<String> userIdList = JSONObject.parseArray(userId, String.class);
                    Set<String> userIdSet = Sets.newHashSet(userIdList);
                    userIdIsRemove = userIdSet.remove(employeeId);
                    if (userIdIsRemove) {
                        userIdSet.add(newEmployeeId);
                        newUserId = JsonUtil.toJson(userIdSet);
                    } else {
                        newUserId = userId;
                    }
                }
                boolean originUserRemove = false;
                String originUserId = qywxAddFanQrCodeEntity.getOrginUserId();
                String newOriginUserId = null;
                if (StringUtils.isNotBlank(originUserId)) {
                    List<String> originUserIdList = JSONObject.parseArray(originUserId, String.class);
                    Set<String> originUserIdSet = Sets.newHashSet(originUserIdList);
                    originUserRemove= originUserIdSet.remove(employeeId);
                    if (originUserRemove) {
                        originUserIdSet.add(newEmployeeId);
                        newOriginUserId = JsonUtil.toJson(originUserIdSet);
                    } else {
                        newOriginUserId = originUserId;
                    }
                }
                if (!userIdIsRemove && !originUserRemove) {
                    continue;
                }
                qywxAddFanQrCodeEntity.setUserId(newUserId);
                qywxAddFanQrCodeEntity.setOrginUserId(newOriginUserId);
                String afterJsonData = JsonUtil.toJson(qywxAddFanQrCodeEntity);
                try {
                    int updateCount = qywxAddFanQrCodeDAO.updateUserId(ea, id, newUserId, newOriginUserId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update trigger_task_instance error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 5. 更新 qywx_welcome_msg
        List<QywxWelcomeMsgEntity> qywxWelcomeMsgEntityList = qywxWelcomeMsgDAO.getUserIdListAndUserIdMergeById(ea, employeeId);
        if (CollectionUtils.isNotEmpty(qywxWelcomeMsgEntityList)) {
            String tableName = "qywx_welcome_msg";
            for (QywxWelcomeMsgEntity qywxWelcomeMsgEntity : qywxWelcomeMsgEntityList) {
                String id = qywxWelcomeMsgEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxWelcomeMsgEntity);
                String userIdListStr = qywxWelcomeMsgEntity.getUserIdList();
                String newUserIdList = null;
                if (StringUtils.isNotBlank(userIdListStr)) {
                    List<String> userIdList = JSONObject.parseArray(userIdListStr, String.class);
                    if (CollectionUtils.isNotEmpty(userIdList)) {
                        Set<String> userIdSet = Sets.newHashSet(userIdList);
                        if (userIdSet.remove(employeeId)) {
                            userIdSet.add(newEmployeeId);
                            newUserIdList = JsonUtil.toJson(userIdSet);
                            qywxWelcomeMsgEntity.setUserIdList(newUserIdList);
                        }
                    }
                }
                String userIdMerge = qywxWelcomeMsgEntity.getUserIdMerge();
                String newUserIdMerge = null;
                if (StringUtils.isNotBlank(userIdMerge)) {
                    List<String> userIdList = JSONObject.parseArray(userIdMerge, String.class);
                    if (CollectionUtils.isNotEmpty(userIdList)) {
                        Set<String> userIdSet = Sets.newHashSet(userIdList);
                        if (userIdSet.remove(employeeId)) {
                            userIdSet.add(newEmployeeId);
                            newUserIdMerge = JsonUtil.toJson(userIdSet);
                            qywxWelcomeMsgEntity.setUserIdMerge(newUserIdMerge);
                        }
                    }
                }
                if (newUserIdList == null && newUserIdMerge == null) {
                    continue;
                }
                String afterJsonData = JsonUtil.toJson(qywxWelcomeMsgEntity);
                try {
                    int updateCount = qywxWelcomeMsgDAO.updateUseIdListAndUserIdMerge(ea, id, newUserIdList, newUserIdMerge);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_welcome_msg error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 6. 更新 qywx_address_book
        QyWxAddressBookEntity qywxAddressBookEntity = qyWxAddressBookDAO.queryByEaAndUserId(ea, employeeId);
        if (qywxAddressBookEntity != null) {
            String tableName = "qywx_address_book";
            String id = qywxAddressBookEntity.getId();
            String beforeUpdateJsonStr = JsonUtil.toJson(qywxAddressBookEntity);
            qywxAddressBookEntity.setUserId(newEmployeeId);
            String afterJsonData = JsonUtil.toJson(qywxAddressBookEntity);
            try {
                int updateCount = qyWxAddressBookDAO.updateUserId(ea, id, newEmployeeId);
                if (updateCount > 0) {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                }
            } catch (Exception e) {
                log.error("upgradeId update qywx_address_book error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                        afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
            }
        }
        // 7. qywx_moment_send_result
        if (handleMomentResult) {
            int momentSendResultCount = qywxMomentSendResultDaO.countByUserId(ea, employeeId);
            int currentCount = 0;
            String lastId = null;
            int momentSendResultPageSize = 500;
            while (momentSendResultCount > currentCount) {
                List<QywxMomentSendResultEntity> qywxMomentSendResultEntityList = qywxMomentSendResultDaO.scanByUserId(ea, employeeId, lastId, momentSendResultPageSize);
                if (CollectionUtils.isEmpty(qywxMomentSendResultEntityList)) {
                    break;
                }
                lastId = qywxMomentSendResultEntityList.get(qywxMomentSendResultEntityList.size() - 1).getId();
                currentCount += qywxMomentSendResultEntityList.size();
                String tableName = "qywx_moment_send_result";
                for (QywxMomentSendResultEntity qywxMomentSendResultEntity : qywxMomentSendResultEntityList) {
                    String id = qywxMomentSendResultEntity.getId();
                    String beforeUpdateJsonStr = JsonUtil.toJson(qywxMomentSendResultEntity);
                    qywxMomentSendResultEntity.setUserId(newEmployeeId);
                    String afterJsonData = JsonUtil.toJson(qywxMomentSendResultEntity);
                    try {
                        int updateCount = qywxMomentSendResultDaO.updateUserId(ea, id, newEmployeeId);
                        if (updateCount > 0) {
                            saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                    afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                        } else {
                            saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                    afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                        }
                    } catch (Exception e) {
                        log.error("upgradeId update qywx_moment_send_result error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                    }
                }
            }
        }
        // 8. 处理 user 表
        List<UserEntity> userEntityList = userManager.queryByEaAndQyUserId(ea, employeeId);
        if (CollectionUtils.isNotEmpty(userEntityList)) {
            String tableName = "user";
            for (UserEntity userEntity : userEntityList) {
                String uid = userEntity.getUid();
                String beforeUpdateJsonStr = JsonUtil.toJson(userEntity);
                userEntity.setQyUserId(newEmployeeId);
                String afterJsonData = JsonUtil.toJson(userEntity);
                try {
                    int updateCount = userManager.updateQyUserId(uid, newEmployeeId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, uid, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, uid, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update user error, ea:{}, id:{} newEmployeeId: {}", ea, uid, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, uid, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 9. trigger_snapshot 表
        List<TriggerSnapshotEntity> triggerSnapshotEntityList = triggerSnapshotDao.queryGroupMsgSenderIdsByEaAndSenderId(ea, employeeId);
        if (CollectionUtils.isNotEmpty(triggerSnapshotEntityList)) {
            String tableName = "trigger_snapshot";
            for (TriggerSnapshotEntity triggerSnapshotEntity : triggerSnapshotEntityList) {
                String beforeUpdateJsonStr = JsonUtil.toJson(triggerSnapshotEntity);
                String id = triggerSnapshotEntity.getId();
                String groupMsgSenderIds = triggerSnapshotEntity.getGroupMsgSenderIds();
                List<String> userIdList = JSONObject.parseArray(groupMsgSenderIds, String.class);
                Set<String> userIdSet = Sets.newHashSet(userIdList);
                boolean isRemove = userIdSet.remove(employeeId);
                if (!isRemove) {
                    continue;
                }
                userIdSet.add(newEmployeeId);
                groupMsgSenderIds = JsonUtil.toJson(userIdSet);
                triggerSnapshotEntity.setGroupMsgSenderIds(groupMsgSenderIds);
                String afterJsonData = JsonUtil.toJson(triggerSnapshotEntity);
                try {
                    int updateCount = triggerSnapshotDao.updateGroupMsgSenderIds(ea, id, groupMsgSenderIds);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update trigger_snapshot error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 10 qywx_virtual_fs_user 表
        List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.getVirtualUserByEaAndQyIds(ea, Lists.newArrayList(employeeId));
        if (CollectionUtils.isNotEmpty(qywxVirtualFsUserEntityList)) {
            String tableName = "qywx_virtual_fs_user";
            for (QywxVirtualFsUserEntity qywxVirtualFsUserEntity : qywxVirtualFsUserEntityList) {
                String id = qywxVirtualFsUserEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxVirtualFsUserEntity);
                qywxVirtualFsUserEntity.setQyUserId(newEmployeeId);
                String afterJsonData = JsonUtil.toJson(qywxVirtualFsUserEntity);
                try {
                    int updateCount = qywxVirtualFsUserManager.updateQyUserIdWithoutHandleUserRelation(ea, id, newEmployeeId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_virtual_fs_user error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }

        // 10.1 user_relation 表
        List<UserRelationEntity> userRelationEntityList = userRelationDao.getByQywxUserIdList(ea, Lists.newArrayList(employeeId));
        if (CollectionUtils.isNotEmpty(userRelationEntityList)) {
            userRelationEntityList.forEach(e -> userRelationDao.updateQywxUserId(ea, e.getId(), newEmployeeId));
        }
        // 11. qywx_task_status 表
        List<QywxTaskEntity> qywxTaskEntityList = qywxTaskDao.getByQywxUserId(ea, employeeId);
        if (CollectionUtils.isNotEmpty(qywxTaskEntityList)) {
            String tableName = "qywx_task_status";
            for (QywxTaskEntity qywxTaskEntity : qywxTaskEntityList) {
                String id = qywxTaskEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxTaskEntity);
                qywxTaskEntity.setQywxUserId(newEmployeeId);
                String afterJsonData = JsonUtil.toJson(qywxTaskEntity);
                try {
                    int updateCount = qywxTaskDao.updateQywxUserId(ea, id, newEmployeeId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_task_status error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 12. qywx_group_send_group_result 表
        List<QywxGroupSendGroupResultEntity> qywxGroupSendGroupResultEntityList = qywxGroupSendGroupResultDAO.getBySender(ea, employeeId);
        if (CollectionUtils.isNotEmpty(qywxGroupSendGroupResultEntityList)) {
            String tableName = "qywx_group_send_group_result";
            for (QywxGroupSendGroupResultEntity qywxGroupSendGroupResultEntity : qywxGroupSendGroupResultEntityList) {
                String id = qywxGroupSendGroupResultEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxGroupSendGroupResultEntity);
                qywxGroupSendGroupResultEntity.setSender(newEmployeeId);
                String afterJsonData = JsonUtil.toJson(qywxGroupSendGroupResultEntity);
                try {
                    int updateCount = qywxGroupSendGroupResultDAO.updateSender(id, newEmployeeId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_group_send_group_result error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 13. qywx_group_send_group_flat_result 表
        List<QywxGroupSendGroupFlatResultEntity> qywxGroupSendGroupFlatResultEntityList = qywxGroupSendGroupResultDAO.getFlatResultBySender(ea, employeeId);
        if (CollectionUtils.isNotEmpty(qywxGroupSendGroupFlatResultEntityList)) {
            String tableName = "qywx_group_send_group_flat_result";
            for (QywxGroupSendGroupFlatResultEntity groupSendGroupFlatResultEntity : qywxGroupSendGroupFlatResultEntityList) {
                String id = groupSendGroupFlatResultEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(groupSendGroupFlatResultEntity);
                groupSendGroupFlatResultEntity.setSender(newEmployeeId);
                String afterJsonData = JsonUtil.toJson(groupSendGroupFlatResultEntity);
                try {
                    int updateCount = qywxGroupSendGroupResultDAO.updateFlatResultSender(id, newEmployeeId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_group_send_group_flat_result error, ea:{}, id:{} newEmployeeId: {}", ea, id, newEmployeeId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
    }

    public void updateNewEmployeeToFriendRecordObj(String ea, String employeeId, String newEmployeeId) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("qywx_user_id", OperatorConstants.EQ, Lists.newArrayList(employeeId));
        List<String> selectFieldList = Lists.newArrayList("_id", "qywx_user_id", "name");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        for (ObjectData objectData : objectDataList) {
            String beforeUpdateJsonStr = JsonUtil.toJson(objectData);
            String id = objectData.getString("_id");
            ObjectData forUpdateObjectData = new ObjectData();
            forUpdateObjectData.put("_id", id);
            forUpdateObjectData.put("qywx_user_id", newEmployeeId);
            String afterJsonData = JsonUtil.toJson(forUpdateObjectData);
            try {
                Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), forUpdateObjectData, false, false);
                if (updateResult.isSuccess()) {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, updateResult.getMessage());
                }
            } catch (Exception e) {
                log.error("upgradeId friend record obj error, ea:{}, objectData:{} newEmployee: {}", ea, objectData, newEmployeeId, e);
                String errorMsg = e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage();
                saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), beforeUpdateJsonStr,
                        afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, errorMsg);
            }
        }
    }

    private void updateNewEmployeeToGroupObj(String ea, String employeeId, String newEmployeeId) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("leader_id", OperatorConstants.EQ, Lists.newArrayList(employeeId));
        List<String> selectFieldList = Lists.newArrayList("_id", "leader_id", "name");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        for (ObjectData objectData : objectDataList) {
            String beforeUpdateJsonStr = JsonUtil.toJson(objectData);
            String id = objectData.getString("_id");
            ObjectData forUpdateObjectData = new ObjectData();
            forUpdateObjectData.put("_id", id);
            forUpdateObjectData.put("leader_id", newEmployeeId);
            String afterJsonData = JsonUtil.toJson(forUpdateObjectData);
            try {
                Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), forUpdateObjectData, false, false);
                if (updateResult.isSuccess()) {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, updateResult.getMessage());
                }
            } catch (Exception e) {
                log.error("upgradeId wechat group obj error, ea:{}, objectData:{} newEmployee: {}", ea, objectData, newEmployeeId, e);
                String errorMsg = e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage();
                saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), beforeUpdateJsonStr,
                        afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, errorMsg);
            }
        }
    }

    public void updateNewEmployeeToGroupUserObj(String ea, String employeeId, String newEmployeeId) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        Wheres invitorIdWhere = new Wheres();
        invitorIdWhere.addFilter("invitor_id", Lists.newArrayList(employeeId), OperatorConstants.EQ);
        Wheres userIdWhere = new Wheres();
        userIdWhere.addFilter("user_id", Lists.newArrayList(employeeId), OperatorConstants.EQ);
        paasQueryArg.setWheres(Lists.newArrayList(invitorIdWhere, userIdWhere));
        List<String> selectFieldList = Lists.newArrayList("_id", "invitor_id", "user_id", "name");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        for (ObjectData objectData : objectDataList) {
            String beforeUpdateJsonStr = JsonUtil.toJson(objectData);
            String id = objectData.getString("_id");

            ObjectData forUpdateObjectData = new ObjectData();
            forUpdateObjectData.put("_id", id);

            String invitorId = objectData.getString("invitor_id");
            if (StringUtils.isNotBlank(invitorId) && invitorId.equals(employeeId)) {
                forUpdateObjectData.put("invitor_id", newEmployeeId);
            }

            String userId = objectData.getString("user_id");
            if (StringUtils.isNotBlank(userId) && userId.equals(employeeId)) {
                forUpdateObjectData.put("user_id", newEmployeeId);
            }

            String afterJsonData = JsonUtil.toJson(forUpdateObjectData);
            try {
                Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), forUpdateObjectData, false, false);
                if (updateResult.isSuccess()) {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, updateResult.getMessage());
                }
            } catch (Exception e) {
                log.error("upgradeId wechat group user obj error, ea:{}, objectData:{} newEmployee: {}", ea, objectData, newEmployeeId, e);
                String errorMsg = e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage();
                saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), beforeUpdateJsonStr,
                        afterJsonData, FAIL, EMPLOYEE_ID_TYPE, employeeId, newEmployeeId, errorMsg);
            }
        }
    }


    public void replaceOldExternalUserIdToNewExternalUserId(String ea, String externalUserId, String newExternalUserId, boolean handleMomentResult) {
        // 更新群成员对象
        updateNewExternalToWechatGroupUserObj(ea, externalUserId, newExternalUserId);
        //1. 处理 user_marketing_wx_work_external_user_relation 表
        UserMarketingWxWorkExternalUserRelationEntity userMarketingWxWorkExternalUserRelationEntity = userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(ea, externalUserId);
        if (userMarketingWxWorkExternalUserRelationEntity != null) {
            String id = userMarketingWxWorkExternalUserRelationEntity.getId();
            String beforeUpdateJsonStr = JsonUtil.toJson(userMarketingWxWorkExternalUserRelationEntity);
            userMarketingWxWorkExternalUserRelationEntity.setWxWorkExternalUserId(newExternalUserId);
            String afterJsonData = JsonUtil.toJson(userMarketingWxWorkExternalUserRelationEntity);
            String tableName = "user_marketing_wx_work_external_user_relation";
            try {
                int updateCount = userMarketingWxWorkExternalUserRelationDao.updateWxWorkExternalUserId(ea, id, newExternalUserId);
                if (updateCount > 0) {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                }
            } catch (Exception e) {
                log.error("upgradeId update user_marketing_wx_work_external_user_relation error, ea:{}, id:{} newExternalUserId: {}", ea, id, newExternalUserId, e);
                saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                        afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
            }
        }
        //2. 处理 qywx_group_send_result 表
        List<QywxGroupSendResultEntity> qywxGroupSendResultEntityList = qywxGroupSendResultDAO.queryExternalUserId(externalUserId);
        if (CollectionUtils.isNotEmpty(qywxGroupSendResultEntityList)) {
            String tableName = "qywx_group_send_result";
            for (QywxGroupSendResultEntity qywxGroupSendResultEntity : qywxGroupSendResultEntityList) {
                String id = qywxGroupSendResultEntity.getId();
                String beforeUpdateJsonStr = JsonUtil.toJson(qywxGroupSendResultEntity);
                qywxGroupSendResultEntity.setExternalUserid(newExternalUserId);
                String afterJsonData = JsonUtil.toJson(qywxGroupSendResultEntity);
                try {
                    int updateCount = qywxGroupSendResultDAO.updateExternalUserId(id, newExternalUserId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                                afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_group_send_result error, ea:{}, id:{} newExternalUserId: {}", ea, id, newExternalUserId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeUpdateJsonStr,
                            afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 3. 处理 qywx_group_send_task 表
        List<QywxGroupSendTaskEntity> qywxGroupSendTaskEntityList = qywxGroupSendTaskDAO.getFailExternalUserIdList(ea, externalUserId);
        if (CollectionUtils.isNotEmpty(qywxGroupSendTaskEntityList)) {
            String tableName = "qywx_group_send_task";
            for (QywxGroupSendTaskEntity qywxGroupSendTaskEntity : qywxGroupSendTaskEntityList) {
                String id = qywxGroupSendTaskEntity.getId();
                String beforeData = qywxGroupSendTaskEntity.getFailList();
                List<String> failList = JSONObject.parseArray(qywxGroupSendTaskEntity.getFailList(), String.class);
                Set<String> failSet = Sets.newHashSet(failList);
                boolean isRemove = failSet.remove(externalUserId);
                if (!isRemove) {
                    continue;
                }
                failSet.add(newExternalUserId);
                String afterData = JsonUtil.toJson(failSet);
                try {
                    int updateCount = qywxGroupSendTaskDAO.updateFailList(ea, id, afterData);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                                afterData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                                afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update qywx_group_send_task error, ea:{}, id:{} newExternalUserId: {}", ea, id, newExternalUserId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                            afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 4. 处理 user 表
        List<UserEntity> userEntityList = userManager.queryByExternalUserId(ea, externalUserId);
        if (CollectionUtils.isNotEmpty(userEntityList)) {
            String tableName = "user";
            for (UserEntity userEntity : userEntityList) {
                String uid = userEntity.getUid();
                String beforeData = JsonUtil.toJson(userEntity);
                userEntity.setExternalUserId(newExternalUserId);
                String afterData = JsonUtil.toJson(userEntity);
                try {
                    int updateCount = userManager.updateExternalUserId(uid, newExternalUserId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, uid, tableName, beforeData,
                                afterData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, uid, tableName, beforeData,
                                afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update user error, ea:{}, id:{} newExternalUserId: {}", ea, uid, newExternalUserId, e);
                    saveQywxIdUpgradeRecordEntity(ea, uid, tableName, beforeData,
                            afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 5. 处理 qywx_task_status 表
        List<QywxTaskEntity> qywxTaskEntityList = qywxTaskDao.getByExternalUserId(ea, externalUserId);
        if (CollectionUtils.isNotEmpty(qywxTaskEntityList)) {
            String tableName = "qywx_task_status";
            for (QywxTaskEntity qywxTaskEntity : qywxTaskEntityList) {
                String id = qywxTaskEntity.getId();
                String beforeData = JsonUtil.toJson(qywxTaskEntity);
                qywxTaskEntity.setExternalUserId(newExternalUserId);
                String afterData = JsonUtil.toJson(qywxTaskEntity);
                try {
                    int updateCount = qywxTaskDao.updateExternalUserId(ea, id, newExternalUserId);
                    if (updateCount > 0) {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                                afterData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                    } else {
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                                afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                    }
                } catch (Exception e) {
                    log.error("upgradeId update user error, ea:{}, id:{} newExternalUserId: {}", ea, id, newExternalUserId, e);
                    saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                            afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                }
            }
        }
        // 6. 处理 qywx_moment_send_result  表
        if (handleMomentResult) {
            List<QywxMomentSendResultEntity> qywxMomentSendResultEntityList = qywxMomentSendResultDaO.getByExternalUserId(ea, externalUserId, null);
            if (CollectionUtils.isNotEmpty(qywxMomentSendResultEntityList)) {
                String tableName = "qywx_moment_send_result";
                for (QywxMomentSendResultEntity qywxMomentSendResultEntity : qywxMomentSendResultEntityList) {
                    String id = qywxMomentSendResultEntity.getId();
                    String beforeData = JsonUtil.toJson(qywxMomentSendResultEntity);
                    qywxMomentSendResultEntity.setExternalUserId(newExternalUserId);
                    String afterData = JsonUtil.toJson(qywxMomentSendResultEntity);
                    try {
                        int updateCount = qywxMomentSendResultDaO.updateExternalUserId(ea, id, newExternalUserId);
                        if (updateCount > 0) {
                            saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                                    afterData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                        } else {
                            saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                                    afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_246));
                        }
                    } catch (Exception e) {
                        log.error("upgradeId update qywx_moment_send_result error, ea:{}, id:{} newExternalUserId: {}", ea, id, newExternalUserId, e);
                        saveQywxIdUpgradeRecordEntity(ea, id, tableName, beforeData,
                                afterData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                    }
                }
            }
        }
    }

    private void updateNewExternalToWechatGroupUserObj(String ea, String externalUserId, String newExternalUserId) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("ext_user_id", OperatorConstants.EQ, Lists.newArrayList(externalUserId));
        List<String> selectFieldList = Lists.newArrayList("_id", "ext_user_id", "name");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        for (ObjectData objectData : objectDataList) {
            String beforeUpdateJsonStr = JsonUtil.toJson(objectData);
            String id = objectData.getString("_id");
            ObjectData forUpdateObjectData = new ObjectData();
            forUpdateObjectData.put("_id", id);
            forUpdateObjectData.put("ext_user_id", newExternalUserId);
            forUpdateObjectData.put("user_id", newExternalUserId);
            String afterJsonData = JsonUtil.toJson(forUpdateObjectData);
            try {
                Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), forUpdateObjectData, false, false);
                if (updateResult.isSuccess()) {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, updateResult.getMessage());
                }
            } catch (Exception e) {
                log.error("upgradeId wechat group user obj error, ea:{}, objectData:{} newExternalUserId: {}", ea, objectData, newExternalUserId, e);
                String errorMsg = e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage();
                saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), beforeUpdateJsonStr,
                        afterJsonData, FAIL, EXTERNAL_USER_ID_TYPE, externalUserId, newExternalUserId, errorMsg);
            }
        }
    }

    public QywxIdUpgradeRecordEntity saveQywxIdUpgradeRecordEntity(String ea, String dataId, String dataSource, String beforeData, String afterData, String status,
                                                                   String qywxIdType, String oldQywxId, String newQywxId, String errorMsg) {
        QywxIdUpgradeRecordEntity recordEntity = new QywxIdUpgradeRecordEntity();
        recordEntity.setEa(ea);
        recordEntity.setId(UUIDUtil.getUUID());
        recordEntity.setDataId(dataId);
        recordEntity.setDataSource(dataSource);
        recordEntity.setBeforeData(beforeData);
        recordEntity.setAfterData(afterData);
        recordEntity.setStatus(status);
        recordEntity.setQywxIdType(qywxIdType);
        recordEntity.setOldQywxId(oldQywxId);
        recordEntity.setNewQywxId(newQywxId);
        recordEntity.setErrorMsg(errorMsg);
        qywxIdUpgradeRecordDAO.insert(recordEntity);
        return recordEntity;
    }

    public void replace(String data) {
        // curl -H "Content-Type: application/json" -X POST -d '{"initAll":false,"eas":["[{\"ea\":\"fs\",\"idType\":\"external\",\"oldId\":\"1\",\"newId\":\"2\"}]"],"beanName":"qywxUpgradeIdManager","methodName":"replace","otherParams":{"noFilter":"Y"}}' "http://127.0.0.1:80/marketing/migrate/invoke"
        JSONArray jsonArray = JSONObject.parseArray(data);
        for (Object object : jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            String ea = jsonObject.getString("ea");
            String idType = jsonObject.getString("idType");
            String oldId = jsonObject.getString("oldId");
            String newId = jsonObject.getString("newId");
            log.info("手动触发更新逻辑, ea:{}, idType:{}, oldId:{}, newId:{}", ea, idType, oldId, newId);
            if (idType.equals("external")) {
                replaceOldExternalUserIdToNewExternalUserId(ea, oldId, newId, true);
            } else if (idType.equals("employee")) {
                replaceOldEmployeeIdToNewEmployeeId(ea, oldId, newId, true);
            } else if (idType.equals("corpId")) {
                replaceOldCorpIdToNewCorpId(ea, oldId, newId);
            }
        }
    }

    public void handleQywxMomentResult(String ea) {
        List<QywxIdUpgradeRecordEntity> externalSuccessList = qywxIdUpgradeRecordDAO.scanByDataSourceAndStatus(ea, EXTERNAL_USER_OBJ_DATA_SOURCE, SUCCESS, null);
        String lastId = null;
        int externalTotalCount = 0;
        while (CollectionUtils.isNotEmpty(externalSuccessList)) {
            externalTotalCount += externalSuccessList.size();
            if (externalTotalCount > 200000) {
                log.info("大于20万 外部联系人 跳出处理， ea: {}", ea);
                break;
            }
            for (QywxIdUpgradeRecordEntity recordEntity : externalSuccessList) {
                if (qywxIdUpgradeHandleMoment.equals("0")) {
                    log.info("跳出处理， ea: {}", ea);
                    return;
                }
                try {
                    List<QywxMomentSendResultEntity> list = qywxMomentSendResultDaO.getByExternalUserId(ea, recordEntity.getOldQywxId(), 1);
                    if (CollectionUtils.isNotEmpty(list)) {
                        qywxMomentSendResultDaO.updateExternalUserIdByOldExternalUserId(ea, recordEntity.getOldQywxId(), recordEntity.getNewQywxId());
                    }
                } catch (Exception e) {
                    log.warn("handleQywxMomentResult update external error, ea: {} recordEntity: {}", ea, recordEntity, e);
                }
            }
            lastId = externalSuccessList.get(externalSuccessList.size() - 1).getId();
            externalSuccessList = qywxIdUpgradeRecordDAO.scanByDataSourceAndStatus(ea, EXTERNAL_USER_OBJ_DATA_SOURCE, SUCCESS, lastId);
        }
        int employeeTotalCount = 0;
        List<QywxIdUpgradeRecordEntity> employeeSuccessList = qywxIdUpgradeRecordDAO.scanByDataSourceAndStatus(ea, QYWX_USER_OBJ_DATA_SOURCE, SUCCESS, null);
        while(CollectionUtils.isNotEmpty(employeeSuccessList)) {
            employeeTotalCount += employeeSuccessList.size();
            if (employeeTotalCount > 100000) {
                log.info("大于10万 员工 跳出处理， ea: {}", ea);
                break;
            }
            for (QywxIdUpgradeRecordEntity recordEntity : employeeSuccessList) {
                if (qywxIdUpgradeHandleMoment.equals("0")) {
                    log.info("跳出处理， ea: {}", ea);
                    return;
                }
                try {
                    List<QywxMomentSendResultEntity> list = qywxMomentSendResultDaO.scanByUserId(ea, recordEntity.getOldQywxId(), null, 1);
                    if (CollectionUtils.isNotEmpty(list)) {
                        qywxMomentSendResultDaO.updateUserIdByOldUserId(ea, recordEntity.getOldQywxId(), recordEntity.getNewQywxId());
                    }
                } catch (Exception e) {
                    log.warn("handleQywxMomentResult update employee error, ea: {} recordEntity: {}", ea, recordEntity, e);
                }
            }
            lastId = employeeSuccessList.get(employeeSuccessList.size() - 1).getId();
            employeeSuccessList = qywxIdUpgradeRecordDAO.scanByDataSourceAndStatus(ea, QYWX_USER_OBJ_DATA_SOURCE, SUCCESS, lastId);
        }
    }

    public void checkAndReUpdateFailData(String data) {
        // curl -H "Content-Type: application/json" -X POST -d '{"initAll":false,"eas":["fs-external_user_id-0"],"beanName":"qywxUpgradeIdManager","methodName":"checkAndReUpdateFailData","otherParams":{"noFilter":"Y"}}' "http://127.0.0.1:80/marketing/migrate/invoke"
        String[] split = data.split("-");
        String ea = split[0];
        // external_user_id employee_id corp_id tag_id
        String idType = split[1];
        String triggerUpdate = split[2];
        List<QywxIdUpgradeRecordEntity> externalUpdateFailList = qywxIdUpgradeRecordDAO.getByQywxTdTypeAndStatus(ea, idType, FAIL);
        if (CollectionUtils.isEmpty(externalUpdateFailList)) {
            log.info("checkAndReUpdateFailData ea: {} idType: {} 没有失败的数据", ea, idType);
            return;
        }
        externalUpdateFailList = externalUpdateFailList.stream().filter(e -> StringUtils.isNotBlank(e.getNewQywxId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(externalUpdateFailList)) {
            log.info("checkAndReUpdateFailData ea: {} idType: {} 没有失败的数据", ea, idType);
            return;
        }
        Map<String, List<QywxIdUpgradeRecordEntity>> sourceMap = externalUpdateFailList.stream().collect(Collectors.groupingBy(QywxIdUpgradeRecordEntity::getDataSource));
        List<QywxIdUpgradeRecordEntity> failList = Lists.newArrayList();
        sourceMap.forEach((dataSource, list) -> {
            List<String> dataIdList = list.stream().map(QywxIdUpgradeRecordEntity::getDataId).collect(Collectors.toList());
            List<QywxIdUpgradeRecordEntity> qywxIdUpgradeRecordEntityList = qywxIdUpgradeRecordDAO.getByDataIdListAndQywxIdTypeAndStatus(ea, idType, dataSource, dataIdList, SUCCESS);
            if (CollectionUtils.isEmpty(qywxIdUpgradeRecordEntityList)) {
                failList.addAll(list);
            } else {
                Set<String> successDataIdSet = qywxIdUpgradeRecordEntityList.stream().map(QywxIdUpgradeRecordEntity::getDataId).collect(Collectors.toSet());
                for (QywxIdUpgradeRecordEntity qywxIdUpgradeRecordEntity : list) {
                    if (!successDataIdSet.contains(qywxIdUpgradeRecordEntity.getDataId())) {
                        failList.add(qywxIdUpgradeRecordEntity);
                    }
                }
            }
        });
        log.info("checkAndReUpdateFailData idType: {} 的错误数据数量:{}", idType, failList.size());
        if (CollectionUtils.isEmpty(failList)) {
            return;
        }
        Set<String> oldIdToNewIdSet = Sets.newHashSet();
        for (QywxIdUpgradeRecordEntity qywxIdUpgradeRecordEntity : failList) {
            String oldId = qywxIdUpgradeRecordEntity.getOldQywxId();
            String newId = qywxIdUpgradeRecordEntity.getNewQywxId();
            if (StringUtils.isBlank(oldId) || StringUtils.isBlank(newId)) {
                continue;
            }
            oldIdToNewIdSet.add(oldId + "#" + newId);
        }
        if (triggerUpdate.equals("1")) {
            for (String s : oldIdToNewIdSet) {
                String[] arr = s.split("#");
                String oldId = arr[0];
                String newId = arr[1];
                log.info("checkAndReUpdateFailData ea: {} idType: {} oldId: {} newId: {}", ea, idType, oldId, newId);
                if (EXTERNAL_USER_ID_TYPE.equals(idType)) {
                    replaceOldExternalUserIdToNewExternalUserId(ea, oldId, newId, true);
                } else if (EMPLOYEE_ID_TYPE.equals(idType)) {
                    replaceOldEmployeeIdToNewEmployeeId(ea, oldId, newId, true);
                } else if (CORP_ID_TYPE.equals(idType)) {
                    replaceOldCorpIdToNewCorpId(ea, oldId, newId);
                } else if (TAG_ID_TYPE.equals(idType)) {
                    upgradeTagId(ea);
                }
            }
        }
    }

    public void updateByUpgradeRecord(String ea, List<QywxIdUpgradeRecordEntity> qywxIdUpgradeRecordEntityList) {
        if (CollectionUtils.isEmpty(qywxIdUpgradeRecordEntityList)) {
            log.info("重新更新本地表数据为空");
            return;
        }
        Map<String, List<QywxIdUpgradeRecordEntity>> qywxIdTypeMap = qywxIdUpgradeRecordEntityList.stream().collect(Collectors.groupingBy(QywxIdUpgradeRecordEntity::getQywxIdType));
        qywxIdTypeMap.forEach((qywxIdType, list) -> {
            Set<String> oldIdToNewIdSet = Sets.newHashSet();
            for (QywxIdUpgradeRecordEntity qywxIdUpgradeRecordEntity : list) {
                String oldId = qywxIdUpgradeRecordEntity.getOldQywxId();
                String newId = qywxIdUpgradeRecordEntity.getNewQywxId();
                oldIdToNewIdSet.add(oldId + "#" + newId);
            }
            for (String s : oldIdToNewIdSet) {
                String[] arr = s.split("#");
                String oldId = arr[0];
                String newId = arr[1];
                if (EXTERNAL_USER_ID_TYPE.equals(qywxIdType)) {
                    replaceOldExternalUserIdToNewExternalUserId(ea, oldId, newId, true);
                } else if (EMPLOYEE_ID_TYPE.equals(qywxIdType)) {
                    replaceOldEmployeeIdToNewEmployeeId(ea, oldId, newId, true);
                } else if (CORP_ID_TYPE.equals(qywxIdType)) {
                    replaceOldCorpIdToNewCorpId(ea, oldId, newId);
                }
            }
        });
    }

    public void fixWechatFriendRecordEmployeeId(String ea) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.NOT_LIKE.getCrmOperator(), Lists.newArrayList("wowx"));
        List<String> selectFieldList = Lists.newArrayList("_id", "qywx_user_id");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        Map<String, String> oldToNewIdMap = Maps.newHashMap();
        for (ObjectData objectData : objectDataList) {
            String beforeUpdateJsonStr = JsonUtil.toJson(objectData);

            String qywxUserId = objectData.getString("qywx_user_id");
            if (StringUtils.isBlank(qywxUserId)) {
                continue;
            }
            QywxIdUpgradeRecordEntity recordEntity = qywxIdUpgradeRecordDAO.getByOldQywxId(ea, EMPLOYEE_ID_TYPE, qywxUserId, SUCCESS);
            String newQywxuserId = null;
            if (oldToNewIdMap.containsKey(qywxUserId)) {
                newQywxuserId = oldToNewIdMap.get(qywxUserId);
            } else if (recordEntity != null && StringUtils.isNotBlank(recordEntity.getNewQywxId())) {
                newQywxuserId = recordEntity.getNewQywxId();
                oldToNewIdMap.put(qywxUserId, newQywxuserId);
            } else {
                Map<String, String> map = qywxUserManager.getUserIdToOpenUserIdMap(ea, Lists.newArrayList(qywxUserId));
                if (MapUtils.isNotEmpty(map) && map.containsKey(qywxUserId)) {
                    newQywxuserId = map.get(qywxUserId);
                    oldToNewIdMap.put(qywxUserId, newQywxuserId);
                }
            }
            String id = objectData.getString("_id");
            if (StringUtils.isBlank(newQywxuserId)) {
                saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), beforeUpdateJsonStr,
                        null, FAIL, EMPLOYEE_ID_TYPE, qywxUserId, newQywxuserId, I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXUPGRADEIDMANAGER_571));
                log.error("找不到新员工ID ea: {} oldQywxUserId: {}", ea, qywxUserId);
                continue;
            }
            ObjectData forUpdateObjectData = new ObjectData();
            forUpdateObjectData.put("_id", id);
            forUpdateObjectData.put("qywx_user_id", newQywxuserId);
            String afterJsonData = JsonUtil.toJson(forUpdateObjectData);
            try {
                Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), forUpdateObjectData, false, false);
                if (updateResult.isSuccess()) {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, SUCCESS, EMPLOYEE_ID_TYPE, qywxUserId, newQywxuserId, null);
                } else {
                    saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), beforeUpdateJsonStr,
                            afterJsonData, FAIL, EMPLOYEE_ID_TYPE, qywxUserId, newQywxuserId, updateResult.getMessage());
                }
            } catch (Exception e) {
                log.error("upgradeId friend record obj error, ea:{}, objectData:{} newEmployee: {}", ea, objectData, null, e);
                String errorMsg = e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage();
                saveQywxIdUpgradeRecordEntity(ea, id, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), beforeUpdateJsonStr,
                        afterJsonData, FAIL, EMPLOYEE_ID_TYPE, qywxUserId, newQywxuserId, errorMsg);
            }
        }
    }

    public void fixGroupUserUserId(String ea) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("ext_user_id", PaasAndCrmOperatorEnum.IS_NOT_NULL.getCrmOperator(), Lists.newArrayList(""));
        List<String> selectFieldList = Lists.newArrayList("_id", "ext_user_id", "user_id");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        for (ObjectData objectData : objectDataList) {
            String extUserId = objectData.getString("ext_user_id");
            String userId = objectData.getString("user_id");
            if (StringUtils.isBlank(extUserId) || StringUtils.isBlank(userId)) {
                log.info("fixGroupUserUserId data is null ea: {} extUserId: {} userId: {} 为空", ea, extUserId, userId);
                continue;
            }
            if (extUserId.equals(userId)) {
                continue;
            }
            ObjectData forUpdateObjectData = new ObjectData();
            forUpdateObjectData.put("_id", objectData.getId());
            forUpdateObjectData.put("user_id", extUserId);
            try {
                Result<ActionEditResult> updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), forUpdateObjectData, false, false);
                log.info("fixGroupUserUserId ea: {} id: {} extUserId: {} userId: {} 更新结果: {}", ea, objectData.getId(), extUserId, userId, updateResult);
            } catch (Exception e) {
                log.error("fixGroupUserUserId error, ea:{}, objectData:{}", ea, objectData, e);
            }
        }
    }

    public void fixZxtRepeatData(String ea) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        List<String> selectFieldList = Lists.newArrayList("_id", "external_user_id", "owner", "wx_union_id");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            log.info("fixZxtRepeatData data is null ea: {}", ea);
            return;
        }
        Map<String, List<ObjectData>> externalUserIdToObjectDataListMap = objectDataList.stream().filter(e -> StringUtils.isNotBlank(e.getString("external_user_id"))).collect(Collectors.groupingBy(e -> e.getString("external_user_id")));
        Map<String, List<ObjectData>> repeatMap = Maps.newHashMap();
        externalUserIdToObjectDataListMap.forEach((k, v) -> {
            if (v.size() > 1) {
                repeatMap.put(k, v);
            }
        });
        objectDataList.clear();
        externalUserIdToObjectDataListMap.clear();
        log.info("fixZxtRepeatData ea: {} 重复的数据数量: {}", ea, repeatMap.size());

        int managerUserId = 1001;
        repeatMap.forEach((k, v) -> {
            boolean isAllBelongManager = v.stream().allMatch(e -> e.getOwner() != null && e.getOwner() == managerUserId);
            boolean isOneBelongManager = v.stream().anyMatch(e -> e.getOwner() != null && e.getOwner() == managerUserId);
            if (isAllBelongManager) {
                // 如果全是杜经理的数据，优先删除掉union为空的数据
                Optional<ObjectData> optional = v.stream().filter(e -> StringUtils.isNotBlank(e.getString("wx_union_id"))).findFirst();
                // 如果union都为空，只保留第一个吧
                List<String> invaildIdsList = optional.map(objectData -> v.stream().map(ObjectData::getId).filter(e -> !e.equals(objectData.getId())).distinct().collect(Collectors.toList()))
                        .orElseGet(() -> v.stream().skip(1).map(ObjectData::getId).distinct().collect(Collectors.toList()));
                List<ObjectData> result = crmV2Manager.bulkInvalidIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), invaildIdsList);
                log.info("fixZxtRepeatData 作废外部联系人数据, ea: {} 作废的数量: {} 作废成功的数量: {}", ea, invaildIdsList.size(), result.size());
                invalidFriendRecord(ea, invaildIdsList);
            } else if (isOneBelongManager) {
                // 如果部分是杜经理的数据，优先删除掉union为空的数据

                // 杜经理的数据
                List<ObjectData> managerDataList = v.stream().filter(e -> e.getOwner() != null && e.getOwner() == managerUserId).collect(Collectors.toList());
                // 不是杜经理的数据
                List<ObjectData> notManagerDataList = v.stream().filter(e -> e.getOwner() == null || e.getOwner() != managerUserId).collect(Collectors.toList());
                // 不属于杜经理的数据且unionId不为空的数据
                Optional<ObjectData> optional = notManagerDataList.stream().filter(e -> StringUtils.isNotBlank(e.getString("wx_union_id"))).findFirst();
                //要保留的对象数据
                ObjectData retainObjectData = optional.orElseGet(() -> notManagerDataList.get(0));
                // 要保留的对象数据的unionId
                String wxUnionId = retainObjectData.getString("wx_union_id");
                // 杜经理的数据的unionId
                Optional<String> managerWxUnionIdOptional = managerDataList.stream().filter(e -> StringUtils.isNotBlank(e.getString("wx_union_id"))).map(e -> e.getString("wx_union_id")).findFirst();
                String managerWxUnionIdStr = managerWxUnionIdOptional.orElse(null);
                // 如果要保留的数据的unionId为空，且杜经理的数据的unionId不为空，那么要保留的数据的unionId设置为杜经理的数据的unionId
                if (StringUtils.isBlank(wxUnionId) && StringUtils.isNotBlank(managerWxUnionIdStr)) {
                    ObjectData forUpdate = new ObjectData();
                    forUpdate.put("_id", retainObjectData.getId());
                    forUpdate.put("wx_union_id", managerWxUnionIdStr);
                    Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), forUpdate);
                    log.info("fixZxtRepeatData 更新外部联系人的unionId, arg: {} result: {}", forUpdate, result);
                }
                List<String> invaildIdsList = v.stream().map(ObjectData::getId).filter(e -> !e.equals(retainObjectData.getId())).distinct().collect(Collectors.toList());
                List<ObjectData> result = crmV2Manager.bulkInvalidIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), invaildIdsList);
                log.info("fixZxtRepeatData 作废外部联系人数据, ea: {} 作废的数量: {} 作废成功的数量: {}", ea, invaildIdsList.size(), result.size());
                invalidFriendRecord(ea, invaildIdsList);
            } else {
                // 如果都不是杜经理的数据，优先删除掉union为空的数据
                Optional<ObjectData> optional = v.stream().filter(e -> StringUtils.isNotBlank(e.getString("wx_union_id"))).findFirst();
                ObjectData retainObjectData = optional.orElseGet(() -> v.get(0));
                List<String> invaildIdsList = v.stream().map(ObjectData::getId).filter(e -> !e.equals(retainObjectData.getId())).distinct().collect(Collectors.toList());
                List<ObjectData> result = crmV2Manager.bulkInvalidIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), invaildIdsList);
                log.info("fixZxtRepeatData 作废外部联系人数据, ea: {} 作废的数量: {} 作废成功的数量: {}", ea, invaildIdsList.size(), result.size());
                invalidFriendRecord(ea, invaildIdsList);
            }
        });
    }

    public void invalidFriendRecord(String ea, List<String> externalUserIdList) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("external_user_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), externalUserIdList);
        List<String> selectFieldList = Lists.newArrayList("_id");
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), selectFieldList, paasQueryArg);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        List<String> invaildIdsList = objectDataList.stream().map(ObjectData::getId).distinct().collect(Collectors.toList());
        List<ObjectData> result = crmV2Manager.bulkInvalidIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), invaildIdsList);
        log.info("fixZxtRepeatData 作废好友记录数据, ea: {} 作废的数量: {} 作废成功的数量: {}", ea, invaildIdsList.size(), result.size());
    }


}