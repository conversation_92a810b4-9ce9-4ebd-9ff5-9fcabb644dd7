package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.provider.entity.mail.MailAddressMapEntity;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2021/04/15
 **/
public interface MailAddressMapDAO {


    @Insert("INSERT INTO mail_address_map (\n"
        + "        \"send_email_address\",\n"
        + "        \"send_email_address_lower_case\"\n"
        + "        )VALUES (\n"
        + "        #{obj.sendEmailAddress},\n"
        + "        #{obj.sendEmailAddressLowerCase}\n"
        + "        ) ON CONFLICT DO NOTHING;")
    void insertMailAddressMap(@Param("obj")MailAddressMapEntity mailAddressMapEntity);


    @Select("<script>"
        + " SELECT * FROM mail_address_map WHERE send_email_address_lower_case IN"
        +   "<foreach collection = 'lowerCases' item = 'item' open = '(' separator = ',' close = ')'>"
        +       "#{item}"
        +   "</foreach>"
        + "</script>")
    List<MailAddressMapEntity> queryEmailMapByLowerCase(@Param("lowerCases") List<String> lowerCases,@Param("ea")String ea);

}
