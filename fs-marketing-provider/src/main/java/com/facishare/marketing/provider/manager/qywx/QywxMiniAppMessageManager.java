/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.enums.NoticeContentTypeEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.innerArg.qywx.QywxSpreadMiniAppMessageArg;
import com.facishare.marketing.provider.innerResult.qywx.SpreadQywxMiniappMessageResult;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2020/04/17
 **/
@Slf4j
@Component
public class QywxMiniAppMessageManager {

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @ReloadableProperty("host")
    private String host;

    private static Integer MAX_USER_AT_ONCE = 1000;

    /**
     * 发送分销人员审核通知
     */
    public boolean sendDistributorApplyMessage(String ea,  List<String> userIds, String operatorId){
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(userIds)) {
            return false;
        }

        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (miniappConfigEntity == null) {
            log.warn("QywxMiniAppMessageManager.sendDistributorApplyMessage error miniappConfigEntity is null ea:{}", ea);
            return false;
        }
        String accessToken = qywxManager.getMiniAppAccessToken(ea);
        if (accessToken == null) {
            log.warn("QywxMiniAppMessageManager.sendDistributorApplyMessage error accessToken is null");
            return false;
        }
        QywxSpreadMiniAppMessageArg arg = new QywxSpreadMiniAppMessageArg();
        QywxSpreadMiniAppMessageArg.MiniprogramNotice miniprogramNotice = new QywxSpreadMiniAppMessageArg.MiniprogramNotice();
        miniprogramNotice.setAppid(miniappConfigEntity.getAppid());
        miniprogramNotice.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_68));
        miniprogramNotice.setPage("/pkgs/pkg-distribute/pages/register-list/register-list?operatorId="+operatorId);
        arg.setMiniprogramNotice(miniprogramNotice);
        return batchSendMessage(accessToken, userIds, arg);
    }

    /**
     * 发送邀约企业微信消息
     */
    public void sendInviteParticipantMessage(String ea, String noticeId, List<String> userIds) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(userIds)) {
            log.warn("QywxMiniAppMessageManager.sendInviteParticipantMessage param error");
            return;
        }
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (miniappConfigEntity == null) {
            log.info("QywxMiniAppMessageManager.sendInviteParticipantMessage error miniappConfigEntity is null ea:{}", ea);
            return;
        }
        String accessToken = qywxManager.getMiniAppAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxMiniAppMessageManager.sendInviteParticipantMessage error accessToken is null ea:{}", ea);
            return;
        }
        String pageBuilder = host
            + "/ec/kemai/release/notice.html?_hash=notice&title=参会邀约"
            + "&noticeId="
            + noticeId
            + "&useNoticeCenterPage=true&contentType="
            + NoticeContentTypeEnum.CONFERENCE_INVITE.getType();
        QywxSpreadMiniAppMessageArg arg = new QywxSpreadMiniAppMessageArg();
        QywxSpreadMiniAppMessageArg.MiniprogramNotice miniprogramNotice = new QywxSpreadMiniAppMessageArg.MiniprogramNotice();
        miniprogramNotice.setAppid(miniappConfigEntity.getAppid());
        miniprogramNotice.setPage(pageBuilder);
        miniprogramNotice.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_102));
        batchSendMessage(accessToken, userIds, arg);
    }

    /**
     * 发送名片邀约信息
     */
    public boolean sendCardInviteMessage(String ea, List<String> userIds) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (miniappConfigEntity == null) {
            log.warn("QywxMiniAppMessageManager.sendCardInviteMessage error miniappConfigEntity is null ea:{}", ea);
            return false;
        }
        String accessToken = qywxManager.getMiniAppAccessToken(ea);
        if (accessToken == null) {
            log.warn("QywxMiniAppMessageManager.sendCardInviteMessage error accessToken is null");
            return false;
        }
        QywxSpreadMiniAppMessageArg arg = new QywxSpreadMiniAppMessageArg();
        QywxSpreadMiniAppMessageArg.MiniprogramNotice miniprogramNotice = new QywxSpreadMiniAppMessageArg.MiniprogramNotice();
        miniprogramNotice.setAppid(miniappConfigEntity.getAppid());
        miniprogramNotice.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_126));
        miniprogramNotice.setPage("/pages/index/index");
        arg.setMiniprogramNotice(miniprogramNotice);
        return batchSendMessage(accessToken, userIds, arg);
    }

    public boolean sendConferenceCheckEnroll(String ea, List<String> userIds, String title, String enrollTime, String conferenceId) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (miniappConfigEntity == null) {
            log.warn("QywxMiniAppMessageManager.sendConferenceCheckEnroll error miniappConfigEntity is null ea:{}", ea);
            return false;
        }
        String accessToken = qywxManager.getMiniAppAccessToken(ea);
        if (accessToken == null) {
            log.warn("QywxMiniAppMessageManager.sendConferenceCheckEnroll error accessToken is null");
            return false;
        }
        QywxSpreadMiniAppMessageArg arg = new QywxSpreadMiniAppMessageArg();
        QywxSpreadMiniAppMessageArg.MiniprogramNotice miniprogramNotice = new QywxSpreadMiniAppMessageArg.MiniprogramNotice();
        miniprogramNotice.setAppid(miniappConfigEntity.getAppid());
        if (StringUtils.isNotBlank(conferenceId)) {
            miniprogramNotice.setPage("/pkgs/pkg-conference/pages/review-list/review-list?conferenceId=" + conferenceId);
        } else {
            miniprogramNotice.setPage("/pkgs/pkg-conference/pages/review-list/review-list");
        }
        miniprogramNotice.setTitle(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_FSMESSAGEMANAGER_296));
        miniprogramNotice.setDescription(DateUtil.format2(new Date()));
        List<Map<String, String>> contentList = Lists.newArrayList();
        contentList.add(buildContentMap(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_ACTIVITYENTITY_117), title));
        contentList.add(buildContentMap(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1750), enrollTime));
        contentList.add(buildContentMap(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_159), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_FSMESSAGEMANAGER_313)));
        miniprogramNotice.setContentItem(contentList);
        arg.setMiniprogramNotice(miniprogramNotice);
        return batchSendMessage(accessToken, userIds, arg);
    }

    public void sendPrivateMessageNotice(String targetUid, String ea, String userId, String sendUsername, Long sendTime) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(userId) || StringUtils.isBlank(sendUsername)) {
            return;
        }
        if (sendTime == null) {
            sendTime = new Date().getTime();
        }
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (miniappConfigEntity == null) {
            log.warn("sendPrivateMessageNotice.sendPrivateMessageNotice error miniappConfigEntity is null ea:{}", ea);
            return;
        }
        String accessToken = qywxManager.getMiniAppAccessToken(ea);
        if (accessToken == null) {
            log.warn("sendPrivateMessageNotice.sendPrivateMessageNotice error accessToken is null");
            return;
        }
        QywxSpreadMiniAppMessageArg arg = new QywxSpreadMiniAppMessageArg();
        QywxSpreadMiniAppMessageArg.MiniprogramNotice miniprogramNotice = new QywxSpreadMiniAppMessageArg.MiniprogramNotice();
        miniprogramNotice.setAppid(miniappConfigEntity.getAppid());
        miniprogramNotice.setPage("pkgs/pkg-sub/pages/msg/chat/chat?targetUid=" + targetUid);
        miniprogramNotice.setTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_186));
        miniprogramNotice.addContentItem(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_187), I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_187_1));
        miniprogramNotice.addContentItem(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_188), sendUsername);
        miniprogramNotice.addContentItem(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_189), DateUtil.format3(new Date(sendTime)));
        arg.setMiniprogramNotice(miniprogramNotice);
        batchSendMessage(accessToken, Lists.newArrayList(userId), arg);
    }

    /**
     * 发送信息
     * @param accessToken
     * @param userIds
     * @param arg
     * @return
     */
    public boolean batchSendMessage(String accessToken, List<String> userIds, QywxSpreadMiniAppMessageArg arg) {
        if (CollectionUtils.isEmpty(userIds) || StringUtils.isBlank(accessToken)) {
            return false;
        }
        SpreadQywxMiniappMessageResult spreadQywxMiniappMessageResult = null;
        // 发送人员单次最多1000人
        if (userIds.size() > MAX_USER_AT_ONCE) {
            PageUtil<String> userPage = new PageUtil<>(userIds, MAX_USER_AT_ONCE);
            for (int i = 1; i <= userPage.getPageCount(); i++) {
                StringJoiner sj = new StringJoiner("|");
                for (String userId : userPage.getPagedList(i)) {
                    sj.add(userId + "");
                }
                arg.setTouser(sj.toString());
                spreadQywxMiniappMessageResult = qywxManager.sendSpreadQywxMiniappMessage(accessToken, arg);
                if (spreadQywxMiniappMessageResult == null || !spreadQywxMiniappMessageResult.isSuccess()) {
                    return false;
                }
            }
        } else {
            StringJoiner sj = new StringJoiner("|");
            for (String userId : userIds) {
                sj.add(userId + "");
            }
            arg.setTouser(sj.toString());
            spreadQywxMiniappMessageResult = qywxManager.sendSpreadQywxMiniappMessage(accessToken, arg);
            return spreadQywxMiniappMessageResult != null && spreadQywxMiniappMessageResult.isSuccess();
        }
        return true;
    }

    /**
     * 裁剪字段
     */
    private String cropField(String value, int length) {
        if (StringUtils.isBlank(value) || value.length() <= length) {
            return value;
        } else {
            return value.substring(0, length);
        }
    }

    public Map<String, String> buildContentMap(String key, String value) {
        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put("key", key);
        contentMap.put("value", value);
        return contentMap;
    }

}