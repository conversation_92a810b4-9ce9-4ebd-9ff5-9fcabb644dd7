/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.distribution.OpenDistributeStatusArg;
import com.facishare.marketing.api.arg.miniAppSetting.ConfigMiniAppAutoUpgradeArg;
import com.facishare.marketing.api.arg.miniAppSetting.GetMiniAppAutoUpgradeStatusArg;
import com.facishare.marketing.api.arg.usermarketingaccount.MarketingUserGroupCustomizeObjectMappingArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.distribution.CommitClueRewardStatusResult;
import com.facishare.marketing.api.result.distribution.QueryClueAuditStatusResult;
import com.facishare.marketing.api.service.MarketingReportService;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.service.WxOfficialAccountsService;
import com.facishare.marketing.api.vo.SystemOperationRecordVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.*;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.marketingAccess.AccessScopeEnum;
import com.facishare.marketing.common.enums.miniAppSetting.MiniAppUpgradeStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.AccessChannel;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.manager.MankeepCrmObjectFieldMappingDaoManager;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxSidebarMaterialSendSettingDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.dto.FsViewProfileDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.QywxSidebarMaterialSendSettingEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.innerArg.qywx.QywxAgentMessageArg;
import com.facishare.marketing.provider.innerResult.qywx.SpreadQywxMiniappMessageResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.miniappLogin.MiniappAccessPermissionsManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WxCloudRestManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.ConfigUtil;
import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.msg.constant.OpenMessageTypeEnum;
import com.facishare.open.msg.model.InternationalInfo;
import com.facishare.open.msg.model.SendAppToCMessageVO;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.SendOpenMessageService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentName;
import com.facishare.organization.api.model.department.arg.GetAllDepartmentNameArg;
import com.facishare.organization.api.model.department.result.GetAllDepartmentNameResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.privilege.api.RolePrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.role.QueryAllRoleInfoByRoleCodesVo;
import com.facishare.privilege.api.module.role.RoleVo;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.webpage.customer.api.model.result.GetTenantBrandColorResult;
import com.facishare.webpage.customer.api.service.TenantBrandColorService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectaDescribeAllData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.wechatrestapi.arg.CommitCodeArg;
import com.fxiaoke.wechatrestapi.arg.GetAuditStatusArg;
import com.fxiaoke.wechatrestapi.data.BusinessInfo;
import com.fxiaoke.wechatrestapi.result.*;
import com.fxiaoke.wechatrestapi.service.miniapp.CodeManageService;
import com.fxiaoke.wechatrestapi.service.miniapp.CodeTemplateService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.reader.UnicodeReader;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.contstant.RoleConstant.SYSTEM_ADMIN_ROLE_ID;
import static com.facishare.marketing.common.contstant.RoleConstant.WECHAT_OPERATION;
import static com.facishare.marketing.provider.manager.permission.DataPermissionManager.defaultAllChannel;

/**
 * <AUTHOR>
 */
@Service("settingService")
@Slf4j
public class SettingServiceImpl implements SettingService {
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private MarketingEnterpriseCommonSettingDAO marketingEnterpriseCommonSettingDAO;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private MankeepCrmObjectFieldMappingDaoManager mankeepCrmObjectFieldMappingDaoManager;
    @Autowired
    private MankeepCrmObjectFieldMappingDao mankeepCrmObjectFieldMappingDao;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private MiniappReleaseRecordDao miniappReleaseRecordDao;
    @Autowired
    private CodeManageService codeManageService;
    @Autowired
    private CodeTemplateService codeTemplateService;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private HexagonOfficialWebsiteDAO hexagonOfficialWebsiteDAO;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private WxCloudRestManager wxCloudRestManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private MarketingUserGroupCustomizeObjectMappingDao marketingUserGroupCustomizeObjectMappingDao;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private PicFsMappingDAO picFsMappingDAO;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;

    @ReloadableProperty("marketing_appid")
    private String appId;
    @ReloadableProperty("marketing_app_web_component")
    private String marketingAppWebComponentId;
    @ReloadableProperty("marketing_app_app_component")
    private String marketingAppAppComponentId;

    @ReloadableProperty("host")
    private String host;

    @ReloadableProperty("system_roles")
    private String systemRoles;
    @ReloadableProperty("system_roles_en")
    private String systemRolesEN;

    @ReloadableProperty("fwt.miniapp.release.version.grey")
    private String fwtReleaseVersionGrey;

    // {"88146":"9.0.1-9.2.10"}
    @ReloadableProperty("marketing.miniapp.gray.config")
    private String marketingMiniappGrayConfig;

    /**
     * 配置中心配置:指定的企业,使用哪个版本小程序
     * 配置格式:configed_mini_program_version=FWT_fktest110_1.3.0;FWT_fktest111_1.3.1
     */
    @ReloadableProperty("configed_mini_program_version")
    private String configedMiniProgramVersion;

    @ReloadableProperty("mini_program_location_appid")
    private String miniProgramLocationAppid;

    @Autowired
    private OpenAppAdminService openAppAdminService;

    @Autowired
    private UserRoleDao userRoleDao;
    @Autowired
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private EmployeeMetaConfigDao employeeMetaConfigDao;
    @Autowired
    private BoardManager boardManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private SystemOperationRecordDao systemOperationRecordDao;
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private H5AccessPermissionsSeetingDao h5AccessPermissionsSeetingDao;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;

    @Autowired
    private EIEAConverter eieaConverter;

    private FsViewProfileDTO fsViewProfileDTO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private EmployeeProviderService employeeProviderService;

    @Autowired
    private QywxSidebarMaterialSendSettingDAO qywxSidebarMaterialSendSettingDAO;

    private static final String READ_TIME_OUT = "__retrofit-spring_read_timeout";
    @Resource
    private TenantBrandColorService tenantBrandColorService;
    @Autowired
    private AuthManager authManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;

    @Autowired
    private MarketingReportService marketingReportService;

    @ReloadableProperty("marketing_report_send_ea")
    private String marketingReportSendEaList;

    @Autowired
    private FsMessageManager fsMessageManager;

    @Autowired
    private UserAccessibleDAO userAccessibleDAO;


    @Autowired
    private WxOfficialAccountsService wxOfficialAccountsService;

    @Autowired
    private DepartmentProviderService departmentProviderService;

    @Autowired
    private OfficialWebsiteDAO officialWebsiteDAO;

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private SendOpenMessageService sendOpenMessageService;

    @Autowired
    private MiniappAccessPermissionsManager miniappAccessPermissionsManager;

    @Autowired
    private MarketingEnterpriseCommonSettingManager marketingEnterpriseCommonSettingManager;

    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    @Autowired
    private RolePrivilegeRestService rolePrivilegeRestService;

    private Gson gs = new Gson();

    @Override
    public Result<AllConfigResult> listAllConfig(String ea, Integer fsUserId) {
        CustomerSaveSettingResult customerSaveSetting = new CustomerSaveSettingResult();
        MankeepSaveSettingResult mankeepSaveSetting = new MankeepSaveSettingResult();

        EnterpriseMetaConfigEntity enterpriseMetaConfig = enterpriseMetaConfigDao.getByEa(ea);
        while (enterpriseMetaConfig == null) {
            enterpriseMetaConfigDao.insertIgnore(UUIDUtil.getUUID(), ea);
            redisManager.setEnterpriseStatus(ea, StringPool.ONE);
            enterpriseMetaConfig = enterpriseMetaConfigDao.getByEa(ea);
        }

        customerSaveSetting.setSaveCustomerToCrmEnabled(enterpriseMetaConfig.isSaveCustomerToCrmEnabled());
        mankeepSaveSetting.setSaveMankeepToCrmEnabled(enterpriseMetaConfig.isSaveMankeepToCrmEnabled());

        List<MankeepFieldResult> customerSaveToCustomerFieldVos = CustomerFieldEnum.listFieldEnumForCustomerToCrmCustomerSave().stream().map(MankeepFieldResult::fromCustomerFieldEnum)
            .collect(Collectors.toList());
        customerSaveSetting.setMankeepFieldsForCrmCustomerSave(customerSaveToCustomerFieldVos);
        List<MankeepFieldResult> customerSaveToContactFieldVos = CustomerFieldEnum.listFieldEnumForCustomerToCrmContactSave().stream().map(MankeepFieldResult::fromCustomerFieldEnum)
            .collect(Collectors.toList());
        customerSaveSetting.setMankeepFieldsForCrmContactSave(customerSaveToContactFieldVos);
        List<MankeepFieldResult> customerSaveToLeadFieldVos = CustomerFieldEnum.listFieldEnumForManKeepToCrmLeadSave().stream().map(MankeepFieldResult::fromCustomerFieldEnum)
            .collect(Collectors.toList());
        mankeepSaveSetting.setMankeepFieldsForCrmLeadSave(customerSaveToLeadFieldVos);

        List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CUSTOMER);
        List<CrmFieldResult> crmCustomerFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(crmCustomerFields);
        List<CrmUserDefineFieldVo> crmContactFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CONTACT);
        List<CrmFieldResult> crmContactFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(crmContactFields);
        List<CrmUserDefineFieldVo> crmLeadFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD);
        List<CrmFieldResult> crmLeadFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(crmLeadFields);

        List<MankeepCrmObjectFieldMappingEntity> customerMappings = mankeepCrmObjectFieldMappingDaoManager
            .listOrCreateByMankeepAndCrmObjectApiName(ea, MankeepObjectApiNameEnum.CUSTOMER.getName(), CrmObjectApiNameEnum.CUSTOMER.getName());
        customerSaveSetting.setCustomerFieldMappingList(customerMappings.stream().map(customerMapping -> {
            FieldMappingConfigResult fieldMappingConfigResult = customerMapping.toFieldMappingConfigVO();
            fieldMappingConfigResult.setCrmFieldList(crmCustomerFieldVOS);
            return fieldMappingConfigResult;
        }).collect(Collectors.toList()));
        List<MankeepCrmObjectFieldMappingEntity> contactMappings = mankeepCrmObjectFieldMappingDaoManager
            .listOrCreateByMankeepAndCrmObjectApiName(ea, MankeepObjectApiNameEnum.CUSTOMER.getName(), CrmObjectApiNameEnum.CONTACT.getName());
        customerSaveSetting.setContactFieldMappingList(contactMappings.stream().map(contactMapping -> {
            FieldMappingConfigResult fieldMappingConfigResult = contactMapping.toFieldMappingConfigVO();
            fieldMappingConfigResult.setCrmFieldList(crmContactFieldVOS);
            return fieldMappingConfigResult;
        }).collect(Collectors.toList()));
        List<MankeepCrmObjectFieldMappingEntity> leadMappings = mankeepCrmObjectFieldMappingDaoManager
            .listOrCreateByMankeepAndCrmObjectApiName(ea, MankeepObjectApiNameEnum.MANKEEP.getName(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        mankeepSaveSetting.setLeadFieldMappingList(leadMappings.stream().map(leadMapping -> {
            FieldMappingConfigResult fieldMappingConfigResult = leadMapping.toFieldMappingConfigVO();
            fieldMappingConfigResult.setCrmFieldList(crmLeadFieldVOS);
            return fieldMappingConfigResult;
        }).collect(Collectors.toList()));

        AllConfigResult allConfigResult = new AllConfigResult();
        allConfigResult.setCustomerSaveSetting(customerSaveSetting);
        allConfigResult.setMankeepSaveSetting(mankeepSaveSetting);
        return new Result<>(SHErrorCode.SUCCESS, allConfigResult);
    }

    @Override
    public Result<ListMappingSettingsResult> listMappingSettings(String ea, Integer fsUserId, String mankeepObjectApiName, String crmObjectApiName) {
        ListMappingSettingsResult listMappingSettingsResult = new ListMappingSettingsResult();

        MankeepObjectApiNameEnum mankeepObjectApiNameEnum = MankeepObjectApiNameEnum.valueOf(MankeepObjectApiNameEnum.class, mankeepObjectApiName);
        CrmObjectApiNameEnum crmObjectApiNameEnum = CrmObjectApiNameEnum.valueOf(CrmObjectApiNameEnum.class, crmObjectApiName);

        List<FieldResult> fieldResults = Objects.requireNonNull(CustomerFieldEnum.listFieldEnumToCrmSave(mankeepObjectApiNameEnum, crmObjectApiNameEnum)).stream().map(FieldResult::fromFieldEnum).collect(Collectors.toList());

        listMappingSettingsResult.setFieldResults(fieldResults);

        List<CrmUserDefineFieldVo> crmFields = crmV2Manager.getObjectFieldDescribesList(ea, crmObjectApiNameEnum);
        List<CrmFieldResult> crmFieldResults = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(crmFields);

        List<MankeepCrmObjectFieldMappingEntity> mankeepCrmObjectFieldMappingEntities = mankeepCrmObjectFieldMappingDaoManager
            .listOrCreateByMankeepAndCrmObjectApiName(ea, MankeepObjectApiNameEnum.MANKEEP.getName(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        listMappingSettingsResult.setFieldMappingConfigResults(mankeepCrmObjectFieldMappingEntities.stream().map(mankeepCrmObjectFieldMappingEntity -> {
            FieldMappingConfigResult fieldMappingConfigResult = mankeepCrmObjectFieldMappingEntity.toFieldMappingConfigVO();
            fieldMappingConfigResult.setCrmFieldList(crmFieldResults);
            return fieldMappingConfigResult;
        }).collect(Collectors.toList()));

        return new Result<>(SHErrorCode.SUCCESS, listMappingSettingsResult);
    }

    @Override
    public Result<Void> updateSaveConfig(String ea, Integer fsUserId, boolean newIsSaveCustomerToCrmStatus) {
        enterpriseMetaConfigDao.updateIsSaveCustomerToCrmEnabled(ea, newIsSaveCustomerToCrmStatus);
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<Void> updateIsSaveMankeepToCrmEnabled(String ea, Integer fsUserId, boolean newIsSaveMankeepToCrmStatus) {
        enterpriseMetaConfigDao.updateIsSaveMankeepToCrmEnabled(ea, newIsSaveMankeepToCrmStatus);
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<Void> mergeFieldMappings(String ea, Integer fsUserId, List<FieldMappingConfigResult> newFieldMappingConfigResults) {
        List<MankeepCrmObjectFieldMappingEntity> entitiesToUpdate = new ArrayList<>();

        for (FieldMappingConfigResult newFieldMappingConfigResult : newFieldMappingConfigResults) {

            String dbRecordType = mankeepCrmObjectFieldMappingDao
                .getRecordTypeByMankeepAndCrmObjectApiName(ea, newFieldMappingConfigResult.getMankeepObjectApiName(), newFieldMappingConfigResult.getCrmObjectApiName());
            if (dbRecordType == null) {
                dbRecordType = CrmConstants.DEFAULT_RECORD_TYPE;
            }

            MankeepCrmObjectFieldMappingEntity entity = mankeepCrmObjectFieldMappingDaoManager
                .getOrCreateByApiNameAndRecordType(ea, newFieldMappingConfigResult.getMankeepObjectApiName(), newFieldMappingConfigResult.getCrmObjectApiName(), dbRecordType);
            entity.setCrmObjectRecordType(newFieldMappingConfigResult.getRecordType());
            entity.setFieldMappingsV2(crmV2MappingManager.doMergeFieldMappings(entity.getFieldMappingsV2(), newFieldMappingConfigResult.getFieldMappings()));
            entitiesToUpdate.add(entity);
        }

        String validateResult = crmV2MappingManager.doVerifyMankeepCrmObjectFieldMappingEntities(ea, entitiesToUpdate);
        if (!CrmV2MappingManager.FieldValidateMessage.FIELD_SUCCESS.equals(validateResult)) {
            return new Result<>(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR.getErrorCode(), validateResult);
        }

        entitiesToUpdate.forEach(entity -> mankeepCrmObjectFieldMappingDao.updateFieldMappingsAndRecordType(entity.getId(), entity.getCrmObjectRecordType(), entity.getFieldMappingsV2()));

        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<Void> initMarketingData(String ea, Integer fsUserId) {
        String version = appVersionManager.getCurrentAppVersion(ea);
        if (StringUtils.isBlank(version)) {
            return new Result<>();
        }
        ThreadPoolUtils.execute(() -> {
            EmployeeMetaConfigEntity employeeMetaConfig = employeeMetaConfigDao.getConfig(ea, fsUserId);
            if (employeeMetaConfig == null){
                employeeMetaConfigDao.insertIgnore(ea, fsUserId);
                employeeMetaConfig = employeeMetaConfigDao.getConfig(ea, fsUserId);
            }
            if(employeeMetaConfig != null && !BooleanUtils.isTrue(employeeMetaConfig.getBoardPreset())){
                boolean markSuccess = employeeMetaConfigDao.markBoardPreset(ea, fsUserId) > 0;
                if (markSuccess){
                    boardManager.initDefaultUserBoard(ea, fsUserId);
                }
            }
            //查询营销活动对象是否存在，如果不存在，初始化版本--兜底（初始化营销通时失败，底层接口调用失败）
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
            if (getDescribeResultResult != null && getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
                //营销活动对象不存在，初始营销通
                log.info("initMarketingData again ea:{} getDescribeResultResult:{}", ea, getDescribeResultResult);
                settingManager.initMarketingData(ea, -10000, version);

            }
            marketingEventCommonSettingService.getSceneHexagonTemplates(ea);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> brushLibrary(String randomNum) {
        return settingManager.brushLibrary(randomNum);
    }

    @Override
    public Result<Void> openDistributeStatus(OpenDistributeStatusArg arg) {
        return settingManager.openDistributeStatus(arg);
    }

    @Override
    public Result<DistributeStatusResult> judgeDistributeStatus(String ea) {
        return settingManager.judgeDistributeStatus(ea);
    }

    @Override
    public Result<Void> updateDistributePlan(OpenDistributeStatusArg arg) {
        return settingManager.updateDistributePlan(arg);
    }

    @Override
    public Result<SocialDistributionDetailResult> getSocialDistributeTableDetail(String ea, String planId) {
        return settingManager.getSocialDistributeTableDetail(ea, planId);
    }

    @Override
    public Result<Void> setEnterpriseInfo(SetEnterpriseInfoArg arg) {
        return settingManager.setEnterpriseInfo(arg);
    }

    @Override
    public Result<EnterpriseInfoResult> queryEnterpriseInfo(String ea, Integer userId) {
        return settingManager.queryEnterpriseInfo(ea, userId);
    }

    @Override
    public Result<Void> setClueAuditStatus(String ea, Integer userId, Integer status) {
        return settingManager.setClueAuditStatus(ea, userId, status);
    }

    @Override
    public Result<QueryClueAuditStatusResult> queryClueAuditStatus(String ea, Integer userId) {
        return settingManager.queryClueAuditStatus(ea, userId);
    }

    @Override
    public Result<Void> setCommitClueRewardStatus(String planId, Integer status, Double clueReward) {
        return settingManager.setCommitClueRewardStatus(planId, status, clueReward);
    }

    @Override
    public Result<CommitClueRewardStatusResult> getCommitClueRewardStatus(String planId) {
        if (Strings.isNullOrEmpty(planId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingManager.getCommitClueRewardStatus(planId);
    }

    @Override
    public Result<GetAppIdResult> getAppId() {
        GetAppIdResult result = new GetAppIdResult();
        result.setAppId(appId);
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetBoundMiniappInfoResult> getBoundMiniappInfo(String ea, String platformId) {
        String wxAppId = eaWechatAccountBindDao.getWxAppIdByEa(ea, platformId);
        if(wxAppId == null){
            return Result.newError(SHErrorCode.EA_NOT_BIND_TO_MINIAPP);
        }
        GetBoundMiniappInfoResult result = new GetBoundMiniappInfoResult();
        result.setEa(ea);
        result.setWxAppId(wxAppId);

        String realWxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        if (WxAppInfoEnum.isMankeep(realWxAppId)) {
            result.setRealAppType(MiniappTypeEnum.MANKEEP.getType());
        } else if (WxAppInfoEnum.isMankeepPro(realWxAppId)) {
            result.setRealAppType(MiniappTypeEnum.MARKETING.getType());
        } else {
            result.setRealAppType(MiniappTypeEnum.SELF_BUILD.getType());
        }
        //营销通的特殊逻辑处理
        if(MKThirdPlatformConstants.PLATFORM_ID.equals(platformId)){
            WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(wxAppId);
            if(wxAppInfoEnum != null){
                if(WxAppInfoEnum.Mankeep == wxAppInfoEnum){
                    result.setAppType(MiniappTypeEnum.MANKEEP.getType());
                }else{
                    result.setAppType(MiniappTypeEnum.MARKETING.getType());
                }
                result.setNextReleaseActionType(NextReleaseActionTypeEnum.DO_NOTHING.getAction());
                return Result.newSuccess(result);
            }else{
                EnterpriseMetaConfigEntity enterpriseMetaConfig = enterpriseMetaConfigDao.getByEa(ea);
                result.setMiniappIntroductionPageType(enterpriseMetaConfig.getMiniappIntroductionSiteType() == null ? MiniappIntroductionSiteTypeEnum.DEFAULT.getType() : enterpriseMetaConfig.getMiniappIntroductionSiteType());
                result.setMiniappIntroductionPageId(enterpriseMetaConfig.getMiniappIntroductionSiteId());
            }
        }

        result.setAppType(MiniappTypeEnum.SELF_BUILD.getType());
        asyncUpdateWechatAccount(ea, platformId, wxAppId);
        WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(wxAppId);
        BeanUtils.copyProperties(wechatAccountConfig, result);
        if(!Strings.isNullOrEmpty(wechatAccountConfig.getBusinessInfo())){
            BusinessInfo businessInfo = GsonUtil.fromJson(wechatAccountConfig.getBusinessInfo(), BusinessInfo.class);
            result.setOpenPay(businessInfo.getOpenPay() != null && businessInfo.getOpenPay() != 0);
        }

        MiniappReleaseRecordEntity latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseAndAuditingRecord(wxAppId);
        if(latestReleaseRecord != null){
            if(latestReleaseRecord.getStatus() ==  MiniappReleaseStatusEnum.AUDITING.getStatus() && !StringUtils.equals("0", latestReleaseRecord.getAuditId())){
                GetAuditStatusArg getAuditStatusArg = new GetAuditStatusArg();
                getAuditStatusArg.setAuditId(latestReleaseRecord.getAuditId());
                log.info("audit arg:{} wxAppId:{}", getAuditStatusArg, wxAppId);
                GetAuditStatusResult getAuditStatusResult = codeManageService.getAuditStatus(wechatAccountManager.getAccessTokenByWxAppId(wxAppId), getAuditStatusArg);
                if(getAuditStatusResult.isSuccess()){
                    log.info("audit status:{} ea:{} wx_app_id:{} getAuditStatusResult:{}", getAuditStatusResult.getStatus(), ea, wxAppId, getAuditStatusResult);
                    if(getAuditStatusResult.getStatus() == GetAuditStatusResult.AuditStatus.SUCCESS){
                        miniappReleaseRecordDao.auditSuccess(wxAppId, new Date());
                        latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseRecord(wxAppId);
                    }
                    if(getAuditStatusResult.getStatus() == GetAuditStatusResult.AuditStatus.REJECTED || getAuditStatusResult.getStatus()  == GetAuditStatusResult.AuditStatus.CANCEL){
                        miniappReleaseRecordDao.auditFail(wxAppId, getAuditStatusResult.getReason(), GsonUtil.toJson(strToList(getAuditStatusResult.getScreenShot(), "|")), new Date());
                        latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseRecord(wxAppId);
                    }
                }
            }
            if(MiniappReleaseStatusEnum.listAllPendingStatus().contains(latestReleaseRecord.getStatus())){
                result.setNextReleaseActionType(NextReleaseActionTypeEnum.FINISH_PENDING_RELEASE_FIRST.getAction());
            }
            GetBoundMiniappInfoResult.ReleaseRecord releaseRecord = new GetBoundMiniappInfoResult.ReleaseRecord();
            BeanUtils.copyProperties(latestReleaseRecord, releaseRecord);
            if(!Strings.isNullOrEmpty(latestReleaseRecord.getScreenShots())){
                List<String> screenShotMaterialIds = GsonUtil.fromJson(latestReleaseRecord.getScreenShots(), ArrayList.class);
                releaseRecord.setScreenShotMaterialIds(screenShotMaterialIds);
            }
            result.setLatestReleaseRecord(releaseRecord);
        }

        GetCodeTemplateListResult getCodeTemplateListResult = codeTemplateService.getTemplateList(wechatThirdPlatformManager.getThirdPlatformAccessToken(platformId));
        log.info("getCodeTemplateListResult,ea: {} platformId: {} result: {}", ea, platformId, getCodeTemplateListResult);
        //从配置中心读取企业指定的版本;【如果配置中心有指定版本,则直接使用配置中心版本,绕过最新版本的逻辑】 by dingc
        Optional<GetCodeTemplateListResult.CodeTemplate> optionalLatestCodeTemplate = getConfigedTemplate(getCodeTemplateListResult, ea, platformId);

        if (optionalLatestCodeTemplate == null || !optionalLatestCodeTemplate.isPresent()) {
            //营销通处理最新版本的灰度情况
            optionalLatestCodeTemplate = getLatestCodeVersionByMarketingGrayConfig(ea, platformId, getCodeTemplateListResult);
            if (optionalLatestCodeTemplate == null || !optionalLatestCodeTemplate.isPresent()) {
                optionalLatestCodeTemplate = getCodeTemplateListResult.getLatestCodeTemplate();
            }
        }

        if (optionalLatestCodeTemplate.isPresent()) {
            result.setLatestCodeTemplateId(optionalLatestCodeTemplate.get().getTemplateId());
            result.setLatestCodeVersion(optionalLatestCodeTemplate.get().getUserVersion());
            result.setLatestCodeDescription(optionalLatestCodeTemplate.get().getUserDescription());
        }

        if(result.getNextReleaseActionType() == null){
            if(Strings.isNullOrEmpty(result.getLatestCodeVersion())){
                result.setNextReleaseActionType(NextReleaseActionTypeEnum.DO_NOTHING.getAction());
            }else{
                if(!Strings.isNullOrEmpty(result.getCurrentCodeVersion()) && GetCodeTemplateListResult.CodeTemplate.compareVersion(result.getLatestCodeVersion(), result.getCurrentCodeVersion()) <= 0){
                    result.setNextReleaseActionType(NextReleaseActionTypeEnum.DO_NOTHING.getAction());
                }else{
                    result.setNextReleaseActionType(NextReleaseActionTypeEnum.UPDATE_TO_LATEST_CODE_VERSION.getAction());
                }
            }
        }

        //服务通处理最新版本的灰度情况
        Optional<String> greyVersion = getLatestVersionGrey(ea, wxAppId, platformId);
        if (greyVersion.isPresent()){
            result.setLatestCodeVersion(greyVersion.get());
        }

        //小程序icon
        String url = result.getQrCodeUrl();
        String fsPath = picFsMappingDAO.getFsPathByRawPicUrl(url,"-100");
        if (fsPath == null){
            byte[] picBytes = fileV2Manager.getByteDataByUrl(result.getQrCodeUrl());
            if (picBytes != null){
                //上传文件服务器
                String ext = "jpg";
                if (url.contains("wx_fmt=gif")){
                    ext = "gif";
                }else if (url.contains("wx_fmt=jpeg")){
                    ext = "jpeg";
                }else if (url.contains("wx_fmt=png")){
                    ext = "png";
                }
                fsPath = fileManager.uploadFileToAWarehouse(ext, picBytes);
                if (fsPath != null){
                    PicFsMappingEntity entity = new PicFsMappingEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setRawPicUrl(url);
                    entity.setFsPath(fsPath);
                    entity.setSize(picBytes.length);
                    entity.setEa("-100");
                    picFsMappingDAO.insert(entity);
                }
            }
        }

        GetMiniAppAutoUpgradeStatusArg autoUpgradeStatusArg = new GetMiniAppAutoUpgradeStatusArg();
        autoUpgradeStatusArg.setPlatformId(platformId);
        autoUpgradeStatusArg.setWxAppId(wxAppId);
        result.setAutoUpgradeVersionStatus(getMiniAppAutoUpgradeStatus(ea, -10000, autoUpgradeStatusArg).getData());
        result.setQrCodeApath(fsPath);

        return Result.newSuccess(result);
    }

    public static int compareVersion(String versionCode1, String versionCode2) {
        List<Integer> intCodeVersionList1 = strCodeVersionToIntList(versionCode1);
        List<Integer> intCodeVersionList2 = strCodeVersionToIntList(versionCode2);
        // 处理列表为null的情况，统一返回合适的值
        if (intCodeVersionList1 == null && intCodeVersionList2 == null) {
            return 0;
        }
        if (intCodeVersionList1 == null) {
            return -1;
        }
        if (intCodeVersionList2 == null) {
            return 1;
        }
        // 取两个列表长度的较小值，按索引依次比较
        int minLength = Math.min(intCodeVersionList1.size(), intCodeVersionList2.size());
        for (int i = 0; i < minLength; i++) {
            int v1 = intCodeVersionList1.get(i);
            int v2 = intCodeVersionList2.get(i);
            if (v1 < v2) {
                return -1;
            }
            if (v1 > v2) {
                return 1;
            }
        }
        // 如果前面部分都相等，再比较长度来确定大小
        if (intCodeVersionList1.size() < intCodeVersionList2.size()) {
            return -1;
        }
        if (intCodeVersionList1.size() > intCodeVersionList2.size()) {
            return 1;
        }
        return 0;
    }

    private static List<Integer> strCodeVersionToIntList(String versionCode) {
        if (versionCode == null) {
            return null;
        }
        String[] parts = versionCode.split("\\.");
        List<Integer> result = new ArrayList<>();
        for (String part : parts) {
            try {
                result.add(Integer.parseInt(part));
            } catch (NumberFormatException e) {
                // 可以根据实际情况决定如何处理格式转换异常，这里简单跳过非法部分继续处理后续的
            }
        }
        return result;
    }
    @Override
    public Result<GetBoundMiniappInfoResult> getBoundMiniappInfoLightVersion(String ea, String platformId) {
        String wxAppId = eaWechatAccountBindDao.getWxAppIdByEa(ea, platformId);
        if(wxAppId == null){
            return Result.newError(SHErrorCode.EA_NOT_BIND_TO_MINIAPP);
        }
        GetBoundMiniappInfoResult result = new GetBoundMiniappInfoResult();
        WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(wxAppId);
        BeanUtils.copyProperties(wechatAccountConfig, result);
        result.setEa(ea);
        return Result.newSuccess(result);
    }

    private void asyncUpdateWechatAccount(String ea, String platformId, String wxAppId) {
        ThreadPoolUtils.execute(() -> wechatAccountManager.updateWechatAccountInfo(ea, platformId, wxAppId), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    private Optional<GetCodeTemplateListResult.CodeTemplate> getLatestCodeVersionByMarketingGrayConfig(String ea, String platformId, GetCodeTemplateListResult getCodeTemplateListResult) {
        if (!"YXT".equals(platformId)) {
            return Optional.empty();
        }
        if (StringUtils.isEmpty(marketingMiniappGrayConfig) || CollectionUtils.isEmpty(getCodeTemplateListResult.getTemplateList())) {
            return Optional.empty();
        }
        JSONObject newGrayConfig = null;
        try {
            newGrayConfig = JSONObject.parseObject(marketingMiniappGrayConfig);
        } catch (Exception e) {
            log.error("marketingMiniappGrayConfig paser error config:{}", marketingMiniappGrayConfig);
        }
        if (newGrayConfig == null) {
            return Optional.empty();
        }
        JSONObject grayConfig = new JSONObject();
        for (Map.Entry<String, Object> entry : newGrayConfig.entrySet()) {
            String[] eaArr = entry.getKey().split(",");
            for (String eaStr : eaArr) {
                grayConfig.put(eaStr, entry.getValue());
            }
        }
        List<GetCodeTemplateListResult.CodeTemplate> allCodeTemplate = getCodeTemplateListResult.getTemplateList().stream()
                .filter(o -> StringUtils.isNotBlank(o.getUserVersion()))
                .sorted((o1, o2) -> compareVersion(o1.getUserVersion(), o2.getUserVersion()))
                .collect(Collectors.toList());
        List<GetCodeTemplateListResult.CodeTemplate> reversedAllCodeTemplate = Lists.reverse(allCodeTemplate);
        // 逆序非灰度版本
        List<GetCodeTemplateListResult.CodeTemplate> releaseVersion = new ArrayList<>(reversedAllCodeTemplate);
        for (Map.Entry<String, Object> entry : grayConfig.entrySet()) {
            String versionRange = String.valueOf(entry.getValue()).trim();
            String[] versionRangArr = versionRange.split("-");
            String minGrayVersion = versionRangArr[0];
            String maxGrayVersion = versionRangArr[1];
            releaseVersion.removeIf(codeTemplate -> {
                String userVersion = codeTemplate.getUserVersion();
                return compareVersion(userVersion, minGrayVersion) >= 0 && compareVersion(maxGrayVersion, userVersion) >= 0;
            });
        }
        // 1. 若在灰度配置中企业, 取灰度配置中最新一个版本
        // 2. 若不在灰度配置中， 取非灰度版本中最新一个版本
        if (grayConfig.get(ea) != null) {
            String versionRange = grayConfig.getString(ea).trim();
            String[] versionRangArr = versionRange.split("-");
            String minGrayVersion = versionRangArr[0];
            String maxGrayVersion = versionRangArr[1];
            for (GetCodeTemplateListResult.CodeTemplate item : reversedAllCodeTemplate) {
                String userVersion = item.getUserVersion();
                if (compareVersion(userVersion, minGrayVersion) >= 0 && compareVersion(maxGrayVersion, userVersion) >= 0) {
                    return Optional.of(item);
                }
            }
        } else {
            return Optional.of(releaseVersion.get(0));
        }
        return Optional.empty();
    }

    //配置中心配置格式 configed_mini_program_version=FWT_fktest110_1.3.0;FWT_fktest111_1.3.1;FWT_all_1.3.1
    //如果配置中心
    private Optional<GetCodeTemplateListResult.CodeTemplate> getConfigedTemplate(GetCodeTemplateListResult getCodeTemplateListResult, String ea, String platformId) {
        try {
            if (StringUtils.isEmpty(configedMiniProgramVersion) || StringUtils.isEmpty(ea) || StringUtils.isEmpty(platformId)) {
                return Optional.empty();
            }

            if (getCodeTemplateListResult == null || CollectionUtils.isEmpty(getCodeTemplateListResult.getTemplateList())) {
                return Optional.empty();
            }

            String version = "";
            String defaultVersion = "";
            String prefix = String.format("%s_%s_", platformId, ea);
            String defaultPrefix = String.format("%s_%s_", platformId, "all");
            for (String config : StringUtils.split(configedMiniProgramVersion, ";")) {
                if (config.startsWith(prefix)) {
                    version = config.replace(prefix, "");
                }
                if (config.startsWith(defaultPrefix)) {
                    defaultVersion = config.replace(prefix, "");
                }
            }

            //没有对应配置,则直接返回空
            if (StringUtils.isEmpty(version) && StringUtils.isEmpty(defaultVersion)) {
                return Optional.empty();
            }


            //遍历从微信获取的版本号列表, 如果有能跟配置中心指定的版本匹配的, 则返回对应版本信息, 否则返回空
            String finalVersion = StringUtils.isEmpty(version)? defaultVersion : version;
            return getCodeTemplateListResult.getTemplateList().stream().filter(x -> finalVersion.equals(x.getUserVersion())).findFirst();

        } catch (Exception e) {
            log.error("getConfigedTemplate for ea: {}, platformId:{} error:", ea, platformId, e);
            return Optional.empty();
        }
    }

    private Optional<String> getLatestVersionGrey(String ea, String wxAppId, String platformId) {
        if(StringUtils.isEmpty(wxAppId)){
            return Optional.empty();
        }
        if (!"FWT".equals(platformId)){
            return Optional.empty();
        }
        if (StringUtils.isEmpty(fwtReleaseVersionGrey)){
            return Optional.empty();
        }

        // greyList中包含的企业返回最新版本，其他返回次新版本
        List<String> greyList = GsonUtil.fromJson(fwtReleaseVersionGrey, List.class);
        if (CollectionUtils.isEmpty(greyList) || greyList.contains("*") || greyList.contains(ea)){
            return Optional.empty();
        }

        WxThirdPlatformVersionResult showVersion = wxCloudRestManager.getPlatformShowVersion(platformId);
        if (showVersion == null) {
            GetCodeTemplateListResult getCodeTemplateListResult = codeTemplateService.getTemplateList(wechatThirdPlatformManager.getThirdPlatformAccessToken(platformId));
            List<GetCodeTemplateListResult.CodeTemplate> verions = getCodeTemplateListResult.getTemplateList();
            if (verions != null && verions.size() >=  2){
                return Optional.ofNullable(verions.get(verions.size() -2 ).getUserVersion());
            }
        }

        MiniappReleaseRecordEntity latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseRecord(wxAppId);
        if (latestReleaseRecord == null){
            return Optional.empty();
        }
        if (latestReleaseRecord.getStatus() != MiniappReleaseStatusEnum.AUDIT_SUCCESS.getStatus()){
            return Optional.empty();
        }
        if (greyList.contains(ea)) {
            MiniappReleaseRecordEntity alreadyReleasedLatestVersion = miniappReleaseRecordDao.getLatestAlreadyReleasedRecord(wxAppId);
            if (alreadyReleasedLatestVersion != null) {
                Optional.ofNullable(alreadyReleasedLatestVersion.getCodeVersion());
            }
        }



        return Optional.empty();
    }

    @Override
    public Result<GetSimpleBoundMiniappInfoResult> getSimpleBoundMiniappInfo(String ea, String platformId) {
        Preconditions.checkArgument(MKThirdPlatformConstants.PLATFORM_ID.equals(platformId));
        String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        GetSimpleBoundMiniappInfoResult getSimpleBoundMiniappInfoResult = new GetSimpleBoundMiniappInfoResult();
        getSimpleBoundMiniappInfoResult.setWxAppId(wxAppId);
        WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(wxAppId);
        if(wxAppInfoEnum != null){
            if(WxAppInfoEnum.Mankeep == wxAppInfoEnum){
                getSimpleBoundMiniappInfoResult.setAppType(MiniappTypeEnum.MANKEEP.getType());
            }else{
                getSimpleBoundMiniappInfoResult.setAppType(MiniappTypeEnum.MARKETING.getType());
            }
        }else{
            getSimpleBoundMiniappInfoResult.setAppType(MiniappTypeEnum.SELF_BUILD.getType());
        }
        return Result.newSuccess(getSimpleBoundMiniappInfoResult);
    }

    @Override
    public Result<String> commitCodeAndSubmitAudit(String ea, Integer userId, CommitCodeAndSubmitAuditArg arg) {
        String wxAppId = arg.getWxAppId();
        checkOperateAuthority(ea, arg.getPlatformId(), wxAppId);
        String lockKey = wxAppId + RedisManager.WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO;
        Preconditions.checkArgument(redisManager.lock(lockKey, 180), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_829));
        try {
            String accessToken = wechatAccountManager.getAccessTokenByWxAppId(wxAppId);

            MiniappReleaseRecordEntity latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseRecord(wxAppId);
            if (latestReleaseRecord != null && MiniappReleaseStatusEnum.listAllPendingStatus().contains(latestReleaseRecord.getStatus())) {
                redisManager.unLock(lockKey);
                return Result.newError(SHErrorCode.COMMIT_CODE_FAIL.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_836));
            }

            WxThirdPlatformDomainResult platformDomain = wechatThirdPlatformManager.getDomainByPlatformId(arg.getPlatformId());
            ModifyDomainResult modifyDomainResult = settingManager.modifyMiniAppDomain(platformDomain, accessToken);
            if (!modifyDomainResult.isSuccess()) {
                miniappReleaseRecordDao.commitCodeFail(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), modifyDomainResult.getErrMsg());
                return Result.newError(SHErrorCode.COMMIT_CODE_FAIL.getErrorCode(), modifyDomainResult.getErrMsg());
            }

            SetWebViewDomainResult setWebViewDomainResult = settingManager.setWebviewDomain(platformDomain.getWebViewDomain(), accessToken);
            if (!setWebViewDomainResult.isSuccess()) {
                miniappReleaseRecordDao.commitCodeFail(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), setWebViewDomainResult.getErrMsg());
                return Result.newError(SHErrorCode.COMMIT_CODE_FAIL.getErrorCode(), setWebViewDomainResult.getErrMsg());
            }

            //设置隐私
            Optional<WechatBaseResult> privacySettingResultOpt = settingManager.setPrivacySetting(ea, userId, accessToken);
            if (!privacySettingResultOpt.isPresent()){
                miniappReleaseRecordDao.commitCodeFail(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), setWebViewDomainResult.getErrMsg());
                return Result.newError(SHErrorCode.COMMIT_CODE_FAIL.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_856));
            }else if (!privacySettingResultOpt.get().isSuccess()){
                miniappReleaseRecordDao.commitCodeFail(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), setWebViewDomainResult.getErrMsg());
                return Result.newError(SHErrorCode.COMMIT_CODE_FAIL.getErrorCode(), privacySettingResultOpt.get().getErrMsg());
            }

            CommitCodeArg commitCodeArg = new CommitCodeArg();
            commitCodeArg.setTemplateId(arg.getCodeTemplateId());
            commitCodeArg.setUserVersion(arg.getCodeVersion());
            commitCodeArg.setUserDescription(arg.getCodeDescription());
            WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(wxAppId);
            boolean privacyApiNotUse = true; //默认，不需要使用隐私api
            Map<String, Object> extTopMap = new HashMap<>(1);
            extTopMap.put("extEnable", true);
            extTopMap.put("extAppid", wechatAccountConfig.getWxAppId());
            extTopMap.put("ext", new ExtAppInfo(wechatAccountConfig.getWxAppId(), wechatAccountConfig.getNickName(), wechatAccountConfig.getHeadImg(), host, ea));
            if (StringUtils.equals(arg.getPlatformId(), "DHT")) {
                extTopMap.put("requiredPrivateInfos", Lists.newArrayList("getLocation"));
                privacyApiNotUse = false;
            }

            //配置了需要地理位置信息的小程序
            if (StringUtils.isNotEmpty(miniProgramLocationAppid)){
                ArrayList<String> configs = GsonUtil.fromJson(miniProgramLocationAppid, ArrayList.class);
                if (configs != null && configs.contains(arg.getWxAppId())){
                    extTopMap.put("privacySetting", "getLocation");
                    privacyApiNotUse = false;
                }
            }
            commitCodeArg.setExtJson(GsonUtil.toJson(extTopMap));

            //新托管小程序，需要延迟提交
            miniappReleaseRecordDao.submitAuditSuccess(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), "0");
            boolean finalPrivacyApiNotUse = privacyApiNotUse;
            ThreadPoolUtils.executeWithNewThread("commitCodeAndSubmitAudit-" + wxAppId, () -> {
                try {
                    Map<String, Object> header = new HashMap<>();
                    header.put(READ_TIME_OUT, 60000);
                    log.info("commitCodeAndSubmitAudit start to commit code ea:{} wxAppId:{}", ea, wxAppId);
                    WechatBaseResult commitCodeResult = codeManageService.commit(header,accessToken, commitCodeArg);
                    if (!commitCodeResult.isSuccess()) {
                        log.info("commitCodeAndSubmitAudit commit failed ea:{} wxAppId:{} errMsg:{}", ea, wxAppId, commitCodeResult.getErrMsg());
                        miniappReleaseRecordDao.commitCodeFail(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), commitCodeResult.getErrMsg());
                        return;
                    }
                    log.info("commitCodeAndSubmitAudit commit code successfully ea:{} wxAppId:{} finalPrivacyApiNotUse:{}", ea, wxAppId, finalPrivacyApiNotUse);
                    settingManager.submitAuditDelay(arg, accessToken, wxAppId, finalPrivacyApiNotUse);
                } catch (InterruptedException e) {
                    log.error("submitAuditDelay e wxAppId:{} e:", wxAppId, e);
                    log.warn(appId + "小程序更新代码并审核出错：", e);
                }
            });
        } catch (Exception e) {
            log.warn(appId + "小程序更新代码并审核出错：", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR, e.toString());
        } finally {
            redisManager.unLock(lockKey);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<String> releaseCode(String ea, ReleaseCodeArg releaseCodeArg) {
        String wxAppId = releaseCodeArg.getWxAppId();
        String lockKey = wxAppId + RedisManager.WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO;
        Preconditions.checkArgument(redisManager.lock(lockKey, 180), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_829));
        try {
            checkOperateAuthority(ea, releaseCodeArg.getPlatformId(), wxAppId);
            MiniappReleaseRecordEntity latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseRecord(wxAppId);
            Preconditions.checkArgument(latestReleaseRecord != null && MiniappReleaseStatusEnum.AUDIT_SUCCESS.getStatus() == latestReleaseRecord.getStatus(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_915));
            WechatBaseResult releaseCodeResult = codeManageService.release(wechatAccountManager.getAccessTokenByWxAppId(wxAppId), new Object());
            if (releaseCodeResult.isSuccess() || 85052 == releaseCodeResult.getErrCode()) {
                miniappReleaseRecordDao.relaseSuccess(wxAppId, "success");
                wechatAccountConfigDao.releaseVersion(latestReleaseRecord.getWxAppId(), latestReleaseRecord.getCodeVersion(), latestReleaseRecord.getCodeDescription());
                return Result.newSuccess();
            } else {
                return Result.newError(SHErrorCode.RELEASE_CODE_FAIL.getErrorCode(), releaseCodeResult.getErrMsg());
            }
        } catch (Exception e) {
            log.warn(appId + "小程序发布代码出错：", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR, e.toString());
        } finally {
            redisManager.unLock(lockKey);
        }
    }

    @Override
    public Result<String> unAuthWxAppId(String ea, UnAuthWxAppArg unAuthArg) {
        if(WxAppInfoEnum.getByAppId(unAuthArg.getWxAppId()) != null){
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        wechatAccountManager.unAuthWxAppId(ea, unAuthArg);
        return Result.newSuccess();
    }

    @AllArgsConstructor
    @NoArgsConstructor
    public static final class ExtAppInfo{
        private String appId;
        private String appName;
        private String appHeadImage;
        private String host;
        private String ea;
    }

    @Override
    public Result<byte[]> getMaterialBytes(String ea, String wxAppId, String materialId) {
        checkOperateAuthority(ea, MKThirdPlatformConstants.PLATFORM_ID, wxAppId);
        String getMaterialUrl = "https://api.weixin.qq.com/cgi-bin/material/get_material?access_token=" + wechatAccountManager.getAccessTokenByWxAppId(wxAppId);
        Map<String, Object> body = new HashMap<>(1);
        body.put("media_id", materialId);
        byte[] bytes = httpManager.postBytesByUrl(getMaterialUrl, body);
        return Result.newSuccess(bytes);
    }

    private static List<String> strToList(String str, String separator){
        if(Strings.isNullOrEmpty(str)){
            return new ArrayList<>(0);
        }
        return Splitter.on(separator).splitToList(str).stream().filter(Objects::nonNull).map(String::trim).filter(s -> !Strings.isNullOrEmpty(s)).collect(Collectors.toList());
    }

    private void checkOperateAuthority(String ea, String platformId, String wxAppId){
        Preconditions.checkArgument(wxAppId.equals(eaWechatAccountBindDao.getWxAppIdByEa(ea, platformId)));
    }

    @Override
    public Result<List<RoleResult>> listRole(String ea) {
        String finalSystemRoles = I18nUtil.getSuitedLangText(systemRoles, systemRolesEN);
        List<RoleResult> systemRoleResults = GsonUtil.fromJson(finalSystemRoles, new TypeToken<List<RoleResult>>(){}.getType());
        String version = appVersionManager.getCurrentAppVersion(ea);
        if (StringUtils.equals(version, VersionEnum.DING_FREE_APP.getVersion())){
            return Result.newSuccess(systemRoleResults.stream().filter(role -> role.getRoleId().equals("super-administrator")).collect(Collectors.toList()));
        }
        return Result.newSuccess(systemRoleResults);
    }

    @Override
    public Result<List<UserRoleResult>> listUserRoles(String ea) {
        Map<String, String> roleIdToNameMap = listRole(ea).getData().stream().collect(Collectors.toMap(RoleResult::getRoleId, RoleResult::getRoleName, (v1, v2) -> v1));
        List<UserRoleEntity> userRoleEntities = userRoleDao.listByEa(ea);
        if (CollectionUtils.isEmpty(userRoleEntities)){
            return Result.newSuccess(new ArrayList<>());
        }

        List<Integer> employeeIds = userRoleEntities.stream().map(UserRoleEntity::getEmployeeId).collect(Collectors.toList());
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEmployeeIds(employeeIds);
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);
        batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        Map<Integer, EmployeeDto> employeeIdToEmployeeMap = null;
        if (batchGetEmployeeDtoResult != null && CollectionUtils.isNotEmpty(batchGetEmployeeDtoResult.getEmployeeDtos())){
            employeeIdToEmployeeMap = batchGetEmployeeDtoResult.getEmployeeDtos().stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, Function.identity()));
        }

        Map<Integer, UserRoleResult> result = new HashMap<>();
        if (employeeIdToEmployeeMap == null){
            employeeIdToEmployeeMap = new HashMap<>();
        }
        for (UserRoleEntity userRole : userRoleEntities) {
            EmployeeDto employee = employeeIdToEmployeeMap.get(userRole.getEmployeeId());
            result.putIfAbsent(userRole.getEmployeeId(), new UserRoleResult(userRole.getEmployeeId()));
            result.get(userRole.getEmployeeId()).pushRoleId(userRole.getRoleId());
            result.get(userRole.getEmployeeId()).pushRoleName(roleIdToNameMap.get(userRole.getRoleId()));
            if (employee != null){
                result.get(userRole.getEmployeeId()).setEmployeeName(employee.getName());
                result.get(userRole.getEmployeeId()).setEmployeeStatus(employee.getStatus().getValue());
            }
        }
        //没有开启权限插件
        if(!dataPermissionManager.getNewDataPermissionSetting(ea)){
            return Result.newSuccess(new ArrayList<>(result.values()));
        }

        Map<Integer, String> fsDepartmentNameMap = null;
        Map<Integer, String> qywxDepartmentNameMap = null;
        Map<String, String> officialAccountMap = Maps.newHashMap();
        Map<String, String> advertisementMap = Maps.newHashMap();
        Map<String, String> officialWebsiteMap = Maps.newHashMap();

        //获取当前企业绑定的公众号
        CompanyWxOfficialAccountsListArg wxOfficialAccountsListArg = new CompanyWxOfficialAccountsListArg();
        wxOfficialAccountsListArg.setEa(ea);
        wxOfficialAccountsListArg.setFsUserId(1000);
        Result<List<AccountDataListResult>> accountsList = wxOfficialAccountsService.getCompanyWxOfficialAccountsList(wxOfficialAccountsListArg);
        if(accountsList.isSuccess() && CollectionUtils.isNotEmpty(accountsList.getData())){
            officialAccountMap = accountsList.getData().stream().collect(Collectors.toMap(AccountDataListResult::getWxAppId, AccountDataListResult::getWxAppName));
        }

        //获取当前企业绑定的官网
        List<OfficialWebsiteEntity> websiteEntities = officialWebsiteDAO.listNormalOfficialWebsiteByEa(ea);
        if (CollectionUtils.isNotEmpty(websiteEntities)){
            officialWebsiteMap = websiteEntities.stream().collect(Collectors.toMap(OfficialWebsiteEntity::getId, OfficialWebsiteEntity::getWebsiteName));
        }

        //获取当前企业绑定的广告
        List<AdAccountEntity> adAccountEntities = adAccountManager.queryEnableAccountByEa(ea);
        if (CollectionUtils.isNotEmpty(adAccountEntities)){
            advertisementMap = adAccountEntities.stream().collect(Collectors.toMap(AdAccountEntity::getId, AdAccountEntity::getUsername));
        }
        //获取企微通讯录
        qywxDepartmentNameMap = qywxManager.getAllDepartmentName(ea);

        //获取fs通讯录
        GetAllDepartmentNameArg nameArg = new GetAllDepartmentNameArg();
        nameArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        nameArg.setRunStatus(RunStatus.ALL);
        GetAllDepartmentNameResult name = departmentProviderService.getAllDepartmentName(nameArg);
        fsDepartmentNameMap = name.getDepartments().stream().collect(Collectors.toMap(DepartmentName::getDepartmentId, DepartmentName::getName, (v1, v2)->v2));

        Map<Integer, UserAccessibleEntity> userAccessibleMap = new HashMap<>();
        List<UserAccessibleEntity> userAccessibleEntities = userAccessibleDAO.getByEaAndUserIds(ea, Sets.newHashSet(employeeIds));
        userAccessibleMap = userAccessibleEntities.stream().collect(Collectors.toMap(UserAccessibleEntity::getUserId, Function.identity()));
        officialAccountMap.put(defaultAllChannel, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
        officialWebsiteMap.put(defaultAllChannel, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
        advertisementMap.put(defaultAllChannel, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
        Map<Integer, String> finalQywxDepartmentNameMap = qywxDepartmentNameMap;
        Map<Integer, String> finalFsDepartmentNameMap = fsDepartmentNameMap;
        Map<String, String> finalOfficialAccountMap = officialAccountMap;
        Map<String, String> finalOfficialWebsiteMap = officialWebsiteMap;
        Map<String, String> finalAdvertisementMap = advertisementMap;
        Map<Integer, UserAccessibleEntity> finalUserAccessibleMap = userAccessibleMap;
        EnterpriseChannelListResult defaultChannelListResult = listChannels(ea);
        result.forEach((k, v) -> {
            UserAccessibleEntity userAccessibleEntity = finalUserAccessibleMap.get(k);
            if (userAccessibleEntity != null ){
                //自定义权限
                if(Objects.equals(AccessScopeEnum.ACCESS_SCOPE_CUSTOM.getType(), userAccessibleEntity.getAccessScope())){
                    result.get(k).setAccessScope(AccessScopeEnum.ACCESS_SCOPE_CUSTOM.getType());

                    //先设置权限后绑定企微userAccessibleEntity.getAccessibleQywxDepartments()是null
                    List<UserRoleResult.DepartmentInfo> qywxDepartmentInfos = userAccessibleEntity.getAccessibleQywxDepartments() != null ? userAccessibleEntity.getAccessibleQywxDepartments().stream().map(o -> {
                        UserRoleResult.DepartmentInfo departmentInfo = new UserRoleResult.DepartmentInfo();
                        departmentInfo.setDepartmentId(o);
                        departmentInfo.setDepartmentName(finalQywxDepartmentNameMap.get(o - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID));
                        return departmentInfo;
                    }).collect(Collectors.toList()) : Lists.newArrayList();
                    result.get(k).setQywxDepartmentRange(qywxDepartmentInfos);

                    List<UserRoleResult.DepartmentInfo> fsDepartmentInfos = userAccessibleEntity.getAccessibleFsDepartments().stream().map(o->{
                        UserRoleResult.DepartmentInfo departmentInfo = new UserRoleResult.DepartmentInfo();
                        departmentInfo.setDepartmentId(o);
                        departmentInfo.setDepartmentName(finalFsDepartmentNameMap.get(o));
                        return departmentInfo;
                    }).collect(Collectors.toList());
                    result.get(k).setFsDepartmentRange(fsDepartmentInfos);
                }else {
                    result.get(k).setAccessScope(AccessScopeEnum.ACCESS_SCOPE_ALL.getType());
                }
                AccessChannel accessChannel = userAccessibleEntity.getAccessChannel();
                if (accessChannel != null){
                    EnterpriseChannelListResult channelListResult = new EnterpriseChannelListResult();
                    result.get(k).setChannelListResult(channelListResult);

                    //公众号
                    List<EnterpriseChannelListResult.ChannelInfo> officialAccountChannelInfo = accessChannel.getOfficialAccount().stream().filter(o -> finalOfficialAccountMap.containsKey(o.getChannelId())).map(o -> {
                        String channelName = finalOfficialAccountMap.get(o.getChannelId());
                        return new EnterpriseChannelListResult.ChannelInfo(o.getChannelId(),channelName, o.getStatus());
                    }).collect(Collectors.toList());
                    channelListResult.setOfficialAccount(officialAccountChannelInfo);

                    //官网
                    List<EnterpriseChannelListResult.ChannelInfo> officialWebsiteChannelInfo = accessChannel.getOfficialWebsite().stream().filter(o -> finalOfficialWebsiteMap.containsKey(o.getChannelId())).map(o -> {
                        String channelName = finalOfficialWebsiteMap.get(o.getChannelId());
                        return new EnterpriseChannelListResult.ChannelInfo(o.getChannelId(),channelName, o.getStatus());
                    }).collect(Collectors.toList());
                    channelListResult.setOfficialWebsite(officialWebsiteChannelInfo);

                    //广告
                    Map<String, List<EnterpriseChannelListResult.ChannelInfo>> advertisementChannelInfo = new HashMap<>();
                    accessChannel.getAdvertisement().forEach((k1, v1) -> {
                        List<EnterpriseChannelListResult.ChannelInfo> channelInfoList = v1.stream().filter(o -> finalAdvertisementMap.containsKey(o.getChannelId())).map(o -> {
                            String channelName = finalAdvertisementMap.get(o.getChannelId());
                            return new EnterpriseChannelListResult.ChannelInfo(o.getChannelId(),channelName, o.getStatus());
                        }).collect(Collectors.toList());
                        advertisementChannelInfo.put(k1, channelInfoList);
                    });
                    channelListResult.setAdvertisement(advertisementChannelInfo);
                }
            }else {
                result.get(k).setAccessScope(AccessScopeEnum.ACCESS_SCOPE_ALL.getType());
                result.get(k).setChannelListResult(defaultChannelListResult);
            }
        });
        return Result.newSuccess(new ArrayList<>(result.values()));
    }

    @Override
    public Result<Void> addUserRoles(String ea, Set<Integer> employeeIds, Set<String> roleIds) {
        for (String roleId : roleIds) {
            userRoleDao.batchInsertIgnoreByEmployeeIds(ea, roleId, employeeIds);
        }
        if(roleIds.contains(SYSTEM_ADMIN_ROLE_ID)){
            doAddEmployeesToAppAdmin(ea, employeeIds);
        }

        doAddEmployeeToComponentView(ea, marketingAppWebComponentId, AppComponentTypeEnum.WEB, employeeIds);
        doAddEmployeeToComponentView(ea, marketingAppAppComponentId, AppComponentTypeEnum.APP, employeeIds);
        if (roleIds.contains(RoleConstant.MEMBER_OPERATION)){
            settingManager.addUsersToPaasMemberManager(ea, employeeIds);
        }

        if (roleIds.contains(RoleConstant.PARTNER_MARKET_PLANNING)){
            List<Integer> addList = new ArrayList<>(employeeIds);
            settingManager.addAdminRoleForPartnerApp(addList, ea);
        }
        //将员工添加到CRM角色
        addUserRoleToCrmRole(ea, employeeIds, roleIds);

        return Result.newSuccess();
    }

    private void addUserRoleToCrmRole(String ea, Set<Integer> employeeIds, Set<String> roleIds){
        if (CollectionUtils.isEmpty(employeeIds) || CollectionUtils.isEmpty(roleIds)){
            return;
        }

        if (roleIds.contains(SYSTEM_ADMIN_ROLE_ID)){
            userRoleManager.addToCrmManagerManagerUserRole(ea, Lists.newArrayList(employeeIds));
        }

        if (roleIds.contains(WECHAT_OPERATION)){
            userRoleManager.addToCrmOfficialAccountRole(ea, Lists.newArrayList(employeeIds));
        }
        if (roleIds.contains(RoleConstant.ADVERTISING_OPERATION)){
            userRoleManager.addToCrmAdAccountRole(ea, Lists.newArrayList(employeeIds));
        }
        if (roleIds.contains(RoleConstant.WECHAT_WORK_OPERATION)){
            userRoleManager.addToCrmQywxAccountRole(ea, Lists.newArrayList(employeeIds));
        }

        if (!(roleIds.contains(SYSTEM_ADMIN_ROLE_ID) && roleIds.size() == 1)){
            userRoleManager.addToCrmManagerCommonUserRole(ea, Lists.newArrayList(employeeIds));
        }
    }

    @Override
    public Result<Void> editUserRoles(String ea, Integer employeeId, Set<String> roleIds) {
        List<String> existedRoleIds = userRoleDao.listByEmployeeId(ea, employeeId);
        Set<String> roleIdsToRemove = new HashSet<>(existedRoleIds);
        roleIdsToRemove.removeAll(roleIds);
        if (!roleIdsToRemove.isEmpty()){
            userRoleDao.deleteByEmployeeIdAndRoleIds(ea, employeeId, roleIdsToRemove);
        }
        Set<String> roleIdsToAdd = new HashSet<>(roleIds);
        existedRoleIds.forEach(roleIdsToAdd::remove);
        if(!roleIdsToAdd.isEmpty()){
            userRoleDao.batchInsertIgnoreByRoleIds(ea, employeeId, roleIdsToAdd);
        }

        //更新用户的权限
        List<String> currentRoleIds = userRoleDao.listByEmployeeId(ea, employeeId);
        userRoleManager.updateMarketingUserRole(ea, currentRoleIds, employeeId);

        if(roleIdsToRemove.contains(SYSTEM_ADMIN_ROLE_ID)){
            doRemoveEmployeeToAppAdmin(ea, ImmutableSet.of(employeeId));
        }

        if(roleIdsToAdd.contains(SYSTEM_ADMIN_ROLE_ID)){
            doAddEmployeesToAppAdmin(ea, ImmutableSet.of(employeeId));
        }

        doAddEmployeeToComponentView(ea, marketingAppWebComponentId, AppComponentTypeEnum.WEB, ImmutableSet.of(employeeId));
        doAddEmployeeToComponentView(ea, marketingAppAppComponentId, AppComponentTypeEnum.APP, ImmutableSet.of(employeeId));
    
        if (roleIdsToAdd.contains(RoleConstant.MEMBER_OPERATION) && memberManager.isOpenMember(ea)){
            settingManager.addUsersToPaasMemberManager(ea, ImmutableSet.of(employeeId));
        }
        if (roleIdsToRemove.contains(RoleConstant.MEMBER_OPERATION) && memberManager.isOpenMember(ea)){
            settingManager.removeUsersFromPaasMemberManager(ea, ImmutableSet.of(employeeId));
        }
        if (roleIdsToAdd.contains(RoleConstant.WECHAT_WORK_OPERATION)){
            settingManager.addUsersToPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.QYWX_ACCOUNT_MARKETING_ROLE_CODE);
        }
        if (roleIdsToRemove.contains(RoleConstant.WECHAT_WORK_OPERATION)){
            settingManager.removeUsersFromPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.QYWX_ACCOUNT_MARKETING_ROLE_CODE);
        }
        if (roleIdsToAdd.contains(RoleConstant.ADVERTISING_OPERATION) ){
            settingManager.addUsersToPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.AD_ACCOUNT_MARKETING_ROLE_CODE);
        }
        if (roleIdsToRemove.contains(RoleConstant.ADVERTISING_OPERATION)){
            settingManager.removeUsersFromPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.AD_ACCOUNT_MARKETING_ROLE_CODE);
        }
        if (roleIdsToAdd.contains(RoleConstant.PARTNER_MARKET_PLANNING)) {
            List curUserList = new ArrayList();
            curUserList.add(employeeId);
            settingManager.addAdminRoleForPartnerApp(curUserList, ea);
        }
        if (roleIdsToRemove.contains(RoleConstant.PARTNER_MARKET_PLANNING)) {
            settingManager.removeAdminRoleForPartnerApp(Arrays.asList(employeeId), ea);
        }
        if (roleIdsToAdd.contains(RoleConstant.MARKETING_SDR) || roleIdsToRemove.contains(RoleConstant.MARKETING_SDR)) {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(ei).operatorId(SuperUserConstants.USER_ID).build();
            QueryAllRoleInfoByRoleCodesVo.Argument queryArgument = new QueryAllRoleInfoByRoleCodesVo.Argument();
            Set<String> roleCodes = new HashSet<>();
            roleCodes.add(UserRoleManager.SDR_ROLE_CODE);
            queryArgument.setRoleCodes(roleCodes);
            List<RoleVo> roleVoList = rolePrivilegeRestService.queryAllRoleInfoByRoleCodes(privilegeContext, queryArgument);
            if (CollectionUtils.isNotEmpty(roleVoList)) {
                if (roleIdsToAdd.contains(RoleConstant.MARKETING_SDR) ) {
                    settingManager.addUsersToPaasManager(ea, ImmutableSet.of(employeeId), UserRoleManager.SDR_ROLE_CODE);
                } else {
                    settingManager.removeUsersFromPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.SDR_ROLE_CODE);
                }
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteUserRoleByUser(String ea, Integer employeeId) {
        List<String> existedRoleIds = userRoleDao.listByEmployeeId(ea, employeeId);
        userRoleDao.deleteByEmployeeId(ea, employeeId);
        if (existedRoleIds.contains(SYSTEM_ADMIN_ROLE_ID)){
            doRemoveEmployeeToAppAdmin(ea, ImmutableSet.of(employeeId));
        }
        doRemoveEmployeeToComponentView(ea, marketingAppWebComponentId, AppComponentTypeEnum.WEB, ImmutableSet.of(employeeId));
        doRemoveEmployeeToComponentView(ea, marketingAppAppComponentId, AppComponentTypeEnum.APP, ImmutableSet.of(employeeId));
        if (existedRoleIds.contains(RoleConstant.MEMBER_OPERATION)){
            settingManager.removeUsersFromPaasMemberManager(ea, ImmutableSet.of(employeeId));
        }
        if (existedRoleIds.contains(RoleConstant.ADVERTISING_OPERATION)){
            settingManager.removeUsersFromPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.AD_ACCOUNT_MARKETING_ROLE_CODE);
        }
        if (existedRoleIds.contains(RoleConstant.WECHAT_WORK_OPERATION)){
            settingManager.removeUsersFromPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.QYWX_ACCOUNT_MARKETING_ROLE_CODE);
        }

        //删除员工在CRM的营销通所有角色
        List<Integer> employeeIds = Lists.newArrayList();
        employeeIds.add(employeeId);
        //List<String> crmRoleCodes = Lists.newArrayList(UserRoleManager.MARKETING_MANAGER_ROLE_CODE, UserRoleManager.MARKETING_USER_ROLE_CODE, UserRoleManager.OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
        List<String> crmRoleCodes = Lists.newArrayList(UserRoleManager.MARKETING_MANAGER_ROLE_CODE, UserRoleManager.MARKETING_USER_ROLE_CODE, UserRoleManager.OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE,UserRoleManager.QYWX_ACCOUNT_MARKETING_ROLE_CODE,UserRoleManager.AD_ACCOUNT_MARKETING_ROLE_CODE);
        userRoleManager.removeMarketingRoleByUser(ea, crmRoleCodes, employeeId);
        settingManager.removeAdminRoleForPartnerApp(Arrays.asList(employeeId),ea);
        userAccessibleDAO.deleteByEaAndUserId(ea, employeeId);
        return Result.newSuccess();
    }

    @Override
    public Result<Set<String>> getUserRoles(String ea, Integer employeeId) {
        return Result.newSuccess(ImmutableSet.copyOf(userRoleDao.listByEmployeeId(ea, employeeId)));
    }

    @Override
    public Result<Void> deleteUserRole(String ea, Integer employeeId, Collection<String> roleIds) {
        if (!roleIds.isEmpty()){
            userRoleDao.deleteByEmployeeIdAndRoleIds(ea, employeeId, roleIds);
            if (roleIds.contains(SYSTEM_ADMIN_ROLE_ID)){
                doRemoveEmployeeToAppAdmin(ea, ImmutableSet.of(employeeId));
            }
            if(userRoleDao.listByEmployeeId(ea, employeeId).isEmpty()){
                doRemoveEmployeeToComponentView(ea, marketingAppWebComponentId, AppComponentTypeEnum.WEB, ImmutableSet.of(employeeId));
                doRemoveEmployeeToComponentView(ea, marketingAppAppComponentId, AppComponentTypeEnum.APP, ImmutableSet.of(employeeId));
            }
            if (roleIds.contains(RoleConstant.MEMBER_OPERATION)){
                settingManager.removeUsersFromPaasMemberManager(ea, ImmutableSet.of(employeeId));
            }
            if (roleIds.contains(RoleConstant.ADVERTISING_OPERATION)){
                settingManager.removeUsersFromPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.AD_ACCOUNT_MARKETING_ROLE_CODE);
            }
            if (roleIds.contains(RoleConstant.WECHAT_WORK_OPERATION)){
                settingManager.removeUsersFromPaasManager(ea, ImmutableSet.of(employeeId),UserRoleManager.QYWX_ACCOUNT_MARKETING_ROLE_CODE);
            }
        }

        List<String> currentRoles = userRoleDao.listByEmployeeId(ea, employeeId);
        userRoleManager.updateMarketingUserRole(ea, currentRoles, employeeId);

        return Result.newSuccess();
    }
    
    @Override
    public Result<Void> setMiniappIntroductionSite(String ea, SetMiniappIntroductionSiteConfigArg arg) {
        enterpriseMetaConfigDao.updateMiniappIntroductionSite(ea, arg.getMiniappIntroductionSiteType(), arg.getMiniappIntroductionSiteId());
        return Result.newSuccess();
    }

    private void doAddEmployeesToAppAdmin(String ea, Set<Integer> employeeIds) {
        BaseResult<List<Integer>> br = openAppAdminService.getAppAdminIds(ea, appId);
        if (br.isSuccess()){
            Set<Integer> employeeIdToAdd = new HashSet<>(employeeIds);
            br.getResult().forEach(employeeIdToAdd::remove);
            if(!employeeIdToAdd.isEmpty()){
                openAppAdminService.addAppAdminIds(new FsUserVO(ea, 1000), appId, employeeIdToAdd);
            }
        }
    }

    private void doRemoveEmployeeToAppAdmin(String ea, Set<Integer> employeeIds) {
        BaseResult<List<Integer>> br = openAppAdminService.getAppAdminIds(ea, appId);
        if (br.isSuccess()){
            Set<Integer> employeeIdToRemove = new HashSet<>(employeeIds);
            employeeIdToRemove.retainAll(br.getResult());
            if(!employeeIdToRemove.isEmpty()){
                openAppAdminService.removeAppAdminIds(new FsUserVO(ea, 1000), appId, employeeIdToRemove);
            }
        }
    }

    private void doAddEmployeeToComponentView(String ea, String componentId, AppComponentTypeEnum appComponentType, Set<Integer> employeeIds){
        if (employeeIds == null || employeeIds.isEmpty()){
            return;
        }
        BaseResult<EmployeeRange> employeeRangeBaseResult = openFsUserAppViewService.loadComponentView(new FsUserVO(ea, 1000), componentId);
        if (employeeRangeBaseResult.isSuccess()) {
            List<Integer> existedDepartmentIds = employeeRangeBaseResult.getResult().getDepartment() == null ? new ArrayList<>(0) : employeeRangeBaseResult.getResult().getDepartment();
            if (existedDepartmentIds.contains(999999)){
                return;
            }
            List<Integer> existedEmployeeIds = employeeRangeBaseResult.getResult().getMember() == null ? new ArrayList<>(0) : employeeRangeBaseResult.getResult().getMember();
            Set<Integer> employeeIdToAdd = new HashSet<>(employeeIds);
            existedEmployeeIds.forEach(employeeIdToAdd::remove);
            Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, new ArrayList<>(employeeIdToAdd), false);
            for (FsAddressBookManager.FSEmployeeMsg employeeMsg : employeeMap.values()) {
                if (employeeMsg != null && employeeMsg.getDepartmentIds() != null){
                    for (Integer departmentId : employeeMsg.getDepartmentIds()) {
                        if(existedDepartmentIds.contains(departmentId)){
                            employeeIdToAdd.remove(employeeMsg.getEmployeeId());
                        }
                    }
                }
            }
            if (!employeeIdToAdd.isEmpty()){
                employeeIdToAdd.addAll(existedEmployeeIds);
                AppViewDO appViewDO = new AppViewDO();
                Integer[] existedDepartmentArray = new Integer[existedDepartmentIds.size()];
                existedDepartmentIds.toArray(existedDepartmentArray);
                appViewDO.setDepartment(existedDepartmentArray);
                Integer[] existedEmployeeIdArray = new Integer[employeeIdToAdd.size()];
                employeeIdToAdd.toArray(existedEmployeeIdArray);
                appViewDO.setMember(existedEmployeeIdArray);
                openFsUserAppViewService.saveFsUserAppViewList(new FsUserVO(ea, 1000), componentId, appComponentType, appViewDO);
            }
        }
    }

    private void doRemoveEmployeeToComponentView(String ea, String componentId, AppComponentTypeEnum appComponentType, Set<Integer> employeeIds){
        BaseResult<EmployeeRange> employeeRangeBaseResult = openFsUserAppViewService.loadComponentView(new FsUserVO(ea, 1000), componentId);
        if (employeeRangeBaseResult.isSuccess()) {
            List<Integer> existedDepartmentIds = employeeRangeBaseResult.getResult().getDepartment() == null ? new ArrayList<>(0) : employeeRangeBaseResult.getResult().getDepartment();
            List<Integer> existedEmployeeIds = employeeRangeBaseResult.getResult().getMember() == null ? new ArrayList<>(0) : employeeRangeBaseResult.getResult().getMember();
            Set<Integer> employeeIdToRemove = new HashSet<>(existedEmployeeIds);
            employeeIdToRemove.removeAll(employeeIds);
            if (employeeIdToRemove.size() != existedEmployeeIds.size()){
                AppViewDO appViewDO = new AppViewDO();
                Integer[] existedDepartmentArray = new Integer[existedDepartmentIds.size()];
                existedDepartmentIds.toArray(existedDepartmentArray);
                appViewDO.setDepartment(existedDepartmentArray);
                Integer[] existedEmployeeIdArray = new Integer[employeeIdToRemove.size()];
                employeeIdToRemove.toArray(existedEmployeeIdArray);
                appViewDO.setMember(existedEmployeeIdArray);
                openFsUserAppViewService.saveFsUserAppViewList(new FsUserVO(ea, 1000), componentId, appComponentType, appViewDO);
            }
        }
    }
    
    @Override
    public Result<PageResult<SystemOperationRecordVO>> pageListOperationRecord(String ea, PageListOperationRecordArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getModule()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getModuleId()));
        Page<Object> page = new Page<>(arg.getPageNo(), arg.getPageSize(), true);
        List<SystemOperationRecordEntity> records = systemOperationRecordDao.listByModuleTargetIdAndOperationType(ea, arg.getModule(), arg.getModuleId(), null, page);
        List<SystemOperationRecordVO> recordVOS = records.stream().map(r -> {
            SystemOperationRecordVO vo = new SystemOperationRecordVO();
            BeanUtils.copyProperties(r, vo);
            return vo;
        }).collect(Collectors.toList());
        return Result.newSuccess(new PageResult<>(page.getTotalNum(), recordVOS));
    }

    @Override
    public Result<ListParamsBySceneAndSpreadTypeResult> listParamsBySceneAndSpreadType(String ea, ListParamsBySceneAndSpreadTypeArg arg) {
        ListParamsBySceneAndSpreadTypeResult result = settingManager.listParamsBySceneAndSpreadType(ea, arg.getSpreadType(), arg.getSceneType(), arg.getChannel());
        return Result.newSuccess(result);
    }

    @Override
    public Result setMarketingUserGroupCustomizeObjectMapping(String ea, Integer fsUserId, MarketingUserGroupCustomizeObjectMappingArg arg) {
        Set<String> objectApiNameSet = new HashSet<>();
        objectApiNameSet.add(arg.getObjectApiName());
        List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (CollectionUtils.isNotEmpty(objectMappingEntityList)){
            objectApiNameSet.addAll(objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toSet()));
        }
        if (objectApiNameSet.size() > calCustomizeObjectMappingCount(ea)){
            Result.newError(SHErrorCode.GROUP_WITH_CUSTOMIZE_OBJECT_BEYOND_LIMIT);
        }

        MarketingUserGroupCustomizeObjectMappingEntity entity = new MarketingUserGroupCustomizeObjectMappingEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setCreateBy(fsUserId);
        entity.setFieldDataCommonMapping(arg.getCustomizeObjectMappings());
        entity.setObjectApiName(arg.getObjectApiName());
        entity.setObjectName(arg.getObjectName());
        int ret = marketingUserGroupCustomizeObjectMappingDao.insert(entity);
        if (ret == 1){
            return Result.newSuccess();
        }else {
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
    }

    private int calCustomizeObjectMappingCount(String ea){
        //专业版和旗舰版支持3个，其他按资源收费，限制限制专业版3个
        String version = appVersionManager.getCurrentAppVersion(ea);
        if (StringUtils.equals(version, VersionEnum.PRO.getVersion())
                || StringUtils.equals(version, VersionEnum.STREN.getVersion())){
           return 3;
        }


        return 0;
    }

    @Override
    public Result<List<MarketingUserGroupCustomizeObjectMappingResult>> getMarketingUserGroupCustomizeObjectMapping(String ea) {
        List<MarketingUserGroupCustomizeObjectMappingEntity> entityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (CollectionUtils.isEmpty(entityList)){
            return Result.newSuccess();
        }

        com.facishare.marketing.common.result.Result<GetRelatedDataListResult> relatedDataListResult = crmV2Manager.findDescribField(ea, -10000, false);
        Map<String, String> apiDisplayNameMap = new HashMap<>();
        if (relatedDataListResult.isSuccess() && relatedDataListResult.getData() != null && CollectionUtils.isNotEmpty(relatedDataListResult.getData().getResultList())) {
            relatedDataListResult.getData().getResultList().forEach(item -> {
                apiDisplayNameMap.put((String)item.get("api_name"), (String)item.get("display_name"));
            });
        }

        List<MarketingUserGroupCustomizeObjectMappingResult> mappingResults = Lists.newArrayList();
        entityList.forEach(entity ->{
            MarketingUserGroupCustomizeObjectMappingResult result = new MarketingUserGroupCustomizeObjectMappingResult();
            result.setId(entity.getId());
            result.setEa(ea);
            result.setObjectApiName(entity.getObjectApiName());

            if (apiDisplayNameMap.get(entity.getObjectApiName()) != null){
                result.setObjectName(apiDisplayNameMap.get(entity.getObjectApiName()));
            }else {
                result.setObjectName(entity.getObjectName());
            }
            result.setCustomizeObjectMappings(entity.getFieldDataCommonMapping());
            result.setCreateBy(entity.getCreateBy());
            result.setCreateTime(entity.getCreateBy().intValue());
            mappingResults.add(result);
        });

        return Result.newSuccess(mappingResults);
    }

    @Override
    public Result deleteMarketingUserGroupCustomizeObjectMapping(String id) {
        marketingUserGroupCustomizeObjectMappingDao.deleteById(id);
        return Result.newSuccess();
    }

    @Override
    public Result getH5AccessPermissionsSeeting(String objectId, Integer objectType, String ea) {
        if ((StringUtils.isBlank(objectId) || null == objectType) && StringUtils.isBlank(ea)) {
            log.warn("OuterWxOfficialAccountsController.getListByObjectInfo error param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        // 查询物料对应ea
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(ea)) {
            ea = objectManager.getObjectEa(objectId, objectType);
        }
        if (com.alibaba.dubbo.common.utils.StringUtils.isBlank(ea)) {
            log.warn("SettingServiceImpl.getH5AccessPermissionsSeeting ea is null ea:{}", ea);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        H5AccessPermissionsSeetingArg arg = new H5AccessPermissionsSeetingArg();
        H5AccessPermissionsSeetingEntity entity = h5AccessPermissionsSeetingDao.getByEa(ea);
        if (entity != null) {
            BeanUtils.copyProperties(entity, arg);
        }
        return Result.newSuccess(arg);
    }

    @Override
    public Result saveH5AccessPermissionsSeeting(String ea, Integer userId, H5AccessPermissionsSeetingArg arg) {
        H5AccessPermissionsSeetingEntity entity = h5AccessPermissionsSeetingDao.getByEa(ea);
        if (entity != null) {
            entity.setEa(ea);
            entity.setStatus(arg.getStatus());
            entity.setWxAppId(arg.getWxAppId());
            entity.setWxAppName(arg.getWxAppName());
            entity.setLastUpdateBy(userId);
            h5AccessPermissionsSeetingDao.update(entity);
        } else {
            entity = new H5AccessPermissionsSeetingEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setStatus(arg.getStatus());
            entity.setWxAppId(arg.getWxAppId());
            entity.setWxAppName(arg.getWxAppName());
            entity.setCreateBy(userId);
            h5AccessPermissionsSeetingDao.insert(entity);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<EnterpriseSettingsResult> getEnterpriseSettings(String ea, Integer userId) {
        EnterpriseSettingsResult result = new EnterpriseSettingsResult();
        result.setMobilePhotoLibrary(marketingPluginConfigManager.getCurrentPluginStatus(ea, MarketingPluginTypeEnum.MOBILE_PHOTO_LIBRARY.getType()));
        result.setEa(ea);
        result.setEi(eieaConverter.enterpriseAccountToId(ea));
        QywxSidebarMaterialSendSettingEntity entity = qywxSidebarMaterialSendSettingDAO.queryByEa(ea);
        result.setSidebarSendType(entity==null ? QywxSidebarSendTypeEnum.MINIAPP.getType() : entity.getSendType());
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<String>> getApiNameList(String ea){

        String apiNameList = redisManager.getGrayEaApiName(ea);
        if (Strings.isNullOrEmpty(apiNameList)) {
            if(fsViewProfileDTO!=null){
                String[] split = fsViewProfileDTO.getRule().split(";");
                if(Arrays.asList(split).contains(ea)){
                    redisManager.setGrayEaApiName(ea,JSON.toJSONString(fsViewProfileDTO.getV()));
                    return Result.newSuccess(fsViewProfileDTO.getV());
                }else{
                    redisManager.setGrayEaApiName(ea,JSON.toJSONString(new ArrayList<>()));
                    return Result.newSuccess(new ArrayList<>());
                }
            }
        }else {
            log.info("SettingServiceImpl -> getApiNameList from redis cache, apiNameList:{}", apiNameList);
        }
        return Result.newSuccess(JSON.parseArray(apiNameList,String.class));
    }

    @Override
    public Result<String> getSpreadContentDomain(String ea) {
        String host = fileV2Manager.getSpreadContentDomain(ea);
        return Result.newSuccess(host);
    }

    //删除缓存,重新获取配置文件并存最新的灰度设置
    @Override
    public Result getConfigProfile() {
        String key = "MARKETING_GRAY_EA_LISt_";
        redisManager.delete(key);
        //拉取配置文件并格式转换
        IConfig config = ConfigFactory.getInstance().getConfig("fs-web-view-profile");
        String yText = config.getString();
        Map<String, Object> map = yamlHandler(yText);
        fsViewProfileDTO = BeanUtil.copyByFastJson(map.get("openMarketingTagObjects"), FsViewProfileDTO.class);
        //存入缓存
        boolean result = redisManager.set(key, gs.toJson(fsViewProfileDTO), 3600 * 24);
        log.info("SettingServiceImpl -> getConfigProfile from redis cache, result:{}", result);
        return Result.newSuccess();
    }

    @Override
    public Result<String> queryTenantBrandColor(TenantBrandColorArg arg) {
        String ea = arg.getEa();
        if(StringUtils.isBlank(ea)){
            ea = objectManager.getObjectEa(arg.getObjectId(),arg.getObjectType());
        }
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetTenantBrandColorResult tenantBrandColor = new GetTenantBrandColorResult();
        try {
            tenantBrandColor = tenantBrandColorService.getTenantBrandColor(String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
            if (tenantBrandColor==null){
                return Result.newError(SHErrorCode.TENANT_BRAND_COLOR_NOT_FIND);
            }
        }catch (Exception e){
            log.warn("SettingServiceImpl queryTenantBrandColor error ea={} , e:",ea, e);
            return Result.newError(SHErrorCode.TENANT_BRAND_COLOR_NOT_FIND);
        }
        return Result.newSuccess(tenantBrandColor.getBrandColor());
    }

    @Override
    public Result<Integer> getVersionAuditStatus(String wxAppId, String platform) {
        MiniappReleaseRecordEntity latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseAndAuditingRecord(wxAppId);
        if (latestReleaseRecord == null){
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if(!StringUtils.equals("0", latestReleaseRecord.getAuditId())) {
            GetAuditStatusArg getAuditStatusArg = new GetAuditStatusArg();
            getAuditStatusArg.setAuditId(latestReleaseRecord.getAuditId());
            GetAuditStatusResult getAuditStatusResult = codeManageService.getAuditStatus(wechatAccountManager.getAccessTokenByWxAppId(wxAppId), getAuditStatusArg);
            if(getAuditStatusResult.isSuccess()){
                return Result.newSuccess(getAuditStatusResult.getStatus());
            }
        }

        return Result.newSuccess();
    }


    @Override
    public void sendMarketingReport(){
        List<String> eas = JSON.parseArray(marketingReportSendEaList, String.class);
        if (CollectionUtils.isEmpty(eas)){
            return;
        }
        log.info("SettingServiceImpl -> sendMarketingReport eas:{}", eas);
        eas.forEach(ea -> {
            ThreadPoolUtils.execute(() -> spreardReport(ea), ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
        });
    }


    @Override
    public void spreardReport(String ea){
        //获取企业管理员账号
        List<Integer> appAdmins = authManager.getAppAdmins(ea);
        if (CollectionUtils.isEmpty(appAdmins)){
            return;
        }
        //app发送
//        sendAppMessage(ea,appAdmins);
        sendAppMessageI18n(ea,appAdmins);

        Map<Integer, String> qywxUserIdMap = qywxUserManager.getQyUserIdByFsUserInfo(ea, appAdmins);
        if (MapUtils.isEmpty(qywxUserIdMap)) {
            return;
        }
        List<String> touser;
        touser = Lists.newArrayList(qywxUserIdMap.values());
        touser = touser.stream().distinct().collect(Collectors.toList());
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return ;
        }
        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), agentConfig.getEa());
        QywxCustomerAppInfoEntity appInfoEntity = null;
        if (qywxCustomerAppInfoEntity.size() != 0) {
            appInfoEntity = qywxCustomerAppInfoEntity.get(0);
        }
        if (appInfoEntity == null) {
            return ;
        }
        //营销助手发送
        sendSpreadQywxAgentppMessage(ea,appInfoEntity,touser);

    }

    private void sendAppMessage(String ea, List<Integer> touser){

        LocalDate today = new LocalDate();
        LocalDate lastWeekStart = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY);
        LocalDate lastWeekEnd = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.SUNDAY);
        //上周开始结束时间
        DateTime lastWeekBeginDate = lastWeekStart.toDateTimeAtStartOfDay();
        DateTime lastWeekEndDate = lastWeekEnd.toDateTimeAtStartOfDay().withTime(23, 59, 59, 999);

        String title = "营销运营周报("+ DateUtil.parse(lastWeekBeginDate.toDate(), DateTimeFormatter.ofPattern("MM.dd"))
                + "~" + DateUtil.parse(lastWeekEndDate.toDate(), DateTimeFormatter.ofPattern("MM.dd")) + ")";

        String description = "上周营销推广 "+getMarketingActivityCount(ea,lastWeekBeginDate.getMillis(),lastWeekEndDate.getMillis())
                +" 次，新增线索 "+getLeadsCount(ea,lastWeekBeginDate.getMillis(),lastWeekEndDate.getMillis())+" 条";

        Map<String, Object> paramMap = getParamMap(ea, lastWeekBeginDate, description, title, null);
        fsMessageManager.sendOpenMessage(appId, ea, touser, title, description, paramMap);
    }

    private Map<String, Object> getParamMap(String ea, DateTime lastWeekBeginDate, String description, String title, String uuid) {
        String domain = host;
        Optional<String> domainOpt = settingManager.getEnterpriseDomain(ea);
        if (domainOpt.isPresent()){
            domain = domainOpt.get();
        }
        //https://crm.ceshi112.com/proj/page/marketing?ea=88146#/spread/chart?lastWeekBeginDate=1700409600000
        String url =  domain + "/proj/page/marketing?ea="+ ea + "#/spread/chart?lastWeekBeginDate=" + lastWeekBeginDate.getMillis();

        String buttonText = "查看详情";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("createTime", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM月dd日"));
        if (StringUtils.isBlank(uuid)) {
            uuid = com.facishare.mankeep.common.util.UUIDUtil.getUUID();
        }
        paramMap.put("messageId", uuid);
        List<Map<String, Object>> imageTextListMap = new ArrayList<>();
        Map<String, Object> imageTextMap = new HashMap<>();
        imageTextMap.put("summary", description);
        imageTextMap.put("contentUrl", url);
        imageTextMap.put("imageUrl", "https://a2.fspage.com/image/548098/C_202311_21_29a0ff3778b64fe4912316ce909cf412");
        imageTextMap.put("title", title);
        imageTextMap.put("buttonText", buttonText);
        imageTextMap.put("buttonUrl", url);
        imageTextMap.put("contentType", "1");
        imageTextMap.put("messageType", "1");
        imageTextMap.put("contentTitle", title);
        imageTextMap.put("description", description);
        List<Map<String, Object>> contentListMap = new ArrayList<>();
        Map<String, Object> conductMap = new HashMap<>();
        conductMap.put("宣传语：", description);
        contentListMap.add(conductMap);
        Map<String, Object> timeMap = new HashMap<>();
        timeMap.put("推广时间：", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM月dd日"));
        contentListMap.add(timeMap);
        imageTextMap.put("contentListMap", contentListMap);

        //推广简报统一h5
        NoticeManager.ParamResult paramResult = new NoticeManager.ParamResult();
        paramResult.setUrl(url);
        imageTextMap.put("customParamJson", GsonUtil.toJson(paramResult));
        imageTextListMap.add(imageTextMap);
        paramMap.put("imageTextList", imageTextListMap);
        log.info("NoticeService.sendEnterpriseSpreadNotice paramMap={}", paramMap);
        return paramMap;
    }

    public void sendAppMessageI18n(String ea, List<Integer> touser){

        LocalDate today = new LocalDate();
        LocalDate lastWeekStart = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY);
        LocalDate lastWeekEnd = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.SUNDAY);
        //上周开始结束时间
        DateTime lastWeekBeginDate = lastWeekStart.toDateTimeAtStartOfDay();
        DateTime lastWeekEndDate = lastWeekEnd.toDateTimeAtStartOfDay().withTime(23, 59, 59, 999);

        String title = "营销运营周报("+ DateUtil.parse(lastWeekBeginDate.toDate(), DateTimeFormatter.ofPattern("MM.dd"))
                + "~" + DateUtil.parse(lastWeekEndDate.toDate(), DateTimeFormatter.ofPattern("MM.dd")) + ")";

        String description = "上周营销推广 "+getMarketingActivityCount(ea,lastWeekBeginDate.getMillis(),lastWeekEndDate.getMillis())
                +" 次，新增线索 "+getLeadsCount(ea,lastWeekBeginDate.getMillis(),lastWeekEndDate.getMillis())+" 条";

        java.util.List<com.facishare.qixin.api.model.session.InternationalInfo> contentParameters = Lists.newArrayList();

        //营销运营周报({0}~{1}})
        com.facishare.qixin.api.model.session.InternationalInfo titleInternationalInfo = new com.facishare.qixin.api.model.session.InternationalInfo("qx.ot.mark.invitation_week_report",
                DateUtil.parse(lastWeekBeginDate.toDate(), DateTimeFormatter.ofPattern("MM.dd")),
                DateUtil.parse(lastWeekEndDate.toDate(), DateTimeFormatter.ofPattern("MM.dd")));

        //上周营销推广{0}次，新增线索{1}条
        com.facishare.qixin.api.model.session.InternationalInfo descriptionInternationalInfo = new com.facishare.qixin.api.model.session.InternationalInfo("qx.ot.mark.invitation_last_week_report",
                getMarketingActivityCount(ea, lastWeekBeginDate.getMillis(), lastWeekEndDate.getMillis()).toString(),
                getLeadsCount(ea, lastWeekBeginDate.getMillis(), lastWeekEndDate.getMillis()).toString());

        String domain = host;
        Optional<String> domainOpt = settingManager.getEnterpriseDomain(ea);
        if (domainOpt.isPresent()){
            domain = domainOpt.get();
        }
        //https://crm.ceshi112.com/proj/page/marketing?ea=88146#/spread/chart?lastWeekBeginDate=1700409600000
        String url =  domain + "/proj/page/marketing?ea="+ea + "#/spread/chart?lastWeekBeginDate=" + lastWeekBeginDate.getMillis();

        Map<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("createTime", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
        String uuid = com.facishare.mankeep.common.util.UUIDUtil.getUUID();
        paramMap.put("messageId", uuid);
        List<Map<String, Object>> imageTextListMap = new ArrayList<>();
        Map<String, Object> imageTextMap = new LinkedHashMap<>();
        imageTextMap.put("summary", "{0}");
        contentParameters.add(descriptionInternationalInfo);
        imageTextMap.put("contentUrl", url);
        imageTextMap.put("imageUrl", "https://a2.fspage.com/image/548098/C_202311_21_29a0ff3778b64fe4912316ce909cf412");
        imageTextMap.put("title", "{1}");
        contentParameters.add(titleInternationalInfo);
        imageTextMap.put("buttonUrl", url);
        imageTextMap.put("contentType", "1");
        imageTextMap.put("messageType", "1");
        imageTextMap.put("contentTitle", "{2}");
        imageTextMap.put("description", "{3}");
        contentParameters.add(descriptionInternationalInfo);
        contentParameters.add(descriptionInternationalInfo);
        List<Map<String, Object>> contentListMap = new ArrayList<>();
        Map<String, Object> conductMap = new LinkedHashMap<>();
        conductMap.put("{4}", "{5}");
        contentParameters.add(new com.facishare.qixin.api.model.session.InternationalInfo("qx.ot.mark.promotion_slogan"));
        contentParameters.add(descriptionInternationalInfo);
        contentListMap.add(conductMap);
        Map<String, Object> timeMap = new LinkedHashMap<>();
        timeMap.put("{6}", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
        contentParameters.add(new com.facishare.qixin.api.model.session.InternationalInfo("qx.ot.mark.promotion_time"));
        contentListMap.add(timeMap);
        imageTextMap.put("contentListMap", contentListMap);
        imageTextMap.put("buttonText", "{7}");
        contentParameters.add(new com.facishare.qixin.api.model.session.InternationalInfo("qx.ot.mark.view_detail"));

        //推广简报统一h5
        NoticeManager.ParamResult paramResult = new NoticeManager.ParamResult();
        paramResult.setUrl(url);
        imageTextMap.put("customParamJson", GsonUtil.toJson(paramResult));
        imageTextListMap.add(imageTextMap);
        paramMap.put("imageTextList", imageTextListMap);
        log.info("NoticeService.sendEnterpriseSpreadNotice paramMap={}", paramMap);
//        fsMessageManager.sendOpenMessage(appId, ea, touser, title, description, paramMap);
        touser = qywxUserManager.filterVirtualUser(touser);
        if (CollectionUtils.isEmpty(touser)) {
            return;
        }
        SendAppToCMessageVO sendAppToCMessageVO = new SendAppToCMessageVO();
        sendAppToCMessageVO.setAppId(appId);
        sendAppToCMessageVO.setPostId(uuid);
        sendAppToCMessageVO.setEnterpriseAccount(ea);
        sendAppToCMessageVO.setToUserList(touser);
        sendAppToCMessageVO.setType(OpenMessageTypeEnum.IMAGE_TEXT);
        sendAppToCMessageVO.setNeedThirdPartyPush(true);
        sendAppToCMessageVO.setTitle(title);
        sendAppToCMessageVO.setDefaultSummary(description);
        sendAppToCMessageVO.setParam(getParamMap(ea, lastWeekBeginDate, title, description, uuid));
        try {
            InternationalInfo contentInfo = new InternationalInfo();
            Map<String, Object> contentInfoMap = new LinkedHashMap<>();
            contentInfoMap.put("Type","IMAGE_TEXT");
            contentInfoMap.put("Title",title);
            contentInfoMap.put("DefaultSummary",description);
            contentInfoMap.put("MessageContent",paramMap);
            contentInfo.setContentFrame(new Gson().toJson(contentInfoMap));
            contentInfo.setContentParameters(contentParameters);
            sendAppToCMessageVO.setContentInfo(contentInfo);
            InternationalInfo summaryInfo = new InternationalInfo("qx.ot.mark.invitation_last_week_report_summary",
                    getMarketingActivityCount(ea, lastWeekBeginDate.getMillis(), lastWeekEndDate.getMillis()).toString(),
                    getLeadsCount(ea, lastWeekBeginDate.getMillis(), lastWeekEndDate.getMillis()).toString());
            sendAppToCMessageVO.setSummaryInfo(summaryInfo);
        } catch (Exception e) {
            log.warn("set InternationalInfo fail:", e);
        }
        log.info("sendOpenMessageService.sendAppToCMessage sendAppToCMessageVO={}", GsonUtil.toJson(sendAppToCMessageVO));
        MessageResult messageResult = sendOpenMessageService.sendAppToCMessage(sendAppToCMessageVO);
        if (!messageResult.isSuccess()) {
            log.error("sendAppToCMessage failed, sendAppToCMessageVO={} messageResult={}", sendAppToCMessageVO, messageResult);
        }
    }

    private SpreadQywxMiniappMessageResult sendSpreadQywxAgentppMessage( String ea, QywxCustomerAppInfoEntity appInfoEntity, List<String> touser) {
        String accessToken = qywxManager.getAgentAccessToken(appInfoEntity.getCorpId(), appInfoEntity.getSuitId(), appInfoEntity.getAuthCode());
        if(StringUtils.isBlank(accessToken)){
            return null;
        }
        QywxAgentMessageArg arg = new QywxAgentMessageArg();
        String toUserString = "";
        for (int i = 0; i < touser.size(); i++) {
            toUserString += touser.get(i);
            if (i != touser.size() - 1) {
                toUserString += "|";
            }
        }
        arg.setTouser(toUserString);
        arg.setMsgtype("template_card");
        QywxAgentMessageArg.TemplateCard templateCard = new QywxAgentMessageArg.TemplateCard();
        arg.setTemplate_card(templateCard);
        QywxAgentMessageArg.TemplateCard.Source source = new QywxAgentMessageArg.TemplateCard.Source();
        source.setDesc(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2704));
        source.setIcon_url(qywxManager.getAppLogo(ea));
        templateCard.setSource(source);
        QywxAgentMessageArg.TemplateCard.Main_title mainTitle = new QywxAgentMessageArg.TemplateCard.Main_title();


        LocalDate today = new LocalDate();
        LocalDate lastWeekStart = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY);
        LocalDate lastWeekEnd = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.SUNDAY);
        //上周开始结束时间
        DateTime lastWeekBeginDate = lastWeekStart.toDateTimeAtStartOfDay();
        DateTime lastWeekEndDate = lastWeekEnd.toDateTimeAtStartOfDay().withTime(23, 59, 59, 999);

        mainTitle.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_1674)+ DateUtil.parse(lastWeekBeginDate.toDate(), DateTimeFormatter.ofPattern("MM.dd"))
                        + "~" + DateUtil.parse(lastWeekEndDate.toDate(), DateTimeFormatter.ofPattern("MM.dd")) + ")");

        templateCard.setMain_title(mainTitle);

        QywxAgentMessageArg.TemplateCard.Card_image cardImage = new QywxAgentMessageArg.TemplateCard.Card_image();
        //推广封面
        cardImage.setUrl("https://a2.fspage.com/image/548098/C_202311_21_29a0ff3778b64fe4912316ce909cf412");
        cardImage.setAspect_ratio(1.8);
        templateCard.setCard_image(cardImage);
        ArrayList<QywxAgentMessageArg.TemplateCard.Horizontal_content_list> horizontalContentLists = new ArrayList<>();
        QywxAgentMessageArg.TemplateCard.Horizontal_content_list horizontalContentDescList = new QywxAgentMessageArg.TemplateCard.Horizontal_content_list();
        //查询上周新增的线索数和营销推广数
        horizontalContentDescList.setKeyname(" ");
        horizontalContentDescList.setValue( I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_1677)+getMarketingActivityCount(ea,lastWeekBeginDate.getMillis(),lastWeekEndDate.getMillis())
                +I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_1678)+getLeadsCount(ea,lastWeekBeginDate.getMillis(),lastWeekEndDate.getMillis())+I18nUtil.get(I18nKeyEnum.MARK_SERVICE_SETTINGSERVICEIMPL_1678_1));
        QywxAgentMessageArg.TemplateCard.Horizontal_content_list horizontalContentTimeList = new QywxAgentMessageArg.TemplateCard.Horizontal_content_list();
        horizontalContentTimeList.setKeyname(" ");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        horizontalContentTimeList.setValue(month + I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972) + day + I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1));
        horizontalContentLists.add(horizontalContentDescList);
        horizontalContentLists.add(horizontalContentTimeList);
        templateCard.setHorizontal_content_list(horizontalContentLists);

        QywxAgentMessageArg.TemplateCard.Jump_list jumpList = new QywxAgentMessageArg.TemplateCard.Jump_list();
        jumpList.setTitle(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_EMPLOYEEMSGSENDER_66));
        String url = null;
        url = host + "/proj/page/marketing?ea="+ea + "#/spread/chart?lastWeekBeginDate=" + lastWeekBeginDate.getMillis();
        jumpList.setUrl(url);
        templateCard.setJump_list(Lists.newArrayList(jumpList));
        QywxAgentMessageArg.TemplateCard.Card_action cardAction = new QywxAgentMessageArg.TemplateCard.Card_action();
        cardAction.setType(1);
        cardAction.setUrl(url);
        templateCard.setCard_action(cardAction);
        arg.setAgentid(Integer.valueOf(appInfoEntity.getAgentId()));
        SpreadQywxMiniappMessageResult result = qywxManager.sendAgentMessage(accessToken, arg);
        return result;
    }



    private Integer getLeadsCount(String ea, Long startTime, Long endTime) {
        HeaderObj header = new HeaderObj(eieaConverter.enterpriseAccountToId(ea),-10000);
        ControllerListArg params = new ControllerListArg();
        params.setObjectDescribeApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("create_time",  Lists.newArrayList(String.valueOf(startTime)), FilterOperatorEnum.GTE);
        searchQuery.addFilter("create_time", Lists.newArrayList(String.valueOf(endTime)), FilterOperatorEnum.LTE);
        params.setSearchQuery(searchQuery);
        return metadataControllerServiceManager.getTotal(header,CrmObjectApiNameEnum.CRM_LEAD.getName(),params);
    }

    private Integer getMarketingActivityCount(String ea, Long startTime, Long endTime) {
        return marketingActivityExternalConfigDao.countMarketingActivityCountByTime(ea,new Date(startTime),new Date(endTime), AssociateIdTypeEnum.entMarketingMarketingType());
    }
    private Map<String, Object> yamlHandler(String text){
        //返回的结果
        Map<String, Object> result = new LinkedHashMap<>();
        try {
        //读取方式
        UnicodeReader reader = new UnicodeReader(new ByteArrayInputStream(text.getBytes()) {
        });
        //单文件处理
        Yaml yaml = new Yaml();
        Object object = yaml.load(reader);
        if (object instanceof Map) {
            result = (Map) object;
        }

            reader.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    @FilterLog
    @Override
    public Result<LinkedTreeMap<String,Object>> queryI18n(QueryI18nArg arg) {
        if(StringUtils.isBlank(arg.getEa())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String url = ConfigUtil.i18nUrl+"/app/load";
        int ei = eieaConverter.enterpriseAccountToId(arg.getEa());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("version",arg.getVersion());
        jsonObject.put("tags",arg.getTags());
        jsonObject.put("needCustom", arg.getNeedCustom() != null && arg.getNeedCustom());
        jsonObject.put("lang",arg.getLang());
        String postData = jsonObject.toJSONString();
        RequestBody requestBody = RequestBody.create(null, postData);
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("X-fs-Client-Info","WEB");
        headerMap.put("X-fs-Enterprise-Id",String.valueOf(ei));
        LinkedTreeMap<String,Object> o = httpManager.executePostHttpWithRequestBodyAndHeader(requestBody,url,new TypeToken<LinkedTreeMap<String,Object>>(){},headerMap);
        return Result.newSuccess(o);
    }

    @Override
    public Result<QueryMarketingUserSyncObjectTagsStatus> queryMarketingUserSyncObjectTagsStatus(String ea, Integer fsUserId) {
        Optional<String> optionalValue = marketingEnterpriseCommonSettingManager.getConfigValue(ea, MarketingCommonSettingEnum.SYNC_OBJECT_HISTORY_TAGS.getName());
        if (!optionalValue.isPresent()){
            return Result.newSuccess(new QueryMarketingUserSyncObjectTagsStatus(ea, SyncObjectTagStatusEnum.DISABLE.getValue()));
        }else {
            return Result.newSuccess(new QueryMarketingUserSyncObjectTagsStatus(ea, Integer.valueOf(optionalValue.get())));
        }
    }

    @Override
    public Result setMarketingUserSyncObjectTagsStatus(String ea, Integer fsUserId, Integer syncObjetctTagStatus) {
        marketingEnterpriseCommonSettingManager.saveConfigValue(ea, fsUserId, MarketingCommonSettingEnum.SYNC_OBJECT_HISTORY_TAGS.getName(), String.valueOf(syncObjetctTagStatus));
        return Result.newSuccess();
    }


    @Override
    public Result<Void> addUserAccessible(AddUserAccessibleArg arg) {
        //设置角色
        addUserRoles(arg.getEa(),new HashSet<>(arg.getEmployeeIds()), new HashSet<>(arg.getRoleIds()));
        //设置权限
        if(dataPermissionManager.getNewDataPermissionSetting(arg.getEa())){
            insertOrUpdateUserAccessiblea(arg);
        }
        return Result.newSuccess();
    }


    @Override
    public Result<Void> editUserAccessible(AddUserAccessibleArg arg) {
        arg.getEmployeeIds().forEach(employeeId -> {
            editUserRoles(arg.getEa(),employeeId, new HashSet<>(arg.getRoleIds()));
        });
        if(dataPermissionManager.getNewDataPermissionSetting(arg.getEa())){
            insertOrUpdateUserAccessiblea(arg);
        }
        return Result.newSuccess();
    }

    private int insertOrUpdateUserAccessiblea(AddUserAccessibleArg arg) {
        List<UserAccessibleEntity> entities = arg.getEmployeeIds().stream().map(employeeId -> {
            UserAccessibleEntity userAccessible = new UserAccessibleEntity();
            userAccessible.setId(UUIDUtil.getUUID());
            userAccessible.setUserId(employeeId);
            userAccessible.setEa(arg.getEa());
            userAccessible.setAccessScope(arg.getAccessScope());
            userAccessible.setAccessibleFsDepartments(arg.getAccessibleFsDepartments());
            userAccessible.setAccessibleQywxDepartments(arg.getAccessibleQywxDepartments());
            userAccessible.setAccessChannel(arg.getAccessChannel());
            userAccessible.setCreator(arg.getUserId());
            return userAccessible;
        }).collect(Collectors.toList());
        return userAccessibleDAO.batchInsertOrUpdate(entities);
    }

    @Override
    public Result<EnterpriseChannelsAndRolesInfoResult> queryEnterpriseChannelsAndRolesInfo(String ea) {
        EnterpriseChannelsAndRolesInfoResult result = new EnterpriseChannelsAndRolesInfoResult();
        result.setRoleList(listRole(ea).getData());
        //开启权限插件
        if(dataPermissionManager.getNewDataPermissionSetting(ea)){
            result.setChannelList(listChannels(ea));
        }
        return Result.newSuccess(result);
    }
    @Override
    public Result<UserAccessibleResult> queryUserAccessible(UserAccessibleArg arg) {
        UserAccessibleResult result = new UserAccessibleResult();
        List<Integer> fsDepartmentIds = dataPermissionManager.getFsAccessibleDepartmentIds(arg.getEa(), arg.getUserId(), false);
        List<Integer> qywxDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(arg.getEa(), arg.getUserId(), false);
        List<String> officialAccountIds = dataPermissionManager.findAccessibleOfficialAccountIds(arg.getEa(), arg.getUserId());
        List<String> websiteIds = dataPermissionManager.findAccessibleOfficialWebsiteIds(arg.getEa(), arg.getUserId());
        List<String> AdvertiseIds = dataPermissionManager.findAccessibleAdvertiseIds(arg.getEa(), arg.getUserId());
        result.setEa(arg.getEa());
        result.setFsDepartmentRange(fsDepartmentIds);
        result.setQywxDepartmentRange(qywxDepartmentIds);
        result.setOfficialAccountChannelList(officialAccountIds);
        result.setWebsiteChannelList(websiteIds);
        result.setAdvertiseChannelList(AdvertiseIds);
        return Result.newSuccess(result);
    }

    @Override
    public Result configMiniAppAutoUpgrade(String ea, Integer fsUserId, ConfigMiniAppAutoUpgradeArg arg) {
        String key = arg.getPlatformId() + "_auto_upgrade_miniapp";
        marketingEnterpriseCommonSettingManager.saveConfigValue(ea, fsUserId, key, GsonUtil.toJson(arg));
        return Result.newSuccess();
    }

    @Override
    public Result<String> getMiniAppAutoUpgradeStatus(String ea, Integer fsUserId, GetMiniAppAutoUpgradeStatusArg arg) {
        String key = arg.getPlatformId() + "_auto_upgrade_miniapp";
        Optional<String> optionalValue = marketingEnterpriseCommonSettingManager.getConfigValue(ea, key);
        if (!optionalValue.isPresent()){
           return Result.newSuccess(MiniAppUpgradeStatusEnum.DISABLE.getStatus());
        }

        ConfigMiniAppAutoUpgradeArg configEntity = GsonUtil.fromJson(optionalValue.get(), ConfigMiniAppAutoUpgradeArg.class);
        return Result.newSuccess(configEntity == null ? MiniAppUpgradeStatusEnum.DISABLE.getStatus() : configEntity.getStatus());
    }

    @Override
    public Result<Boolean> isPluginEnabled(PluginEnabledArg arg) {
        String ea = arg.getEa();
        Integer type = arg.getType();
        if (StringUtils.isBlank(ea) || type == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String key = "MARKETING_AI_PLUGIN_ENABLED:" + ea + ":" + type;
        String pluginStat = redisManager.get(key);
        Boolean pluginStatus = false;
        if (StringUtils.isNotBlank(pluginStat)) {
            pluginStatus = Boolean.valueOf(pluginStat);
        } else {
            pluginStatus = marketingPluginConfigManager.getCurrentPluginStatus(ea, type);
            redisManager.set(key, String.valueOf(pluginStatus), 60);
        }
        return Result.newSuccess(pluginStatus);
    }

    @Override
    public Result<GetEnvironmentResult> getEnvironment(String ea) {
        GetEnvironmentResult result = new GetEnvironmentResult();
        result.setCloudEnv(appVersionManager.getCurrentCloudEnv(ea));

        return Result.newSuccess(result);
    }

    public EnterpriseChannelListResult listChannels(String ea) {
        EnterpriseChannelListResult result = new EnterpriseChannelListResult();
        //获取当前企业绑定的公众号
        CompanyWxOfficialAccountsListArg wxOfficialAccountsListArg = new CompanyWxOfficialAccountsListArg();
        wxOfficialAccountsListArg.setEa(ea);
        wxOfficialAccountsListArg.setFsUserId(1000);
        Result<List<AccountDataListResult>> accountsList = wxOfficialAccountsService.getCompanyWxOfficialAccountsList(wxOfficialAccountsListArg);
        if(accountsList.isSuccess() && CollectionUtils.isNotEmpty(accountsList.getData())){
            List<EnterpriseChannelListResult.ChannelInfo> wxOfficialChannels = Lists.newArrayList(new EnterpriseChannelListResult.ChannelInfo(defaultAllChannel, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            accountsList.getData().stream().map(o -> new EnterpriseChannelListResult.ChannelInfo(o.getWxAppId(), o.getWxAppName())).forEach(wxOfficialChannels::add);
            result.setOfficialAccount(wxOfficialChannels);
        }

        //获取当前企业绑定的官网
        List<OfficialWebsiteEntity> websiteEntities = officialWebsiteDAO.listNormalOfficialWebsiteByEa(ea);
        if (CollectionUtils.isNotEmpty(websiteEntities)){
            List<EnterpriseChannelListResult.ChannelInfo> websiteChannel = Lists.newArrayList(new EnterpriseChannelListResult.ChannelInfo(defaultAllChannel, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            websiteEntities.stream().map(o -> new EnterpriseChannelListResult.ChannelInfo(o.getId(), o.getWebsiteName())).forEach(websiteChannel::add);
            result.setOfficialWebsite(websiteChannel);
        }

        //获取当前企业绑定的广告
        List<AdAccountEntity> adAccountEntities = adAccountManager.queryEnableAccountByEa(ea);
        //adAccountEntities根据source转成map
        Map<String, List<AdAccountEntity>> adAccountEntityMap = adAccountEntities.stream().collect(Collectors.groupingBy(AdAccountEntity::getSource));
        //adAccountEntityMap转成<String, List<ChannelInfo>>
        Map<String, List<EnterpriseChannelListResult.ChannelInfo>> channelInfoMap = new HashMap<>();
        adAccountEntityMap.forEach((k,v) -> {
            List<EnterpriseChannelListResult.ChannelInfo> channelInfos = Lists.newArrayList(new EnterpriseChannelListResult.ChannelInfo(defaultAllChannel, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            v.stream().map(o -> new EnterpriseChannelListResult.ChannelInfo(o.getId(), o.getUsername())).forEach(channelInfos::add);
            channelInfoMap.put(AdSourceEnum.getBySource(k).getSourceCode(),channelInfos);
        });
        result.setAdvertisement(channelInfoMap);

        return result;
    }

    //提前计算统计数据
    @Override
    public void calculateMarketingStatistic(){
        List<String> eas = JSON.parseArray(marketingReportSendEaList, String.class);
        if (CollectionUtils.isEmpty(eas)){
            return;
        }
        eas.forEach(ea -> {
            ThreadPoolUtils.execute(() -> {
                MarketingReportArg arg = new MarketingReportArg();
                arg.setEa(ea);
                arg.setStartDate(LocalDate.now().minusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY).toDateTimeAtStartOfDay().getMillis());
                marketingReportService.marketingStatisticInfo(arg);
                //fs多调一次,防止数据过大查不动丢失,第二次有缓存会容易查
                if("fs".equals(ea)){
                    ThreadPoolUtils.execute(() -> {
                        try {
                            Thread.sleep(1000*60*2);//休眠60*2秒，再次调用
                        } catch (Exception e) {
                        }
                        marketingReportService.marketingStatisticInfo(arg);
                    }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
        });
    }

    @Override
    public Result saveMiniappAccessPermissionsSetting(String ea, Integer fsUserId, MiniappAccessPermissionsSettingArg arg) {
        marketingEnterpriseCommonSettingManager.saveConfigValue(ea, fsUserId, MarketingCommonSettingEnum.MINIAPP_ACCESSPERMISSIONS_SETTING.getName(), arg);
        return Result.newSuccess();
    }

    @Override
    public Result getMiniappAccessPermissionsSetting(String ea) {
        return Result.newSuccess(miniappAccessPermissionsManager.getMiniappAccessPermissionsSetting(ea));
    }
}
