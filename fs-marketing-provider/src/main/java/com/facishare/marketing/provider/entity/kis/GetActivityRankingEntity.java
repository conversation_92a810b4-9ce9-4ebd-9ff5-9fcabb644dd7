package com.facishare.marketing.provider.entity.kis;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * Created  By zhoux 2019/02/26
 **/
@Data
@ToString
public class GetActivityRankingEntity extends BaseEaEntity implements Serializable  {
    private static final long serialVersionUID = 8634324082563570272L;
    private String marketingActivityId;
    private Integer count;
}
