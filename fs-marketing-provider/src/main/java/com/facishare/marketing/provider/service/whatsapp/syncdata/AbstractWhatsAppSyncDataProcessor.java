package com.facishare.marketing.provider.service.whatsapp.syncdata;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.whatsapp.QueryDataByIdsArg;
import com.facishare.marketing.api.arg.whatsapp.SyncDataArg;
import com.facishare.marketing.api.vo.whatsapp.SyncDataResultVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.manager.lock.LockManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.common.contants.SHErrorCode;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionBulkCreateResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.limit.GuavaLimiter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public abstract class AbstractWhatsAppSyncDataProcessor implements IWhatsAppSyncDataProcessor {
    @Autowired
    protected CrmV2Manager crmV2Manager;
    @Autowired
    protected EIEAConverter eieaConverter;
    @Autowired
    protected LockManager lockManager;

    protected final String WHATSAPP_RECORD_TYPE = "whatsapp__c";

    protected static final String WHATSAPP_SYNC_DATA_LIMIT_KEY = "limit-yxt-whatsapp-sync-data";

    protected final int batchSyncDataSize = 100;

    protected String getIdKeyFieldName() {
        return "_id";
    }

    protected ObjectData convert2CrmObjectData(ObjectData sourceData) {
        String id = sourceData.getId();
        if(StringUtils.isBlank(id)) {
            id = sourceData.getString("id");
        }
        sourceData.put("_id", id);
        ObjectData data = new ObjectData();
        data.putAll(sourceData);
        data.put("whatsAppDataOriId", sourceData.getString("whatsAppDataOriId"));
        data.put("object_describe_api_name", getObjectApiName());
        data.put("object_describe_id", "");
        data.put("record_type", WHATSAPP_RECORD_TYPE);
        return data;
    }

    protected void prepareObjectDataList(SyncDataArg syncDataArg, List<ObjectData> objectDataList) {
    }

    protected void afterCreateObject(SyncDataArg syncDataArg, List<ObjectData> objectDataList) {
    }

    protected void afterEditObject(SyncDataArg syncDataArg, ObjectData objectData) {
    }

    @Override
    public com.facishare.marketing.common.result.Result<SyncDataResultVO> syncData(SyncDataArg syncDataArg) {
        SyncDataResultVO result = new SyncDataResultVO();
        result.setObjectId("");
        if(Boolean.TRUE.equals(syncDataArg.getSync())) {
            List<ObjectData> insertDataList = Lists.newArrayList();
            List<ObjectData> editDataList = Lists.newArrayList();
            getNeedProcessObjectDataList(syncDataArg, insertDataList, editDataList);
            log.info("WhatsAppSyncDataProcessor syncData, ea: {}, insertDataList size: {}, editDataList size: {}", syncDataArg.getEa(), insertDataList.size(), editDataList.size());
            if(CollectionUtils.isNotEmpty(insertDataList)) {
                ObjectData objectData = insertDataList.get(0);
                Result<ActionAddResult> actionAddResultResult = crmV2Manager.addObjectData(syncDataArg.getEa(), getObjectApiName(), -10000, objectData);
                if (actionAddResultResult.isSuccess()) {
                    result.setObjectId(objectData.getId());
                    ThreadPoolUtils.execute(() -> {
                        afterCreateObject(syncDataArg, Lists.newArrayList(objectData));
                    }, ThreadPoolUtils.ThreadPoolTypeEnums.WHATS_APP);
                    return com.facishare.marketing.common.result.Result.newSuccess(result);
                } else {
                    return com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(), actionAddResultResult.getMessage(), result);
                }
            }

            if(CollectionUtils.isNotEmpty(editDataList)) {
                ObjectData objectData = editDataList.get(0);
                editData(syncDataArg, getObjectApiName(), objectData);
                result.setObjectId(objectData.getId());
                return com.facishare.marketing.common.result.Result.newSuccess(result);
            }
        } else {
            ThreadPoolUtils.execute(() -> {
                innerSyncData(syncDataArg);
            }, ThreadPoolUtils.ThreadPoolTypeEnums.WHATS_APP);
        }
        return com.facishare.marketing.common.result.Result.newSuccess(result);
    }

    private void innerSyncData(SyncDataArg syncDataArg) {
        try {
            List<ObjectData> insertDataList = Lists.newArrayList();
            List<ObjectData> editDataList = Lists.newArrayList();
            getNeedProcessObjectDataList(syncDataArg, insertDataList, editDataList);
            log.info("WhatsAppSyncDataProcessor syncData, ea: {}, insertDataList size: {}, editDataList size: {}", syncDataArg.getEa(), insertDataList.size(), editDataList.size());
            if(CollectionUtils.isNotEmpty(insertDataList)) {
                List<ObjectData> dataList = bulkCreateData(syncDataArg.getEa(), getObjectApiName(), insertDataList);
                afterCreateObject(syncDataArg, dataList);
            }

            if(CollectionUtils.isNotEmpty(editDataList)) {
                bulkEditData(syncDataArg, getObjectApiName(), editDataList);
            }
        } catch (Exception e) {
            log.error("WhatsAppSyncDataProcessor syncData error, ea: {}, syncDataArg: {}", syncDataArg.getEa(), syncDataArg, e);
        }
    }

    private void getNeedProcessObjectDataList(SyncDataArg syncDataArg, List<ObjectData> insertDataList, List<ObjectData> editDataList) {
        int tenantId = eieaConverter.enterpriseAccountToId(syncDataArg.getEa());
        List<ObjectData> objectDataList = convertObjectDataList(syncDataArg.getObjectDataList());
        log.info("WhatsAppSyncDataProcessor syncData, ea: {}, objectDataList size: {}, syncDataArg objectDataList size: {}", syncDataArg.getEa(), objectDataList.size(), syncDataArg.getObjectDataList().size());
        prepareObjectDataList(syncDataArg, objectDataList);
        Set<String> dataIds = objectDataList.stream().map(x -> x.getString(getIdKeyFieldName())).collect(Collectors.toSet());
        log.info("WhatsAppSyncDataProcessor syncData, ea: {}, dataIds: {}", syncDataArg.getEa(), String.join(",", dataIds));
        List<PaasQueryArg.Condition> filters = Lists.newArrayList();
        filters.add(new PaasQueryArg.Condition(getIdKeyFieldName(), Lists.newArrayList(dataIds), OperatorConstants.IN));
        List<ObjectData> dbDataList = getObjectDataList(syncDataArg.getEa(), syncDataArg.getObjectApiName(), Lists.newArrayList(), filters);
        List<String> existsIds = dbDataList.stream().map(x -> String.valueOf(x.get(getIdKeyFieldName()))).collect(Collectors.toList());
        List<ObjectData> notExistDataList = Lists.newArrayList(objectDataList);
        notExistDataList.removeIf(x -> existsIds.contains(x.getString(getIdKeyFieldName())));
        if(CollectionUtils.isNotEmpty(notExistDataList)) {
            notExistDataList.forEach(objectData -> {
                objectData.put("_id", UUIDUtil.getUUID());
                objectData.setTenantId(tenantId);
                objectData.setOwner(syncDataArg.getFsUserId());
                objectData.setCreateBy(syncDataArg.getFsUserId());
                String lockKey = String.format("mk:whatsapp:%s:%s:%s", syncDataArg.getEa(), objectData.getString("object_describe_api_name"), objectData.getString(getIdKeyFieldName()));
                boolean redisLock = tryLockKey(lockKey);
                //防止多次同步相同数据重复插入数据，
                if (redisLock) {
                    insertDataList.add(objectData);
                }
            });
        }

        List<ObjectData> existsDataList = Lists.newArrayList(objectDataList);
        existsDataList.removeIf(x -> !existsIds.contains(x.getString(getIdKeyFieldName())));
        if(CollectionUtils.isNotEmpty(existsDataList)) {
            existsDataList.forEach(objectData -> {
                ObjectData dbData = dbDataList.stream().filter(x -> objectData.getString(getIdKeyFieldName()).equals(x.getString(getIdKeyFieldName()))).findFirst().orElse(null);
                if(dbData != null) {
                    objectData.put("_id", dbData.getId());
                    editDataList.add(objectData);
                }
            });
        }
    }

    @Override
    public com.facishare.marketing.common.result.Result<List<ObjectData>> queryDataByIds(QueryDataByIdsArg queryDataByIdsArg) {
        List<PaasQueryArg.Condition> filters = Lists.newArrayList();
        filters.add(new PaasQueryArg.Condition(getIdKeyFieldName(), Lists.newArrayList(queryDataByIdsArg.getObjectDataIds()), OperatorConstants.IN));
        List<ObjectData> dbDataList = getObjectDataList(queryDataByIdsArg.getEa(), queryDataByIdsArg.getObjectApiName(), Lists.newArrayList(), filters);
        return com.facishare.marketing.common.result.Result.newSuccess(dbDataList);
    }

    protected List<ObjectData> getObjectDataList(String ea, String objectApiName, List<String> fieldNameList, List<PaasQueryArg.Condition> conditions) {
        String tenantId = getTenantId(ea);
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        paasQueryFilterArg.setSelectFields(fieldNameList);
        paasQueryFilterArg.setObjectAPIName(objectApiName);
        PaasQueryArg query = new PaasQueryArg(0, 1000);
        paasQueryFilterArg.setQuery(query);
        query.setFilters(conditions);
        query.addFilter("record_type", OperatorConstants.EQ, Lists.newArrayList(WHATSAPP_RECORD_TYPE));
        query.addFilter("tenant_id", OperatorConstants.EQ, Lists.newArrayList(tenantId));
        InnerPage<ObjectData> result = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, 1, 1000);
        if(result != null && CollectionUtils.isNotEmpty(result.getDataList())) {
            return result.getDataList();
        }
        return Lists.newArrayList();
    }

    protected String getTenantId(String ea) {
        return String.valueOf(eieaConverter.enterpriseAccountToId(ea));
    }

    protected List<ObjectData> bulkCreateData(String ea, String objectApiName, List<ObjectData> objectDataList) {
        List<ObjectData> resultList = Lists.newArrayList();
        for (List<ObjectData> partitionDataList : Lists.partition(objectDataList, batchSyncDataSize)) {
            try {
                log.info("WhatsAppSyncDataProcessor bulkCreateData begin，ea: {} size: {}", ea, objectDataList.size());
                GuavaLimiter.acquire(WHATSAPP_SYNC_DATA_LIMIT_KEY, ea);
                Result<ActionBulkCreateResult> result = crmV2Manager.bulkCreate(ea, SuperUserConstants.USER_ID, objectApiName, partitionDataList, false, false, true);
                log.info("WhatsAppSyncDataProcessor bulkCreateData, ea: {} result: {}", ea, result);
                if(result != null && result.isSuccess()) {
                    resultList.addAll(partitionDataList);
                }
            } catch (Exception e) {
                log.error("WhatsAppSyncDataProcessor bulkCreateData error, ea: {} objectDataList: {}", ea, partitionDataList, e);
            }
        }
        return resultList;
    }

    protected void bulkEditData(SyncDataArg syncDataArg, String objectApiName, List<ObjectData> objectDataList) {
        if(CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        if(Boolean.TRUE.equals(syncDataArg.getSync())) {
            ThreadPoolUtils.execute(() -> {
                try {
                    for (ObjectData objectData : objectDataList) {
                        GuavaLimiter.acquire(WHATSAPP_SYNC_DATA_LIMIT_KEY, syncDataArg.getEa());
                        editData(syncDataArg, objectApiName, objectData);
                    }
                } catch (Exception e) {
                    List<String> dataIds = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
                    log.error("WhatsAppSyncDataProcessor bulkEditData error, ea: {}, objectApiName:{}, dataIds: {} ", syncDataArg.getEa(), getObjectApiName(), String.join(",", dataIds), e);
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.WHATS_APP);
        } else {
            for (ObjectData objectData : objectDataList) {
                GuavaLimiter.acquire(WHATSAPP_SYNC_DATA_LIMIT_KEY, syncDataArg.getEa());
                editData(syncDataArg, objectApiName, objectData);
            }
        }
    }

    private void editData(SyncDataArg syncDataArg, String objectApiName, ObjectData objectData) {
        Result<ActionEditResult> result = crmV2Manager.editWithNotValidateParam(syncDataArg.getEa(), objectApiName, objectData, false, false, true);
        if(result == null || !result.isSuccess()) {
            log.info("WhatsAppSyncDataProcessor bulkEditData failed, ea: {}, objectData: {}", syncDataArg.getEa(), objectData);
        }
        afterEditObject(syncDataArg, objectData);
    }

    protected List<ObjectData> convertObjectDataList(List<ObjectData> sourceDataList) {
        List<ObjectData> resultList = Lists.newArrayList();
        sourceDataList.forEach(sourceData -> {
            ObjectData crmData = convert2CrmObjectData(sourceData);
            resultList.add(crmData);
        });
        return resultList;
    }

    protected String getWechatEmployeeId(String ea, String whatsappUserId) {
        List<PaasQueryArg.Condition> filters = Lists.newArrayList();
        filters.add(new PaasQueryArg.Condition("user_id", Lists.newArrayList(whatsappUserId), OperatorConstants.EQ));
        List<ObjectData> dbDataList = getObjectDataList(ea, CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName(), Lists.newArrayList("_id", "user_id", "name"), filters);
        if (CollectionUtils.isNotEmpty(dbDataList)) {
            return dbDataList.get(0).getId();
        }
        return null;
    }

    protected boolean tryLockKey(String key) {
       return lockManager.retryGetLock(key, 3, 180, 500);
    }
}
