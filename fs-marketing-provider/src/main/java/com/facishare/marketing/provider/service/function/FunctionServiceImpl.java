package com.facishare.marketing.provider.service.function;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.arg.QueryRelationInfoArg;
import com.facishare.marketing.api.arg.RelationConfig;
import com.facishare.marketing.api.result.function.SendQywxApplicationMessageResult;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.service.FunctionService;
import com.facishare.marketing.api.vo.function.FunctionSendMarketingEmailVO;
import com.facishare.marketing.api.service.TraceabilityRelationService;
import com.facishare.marketing.api.vo.function.SendQywxApplicationMessageVO;
import com.facishare.marketing.api.vo.mail.MailServiceMarketingActivityVO;
import com.facishare.marketing.api.vo.function.UpdateQywxExternalUserRemarkVO;
import com.facishare.marketing.common.result.FunctionResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.mail.MailSendReplyDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.entity.mail.MailSendReplyEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.innerArg.qywx.QywxApplicationMessageArg;
import com.facishare.marketing.provider.innerResult.qywx.SpreadQywxMiniappMessageResult;
import com.facishare.marketing.provider.manager.marketingactivity.MailGroupSendManager;
import com.facishare.marketing.provider.manager.qywx.MomentManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.limit.GuavaLimiter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager.CRM_OBJ_QUERY_RATE_LIMIT_KEY;

@Service("functionService")
@Slf4j
public class FunctionServiceImpl implements FunctionService {
    @Autowired
    public QywxManager qywxManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private MomentManager momentManager;
    @Autowired
    private TraceabilityRelationService traceabilityRelationService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MailGroupSendManager mailGroupSendManager;
    @Autowired
    private MailSendReplyDAO mailSendReplyDAO;

    @Override
    public FunctionResult<SendQywxApplicationMessageResult> sendQywxApplicationMessage(Integer tenantId, Integer fsUserId, SendQywxApplicationMessageVO vo) {
        String ea =eieaConverter.enterpriseIdToAccount(tenantId);
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null) {
            log.info("sendQywxApplicationMessage failed not set agent config ea:{}", ea);
            return FunctionResult.newError(SHErrorCode.SYSTEM_ERROR);
        }
        List<QywxCustomerAppInfoEntity> appInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(qywxCorpAgentConfigEntity.getCorpid(), qywxCorpAgentConfigEntity.getEa());
        if (CollectionUtils.isEmpty(appInfoEntities)){
            log.info("sendQywxAgentAppMessage failed not set customer app info config ea:{}", ea);
            return FunctionResult.newError(SHErrorCode.SYSTEM_ERROR);
        }

        QywxApplicationMessageArg arg = new QywxApplicationMessageArg();
        arg.setToUser(vo.getTouser());
        arg.setToParty(vo.getToparty());
        arg.setToTag(vo.getTotag());
        arg.setMsgType("text");
        arg.setAgentId(appInfoEntities.get(0).getAgentId());
        QywxApplicationMessageArg.QywxTextMsg text = new QywxApplicationMessageArg.QywxTextMsg();
        text.setContent(vo.getContent());
        arg.setText(text);
        SpreadQywxMiniappMessageResult messageResult = qywxManager.sendQywxApplicationMessage(momentManager.getOrCreateAccessToken(ea), arg);
        if (!messageResult.isSuccess()) {
            return FunctionResult.newError(messageResult.getErrcode(), messageResult.getErrmsg());
        }
        SendQywxApplicationMessageResult result = BeanUtil.copy(messageResult, SendQywxApplicationMessageResult.class);
        return FunctionResult.newSuccess(result);
    }


    @Override
    public FunctionResult<String> sendMarketingEmail(Integer tenantId, Integer fsUserId, FunctionSendMarketingEmailVO vo) {
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        //判断市场活动是否存在
        ObjectData objectData = crmV2Manager.getDetail(ea, fsUserId, "MarketingEventObj", vo.getMarketingEventId());
        if (objectData == null){
            log.info("FunctionServiceImpl.sendMarketingEmail MarketingEventObj not exist. tenantId:{}, fsUserId:{}, MarketingEventId:{}", tenantId, fsUserId, vo.getMarketingEventId());
            return FunctionResult.newError(SHErrorCode.PARAMS_ERROR);
        }

        MailSendReplyEntity mailSendToEntity = mailSendReplyDAO.queryByEmailAndType(ea, 0, vo.getSendTo());
        MailSendReplyEntity  mailReplyFromEntity = mailSendReplyDAO.queryByEmailAndType(ea, 1, vo.getReplyFrom());
        AddMarketingActivityArg marketingActivityArg = new AddMarketingActivityArg();
        MailServiceMarketingActivityVO mailServiceMarketingActivityVO = new MailServiceMarketingActivityVO();
        marketingActivityArg.setMailServiceMarketingActivityVO(mailServiceMarketingActivityVO);
        marketingActivityArg.setSpreadType(6);
        marketingActivityArg.setMarketingEventId(vo.getMarketingEventId());
        mailServiceMarketingActivityVO.setTitle(vo.getTitle());
        mailServiceMarketingActivityVO.setContent(vo.getContent());
        mailServiceMarketingActivityVO.setTemplateId(vo.getTemplateId());
        mailServiceMarketingActivityVO.setFixedTime(vo.getFixedTime());
        mailServiceMarketingActivityVO.setMailType(1); //批量
        mailServiceMarketingActivityVO.setSendMailIds(Lists.newArrayList(mailSendToEntity.getId()));
        mailServiceMarketingActivityVO.setReplyToIds(Lists.newArrayList(mailReplyFromEntity.getId()));
        mailServiceMarketingActivityVO.setFilterNDaySentUser(vo.getFilterNDaySentUser());
        mailServiceMarketingActivityVO.setSendRange(vo.getSendRange());
        mailServiceMarketingActivityVO.setType(vo.getType());
        mailServiceMarketingActivityVO.setMarketingUserGroupIds(vo.getMarketingUserGroupIds());
        mailServiceMarketingActivityVO.setTagNames(vo.getTagNames());
        mailServiceMarketingActivityVO.setTagOperator(vo.getTagOperator());
        mailServiceMarketingActivityVO.setExcludeTagNames(vo.getExcludeTagNames());
        log.info("sendMarketingEmail marketingActivityArg:{}", marketingActivityArg);
        AddMarketingActivityResult result = mailGroupSendManager.doAddAction(ea, fsUserId, marketingActivityArg);

        return FunctionResult.newSuccess(result.getMarketingActivityId());
    }

    @Override
    public FunctionResult<String> updateQywxExternalUserRemark(Integer tenantId, Integer fsUserId, UpdateQywxExternalUserRemarkVO vo) {
        String errorMesg = qywxManager.syncExternalUserRemark(eieaConverter.enterpriseIdToAccount(tenantId), vo);
        return FunctionResult.newSuccess(errorMesg);
    }

    @Override
    public FunctionResult<RelationConfig> queryRelationInfo(Integer tenantId, QueryRelationInfoArg arg) {
        String ea =eieaConverter.enterpriseIdToAccount(tenantId);
        Result<RelationConfig> configResult = traceabilityRelationService.getRelationConfig(ea, arg.getAggregateKey());
        if (!configResult.isSuccess()) {
            return FunctionResult.newError(configResult.getErrCode(), configResult.getErrMsg());
        }
        return FunctionResult.newSuccess(configResult.getData());
    }
}
