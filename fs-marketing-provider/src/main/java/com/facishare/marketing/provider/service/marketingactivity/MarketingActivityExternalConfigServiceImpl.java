package com.facishare.marketing.provider.service.marketingactivity;

import com.facishare.marketing.api.arg.marketingactivityexternalconfig.AddarketingActivityExternalConfigArg;
import com.facishare.marketing.api.arg.marketingactivityexternalconfig.DeleteArg;
import com.facishare.marketing.api.arg.marketingactivityexternalconfig.GetArg;
import com.facishare.marketing.api.arg.marketingactivityexternalconfig.UpdateArg;
import com.facishare.marketing.api.result.marketingactivityexternalconfig.AddResult;
import com.facishare.marketing.api.result.marketingactivityexternalconfig.DeleteResult;
import com.facishare.marketing.api.result.marketingactivityexternalconfig.GetResult;
import com.facishare.marketing.api.result.marketingactivityexternalconfig.UpdateResult;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityExternalConfigService;
import com.facishare.marketing.api.vo.ExternalConfigVO;
import com.facishare.marketing.api.vo.MarketingActivityExternalConfigVO;
import com.facishare.marketing.api.vo.MarketingActivityGroupSenderVO;
import com.facishare.marketing.api.vo.MarketingActivityNoticeSendVO;
import com.facishare.marketing.api.vo.MarketingActivityVO;
import com.facishare.marketing.api.vo.WeChatServiceMarketingActivityVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupSendMessageVO;
import com.facishare.marketing.common.enums.SendStatusEnum;
import com.facishare.marketing.common.enums.MarketingActivitySpreadTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DBRoutUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasAddMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasUpdateMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.GetMarketingActivityDetailVo;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/20.
 */
@Service("marketingActivityExternalConfigService")
@Slf4j
public class MarketingActivityExternalConfigServiceImpl implements MarketingActivityExternalConfigService {
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;

    @Override
    public AddResult add(String ea, Integer fsUserId, AddarketingActivityExternalConfigArg addarketingActivityExternalConfigArg) {
        PaasAddMarketingActivityArg arg = new PaasAddMarketingActivityArg();
        MarketingActivityVO marketingActivityVO = addarketingActivityExternalConfigArg.getMarketingActivityVO();
        MarketingActivityExternalConfigVO marketingActivityExternalConfigVO = addarketingActivityExternalConfigArg.getMarketingActivityExternalConfigVO();
        arg.setMarketingEventId(marketingActivityVO.getMarketingEventId());
        arg.setName(marketingActivityExternalConfigVO.getExternalConfig().getWeChatServiceMarketingActivityVO().getContent());
        arg.setSpreadType(String.valueOf(marketingActivityVO.getSpreadType()));
        arg.setStatus(SendStatusEnum.DRAFT.getStatus() + "");
        com.facishare.marketing.common.result.Result<String> marketingActivityIdResult = marketingActivityCrmManager.addMarketingActivity(ea, fsUserId, arg);
        if (!marketingActivityIdResult.isSuccess()){
            return new AddResult();
        }
        String marketingActivityId = marketingActivityIdResult.getData();
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = new MarketingActivityExternalConfigEntity();
        marketingActivityExternalConfigEntity.setMarketingActivityId(marketingActivityId);
        marketingActivityExternalConfigEntity.setId(UUIDUtil.getUUID());
        marketingActivityExternalConfigEntity.setEa(ea);
        marketingActivityExternalConfigEntity.setMarketingEventId(marketingActivityVO.getMarketingEventId());
        if (marketingActivityVO.getSpreadType() == MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType()) {
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(marketingActivityExternalConfigVO.getExternalConfig().getWeChatServiceMarketingActivityVO());
        } else if (marketingActivityVO.getSpreadType() == MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType()) {
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(marketingActivityExternalConfigVO.getExternalConfig().getMarketingActivityNoticeSendVO());
        } else if (marketingActivityVO.getSpreadType() == MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType()) {
            //TODO 合并改
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(marketingActivityExternalConfigVO.getExternalConfig().getMarketingActivityGroupSenderVO());
        } else if (marketingActivityVO.getSpreadType() == MarketingActivitySpreadTypeEnum.QYWX_GROUP_SEND.getSpreadType()) {
            //TODO 合并改
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(marketingActivityExternalConfigVO.getExternalConfig().getQywxGroupSendMessageVO());
        }
        marketingActivityExternalConfigDao.insert(marketingActivityExternalConfigEntity);
        return new AddResult(marketingActivityExternalConfigEntity.getId());
    }

    @Override
    public DeleteResult delete(String ea, Integer fsUserId, DeleteArg deleteArg) {
        marketingActivityExternalConfigDao.deleteById(ea, deleteArg.getId());
        return new DeleteResult();
    }

    @Override
    public GetResult get(String ea, Integer fsUserId, GetArg getArg) {
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getById(ea, getArg.getId());
        GetMarketingActivityDetailVo getMarketingActivityDetailVo = marketingActivityCrmManager.getByIdMarketingActivity(ea, fsUserId, marketingActivityExternalConfigEntity.getMarketingActivityId());
        if (getMarketingActivityDetailVo == null) {
            return null;
        }
        MarketingActivityVO marketingActivityVO = new MarketingActivityVO();
        BeanUtils.copyProperties(getMarketingActivityDetailVo, marketingActivityVO);
        marketingActivityVO.setSpreadType(Integer.valueOf(getMarketingActivityDetailVo.getSpreadType()));
        marketingActivityVO.setStatus(Integer.valueOf(getMarketingActivityDetailVo.getStatus()));
        GetResult getResult = new GetResult();
        getResult.setMarketingActivityVO(marketingActivityVO);
        getResult.setMarketingActivityExternalConfigVO(BeanUtil.copyByGson(marketingActivityExternalConfigEntity, MarketingActivityExternalConfigVO.class));
        ExternalConfigVO externalConfigVO = getResult.getMarketingActivityExternalConfigVO().getExternalConfig();
        if (getResult.getMarketingActivityVO().getSpreadType() == MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType()) {
            externalConfigVO.setWeChatServiceMarketingActivityVO(
                BeanUtil.copyByGson(marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO(), WeChatServiceMarketingActivityVO.class));
        } else if (getResult.getMarketingActivityVO().getSpreadType() == MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType()) {
            MarketingActivityNoticeSendVO marketingActivityNoticeSendVO = BeanUtil
                .copyByGson(marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO(), MarketingActivityNoticeSendVO.class);
            externalConfigVO.setMarketingActivityNoticeSendVO(marketingActivityNoticeSendVO);
        } else if (getResult.getMarketingActivityVO().getSpreadType() == MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType()) {
            externalConfigVO.setMarketingActivityGroupSenderVO(
                BeanUtil.copyProperties(marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO(), MarketingActivityGroupSenderVO.class));
        }else if (getResult.getMarketingActivityVO().getSpreadType() == MarketingActivitySpreadTypeEnum.QYWX_GROUP_SEND.getSpreadType()) {
            externalConfigVO.setQywxGroupSendMessageVO(
                BeanUtil.copyProperties(marketingActivityExternalConfigEntity.getExternalConfig().getQywxGroupSendMessageVO(), QywxGroupSendMessageVO.class));
        }
        return getResult;
    }

    @Override
    public UpdateResult update(String ea, Integer fsUserId, UpdateArg updateArg) {
        MarketingActivityExternalConfigVO marketingActivityExternalConfigVO = updateArg.getMarketingActivityExternalConfigVO();
        ExternalConfigVO externalConfigVO = updateArg.getMarketingActivityExternalConfigVO().getExternalConfig();
        MarketingActivityVO marketingActivityVO = updateArg.getMarketingActivityVO();
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getById(ea, marketingActivityExternalConfigVO.getId());
        if (marketingActivityExternalConfigVO.getAssociateIdType() == MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType()) {
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(externalConfigVO.getWeChatServiceMarketingActivityVO());
        } else if (marketingActivityExternalConfigVO.getAssociateIdType() == MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType()) {
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(externalConfigVO.getMarketingActivityNoticeSendVO());
        } else if (marketingActivityExternalConfigVO.getAssociateIdType() == MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType()) {
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(externalConfigVO.getMarketingActivityGroupSenderVO());
        } else if (marketingActivityExternalConfigVO.getAssociateIdType() == MarketingActivitySpreadTypeEnum.QYWX_GROUP_SEND.getSpreadType()) {
            marketingActivityExternalConfigEntity.fillExternalConfigEntity(externalConfigVO.getQywxGroupSendMessageVO());
        }
        PaasUpdateMarketingActivityArg paasUpdateMarketingActivityArg = new PaasUpdateMarketingActivityArg();
        paasUpdateMarketingActivityArg.setMarketingEventId(marketingActivityVO.getMarketingEventId());
        paasUpdateMarketingActivityArg.setName(externalConfigVO.getWeChatServiceMarketingActivityVO() != null ? externalConfigVO.getWeChatServiceMarketingActivityVO().getContent()
            : externalConfigVO.getMarketingActivityGroupSenderVO() != null ? externalConfigVO.getMarketingActivityGroupSenderVO().getTemplateContent()
                : externalConfigVO.getMarketingActivityNoticeSendVO().getTitle());
        paasUpdateMarketingActivityArg.setSpreadType(String.valueOf(marketingActivityVO.getSpreadType()));
        paasUpdateMarketingActivityArg.setStatus(SendStatusEnum.DRAFT.getStatus() + "");
        paasUpdateMarketingActivityArg.setId(marketingActivityExternalConfigEntity.getMarketingActivityId());
        marketingActivityCrmManager.updateMarketingActivity(ea, fsUserId, paasUpdateMarketingActivityArg);
        marketingActivityExternalConfigDao.update(marketingActivityExternalConfigEntity);
        return new UpdateResult(marketingActivityExternalConfigEntity.getId());
    }

    @Override
    public Result<Integer> getMarketingActivityWeeklyStatistics(Date startDate, Date endDate, String ea) {
        Integer count = marketingActivityExternalConfigDao.getEnterpriseEmployeeLeadsWeeklyStatistics(ea, startDate, endDate);
        return new Result<>(SHErrorCode.SUCCESS, count);
    }
}