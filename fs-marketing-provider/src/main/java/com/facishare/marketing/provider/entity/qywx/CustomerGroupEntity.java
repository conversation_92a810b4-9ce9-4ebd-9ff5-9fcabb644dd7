package com.facishare.marketing.provider.entity.qywx;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerGroupEntity extends BaseEaEntity implements Serializable  {
    private String id;                //主键
    private Long corpId;              //公司corpId
    private String groupId;           //群id
    private String groupName;         //群名称
    private String groupHeadPath;     //群头像apath
    private Integer groupMemberCount; //群成员数量
    private String groupOwner;        //群主
    private long createTime;          //群创建时间
    private long updateTime;          //群更新时间
}
