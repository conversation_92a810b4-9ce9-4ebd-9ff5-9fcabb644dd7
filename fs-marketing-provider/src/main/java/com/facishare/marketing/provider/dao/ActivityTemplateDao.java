package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.ActivityTemplateEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 02/09/2018
 */
public interface ActivityTemplateDao {
    @Select("SELECT a.id,a.cover_image_url,a.background_image_url,a.cover_image_small_url,a.background_image_small_url,a.cover_text_describe,a.template_name,"
        + "a.enroll_api_name,o.api_name ,o.field_describes,o.display_name,o.welcome_msg FROM activity_template  a left join object_description o on a.enroll_api_name =o.api_name")
    List<ActivityTemplateEntity> listAll();

    @Select("SELECT id,cover_image_url,background_image_url,cover_image_small_url,background_image_small_url,cover_text_describe,template_name " + "FROM activity_template where id = #{id}")
    ActivityTemplateEntity get(@Param("id") String id);

    @Update("UPDATE activity_template SET ea = #{ea} WHERE ea is null")
    void setDefaultEa(@Param("ea") String ea);
}
