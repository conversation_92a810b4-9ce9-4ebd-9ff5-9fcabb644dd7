/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.sharegpt.agent.tool.impl;

import com.beust.jcommander.internal.Maps;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.manager.ai.AiChatContentType;
import com.facishare.marketing.provider.sharegpt.agent.tool.ToolParam;
import com.facishare.marketing.provider.sharegpt.agent.tool.AbstractCommand;
import com.facishare.marketing.provider.sharegpt.agent.tool.CommandType;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.remote.ai.AiHeaderObj;
import com.facishare.marketing.provider.remote.ai.AiResult;
import com.facishare.marketing.provider.remote.ai.AiService;
import com.facishare.marketing.provider.remote.ai.ImageCreateArg;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.data.message.ChatMessageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ImageCreateCommand extends AbstractCommand {

    @Autowired
    private AiService aiService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private StoneProxyApi stoneProxyApi;

    @ReloadableProperty("ai_helper_default_img_model")
    private String defaultImgModel;

    public static final String name = "图片生成";

    @Override
    public String name() {
        return name;
    }

    @Override
    public CommandType type() {
        return CommandType.EPILOG;
    }

    @Override
    public AiChatContentType contentType() {
        return AiChatContentType.IMAGE;
    }

    @Tool("根据提示语生成图片")
    public String imageCreate(
            @ToolParam("ea") String ea,
            @ToolParam("userId") Integer userId,
            @ToolParam("businessName") String businessName,
            @ToolParam(name = "prompt", type = ChatMessageType.AI, desc = "图片生成的提示语") String prompt
    ) {
        List<Object> result = Lists.newArrayList();
        try {
            AiHeaderObj head = new AiHeaderObj(eieaConverter.enterpriseAccountToId(ea), userId, businessName, TraceContext.get().getTraceId());
            ImageCreateArg arg = new ImageCreateArg();
            arg.setPrompt(prompt);
            arg.setModel(defaultImgModel);
            AiResult<AiResult.ImageCreateResult> imageCreateResultAiResult = aiService.imageCreate(head, arg);
            if (imageCreateResultAiResult != null && imageCreateResultAiResult.isSuccess()
                    && CollectionUtils.isNotEmpty(imageCreateResultAiResult.getResult().getData())) {
                List<String> previewUrls = Lists.newArrayList();
                imageCreateResultAiResult.getResult().getData().forEach(e -> {
                    Map<String, Object> convert = convert(ea, e.getB64Json(), UUIDUtil.getUUID());
                    if (convert != null) {
                        result.add(convert);
                    }
                });
            }
        } catch (Exception e) {
            log.warn("ImageCreateCommand execute error", e);
        }
        return GsonUtil.toJson(result);
    }

    public Map<String, Object> convert(String ea, String base64Image, String name) {
        Map<String, Object> imageInfo = Maps.newHashMap();
        try {
            byte[] decodeBytes = Base64.getDecoder().decode(base64Image);
            if (null == decodeBytes || decodeBytes.length <= 0) {
                log.warn("convert error, bytes is null");
                throw new RuntimeException("convert error, bytes is null");
            }
            StoneFileUploadRequest req = new StoneFileUploadRequest();
            int photoSize = decodeBytes.length;
            req.setFileSize(photoSize);
            if (StringUtils.isNotBlank(name)) {
                req.setOriginName(name);
            }
            req.setEa(ea);
            req.setNeedCdn(true);
            StoneFileUploadResponse stoneFileUploadResponse = stoneProxyApi.uploadByStream("n", req, new ByteArrayInputStream(decodeBytes));
            if (null != stoneFileUploadResponse) {
                String path = stoneFileUploadResponse.getPath();
                imageInfo.put("photoPath", path);
                String previewUrl = fileV2Manager.getUrlByPath(ea, path);
                imageInfo.put("previewUrl", previewUrl);
                imageInfo.put("ext", stoneFileUploadResponse.getExtensionName());
                imageInfo.put("photoName", name);
                imageInfo.put("photoSize", photoSize);
                imageInfo.put("width", stoneFileUploadResponse.getImageProcessResponse().getWidth());
                imageInfo.put("height", stoneFileUploadResponse.getImageProcessResponse().getHeight());
            }
        } catch (Exception e) {
            log.warn("convert error ea:{}", ea, e);
        }
        return imageInfo;
    }

}
