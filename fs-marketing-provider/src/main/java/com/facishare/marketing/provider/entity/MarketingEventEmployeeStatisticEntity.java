package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2020/05/21
 **/
@Data
public class MarketingEventEmployeeStatisticEntity extends BaseEaEntity implements Serializable  {

    private String id;
       // 公司帐号
    private String marketingEventId; //市场活动id
    private Integer fsUserId;
    private Date date; // 日期
    private Integer spreadCount;   // 员工推广次数
    private Integer forwardCount; // 转发次数
    private Integer lookUpCount; // 访问次数
    private Integer forwardUserCount; // 转发人数
    private Integer lookUpUserCount; // 访问人数
    private Date createTime;
    private Date updateTime;

    public void mergeData(MarketingEventEmployeeStatisticEntity mergeData) {
        this.setSpreadCount(getIntValue(this.getSpreadCount()) + getIntValue(mergeData.getSpreadCount()));
        this.setForwardCount(getIntValue(this.getForwardCount()) + getIntValue(mergeData.getForwardCount()));
        this.setLookUpCount(getIntValue(this.getLookUpCount()) + getIntValue(mergeData.getLookUpCount()));
        this.setForwardUserCount(getIntValue(this.getForwardUserCount()) + getIntValue(mergeData.getForwardUserCount()));
        this.setLookUpUserCount(getIntValue(this.getLookUpUserCount()) + getIntValue(mergeData.getLookUpUserCount()));
    }

    private int getIntValue(Integer value){
        return value == null ? 0 : value.intValue();
    }

}
