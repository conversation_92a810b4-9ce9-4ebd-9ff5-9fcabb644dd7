package com.facishare.marketing.provider.manager.distribution;

import com.facishare.mankeep.api.outService.arg.qrCode.CreateQRCodeArg;
import com.facishare.mankeep.api.outService.result.qrCode.CreateQRCodeResult;
import com.facishare.mankeep.api.outService.service.OutQRCodeService;
import com.facishare.mankeep.common.enums.ClueQueryResultStatusEnum;
import com.facishare.mankeep.common.enums.QRCodeTypeEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.util.GsonUtil;
import com.facishare.mankeep.common.util.UUIDUtil;
import com.facishare.marketing.api.arg.PageOperatorArg;
import com.facishare.marketing.api.result.OperatorResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.distribution.OperatorQRCodeResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorByOperatorResult;
import com.facishare.marketing.api.result.distribution.QueryOperatorClueResult;
import com.facishare.marketing.api.result.distribution.QueryOperatorInfoResult;
import com.facishare.marketing.api.result.distribution.QueryPlanListResult;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.vo.QueryDistributorByOperatorVO;
import com.facishare.marketing.api.vo.QueryOperatorClueVO;
import com.facishare.marketing.api.vo.QueryOperatorInfoVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.RoleConstant;
import com.facishare.marketing.common.enums.OperatorTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.DistributePlanDao;
import com.facishare.marketing.provider.dao.OperatorDao;
import com.facishare.marketing.provider.dao.distribution.ClueDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorApplicationDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.dao.distribution.OperatorDistributorDAO;
import com.facishare.marketing.provider.dto.OperatorDistributorEntityCount;
import com.facishare.marketing.provider.dto.distribution.DistributorClueCountDTO;
import com.facishare.marketing.provider.dto.distribution.OperatorPlanDTO;
import com.facishare.marketing.provider.dto.distribution.QueryDistributorByOperatorDTO;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.entity.Operator;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.distribution.ClueEntity;
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorApplicationEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorBaseInfoResult;
import com.facishare.marketing.provider.entity.distribution.DistributorEntity;
import com.facishare.marketing.provider.entity.distribution.OperatorDistributorEntity;
import com.facishare.marketing.provider.entity.distribution.RecruitCountEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.common.model.FsUserVO;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.DbRouteFillEaDataManager.defaultEa;

/**
 * Created by zhengh on 2018/12/12.
 */
@Component
@Slf4j
public class OperatorManager {
    @Autowired
    private AuthManager authManager;

    @Autowired
    private OperatorDao operatorDao;

    @Autowired
    private ClueDAO clueDAO;

    @Autowired
    private OutQRCodeService outQRCodeService;

    @Autowired
    private OperatorDistributorDAO operatorDistributorDAO;

    @Autowired
    private OpenFsUserAppViewService openFsUserAppViewService;

    @ReloadableProperty("marketing_app_web_component")
    private String appWebComponent;

    @Autowired
    private DistributePlanDao distributePlanDao;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private OperatorDistributorDAO operatorDistributorDao;

    @Autowired
    private DistributorApplicationDAO distributorApplicationDAO;

    @Autowired
    private DistributorDao distributorDao;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private DistributorManager distributorManager;

    @ReloadableProperty("host")
    private String host;

    @Autowired
    private SettingService settingService;

    public Result<Void> addOperatorByFsUserIds(String ea, Integer currentUserId, List<Integer> fsUserIds, String planId) {
        List<Integer> existFsUserIds = null;

        //检查重复添加运营人员
        List<Operator> existOperatorEntityList = operatorDao.getOperatorBatchedByFsUidAndFsEa(planId, fsUserIds,ea);
        if (!CollectionUtils.isEmpty(existOperatorEntityList)) {
            existFsUserIds = existOperatorEntityList.stream().map(operatorEntity -> operatorEntity.getFsUserId()).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(existFsUserIds)) {
            fsUserIds.removeAll(existFsUserIds);
        }
        if (CollectionUtils.isEmpty(fsUserIds)) {
            return Result.newSuccess();
        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIds, false);
        List<Operator> operatorEntityList = Lists.newArrayList();
        for (Integer userId : fsUserIds) {
            Operator operatorEntity = new Operator();
            operatorEntity.setEa(ea);
            operatorEntity.setFsUserId(userId);
            operatorEntity.setId(UUIDUtil.getUUID());
            operatorEntity.setPlanId(planId);
            FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(userId);
            if (fsEmployeeMsg != null) {
                if (StringUtils.isNotEmpty(fsEmployeeMsg.getFullName())) {
                    operatorEntity.setName(fsEmployeeMsg.getFullName());
                } else {
                    operatorEntity.setName(fsEmployeeMsg.getName());
                }
                operatorEntity.setPhone(fsEmployeeMsg.getMobile());
            }
            operatorEntity.setType(OperatorTypeEnum.FS.getType());
            operatorEntityList.add(operatorEntity);
        }

        settingService.addUserRoles(ea, new HashSet<>(fsUserIds), ImmutableSet.of(RoleConstant.CORPORATE_DISTRIBUTOR));

        operatorDao.batchInsert(operatorEntityList);

        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                for (Operator operatorEntity : operatorEntityList) {
                    getQRCode(operatorEntity.getEa(), operatorEntity.getPlanId(), operatorEntity.getFsUserId());
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess();
    }

    public Result<Void> deleteByOperatorIds(String ea, Integer currentUserId, String operatorId) {
        Operator operator = operatorDao.getById(operatorId,ea);
        if (operator == null){
            log.error("deleteByOperatorIds operator is not exist ea:{} currentUserId:{} operatorId:{}", ea,
                    currentUserId, operatorId);
            return Result.newSuccess();
        }

        //检查运营人员关联分销人员
        List<String> operatorIds = Lists.newArrayList();
        operatorIds.add(operatorId);
        List<OperatorDistributorEntityCount> distributorCountList =
                operatorDistributorDAO.queryDistributorEntityCountByOperator(ea, operatorIds);

        if (!CollectionUtils.isEmpty(distributorCountList)){
            return Result.newError(SHErrorCode.DELETE_OPERATOR_FAILED_AS_CONNECT_DISTRIBUTOR);
        }

        //获取应用管理员列表
        List<Integer> adminUserIds = authManager.getAppAdmins(ea);
        if (CollectionUtils.isEmpty(adminUserIds)){
            log.error("get marketing admins failed ea:{}", ea);
            return Result.newError(SHErrorCode.GET_ADMIN_FAILED);
        }

        //移除应用中心web可见性
        settingService.deleteUserRoleByUser(ea, operator.getFsUserId());

        operatorDao.deleteByOperatorId(operatorId,ea);
        return Result.newSuccess();
    }

    public Result<PageResult<OperatorResult>> pageOperators(PageOperatorArg vo) {
        Page page = new Page( vo.getPageNum(), vo.getPageSize(), true);
        PageResult<OperatorResult> pr = new PageResult<>();

        List<OperatorResult> operatorResults = Lists.newArrayList();
        pr.setData(operatorResults);
        List<Operator> data = operatorDao.queryEntityByPager(vo.getFsEa(), vo.getPlanId(), page);
        if (CollectionUtils.isEmpty(data)){
            return Result.newSuccess(pr);
        }
        pr.setTotalCount(page.getTotalNum());

        List<String> operatorIds = Lists.newArrayList();
        List<Integer> fsUserIds = Lists.newArrayList();
        List<String> planIds = Lists.newArrayList();
        for (Operator operator : data){
            operatorIds.add(operator.getId());
            planIds.add(operator.getPlanId());
            if (operator.getType() == OperatorTypeEnum.FS.getType()) {
                fsUserIds.add(operator.getFsUserId());
            }

        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getFsEa(), fsUserIds, true);

        List<OperatorDistributorEntityCount> operatorDistributorEntityCountList =
                operatorDistributorDAO.queryDistributorEntityCountByOperator(vo.getFsEa(), operatorIds);

        List<DistributePlanEntity> distributePlanEntities = distributePlanDao.queryDistributePlanByIds(vo.getFsEa(),planIds);
        Map<String, DistributePlanEntity> planMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(distributePlanEntities)) {
            planMap = distributePlanEntities.stream().collect(Collectors.toMap(DistributePlanEntity::getId, a -> a,(k1,k2)->k1));
        }

        for (Operator operatorEntity : data){
            OperatorResult operatorResult = new OperatorResult();
            operatorResult.setPhone(operatorEntity.getPhone());
            operatorResult.setName(operatorEntity.getName());
            operatorResult.setId(operatorEntity.getId());
            operatorResult.setFsUserId(operatorEntity.getFsUserId());
            operatorResult.setType(operatorEntity.getType());
            operatorResult.setPlanId(operatorEntity.getPlanId());
            DistributePlanEntity distributePlan = planMap.get(operatorEntity.getPlanId());
            if (distributePlan != null) {
                operatorResult.setPlanTitle(distributePlan.getPlanTitle());
            }
            FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(operatorEntity.getFsUserId());
            if (fsEmployeeMsg != null) {
                operatorResult.setProfile(fsEmployeeMsg.getProfileImage());
            }
            for (OperatorDistributorEntityCount operatorDistributorEntityCount :operatorDistributorEntityCountList){
                if (operatorDistributorEntityCount.getOperatorId().equals(operatorEntity.getId())){
                    if (operatorDistributorEntityCount.getDistributorCount() == null){
                        operatorResult.setDistributorCount(0);
                    }else {
                        operatorResult.setDistributorCount(operatorDistributorEntityCount.getDistributorCount());
                    }
                    break;
                }
            }
            operatorIds.add(operatorEntity.getId());
            operatorResults.add(operatorResult);
        }

        return Result.newSuccess(pr);
    }

    @FilterLog
    public Result<Boolean> isOperator(String ea, Integer userId) {
        List<Operator> operatorEntitys = operatorDao.getOperatorByFsUidAndFsEa(ea, userId);
        if (CollectionUtils.isEmpty(operatorEntitys)){
            return Result.newSuccess(Boolean.FALSE);
        }

        return Result.newSuccess(Boolean.TRUE);
    }

    public Result<OperatorQRCodeResult> getQRCode(String ea, String planId, Integer fsUserId) {
        if  (StringUtils.isEmpty(planId)) {
            log.warn("OperatorManager getQRCode planId is null");
            return new Result<>(SHErrorCode.OPERATOR_GET_QRCODE_FAILED);
        }

        Operator queryOperatorEntity = operatorDao.getOperatorByFsUidAndPlanId(planId, fsUserId,ea);
        if (null == queryOperatorEntity) {
            log.warn("OperatorManager getQRCode queryOperatorEntity is null, planId:{}, fsUserId:{}",planId, fsUserId);
            return new Result<>(SHErrorCode.OPERATOR_ONT_FOUND);
        }

        DistributePlanEntity distributePlanEntity =  distributePlanDao.getDistributePlanByPlanId(ea, queryOperatorEntity.getPlanId());
        if (distributePlanEntity == null) {
            log.warn("OperatorManager getQRCode distributePlanEntity is null, planId:{}, fsUserId:{}",planId, fsUserId);
            return new Result<>(SHErrorCode.DISTRIBUTE_PLAN_ONT_FOUND);
        }

        photoManager.queryOperatorPhotoUrl(queryOperatorEntity);

        String qrUrl = queryOperatorEntity.getQrUrl();
        String path = fileV2Manager.getApathByUrl(queryOperatorEntity.getQrUrl());
        if (org.apache.commons.lang3.StringUtils.isBlank(qrUrl)) {
            Map<String, String> valueMap = new HashMap<>();
            valueMap.put("operatorId", queryOperatorEntity.getId());
            valueMap.put("distributePlanId", distributePlanEntity.getId());
            valueMap.put("ea", distributePlanEntity.getFsEa());
            String valueJson = GsonUtil.getGson().toJson(valueMap, HashMap.class);

            CreateQRCodeArg createQRCodeArg = new CreateQRCodeArg();
            createQRCodeArg.setType(QRCodeTypeEnum.OPREATOR_DUSTRIBUTOR.getType());
            createQRCodeArg.setValue(valueJson);
            createQRCodeArg.setEa(ea);

            ModelResult<CreateQRCodeResult> createQRCodeResultModelResult = outQRCodeService.createQRCode(createQRCodeArg);
            if (!createQRCodeResultModelResult.isSuccess()) {
                log.warn("OperatorManager getQRCode creator is failed, planId:{}, fsUserId:{}",planId, fsUserId);
                return new Result<>(SHErrorCode.OPERATOR_GET_QRCODE_FAILED);
            }

            CreateQRCodeResult createQRCodeResult = createQRCodeResultModelResult.getData();
            qrUrl = createQRCodeResult.getQrCodeUrl();
            if (org.apache.commons.lang3.StringUtils.isBlank(qrUrl)) {
                log.warn("OperatorManager createQRCodeResult qrUrl is null");
                return new Result<>(SHErrorCode.OPERATOR_GET_QRCODE_FAILED);
            }

            Operator operatorEntity = new Operator();
            operatorEntity.setId(queryOperatorEntity.getId());
            operatorEntity.setQrUrl(qrUrl);
            boolean updateQrUrlResult = operatorDao.updateQrUrl(operatorEntity.getQrUrl(), operatorEntity.getId(),ea);
            if (!updateQrUrlResult) {
                log.info("OperatorManager updateQrUrl failed, operatorEntity={}", operatorEntity);
            }

            //String path = fileManager.getPathByShareId(qrUrl.split("fileId=")[1]);

            path = fileV2Manager.getApathByUrl(operatorEntity.getQrUrl());
            if (StringUtils.isNotBlank(path)) {
                photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.DISTRIBUTION_OPERATOR_QRCODE, operatorEntity.getId(), path, path);
            }
        }

        OperatorQRCodeResult result = new OperatorQRCodeResult();
        result.setQrCodeUrl(qrUrl);
        result.setPath(path);
        return Result.newSuccess(result);
    }

    /**
     * 查询分销人员信息，一个分销人员属于多个分销计划
     * @param operatorId
     * @param planId
     * @return
     */
    public Result<QueryOperatorInfoResult> queryOperatorInfo(String operatorId, String planId,String ea){
       OperatorPlanDTO operatorPlanDTO = operatorDao.queryOperatorPlanDtoById(operatorId, planId,ea);
        if (operatorPlanDTO == null){
            log.error("queryOperatorInfo failed operator cannot exist plan operatorId:{}", planId);
            return Result.newError(SHErrorCode.OPERATOR_GET_DISTRIBUTE_PLAN_FAILED);
        }

        QueryOperatorInfoResult result = new QueryOperatorInfoResult();
        result.setOperatorId(operatorPlanDTO.getOperatorId());
        result.setOperatorQrUrl(getOperatorQrUrl(ea, operatorId, operatorPlanDTO.getPlanId()));
        result.setOperatorName(operatorPlanDTO.getOperatorName());

        List<OperatorDistributorEntity> operatorDistributorEntityList =  operatorDistributorDao.getDistributorIdsByOperatorId(ea, operatorPlanDTO.getOperatorId());
        List<String> distributorIds = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(operatorDistributorEntityList)){
            for (OperatorDistributorEntity operatorDistributorEntity : operatorDistributorEntityList){
                distributorIds.add(operatorDistributorEntity.getDistributorId());
            }
        }

        //计算线索数
        int clueCount = 0;
        if (!CollectionUtils.isEmpty(distributorIds)){
            clueCount = clueDAO.queryClueCountByDistributorIds(ea, distributorIds);
        }
        result.setClueNum(clueCount);
        result.setDistributorNum(distributorIds.size());
        result.setPlanTitle(operatorPlanDTO.getPlanTitle());
        result.setPlanId(operatorPlanDTO.getPlanId());

        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.DISTRIBUTION_PLAN_DETAIL.getType(),
                                                                      operatorPlanDTO.getPlanId(),ea);
        if (!CollectionUtils.isEmpty(photoEntityList)) {
            List<String> picUrlList = photoEntityList.stream().map(PhotoEntity::getUrl).collect(Collectors.toList());
            List<String> picThumbnailList = photoEntityList.stream().map(data -> data.getThumbnailUrl() != null ? data.getThumbnailUrl() : data.getUrl()).collect(Collectors.toList());
            result.setPicList(picUrlList);
            result.setPicThumbnailList(picThumbnailList);
        }

        return Result.newSuccess(result);
    }

    /**
     * 根据运营id和分销计划id查询分销人员信息，分页查询
     * @param vo
     * @return
     */
    public Result<PageResult<QueryDistributorByOperatorResult>> queryDistributorByOperator(QueryDistributorByOperatorVO vo){
        Page page = new Page( vo.getPageNum(), vo.getPageSize(), true);
        PageResult<QueryDistributorByOperatorResult> pr = new PageResult<>();
        pr.setTotalCount(0);
        List<QueryDistributorByOperatorResult> queryDistributorByOperatorResult = Lists.newArrayList();
        List<QueryDistributorByOperatorDTO> queryDistributorByOperatorResultList = operatorDao.queryDistributorByOperator(vo.getOperatorId(),
                vo.getPlanId(), vo.getKeyWord(), vo.getStatus(), page,vo.getEa());

        List<String> distributorIds = Lists.newArrayList();
        Map<String, QueryDistributorByOperatorResult> distributorIdResultMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(queryDistributorByOperatorResultList)){
            List<String> distributorAndRecruitIds = Lists.newArrayList();
            queryDistributorByOperatorResultList.forEach(queryDistributorByOperatorDTO -> {
                distributorIds.add(queryDistributorByOperatorDTO.getDistributorId());
                distributorAndRecruitIds.add(queryDistributorByOperatorDTO.getDistributorId());
                if (StringUtils.isNotEmpty(queryDistributorByOperatorDTO.getRecruitId())) {
                    distributorAndRecruitIds.add(queryDistributorByOperatorDTO.getRecruitId());
                }
            });

            List<RecruitCountEntity> recruitCounts = distributorDao.getRecruitCounts(vo.getEa(), distributorIds);
            recruitCounts = recruitCounts == null ? Lists.newArrayList() : recruitCounts;
            final Map<String, RecruitCountEntity> recruitCountMap = recruitCounts.stream().collect(Collectors.toMap(RecruitCountEntity :: getRecruitId, v -> v, (k1, k2) -> k1));

            Map<String, DistributorBaseInfoResult> distributorFormDataMap = distributorManager.getDistributorFormDataMap(vo.getEa(), vo.getPlanId(), distributorAndRecruitIds, null);
            pr.setTotalCount(page.getTotalNum());

            for (QueryDistributorByOperatorDTO dto : queryDistributorByOperatorResultList){
                QueryDistributorByOperatorResult operatorResult = new QueryDistributorByOperatorResult();
                operatorResult.setDistributorId(dto.getDistributorId());
                operatorResult.setCompanyName(dto.getCompanyName());
                operatorResult.setName(dto.getName());
                operatorResult.setPhone(dto.getPhone());
                operatorResult.setCreateTime(dto.getCreateTime().getTime());
                operatorResult.setStatus(dto.getStatus());
                operatorResult.setWechat(dto.getWechat());
                DistributorApplicationEntity distributorApplicationEntity = distributorApplicationDAO.getLatestInfoById(vo.getEa(), dto.getDistributorId(), vo.getOperatorId());
                if (distributorApplicationEntity != null) {
                    operatorResult.setRefuseDesc(distributorApplicationEntity.getRefuseDesc());
                }

                String avatarUrl = null;
                if (StringUtils.isNotBlank(dto.getAvatar()) && !dto.getAvatar().startsWith(host)) {
                    avatarUrl = dto.getAvatar();
                } else {
                    if (StringUtils.isNotBlank(dto.getAvatarThumbnailPath())) {
                        String avatarThumbnailUrl = fileV2Manager.getUrlByPath(dto.getAvatarThumbnailPath(), null, false);
                        if (StringUtils.isNotBlank(avatarThumbnailUrl)) {
                            avatarUrl = avatarThumbnailUrl;
                        }
                    }
                    if (StringUtils.isBlank(avatarUrl)) {
                        if (StringUtils.isNotBlank(dto.getAvatarPath())) {
                            avatarUrl = fileV2Manager.getUrlByPath(dto.getAvatarPath(), null, false);
                        }
                    }
                }

                operatorResult.setAvatarThumbnail(avatarUrl);

                DistributorBaseInfoResult baseInfoResult = distributorFormDataMap.get(dto.getDistributorId());
                if (baseInfoResult != null ) {
                    if (StringUtils.isNotEmpty(baseInfoResult.getPhone())) {
                        operatorResult.setPhone(baseInfoResult.getPhone());
                    }

                    if (StringUtils.isNotEmpty(baseInfoResult.getWechat())) {
                        operatorResult.setWechat(baseInfoResult.getWechat());
                    }

                    if (StringUtils.isNotEmpty(baseInfoResult.getName())) {
                        operatorResult.setName(baseInfoResult.getName());
                    }

                    operatorResult.setJoinDesc(baseInfoResult.getJoinDesc());
                    operatorResult.setIdCardNumber(baseInfoResult.getIdCardNumber());
                    operatorResult.setBankCardNo(baseInfoResult.getBankCardNo());
                    operatorResult.setBankName(baseInfoResult.getBankName());
                    operatorResult.setGradeName(baseInfoResult.getGradeName());
                    operatorResult.setWechatName(baseInfoResult.getWechatName());
                }
                if (StringUtils.isNotEmpty(dto.getRecruitId())) {
                    DistributorBaseInfoResult recruitBaseInfoResult = distributorFormDataMap.get(dto.getDistributorId());
                    if (recruitBaseInfoResult != null ) {
                        operatorResult.setRecruitName(recruitBaseInfoResult.getName());
                    } else {
                        DistributorEntity distributorRecruit = distributorDao.queryDistributorById(vo.getEa(), dto.getRecruitId());
                        if (distributorRecruit != null) {
                            CardEntity cardRecruitEntity = cardDAO.queryCardInfoByUid(vo.getEa(), distributorRecruit.getUid());
                            operatorResult.setRecruitName(cardRecruitEntity.getName());
                        }
                    }
                }
                RecruitCountEntity recruitCountEntity = recruitCountMap.get(dto.getDistributorId());
                if (recruitCountEntity != null) {
                    operatorResult.setRecruitCount(recruitCountEntity.getRecruitCount());
                }
                queryDistributorByOperatorResult.add(operatorResult);
                distributorIdResultMap.put(dto.getDistributorId(), operatorResult);
            }
        }

        if (!CollectionUtils.isEmpty(distributorIds)) {
            List<DistributorClueCountDTO> distributorClueCountDTOList = clueDAO.queryClueCountGroupByDistributors(vo.getEa(), distributorIds);
            if (!CollectionUtils.isEmpty(distributorClueCountDTOList)) {
                for (DistributorClueCountDTO distributorClueCountDTO : distributorClueCountDTOList) {
                    QueryDistributorByOperatorResult result = distributorIdResultMap.get(distributorClueCountDTO.getDistributorId());
                    result.setClueCount(distributorClueCountDTO.getClueCount());
                }
            }
        }
        pr.setData(queryDistributorByOperatorResult);

        return Result.newSuccess(pr);
    }

    /**
     * 运营人员分页查询线索列表
     * @param vo
     * @return
     */
    public Result<PageResult<QueryOperatorClueResult>> queryOperatorClue(QueryOperatorClueVO vo){
        Page page = new Page( vo.getPageNum(), vo.getPageSize(), true);
        PageResult<QueryOperatorClueResult> pr = new PageResult<>();
        pr.setTotalCount(0);

        //状态转换，前端显示状态->数据库状态list
        List<Integer> crmStatus = ClueQueryResultStatusEnum.getCrmStatusFromStatus(vo.getStatus());
        List<QueryOperatorClueResult> queryOperatorClueResultList = Lists.newArrayList();
        List<ClueEntity> queryOperatorClueDTOList = null;
        if (vo.getStatus() == ClueQueryResultStatusEnum.ALL.getType() || CollectionUtils.isEmpty(crmStatus)) {
            queryOperatorClueDTOList = operatorDao.queryOperatorClue(vo.getOperatorId(),
                    vo.getPlanId(), null, vo.getKeyWord(), page, vo.getEa());
        }else {
            queryOperatorClueDTOList = operatorDao.queryOperatorClue(vo.getOperatorId(),
                    vo.getPlanId(), crmStatus, vo.getKeyWord(), page,vo.getEa());
        }

        if (CollectionUtils.isEmpty(queryOperatorClueDTOList)){
            pr.setTotalCount(0);
        }else {
            pr.setTotalCount(page.getTotalNum());
        }

        for (ClueEntity dto : queryOperatorClueDTOList){
            QueryOperatorClueResult clueResult = new QueryOperatorClueResult();
            clueResult.setId(dto.getId());
            clueResult.setName(dto.getName());
            clueResult.setCompanyName(dto.getCompanyName());
            clueResult.setStatus(ClueQueryResultStatusEnum.getStatusFromCrmStatus(dto.getStatus()));
            clueResult.setDetail(dto.getDetail());
            clueResult.setPhone(dto.getPhone());
            clueResult.setCreateTime(dto.getCreateTime().getTime());
            clueResult.setReward(dto.getReward());
            clueResult.setGranted(dto.getGranted());
            clueResult.setRecruitReward(dto.getRecruitReward());
            clueResult.setRecruitGranted(dto.getRecruitGranted());
            clueResult.setValidClueReward(dto.getValidClueReward());
            float balance = dto.getReward()  + dto.getValidClueReward() - dto.getGranted();
            if (balance < 0) {
                balance = 0;
            }
            clueResult.setBalance((float) (Math.round(balance * 1000d) / 1000d));
            queryOperatorClueResultList.add(clueResult);
        }
        pr.setData(queryOperatorClueResultList);

        return Result.newSuccess(pr);
    }

    /**
     * 查询分销计划列表
     * @param ea
     * @param userId
     * @return
     */
    public Result<List<QueryPlanListResult>> queryPlanList(String ea, Integer userId){
        List<QueryPlanListResult> planList = operatorDao.queryPlanList(ea, userId);
        return Result.newSuccess(planList);
    }

    /**
     * 查询运营人员信息，一个分销人员属于多个分销计划
     * @param vo
     * @return
     */
    public Result<QueryOperatorInfoResult> queryOperatorInfo(QueryOperatorInfoVO vo){
        if (StringUtils.isBlank(vo.getPlanId()) && StringUtils.isBlank(vo.getOperatorId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        OperatorPlanDTO operatorPlanDTO = null;
        if (StringUtils.isNotEmpty(vo.getPlanId())) {
            operatorPlanDTO = operatorDao.queryOperatorPlanDtoByFsUserId(vo.getFsUid(), vo.getPlanId(),vo.getFsEa());
        } else if (StringUtils.isNotEmpty(vo.getOperatorId())) {
            Operator operator = operatorDao.getById(vo.getOperatorId(),vo.getFsEa());
            if (operator == null) {
                log.info("OperatorManager queryOperatorInfo failed operator is not exist vo:{}", vo);
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            operatorPlanDTO = operatorDao.queryOperatorPlanDtoById(vo.getOperatorId(), operator.getPlanId(),vo.getFsEa());
        }
        if (operatorPlanDTO == null){
            log.info("OperatorManager queryOperatorInfo failed operatorPlanDTO is not exist vo:{}", vo);
            return Result.newError(SHErrorCode.OPERATOR_GET_DISTRIBUTE_PLAN_FAILED);
        }

        QueryOperatorInfoResult result = new QueryOperatorInfoResult();
        result.setOperatorId(operatorPlanDTO.getOperatorId());
        result.setOperatorQrUrl(getOperatorQrUrl(vo.getFsEa(), operatorPlanDTO.getOperatorId(), operatorPlanDTO.getPlanId()));

        List<OperatorDistributorEntity> operatorDistributorEntityList =  operatorDistributorDao.getDistributorIdsByOperatorId(vo.getFsEa(), operatorPlanDTO.getOperatorId());
        List<String> distributorIds = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(operatorDistributorEntityList)){
            for (OperatorDistributorEntity operatorDistributorEntity : operatorDistributorEntityList){
                distributorIds.add(operatorDistributorEntity.getOperatorId());
            }
        }

        //计算线索数
        int clueCount = 0;
        if (!CollectionUtils.isEmpty(distributorIds)){
            clueCount = clueDAO.queryClueCountByDistributorIds(vo.getFsEa(), distributorIds);
        }
        result.setClueNum(clueCount);
        result.setDistributorNum(distributorIds.size());
        result.setPlanTitle(operatorPlanDTO.getPlanTitle());
        result.setPlanId(operatorPlanDTO.getPlanId());

        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.DISTRIBUTION_PLAN_DETAIL.getType(),
            operatorPlanDTO.getPlanId(),vo.getFsEa());
        if (!CollectionUtils.isEmpty(photoEntityList)) {
            List<String> picUrlList = photoEntityList.stream().map(PhotoEntity::getUrl).collect(Collectors.toList());
            List<String> picThumbnailList = photoEntityList.stream().map(data -> data.getThumbnailUrl() != null ? data.getThumbnailUrl() : data.getUrl()).collect(Collectors.toList());
            result.setPicList(picUrlList);
            result.setPicThumbnailList(picThumbnailList);
        }

        return Result.newSuccess(result);
    }

    /**
     * 获取运营人员二维码
     * @param operatorId
     * @param distributePlanId
     * @return
     */
    private String getOperatorQrUrl(String ea, String operatorId, String distributePlanId) {
        String qrUrl = null;
        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.DISTRIBUTION_OPERATOR_QRCODE.getType(), operatorId,ea);
        DistributePlanEntity distributePlanEntity =  distributePlanDao.getDistributePlanByPlanId(ea, distributePlanId);
        if (CollectionUtils.isEmpty(photoEntityList)) {
            // 若不存在则重新生成保存
            Map<String, String> valueMap = new HashMap<>();
            valueMap.put("operatorId", operatorId);
            valueMap.put("distributePlanId", distributePlanId);
            if (distributePlanEntity != null) {
                valueMap.put("ea", distributePlanEntity.getFsEa());
                ea = distributePlanEntity.getFsEa();
            }
            String valueJson = GsonUtil.getGson().toJson(valueMap, HashMap.class);

            CreateQRCodeArg createQRCodeArg = new CreateQRCodeArg();
            createQRCodeArg.setType(QRCodeTypeEnum.OPREATOR_DUSTRIBUTOR.getType());
            createQRCodeArg.setValue(valueJson);
            createQRCodeArg.setEa(ea);
            ModelResult<CreateQRCodeResult> createQRCodeResultModelResult = outQRCodeService.createQRCode(createQRCodeArg);
            if (createQRCodeResultModelResult.isSuccess() && createQRCodeResultModelResult.getData() != null) {
                qrUrl = createQRCodeResultModelResult.getData().getQrCodeUrl();
                photoManager.savePhotoByAapath(ea,PhotoTargetTypeEnum.DISTRIBUTION_OPERATOR_QRCODE, operatorId, createQRCodeResultModelResult.getData().getApath(), createQRCodeResultModelResult.getData().getApath());
            }
        } else {
            qrUrl = photoEntityList.get(0).getUrl();
        }
        return qrUrl;
    }

    public void refreshOperatorDistributorEa() {
        log.info("start to refreshEa for miniapp_release_record table");
        List<OperatorDistributorEntity> operatorDistributorEntityList = operatorDistributorDao.getAll();
        List<String> distributorIds = operatorDistributorEntityList.stream().map(OperatorDistributorEntity::getDistributorId).distinct().collect(Collectors.toList());
        List<DistributorEntity> distributorEntityList = distributorDao.queryByIds(distributorIds);
        Map<String, String> distributorIdEaMap = distributorEntityList.stream().collect(Collectors.toMap(DistributorEntity::getId, DistributorEntity::getFsEa, (v1, v2) -> v2));
        for (OperatorDistributorEntity operatorDistributorEntity : operatorDistributorEntityList) {
            if (distributorIdEaMap.get(operatorDistributorEntity.getDistributorId()) != null) {
                operatorDistributorEntity.setEa(distributorIdEaMap.get(operatorDistributorEntity.getDistributorId()));
            } else {
                operatorDistributorEntity.setEa(defaultEa);
            }
        }

        operatorDistributorDao.batchUpdateEa(operatorDistributorEntityList);
        log.info("stop to refreshEa for operator_distributor table");
    }

}
