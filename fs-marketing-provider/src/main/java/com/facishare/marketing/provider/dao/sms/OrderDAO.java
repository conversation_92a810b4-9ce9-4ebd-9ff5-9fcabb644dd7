package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.OrderEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Created by z<PERSON><PERSON> on 2018/12/21.
 */
public interface OrderDAO {
    @Select("SELECT * FROM sms_order WHERE ea=#{ea} AND status=1 ORDER BY create_time DESC")
    List<OrderEntity> queryQuotaOrder(@Param("ea")String ea, @Param("page") Page page);

    @Insert("INSERT INTO sms_order(\n" +
        "        \"id\",\n" +
        "        \"ea\",\n" +
        "        \"user_id\",\n" +
        "        \"creator\",\n" +
        "        \"price\",\n" +
        "        \"payment_count\",\n" +
        "        \"purchase_count\",\n" +
        "        \"status\",\n" +
        "        \"copies\",\n" +
        "        \"crm_order_id\",\n" +
        "        \"order_package\",\n" +
        "        \"order_source\",\n" +
        "        \"create_time\",\n" +
        "        \"update_time\"\n" +
        "        )\n" +
        "        VALUES (\n" +
        "        #{obj.id},\n" +
        "        #{obj.ea},\n" +
        "        #{obj.userId},\n" +
        "        #{obj.creator},\n" +
        "        #{obj.price},\n" +
        "        #{obj.paymentCount},\n" +
        "        #{obj.purchaseCount},\n" +
        "        #{obj.status},\n" +
        "        #{obj.copies},\n" +
        "        #{obj.crmOrderId},\n" +
        "        #{obj.orderPackage},\n" +
        "        #{obj.orderSource},\n" +
        "        now(),\n" +
        "        now()\n" +
        "        )")
    int addOrder(@Param("obj") OrderEntity orderEntity);

    @Select("SELECT * FROM sms_order WHERE id=#{orderNo}")
    OrderEntity findByOrderNo(@Param("ea")String ea, @Param("orderNo")String orderNo);

    @Select("update sms_order SET status = #{status}, update_time = now() WHERE id=#{orderNo}")
    void callbackUpdate(@Param("ea")String ea, @Param("status")int status, @Param("orderNo")String orderNo);


    @Select("SELECT * FROM sms_order WHERE crm_order_id=#{crmOrderId} AND ea=#{ea}")
    OrderEntity queryOrderByCrmOrderId(@Param("crmOrderId")String crmOrderId, @Param("ea")String ea);
}
