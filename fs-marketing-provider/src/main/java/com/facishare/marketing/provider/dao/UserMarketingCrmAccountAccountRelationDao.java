package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.dto.ObjectIdWithMarketingUserIdAndPhoneDTO;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmAccountAccountRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmContactAccountRelationEntity;
import com.github.mybatis.mapper.IPaginationPostgresqlMapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Delete;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 08/03/2019
 */
public interface UserMarketingCrmAccountAccountRelationDao extends IPaginationPostgresqlMapper<UserMarketingCrmAccountAccountRelationEntity> {

    @Update("INSERT INTO user_marketing_crm_account_account_relation(id, crm_account_id, ea, user_marketing_id, create_time, update_time, user_name, email) VALUES (#{entity.id}, #{entity.crmAccountId}, #{entity.ea}, #{entity.userMarketingId}, now(), now(), #{entity.userName}, #{entity.email}) ON CONFLICT DO NOTHING;")
    int insert(@Param("entity") UserMarketingCrmAccountAccountRelationEntity entity);

    @Update("DELETE FROM user_marketing_crm_account_account_relation WHERE ea = #{ea} AND crm_account_id = #{crmAccountId}")
    void deleteByEaAndCrmAccountId(@Param("ea") String ea, @Param("crmAccountId") String crmAccountId);
    
    @Update("UPDATE user_marketing_crm_account_account_relation SET user_marketing_id = #{newUserMarketingId}, update_time = now() WHERE user_marketing_id = #{oldUserMarketingId} AND ea = #{ea}")
    public void updateOldUserMarketingIdToNewUserMarketingId(@Param("ea") String ea, @Param("oldUserMarketingId") String oldUserMarketingId, @Param("newUserMarketingId") String newUserMarketingId);
    
    @Update("UPDATE user_marketing_crm_account_account_relation SET user_name = #{userName}, update_time = now() WHERE ea = #{ea} AND crm_account_id=#{objectId}")
    int updateUserName(@Param("ea") String ea, @Param("objectId") String objectId, @Param("userName") String userName);
    
    @Update("UPDATE user_marketing_crm_account_account_relation SET email = #{email}, update_time = now() WHERE ea = #{ea} AND crm_account_id=#{objectId}")
    int updateEmail(@Param("ea") String ea, @Param("objectId") String objectId, @Param("email") String email);
    
    @Update("<script>UPDATE user_marketing_crm_account_account_relation as r SET user_name=tmp.user_name, email=tmp.email FROM (values" +
        "<foreach separator=',' collection='dataList' item='item'>(#{item.ea}, #{item.objectId}, #{item.userName}, #{item.email})</foreach>" +
        ") as tmp(ea, object_id, user_name, email) WHERE r.ea=tmp.ea AND r.crm_account_id=tmp.object_id" +
        "</script>")
    int batchUpdateNameAndEmail(@Param("dataList") List<Map<String, String>> dataList);
    
    @Select("SELECT * FROM user_marketing_crm_account_account_relation WHERE ea = #{ea} AND crm_account_id = #{crmAccountId}")
    UserMarketingCrmAccountAccountRelationEntity getByEaAndCrmAccountId(@Param("ea") String ea,@Param("crmAccountId") String crmAccountId);

    @Select("<script>select * from user_marketing_crm_account_account_relation where ea = #{ea} and user_marketing_id=ANY(ARRAY <foreach collection='userMarketingIds' open='[' close=']' separator=',' item='id'>#{id}</foreach>) </script>")
    List<UserMarketingCrmAccountAccountRelationEntity> listByUserMarketingIds(@Param("ea") String ea, @Param("userMarketingIds") Collection<String> userMarketingIds);

    @Select("<script>select user_marketing_id, email from user_marketing_crm_account_account_relation where ea = #{ea} and user_marketing_id=ANY(ARRAY <foreach collection='userMarketingIds' open='[' close=']' separator=',' item='id'>#{id}</foreach>) </script>")
    List<UserMarketingCrmAccountAccountRelationEntity> listEmailByUserMarketingIds(@Param("ea") String ea, @Param("userMarketingIds") Collection<String> userMarketingIds);

    @Select("<script>"
        + "SELECT DISTINCT ON (ea,user_marketing_id) id,ea,crm_account_id,user_marketing_id,create_time,update_time, user_name, email FROM "
        + "user_marketing_crm_account_account_relation "
        + "where ea = #{ea} "
        + "and "
        + "user_marketing_id = #{userMarketingId} "
        + " ORDER  BY  ea,user_marketing_id,create_time asc "
        + "</script>")
    UserMarketingCrmAccountAccountRelationEntity getEachDataFirstByUserMarketingId(@Param("ea") String ea, @Param("userMarketingId") String userMarketingId);

    @Select("<script>SELECT user_marketing_id from user_marketing_crm_account_account_relation WHERE ea=#{ea} AND crm_account_id=ANY(ARRAY <foreach collection='accountIds' open='[' close=']' separator=',' item='id'>#{id}</foreach>)</script>")
    List<String> listByAccountIds(@Param("ea") String ea, @Param("accountIds") Collection<String> accountIds);

    @Select("<script>SELECT * from user_marketing_crm_account_account_relation WHERE ea=#{ea} AND crm_account_id=ANY(ARRAY <foreach collection='accountIds' open='[' close=']' separator=',' item='id'>#{id}</foreach>)</script>")
    List<UserMarketingCrmAccountAccountRelationEntity> listUserMarketingCrmAccountAccountRelationEntityByAccountIds(@Param("ea") String ea, @Param("accountIds") Collection<String> accountIds);
    
    @Select("SELECT crm_account_id FROM user_marketing_crm_account_account_relation WHERE ea=#{ea} ORDER BY crm_account_id asc offset #{offset} limit #{limit}")
    List<String> pageListObjectIdByEa(@Param("ea")String ea, @Param("offset") int offset, @Param("limit") int limit);

    @Select("<script> " +
            "SELECT r.crm_account_id as object_id, r.user_marketing_id, u.phone, u.email, u.union_id FROM user_marketing_crm_account_account_relation r" +
            " LEFT JOIN user_marketing_account u ON r.user_marketing_id = u.id WHERE r.ea=#{ea} AND r.crm_account_id=ANY(ARRAY" +
            "<foreach collection='objectIds' open='[' close=']' separator=',' item='objectId'>#{objectId}</foreach>" +
            ")\n" +
            "</script>")
    List<ObjectIdWithMarketingUserIdAndPhoneDTO> listMarketingUserIdAndPhoneByObjectIds(@Param("ea") String ea, @Param("objectIds") Collection<String> objectIds);

    @Select("<script>"
        + "SELECT DISTINCT ON (ea,user_marketing_id) id,ea,crm_account_id,user_marketing_id,create_time,update_time, user_name, email FROM "
        + "user_marketing_crm_account_account_relation "
        + "where ea = #{ea} "
        + "and "
        + "user_marketing_id = #{userMarketingId} "
        + " ORDER  BY  ea,user_marketing_id,create_time DESC "
        + "</script>")
    UserMarketingCrmAccountAccountRelationEntity getEachDataLastByUserMarketingId(@Param("ea") String ea, @Param("userMarketingId") String userMarketingId);
   
    @Delete("<script>"
    + "delete FROM user_marketing_crm_account_account_relation WHERE ea = #{ea} AND user_marketing_id =ANY(ARRAY "
    +   "<foreach collection='userMarketingIds' item='id' open='[' close=']' separator=','>#{id}</foreach> )"
    + "</script>")
    int deleteByUserMarketingIds(@Param("ea") String ea, @Param("userMarketingIds") List<String> userMarketingIds);

    @Select("SELECT * FROM user_marketing_crm_account_account_relation WHERE ea = #{ea} AND email = #{email}")
    List<UserMarketingCrmAccountAccountRelationEntity> listByEmail(@Param("ea") String ea, @Param("email") String email);

}
