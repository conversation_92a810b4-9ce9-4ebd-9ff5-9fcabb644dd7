package com.facishare.marketing.provider.manager.mail.result;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhengh on 2020/6/3.
 */
@Data
public class MailStatisticsResp implements Serializable {
    private String labelId;
    private Long deliveredNum;
    private Long openNum;
    private Long clickNum;
    private Long openUserNum;
    private Long clickUserNum;

    public boolean isAvailable() {
        return (deliveredNum != null && deliveredNum > 0)
                || (openNum != null && openNum > 0)
                || (clickNum != null && clickNum > 0)
                || (openUserNum != null && openUserNum > 0)
                || (clickUserNum != null && clickUserNum > 0);
    }
}
