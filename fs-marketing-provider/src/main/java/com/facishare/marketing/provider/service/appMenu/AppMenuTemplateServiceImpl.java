package com.facishare.marketing.provider.service.appMenu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.eservice.base.result.EserviceResult;
import com.facishare.eservice.rest.common.Arg1;
import com.facishare.eservice.rest.common.HeaderObj;
import com.facishare.eservice.rest.online.model.QueryKnowledgeUrlModel;
import com.facishare.eservice.rest.online.service.KnowledgeService;
import com.facishare.marketing.api.arg.appMenu.*;
import com.facishare.marketing.api.result.userrelation.UserRelationVO;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.vo.appMenu.*;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.UserTagConstants;
import com.facishare.marketing.common.enums.CrmMemberFieldEnum;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.I18nKeyStaticEnum;
import com.facishare.marketing.common.enums.MemberTypeEnum;
import com.facishare.marketing.common.enums.appMenu.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.material.MaterialTypeEnum;
import com.facishare.marketing.common.enums.user.UserRelationTypeEnum;
import com.facishare.marketing.common.enums.user.UserTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.bo.appMenu.AppMenuTagBO;
import com.facishare.marketing.provider.bo.appMenu.AppMenuTemplateScopeBO;
import com.facishare.marketing.provider.bo.appMenu.UserMenuTemplateStatisticsBO;
import com.facishare.marketing.provider.dao.UserTagDao;
import com.facishare.marketing.provider.dao.appMenu.AppMenuDetailDAO;
import com.facishare.marketing.provider.dao.appMenu.AppMenuTemplateDAO;
import com.facishare.marketing.provider.dao.appMenu.UserAppMenuTemplateRelationDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.material.MaterialShowSettingDAO;
import com.facishare.marketing.provider.dto.PartnerUserDTO;
import com.facishare.marketing.provider.dto.UserRelationDTO;
import com.facishare.marketing.provider.entity.BaseEntity;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.UserTagEntity;
import com.facishare.marketing.provider.entity.appMenu.AppMenuDetailEntity;
import com.facishare.marketing.provider.entity.appMenu.AppMenuTemplateEntity;
import com.facishare.marketing.provider.entity.appMenu.UserAppMenuTemplateRelationEntity;
import com.facishare.marketing.provider.entity.material.MaterialShowSettingEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerResult.qywx.Department;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult;
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.paasauthrestapi.result.OuterTenantIdGroupIdsData;
import com.fxiaoke.paasauthrestapi.result.TenantGroupResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("appMenuTemplateService")
public class AppMenuTemplateServiceImpl implements AppMenuTemplateService {

    @Autowired
    private AppMenuTemplateDAO appMenuTemplateDAO;

    @Autowired
    private UserAppMenuTemplateRelationDAO userAppMenuTemplateRelationDAO;

    @Autowired
    private AppMenuDetailDAO appMenuDetailDAO;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private KnowledgeService knowledgeService;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private AuthManager authManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private MaterialShowSettingDAO materialShowSettingDAO;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private UserTagDao userTagDao;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private AuthPartnerManager authPartnerManager;

    @Autowired
    private AppMenuTemplateManager appMenuTemplateManager;

    @Value("${partner.appid}")
    private String partnerAppId;


    @Override
    public Result<String> createTemplate(CreateTemplateArg arg) {
        if (StringUtils.isBlank(arg.getName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AppMenuTemplateEntity appMenuTemplateEntity = appMenuTemplateDAO.getByName(arg.getEa(), arg.getName());
        if (appMenuTemplateEntity != null) {
            return Result.newError(SHErrorCode.HEADLINES_NAME_EXISTED);
        }
        String ea = arg.getEa();
        AppMenuTemplateEntity forInsert = new AppMenuTemplateEntity();
        forInsert.setEa(ea);
        String templateId = UUIDUtil.getUUID();
        forInsert.setId(templateId);
        forInsert.setName(arg.getName());
        forInsert.setTitle(I18nKeyEnum.MARK_APP_MENU_TEMPLATE_TITLE.getI18nKey());
        forInsert.setStatus(AppMenuTemplateStatusEnum.ENABLE.getStatus());
        forInsert.setType(AppMenuTemplateTypeEnum.CUSTOMIZE.getType());
        forInsert.setCreateUserId(arg.getFsUserId());
        forInsert.setUpdateUserId(arg.getFsUserId());
        forInsert.setUserType(arg.getUserType());
        forInsert.setMiniAppVisitType(MiniAppVisitTypeEnum.VISIT_MAIN_PAGE.getCode());
        // 创建模板时 适用范围为空
        forInsert.setScope("{}");
        appMenuTemplateDAO.batchInsert(Lists.newArrayList(forInsert));

        List<MaterialTypeEnum> materialTypeEnumList = null;
        if (arg.getUserType().equals(UserRelationTypeEnum.EMPLOYEE.getCode())) {
            materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(MaterialTypeEnum::isDefaultShowEmployeeMenuTemplate).sorted(Comparator.comparingInt(MaterialTypeEnum::getOrder)).collect(Collectors.toList());
        } else if (arg.getUserType().equals(UserRelationTypeEnum.PARTNER.getCode())) {
            materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(MaterialTypeEnum::isDefaultShowPartnerMenuTemplate).sorted(Comparator.comparingInt(MaterialTypeEnum::getOrder)).collect(Collectors.toList());
        } else if (arg.getUserType().equals(UserRelationTypeEnum.MEMBER.getCode())) {
            materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(MaterialTypeEnum::isDefaultShowMemberMenuTemplate).sorted(Comparator.comparingInt(MaterialTypeEnum::getOrder)).collect(Collectors.toList());
        }
        createTemplateMenuRelation(materialTypeEnumList, ea, templateId, false);
        return Result.newSuccess(forInsert.getId());
    }

    private List<AppMenuDetailEntity> createTemplateMenuRelation(List<MaterialTypeEnum> materialTypeEnumList, String ea, String templateId, boolean getNewOrderNum) {
        if (CollectionUtils.isEmpty(materialTypeEnumList)) {
            return Lists.newArrayList();
        }
        Integer orderNum = null;
        if (getNewOrderNum) {
            orderNum = appMenuDetailDAO.getMaxOrderNum(ea, templateId);
        }
        List<AppMenuDetailEntity> appMenuDetailEntityList = Lists.newArrayList();
        for (MaterialTypeEnum materialTypeEnum : materialTypeEnumList) {
            AppMenuDetailEntity appMenuDetailEntity = new AppMenuDetailEntity();
            appMenuDetailEntity.setEa(ea);
            appMenuDetailEntity.setId(UUIDUtil.getUUID());
            appMenuDetailEntity.setMenuTemplateId(templateId);
            // 默认展示名字写null,主要是为了多语，前端如果判断名字为空，会展示默认的多语
            appMenuDetailEntity.setName(null);
            appMenuDetailEntity.setTargetMaterialType(materialTypeEnum.getType());
            appMenuDetailEntity.setObjectAccessibleRule(getDefaultObjectAccessibleRuleByType(materialTypeEnum));
            appMenuDetailEntity.setOrderNum(orderNum == null ? materialTypeEnum.getOrder() : ++orderNum);
            appMenuDetailEntity.setStatus(MenuStatusEnum.NORMAL.getStatus());
            appMenuDetailEntityList.add(appMenuDetailEntity);
        }
        appMenuDetailDAO.batchInsert(appMenuDetailEntityList);
        log.info("createTemplateMenuRelation, templateId: {}, appMenuDetailEntityList: {}", templateId, appMenuDetailEntityList);
        return appMenuDetailEntityList;
    }

    @Override
    public Result<PageResult<AppMenuTemplateListVO>> list(QueryTemplateArg arg) {
        if (arg.getPageNum() == null || arg.getPageSize() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        PageResult<AppMenuTemplateListVO> pageResult = new PageResult<>();
        List<AppMenuTemplateListVO> appMenuTemplateListVOList = Lists.newArrayList();
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setTotalCount(0);
        pageResult.setResult(appMenuTemplateListVOList);

        String ea = arg.getEa();
        Page<UserRelationEntity> page = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
        List<AppMenuTemplateEntity> templateEntityList = appMenuTemplateDAO.list(ea, page);
        if (CollectionUtils.isEmpty(templateEntityList)) {
            if (arg.getPageNum() == 1) {
                // 如果第一页都没有数据，可能是系统模板没刷上去  补刷一下
                refreshAllSystemTemplate(ea);
                templateEntityList = appMenuTemplateDAO.list(ea, page);
            }
            if (CollectionUtils.isEmpty(templateEntityList)) {
                return Result.newSuccess(pageResult);
            }
        }
        pageResult.setTotalCount(page.getTotalNum());

        List<String> templateIdList = templateEntityList.stream().map(BaseEntity::getId).collect(Collectors.toList());
        // 查询模板的用户数量
        List<UserMenuTemplateStatisticsBO> userMenuTemplateStatisticsList = userAppMenuTemplateRelationDAO.statisticsByTemplate(ea, templateIdList);
        Map<String, Integer> templateIdToUserCountMap = userMenuTemplateStatisticsList.stream().collect(Collectors.toMap(UserMenuTemplateStatisticsBO::getTemplateId, UserMenuTemplateStatisticsBO::getCount));
        // 获取适用范围
        Map<String, AddressBookScopeVO> templateToFsAddressScopeVO = buildFsAddrssBookScopeMap(ea, templateEntityList);
        Map<String, AddressBookScopeVO> templateToQywxAddressScopeVO = buildQywxAddrssBookScopeMap(ea, templateEntityList);
        Map<String, PassObjScopeVO> templateToMemberObjScopeVO = buildMemberObjScopeMap(templateEntityList);
        Map<String, EnterpriseRelationScopeVO> templateToEnterpriseRelationScopeVO = buildEnterpriseRelationScopeMap(ea, templateEntityList);
        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            // 替换系统模板的多语名字
            replaceSystemTemplateName(appMenuTemplateEntity);
            AppMenuTemplateListVO appMenuTemplateListVO = BeanUtil.copy(appMenuTemplateEntity, AppMenuTemplateListVO.class);
            // 填充适用范围
            appMenuTemplateListVO.setFsAddressBookScope(templateToFsAddressScopeVO.get(appMenuTemplateEntity.getId()));
            appMenuTemplateListVO.setQywxAddressBookScope(templateToQywxAddressScopeVO.get(appMenuTemplateEntity.getId()));
            appMenuTemplateListVO.setMemberScope(templateToMemberObjScopeVO.get(appMenuTemplateEntity.getId()));
            appMenuTemplateListVO.setEnterpriseRelationScope(templateToEnterpriseRelationScopeVO.get(appMenuTemplateEntity.getId()));
            // 填充模板使用人数
            int userCount = templateIdToUserCountMap.getOrDefault(appMenuTemplateEntity.getId(), 0);
            appMenuTemplateListVO.setUserCount(userCount);
            appMenuTemplateListVOList.add(appMenuTemplateListVO);
        }
        return Result.newSuccess(pageResult);
    }

    private void refreshAllSystemTemplate(String ea) {
        createSystemTemplate(ea, AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType());
        createSystemTemplate(ea, AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType());
        createSystemTemplate(ea, AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType());
    }


    // 如果系统模板的名字还是多语的key，替换成真正的语言
    private void replaceSystemTemplateName(AppMenuTemplateEntity appMenuTemplateEntity) {
        if (AppMenuTemplateTypeEnum.CUSTOMIZE.getType().equals(appMenuTemplateEntity.getType())) {
            return;
        }
        String name = appMenuTemplateEntity.getName();
        I18nKeyEnum i18nKeyEnum = I18nKeyEnum.getByI18nKey(name);
        if (i18nKeyEnum == null) {
            return;
        }
        appMenuTemplateEntity.setName(I18nUtil.get(i18nKeyEnum));
    }

    private Map<String, AddressBookScopeVO> buildFsAddrssBookScopeMap(String ea, List<AppMenuTemplateEntity> templateEntityList) {
        Set<Integer> fsDepartmentIdSet = Sets.newHashSet();
        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
            if (scope.getFsAddressBookScope() != null && CollectionUtils.isNotEmpty(scope.getFsAddressBookScope().getDepartmentIdList())) {
                fsDepartmentIdSet.addAll(scope.getFsAddressBookScope().getDepartmentIdList());
            }
        }
        // 查询纷享部门信息
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Map<Integer, DepartmentDto> fsDepartmentIdToDepartmentDtoMap = Maps.newHashMap();
        for (List<Integer> partition : Lists.partition(Lists.newArrayList(fsDepartmentIdSet), 100)) {
            List<DepartmentDto> departmentDtoList = authManager.batchGetByDepartmentIds(ei, partition);
            departmentDtoList.forEach(e -> fsDepartmentIdToDepartmentDtoMap.put(e.getDepartmentId(), e));
        }
        Map<String, AddressBookScopeVO> templateIdToScopeMap = Maps.newHashMap();
        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
            // 填充适用范围-纷享通讯录
            AppMenuTemplateScopeBO.FsAddressBookScope fsAddressBookScope = scope.getFsAddressBookScope();
            if (fsAddressBookScope != null && CollectionUtils.isNotEmpty(fsAddressBookScope.getDepartmentIdList())) {
                AddressBookScopeVO fsAddressBookScopeVO = new AddressBookScopeVO();
                List<AddressBookScopeVO.Department> departmentList = Lists.newArrayList();
                for (Integer departmentId : fsAddressBookScope.getDepartmentIdList()) {
                    AddressBookScopeVO.Department department = new AddressBookScopeVO.Department();
                    department.setId(departmentId);
                    DepartmentDto departmentDto = fsDepartmentIdToDepartmentDtoMap.get(departmentId);
                    department.setName(departmentDto == null ? null : departmentDto.getName());
                    departmentList.add(department);
                }
                fsAddressBookScopeVO.setDepartmentList(departmentList);
                templateIdToScopeMap.put(appMenuTemplateEntity.getId(), fsAddressBookScopeVO);
            }
        }
        return templateIdToScopeMap;
    }

    private Map<String, AddressBookScopeVO> buildQywxAddrssBookScopeMap(String ea, List<AppMenuTemplateEntity> templateEntityList) {

        Set<Integer> qywxDepartmentIdSet = Sets.newHashSet();
        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
            if (scope.getQywxAddressBookScope() != null && CollectionUtils.isNotEmpty(scope.getQywxAddressBookScope().getDepartmentIdList())) {
                qywxDepartmentIdSet.addAll(scope.getQywxAddressBookScope().getDepartmentIdList());
            }
        }
        // 查询企微部门信息
        Map<Integer, Department> qywxDepartmentIdToDepartmentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxDepartmentIdSet)) {
            boolean containAllDepartment = qywxDepartmentIdSet.remove(AuthManager.defaultAllDepartment);
            if (containAllDepartment) {
                Department department = new Department();
                department.setName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
                qywxDepartmentIdToDepartmentMap.put(AuthManager.defaultAllDepartment, department);
            }
            String accessToken = qywxManager.getAccessToken(ea);
            DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
            if (departmentListResult != null) {
                departmentListResult.getDepartmentList().stream().peek(e -> e.setId(e.getId() + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID))
                        .filter(e -> qywxDepartmentIdSet.contains(e.getId())).forEach(e -> qywxDepartmentIdToDepartmentMap.put(e.getId(), e));
            }

        }
        Map<String, AddressBookScopeVO> templateIdToScopeMap = Maps.newHashMap();
        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
            // 填充适用范围-企微通讯录
            AppMenuTemplateScopeBO.QywxAddressBookScope qywxAddressBookScope = scope.getQywxAddressBookScope();
            if (qywxAddressBookScope != null && CollectionUtils.isNotEmpty(qywxAddressBookScope.getDepartmentIdList())) {
                AddressBookScopeVO qywxAddressBookScopeVO = new AddressBookScopeVO();
                List<AddressBookScopeVO.Department> departmentList = Lists.newArrayList();
                for (Integer departmentId : qywxAddressBookScope.getDepartmentIdList()) {
                    AddressBookScopeVO.Department department = new AddressBookScopeVO.Department();
                    department.setId(departmentId);
                    Department departmentDto = qywxDepartmentIdToDepartmentMap.get(departmentId);
                    department.setName(departmentDto == null ? null : departmentDto.getName());
                    departmentList.add(department);
                }
                qywxAddressBookScopeVO.setDepartmentList(departmentList);
                templateIdToScopeMap.put(appMenuTemplateEntity.getId(), qywxAddressBookScopeVO);
            }
        }
        return templateIdToScopeMap;
    }

    private Map<String, PassObjScopeVO> buildMemberObjScopeMap(List<AppMenuTemplateEntity> templateEntityList) {
        Map<String, PassObjScopeVO> templateIdToScopeMap = Maps.newHashMap();
        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
            AppMenuTemplateScopeBO.MemberScope memberScope = scope.getMemberScope();
            if (memberScope != null && CollectionUtils.isNotEmpty(memberScope.getFilters())) {
                PassObjScopeVO passObjScopeVO = new PassObjScopeVO();
                passObjScopeVO.setFilters(memberScope.getFilters());
                templateIdToScopeMap.put(appMenuTemplateEntity.getId(), passObjScopeVO);
            }
        }
        return templateIdToScopeMap;
    }

    private Map<String, EnterpriseRelationScopeVO> buildEnterpriseRelationScopeMap(String ea, List<AppMenuTemplateEntity> templateEntityList) {
        Map<String, EnterpriseRelationScopeVO> templateIdToScopeMap = Maps.newHashMap();
        Map<String, String> outTenantGroupMap = new HashMap<>();
        Map<String, String> outTenantMap = new HashMap<>();
        Set<String> outTeantIdSet = new HashSet<>();
        Set<String> outTenantGroupIdSet = new HashSet<>();

        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
            AppMenuTemplateScopeBO.EnterpriseRelationScope enterpriseRelationScope = scope.getEnterpriseRelationScope();
            if (enterpriseRelationScope == null) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(enterpriseRelationScope.getOutTenantGroupIdList())) {
                outTenantGroupIdSet.addAll(enterpriseRelationScope.getOutTenantGroupIdList());
            }
            if (CollectionUtils.isNotEmpty(enterpriseRelationScope.getOutTenantIdList())) {
                List<String> outTenantIds = enterpriseRelationScope.getOutTenantIdList().stream().map(String::valueOf).filter(Objects::nonNull).filter(e -> !e.equals(String.valueOf(AuthManager.defaultAllDepartment))).collect(Collectors.toList());
                outTeantIdSet.addAll(outTenantIds);
            }
        }

        if (CollectionUtils.isNotEmpty(outTenantGroupIdSet)) {
            List<TenantGroupResult> tenantGroupResultList = authPartnerManager.batchGetTenantGroupByIds(ea, Lists.newArrayList(outTenantGroupIdSet));
            if (CollectionUtils.isNotEmpty(tenantGroupResultList)) {
                outTenantGroupMap = tenantGroupResultList.stream().collect(Collectors.toMap(TenantGroupResult::getId, TenantGroupResult::getName));
            }
        }
        if (CollectionUtils.isNotEmpty(outTeantIdSet)) {
            List<ObjectData> tenantResultList = authPartnerManager.batchGetOutTenantByIds(ea, -10000, Lists.newArrayList("_id", "name"), Lists.newArrayList(outTeantIdSet));
            if (CollectionUtils.isNotEmpty(tenantResultList)) {
                outTenantMap = tenantResultList.stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName));
            }
        }

        for (AppMenuTemplateEntity appMenuTemplateEntity : templateEntityList) {
            EnterpriseRelationScopeVO enterpriseRelationScopeVO = new EnterpriseRelationScopeVO();
            templateIdToScopeMap.put(appMenuTemplateEntity.getId(), enterpriseRelationScopeVO);
            AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
            AppMenuTemplateScopeBO.EnterpriseRelationScope enterpriseRelationScope = scope.getEnterpriseRelationScope();
            if (enterpriseRelationScope == null) {
                continue;
            }

            if (CollectionUtils.isNotEmpty(enterpriseRelationScope.getOutTenantGroupIdList())) {
                List<EnterpriseRelationScopeVO.TenantGroupInfo> tenantGroupList = Lists.newArrayList();
                ;
                for (String tenantGroupId : enterpriseRelationScope.getOutTenantGroupIdList()) {
                    EnterpriseRelationScopeVO.TenantGroupInfo tenantGroupInfo = new EnterpriseRelationScopeVO.TenantGroupInfo();
                    tenantGroupInfo.setOutTenantGroupId(tenantGroupId);
                    tenantGroupInfo.setOutTenantGroupName(outTenantGroupMap.get(tenantGroupId));
                    tenantGroupList.add(tenantGroupInfo);
                }
                enterpriseRelationScopeVO.setOutTenantGroupList(tenantGroupList);
            }
            if (CollectionUtils.isNotEmpty(enterpriseRelationScope.getOutTenantIdList())) {
                List<EnterpriseRelationScopeVO.OutTenantInfo> outTenantList = Lists.newArrayList();
                for (Long outTenantId : enterpriseRelationScope.getOutTenantIdList()) {
                    EnterpriseRelationScopeVO.OutTenantInfo outTenantInfo = new EnterpriseRelationScopeVO.OutTenantInfo();
                    outTenantInfo.setOutTenantId(String.valueOf(outTenantId));
                    if ((long) AuthManager.defaultAllDepartment == outTenantId) {
                        outTenantInfo.setOutTenantName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
                    } else {
                        outTenantInfo.setOutTenantName(outTenantMap.get(outTenantInfo.getOutTenantId()));
                    }
                    outTenantList.add(outTenantInfo);
                }
                enterpriseRelationScopeVO.setOutTenantList(outTenantList);
            }
        }

        return templateIdToScopeMap;

    }

    @Override
    public Result<AppMenuTemplateDetailVO> detail(QueryTemplateDetailArg arg) {
        if (StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        AppMenuTemplateEntity appMenuTemplateEntity = appMenuTemplateDAO.getById(ea, arg.getId());
        if (appMenuTemplateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        // 替换系统模板的多语名字
        replaceSystemTemplateName(appMenuTemplateEntity);
        AppMenuTemplateDetailVO appMenuTemplateDetailVO = BeanUtil.copy(appMenuTemplateEntity, AppMenuTemplateDetailVO.class);
        appMenuTemplateDetailVO.setTitle(getTitle(appMenuTemplateEntity.getTitle()));
        appMenuTemplateDetailVO.setUserType(appMenuTemplateEntity.getUserType());
        // 填充适用范围
        Map<String, AddressBookScopeVO> templateToFsAddressScopeVO = buildFsAddrssBookScopeMap(ea, Lists.newArrayList(appMenuTemplateEntity));
        appMenuTemplateDetailVO.setFsAddressBookScope(templateToFsAddressScopeVO.get(appMenuTemplateEntity.getId()));

        Map<String, AddressBookScopeVO> templateToQywxAddressScopeVO = buildQywxAddrssBookScopeMap(ea, Lists.newArrayList(appMenuTemplateEntity));
        appMenuTemplateDetailVO.setQywxAddressBookScope(templateToQywxAddressScopeVO.get(appMenuTemplateEntity.getId()));

        Map<String, PassObjScopeVO> templateToMemberObjScopeVO = buildMemberObjScopeMap(Lists.newArrayList(appMenuTemplateEntity));
        appMenuTemplateDetailVO.setMemberScope(templateToMemberObjScopeVO.get(appMenuTemplateEntity.getId()));

        Map<String, EnterpriseRelationScopeVO> templateToEnterpriseRelationScopeVO = buildEnterpriseRelationScopeMap(ea, Lists.newArrayList(appMenuTemplateEntity));
        appMenuTemplateDetailVO.setEnterpriseRelationScope(templateToEnterpriseRelationScopeVO.get(appMenuTemplateEntity.getId()));

        // 填充菜单列表
        List<AppMenuDetailVO> menuList = buildAppMenuDetailList(ea, arg.getId());
        appMenuTemplateDetailVO.setMenuList(menuList);
        Set<Integer> fsUserIdSet = Sets.newHashSet(appMenuTemplateEntity.getCreateUserId(), appMenuTemplateEntity.getUpdateUserId());
        // 查询纷享员工信息
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsUserIdToFsUserMap = fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, Lists.newArrayList(fsUserIdSet), true);
        // 填充创建人
        if (Objects.equals(appMenuTemplateEntity.getCreateUserId(), SuperUserConstants.USER_ID)) {
            String createUserName = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193);
            appMenuTemplateDetailVO.setCreateUserName(createUserName);
        } else {
            FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsUserIdToFsUserMap.get(appMenuTemplateEntity.getCreateUserId());
            if (fsEmployeeMsg != null) {
                appMenuTemplateDetailVO.setCreateUserName(fsEmployeeMsg.getName());
            }
        }
        // 填充更新人
        Integer updateUserId = appMenuTemplateEntity.getUpdateUserId();
        if (updateUserId != null) {
            if (Objects.equals(updateUserId, SuperUserConstants.USER_ID)) {
                String updateUserName = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193);
                appMenuTemplateDetailVO.setUpdateUserName(updateUserName);
            } else {
                FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsUserIdToFsUserMap.get(updateUserId);
                if (fsEmployeeMsg != null) {
                    appMenuTemplateDetailVO.setUpdateUserName(fsEmployeeMsg.getName());
                }
            }
        }
        return Result.newSuccess(appMenuTemplateDetailVO);
    }

    private List<AppMenuDetailVO> buildAppMenuDetailList(String ea, String templateId) {
        AppMenuTemplateEntity appMenuTemplateEntity = appMenuTemplateDAO.getById(ea, templateId);
        List<AppMenuDetailEntity> appMenuDetailEntityList = appMenuDetailDAO.getOrderlyByTemplateId(ea, templateId);
        // 包含删除的菜单
        Set<Integer> targetMaterialTypeSet = appMenuDetailEntityList.stream().map(AppMenuDetailEntity::getTargetMaterialType).collect(Collectors.toSet());
        List<AppMenuDetailVO> menuList = Lists.newArrayList();
        //如果MaterialTypeEnum里面新增了菜单枚举，并且菜单需要默认展示在模板中 如果这个菜单没有落过库，这里写入数据库并返回
        List<MaterialTypeEnum> materialTypeEnumList = null;
        if (appMenuTemplateEntity.getUserType().equals(UserRelationTypeEnum.EMPLOYEE.getCode())) {
            materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(
                    e -> e.isDefaultShowEmployeeMenuTemplate() && !targetMaterialTypeSet.contains(e.getType())).collect(Collectors.toList());
        } else if (appMenuTemplateEntity.getUserType().equals(UserRelationTypeEnum.PARTNER.getCode())) {
            materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(
                    e -> e.isDefaultShowPartnerMenuTemplate() && !targetMaterialTypeSet.contains(e.getType())).collect(Collectors.toList());
        } else if (appMenuTemplateEntity.getUserType().equals(UserRelationTypeEnum.MEMBER.getCode())) {
            materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(
                    e -> e.isDefaultShowMemberMenuTemplate() && !targetMaterialTypeSet.contains(e.getType())).collect(Collectors.toList());
        }

        List<AppMenuDetailEntity> newInsertmenuDetailList = createTemplateMenuRelation(materialTypeEnumList, ea, templateId, true);
        appMenuDetailEntityList.addAll(newInsertmenuDetailList);
        appMenuDetailEntityList.sort(Comparator.comparing(AppMenuDetailEntity::getOrderNum));
        for (AppMenuDetailEntity appMenuDetailEntity : appMenuDetailEntityList) {
            if (MenuStatusEnum.DELETED.getStatus().equals(appMenuDetailEntity.getStatus())) {
                continue;
            }
            AppMenuDetailVO appMenuDetailVO = new AppMenuDetailVO();
            appMenuDetailVO.setId(appMenuDetailEntity.getId());
            appMenuDetailVO.setName(appMenuDetailEntity.getName());
            appMenuDetailVO.setTargetMaterialType(appMenuDetailEntity.getTargetMaterialType());
            appMenuDetailVO.setColor(appMenuDetailEntity.getColor());
            appMenuDetailVO.setLineIcon(appMenuDetailEntity.getLineIcon());
            appMenuDetailVO.setFlatIcon(appMenuDetailEntity.getFlatIcon());
            menuList.add(appMenuDetailVO);
        }
        return menuList;
    }

    @Override
    public Result<Void> updateBaseInfo(UpdateTemplateBaseInfoArg arg) {
        if (StringUtils.isBlank(arg.getId()) || (StringUtils.isBlank(arg.getName()) && StringUtils.isBlank(arg.getTitle()) && arg.getIconType() == null)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isNotBlank(arg.getMiniappVisitType()) && MiniAppVisitTypeEnum.getByCode(arg.getMiniappVisitType()) == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        AppMenuTemplateEntity templateEntity = appMenuTemplateDAO.getById(ea, arg.getId());
        if (templateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        log.info("updateBaseInfo arg:{}", arg);
        AppMenuTemplateEntity forUpdate = new AppMenuTemplateEntity();
        forUpdate.setEa(ea);
        forUpdate.setId(arg.getId());
        if (StringUtils.isNotBlank(arg.getName()) && !arg.getName().equals(templateEntity.getName())) {
            AppMenuTemplateEntity sameNameTemplateEntity = appMenuTemplateDAO.getByName(ea, arg.getName());
            if (sameNameTemplateEntity != null && !sameNameTemplateEntity.getId().equals(arg.getId())) {
                return Result.newError(SHErrorCode.HEADLINES_NAME_EXISTED);
            }
            forUpdate.setName(arg.getName());
        }
        if (StringUtils.isNotBlank(arg.getTitle()) && !arg.getTitle().equals(templateEntity.getTitle())) {
            forUpdate.setTitle(arg.getTitle());
        }
        if (arg.getIconType() != null) {
            forUpdate.setIconType(arg.getIconType());
        }
        if (StringUtils.isNotBlank(arg.getMiniappVisitType())) {
            forUpdate.setMiniAppVisitType(arg.getMiniappVisitType());
        }
        forUpdate.setUpdateUserId(arg.getFsUserId());
        appMenuTemplateDAO.update(forUpdate);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateScope(UpdateTemplateScopeArg arg) {
        if (StringUtils.isBlank(arg.getId()) || (arg.getFsAddressBookScope() == null && arg.getQywxAddressBookScope() == null && arg.getMemberScope() == null && arg.getEnterpriseRelationScope() == null)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        String templateId = arg.getId();
        AppMenuTemplateEntity templateEntity = appMenuTemplateDAO.getById(ea, templateId);
        if (templateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (!AppMenuTemplateTypeEnum.CUSTOMIZE.getType().equals(templateEntity.getType())) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_246));
        }
        if (StringUtils.equals(templateEntity.getUserType(), UserRelationTypeEnum.EMPLOYEE.getCode()) && (arg.getFsAddressBookScope() == null && arg.getQywxAddressBookScope() == null && arg.getMemberScope() == null)) {
            log.info("updateScope user employee scope failed arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.equals(templateEntity.getUserType(), UserRelationTypeEnum.PARTNER.getCode()) && arg.getEnterpriseRelationScope() == null) {
            log.info("updateScope user partner scope failed arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.equals(templateEntity.getUserType(), UserRelationTypeEnum.MEMBER.getCode()) && arg.getMemberScope() == null) {
            log.info("updateScope user member scope failed arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        log.info("updateScope arg:{}", arg);
        // 更新模板的scope字段
        AppMenuTemplateScopeBO appMenuTemplateScopeBO = BeanUtil.copyByGson(arg, AppMenuTemplateScopeBO.class);
        AppMenuTemplateEntity forUpdate = new AppMenuTemplateEntity();
        forUpdate.setEa(ea);
        forUpdate.setId(templateId);
        forUpdate.setScope(JsonUtil.toJson(appMenuTemplateScopeBO));
        forUpdate.setUpdateUserId(arg.getFsUserId());
        appMenuTemplateDAO.update(forUpdate);
        // 将user的关联关系删除掉
        List<Integer> oldUserIdList = userAppMenuTemplateRelationDAO.getFsUserIdByTemplateId(ea, templateId);
        int userCount = deleteUserByTemplateId(ea, templateId);
        log.info("updateScope, templeteId: {} delete user count:{}", templateId, userCount);
        // 处理纷享通讯录
        AppMenuTemplateScopeBO.FsAddressBookScope fsAddressBookScope = appMenuTemplateScopeBO.getFsAddressBookScope();
        insertUserTemplateRelationByFsAddressScope(ea, fsAddressBookScope, templateId);
        // 处理企微通讯录
        AppMenuTemplateScopeBO.QywxAddressBookScope qywxAddressBookScope = appMenuTemplateScopeBO.getQywxAddressBookScope();
        insertUserTemplateRelationByQywxAddressScope(ea, qywxAddressBookScope, templateId);
        // 处理会员
        AppMenuTemplateScopeBO.MemberScope memberScope = appMenuTemplateScopeBO.getMemberScope();
        insertUserTemplateRelationByMemberScope(ea, memberScope, templateEntity.getUserType(), templateId);
        //处理互联通讯录
        AppMenuTemplateScopeBO.EnterpriseRelationScope enterpriseRelationScope = appMenuTemplateScopeBO.getEnterpriseRelationScope();
        insertUserTemplateRelationByEnterpriseScope(ea, enterpriseRelationScope, templateId);
        // 只要使用范围有变更，就把缓存的数据清掉
        List<Integer> newUserIdList = userAppMenuTemplateRelationDAO.getFsUserIdByTemplateId(ea, templateId);
        List<Integer> needDeleteCacheUserIdList = Lists.newArrayList(oldUserIdList);
        needDeleteCacheUserIdList.addAll(newUserIdList);
        appMenuTemplateManager.deleteShowTemplateFromRedis(ea, needDeleteCacheUserIdList);
        return Result.newSuccess();
    }

    private int deleteUserByTemplateId(String ea, String templateId) {
        int count = 0;
        List<String> ids = userAppMenuTemplateRelationDAO.getIdByTemplateLimit(ea, templateId, 1000);
        while(CollectionUtils.isNotEmpty(ids)){
            int currentDeleteCount = userAppMenuTemplateRelationDAO.deleteByIds(ea, ids);
            count += currentDeleteCount;
            ids = userAppMenuTemplateRelationDAO.getIdByTemplateLimit(ea, templateId, 1000);
        }

        return count;
    }

    private void insertUserTemplateRelationByMemberScope(String ea, AppMenuTemplateScopeBO.MemberScope memberScope, String templateUserType, String templateId) {
        // 处理会员
        if (memberScope == null || CollectionUtils.isEmpty(memberScope.getFilters())) {
            return;
        }
        boolean isExistMemberTypeField = false;
        try {
            isExistMemberTypeField = crmV2Manager.checkObjectFieldExist(ea, CrmObjectApiNameEnum.MEMBER.getName(), CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
        } catch (Exception e) {
            log.warn("insertUserTemplateRelationByMemberScope checkObjectFieldExist error, ea: {}", ea, e);
        }
        if (!isExistMemberTypeField) {
            log.info("insertUserTemplateRelationByMemberScope do not have member type field, ea: {}", ea);
            return;
        }
        List<PaasQueryArg.Condition> conditionList = BeanUtil.copyByGson(memberScope.getFilters(), PaasQueryArg.Condition.class);
        List<String> memberTypeList = getMemberTypeByUserType(templateUserType);
        PaasQueryArg.Condition memberTypeCondition = new PaasQueryArg.Condition(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), memberTypeList, OperatorConstants.IN);
        conditionList.add(memberTypeCondition);
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MEMBER.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.setFilters(conditionList);
        paasQueryFilterArg.setQuery(paasQueryArg);
        paasQueryFilterArg.setSelectFields(Lists.newArrayList(CrmMemberFieldEnum.ID.getApiName(), CrmMemberFieldEnum.NAME.getApiName(), CrmMemberFieldEnum.MEMBER_TYPE.getApiName()));
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg);
        int count = 0;
        String lastId = null;
        int pageSize = 100;
        while (count < totalCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, lastId, pageSize);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            count += objectDataList.size();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
            List<String> memberObjIdList = Lists.newArrayList();
            Map<String, ObjectData> memberObjIdToDataMap = Maps.newHashMap();
            for (ObjectData objectData : objectDataList) {
                String id = objectData.getId();
                memberObjIdList.add(id);
                memberObjIdToDataMap.put(id, objectData);
            }
            List<UserRelationEntity> userRelationEntityList = userRelationManager.getByMemberIdList(ea, memberObjIdList);
            List<UserAppMenuTemplateRelationEntity> userTemplateRelationEntityList = Lists.newArrayList();
            for (UserRelationEntity userRelationEntity : userRelationEntityList) {
                UserAppMenuTemplateRelationEntity userTemplateRelationEntity = new UserAppMenuTemplateRelationEntity();
                userTemplateRelationEntity.setId(UUIDUtil.getUUID());
                userTemplateRelationEntity.setEa(ea);
                Integer fsUserId = userRelationEntity.getFsUserId();
                if (fsUserId == null) {
                    log.warn("user relation fsUserId is nul, entity: {}", userRelationEntity);
                    continue;
                }
                userTemplateRelationEntity.setFsUserId(fsUserId);
                String userType;
                if (QywxUserConstants.isFsUserId(fsUserId)) {
                    userType = UserTypeEnum.CRM.getCode();
                } else if (QywxUserConstants.isQywxVirtualUserId(fsUserId)) {
                    userType = UserTypeEnum.QYWX.getCode();
                } else {
                    ObjectData objectData = memberObjIdToDataMap.get(userRelationEntity.getMemberId());
                    String memberType = objectData.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
                    UserTypeEnum userTypeEnum = UserTypeEnum.getByMemberType(memberType);
                    userType = userTypeEnum.getCode();
                }
                userTemplateRelationEntity.setUserType(userType);
                userTemplateRelationEntity.setMenuTemplateId(templateId);
                userTemplateRelationEntityList.add(userTemplateRelationEntity);
            }
            if (CollectionUtils.isNotEmpty(userTemplateRelationEntityList)) {
                Lists.partition(userTemplateRelationEntityList, 100).forEach(e -> userAppMenuTemplateRelationDAO.batchInsert(e));
            }
        }
    }

    private static List<String> getMemberTypeByUserType(String userType) {
        List<String> memberTypeList = Lists.newArrayList();
        if (UserRelationTypeEnum.MEMBER.getCode().equals(userType)) {
            memberTypeList.add(MemberTypeEnum.SPREAD_EMPLOYEE.getType());
        } else if (UserRelationTypeEnum.PARTNER.getCode().equals(userType)) {
            memberTypeList.add(MemberTypeEnum.PARTNER.getType());
        } else {
            memberTypeList.add(MemberTypeEnum.EMPLOYEE.getType());
        }
        return memberTypeList;
    }

    private void insertUserTemplateRelationByQywxAddressScope(String ea, AppMenuTemplateScopeBO.QywxAddressBookScope qywxAddressBookScope, String templateId) {
        if (qywxAddressBookScope == null || CollectionUtils.isEmpty(qywxAddressBookScope.getDepartmentIdList())) {
            return;
        }
        List<Integer> departmentIdList = qywxAddressBookScope.getDepartmentIdList();
        if (departmentIdList.contains(AuthManager.defaultAllDepartment)) {
            String accessToken = qywxManager.getAccessToken(ea);
            DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
            if (departmentListResult != null && CollectionUtils.isNotEmpty(departmentListResult.getDepartmentList())) {
                departmentIdList = departmentListResult.getDepartmentList().stream().map(Department::getId).collect(Collectors.toList());
            }
        }
        departmentIdList = departmentIdList.stream().map(e -> e - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
        List<String> qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(ea, departmentIdList);
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByQywxUserIdList(ea, qywxUserIds);
        List<UserAppMenuTemplateRelationEntity> userTemplateRelationEntityList = Lists.newArrayList();
        for (UserRelationEntity userRelationEntity : userRelationEntityList) {
            UserAppMenuTemplateRelationEntity userTemplateRelationEntity = new UserAppMenuTemplateRelationEntity();
            userTemplateRelationEntity.setId(UUIDUtil.getUUID());
            userTemplateRelationEntity.setEa(ea);
            userTemplateRelationEntity.setFsUserId(userRelationEntity.getFsUserId());
            String userType = QywxUserConstants.isQywxVirtualUserId(userRelationEntity.getFsUserId()) ? UserTypeEnum.QYWX.getCode() : UserTypeEnum.CRM.getCode();
            userTemplateRelationEntity.setUserType(userType);
            userTemplateRelationEntity.setMenuTemplateId(templateId);
            userTemplateRelationEntityList.add(userTemplateRelationEntity);
        }
        if (CollectionUtils.isNotEmpty(userTemplateRelationEntityList)) {
            Lists.partition(userTemplateRelationEntityList, 100).forEach(e -> userAppMenuTemplateRelationDAO.batchInsert(e));
        }
    }

    private void insertUserTemplateRelationByFsAddressScope(String ea, AppMenuTemplateScopeBO.FsAddressBookScope fsAddressBookScope, String templateId) {
        if (fsAddressBookScope == null || CollectionUtils.isEmpty(fsAddressBookScope.getDepartmentIdList())) {
            return;
        }
        List<Integer> fsUserIdList = fsAddressBookManager.getFsUserIdByDepartmentIdList(ea, fsAddressBookScope.getDepartmentIdList(), false, MainDepartment.MAIN);
        if (CollectionUtils.isEmpty(fsUserIdList)) {
            log.info("insertUserTemplateRelationByFsAddressScope fsUserId is null, ea: {} templateId: {} scope: {}", ea, templateId, fsAddressBookScope);
            return;
        }
        List<UserAppMenuTemplateRelationEntity> userTemplateRelationEntityList = Lists.newArrayList();
        for (Integer fsUserId : fsUserIdList) {
            UserAppMenuTemplateRelationEntity userTemplateRelationEntity = new UserAppMenuTemplateRelationEntity();
            userTemplateRelationEntity.setId(UUIDUtil.getUUID());
            userTemplateRelationEntity.setEa(ea);
            userTemplateRelationEntity.setFsUserId(fsUserId);
            userTemplateRelationEntity.setUserType(UserTypeEnum.CRM.getCode());
            userTemplateRelationEntity.setMenuTemplateId(templateId);
            userTemplateRelationEntityList.add(userTemplateRelationEntity);
        }
        Lists.partition(userTemplateRelationEntityList, 100).forEach(e -> userAppMenuTemplateRelationDAO.batchInsert(e));

    }

    private void insertUserTemplateRelationByEnterpriseScope(String ea, AppMenuTemplateScopeBO.EnterpriseRelationScope enterpriseScope, String templateId) {
        if (enterpriseScope == null || (CollectionUtils.isEmpty(enterpriseScope.getOutTenantIdList()) && CollectionUtils.isEmpty(enterpriseScope.getOutTenantGroupIdList()))) {
            return;
        }
        Set<Long> outTenantIdSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(enterpriseScope.getOutTenantIdList())) {
            outTenantIdSet.addAll(enterpriseScope.getOutTenantIdList());
        }
        if (CollectionUtils.isNotEmpty(enterpriseScope.getOutTenantGroupIdList())) {
            List<OuterTenantIdGroupIdsData> tenantIdGroupIdsData = authPartnerManager.getTenantIdsByGroupIds(ea, enterpriseScope.getOutTenantGroupIdList());
            if (CollectionUtils.isNotEmpty(tenantIdGroupIdsData)) {
                tenantIdGroupIdsData.stream().map(OuterTenantIdGroupIdsData::getOutTenantId).forEach(e -> outTenantIdSet.add(Long.valueOf(e)));
            }
        }
        if (CollectionUtils.isEmpty(outTenantIdSet)) {
            return;
        }
        boolean isGetAllOuterUid = outTenantIdSet.contains((long) AuthManager.defaultAllDepartment);
        if (isGetAllOuterUid) {
            int totalCount = authPartnerManager.getPublicEmployeeCount(ea);
            if (totalCount <= 0) {
                return;
            }
            int currentCount = 0;
            int limit = 100;
            int offset = 0;
            while (currentCount < totalCount) {
                List<PartnerUserDTO> partnerUserDTOList = authPartnerManager.getPublicEmployee(ea, offset, limit);
                createPartnerUserToUserMenuRelation(ea, templateId, partnerUserDTOList);
                currentCount += partnerUserDTOList.size();
                offset += limit;
            }
            return;
        }
        List<PartnerUserDTO> partnerUserDTOList = authPartnerManager.getPartnerUserByTenantIds(ea, Lists.newArrayList(outTenantIdSet));
        createPartnerUserToUserMenuRelation(ea, templateId, partnerUserDTOList);
    }

    private void createPartnerUserToUserMenuRelation(String ea, String templateId, List<PartnerUserDTO> partnerUserDTOList) {
        if (CollectionUtils.isEmpty(partnerUserDTOList)) {
            return;
        }
        for (List<PartnerUserDTO> partition : Lists.partition(partnerUserDTOList, 100)) {
            Set<Long> outerTenantIdSet = Sets.newHashSet();
            List<Long> outerUserIdList = Lists.newArrayList();
            for (PartnerUserDTO partnerUser : partition) {
                outerTenantIdSet.add(partnerUser.getOuterTenantId());
                outerUserIdList.add(partnerUser.getOuterUid());
            }
            List<UserRelationEntity> userRelationEntityList = userRelationManager.getByOutTenantIdAndOuterUid(ea, Lists.newArrayList(outerTenantIdSet), outerUserIdList);
            Map<Long, Integer> outerUidToFsUserIdMap = userRelationEntityList.stream().filter(e -> e.getFsUserId() != null).collect(Collectors.toMap(UserRelationEntity::getOuterUid, UserRelationEntity::getFsUserId, (v1, v2) -> v1));
            List<UserAppMenuTemplateRelationEntity> userTemplateRelationEntityList = Lists.newArrayList();
            for (PartnerUserDTO partnerUser : partition) {
                UserAppMenuTemplateRelationEntity userTemplateRelationEntity = new UserAppMenuTemplateRelationEntity();
                Integer fsUserId = outerUidToFsUserIdMap.get(partnerUser.getOuterUid());
                if (fsUserId == null) {
                    log.warn("insertUserTemplateRelationByEnterpriseScope fsUserId is null, ea: {} templateId: {} partnerUser: {}", ea, templateId, partnerUser);
                    continue;
                }
                userTemplateRelationEntity.setId(UUIDUtil.getUUID());
                userTemplateRelationEntity.setMenuTemplateId(templateId);
                userTemplateRelationEntity.setUserType(UserTypeEnum.PARTNER.getCode());
                userTemplateRelationEntity.setFsUserId(fsUserId);
                userTemplateRelationEntity.setEa(ea);
                userTemplateRelationEntityList.add(userTemplateRelationEntity);
            }
            if (CollectionUtils.isNotEmpty(userTemplateRelationEntityList)) {
                userAppMenuTemplateRelationDAO.batchInsert(userTemplateRelationEntityList);
            }
        }
    }

    @Override
    public Result<Void> createOrUpdateMenu(UpdateAppMenuArg arg) {
        if (StringUtils.isBlank(arg.getName()) || StringUtils.isBlank(arg.getObjectAccessibleRule())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum = AppMenuAccessibleRuleEnum.getByType(arg.getObjectAccessibleRule());
        if (appMenuAccessibleRuleEnum == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.getByType(arg.getTargetMaterialType());
        if (materialTypeEnum == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String ea = arg.getEa();
        AppMenuTemplateEntity templateEntity = appMenuTemplateDAO.getById(ea, arg.getTemplateId());
        if (templateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String id = arg.getId();
        if (StringUtils.isBlank(id)) {
            List<AppMenuDetailEntity> sameNameEntityList = appMenuDetailDAO.getByTemplateIdAndName(ea, arg.getTemplateId(), arg.getName());
            if (CollectionUtils.isNotEmpty(sameNameEntityList)) {
                return Result.newError(SHErrorCode.HEADLINES_NAME_EXISTED);
            }
            AppMenuDetailEntity appMenuDetailEntity = new AppMenuDetailEntity();
            appMenuDetailEntity.setId(UUIDUtil.getUUID());
            appMenuDetailEntity.setEa(ea);
            appMenuDetailEntity.setMenuTemplateId(arg.getTemplateId());
            appMenuDetailEntity.setName(arg.getName());
            appMenuDetailEntity.setColor(arg.getColor());
            appMenuDetailEntity.setLineIcon(arg.getLineIcon());
            appMenuDetailEntity.setFlatIcon(arg.getFlatIcon());
            appMenuDetailEntity.setTargetMaterialType(arg.getTargetMaterialType());
            appMenuDetailEntity.setObjectAccessibleRule(arg.getObjectAccessibleRule());
            appMenuDetailEntity.setStatus(MenuStatusEnum.NORMAL.getStatus());
            appMenuDetailEntity.setObjectAccessibleTarget(getObjectAccessibleTarget(appMenuAccessibleRuleEnum, arg));
            Integer orderNum = appMenuDetailDAO.getMaxOrderNum(ea, arg.getTemplateId());
            orderNum = orderNum == null ? materialTypeEnum.getOrder() : ++orderNum;
            appMenuDetailEntity.setOrderNum(orderNum);
            appMenuDetailDAO.batchInsert(Lists.newArrayList(appMenuDetailEntity));
            return Result.newSuccess();
        }
        AppMenuDetailEntity appMenuDetailEntity = appMenuDetailDAO.getById(ea, id);
        if (appMenuDetailEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        AppMenuDetailEntity forUpdate = new AppMenuDetailEntity();
        if (StringUtils.isNotBlank(arg.getName()) && !arg.getName().equals(appMenuDetailEntity.getName())) {
            List<AppMenuDetailEntity> sameNameEntityList = appMenuDetailDAO.getByTemplateIdAndName(ea, arg.getTemplateId(), arg.getName());
            if (CollectionUtils.isNotEmpty(sameNameEntityList) && sameNameEntityList.stream().anyMatch(e -> e.getId().equals(id))) {
                return Result.newError(SHErrorCode.HEADLINES_NAME_EXISTED);
            }
            forUpdate.setName(arg.getName());
        }
        forUpdate.setEa(ea);
        forUpdate.setId(id);
        forUpdate.setColor(arg.getColor());
        forUpdate.setLineIcon(arg.getLineIcon());
        forUpdate.setFlatIcon(arg.getFlatIcon());
        forUpdate.setTargetMaterialType(arg.getTargetMaterialType());
        forUpdate.setObjectAccessibleRule(arg.getObjectAccessibleRule());
        forUpdate.setObjectAccessibleTarget(getObjectAccessibleTarget(appMenuAccessibleRuleEnum, arg));
        forUpdate.setStatus(MenuStatusEnum.NORMAL.getStatus());
        appMenuDetailDAO.update(forUpdate);
        AppMenuTemplateEntity templateForUpdate = new AppMenuTemplateEntity();
        templateForUpdate.setId(arg.getTemplateId());
        templateForUpdate.setEa(ea);
        templateForUpdate.setUpdateUserId(arg.getFsUserId());
        appMenuTemplateDAO.update(templateForUpdate);

        return Result.newSuccess();
    }

    private String getObjectAccessibleTarget(AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum, UpdateAppMenuArg arg) {
        switch (appMenuAccessibleRuleEnum) {
            case OBJECT_FILTER:
                return JsonUtil.toJson(arg.getFilters());
            case SINGLE_GROUP:
            case MULTI_GROUP:
                return JsonUtil.toJson(arg.getGroupIdList());
            case TAG:
                AppMenuTagBO appMenuTagBO = new AppMenuTagBO();
                appMenuTagBO.setTagIdList(arg.getTagIdList());
                appMenuTagBO.setTagOperator(arg.getTagOperator());
                return JsonUtil.toJson(appMenuTagBO);
            case SPECIFIC:
                if (Objects.equals(arg.getTargetMaterialType(), MaterialTypeEnum.KNOWLEDGE.getType())) {
                    return JsonUtil.toJson(Lists.newArrayList(arg.getKnowledgeScene()));
                }
                if (Objects.equals(arg.getTargetMaterialType(), MaterialTypeEnum.AI_HELPER.getType())) {
                    return JsonUtil.toJson(Lists.newArrayList(arg.getAgentList()));
                }
                return JsonUtil.toJson(arg.getObjectIdList());
            default:
                return null;
        }
    }

    @Override
    public Result<Void> updateStatus(UpdateTemplateStatusArg arg) {
        if (StringUtils.isBlank(arg.getId()) || StringUtils.isBlank(arg.getStatus())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AppMenuTemplateStatusEnum statusEnum = AppMenuTemplateStatusEnum.getByStatus(arg.getStatus());
        if (statusEnum == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        String id = arg.getId();
        AppMenuTemplateEntity appMenuTemplateEntity = appMenuTemplateDAO.getById(ea, id);
        if (appMenuTemplateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (!AppMenuTemplateTypeEnum.CUSTOMIZE.getType().equals(appMenuTemplateEntity.getType())) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_246));
        }
        log.info("update menu template status, oldStatus: {} arg: {}", appMenuTemplateEntity.getStatus(), arg);
        AppMenuTemplateEntity forUpdate = new AppMenuTemplateEntity();
        forUpdate.setEa(ea);
        forUpdate.setId(id);
        forUpdate.setStatus(arg.getStatus());
        forUpdate.setUpdateUserId(arg.getFsUserId());
        appMenuTemplateDAO.update(forUpdate);
        List<Integer> fsUserIdList = userAppMenuTemplateRelationDAO.getFsUserIdByTemplateId(ea, id);
        appMenuTemplateManager.deleteShowTemplateFromRedis(ea, fsUserIdList);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> delete(DeleteTemplateArg arg) {
        String ea = arg.getEa();
        String id = arg.getId();
        AppMenuTemplateEntity appMenuTemplateEntity = appMenuTemplateDAO.getById(ea, id);
        if (appMenuTemplateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        if (!AppMenuTemplateTypeEnum.CUSTOMIZE.getType().equals(appMenuTemplateEntity.getType())) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_246));
        }

        if (!AppMenuTemplateStatusEnum.DISABLE.getStatus().equals(appMenuTemplateEntity.getStatus())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        log.info("delete menu template, arg: {}", arg);
        appMenuTemplateDAO.delete(ea, id);
        appMenuDetailDAO.deleteByTemplateId(ea, id);
        // 删除模板，也要清空对应用户的缓存
        List<Integer> oldFsUserIdList = userAppMenuTemplateRelationDAO.getFsUserIdByTemplateId(ea, id);
        appMenuTemplateManager.deleteShowTemplateFromRedis(ea, oldFsUserIdList);
        userAppMenuTemplateRelationDAO.deleteByTemplateId(ea, id);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> copy(CopyTemplateArg arg) {
        if (StringUtils.isBlank(arg.getSourceId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        String sourceId = arg.getSourceId();
        AppMenuTemplateEntity sourceTemplateEntity = appMenuTemplateDAO.getById(ea, sourceId);
        if (sourceTemplateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String oldName = sourceTemplateEntity.getName();
        if (!AppMenuTemplateTypeEnum.CUSTOMIZE.getType().equals(sourceTemplateEntity.getType())) {
            String name = sourceTemplateEntity.getName();
            I18nKeyEnum i18nKeyEnum = I18nKeyEnum.getByI18nKey(name);
            if (i18nKeyEnum != null) {
                oldName = I18nUtil.get(i18nKeyEnum);
            }
        }
        String newName = oldName + "-" + I18nUtil.get(I18nKeyEnum.MARK_APP_MENU_TEMPLATE_COPY);
        AppMenuTemplateEntity forInsert = BeanUtil.copy(sourceTemplateEntity, AppMenuTemplateEntity.class);
        forInsert.setId(UUIDUtil.getUUID());
        forInsert.setName(newName);
        forInsert.setCreateUserId(arg.getFsUserId());
        forInsert.setUpdateUserId(arg.getFsUserId());
        forInsert.setType(AppMenuTemplateTypeEnum.CUSTOMIZE.getType());
        appMenuTemplateDAO.batchInsert(Lists.newArrayList(forInsert));

        List<AppMenuDetailEntity> sourceAppMenuDetailEntityList = appMenuDetailDAO.getOrderlyByTemplateId(ea, sourceId);
        if (CollectionUtils.isNotEmpty(sourceAppMenuDetailEntityList)) {
            List<AppMenuDetailEntity> forInsertMenuDetailList = BeanUtil.copy(sourceAppMenuDetailEntityList, AppMenuDetailEntity.class);
            forInsertMenuDetailList.forEach(e -> {
                e.setId(UUIDUtil.getUUID());
                e.setMenuTemplateId(forInsert.getId());
            });
            Lists.partition(forInsertMenuDetailList, 100).forEach(appMenuDetailDAO::batchInsert);
        }

        List<UserAppMenuTemplateRelationEntity> userTemplateRelationEntityList = userAppMenuTemplateRelationDAO.getByTemplateId(ea, sourceId);
        if (CollectionUtils.isNotEmpty(userTemplateRelationEntityList)) {
            List<UserAppMenuTemplateRelationEntity> forInsertUserTemplateRelationList = BeanUtil.copy(userTemplateRelationEntityList, UserAppMenuTemplateRelationEntity.class);
            forInsertUserTemplateRelationList.forEach(e -> {
                e.setId(UUIDUtil.getUUID());
                e.setMenuTemplateId(forInsert.getId());
            });
            Lists.partition(forInsertUserTemplateRelationList, 100).forEach(userAppMenuTemplateRelationDAO::batchInsert);
            List<Integer> oldUserIdList = userTemplateRelationEntityList.stream().map(UserAppMenuTemplateRelationEntity::getFsUserId).distinct().collect(Collectors.toList());
            appMenuTemplateManager.deleteShowTemplateFromRedis(ea, oldUserIdList);
        }

        return Result.newSuccess(forInsert.getId());
    }

    @Transactional
    public Result<Void> createSystemTemplate(String ea, String templateType) {
        List<AppMenuTemplateEntity> exsitSystemTemplateList = appMenuTemplateDAO.getByType(ea, templateType);
        if (CollectionUtils.isNotEmpty(exsitSystemTemplateList)) {
            log.info("createSystemTemplate, ea: {} has system template", ea);
            return Result.newSuccess();
        }

        AppMenuTemplateEntity templateEntity = initAppMenuTemplateEntity(ea, templateType);
        if (StringUtils.isBlank(templateEntity.getName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AppMenuTemplateScopeBO appMenuTemplateScopeBO = initAppMenuTemplateScopeBO(ea, templateType);
        templateEntity.setScope(JsonUtil.toJson(appMenuTemplateScopeBO));
        appMenuTemplateDAO.batchInsert(Lists.newArrayList(templateEntity));
        ThreadPoolUtils.executeWithNewThread(() -> insertUserTemplateRelation(ea, templateType, appMenuTemplateScopeBO, templateEntity.getId()));
        List<MaterialShowSettingEntity> exsitMaterialShowSettingList = materialShowSettingDAO.queryMaterialSettingListByEa(ea);
        if (CollectionUtils.isNotEmpty(exsitMaterialShowSettingList) && templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType())) {
            Map<Integer, MaterialShowSettingEntity> typeToMaterialShowSettingEntity = exsitMaterialShowSettingList.stream().collect(Collectors.toMap(MaterialShowSettingEntity::getType, e -> e));
            List<MaterialTypeEnum> materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(MaterialTypeEnum::isDefaultShowEmployeeMenuTemplate).sorted(Comparator.comparingInt(MaterialTypeEnum::getOrder)).collect(Collectors.toList());
            List<MaterialTypeEnum> createDefaultList = Lists.newArrayList();
            List<AppMenuDetailEntity> customizeInsertList = Lists.newArrayList();
            for (MaterialTypeEnum materialTypeEnum : materialTypeEnumList) {
                MaterialShowSettingEntity showSettingEntity = typeToMaterialShowSettingEntity.get(materialTypeEnum.getType());
                if (showSettingEntity != null) {
                    if (materialTypeEnum == MaterialTypeEnum.IMAGE) {
                        // 由于图片库插件可以设置子级分组，应用菜单只能用父级分组，所以刷库的时候 图片库都不处理 由产品跟客户沟通，手动添加
                        continue;
                    }
                    AppMenuDetailEntity appMenuDetailEntity = new AppMenuDetailEntity();
                    appMenuDetailEntity.setEa(ea);
                    appMenuDetailEntity.setId(UUIDUtil.getUUID());
                    appMenuDetailEntity.setMenuTemplateId(templateEntity.getId());
                    String showName = showSettingEntity.getShowName();
                    if (StringUtils.isBlank(showName) || materialTypeEnum.getName().equals(showName)) {
                        showName = null;
                    }
                    appMenuDetailEntity.setName(showName);
                    appMenuDetailEntity.setTargetMaterialType(materialTypeEnum.getType());
                    appMenuDetailEntity.setObjectAccessibleRule(getDefaultObjectAccessibleRuleByType(materialTypeEnum));
                    appMenuDetailEntity.setOrderNum(materialTypeEnum.getOrder());
                    appMenuDetailEntity.setStatus(BooleanUtils.isTrue(showSettingEntity.getShowStatus()) ? MenuStatusEnum.NORMAL.getStatus() : MenuStatusEnum.DELETED.getStatus());
                    customizeInsertList.add(appMenuDetailEntity);
                } else {
                    createDefaultList.add(materialTypeEnum);
                }
            }
            if (CollectionUtils.isNotEmpty(customizeInsertList)) {
                appMenuDetailDAO.batchInsert(customizeInsertList);
            }
            createTemplateMenuRelation(createDefaultList, ea, templateEntity.getId(), false);
        } else {
            // 没有设置过 生成菜单
            List<MaterialTypeEnum> materialTypeEnumList = null;
            if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType())) {
                materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(MaterialTypeEnum::isDefaultShowEmployeeMenuTemplate).sorted(Comparator.comparingInt(MaterialTypeEnum::getOrder)).collect(Collectors.toList());
            } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType())) {
                materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(MaterialTypeEnum::isDefaultShowPartnerMenuTemplate).sorted(Comparator.comparingInt(MaterialTypeEnum::getOrder)).collect(Collectors.toList());
            } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType())) {
                materialTypeEnumList = Arrays.stream(MaterialTypeEnum.values()).filter(MaterialTypeEnum::isDefaultShowMemberMenuTemplate).sorted(Comparator.comparingInt(MaterialTypeEnum::getOrder)).collect(Collectors.toList());
            }
            createTemplateMenuRelation(materialTypeEnumList, ea, templateEntity.getId(), true);
        }

        return Result.newSuccess();
    }

    private AppMenuTemplateEntity initAppMenuTemplateEntity(String ea, String templateType) {
        AppMenuTemplateEntity templateEntity = new AppMenuTemplateEntity();
        templateEntity.setEa(ea);
        templateEntity.setId(UUIDUtil.getUUID());
        if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType())) {
            templateEntity.setName(I18nKeyEnum.MARK_APP_MENU_TEMPLATE_SYSTEM.getI18nKey());
            templateEntity.setTitle(I18nKeyEnum.MARK_APP_MENU_TEMPLATE_TITLE.getI18nKey());
            templateEntity.setType(AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType());
            templateEntity.setUserType(UserRelationTypeEnum.EMPLOYEE.getCode());
        } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType())) {
            templateEntity.setName(I18nKeyEnum.MART_APP_MENU_PARTNER_TEMPLATE.getI18nKey());
            templateEntity.setTitle(I18nKeyEnum.MARK_APP_MENU_PARTNER_TEMPLATE_TITLE.getI18nKey());
            templateEntity.setType(AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType());
            templateEntity.setUserType(UserRelationTypeEnum.PARTNER.getCode());
        } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType())) {
            templateEntity.setName(I18nKeyEnum.MART_APP_MENU_MEMBER_TEMPLATE.getI18nKey());
            templateEntity.setTitle(I18nKeyEnum.MARK_APP_MENU_PARTNER_TEMPLATE_TITLE.getI18nKey());
            templateEntity.setType(AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType());
            templateEntity.setUserType(UserRelationTypeEnum.MEMBER.getCode());
        }
        templateEntity.setStatus(AppMenuTemplateStatusEnum.ENABLE.getStatus());
        templateEntity.setCreateUserId(SuperUserConstants.USER_ID);
        templateEntity.setUpdateUserId(SuperUserConstants.USER_ID);
        templateEntity.setIconType(1);
        templateEntity.setMiniAppVisitType(MiniAppVisitTypeEnum.VISIT_MAIN_PAGE.getCode());
        return templateEntity;
    }

    private AppMenuTemplateScopeBO initAppMenuTemplateScopeBO(String ea, String templateType) {
        AppMenuTemplateScopeBO appMenuTemplateScopeBO = new AppMenuTemplateScopeBO();
        if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType())) {
            // 系统模板- 适用范围 纷享通讯录——全部
            AppMenuTemplateScopeBO.FsAddressBookScope fsAddressBookScope = new AppMenuTemplateScopeBO.FsAddressBookScope();
            fsAddressBookScope.setDepartmentIdList(Lists.newArrayList(AuthManager.defaultAllDepartment));
            appMenuTemplateScopeBO.setFsAddressBookScope(fsAddressBookScope);
            // 系统模板- 适用范围 企微通讯录——全部
            AppMenuTemplateScopeBO.QywxAddressBookScope qywxAddressBookScope = new AppMenuTemplateScopeBO.QywxAddressBookScope();
            qywxAddressBookScope.setDepartmentIdList(Lists.newArrayList(AuthManager.defaultAllDepartment));
            appMenuTemplateScopeBO.setQywxAddressBookScope(qywxAddressBookScope);
            // 系统模板- 适用范围 会员——全部员工
            AppMenuTemplateScopeBO.MemberScope memberScope = new AppMenuTemplateScopeBO.MemberScope();
            Filter filter = new Filter(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), OperatorConstants.IN, Lists.newArrayList(MemberTypeEnum.EMPLOYEE.getType()));
            memberScope.setFilters(Lists.newArrayList(filter));
            appMenuTemplateScopeBO.setMemberScope(memberScope);
        } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType())) {
            AppMenuTemplateScopeBO.EnterpriseRelationScope enterpriseRelationScope = new AppMenuTemplateScopeBO.EnterpriseRelationScope();
            enterpriseRelationScope.setOutTenantIdList(Lists.newArrayList((long) AuthManager.defaultAllDepartment));
            appMenuTemplateScopeBO.setEnterpriseRelationScope(enterpriseRelationScope);
            // 系统模板- 适用范围 会员——全部伙伴
            AppMenuTemplateScopeBO.MemberScope memberScope = new AppMenuTemplateScopeBO.MemberScope();
            Filter filter = new Filter(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), OperatorConstants.IN, Lists.newArrayList(MemberTypeEnum.PARTNER.getType()));
            memberScope.setFilters(Lists.newArrayList(filter));
            appMenuTemplateScopeBO.setMemberScope(memberScope);
        } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType())) {
            // 系统模板- 适用范围 会员——全部推广会员
            AppMenuTemplateScopeBO.MemberScope memberScope = new AppMenuTemplateScopeBO.MemberScope();
            Filter filter = new Filter(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), OperatorConstants.IN, Lists.newArrayList(MemberTypeEnum.SPREAD_EMPLOYEE.getType()));
            memberScope.setFilters(Lists.newArrayList(filter));
            appMenuTemplateScopeBO.setMemberScope(memberScope);
        }
        return appMenuTemplateScopeBO;
    }

    private void insertUserTemplateRelation(String ea, String templateType, AppMenuTemplateScopeBO appMenuTemplateScopeBO, String templateId) {
        if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType())) {
            // 处理纷享通讯录
            insertUserTemplateRelationByFsAddressScope(ea, appMenuTemplateScopeBO.getFsAddressBookScope(), templateId);
            // 处理企微通讯录
            insertUserTemplateRelationByQywxAddressScope(ea, appMenuTemplateScopeBO.getQywxAddressBookScope(), templateId);
            // 处理会员的推广身份等于员工的
            insertUserTemplateRelationByMemberScope(ea, appMenuTemplateScopeBO.getMemberScope(), UserRelationTypeEnum.EMPLOYEE.getCode(), templateId);
        } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType())) {
            // 处理企业关系
            insertUserTemplateRelationByEnterpriseScope(ea, appMenuTemplateScopeBO.getEnterpriseRelationScope(), templateId);
            // 处理会员的推广身份等于伙伴的
            insertUserTemplateRelationByMemberScope(ea, appMenuTemplateScopeBO.getMemberScope(), UserRelationTypeEnum.PARTNER.getCode(), templateId);
        } else if (templateType.equals(AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType())) {
            // 处理会员的推广身份等于会员的
            insertUserTemplateRelationByMemberScope(ea, appMenuTemplateScopeBO.getMemberScope(), UserRelationTypeEnum.MEMBER.getCode(), templateId);
        }
    }

    private String getDefaultObjectAccessibleRuleByType(MaterialTypeEnum materialTypeEnum) {
        if (materialTypeEnum == MaterialTypeEnum.CARD || materialTypeEnum == MaterialTypeEnum.MICRO_STATION) {
            return AppMenuAccessibleRuleEnum.ALL.getType();
        }
        if (materialTypeEnum == MaterialTypeEnum.ACTIVITY || materialTypeEnum == MaterialTypeEnum.LIVE || materialTypeEnum == MaterialTypeEnum.CONFERENCE) {
            return AppMenuAccessibleRuleEnum.ALL.getType();
        }
        return AppMenuAccessibleRuleEnum.OBJECT_GROUP_ACCESSIBLE.getType();
    }

    public void handlePartnerUserTemplateRelationByMQ(ObjectData objectData, boolean add) {

    }

    @Override
    public Result<AppMenuDetailVO> menuDetail(MenuDetailArg arg) {
        String id = arg.getId();
        if (StringUtils.isBlank(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        AppMenuDetailEntity appMenuDetailEntity = appMenuDetailDAO.getById(ea, id);
        if (appMenuDetailEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        Integer targetMaterialType = appMenuDetailEntity.getTargetMaterialType();
        MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.getByType(targetMaterialType);
        if (materialTypeEnum == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String objectAccessibleRule = appMenuDetailEntity.getObjectAccessibleRule();
        AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum = AppMenuAccessibleRuleEnum.getByType(objectAccessibleRule);
        if (appMenuAccessibleRuleEnum == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        AppMenuDetailVO appMenuDetailVO = BeanUtil.copy(appMenuDetailEntity, AppMenuDetailVO.class);
        String objectAccessibleTarget = appMenuDetailEntity.getObjectAccessibleTarget();
        // 填充展示规则目标
        fillObjectAccessibleTarget(appMenuAccessibleRuleEnum, ea, objectAccessibleTarget, appMenuDetailVO, materialTypeEnum, appMenuDetailEntity);
        return Result.newSuccess(appMenuDetailVO);
    }

    private void fillObjectAccessibleTarget(AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum, String ea, String objectAccessibleTarget, AppMenuDetailVO appMenuDetailVO, MaterialTypeEnum materialTypeEnum, AppMenuDetailEntity appMenuDetailEntity) {
        // 这里将参数都平铺到每个字段，方便前端展示
        switch (appMenuAccessibleRuleEnum) {
            case SINGLE_GROUP:
            case MULTI_GROUP:
                List<ObjectGroupEntity> objectGroupEntityList = objectGroupManager.getByIdList(ea, JSONArray.parseArray(objectAccessibleTarget, String.class));
                List<AppMenuDetailVO.ObjectAccessibleTargetDetail> targetDetailList = Lists.newArrayList();
                for (ObjectGroupEntity objectGroupEntity : objectGroupEntityList) {
                    AppMenuDetailVO.ObjectAccessibleTargetDetail targetDetail = new AppMenuDetailVO.ObjectAccessibleTargetDetail();
                    targetDetail.setId(objectGroupEntity.getId());
                    targetDetail.setName(objectGroupEntity.getName());
                    targetDetailList.add(targetDetail);
                }
                appMenuDetailVO.setGroupList(targetDetailList);
                break;
            case TAG:
                AppMenuTagBO appMenuTagBO = JsonUtil.fromJson(objectAccessibleTarget, AppMenuTagBO.class);
                List<String> tagIdList = appMenuTagBO.getTagIdList();
                List<UserTagEntity> userTagEntityList = userTagDao.getByIdList(ea, tagIdList);
                Map<String, String> tagIdToParentIdMap = userTagEntityList.stream().collect(Collectors.toMap(UserTagEntity::getId, UserTagEntity::getParentTagId));
                List<String> parentTagIdList = userTagEntityList.stream().map(UserTagEntity::getParentTagId).filter(e -> !UserTagConstants.NO_PARENT_TAG_ID.equals(e)).collect(Collectors.toList());
                Map<String, UserTagEntity> parentIdToTagEntityMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(parentTagIdList)) {
                    List<UserTagEntity> parentUserTagEntityList = userTagDao.getByIdList(ea, parentTagIdList);
                    parentUserTagEntityList.forEach(e -> parentIdToTagEntityMap.put(e.getId(), e));
                }

                List<AppMenuDetailVO.ObjectAccessibleTargetDetail> tagDetailList = Lists.newArrayList();
                for (UserTagEntity userTagEntity : userTagEntityList) {
                    AppMenuDetailVO.ObjectAccessibleTargetDetail targetDetail = new AppMenuDetailVO.ObjectAccessibleTargetDetail();
                    String tagId = userTagEntity.getId();
                    targetDetail.setId(tagId);
                    String parentTagId = tagIdToParentIdMap.get(tagId);
                    UserTagEntity parentTag = parentIdToTagEntityMap.get(parentTagId);
                    if (parentTag != null) {
                        targetDetail.setFirstTagName(parentTag.getName());
                        targetDetail.setSecondTagName(userTagEntity.getName());
                    } else {
                        targetDetail.setFirstTagName(userTagEntity.getName());
                    }
                    tagDetailList.add(targetDetail);
                }
                appMenuDetailVO.setTagOperator(appMenuTagBO.getTagOperator());
                appMenuDetailVO.setTagList(tagDetailList);
                break;
            case SPECIFIC:
                if (materialTypeEnum == MaterialTypeEnum.KNOWLEDGE) {
                    appMenuDetailVO.setKnowledgeScene(JSONArray.parseArray(objectAccessibleTarget, String.class).get(0));
                } else if (materialTypeEnum == MaterialTypeEnum.AI_HELPER) {
                    appMenuDetailVO.setAgentList(JSONArray.parseArray(objectAccessibleTarget));
                } else {
                    Map<Integer, List<String>> objectTypeToIdListMap = Maps.newHashMap();
                    objectTypeToIdListMap.put(materialTypeEnum.getObjectType(), JSONArray.parseArray(objectAccessibleTarget, String.class));
                    Map<String, String> objectIdToNameMap = objectManager.getObjectName(ea, objectTypeToIdListMap);
                    List<AppMenuDetailVO.ObjectAccessibleTargetDetail> objectDetailList = Lists.newArrayList();
                    for (Map.Entry<String, String> entry : objectIdToNameMap.entrySet()) {
                        AppMenuDetailVO.ObjectAccessibleTargetDetail targetDetail = new AppMenuDetailVO.ObjectAccessibleTargetDetail();
                        targetDetail.setId(entry.getKey());
                        targetDetail.setName(entry.getValue());
                        objectDetailList.add(targetDetail);
                    }
                    appMenuDetailVO.setObjectList(objectDetailList);
                }
                break;
            case OBJECT_FILTER:
                List<Filter> filterList = JsonUtil.fromJson(objectAccessibleTarget, new TypeToken<List<Filter>>() {
                }.getType());
                appMenuDetailVO.setFilters(filterList);
                break;
            default:
                appMenuDetailVO.setObjectAccessibleTarget(appMenuDetailEntity.getObjectAccessibleTarget());
                break;
        }
    }

    @Override
    public Result<Void> deleteMenu(DeleteMenuArg arg) {
        String id = arg.getId();
        if (StringUtils.isBlank(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        AppMenuDetailEntity appMenuDetailEntity = appMenuDetailDAO.getById(ea, id);
        if (appMenuDetailEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        AppMenuDetailEntity forUpdate = new AppMenuDetailEntity();
        forUpdate.setEa(ea);
        forUpdate.setId(id);
        forUpdate.setStatus(MenuStatusEnum.DELETED.getStatus());
        appMenuDetailDAO.update(forUpdate);

        AppMenuTemplateEntity templateForUpdate = new AppMenuTemplateEntity();
        templateForUpdate.setId(appMenuDetailEntity.getMenuTemplateId());
        templateForUpdate.setEa(ea);
        templateForUpdate.setUpdateUserId(arg.getFsUserId());
        appMenuTemplateDAO.update(templateForUpdate);

        return Result.newSuccess();
    }

    @Override
    public Result<Void> sortMenu(SortMenuArg arg) {
        if (CollectionUtils.isEmpty(arg.getSortList()) || StringUtils.isBlank(arg.getTemplateId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        List<AppMenuDetailEntity> appMenuDetailEntityList = Lists.newArrayList();
        arg.getSortList().forEach(e -> {
            AppMenuDetailEntity forUpdate = new AppMenuDetailEntity();
            forUpdate.setEa(ea);
            forUpdate.setId(e.getId());
            forUpdate.setOrderNum(e.getOrderNum());
            appMenuDetailEntityList.add(forUpdate);
        });
        appMenuDetailDAO.batchUpdateOrderNum(ea, appMenuDetailEntityList);
        AppMenuTemplateEntity forUpdate = new AppMenuTemplateEntity();
        forUpdate.setId(arg.getTemplateId());
        forUpdate.setEa(ea);
        forUpdate.setUpdateUserId(arg.getFsUserId());
        appMenuTemplateDAO.update(forUpdate);
        return Result.newSuccess();
    }


    @Override
    public Result<AppMenuTemplateDetailVO> getShowAppMenuTemplate(GetShowAppMenuTemplateArg arg) {
        int fsUserId = arg.getFsUserId();
        String ea = arg.getEa();
        UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, fsUserId);
        if (userRelationEntity == null) {
            log.error("getShowAppMenuTemplate userRelationEntity is null, ea: {}, fsUserId: {}", ea, fsUserId);
            // 这里根据fsUserId的类型去获取对应的系统模板
            AppMenuTemplateDetailVO appMenuTemplateDetailVO = buildSystemEmployeeMenuDetail(ea, fsUserId);
            if (appMenuTemplateDetailVO == null) {
                log.error("getShowAppMenuTemplate refresh system template failed, ea: {}, fsUserId: {}", ea, fsUserId);
                return Result.newSuccess();
            }
            return Result.newSuccess(appMenuTemplateDetailVO);
        }
        String userRelationType = userRelationEntity.getType();
        AppMenuTemplateEntity cacheUserTemplate = getShowTemplateFromRedis(ea, userRelationType, fsUserId);
        if (cacheUserTemplate != null) {
            AppMenuTemplateDetailVO appMenuTemplateDetailVO = buildAppMenuShowDetailVO(userRelationEntity.getEa(), fsUserId, cacheUserTemplate);
            return Result.newSuccess(appMenuTemplateDetailVO);
        }
        AppMenuTemplateDetailVO appMenuTemplateDetailVO = getAppMenuTemplateFromDb(ea, userRelationEntity);
        // 写入redis
        setShowTemplateToRedis(ea, fsUserId, userRelationType, appMenuTemplateDetailVO.getId());
        return Result.newSuccess(appMenuTemplateDetailVO);
    }

    private AppMenuTemplateDetailVO getAppMenuTemplateFromDb(String ea, UserRelationEntity userRelationEntity) {
        int fsUserId = userRelationEntity.getFsUserId();
        String templateUserType = userRelationEntity.getType();
        List<UserAppMenuTemplateRelationEntity> userTemplateRelationList = userAppMenuTemplateRelationDAO.getByFsUserId(ea, templateUserType, fsUserId);
        // 如果没找到任何模板，返回系统模板
        if (CollectionUtils.isEmpty(userTemplateRelationList)) {
            return buildSystemEmployeeMenuDetail(ea, userRelationEntity);
        }
        // 只命中一个模板
        if (userTemplateRelationList.size() == 1) {
            AppMenuTemplateEntity appMenuTemplateEntity = appMenuTemplateDAO.getById(ea, userTemplateRelationList.get(0).getMenuTemplateId());
            // 还在启用，直接返回
            if (appMenuTemplateEntity != null && AppMenuTemplateStatusEnum.ENABLE.getStatus().equals(appMenuTemplateEntity.getStatus())) {
                return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), appMenuTemplateEntity);
            }
            // 删除或者禁用了，返回系统模板
            return buildSystemEmployeeMenuDetail(ea, userRelationEntity);
        }
        // 下面开始是命中多个模板的逻辑
        List<String> templateIdList = userTemplateRelationList.stream().map(UserAppMenuTemplateRelationEntity::getMenuTemplateId).distinct().collect(Collectors.toList());
        List<AppMenuTemplateEntity> appMenuTemplateEntityList = appMenuTemplateDAO.getByIdList(ea, templateIdList);
        // 过滤禁用的模板
        appMenuTemplateEntityList = appMenuTemplateEntityList.stream().filter(e -> AppMenuTemplateStatusEnum.ENABLE.getStatus().equals(e.getStatus())).collect(Collectors.toList());
        // 没有启用的模板 返回系统模板
        if (CollectionUtils.isEmpty(appMenuTemplateEntityList)) {
            return buildSystemEmployeeMenuDetail(ea, userRelationEntity);
        }
        // 过滤状态后只剩一个模板，直接返回
        if (appMenuTemplateEntityList.size() == 1) {
            return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), appMenuTemplateEntityList.get(0));
        }

        Map<String, List<AppMenuTemplateEntity>> templeteTypeToEntityMap = appMenuTemplateEntityList.stream().collect(Collectors.groupingBy(AppMenuTemplateEntity::getType));
        // 不包含自定义模板，只有系统模板，直接返回系统模板
        if (!templeteTypeToEntityMap.containsKey(AppMenuTemplateTypeEnum.CUSTOMIZE.getType())) {
            List<AppMenuTemplateEntity> templateEntityList = templeteTypeToEntityMap.get(AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType());
            return CollectionUtils.isEmpty(templateEntityList) ? buildSystemEmployeeMenuDetail(ea, userRelationEntity) : buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), templateEntityList.get(0));
        }
        // 包含自定义模板，就匹配自定义模板
        appMenuTemplateEntityList = templeteTypeToEntityMap.get(AppMenuTemplateTypeEnum.CUSTOMIZE.getType());
        if (appMenuTemplateEntityList.size() == 1) {
            return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), appMenuTemplateEntityList.get(0));
        }
        if (UserRelationTypeEnum.PARTNER.getCode().equals(templateUserType)) {
            return getPartnerAppMenuTemplateDetail(ea, userRelationEntity, appMenuTemplateEntityList);
        } else if (UserRelationTypeEnum.MEMBER.getCode().equals(templateUserType)) {
            // 推广会员的模板
            appMenuTemplateEntityList.sort(Comparator.comparing(BaseEntity::getCreateTime).reversed());
            return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), appMenuTemplateEntityList.get(0));
        }
        // 下面是员工的模板
        // fs部门id对应的模板
        Map<Integer, List<AppMenuTemplateEntity>> fsDepertmentIdToTemplateListMap = Maps.newHashMap();
        Map<Integer, List<AppMenuTemplateEntity>> qywxDepertmentIdToTemplateListMap = Maps.newHashMap();
        for (AppMenuTemplateEntity appMenuTemplateEntity : appMenuTemplateEntityList) {
            String scope = appMenuTemplateEntity.getScope();
            AppMenuTemplateScopeBO appMenuTemplateScopeBO = JsonUtil.fromJson(scope, AppMenuTemplateScopeBO.class);
            AppMenuTemplateScopeBO.FsAddressBookScope fsAddressBookScope = appMenuTemplateScopeBO.getFsAddressBookScope();
            if (fsAddressBookScope != null && CollectionUtils.isNotEmpty(fsAddressBookScope.getDepartmentIdList())) {
                List<Integer> fsDepartmentIdList = fsAddressBookScope.getDepartmentIdList();
                for (Integer fsDepartmentId : fsDepartmentIdList) {
                    List<AppMenuTemplateEntity> templateEntityList = fsDepertmentIdToTemplateListMap.computeIfAbsent(fsDepartmentId, k -> Lists.newArrayList());
                    templateEntityList.add(appMenuTemplateEntity);
                }
            }
            AppMenuTemplateScopeBO.QywxAddressBookScope qywxAddressBookScope = appMenuTemplateScopeBO.getQywxAddressBookScope();
            if (qywxAddressBookScope != null && CollectionUtils.isNotEmpty(qywxAddressBookScope.getDepartmentIdList())) {
                List<Integer> qywxDepartmentIdList = qywxAddressBookScope.getDepartmentIdList();
                for (Integer qywxDepartmentId : qywxDepartmentIdList) {
                    List<AppMenuTemplateEntity> templateEntityList = qywxDepertmentIdToTemplateListMap.computeIfAbsent(qywxDepartmentId, k -> Lists.newArrayList());
                    templateEntityList.add(appMenuTemplateEntity);
                }
            }
        }
        // 1. 优先匹配纷享通讯录
        if (MapUtils.isNotEmpty(fsDepertmentIdToTemplateListMap) && QywxUserConstants.isFsUserId(fsUserId)) {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            // 获取纷享员工的部门，有序的，最前面是子部门，后面是父部门
            List<Integer> fsDepartmentList = authManager.getUserMainDepartmentsOrderly(ei, fsUserId);
            List<AppMenuTemplateEntity> hitAppMenuTemplateList = null;
            // 找到该员工最近部门的模板
            for (Integer departmentId : fsDepartmentList) {
                hitAppMenuTemplateList = fsDepertmentIdToTemplateListMap.get(departmentId);
                if (CollectionUtils.isNotEmpty(hitAppMenuTemplateList)) {
                    break;
                }
            }
            // 找到纷享通讯录的模板，找到最新创建的一个模板
            if (CollectionUtils.isNotEmpty(hitAppMenuTemplateList)) {
                hitAppMenuTemplateList.sort(Comparator.comparing(BaseEntity::getCreateTime).reversed());
                return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), hitAppMenuTemplateList.get(0));
            }
        }
        // 2. 企微通讯录
        String qywxUserId = userRelationEntity.getQywxUserId();
        if (StringUtils.isNotBlank(qywxUserId) && MapUtils.isNotEmpty(qywxDepertmentIdToTemplateListMap)) {
            List<Integer> qywxDepartmentIdList = qywxUserManager.getUserMainDepartmentsOrderly(ea, qywxUserId);
            List<AppMenuTemplateEntity> hitAppMenuTemplateList = null;
            for (Integer qywxDepartmentId : qywxDepartmentIdList) {
                qywxDepartmentId += QywxUserConstants.BASE_QYWX_DEPARTMENT_ID;
                hitAppMenuTemplateList = qywxDepertmentIdToTemplateListMap.get(qywxDepartmentId);
                if (CollectionUtils.isNotEmpty(hitAppMenuTemplateList)) {
                    break;
                }
            }
            // 找到企微通讯录的模板，找到最新创建的一个模板
            if (CollectionUtils.isNotEmpty(hitAppMenuTemplateList)) {
                hitAppMenuTemplateList.sort(Comparator.comparing(BaseEntity::getCreateTime).reversed());
                return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), hitAppMenuTemplateList.get(0));
            }
        }
        // 3. 会员, 不管会员有没有关联crm和企微员工,前面纷享通讯录和企微通讯录匹配不到，这里只会返回最新的模板就好了，因为会员通讯录的规则就是去最新的一个
        appMenuTemplateEntityList.sort(Comparator.comparing(BaseEntity::getCreateTime).reversed());
        return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), appMenuTemplateEntityList.get(0));
    }

    private AppMenuTemplateDetailVO getPartnerAppMenuTemplateDetail(String ea, UserRelationEntity userRelationEntity, List<AppMenuTemplateEntity> appMenuTemplateEntityList) {
        // 获取互联用户所在的分组
        Set<String> tenantGroupIdSet = appMenuTemplateManager.getOutTenantGroupIdSet(ea, userRelationEntity.getOuterTenantId());
        List<AppMenuTemplateEntity> hitAppMenuTemplateList = Lists.newArrayList();
        for (AppMenuTemplateEntity appMenuTemplateEntity : appMenuTemplateEntityList) {
            String scope = appMenuTemplateEntity.getScope();
            AppMenuTemplateScopeBO appMenuTemplateScopeBO = JsonUtil.fromJson(scope, AppMenuTemplateScopeBO.class);
            AppMenuTemplateScopeBO.EnterpriseRelationScope enterpriseRelationScope = appMenuTemplateScopeBO.getEnterpriseRelationScope();
            //如果有配置互联企微范围
            if (enterpriseRelationScope != null) {
                boolean isHit = false;
                // 配置的企业组包含该互联企业
                if (CollectionUtils.isNotEmpty(enterpriseRelationScope.getOutTenantGroupIdList()) && enterpriseRelationScope.getOutTenantGroupIdList().stream().anyMatch(tenantGroupIdSet::contains)) {
                    isHit = true;
                }
                // 配置的互联企业包含该互联企业
                if (CollectionUtils.isNotEmpty(enterpriseRelationScope.getOutTenantIdList()) && enterpriseRelationScope.getOutTenantIdList().contains(userRelationEntity.getOuterTenantId())) {
                    isHit = true;
                }
                if (isHit) {
                    hitAppMenuTemplateList.add(appMenuTemplateEntity);
                }
            }
        }
        // 如果有成功匹配互联企业，直接返回
        if (CollectionUtils.isNotEmpty(hitAppMenuTemplateList)) {
            hitAppMenuTemplateList.sort(Comparator.comparing(BaseEntity::getCreateTime).reversed());
            return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), hitAppMenuTemplateList.get(0));
        }
        // 如果没有匹配到互联企业，意味着是会员伙伴，直接能返回最新的一个
        appMenuTemplateEntityList.sort(Comparator.comparing(BaseEntity::getCreateTime).reversed());
        return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), appMenuTemplateEntityList.get(0));
    }


    private AppMenuTemplateEntity getShowTemplateFromRedis(String ea, String templateUserType, int fsUserId) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        String redisKey = appMenuTemplateManager.getRedisKey(ei, templateUserType, fsUserId);
        String cacheTemplateId = redisManager.get(redisKey);
        log.info("getFromRedis, ea: {}, fsUserId: {}, cacheTemplateId: {}", ea, fsUserId, cacheTemplateId);
        if (StringUtils.isBlank(cacheTemplateId)) {
            return null;
        }
        AppMenuTemplateEntity appMenuTemplateEntity = appMenuTemplateDAO.getById(ea, cacheTemplateId);
        if (appMenuTemplateEntity != null && AppMenuTemplateStatusEnum.ENABLE.getStatus().equals(appMenuTemplateEntity.getStatus())) {
            return appMenuTemplateEntity;
        }
        return null;
    }

    private void setShowTemplateToRedis(String ea, int fsUserId, String userRelationType, String templateId) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        String redisKey = appMenuTemplateManager.getRedisKey(ei, userRelationType, fsUserId);
        redisManager.set(redisKey, templateId, 60 * 60 * 24);
    }

    private AppMenuTemplateDetailVO buildSystemEmployeeMenuDetail(String ea, Integer fsUserId) {
        String type;
        if (QywxUserConstants.isMemberVirtualUserId(fsUserId)) {
            type = AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType();
        } else if (QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
            type = AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType();
        } else {
            type = AppMenuTemplateTypeEnum.SYSTEM_EMPLOYEE.getType();
        }
        List<AppMenuTemplateEntity> appMenuTemplateEntityList = appMenuTemplateDAO.getByType(ea, type);
        if (CollectionUtils.isEmpty(appMenuTemplateEntityList)) {
            // 如果对应的系统模板都没有，补刷一下
            createSystemTemplate(ea, type);
            appMenuTemplateEntityList = appMenuTemplateDAO.getByType(ea, type);
        }
        if (CollectionUtils.isEmpty(appMenuTemplateEntityList)) {
            return null;
        }
        return buildAppMenuShowDetailVO(ea, fsUserId, appMenuTemplateEntityList.get(0));
    }

    private AppMenuTemplateDetailVO buildSystemEmployeeMenuDetail(String ea, UserRelationEntity userRelationEntity) {
        AppMenuTemplateTypeEnum appMenuTemplateTypeEnum = AppMenuTemplateTypeEnum.getByUserRelationType(userRelationEntity.getType());
        List<AppMenuTemplateEntity> appMenuTemplateEntityList = appMenuTemplateDAO.getByType(ea, appMenuTemplateTypeEnum.getType());
        return buildAppMenuShowDetailVO(userRelationEntity.getEa(), userRelationEntity.getFsUserId(), appMenuTemplateEntityList.get(0));
    }

    private AppMenuTemplateDetailVO buildAppMenuShowDetailVO(String ea, Integer fsUserId, AppMenuTemplateEntity appMenuTemplateEntity) {
        AppMenuTemplateDetailVO appMenuTemplateDetailVO = new AppMenuTemplateDetailVO();
        appMenuTemplateDetailVO.setTitle(getTitle(appMenuTemplateEntity.getTitle()));
        appMenuTemplateDetailVO.setId(appMenuTemplateEntity.getId());
        appMenuTemplateDetailVO.setIconType(appMenuTemplateEntity.getIconType());
        appMenuTemplateDetailVO.setUserType(appMenuTemplateEntity.getUserType());
        appMenuTemplateDetailVO.setMiniappVisitType(appMenuTemplateEntity.getMiniAppVisitType());
        List<AppMenuDetailVO> menuList = Lists.newArrayList();
        appMenuTemplateDetailVO.setMenuList(menuList);
        List<AppMenuDetailEntity> appMenuDetailEntityList = appMenuDetailDAO.getOrderlyByTemplateId(appMenuTemplateEntity.getEa(), appMenuTemplateEntity.getId());
        if (CollectionUtils.isEmpty(appMenuDetailEntityList)) {
            return appMenuTemplateDetailVO;
        }
        boolean isAppAdmin = QywxUserConstants.isFsUserId(fsUserId) && objectGroupManager.isAppAdmin(ea, fsUserId);
        // 获取知识库对应的url
        Map<String, String> knowledgeToUrlMap = getKnowledgeUrl(ea, appMenuDetailEntityList);
        for (AppMenuDetailEntity appMenuDetailEntity : appMenuDetailEntityList) {
            if (MenuStatusEnum.DELETED.getStatus().equals(appMenuDetailEntity.getStatus())) {
                continue;
            }
            //只有营销通管理员才能看到企业简报
            if (!isAppAdmin && MaterialTypeEnum.ENTERPRISE_REPORT.getType().equals(appMenuDetailEntity.getTargetMaterialType())) {
                continue;
            }
            AppMenuDetailVO appMenuDetailVO = new AppMenuDetailVO();
            appMenuDetailVO.setId(appMenuDetailEntity.getId());
            appMenuDetailVO.setName(appMenuDetailEntity.getName());
            appMenuDetailVO.setColor(appMenuDetailEntity.getColor());
            appMenuDetailVO.setLineIcon(appMenuDetailEntity.getLineIcon());
            appMenuDetailVO.setFlatIcon(appMenuDetailEntity.getFlatIcon());
            appMenuDetailVO.setTargetMaterialType(appMenuDetailEntity.getTargetMaterialType());
            if (Objects.equals(appMenuDetailEntity.getTargetMaterialType(), MaterialTypeEnum.KNOWLEDGE.getType()) && StringUtils.isNotBlank(appMenuDetailEntity.getObjectAccessibleTarget())) {
                List<String> scencList = JSONObject.parseArray(appMenuDetailEntity.getObjectAccessibleTarget(), String.class);
                appMenuDetailVO.setUrl(knowledgeToUrlMap.get(scencList.get(0)));
            }
            if (Objects.equals(appMenuDetailEntity.getTargetMaterialType(), MaterialTypeEnum.AI_HELPER.getType()) && StringUtils.isNotBlank(appMenuDetailEntity.getObjectAccessibleTarget())) {
                appMenuDetailVO.setAgentList(JSONObject.parseArray(appMenuDetailEntity.getObjectAccessibleTarget()));
            }
            menuList.add(appMenuDetailVO);
        }
        return appMenuTemplateDetailVO;
    }

    private Map<String, String> getKnowledgeUrl(String ea, List<AppMenuDetailEntity> appMenuDetailEntityList) {
        try {
            List<String> knowledgeSceneList = appMenuDetailEntityList.stream().filter(e -> MenuStatusEnum.NORMAL.getStatus().equals(e.getStatus()) && Objects.equals(e.getTargetMaterialType(), MaterialTypeEnum.KNOWLEDGE.getType())).map(e -> JSONObject.parseArray(e.getObjectAccessibleTarget(), String.class)).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(knowledgeSceneList)) {
                return Maps.newHashMap();
            }
            int ei = eieaConverter.enterpriseAccountToId(ea);
            HeaderObj headerObj = HeaderObj.newInstance(ei);
            QueryKnowledgeUrlModel.Arg arg;
//            if (UserRelationTypeEnum.EMPLOYEE.getCode().equals(userRelationEntity.getType()) && QywxUserConstants.isFsUserId(userRelationEntity.getFsUserId())) {
//                arg = QueryKnowledgeUrlModel.Arg.builder().fsEa(ea).fsUserId(Long.valueOf(userRelationEntity.getFsUserId()))
//                        .identityType(QueryKnowledgeUrlModel.IdentityType.FS.getType()).build();
//            } else if (userRelationEntity.getOuterTenantId() != null && userRelationEntity.getOuterUid() != null) {
//                headerObj = HeaderObj.newInstance(ei, userRelationEntity.getOuterTenantId(), userRelationEntity.getOuterUid());
//                arg = QueryKnowledgeUrlModel.Arg.builder().fsEa(ea)
//                        .linkAppId(partnerAppId)
//                        .identityType(QueryKnowledgeUrlModel.IdentityType.ER.getType()).build();
//            } else {
//                arg = QueryKnowledgeUrlModel.Arg.builder().fsEa(ea)
//                        .identityType(QueryKnowledgeUrlModel.IdentityType.GUEST.getType()).build();
//            }
            arg = QueryKnowledgeUrlModel.Arg.builder().fsEa(ea)
                    .identityType(QueryKnowledgeUrlModel.IdentityType.GUEST.getType()).build();
            arg.setScenes(knowledgeSceneList);
            Arg1<QueryKnowledgeUrlModel.Arg> getKnowledgeUrlArg = new Arg1<>();
            getKnowledgeUrlArg.setArg1(arg);
            EserviceResult<List<QueryKnowledgeUrlModel.Result>> searchResult = knowledgeService.getKnowledgeUrl(headerObj, getKnowledgeUrlArg);
            log.info("getKnowledgeUrl arg: {} result: {}", arg, searchResult);
            if (searchResult == null || !searchResult.isSuccess() || CollectionUtils.isEmpty(searchResult.getData())) {
                return Maps.newHashMap();
            }
            return searchResult.getData().stream().filter(e -> StringUtils.isNotBlank(e.getUrl())).collect(Collectors.toMap(QueryKnowledgeUrlModel.Result::getScene, QueryKnowledgeUrlModel.Result::getUrl, (v1, v2) -> v1));
        } catch (Exception e) {
            log.error("app menu template, getKnowledgeUrl error, ea: {}", ea, e);
        }
        return Maps.newHashMap();
    }

    private String getTitle(String title) {
        I18nKeyEnum i18nKeyEnum = I18nKeyEnum.getByI18nKey(title);
        return i18nKeyEnum == null ? title : I18nUtil.get(i18nKeyEnum);
    }

    @Override
    public Result<List<String>> getSpecificObjectIdList(String ea, String menuId, Integer objectType) {
        if (StringUtils.isBlank(menuId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        AppMenuDetailEntity appMenuDetailEntity = appMenuDetailDAO.getById(ea, menuId);
        if (appMenuDetailEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        String objectAccessibleRule = appMenuDetailEntity.getObjectAccessibleRule();
        AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum = AppMenuAccessibleRuleEnum.getByType(objectAccessibleRule);
        if (appMenuAccessibleRuleEnum != AppMenuAccessibleRuleEnum.SPECIFIC) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.getByType(appMenuDetailEntity.getTargetMaterialType());
        // 如果传进来的物料类型和菜单跳转的物料不一样，直接返回
        if (materialTypeEnum == null || materialTypeEnum.getObjectType() != objectType) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String objectAccessibleTarget = appMenuDetailEntity.getObjectAccessibleTarget();
        List<String> objectIdList = JSONArray.parseArray(objectAccessibleTarget, String.class);
        return Result.newSuccess(objectIdList);
    }

    @Override
    public Result<AppMenuTagVO> getMenuTagRule(String ea, String menuId, Integer objectType) {

        if (StringUtils.isBlank(menuId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        AppMenuDetailEntity appMenuDetailEntity = appMenuDetailDAO.getById(ea, menuId);
        if (appMenuDetailEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        String objectAccessibleRule = appMenuDetailEntity.getObjectAccessibleRule();
        AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum = AppMenuAccessibleRuleEnum.getByType(objectAccessibleRule);
        if (appMenuAccessibleRuleEnum != AppMenuAccessibleRuleEnum.TAG) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.getByType(appMenuDetailEntity.getTargetMaterialType());
        // 如果传进来的物料类型和菜单跳转的物料不一样，直接返回
        if (materialTypeEnum == null || materialTypeEnum.getObjectType() != objectType) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String objectAccessibleTarget = appMenuDetailEntity.getObjectAccessibleTarget();
        AppMenuTagBO appMenuTagBO = JsonUtil.fromJson(objectAccessibleTarget, AppMenuTagBO.class);
        AppMenuTagVO appMenuTagVO = BeanUtil.copy(appMenuTagBO, AppMenuTagVO.class);
        return Result.newSuccess(appMenuTagVO);

    }

    @Override
    public Result<List<String>> getMenuObjectGroupRule(String ea, int fsUserId, String menuId, Integer objectType) {
        if (StringUtils.isBlank(menuId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AppMenuDetailEntity appMenuDetailEntity = appMenuDetailDAO.getById(ea, menuId);
        if (appMenuDetailEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String objectAccessibleRule = appMenuDetailEntity.getObjectAccessibleRule();

        if (!AppMenuAccessibleRuleEnum.isObjectGroupAccessibleRule(objectAccessibleRule)) {
            return Result.newError(SHErrorCode.MENU_IS_NOT_OBJECT_GROUP_RULE);
        }
        MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.getByType(appMenuDetailEntity.getTargetMaterialType());
        // 如果传进来的物料类型和菜单跳转的物料不一样，直接返回
        if (materialTypeEnum == null || materialTypeEnum.getObjectType() != objectType) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String objectAccessibleTarget = appMenuDetailEntity.getObjectAccessibleTarget();
        AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum = AppMenuAccessibleRuleEnum.getByType(objectAccessibleRule);
        if (appMenuAccessibleRuleEnum == AppMenuAccessibleRuleEnum.SINGLE_GROUP || appMenuAccessibleRuleEnum == AppMenuAccessibleRuleEnum.MULTI_GROUP) {
            // 菜单模板设置——单个分组、多个分组  这里也要返回分组的子级分组
            List<String> objectGroupIdList = JSONArray.parseArray(objectAccessibleTarget, String.class);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupManager.getAllSubGroupWithoutPermission(ea, objectGroupIdList, objectType);
            objectGroupEntityList.stream().map(ObjectGroupEntity::getId).forEach(objectGroupIdList::add);
            return Result.newSuccess(objectGroupIdList);
        } else if (appMenuAccessibleRuleEnum == AppMenuAccessibleRuleEnum.ALL) {
            // 菜单模板设置——全部
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, objectType);
            List<String> objectGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            return Result.newSuccess(objectGroupIdList);
        }
        if (QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
            UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, fsUserId);
            List<String> groupIds = objectGroupRelationVisibleManager.getOuterAccessibleGroupIds(ea, String.valueOf(userRelationEntity.getOuterTenantId()), String.valueOf(userRelationEntity.getOuterUid()), objectType);
            return Result.newSuccess(groupIds);
        }
        // 适应分组权限
        List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, objectType);
        return Result.newSuccess(objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList()));
    }

    @Override
    public Result<PaasObjectRuleVO> getPaasObjectRule(String ea, String menuId, Integer objectType) {
        if (StringUtils.isBlank(menuId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AppMenuDetailEntity appMenuDetailEntity = appMenuDetailDAO.getById(ea, menuId);
        if (appMenuDetailEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.getByType(appMenuDetailEntity.getTargetMaterialType());
        // 如果传进来的物料类型和菜单跳转的物料不一样，直接返回
        if (materialTypeEnum == null || materialTypeEnum.getObjectType() != objectType) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String objectAccessibleRule = appMenuDetailEntity.getObjectAccessibleRule();

        PaasObjectRuleVO paasObjectRuleVO = new PaasObjectRuleVO();
        paasObjectRuleVO.setObjectAccessibleRule(objectAccessibleRule);

        AppMenuAccessibleRuleEnum appMenuAccessibleRuleEnum = AppMenuAccessibleRuleEnum.getByType(objectAccessibleRule);
        String objectAccessibleTarget = appMenuDetailEntity.getObjectAccessibleTarget();

        if (appMenuAccessibleRuleEnum == AppMenuAccessibleRuleEnum.TAG) {
            AppMenuTagBO appMenuTagBO = JsonUtil.fromJson(objectAccessibleTarget, AppMenuTagBO.class);
            AppMenuTagVO appMenuTagVO = BeanUtil.copy(appMenuTagBO, AppMenuTagVO.class);
            paasObjectRuleVO.setAppMenuTagVO(appMenuTagVO);
        } else if (appMenuAccessibleRuleEnum == AppMenuAccessibleRuleEnum.OBJECT_FILTER) {
            List<Filter> filterList = JsonUtil.fromJson(objectAccessibleTarget, new TypeToken<List<Filter>>() {
            }.getType());
            paasObjectRuleVO.setFilters(filterList);
        }
        return Result.newSuccess(paasObjectRuleVO);

    }

    @Override
    public Result<Void> handleFsEmployeeChangeEvent(String ea, int fsUserId) {
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getFsEmployeeInfoByUserIds(ea, Lists.newArrayList(fsUserId), true);
        FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(fsUserId);
        if (fsEmployeeMsg == null) {
            int count = userAppMenuTemplateRelationDAO.deleteByFsUserId(ea, fsUserId);
            log.info("handleEmployeeChangeEvent can not find employee, delete data, ea: {} fsUserId: {} count: {}", ea, fsUserId, count);
            return Result.newSuccess();
        }

        EmployeeEntityStatus employeeEntityStatus = fsEmployeeMsg.getStatus();
        if (employeeEntityStatus == EmployeeEntityStatus.STOP || employeeEntityStatus == EmployeeEntityStatus.DELETE) {
            int count = userAppMenuTemplateRelationDAO.deleteByFsUserId(ea, fsUserId);
            log.info("handleEmployeeChangeEvent employee is not normal, delete data, ea: {} fsUserId: {} count: {}", ea, fsUserId, count);
            return Result.newSuccess();
        }
        Integer mainDepartmentId = fsEmployeeMsg.getMainDepartmentId();
        if (mainDepartmentId == null) {
            int count = userAppMenuTemplateRelationDAO.deleteByFsUserId(ea, fsUserId);
            log.info("handleEmployeeChangeEvent employee do not have main department, delete data, ea: {} fsUserId: {} count: {}", ea, fsUserId, count);
            return Result.newSuccess();
        }
        int pageSize = 100;
        List<AppMenuTemplateEntity> appMenuTemplateEntityList = appMenuTemplateDAO.scanById(ea, UserRelationTypeEnum.EMPLOYEE.getCode(), null, pageSize);
        List<String> existUserTemplateIdList = userAppMenuTemplateRelationDAO.getAllMenuTemplateIdByUserId(ea, fsUserId);
        Set<String> existUserTemplateIdSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(existUserTemplateIdList)) {
            existUserTemplateIdSet.addAll(existUserTemplateIdList);
        }
        String lastId;
        while (CollectionUtils.isNotEmpty(appMenuTemplateEntityList)) {
            List<UserAppMenuTemplateRelationEntity> insertList = Lists.newArrayList();
            for (AppMenuTemplateEntity appMenuTemplateEntity : appMenuTemplateEntityList) {
                AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
                AppMenuTemplateScopeBO.FsAddressBookScope fsAddressBookScope = scope.getFsAddressBookScope();
                String templateId = appMenuTemplateEntity.getId();
                if (fsAddressBookScope == null) {
                    // 可见范围没有设置纷享通讯录
                    continue;
                }
                List<Integer> departmentIdList = fsAddressBookScope.getDepartmentIdList();
                if (!departmentIdList.contains(AuthManager.defaultAllDepartment) && !departmentIdList.contains(mainDepartmentId)) {
                    // 可见范围纷享通讯录不包含该部门
                    if (existUserTemplateIdList.contains(templateId)) {
                        int count = userAppMenuTemplateRelationDAO.deleteByTemplateIdAndUserId(ea, templateId, fsUserId);
                        log.info("handleEmployeeChangeEvent does not match, delete data, ea: {} fsUserId: {} count: {} templateId: {}", ea, fsUserId, count, templateId);
                    }
                    continue;
                }

                if (existUserTemplateIdSet.contains(templateId)) {
                    // 该用户已经存在该模板中
                    continue;
                }
                UserAppMenuTemplateRelationEntity userTemplateRelationEntity = new UserAppMenuTemplateRelationEntity();
                userTemplateRelationEntity.setId(UUIDUtil.getUUID());
                userTemplateRelationEntity.setEa(ea);
                userTemplateRelationEntity.setFsUserId(fsUserId);
                userTemplateRelationEntity.setUserType(UserTypeEnum.CRM.getCode());
                userTemplateRelationEntity.setMenuTemplateId(templateId);
                insertList.add(userTemplateRelationEntity);
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                Lists.partition(insertList, 100).forEach(e -> userAppMenuTemplateRelationDAO.batchInsert(e));
            }
            lastId = appMenuTemplateEntityList.get(appMenuTemplateEntityList.size() - 1).getId();
            appMenuTemplateEntityList = appMenuTemplateDAO.scanById(ea, UserRelationTypeEnum.EMPLOYEE.getCode(), lastId, pageSize);
        }
        // 将该用户的缓存清除掉
        appMenuTemplateManager.deleteShowTemplateFromRedis(ea, Lists.newArrayList(fsUserId));
        return Result.newSuccess();
    }


    @Override
    public Result<Void> handleQywxEmployeeChangeEvent(String ea, String qywxUserId, String changeType) {

        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByQywxUserIdList(ea, Lists.newArrayList(qywxUserId));
        if (CollectionUtils.isEmpty(userRelationEntityList)) {
            log.warn("handleQywxEmployeeChangeEvent do not have user relation, ea: {} qywxUserId: {}", ea, qywxUserId);
            return Result.newSuccess();
        }

        int fsUserId = userRelationEntityList.get(0).getFsUserId();

        if ("delete_user".equals(changeType)) {
            int count = userAppMenuTemplateRelationDAO.deleteByFsUserId(ea, fsUserId);
            log.info("handleQywxEmployeeChangeEvent qywx employee is deleted, delete data, ea: {} fsUserId: {} count: {}", ea, fsUserId, count);
            return Result.newSuccess();
        }
        String accessToken = qywxManager.getAccessToken(ea);
        StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(ea, qywxUserId, accessToken, true);
        if (staffDetailResult == null) {
            int count = userAppMenuTemplateRelationDAO.deleteByFsUserId(ea, fsUserId);
            log.info("handleQywxEmployeeChangeEvent staffDetailResult is null, delete data, ea: {} fsUserId: {} count: {}", ea, fsUserId, count);
            return Result.newSuccess();
        }

        List<Integer> departmentIdList = staffDetailResult.getDepartment();
        if (CollectionUtils.isEmpty(departmentIdList)) {
            log.info("handleQywxEmployeeChangeEvent departmentIdList is empty, delete data, ea: {} qywxUserId: {}", ea, qywxUserId);
            return Result.newSuccess();
        }
        Set<Integer> departmentIdSet = Sets.newHashSet(departmentIdList);
        int pageSize = 100;
        List<AppMenuTemplateEntity> appMenuTemplateEntityList = appMenuTemplateDAO.scanById(ea, UserRelationTypeEnum.EMPLOYEE.getCode(), null, pageSize);
        List<String> existUserTemplateIdList = userAppMenuTemplateRelationDAO.getAllMenuTemplateIdByUserId(ea, fsUserId);
        Set<String> existUserTemplateIdSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(existUserTemplateIdList)) {
            existUserTemplateIdSet.addAll(existUserTemplateIdList);
        }
        String lastId;
        while (CollectionUtils.isNotEmpty(appMenuTemplateEntityList)) {
            List<UserAppMenuTemplateRelationEntity> insertList = Lists.newArrayList();
            for (AppMenuTemplateEntity appMenuTemplateEntity : appMenuTemplateEntityList) {
                AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
                AppMenuTemplateScopeBO.QywxAddressBookScope qywxAddressBookScope = scope.getQywxAddressBookScope();
                if (qywxAddressBookScope == null || CollectionUtils.isEmpty(qywxAddressBookScope.getDepartmentIdList())) {
                    // 可见范围没有设置企微通讯录
                    continue;
                }
                List<Integer> settingDepartmentIdList = qywxAddressBookScope.getDepartmentIdList().stream().map(e -> {
                    if (e != AuthManager.defaultAllDepartment) {
                        e = e - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID;
                    }
                    return e;
                }).collect(Collectors.toList());

                boolean match = settingDepartmentIdList.contains(AuthManager.defaultAllDepartment) || settingDepartmentIdList.stream().anyMatch(departmentIdSet::contains);
                String templateId = appMenuTemplateEntity.getId();
                if (!match) {
                    // 可见范围企微通讯录不包含该部门
                    if (existUserTemplateIdList.contains(templateId)) {
                        int count = userAppMenuTemplateRelationDAO.deleteByTemplateIdAndUserId(ea, templateId, fsUserId);
                        log.info("handleQywxEmployeeChangeEvent does not match, delete data, ea: {} fsUserId: {} count: {} templateId: {}", ea, fsUserId, count, templateId);
                    }
                    continue;
                }
                if (existUserTemplateIdSet.contains(templateId)) {
                    // 该用户已经存在该模板中
                    continue;
                }
                UserAppMenuTemplateRelationEntity userTemplateRelationEntity = new UserAppMenuTemplateRelationEntity();
                userTemplateRelationEntity.setId(UUIDUtil.getUUID());
                userTemplateRelationEntity.setEa(ea);
                userTemplateRelationEntity.setFsUserId(fsUserId);
                String userType = QywxUserConstants.isQywxVirtualUserId(fsUserId) ? UserTypeEnum.QYWX.getCode() : UserTypeEnum.CRM.getCode();
                userTemplateRelationEntity.setUserType(userType);
                userTemplateRelationEntity.setMenuTemplateId(templateId);
                insertList.add(userTemplateRelationEntity);
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                Lists.partition(insertList, 100).forEach(e -> userAppMenuTemplateRelationDAO.batchInsert(e));
            }
            lastId = appMenuTemplateEntityList.get(appMenuTemplateEntityList.size() - 1).getId();
            appMenuTemplateEntityList = appMenuTemplateDAO.scanById(ea, UserRelationTypeEnum.EMPLOYEE.getCode(), lastId, pageSize);
        }
        // 将该用户的缓存清除掉
        appMenuTemplateManager.deleteShowTemplateFromRedis(ea, Lists.newArrayList(fsUserId));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> handleMemberChangeEvent(String ea, String memberObjId, Integer fsUserId) {
        if (fsUserId == null) {
            UserRelationEntity userRelationEntity = userRelationManager.getByMemberId(ea, memberObjId);
            if (userRelationEntity == null) {
                log.warn("handleMemberChangeEvent do not have user relation, ea: {} memberObjId: {}", ea, memberObjId);
                return Result.newSuccess();
            }
            fsUserId = userRelationEntity.getFsUserId();
        }
        ObjectData memberObject = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), memberObjId);
        if (memberObject == null) {
            int count = userAppMenuTemplateRelationDAO.deleteByFsUserId(ea, fsUserId);
            log.info("handleMemberChangeEvent memberObject is null, delete data, ea: {} fsUserId: {} count: {}", ea, fsUserId, count);
            return Result.newSuccess();
        }

        String memberType = memberObject.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
        if (!MemberTypeEnum.isMemberMarketing(memberType)) {
            int count = userAppMenuTemplateRelationDAO.deleteByFsUserId(ea, fsUserId);
            log.info("handleMemberChangeEvent memberType is not right, delete data, ea: {} memberObjId: {} count: {}", ea, memberObjId, count);
            return Result.newSuccess();
        }

        int pageSize = 100;
        List<AppMenuTemplateEntity> appMenuTemplateEntityList = appMenuTemplateDAO.scanById(ea, null, null, pageSize);
        List<String> existUserTemplateIdList = userAppMenuTemplateRelationDAO.getAllMenuTemplateIdByUserId(ea, fsUserId);
        Set<String> existUserTemplateIdSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(existUserTemplateIdList)) {
            existUserTemplateIdSet.addAll(existUserTemplateIdList);
        }
        String lastId;
        while (CollectionUtils.isNotEmpty(appMenuTemplateEntityList)) {
            List<UserAppMenuTemplateRelationEntity> insertList = Lists.newArrayList();
            for (AppMenuTemplateEntity appMenuTemplateEntity : appMenuTemplateEntityList) {
                List<String> memberTypeList = getMemberTypeByUserType(appMenuTemplateEntity.getUserType());
                String templateId = appMenuTemplateEntity.getId();
                if (!memberTypeList.contains(memberType)) {
                    // 如果会员的推广身份和模板的用户类型不一致，直接删除
                    int count = userAppMenuTemplateRelationDAO.deleteByTemplateIdAndUserId(ea, templateId, fsUserId);
                    log.info("handleMemberChangeEvent memberObject is null, delete data, ea: {} fsUserId: {} count: {}", ea, fsUserId, count);
                    continue;
                }
                AppMenuTemplateScopeBO scope = JsonUtil.fromJson(appMenuTemplateEntity.getScope(), AppMenuTemplateScopeBO.class);
                AppMenuTemplateScopeBO.MemberScope memberScope = scope.getMemberScope();
                if (memberScope == null) {
                    // 可见范围没有设置会员
                    continue;
                }
                List<Filter> filterList = memberScope.getFilters();
                if (CollectionUtils.isEmpty(filterList)) {
                    continue;
                }
                Filter filter = new Filter("_id", OperatorConstants.EQ, Lists.newArrayList(memberObjId));
                filterList.add(filter);
                List<PaasQueryArg.Condition> conditionList = BeanUtil.copyByGson(filterList, PaasQueryArg.Condition.class);
                PaasQueryArg.Condition memberTypeCondition = new PaasQueryArg.Condition(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), memberTypeList, OperatorConstants.IN);
                conditionList.add(memberTypeCondition);
                PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
                paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                paasQueryFilterArg.setSelectFields(Lists.newArrayList(CrmMemberFieldEnum.ID.getApiName(), CrmMemberFieldEnum.NAME.getApiName(), CrmMemberFieldEnum.MEMBER_TYPE.getApiName()));
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                paasQueryArg.setFilters(conditionList);
                paasQueryFilterArg.setQuery(paasQueryArg);
                int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg);
                if (totalCount <= 0) {
                    // 没有匹配
                    if (existUserTemplateIdSet.contains(templateId)) {
                        int count = userAppMenuTemplateRelationDAO.deleteByTemplateIdAndUserId(ea, templateId, fsUserId);
                        log.info("handleMemberChangeEvent does not match, delete data, ea: {} memberObjId: {} count: {} templateId: {}", ea, memberObjId, count, templateId);
                    }
                    continue;
                }
                if (existUserTemplateIdSet.contains(templateId)) {
                    // 该用户已经存在该模板中
                    continue;
                }
                UserAppMenuTemplateRelationEntity userTemplateRelationEntity = new UserAppMenuTemplateRelationEntity();
                userTemplateRelationEntity.setId(UUIDUtil.getUUID());
                userTemplateRelationEntity.setEa(ea);
                userTemplateRelationEntity.setFsUserId(fsUserId);
                String userType;
                if (QywxUserConstants.isFsUserId(fsUserId)) {
                    userType = UserTypeEnum.CRM.getCode();
                } else if (QywxUserConstants.isQywxVirtualUserId(fsUserId)) {
                    userType = UserTypeEnum.QYWX.getCode();
                } else {
                    userType = UserTypeEnum.getByMemberType(memberType).getCode();
                }
                userTemplateRelationEntity.setUserType(userType);
                userTemplateRelationEntity.setMenuTemplateId(templateId);
                insertList.add(userTemplateRelationEntity);
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                Lists.partition(insertList, 100).forEach(e -> userAppMenuTemplateRelationDAO.batchInsert(e));
            }
            lastId = appMenuTemplateEntityList.get(appMenuTemplateEntityList.size() - 1).getId();
            appMenuTemplateEntityList = appMenuTemplateDAO.scanById(ea, lastId, null, pageSize);
        }
        // 将该用户的缓存清除掉
        appMenuTemplateManager.deleteShowTemplateFromRedis(ea, Lists.newArrayList(fsUserId));
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<UserRelationVO>> userList(TemplateUserListArg arg) {
        if (arg.getPageNum() == null || arg.getPageSize() == null || StringUtils.isBlank(arg.getTemplateId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        Page<UserRelationDTO> page = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
        List<UserRelationDTO> userRelationDTOList = userAppMenuTemplateRelationDAO.list(arg, page, ea);
        PageResult<UserRelationVO> pageResult = new PageResult<>();
        List<UserRelationVO> userRelationVoList = Lists.newArrayList();
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setTotalCount(0);
        pageResult.setResult(userRelationVoList);
        if (CollectionUtils.isEmpty(userRelationDTOList)) {
            return Result.newSuccess(pageResult);
        }
        userRelationVoList = userRelationManager.buildUserRelationVoList(ea, userRelationDTOList);
        pageResult.setResult(userRelationVoList);
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    @Override
    public boolean needStrictCheckGroup(String ea, int fsUserId, String menuId) {
        if (StringUtils.isBlank(menuId)) {
            return false;
        }
        MenuDetailArg menuDetailArg = new MenuDetailArg();
        menuDetailArg.setEa(ea);
        menuDetailArg.setFsUserId(fsUserId);
        menuDetailArg.setId(menuId);
        Result<AppMenuDetailVO> result = menuDetail(menuDetailArg);
        if (!result.isSuccess()) {
            return false;
        }
        String rule = result.getData().getObjectAccessibleRule();
        if (!AppMenuAccessibleRuleEnum.isObjectGroupAccessibleRule(rule)) {
            return false;
        }
        // 只有配置了单个分组和多个分组才需要强制校验分组
        return !AppMenuAccessibleRuleEnum.OBJECT_GROUP_ACCESSIBLE.getType().equals(rule) && !AppMenuAccessibleRuleEnum.ALL.getType().equals(rule);
    }
}
