package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.AssociateIdTypeEnum;
import com.facishare.marketing.common.enums.CrmMailObjFieldEnum;
import com.facishare.marketing.common.enums.NoticeContentTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.HttpUtil;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.NoticeDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.NoticeEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.innerResult.EmployeeEmailSendResult;
import com.facishare.marketing.provider.innerResult.MailObjResult;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: Smallfan
 * @Date: created in 2019-12-24 16:05
 * @Description:
 */
@Slf4j
@Service
public class NoticeManager {

    @ReloadableProperty("host")
    private String host;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private NoticeDAO noticeDAO;

    @Autowired
    private MailObjManager mailObjManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private EIEAConverter eieaConverter;

    public String urlDispatcher(String ea, Integer contentType, String contentId, String noticeId, String marketingActivityId) {
        String bundlePageName = getBundleName(contentType);
        if (StringUtils.isBlank(bundlePageName)) {
            log.warn("NoticeManager.urlDispatcher bundlePageName not found, contentType={}", contentType);
        }

        if (needHttpUrl(contentType)) {
            return httpUrlGenerator(ea, contentType, contentId, noticeId, marketingActivityId);
        }

        return cmlUrlGenerator(bundlePageName, contentType, contentId, noticeId, marketingActivityId);
    }

    private String getBundleName(Integer contentType) {
        String bundlePageName = null;

        NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(contentType);
        switch (noticeContentTypeEnum) {
            case ARTICLE: {
                bundlePageName = "article_detail";
                break;
            }
            case ACTIVITY: {
                // bundlePageName = "conference_detail";
                // 会议直接跳转微页面
                bundlePageName = "hexagon_detail";
                break;
            }
            case PRODUCT: {
                bundlePageName = "spread_product_detail";
                break;
            }
            case QR_POSTER: {
                bundlePageName = "generate_poster";
                break;
            }
            case CLUE_CONFIRM: {
                bundlePageName = "distri_clue_detail";
                break;
            }
            case DISTRIBUTOR_REVIEW: {
                bundlePageName = "distri_register_review";
                break;
            }
            case CONFERENCE_INVITE: {
                bundlePageName = "invitation_list";
                break;
            }
            case CONFERENCE_ENROLL_REVIEW: {
                bundlePageName = "conference_review";
                break;
            }
            case HEXAGON: {
                bundlePageName = "hexagon_detail";
                break;
            }
            case OUT_LINK: {
                bundlePageName = "external_content_detail";
                break;
            }
            default:
                break;
        }

        return bundlePageName;
    }

    private boolean needHttpUrl(Integer contentType) {
        NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(contentType);
        switch (noticeContentTypeEnum) {
            case QR_POSTER: {
                return true;
            }
        }

        return false;
    }

    private String httpUrlGenerator(String ea, Integer contentType, String contentId, String noticeId, String marketingActivityId) {
        ParamResult paramResult = new ParamResult();

        String url = host + "/proj/page/marketing-poster/" + ea +"?type=3&posterid={contentId}&ea=" + ea;
        url = url.replace("{contentId}", contentId);

        StringBuilder sb = new StringBuilder(url);

        sb.append("&noticeId=");
        sb.append(noticeId);
        sb.append("&ea=");
        sb.append(ea);
        if (StringUtils.isNotBlank(marketingActivityId)) {
            sb.append("&marketingActivityId=");
            sb.append(marketingActivityId);
        }

        url = sb.toString();

        paramResult.setUrl(url);

        return GsonUtil.toJson(paramResult);
    }

    private String cmlUrlGenerator(String bundlePageName, Integer contentType, String contentId, String noticeId, String marketingActivityId) {
        ParamResult paramResult = new ParamResult();
        StringBuilder sb = new StringBuilder();
        sb.append("cml://cmlMarketing/");
        sb.append(bundlePageName);

        sb.append("?contentType=");
        sb.append(contentType);

        if (NoticeContentTypeEnum.ACTIVITY.getType() == contentType) {
            ActivityEntity conference = conferenceDAO.getConferenceById(contentId);
            if (conference != null) {
                sb.append("&id=");
                sb.append(conference.getActivityDetailSiteId());
                sb.append("&targetObjectType=");
                sb.append(ObjectTypeEnum.ACTIVITY.getType());
                sb.append("&targetObjectId=");
                sb.append(conference.getId());
            }
        } else {
            sb.append("&id=");
            sb.append(contentId);
        }

        sb.append("&noticeId=");
        sb.append(noticeId);

        if (StringUtils.isNotBlank(marketingActivityId)) {
            sb.append("&marketingActivityId=");
            sb.append(marketingActivityId);
        }
        String url = sb.toString();
        String baseUrl = "ava://marketing_app/";
        String resourceUrl = "";
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("objectId",contentId);
        if (StringUtils.isNotEmpty(marketingActivityId)) {
            paramMap.put("marketingActivityId",marketingActivityId);
        }
        //替换cml 为 ava
        if (NoticeContentTypeEnum.HEXAGON.equalsType(contentType)) {
            paramMap.put("objectType",NoticeContentTypeEnum.fromType(contentType).toObjectType());
            resourceUrl = "pkgs/pkg-hexagon/pages/detail/hexagon-detail.html";
            resourceUrl = HttpUtil.joinParamters(resourceUrl,paramMap);
            url = baseUrl + resourceUrl;
        } else if (NoticeContentTypeEnum.ACTIVITY.equalsType(contentType)) {
            paramMap.put("objectType",NoticeContentTypeEnum.fromType(contentType).toObjectType());
            resourceUrl = "pkgs/pkg-conference/pages/detail/conference-detail.html";
            resourceUrl = HttpUtil.joinParamters(resourceUrl,paramMap);
            url = baseUrl + resourceUrl;
        } else if (NoticeContentTypeEnum.PRODUCT.equalsType(contentType)) {
            paramMap.put("objectType",NoticeContentTypeEnum.fromType(contentType).toObjectType());
            resourceUrl = "pkgs/pkg-product/pages/detail/product-detail.html";
            resourceUrl = HttpUtil.joinParamters(resourceUrl,paramMap);
            url = baseUrl + resourceUrl;
        } else if (NoticeContentTypeEnum.ARTICLE.equalsType(contentType)) {
            paramMap.put("objectType",NoticeContentTypeEnum.fromType(contentType).toObjectType());
            resourceUrl = "pkgs/pkg-article/pages/detail/article-detail.html";
            resourceUrl = HttpUtil.joinParamters(resourceUrl,paramMap);
            url = baseUrl + resourceUrl;
        } else if (NoticeContentTypeEnum.OUT_LINK.equalsType(contentType)) {
            paramMap.put("objectType",NoticeContentTypeEnum.fromType(contentType).toObjectType());
            resourceUrl = "pkgs/pkg-external-content/pages/detail/external-content-detail";
            resourceUrl = HttpUtil.joinParamters(resourceUrl,paramMap);
            url = baseUrl + resourceUrl;
        } else if (NoticeContentTypeEnum.CONFERENCE_INVITE.equalsType(contentType)) {
            resourceUrl = "pkgs/pkg-conference/pages/invitation/invitation?conferenceId="+contentId;
            url = baseUrl + resourceUrl;
        } else if (NoticeContentTypeEnum.CONFERENCE_ENROLL_REVIEW.equalsType(contentType)) {
            resourceUrl = "pkgs/pkg-conference/pages/review-list/review-list?conferenceId="+contentId;
            url = baseUrl + resourceUrl;
        }
        paramResult.setUrl(url);

        return GsonUtil.toJson(paramResult);
    }

    @Data
    public static class ParamResult implements Serializable {
        private String url;
    }

    /**
     * 是否为全员营销邮件推广
     * @param ea
     * @param marketingActivityId
     * @return
     */
    public boolean isEmployeeEmailSpread(String ea, String marketingActivityId) {
        MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByEaAndMarketingActivityId(ea, marketingActivityId);
        if (configEntity == null) {
            return false;
        }

        if (Objects.equals(configEntity.getAssociateIdType(), AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType())) {
            String associateId = configEntity.getAssociateId();
            NoticeEntity noticeEntity = noticeDAO.getNoticeById(associateId);
            return noticeEntity != null && Objects.equals(noticeEntity.getContentType(), NoticeContentTypeEnum.EMAIL_MATERIAL.getType());
        }
        return false;
    }

    public Map<Integer, EmployeeEmailSendResult> getEmployeeEmailSendCount(String ea, List<Integer> userIds, String marketingActivityId) {
        // 是否为全员营销邮件推广
        boolean employeeEmailSpread = isEmployeeEmailSpread(ea, marketingActivityId);
        if (!employeeEmailSpread) {
            return Maps.newHashMap();
        }

        //从邮件对象中查询，然后再汇总
        List<MailObjResult> mailObjResults = mailObjManager.queryByOwners(ea, userIds, marketingActivityId);
        if (CollectionUtils.isEmpty(mailObjResults)) {
            return Maps.newHashMap();
        }

        Map<Integer, EmployeeEmailSendResult> map = Maps.newHashMap();
        Map<Integer, List<MailObjResult>> collect = mailObjResults.stream().collect(Collectors.groupingBy(MailObjResult::getOwner));
        collect.forEach((userId, mailObjResults1) -> {
            EmployeeEmailSendResult employeeEmailSendResult = new EmployeeEmailSendResult();
            int sendCount = 0,receiveCount = 0;
            for (MailObjResult mailObjResult : mailObjResults1) {
                String processingStatus = mailObjResult.getProcessingStatus();
                String toAddress = mailObjResult.getToAddress();
                if (StringUtils.isNotBlank(toAddress)) {
                    String[] split = toAddress.split(";");
                    int length = split.length;
                    sendCount += length;
                    if (Objects.equals(processingStatus, MailObjManager.SEND_SUCCESS)) {
                        // 送达数量
                        receiveCount += length;
                    }
                }
            }
            employeeEmailSendResult.setSendCount(sendCount);
            employeeEmailSendResult.setReceiveCount(receiveCount);
            map.put(userId, employeeEmailSendResult);
        });
        return map;
    }

    /**
     * 查询活动下的邮件发送统计数据
     * @param ea
     * @param marketingActivityId
     * @return
     */
    public EmployeeEmailSendResult getAllEmailSendCount(String ea, String marketingActivityId) {
        EmployeeEmailSendResult result = new EmployeeEmailSendResult();
        boolean employeeEmailSpread = isEmployeeEmailSpread(ea, marketingActivityId);
        if (!employeeEmailSpread) {
            result.setSendCount(0);
            result.setReceiveCount(0);
            return result;
        }

        // 先查询总数
        HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        ControllerListArg arg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(CrmMailObjFieldEnum.MARKETING_ACTIVITY_ID.getFieldName(), Collections.singletonList(marketingActivityId), FilterOperatorEnum.EQ);
        arg.setSearchQuery(searchQuery);
        arg.setFieldProjection(Lists.newArrayList(CrmMailObjFieldEnum.ID.getFieldName(), CrmMailObjFieldEnum.TO_ADDRESS.getFieldName(), CrmMailObjFieldEnum.PROCESSING_STATUS.getFieldName()));
        Integer total = metadataControllerServiceManager.getTotal(headerObj, MailObjManager.API_NAME, arg);
        if (total <= 0) {
            result.setSendCount(0);
            result.setReceiveCount(0);
            return result;
        }

        arg.setSearchRichTextExtra(true);// 查询大文本字段
        int sendCount = 0;
        int receiveCount = 0;
        //分批查询，一次查1000条
        int limit = 1000;
        int offset = 0;
        int page = (total - 1) / limit + 1;
        for (int i = 1; i <= page; i++) {
            searchQuery.setLimit(limit);
            searchQuery.setOffset(offset);
            offset = offset + limit;
            Page<ObjectData> dataPage = metadataControllerServiceManager.list(headerObj, MailObjManager.API_NAME, arg);
            if (dataPage != null && CollectionUtils.isNotEmpty(dataPage.getDataList())) {
                List<ObjectData> dataList = dataPage.getDataList();
                for (ObjectData data : dataList) {
                    String toAddress = data.getString(CrmMailObjFieldEnum.TO_ADDRESS.getFieldName());
                    String processingStatus = data.getString(CrmMailObjFieldEnum.PROCESSING_STATUS.getFieldName());
                    if (StringUtils.isNotBlank(toAddress)) {
                        String[] split = toAddress.split(";");
                        int length = split.length;
                        sendCount += length;
                        if (Objects.equals(processingStatus, "send_success")) {
                            receiveCount += length;
                        }
                    }

                }
            }
        }
        result.setSendCount(sendCount);
        result.setReceiveCount(receiveCount);
        return result;
    }

}
