package com.facishare.marketing.provider.dao.live;

import com.facishare.marketing.provider.entity.live.ThirdLiveAccountEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ThirdLiveAccountDAO {
    @Insert("INSERT INTO third_live_account(id, ea, app_id, secret_key, platform, create_time, update_time) VALUES(#{entity.id}, #{entity.ea}, #{entity.appId}, #{entity.secretKey}, #{entity.platform}, now(), now())")
    int insert(@Param("entity") ThirdLiveAccountEntity entity);

    @Select("SELECT * FROM third_live_account WHERE ea=#{ea} and platform = #{platform}")
    ThirdLiveAccountEntity getByEa(@Param("ea")String ea, @Param("platform") Integer platform);

    @Select("SELECT * FROM third_live_account WHERE app_id=#{appId} and platform = #{platform} order by create_time desc limit 1")
    ThirdLiveAccountEntity getByAppId(@Param("ea")String ea, @Param("appId")String appId, @Param("platform") Integer platform);

    @Update("update third_live_account set app_id = #{entity.appId}, secret_key = #{entity.secretKey}, update_time = now() where ea = #{entity.ea} and platform = #{entity.platform}")
    int update(@Param("entity") ThirdLiveAccountEntity entity);
}
