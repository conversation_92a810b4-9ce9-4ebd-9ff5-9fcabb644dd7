package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.provider.entity.mail.MailTaskStatisticsEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface MailTaskStatisticsDAO {

    @Insert("INSERT INTO mail_task_statistics (\n"
            + "        \"id\",\n"
            + "        \"ea\",\n"
            + "        \"task_id\",\n"
            + "        \"send_num\",\n"
            + "        \"delivered_num\",\n"
            + "        \"open_num\",\n"
            + "        \"click_num\",\n"
            + "        \"open_user_num\",\n"
            + "        \"click_user_num\",\n"
            + "        \"create_time\",\n"
            + "        \"update_time\"\n"
            + "        )VALUES (\n"
            + "        #{obj.id},\n"
            + "        #{obj.ea},\n"
            + "        #{obj.taskId},\n"
            + "        #{obj.sendNum},\n"
            + "        #{obj.deliveredNum},\n"
            + "        #{obj.openNum},\n"
            + "        #{obj.clickNum},\n"
            + "        #{obj.openUserNum},\n"
            + "        #{obj.clickUserNum},\n"
            + "        now(),\n"
            + "        now()\n"
            + "        ) ON CONFLICT (ea,task_id) DO UPDATE SET send_num = #{obj.sendNum}, delivered_num = #{obj.deliveredNum}, open_num = #{obj.openNum}, click_num = #{obj.clickNum}, open_user_num = #{obj.openUserNum}, click_user_num = #{obj.clickUserNum}, update_time = now();")
    void upsertStatisticsData(@Param("obj") MailTaskStatisticsEntity mailTaskStatisticsEntity);

    @Select(" SELECT * FROM mail_task_statistics WHERE ea = #{ea} AND task_id = #{taskId}")
    MailTaskStatisticsEntity getByEaAndTaskId(@Param("ea") String ea, @Param("taskId") String taskId);


    @Select("<script>" +
            " SELECT * FROM mail_task_statistics WHERE ea=#{ea} AND task_id IN " +
            " <foreach open='(' close=')' separator=',' collection='taskIds' item='item'>" +
            " #{item}" +
            " </foreach> " +
            " </script>")
    List<MailTaskStatisticsEntity> getByEaAndTaskIds(@Param("ea") String ea, @Param("taskIds") List<String> taskIds);


    @Update("<script>"
            + "update mail_task_statistics\n"
            + "<set>\n"
            + "<if test=\"obj.deliveredNum != null\">\n"
            + "\"delivered_num\" = #{obj.deliveredNum},\n"
            + "</if>\n"
            + "<if test=\"obj.sendNum != null\">\n"
            + "\"send_num\" = #{obj.sendNum},\n"
            + "</if>\n"
            + "<if test=\"obj.openNum != null\">\n"
            + "\"open_num\" = #{obj.openNum},\n"
            + "</if>\n"
            + "<if test=\"obj.clickNum != null\">\n"
            + "\"click_num\" = #{obj.clickNum},\n"
            + "</if>\n"
            + "<if test=\"obj.openUserNum != null\">\n"
            + "\"open_user_num\" = #{obj.openUserNum},\n"
            + "</if>\n"
            + "<if test=\"obj.clickUserNum != null\">\n"
            + "\"click_user_num\" = #{obj.clickUserNum},\n"
            + "</if>\n"
            + "\"update_time\" = now()"
            + " </set>"
            + "  where \"ea\" = #{obj.ea} and \"id\" = #{obj.id}"
            +"</script>")
    int update(@Param("obj") MailTaskStatisticsEntity entity);
}
