package com.facishare.marketing.provider.dto.conference;

import com.facishare.marketing.common.enums.conference.ConferenceParamEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.entity.ActivityEntity;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

/**
 * Created  By zhoux 2020/08/18
 **/
@Data
public class ConferenceEnrollBaseInfoDTO implements Serializable {

    private String title;

    private Date startTimeStamp;

    private Date endTimeStamp;

    private String startTime;

    private String endTime;

    private String location;

    private Integer ticketCode;

    private String enrollName;

    private String enrollPhone;

    private String enrollEmail;

    private String activityEnrollId;

    private String campaignId;

    private String activityId;

    private String marketingEventId;

    private String activityUrl;

    private String campaignMembersObjId;

    public Map<String, String> getAllEnrollParams(String ea, String host) {
        Map<String, String> enrollParams = new HashMap<>(8);
        enrollParams.put("ticketCode", ticketCode + "");
        enrollParams.put("ticketUrl", ConferenceParamEnum.buildConferenceTicketUrl(host, ea, campaignId));
        enrollParams.put("enrollName", enrollName);
        enrollParams.put("enrollPhone", enrollPhone);
        enrollParams.put("enrollEmail", enrollEmail);
        return enrollParams;
    }

    public void conversionByActivity(ActivityEntity activityEntity) {
        if (activityEntity == null) {
            return;
        }
        title = activityEntity.getTitle();
        if (activityEntity.getStartTime() != null) {
            startTime = DateUtil.format(activityEntity.getStartTime(), "yyyy-MM-dd HH:mm");
        }
        if (activityEntity.getEndTime() != null) {
            endTime = DateUtil.format(activityEntity.getEndTime(), "yyyy-MM-dd HH:mm");
        }
        location = activityEntity.getLocation();
        activityId = activityEntity.getId();
        marketingEventId = activityEntity.getMarketingEventId();
    }

}
