package com.facishare.marketing.provider.service;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.marketing.api.arg.RecordWxUserMkActionArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.service.WxUserMkActionService;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.manager.ObjectTagManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.mq.sender.MarketingRecordActionSender;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("wxUserMkActionService")
@Slf4j
public class WxUserMkActionServiceImpl implements WxUserMkActionService {
    @Autowired
    private MarketingRecordActionSender marketingRecordActionSender;
    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private ObjectManager objectManager;

    @Override
    public Result<Void> record(RecordWxUserMkActionArg arg) {
        Preconditions.checkArgument(arg.isValid());
        if (StringUtils.isEmpty(arg.getMarketingEventId()) && StringUtils.isNotEmpty(arg.getMarketingActivityId())) {
            arg.setMarketingEventId(objectManager.queryMarketingEventId(arg.getMarketingActivityId()));
        }
        MarketingUserActionEvent marketingUserActionEvent = new MarketingUserActionEvent();
        BeanUtils.copyProperties(arg, marketingUserActionEvent);
        marketingUserActionEvent.setChannelType(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType());
        marketingUserActionEvent.setActionTime(System.currentTimeMillis());
        Map<String, Object> extensionParams = marketingUserActionEvent.getExtensionParams();
        if (extensionParams == null) {
            extensionParams = Maps.newHashMap();
            marketingUserActionEvent.setExtensionParams(extensionParams);
        }
        extensionParams.put(RecordActionArg.SPREAD_CHANNEL_KEY, SpreadChannelEnum.OFFICIAL_ACCOUNT.getCode());
        extensionParams.put(RecordActionArg.MARKETING_SCENE_KEY, MarketingSceneEnum.OFFICIAL_ACCOUNT.getCode());
        extensionParams.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, arg.getWxAppId());
        extensionParams.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, ChannelAccountPlatformEnum.OFFICIAL_ACCOUNT.getPlatform());
        marketingUserActionEvent.setExtensionParams(extensionParams);

        log.info("marketingRecordActionSender.send,marketingUserActionEvent = {}", marketingUserActionEvent);
        marketingRecordActionSender.send(marketingUserActionEvent);
        if (arg.getActionType().equals(MarketingUserActionType.LOOK_UP_ARTICLE.getActionType())) {
            ThreadPoolUtils.execute(() -> {
                objectTagManager.batchAddTagsToUserMarketings(arg.getEa(), null, null, ChannelEnum.WX_SERVICE.getType(),
                        arg.getWxOpenId(), arg.getWxAppId(), arg.getObjectId(), arg.getObjectType(), null,
                        arg.getMarketingEventId(), "wxUserMkAction");
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
        return Result.newSuccess();
    }
}
