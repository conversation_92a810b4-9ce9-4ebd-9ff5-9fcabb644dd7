package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.QywxContactMeConfigEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Created by zhengh on 2020/3/11.
 */
public interface QywxContactMeConfigDAO {
    @Insert("INSERT INTO qywx_contact_config(ea, uid, config_id, create_time, update_time) VALUES(#{entity.ea}, #{entity.uid}, #{entity.configId}, now(), now()) ON CONFLICT DO NOTHING")
    int insert(@Param("entity") QywxContactMeConfigEntity entity);

    @Select("SELECT * FROM qywx_contact_config WHERE uid=#{uid}")
    QywxContactMeConfigEntity getByUid(@Param("ea")String ea, @Param("uid")String uid);

    @Select("SELECT * FROM qywx_contact_config WHERE ea is null LIMIT 1000")
    List<QywxContactMeConfigEntity> getByEaIsNull();

    /**
     * 批量更新ea字段
     * 使用PostgreSQL的UPDATE FROM VALUES语法，效率最高
     * @param list 待更新的实体列表
     * @return 更新的记录数
     */
    @Update("<script>UPDATE qywx_contact_config as c SET ea=tmp.ea, update_time=now() FROM (values"
            + "<foreach separator=',' collection='list' item='item'>"
            +   "(#{item.uid}, #{item.ea})"
            + "</foreach>"
            + ") as tmp(uid, ea) WHERE c.uid=tmp.uid"
            + "</script>")
    int batchUpdateEa(@Param("list")List<QywxContactMeConfigEntity> list);
}
