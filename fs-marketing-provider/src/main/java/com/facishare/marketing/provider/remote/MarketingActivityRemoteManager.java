/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.remote;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.MarketingActivitySpreadTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.MarketingActivityData;
import com.fxiaoke.crmrestapi.service.MarketingService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MarketingActivityRemoteManager {
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private EIEAConverter eieaConverter;


    public String addSpreadTypeWxSpreadOption(Integer ei, Integer fsUserId){
        Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, fsUserId), "MarketingActivityObj");
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null){
            return "marketingEventObj not existed";
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("spread_type");
        if (fieldDescribe == null){
            return "marketingEventObj#eventType not existed";
        }
        Map<String, Double> unRemoveFieldConfig = new HashMap<>(3);
        unRemoveFieldConfig.put("edit", 1d);
        unRemoveFieldConfig.put("enable", 1d);
        unRemoveFieldConfig.put("remove", 0d);

        List<Map<String, Object>> options = (List<Map<String, Object>>)(fieldDescribe.get("options"));
        Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(o -> o.toString()).collect(Collectors.toSet());
        if(!optionValues.contains("5")){
            Map<String, Object> contentMarketingOption = new HashMap<>(4);
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "企业微信群发");
            contentMarketingOption.put("value", "5");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        if (!optionValues.contains("6")){
            Map<String, Object> contentMarketingOption = new HashMap<>(4);
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "邮件群发");
            contentMarketingOption.put("value", "6");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        if (!optionValues.contains("7")){
            Map<String, Object> contentMarketingOption = new HashMap<>(4);
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "伙伴营销");
            contentMarketingOption.put("value", "7");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }
        if (!optionValues.contains("8")){
            Map<String, Object> contentMarketingOption = new HashMap<>(4);
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "朋友圈营销");
            contentMarketingOption.put("value", "8");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        if (!optionValues.contains("9")){
            Map<String, Object> contentMarketingOption = new HashMap<>(4);
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "员工主动推广");
            contentMarketingOption.put("value", "9");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        if (!optionValues.contains("10")){
            Map<String, Object> contentMarketingOption = new HashMap<>(4);
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "伙伴主动推广");
            contentMarketingOption.put("value", "10");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        if (!optionValues.contains("11")){
            Map<String, Object> contentMarketingOption = new HashMap<>(4);
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "用户主动推广");
            contentMarketingOption.put("value", "11");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        return objectDescribeService.updateField(new HeaderObj(ei, fsUserId), "MarketingActivityObj", "spread_type", fieldDescribe).isSuccess() ? "success":"fail";
    }

    public String addCancelStatusOption(Integer ei, Integer fsUserId){
        Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, fsUserId), "MarketingActivityObj");
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null){
            return "MarketingActivityObj not existed";
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("status");
        if (fieldDescribe == null){
            return "MarketingActivityObj#status not existed";
        }
        Map<String, Double> unRemoveFieldConfig = new HashMap<>(3);
        unRemoveFieldConfig.put("edit", 1d);
        unRemoveFieldConfig.put("enable", 1d);
        unRemoveFieldConfig.put("remove", 0d);

        List<Map<String, Object>> options = (List<Map<String, Object>>)(fieldDescribe.get("options"));
        Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(o -> o.toString()).collect(Collectors.toSet());
        if(optionValues.contains("10")){
            return "success";
        }

        Map<String, Object> contentMarketingOption = new HashMap<>(4);
        contentMarketingOption.put("label", "已取消");
        contentMarketingOption.put("value", "10");
        contentMarketingOption.put("config", unRemoveFieldConfig);
        options.add(contentMarketingOption);

        return objectDescribeService.updateField(new HeaderObj(ei, fsUserId), "MarketingActivityObj", "status", fieldDescribe).isSuccess() ? "success":"fail";
    }

    /**
     * 给市场活动相关数据权限增加营销活动查看权限
     * @param ei
     * @param fsUserId
     */
    public String addMarketingEventLookRoler(Integer ei, Integer fsUserId) {
        Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, fsUserId), "MarketingActivityObj");
        if (!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null) {
            return "marketingEventObj not existed";
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("marketing_event_id");
        if (fieldDescribe == null) {
            return "marketingEventObj#eventType not existed";
        }

        List<String> lookupRoles = (List<String>) fieldDescribe.get("lookup_roles");
        if (lookupRoles == null){
            lookupRoles = new ArrayList<>();
            fieldDescribe.put("lookup_roles", lookupRoles);
        }

        //1-负责人 4-普通成员
        if (!lookupRoles.contains("1")){
            lookupRoles.add("1");
        }
        if (!lookupRoles.contains("4")){
            lookupRoles.add("4");
        }
        return objectDescribeService.updateField(new HeaderObj(ei, fsUserId), "MarketingActivityObj", "status", fieldDescribe).isSuccess() ? "success":"fail";
    }

    //给MarketingActivityObj添加一个属性
    public void addMarketingObjField(Integer ei, Integer fsUserId, String ea) {
        if (enterpriseStop(ea)) {
            return;
        }
        HeaderObj headerObj = new HeaderObj(ei, fsUserId);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(headerObj, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (describeResult.isSuccess()) {
            HashMap<String, FieldDescribe> fields = describeResult.getData().getDescribe().getFields();
            if (!fields.containsKey("wx_app_id")) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                arg.setFieldDescribe("{\"describe_api_name\": \"MarketingActivityObj\",\"default_is_expression\": false,\"is_index\": true,\"is_active\": true,\"pattern\": \"\",\"is_unique\": false,\"default_value\": \"\",\"label\": \"微信公众号ID\",\"type\": \"text\",\"default_to_zero\": false,\"is_required\": false,\"api_name\": \"wx_app_id\",\"define_type\": \"package\",\"is_index_field\": false,\"is_single\": false,\"help_text\": \"\",\"max_length\": 100,\"status\": \"new\"}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingActivity__default_layout__c\",\"label\":\"默认布局\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(headerObj, arg);
            }
        }
    }

    public boolean enterpriseStop(String ea) {
        GetEnterpriseRunStatusArg arg = new GetEnterpriseRunStatusArg();
        arg.setEnterpriseAccount(ea);

        try {
            arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            GetEnterpriseRunStatusResult getEnterpriseRunStatusResult = enterpriseEditionService.getEnterpriseRunStatus(arg);
            if (getEnterpriseRunStatusResult != null && !getEnterpriseRunStatusResult.getRunStatus().equals(RunStatus.RUN_STATUS_NORMAL)) {
                return true;
            }
        } catch (Exception e) {
            log.warn("CampaignMergeDataResetManager.enterpriseStop error e:{}", e);
            return true;
        }
        return false;
    }
    public void updateWeChatMarketingActivityWxAppId(MarketingActivityExternalConfigEntity entity, Integer ei,Map<String,String> appIdToWxAppIdMap) {
        if (null == entity) {
            return;
        }
        String ea = entity.getEa();
        String marketingActivityId = entity.getMarketingActivityId();
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        ObjectData queryObjectData = new ObjectData();
        queryObjectData.put("id", marketingActivityId);
        Result<MarketingActivityData> byIdMarketingActivity = marketingService.getByIdMarketingActivity(headerObj, queryObjectData);
        if (null == byIdMarketingActivity || !byIdMarketingActivity.isSuccess() || null == byIdMarketingActivity.getData()) {
            return;
        }
        String appId;
        try {
            if (entity.getAssociateIdType() == 1001) {
                appId = entity.getExternalConfig().getWeChatServiceMarketingActivityVO().getAppId();
            } else {
                appId = entity.getExternalConfig().getWeChatTemplateMessageVO().getAppId();
            }
        } catch (Exception e) {
            log.info("updateWeChatMarketingActivityWxAppId get appid error");
            appId = "";
        }
        if (StringUtils.isBlank(appId) || !appIdToWxAppIdMap.containsKey(appId)) {
            return;
        }
        MarketingActivityData data = byIdMarketingActivity.getData();
        Map<String, Object> updateObjectData = Maps.newHashMap();
        addDataMapIfValueNotBlack("_id", data.getId(), updateObjectData);
        addDataMapIfValueNotBlack("wx_app_id", appIdToWxAppIdMap.get(appId), updateObjectData);
        crmV2Manager.updateMarketingActivityObj(ea, updateObjectData, null);
    }

    private void addDataMapIfValueNotBlack(String key, Object value, Map<String, Object> objectData) {
        if (StringUtils.isBlank(key) || value == null) {
            return;
        }
        objectData.put(key, value);
    }


    public void addSpreadTypeOptions(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null) {
            log.warn("addSpreadTypeOptions getDescribe fail, ea: {}", ea);
            return;
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("spread_type");
        if (fieldDescribe == null){
            log.warn("addSpreadTypeOptions spread_type is null, ea: {}", ea);
            return;
        }
        Map<String, Double> unRemoveFieldConfig = new HashMap<>(3);
        unRemoveFieldConfig.put("edit", 1d);
        unRemoveFieldConfig.put("enable", 1d);
        unRemoveFieldConfig.put("remove", 0d);

        List<Map<String, Object>> options = (List<Map<String, Object>>)(fieldDescribe.get("options"));
        Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(o -> o.toString()).collect(Collectors.toSet());

        if (!optionValues.contains("9")){
            Map<String, Object> contentMarketingOption = new HashMap<>();
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "员工主动推广");
            contentMarketingOption.put("value", "9");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        if (!optionValues.contains("10")){
            Map<String, Object> contentMarketingOption = new HashMap<>();
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "伙伴主动推广");
            contentMarketingOption.put("value", "10");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }

        if (!optionValues.contains("11")){
            Map<String, Object> contentMarketingOption = new HashMap<>();
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "用户主动推广");
            contentMarketingOption.put("value", "11");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
        }
        Result<ControllerGetDescribeResult> result = objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), "spread_type", fieldDescribe);
        log.info("addSpreadTypeOptions updateField ea: {} result:{}", ea, result);
    }

    public void addWhatsAppSpreadTypeOptions(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null) {
            log.warn("addSpreadTypeOptions getDescribe fail, ea: {}", ea);
            return;
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("spread_type");
        if (fieldDescribe == null){
            log.warn("addSpreadTypeOptions spread_type is null, ea: {}", ea);
            return;
        }
        Map<String, Double> unRemoveFieldConfig = new HashMap<>(3);
        unRemoveFieldConfig.put("edit", 1d);
        unRemoveFieldConfig.put("enable", 1d);
        unRemoveFieldConfig.put("remove", 0d);

        List<Map<String, Object>> options = (List<Map<String, Object>>)(fieldDescribe.get("options"));
        Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(o -> o.toString()).collect(Collectors.toSet());

        if (!optionValues.contains("12")){
            Map<String, Object> contentMarketingOption = new HashMap<>();
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", "WhatsApp推广");
            contentMarketingOption.put("value", "12");
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
            Result<ControllerGetDescribeResult> result = objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), "spread_type", fieldDescribe);
            log.info("addSpreadTypeOptions updateField ea: {} result:{}", ea, result);
        }
    }

    public void addMemberMarketingSpreadTypeOptions(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null) {
            log.warn("addMemberMarketingSpreadTypeOptions getDescribe fail, ea: {}", ea);
            return;
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get("spread_type");
        if (fieldDescribe == null){
            log.warn("addMemberMarketingSpreadTypeOptions spread_type is null, ea: {}", ea);
            return;
        }
        Map<String, Double> unRemoveFieldConfig = new HashMap<>();
        unRemoveFieldConfig.put("edit", 1d);
        unRemoveFieldConfig.put("enable", 1d);
        unRemoveFieldConfig.put("remove", 0d);

        List<Map<String, Object>> options = (List<Map<String, Object>>)(fieldDescribe.get("options"));
        Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(o -> o.toString()).collect(Collectors.toSet());
        String memberMarketingSpread = String.valueOf(MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getSpreadType());
        if (!optionValues.contains(memberMarketingSpread)){
            Map<String, Object> contentMarketingOption = new HashMap<>();
            contentMarketingOption.put("resource_bundle_key", "");
            contentMarketingOption.put("label", MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getDescription());
            contentMarketingOption.put("value", memberMarketingSpread);
            contentMarketingOption.put("config", unRemoveFieldConfig);
            options.add(contentMarketingOption);
            Result<ControllerGetDescribeResult> result = objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), "spread_type", fieldDescribe);
            log.info("addMemberMarketingSpreadTypeOptions updateField ea: {} result:{}", ea, result);
        }

    }
}
