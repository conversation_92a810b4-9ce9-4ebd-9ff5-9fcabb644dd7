package com.facishare.marketing.provider.dao.pay;

import com.facishare.marketing.api.arg.pay.CallbackParam;
import com.facishare.marketing.provider.entity.pay.FsPayOrder;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface FsPayOrderDao extends ICrudMapper<FsPayOrder> {

    @Update("UPDATE fs_pay_order SET " +
            "order_no=#{orderNo}, " +
            "request_time=#{requestTime}, " +
            "response_time=#{responseTime}, " +
            "status=#{status}, " +
            "enterprise_account=#{enterpriseAccount}, " +
            "to_ea=#{toEA}, " +
            "to_user_id=#{toUserId}, " +
            "fs_user_id=#{fsUserId}, " +
            "pay_channel=#{payChannel}, " +
            "trade_no=#{tradeNo}, " +
            "update_time=now() where id = #{merchantOrderNo}"
    )
    void updateById(CallbackParam callbackParam,@Param("ea")String ea);


    @Select("select status from fs_pay_order where id = #{id}")
    Integer queryById(@Param("id") String id,@Param("ea")String ea);

    @Select("<script>" +
            "select * from fs_pay_order where id in " +
            "<foreach open='(' close=')' separator=',' collection='ids' item='ite'>" +
            "#{ite}" +
            "</foreach>" +
            "</script>")
    List<FsPayOrder> queryByIds(@Param("ids") Collection<String> ids,@Param("ea")String ea);
}
