package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.SMSSendDetailEntity;
import com.facishare.marketing.provider.entity.sms.SMSSendEntity;
import com.facishare.marketing.provider.entity.sms.SMSSendSumEntity;
import com.github.mybatis.pagination.Page;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @创建人 zhengliy
 * @创建时间 2018/12/24 20:02
 * @描述
 */
public interface SendDao {



    @Select("select * from sms_send where id = #{id}")
    SMSSendEntity getSMSSendById( @Param("id") String id,@Param("ea") String ea);


    @Select("select * from sms_detail where sid = #{sid}")
    SMSSendDetailEntity querySMSSendDetailBySid(@Param("sid") String sid,@Param("ea") String ea);


    @Update("update sms_detail set report_status = #{obj.reportStatus}, report_err_msg = #{obj.reportErrMsg}, report_description = #{obj.reportDescription} where id = #{obj.id}")
    void updateSendDetailCallback(@Param("obj") SMSSendDetailEntity smsSendDetailEntity);


}
