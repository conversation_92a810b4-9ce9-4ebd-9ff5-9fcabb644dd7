package com.facishare.marketing.outapi.service;

import com.facishare.marketing.common.enums.ConferenceEnrollOrCheckInEnum;
import com.facishare.marketing.common.result.Result;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019-08-06
 */
public interface MarketingFlowInstanceOuterService {
    /**
     *
     * @param ea
     * @param wxAppId 微联服务号AppId
     * @param wxOpenId 微联服务号openId
     * @param phone 手机号码
     * @param email 邮箱
     * @param conferenceId 会议Id
     */
    Result<Boolean> enrollOrCheckInConference(String ea, String wxAppId, String wxOpenId, String phone, String email, String conferenceId, ConferenceEnrollOrCheckInEnum conferenceEnrollOrCheckInEnum);

    /**
     *
     * @param ea
     * @param phone 手机号码
     * @param conferenceId 会议Id
     */
    Result<Boolean> enrollOrCheckInConferenceByH5(String ea, String fingerPrintId, String phone, String email, String conferenceId, ConferenceEnrollOrCheckInEnum conferenceEnrollOrCheckInEnum);

    /**
     *
     * @param ea
     * @param uid 小程序uid
     * @param phone 手机号码
     * @param conferenceId 会议id
     */
    Result<Boolean> enrollOrCheckInConference(String ea, String uid, String phone, String email, String conferenceId, ConferenceEnrollOrCheckInEnum conferenceEnrollOrCheckInEnum);
}
