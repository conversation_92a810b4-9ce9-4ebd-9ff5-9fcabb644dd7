package com.facishare.marketing.common.filters;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.MD5Util;
import com.facishare.marketing.common.util.SpringContextUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.params.SetParams;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.*;

@Slf4j
public class AuthenticationFilter implements Filter {

    private static boolean authSwitch = false;
    private static String authBlackList;
    private static boolean allAuthSwitch = false;
    private static String authWhiteList;
    private static String secretKey = "2a15712b85da4191b2053e5e65f079b5";

    public AuthenticationFilter(String configName) {
        if (StringUtils.isNotEmpty(configName)) {
            ConfigFactory.getInstance().getConfig(configName, this::reloadConfig);
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        // 文件上传的跳过
        if (StringUtils.startsWithIgnoreCase(request.getContentType(), "multipart/")) {
            chain.doFilter(request, response);
            return;
        }
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            // 开启校验,默认关闭
            if (authSwitch) {
                CustomHttpServletRequestWrapper requestWrapper = new CustomHttpServletRequestWrapper(httpServletRequest);
                String uri = requestWrapper.getRequestURI();
                try {
                    // 开启全部校验,默认关闭
                    if (allAuthSwitch) {
                        // 白名单跳过
                        if (StringUtils.isNotEmpty(authWhiteList) && Arrays.stream(authWhiteList.split(",")).anyMatch(uri::contains)) {
                            chain.doFilter(requestWrapper, response);
                            return;
                        }
                    } else {
                        // 不在黑名单跳过
                        if (StringUtils.isEmpty(authBlackList) || Arrays.stream(authBlackList.split(",")).noneMatch(uri::contains)) {
                            chain.doFilter(requestWrapper, response);
                            return;
                        }
                    }
                    JSONObject bodyObj = null;
                    try {
                        Reader tmp = new InputStreamReader(requestWrapper.getInputStream(), requestWrapper.getCharacterEncoding() == null ? "UTF-8" : requestWrapper.getCharacterEncoding());
                        String bodyStr = IOUtils.toString(tmp);
                        bodyObj = JSONObject.parseObject(bodyStr);
                    } catch (Exception e) {
                        log.warn("uri:{} get bodyStr error:", uri, e);
                        chain.doFilter(requestWrapper, response);
                        return;
                    }
                    if (null == bodyObj) {
                        chain.doFilter(requestWrapper, response);
                        return;
                    }
                    // yxtnb:yxtnb
                    if (StringUtils.equals("yxtnb", bodyObj.getString("yxtnb"))) {
                        chain.doFilter(requestWrapper, response);
                        return;
                    }
                    String sign = bodyObj.getString("sign");
                    if (StringUtils.isBlank(sign)) {
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.SIGN_NOT_FOUND);
                        return;
                    }
                    Long timeStamp = null;
                    try {
                        timeStamp = bodyObj.getLong("timestamp");
                    } catch (Exception e) {
                        log.warn("get timestamp error");
                    }
                    if (timeStamp == null) {
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.TIME_STAMP_NOT_FOUND);
                        return;
                    }
                    String nonce = bodyObj.getString("nonce");
                    if (StringUtils.isBlank(nonce)) {
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.NONCE_NOT_FOUND);
                        return;
                    }
                    if (nonce.length() > 128) {
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.NONCE_TOO_LONG);
                        return;
                    }
                    MergeJedisCmd jedisCmd = SpringContextUtil.getBean(MergeJedisCmd.class);
                    String key = "AuthenticationInterceptor_" + nonce;
                    String value = jedisCmd.get(key);
                    if (StringUtils.isNotBlank(value)) {
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.NONCE_REPEAT);
                        return;
                    }
                    // 如果设置成功 会返回OK
                    String val = jedisCmd.set(key, "1", SetParams.setParams().nx().ex(180L));
                    if (StringUtils.isBlank(val)) {
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.NONCE_REPEAT);
                        return;
                    }
                    String secret = secretKey;
                    if (StringUtils.isBlank(secret)) {
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.SECRET_KEY_NOT_CONFIG);
                        return;
                    }
                    Map<String, Object> parameterMap = bodyObj.getInnerMap();
                    // secretKey不要再日志里打印出来
                    String paramWithoutSecretKey = JSONObject.toJSONString(parameterMap);
                    // 复杂类型不纳入校验
                    Map<String, Object> finalParameterMap = Maps.newHashMap();
                    parameterMap.forEach((k, v) -> {
                        if (v instanceof String) {
                            finalParameterMap.put(k, v);
                        }
                    });
                    finalParameterMap.remove("sign");
                    finalParameterMap.put("secretKey", secret);
                    Map<String, Object> sortedParameterMap = timeStamp % 2 == 1 ? sortJson(finalParameterMap, true) : sortJson(finalParameterMap, false);
                    String finalParam = JSONObject.toJSONString(sortedParameterMap);
                    String computerSign = MD5Util.md5String(finalParam);
                    if (!StringUtils.equals(sign, computerSign)) {
                        log.warn("AuthenticationInterceptor sign is not equals computerSign, parameterMap:{}, computerSign: {}", paramWithoutSecretKey, computerSign);
                        writeErrorInfo((HttpServletResponse) response, SHErrorCode.SIGN_VERIFY_ERROR);
                        return;
                    }
                } catch (Exception e) {
                    log.warn("AuthenticationFilter error", e);
                }
                chain.doFilter(requestWrapper, response);
                return;
            }
        }
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
    }

    private void reloadConfig(IConfig config) {
        if (ObjectUtils.isEmpty(config.getString())) {
            log.warn("AuthenticationFilter config is empty ,configName :{}", config.getName());
            return;
        }
        try {
            this.updateConfig(config);
        } catch (Exception e) {
            log.error("AuthenticationFilter update config error,config file :{} ", config.getName(), e);
        }
    }

    private void updateConfig(IConfig config) {
        if (config != null) {
            authSwitch = config.getBool("authentication.authSwitch");
            authBlackList = config.get("authentication.authBlackList");
            allAuthSwitch = config.getBool("authentication.allAuthSwitch");
            authWhiteList = config.get("authentication.authWhiteList");
            secretKey = StringUtils.isEmpty(config.get("authentication.secretKey")) ? secretKey : config.get("authentication.secretKey");
        }
    }

    private static void writeErrorInfo(HttpServletResponse response, SHErrorCode errorCode) throws IOException {
        response.addHeader("Content-Type", "application/json;charset=utf-8");
        response.setStatus(HttpStatus.SC_OK);
        response.getWriter().write(JSONObject.toJSONString(Result.newError(errorCode)));
    }

    public static LinkedHashMap<String, Object> sortJson(Map<String, Object> json, boolean isAsc) {
        LinkedHashMap<String, Object> sortedParameterMap = new LinkedHashMap<>();
        List<String> keys = new ArrayList<>(json.keySet());
        if (isAsc) {
            Collections.sort(keys);
        } else {
            keys.sort(Collections.reverseOrder());
        }
        for (String key : keys) {
            Object value = json.get(key);
            if (value instanceof Map) {
                sortedParameterMap.put(key, sortJson((Map<String, Object>) value, isAsc));
            } else if (value instanceof List) {
                sortedParameterMap.put(key, sortArrayJson((List<Object>) value, isAsc));
            } else {
                sortedParameterMap.put(key, value);
            }
        }
        return sortedParameterMap;
    }

    private static List<Object> sortArrayJson(List<Object> jsonArray, boolean isAsc) {
        List<Object> newJsonArray = Lists.newArrayList();
        for (Object obj : jsonArray) {
            if (obj instanceof Map) {
                newJsonArray.add(sortJson((Map<String, Object>) obj, isAsc));
            } else if (obj instanceof List) {
                newJsonArray.add(sortArrayJson((List<Object>) obj, isAsc));
            } else {
                newJsonArray.add(obj);
            }
        }
        return newJsonArray;
    }

}
