/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.common.util;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2024/9/10
 * @Desc
 **/
public class TemplateUtil {

    public static final String REGEX = "\\{([^}]+)\\}";//匹配{code}的正则表达式

    /**
     * 提取模板内容的{}变量
     * @param template
     * @return
     */
    public static List<String> extractVariables(String template) {
        if (StringUtils.isBlank(template)) {
            return Lists.newArrayList();
        }
        // 正则表达式匹配 {} 中的变量名
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(template);

        // 用于存储提取出来的变量
        List<String> variables = new ArrayList<>();

        // 遍历匹配结果
        while (matcher.find()) {
            // 提取 {} 中的变量名
            variables.add(matcher.group(1));
        }

        return variables;
    }

    /**
     * 判断模板中是否存在{}变量
     * @param template
     * @return
     */
    public static boolean containsVariables(String template) {
        if (StringUtils.isBlank(template)) {
            return false;
        }
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(template);

        // 如果找到匹配的变量，返回true
        return matcher.find();
    }

    /**
     * 使用正则表达式替换所有的 {variable} 为 ${variable}
     * @param template
     * @return
     */
    public static String replaceBracesWithDollar(String template) {
        // 使用正则表达式替换所有的 {variable} 为 ${variable}
        return template.replaceAll(REGEX, "\\$\\{$1\\}");
    }

    public static void main(String[] args) {
        String str = "This is test template{name},age{age},{aaa},{bbb}";
        System.out.println(str);
        List<String> strings = extractVariables(str);
        System.out.println(strings);
        String replaceBracesWithDollar = replaceBracesWithDollar(str);
        System.out.println(replaceBracesWithDollar);
        boolean b = containsVariables(str);
        System.out.println(b);
    }
}
