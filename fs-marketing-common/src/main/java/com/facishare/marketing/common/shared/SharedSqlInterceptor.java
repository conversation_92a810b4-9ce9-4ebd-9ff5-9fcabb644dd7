package com.facishare.marketing.common.shared;

import com.alibaba.druid.sql.ast.SQLName;
import com.alibaba.druid.sql.ast.SQLObject;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.SQLIdentifierExpr;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLJoinTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.druid.sql.ast.statement.SQLSubqueryTableSource;
import com.alibaba.druid.sql.ast.statement.SQLTableSource;
import com.alibaba.druid.sql.ast.statement.SQLUnionQuery;
import com.alibaba.druid.sql.dialect.postgresql.ast.stmt.PGDeleteStatement;
import com.alibaba.druid.sql.dialect.postgresql.ast.stmt.PGInsertStatement;
import com.alibaba.druid.sql.dialect.postgresql.ast.stmt.PGSelectQueryBlock;
import com.alibaba.druid.sql.dialect.postgresql.ast.stmt.PGUpdateStatement;
import com.alibaba.druid.sql.dialect.postgresql.parser.PGSQLStatementParser;
import com.alibaba.druid.sql.dialect.postgresql.visitor.PGASTVisitorAdapter;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.HashSet;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.springframework.util.ReflectionUtils;

/**
 * @IgnoreI18nFile
 */
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class SharedSqlInterceptor implements Interceptor {
    public static Set<String> tables = new HashSet<>();

    public static String generateSharedSql(String sql, String suffix) {
        PGSQLStatementParser parser = new PGSQLStatementParser(sql);
        SQLStatement statement = parser.parseStatement();
        statement.accept(new TableNameModifier(suffix, tables));
        String newSql = statement.toString();
        return newSql;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        BoundSql boundSql = statementHandler.getBoundSql();
        ParameterHandler params = statementHandler.getParameterHandler();
        Object paramObj = params.getParameterObject();
        Object sharedValue = null;
        if (paramObj instanceof Map) {
            @SuppressWarnings("rawtypes") Map paramMap = (Map) params.getParameterObject();
            if (paramMap.containsKey(SharedContants.SHARED_KEY)) {
                sharedValue = paramMap.get(SharedContants.SHARED_KEY);
            }
        }
        if (sharedValue != null) {
            String sql = boundSql.getSql();
            int index = SharedContants.getSharedIndex(sharedValue);
            String newSql = generateSharedSql(sql, "_" + index);
            Field field = BoundSql.class.getDeclaredField("sql");
            field.setAccessible(true);
            ReflectionUtils.setField(field, boundSql, newSql);
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        } else {
            return target;
        }
    }

    @Override
    public void setProperties(Properties properties) {
        String tablesStr = properties.getProperty("tables", "");
        String[] tableArray = tablesStr.split(",");
        for (String table : tableArray) {
            tables.add(table);
        }
    }

    private static class TableNameModifier extends PGASTVisitorAdapter {
        private String index;
        private Set<String> tables = new HashSet<>();

        public TableNameModifier(String index, Set<String> tables) {
            this.index = index;
            this.tables = tables;
        }

        @Override
        public boolean visit(PGSelectQueryBlock x) {
            if (x.getFrom() instanceof SQLExprTableSource) {
                SQLExprTableSource tableSource = (SQLExprTableSource) x.getFrom();
                appendIndexForSqlExprTableSource(tableSource);
            } else if (x.getFrom() instanceof SQLJoinTableSource) {
                SQLJoinTableSource tableSource = (SQLJoinTableSource) x.getFrom();
                appendIndexForSqlJoinTableSource(tableSource);
            }
            return true;
        }

        private void appendIndexForSqlJoinTableSource(SQLJoinTableSource tableSource) {
            tableSource.setLeft(newSQLTableSource(tableSource.getLeft()));
            tableSource.setRight(newSQLTableSource(tableSource.getRight()));
        }

        private void appendIndexForSqlExprTableSource(SQLExprTableSource tableSource) {
            if (tableSource.getExpr() instanceof SQLName) {
                String oldTableName = tableSource.getExpr().toString();

                //六度人脉定制
                if (oldTableName.equals("spread_r")) {
                    return;
                }
                if (!tables.contains(oldTableName)) {
                    throw new IllegalArgumentException("table=" + oldTableName + " 需配置分表");
                }
                String newTableName = oldTableName + index;
                tableSource.setExpr(newTableName);

                //六度人脉定制
                if (oldTableName.equals("spread_stat")) {
                    if (tableSource.getParent() != null && tableSource.getParent().getParent() != null) {
                        SQLObject sqlObject = tableSource.getParent().getParent();
                        if (sqlObject instanceof SQLUnionQuery) {
                            SQLUnionQuery sqlUnionQuery = (SQLUnionQuery) sqlObject;
                            SQLSelectQueryBlock sqlSelectQueryBlock = (SQLSelectQueryBlock) sqlUnionQuery.getRight();
                            SQLJoinTableSource sqlJoinTableSource = (SQLJoinTableSource) sqlSelectQueryBlock.getFrom();
                            SQLExprTableSource sqlExprTableSource = (SQLExprTableSource) sqlJoinTableSource.getLeft();
                            if (sqlExprTableSource.getExpr().toString().equals("spread_stat")) {
                                sqlExprTableSource.setExpr(newTableName);
                            }
                        }
                    }
                }
            }
        }

        private SQLTableSource newSQLTableSource(SQLTableSource tableSource) {
            if (tableSource instanceof SQLExprTableSource) {
                String tableName = tableSource.toString();
                String alias = tableSource.getAlias();
                String newTableName = tableName;
                if (tables.contains(tableName)) {
                    newTableName = tableName + index;
                }
                SQLExprTableSource source = new SQLExprTableSource();
                source.setExpr(newTableName);
                source.setAlias(alias);
                return source;
            } else if (tableSource instanceof SQLSubqueryTableSource) {
                return tableSource;
            }
            throw new IllegalArgumentException("不支持该sql格式分表");
        }

        @Override
        public boolean visit(PGInsertStatement x) {
            SQLName sqlName = x.getTableName();
            String tableName = sqlName.getSimpleName() + index;
            x.setTableSource(new SQLIdentifierExpr(tableName));
            return true;
        }

        @Override
        public boolean visit(PGUpdateStatement x) {
            SQLName sqlName = x.getTableName();
            String tableName = sqlName.getSimpleName() + index;
            x.setTableSource(new SQLIdentifierExpr(tableName));
            return true;
        }

        @Override
        public boolean visit(PGDeleteStatement x) {
            SQLName sqlName = x.getTableName();
            String tableName = sqlName.getSimpleName() + index;
            x.setTableSource(new SQLIdentifierExpr(tableName));
            return true;
        }
    }
}
