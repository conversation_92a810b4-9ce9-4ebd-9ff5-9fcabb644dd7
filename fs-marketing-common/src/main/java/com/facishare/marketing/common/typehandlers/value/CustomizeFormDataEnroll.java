package com.facishare.marketing.common.typehandlers.value;

import com.facishare.marketing.common.contstant.CustomizeFormDataConstants;
import com.facishare.marketing.common.dto.CrmLeadSaveDTO;
import com.facishare.marketing.common.enums.leads.LeadMarketingSourceNameEnum;
import com.facishare.marketing.common.enums.leads.LeadMarketingSourceTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2019/04/09
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CustomizeFormDataEnroll extends CrmLeadSaveDTO implements Serializable {

    // 姓名
    private String name;

    // 手机
    private String phone;

    // 密码
    private String password;

    // 邮箱
    private String email;

    // 公司名
    private String companyName;

    // 职务
    private String position;

    // 身份证
    private String identityCard;

    // 文本一
    private String text1;

    // 文本二
    private String text2;

    // 文本三
    private String text3;

    // 文本四
    private String text4;

    // 文本五
    private String text5;

    // 单行文本参数(新版表单 key text5开头
    @ApiModelProperty("单行文本参数 key : apiName, value : 对应值")
    private Map<String, String> singleLineText;

    // 多行文本1
    private String text6;

    // 多行文本参数(新版表单) key text6开头
    @ApiModelProperty("多行文本参数 key : apiName, value : 对应值")
    private Map<String, String> multilineText;

    // 单选1
    private String text7;

    // 单选2
    private String text7A;

    // 单选3
    private String text7B;

    // 单选4
    private String text7C;

    // 单选5
    private String text7D;

    // 单选参数(新版表单) key text7 开头
    @ApiModelProperty("单选参数 key : apiName, value : 对应值")
    private Map<String, String> singleChoice;

    // 数值1
    private BigDecimal num1;

    // 数值映射(新版表单) key num1开头
    @ApiModelProperty("数值映射 key : apiName, value : 对应值")
    private Map<String, BigDecimal> num1Map;

    // 时间
    private BigDecimal num2;

    // 时间映射(新版表单) key num2 开头
    @ApiModelProperty("时间映射 key : apiName, value : 对应值")
    private Map<String, BigDecimal> timeMap;

    // 多选1
    private List<String> texts1;

    // 多选2
    private List<String> texts1A;

    // 多选3
    private List<String> texts1B;

    // 多选4
    private List<String> texts1C;

    // 多选5
    private List<String> texts1D;

    // 多选(新版表单) key texts1开头
    @ApiModelProperty("多选映射 key : apiName, value : 对应值列表")
    private Map<String, List<String>> multipleChoice;

    @ApiModelProperty("总支付金额")
    private Integer totalFee;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品金额")
    private Long amount;

    @ApiModelProperty("设备信息")
    private String deviceInfo;

    private String realIp;

    // 国家
    private String country;

    // 省
    private String province;

    // 市
    private String city;

    // 区
    private String district;

    // 详细地址
    private String address;

    @ApiModelProperty("图片映射 key : apiName, value : 对应图片列表数据")
    private Map<String, List<PicContainer>> picMap;

    @ApiModelProperty("文件映射 key : apiName, value : 对应文件列表数据")
    private Map<String, List<FileAttachmentContainer>> fileAttachmentMap;

    /**
     * fs 官网特殊值
     */
    @ApiModelProperty("用户状态")
    private String userStatus;

    @ApiModelProperty("推广方式")
    private String utmMedium;

    @ApiModelProperty("推广渠道")
    private String utmSource;

    @ApiModelProperty("推广计划")
    private String utmCampaig;

    @ApiModelProperty("广告用户id")
    private String accountId;

    @ApiModelProperty("推广计划id")
    private String unitId;

    @ApiModelProperty("推广单元")
    private String utmContent;

    @ApiModelProperty("关键词")
    private String utmTerm;

    @ApiModelProperty("搜索词")
    private String searchKeyword;

    @ApiModelProperty("广告平台的关键词的id")
    private String keywordId;

    @ApiModelProperty("神策用户id")
    private String sensorsUserId;

    /**
     * {@link LeadMarketingSourceNameEnum }
     */
    @ApiModelProperty("来源渠道(对应crm来源渠道)")
    public String marketingSourceName;

    /**
     * {@link LeadMarketingSourceTypeEnum}
     */
    @ApiModelProperty("来源类型(对应crm来源类型)")
    private String marketingSourceType;

    @ApiModelProperty("来源站点")
    private String marketingSourceSite;

    @ApiModelProperty("表单查询类型 1 单表单查询 2 多表单查询")
    private Integer customizeFormSearchType;
    @ApiModelProperty("手机校验码")
    private String phoneVerifyCode;
    @ApiModelProperty("附加参数")
    private Map<String, Object> functionAdditionalMsg;
    @ApiModelProperty("加密后的手机数据")
    private String encryptedPhoneData;

    @ApiModelProperty("加密后的手机IV")
    private String phoneIv;

    @ApiModelProperty("userAgent")
    private String userAgent;

    @ApiModelProperty("ip地址")
    private String ipAddr;

    @ApiModelProperty("官网埋点EA")
    private String fixEa;

    @ApiModelProperty("第一次分享物料id")
    private String sourceObjectId;

    @ApiModelProperty("第一次分享物料类型")
    private Integer sourceObjectType;

    @ApiModelProperty("推广人fsUserId")
    private Integer spreadFsUid;

    @ApiModelProperty("营销推广来源对象ID")
    private String marketingPromotionSourceId;

    @ApiModelProperty("第三方平台类型")
    private Integer thirdPlatformType;

    @ApiModelProperty("第三方平台用户id")
    private String thirdPlatformUserId;

    @ApiModelProperty("多选的省市区 从第二个开始")
    private Map<String, String> regionMap;

    // 表单提交时，是否强制同步将UTM创建对应的市场活动
    private boolean forceSynUtm = false;

    @Override
    public Object getFieldValueByName(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return null;
        }
        if (fieldName.equals("name")) {
            return this.getName();
        } else if (fieldName.equals("phone")) {
            return this.getPhone();
        } else if (fieldName.equals("email")) {
            return this.getEmail();
        } else if (fieldName.equals("companyName")) {
            return this.getCompanyName();
        } else if (fieldName.equals("position")) {
            return this.getPosition();
        } else if (fieldName.equals("text1")) {
            return this.getText1();
        } else if (fieldName.equals("text2")) {
            return this.getText2();
        } else if (fieldName.equals("text3")) {
            return this.getText3();
        } else if (fieldName.equals("text4")) {
            return this.getText4();
        } else if (fieldName.equals("text5")) {
            return this.getText5();
        } else if (fieldName.equals("text6")) {
            return this.getText6();
        } else if (fieldName.equals("num1")) {
            return this.getNum1();
        } else if (fieldName.equals("num2")) {
            return this.getNum2();
        } else if (fieldName.equals("country")) {
            return this.getCountry();
        } else if (fieldName.equals("province")) {
            return this.getProvince();
        } else if (fieldName.equals("city")) {
            return this.getCity();
        } else if (fieldName.equals("district")) {
            return this.getDistrict();
        } else if(fieldName.equals("identityCard")) {
            return this.getIdentityCard();
        } else if (fieldName.equals("address")) {
            return this.getAddress();
        } else if (fieldName.contains(CustomizeFormDataConstants.SINGLE_LINE_TEXT_KEY)) {
            // 单行文本
            if (MapUtils.isEmpty(this.getSingleLineText())) {
                return null;
            }
            return this.getSingleLineText().get(fieldName);
        } else if (fieldName.contains(CustomizeFormDataConstants.MULTI_LINE_TEXT_KEY)) {
            // 多行文本
            if (MapUtils.isEmpty(this.getMultilineText())) {
                return null;
            }
            return this.getMultilineText().get(fieldName);
        } else if (fieldName.contains(CustomizeFormDataConstants.NUM_KEY)) {
            // 数值
            if (MapUtils.isEmpty(this.getNum1Map())) {
                return null;
            }
            return this.getNum1Map().get(fieldName);
        } else if (fieldName.contains(CustomizeFormDataConstants.TIME_KEY)) {
            // 时间
            if (MapUtils.isEmpty(this.getTimeMap())) {
                return null;
            }
            return this.getTimeMap().get(fieldName);
        } else if (fieldName.contains(CustomizeFormDataConstants.PIC_KEY)) {
            // 图片
            if (MapUtils.isEmpty(this.getPicMap())) {
                return null;
            }
            return this.getPicMap().get(fieldName);
        } else if(fieldName.contains(CustomizeFormDataConstants.FILE_ATTACHMENT_KEY)) {
            //附件
            if (MapUtils.isEmpty(this.getFileAttachmentMap())) {
                return null;
            }
            return this.getFileAttachmentMap().get(fieldName);
        }else if (fieldName.equals("userAgent")){
            return this.getUserAgent();
        }else if (fieldName.equals("ipAddr")){
            return this.getIpAddr();
        } else if (fieldName.contains(CustomizeFormDataConstants.REGION_COUNTRY) || fieldName.contains(CustomizeFormDataConstants.REGION_PROVINCE)
                || fieldName.contains(CustomizeFormDataConstants.REGION_CITY) || fieldName.contains(CustomizeFormDataConstants.REGION_DISTRICT)
                || fieldName.contains(CustomizeFormDataConstants.REGION_ADDRESS)) {
            //多选省市区
            if (MapUtils.isEmpty(this.getRegionMap())) {
                return null;
            }
            return this.getRegionMap().get(fieldName);
        }
        return null;
    }

    public void setByFieldName(String fieldName, Object value) {
        if (StringUtils.isBlank(fieldName) || StringUtils.isBlank(value.toString())) {
            return;
        }
        if (fieldName.equals("name")) {
            this.setName(value.toString());
        } else if (fieldName.equals("phone")) {
            this.setPhone(value.toString());
        } else if (fieldName.equals("email")) {
            this.setEmail(value.toString());
        } else if (fieldName.equals("companyName")) {
            this.setCompanyName(value.toString());
        } else if (fieldName.equals("position")) {
            this.setPosition(value.toString());
        } else if (fieldName.equals("text1")) {
            this.setText1(value.toString());
        } else if (fieldName.equals("text2")) {
            this.setText2(value.toString());
        } else if (fieldName.equals("text3")) {
            this.setText3(value.toString());
        } else if (fieldName.equals("text4")) {
            this.setText4(value.toString());
        } else if (fieldName.equals("text5")) {
            this.setText5(value.toString());
        } else if (fieldName.equals("text6")) {
            this.setText6(value.toString());
        } else if (fieldName.equals("text7")) {
            this.setText7(value.toString());
        } else if (fieldName.equals("text7A")) {
            this.setText7A(value.toString());
        } else if (fieldName.equals("text7B")) {
            this.setText7B(value.toString());
        } else if (fieldName.equals("text7C")) {
            this.setText7C(value.toString());
        } else if (fieldName.equals("text7D")) {
            this.setText7D(value.toString());
        } else if (fieldName.equals("num1")) {
            this.setNum1((BigDecimal) value);
        } else if (fieldName.equals("num2")) {
            this.setNum2((BigDecimal) value);
        } else if (fieldName.equals("texts1")) {
            this.setTexts1((List<String>) value);
        } else if (fieldName.equals("texts1A")) {
            this.setTexts1A((List<String>) value);
        } else if (fieldName.equals("texts1B")) {
            this.setTexts1B((List<String>) value);
        } else if (fieldName.equals("texts1C")) {
            this.setTexts1C((List<String>) value);
        } else if (fieldName.equals("texts1D")) {
            this.setTexts1D((List<String>) value);
        } else if (fieldName.equals("country")) {
            this.setCountry(value.toString());
        } else if (fieldName.equals("province")) {
            this.setProvince(value.toString());
        } else if (fieldName.equals("city")) {
            this.setCity(value.toString());
        } else if (fieldName.equals("district")) {
            this.setDistrict(value.toString());
        } else if (fieldName.equals("address")) {
            this.setAddress(value.toString());
        } else if (fieldName.equals("identityCard")) {
            this.setIdentityCard(value.toString());
        } else if(fieldName.contains(CustomizeFormDataConstants.SINGLE_LINE_TEXT_KEY)) {
            // 单行文本
            if (this.getSingleLineText() == null) {
                this.setSingleLineText(Maps.newHashMap());
            }
            this.getSingleLineText().put(fieldName, value.toString());
        }  else if (fieldName.contains(CustomizeFormDataConstants.MULTI_LINE_TEXT_KEY)) {
            // 多行文本
            if (this.getMultilineText() == null) {
                this.setMultilineText(Maps.newHashMap());
            }
            this.getMultilineText().put(fieldName, value.toString());
        } else if (fieldName.contains(CustomizeFormDataConstants.SINGLE_CHOICE_KEY)) {
            // 单选
            if (this.getSingleChoice() == null) {
                this.setSingleChoice(Maps.newHashMap());
            }
            this.getSingleChoice().put(fieldName, value.toString());
        } else if (fieldName.contains(CustomizeFormDataConstants.NUM_KEY)) {
            // 数值
            if (this.getNum1Map() == null) {
                this.setNum1Map(Maps.newHashMap());
            }
            this.getNum1Map().put(fieldName, (BigDecimal) value);
        } else if (fieldName.contains(CustomizeFormDataConstants.TIME_KEY)) {
            // 时间
            if (this.getTimeMap() == null) {
                this.setTimeMap(Maps.newHashMap());
            }
            this.getTimeMap().put(fieldName, (BigDecimal) value);
        } else if (fieldName.contains(CustomizeFormDataConstants.MULTIPLE_CHOICE_KEY)) {
            // 多选
            if (this.getMultipleChoice() == null) {
                this.setMultipleChoice(Maps.newHashMap());
            }
            this.getMultipleChoice().put(fieldName, (List<String>) value);
        } else if (fieldName.contains(CustomizeFormDataConstants.REGION_COUNTRY) || fieldName.contains(CustomizeFormDataConstants.REGION_PROVINCE)
                || fieldName.contains(CustomizeFormDataConstants.REGION_CITY) || fieldName.contains(CustomizeFormDataConstants.REGION_DISTRICT)
                || fieldName.contains(CustomizeFormDataConstants.REGION_ADDRESS)) {
            // 多选
            if (this.getRegionMap() == null) {
                this.setRegionMap(Maps.newHashMap());
            }
            this.getRegionMap().put(fieldName, value.toString());
        }
    }


    @Override
    public Object getRadioValueByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        if (name.equals("text7")) {
            return this.getText7();
        } else if (name.equals("text7A")) {
            return this.getText7A();
        } else if (name.equals("text7B")) {
            return this.getText7B();
        } else if (name.equals("text7C")) {
            return this.getText7C();
        } else if (name.equals("text7D")) {
            return this.getText7D();
        } else if (name.contains(CustomizeFormDataConstants.SINGLE_CHOICE_KEY)) {
            if (MapUtils.isEmpty(this.getSingleChoice())) {
                return null;
            }
            return this.getSingleChoice().get(name);
        }
        return null;
    }

    @Override
    public List<String> getMultipleSelectionValueByName(String name) {
        if (StringUtils.isBlank(name)) {
            return Lists.newArrayList();
        }
        if (name.equals("texts1")) {
            return this.getTexts1();
        } else if (name.equals("texts1A")) {
            return this.getTexts1A();
        } else if (name.equals("texts1B")) {
            return this.getTexts1B();
        } else if (name.equals("texts1C")) {
            return this.getTexts1C();
        } else if (name.equals("texts1D")) {
            return this.getTexts1D();
        } else if (name.contains(CustomizeFormDataConstants.MULTIPLE_CHOICE_KEY)) {
            if (MapUtils.isEmpty(this.getMultipleChoice())) {
                return Lists.newArrayList();
            }
            return this.getMultipleChoice().get(name);
        }
        return Lists.newArrayList();
    }
}
