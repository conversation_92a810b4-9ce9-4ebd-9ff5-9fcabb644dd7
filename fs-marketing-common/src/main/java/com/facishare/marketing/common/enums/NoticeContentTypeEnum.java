package com.facishare.marketing.common.enums;

/**
 * Created By tianh, 2018/7/2.
 **/
public enum NoticeContentTypeEnum {
    // 文章
    ARTICLE(1, "文章", "qx.ot.mark.article"),
    // 图片
    PICTURE(2, "图片", "qx.ot.mark.picture"),
    // 活动
    ACTIVITY(3, "活动", "qx.ot.mark.activity"),
    // 产品
    PRODUCT(4, "产品", "qx.ot.mark.product"),
    // 海报
    QR_POSTER(5, "海报", "qx.ot.mark.qr_poster"),
    //线索审批
    CLUE_CONFIRM(6, "线索审批", "qx.ot.mark.clue_confirm"),
    //分销人员审核
    DISTRIBUTOR_REVIEW(7, "分销人员审核", "qx.ot.mark.distributor_review"),
    //会议邀约通知
    CONFERENCE_INVITE(8, "参会人员邀约", "qx.ot.mark.conference_invite"),
    //会议报名审核通知
    CONFERENCE_ENROLL_REVIEW(9, "会议报名审核", "qx.ot.mark.conference_enroll_review"),
    //微页面
    HEXAGON(10, "微页面", "qx.ot.mark.hexagon"),
    // 表单
    CUSTOMIZE_FORM_DATA(11, "表单", "qx.ot.mark.customize_form_data"),
    // 保存线索失败通知
    SAVE_CRM_FAIL(12, "保存线索失败通知","qx.ot.mark.save_crm_fail"),
    //优惠券下发通知
    SEND_COUPON(13,"纷享优惠券下发通知","qx.ot.mark.send_coupon"),

    SEND_UNION_MSG(99,"互动通知", "qx.ot.mark.send_union_msg"),

    OUT_LINK(9999,"外部链接下发通知", "qx.ot.mark.out_link"),

    QYWX_FILE(500,"企微下发的文件", "qx.ot.mark.qywx_file"),

    QYWX_VIDEO(501,"企微下发的视频", "qx.ot.mark.qywx_video"),

    CARD_OPEN(502, "名片开通提醒", "qx.ot.mark.card_open"),
    EMAIL_MATERIAL(503, "邮件物料", "qx.ot.mark.email_material"),
    ;
    private int type;
    private String description;
    private String internationalKey;

    NoticeContentTypeEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    NoticeContentTypeEnum(int type, String description, String internationalKey) {
        this.type = type;
        this.description = description;
        this.internationalKey = internationalKey;
    }

    public static NoticeContentTypeEnum fromType(int type) {
        for (NoticeContentTypeEnum noticeContentTypeEnum : NoticeContentTypeEnum.values()) {
            if (noticeContentTypeEnum.getType() == type) {
                return noticeContentTypeEnum;
            }
        }
        throw new IllegalArgumentException();
    }

    public int getType() {
        return this.type;
    }

    public String getDescription() {
        return description;
    }

    public String getInternationalKey() {
        return internationalKey;
    }

    public int toObjectType() {
        switch (this) {
            case PRODUCT:
                return ObjectTypeEnum.PRODUCT.getType();
            case ARTICLE:
                return ObjectTypeEnum.ARTICLE.getType();
            case ACTIVITY:
                return ObjectTypeEnum.ACTIVITY.getType();
            case QR_POSTER:
                return ObjectTypeEnum.QR_POSTER.getType();
            case HEXAGON:
                return ObjectTypeEnum.HEXAGON_SITE.getType();
            case CUSTOMIZE_FORM_DATA:
                return ObjectTypeEnum.CUSTOMIZE_FORM.getType();
            case OUT_LINK:
                return ObjectTypeEnum.OUT_LINK.getType();
            case PICTURE:
                return ObjectTypeEnum.IMAGE.getType();
            case QYWX_FILE:
                return ObjectTypeEnum.FILE.getType();
            case QYWX_VIDEO:
                return ObjectTypeEnum.VIDEO.getType();
            case EMAIL_MATERIAL:
                return ObjectTypeEnum.EMAIL_MATERIAL.getType();
            default:
                throw new UnsupportedOperationException();
        }
    }

    public boolean equalsType(Integer type) {
        if (type == null) {
            return false;
        }
        if (this.type == type) {
            return true;
        }
        return false;
    }
}
