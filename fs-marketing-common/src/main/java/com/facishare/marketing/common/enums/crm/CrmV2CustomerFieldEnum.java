package com.facishare.marketing.common.enums.crm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum CrmV2CustomerFieldEnum {

    ID("_id", "_id", true),
    ObjectDescribeID("object_describe_id", "object_describe_id", true),
    ApiName("object_describe_api_name", "object_describe_api_name", true),
    Name("Name", "name", false),
    CustomerNo("CustomerNo", "account_no", false),
    Level("Level", "account_level", false),
    CustomerType("CustomerType", "account_type", false),
    Source("Source", "account_source", false),
    Address("Address", "location", false),
    Tel("Tel", "tel", false),
    Fax("Fax", "fax", false),
    Email("Email", "email", false),
    HouseNo("HouseNo", "address", false),
    URL("URL", "url", false),
    Remark("Remark", "remark", false),
    HighSeasID("HighSeasID", "high_seas_id", false),
    HighSeasName("HighSeasName", "high_seas_name", false),
    Industry("Industry", "industry_level1", false),
    SubIndustry("SubIndustry", "industry_level2", false),
    Country("Country", "country", false),
    Province("Province", "province", false),
    City("City", "city", false),
    District("District", "district", false),
    NameSpell("NameSpell", "pin_yin", false),
    Status("Status", "account_status", true),
    SaleActionStageID("SaleActionStageID", "sales_stage_ID", true),
    TradeTotalMoney("TradeTotalMoney", "total_order_amount", true),
    PaymentTotalMoney("PaymentTotalMoney", "total_payment_amount", true),
    RefundTotalMoney("RefundTotalMoney", "total_refund_amount", true),
    DealStatus("DealStatus", "deal_status", true),
    DealTime("DealTime", "last_deal_closed_time", true),
    OpportunityTotalMoney("OpportunityTotalMoney", "total_opportunity_revenue", true),
    LastTradeMoney("LastTradeMoney", "last_deal_closed_amount", true),
    OwnerID("OwnerID", "owner", true),
    OwnerChangedTime("OwnerChangedTime", "owner_modified_time", true),
    OldOwnerID("OldOwnerID", "last_owner", true),
    ClaimTime("ClaimTime", "claimed_time", true),
    ResaleCount("ResaleCount", "transfer_count", true),
    ExpireTime("ExpireTime", "expire_time", true),
    CreatorID("CreatorID", "created_by", true),
    CreateTime("CreateTime", "create_time", true),
    UpdateTime("UpdateTime", "last_modified_time", true),
    UpdatorID("UpdatorID", "last_modified_by", true),
    IsDeleted("IsDeleted", "is_deleted", true),
    JoinHighSeasTime("JoinHighSeasTime", "high_seas_allocation_time", true),
    TakeBackTime("TakeBackTime", "returned_time", true),
    LastFollowTime("LastFollowTime", "last_followed_time", true),
    FilingCheckerID("FilingCheckerID", "filling_checker_id", true),
    InputFieldNumber("InputFieldNumber", "completed_field_quantity", true),
    RecyclingReason("RecyclingReason", "recycled_reason", true),
    RemainingTime("RemainingTime", "remaining_time", true),
    CompletionRate("CompletionRate", "completion_rate", true),
    Circles("Circles", "owner_department", true),
    RecordType("RecordType", "record_type", true),
    CustomerID("CustomerID", "account_id", true),
    LockStatus("LockStatus", "lock_status", true),
    IsRemindRecycling("IsRemindRecycling", "is_remind_recycling", true),
    BackReason("BackReason", "back_reason", false),
    PartnerID("PartnerID", "partner_id", true),
    PartnerName("PartnerName", "partner_id__r", true),
    OutEI("OutEI", "out_tenant_id", true),
    OutOwnerID("OutOwnerID", "out_owner", true),
    OutResources("OutResources", "out_resources", true),
    DataOwnDepartment("DataOwnDepartment", "data_own_department", false),
    DataOwnDepartmentName("DataOwnDepartmentName", "data_own_department__r", false),
    APPLICANT_NAME("applicant_name", "applicant_name", false),
    ;

    private String oldFieldName;
    private String newFieldName;
    private Boolean needFilter;

    public static String getNewFieldName(String oldFieldName) {
        for (CrmV2CustomerFieldEnum code : values()) {
            if (code.getOldFieldName().equals(oldFieldName)) {
                return code.getNewFieldName();
            }
        }

        return null;
    }

    public static Boolean needFilter(String newFieldName) {
        for (CrmV2CustomerFieldEnum code : values()) {
            if (code.getNewFieldName().equals(newFieldName)) {
                return code.getNeedFilter();
            }
        }

        return null;
    }

}
