/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.common.util.threadpool;

import com.alibaba.dubbo.rpc.RpcContext;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import java.util.Map;
import java.util.concurrent.*;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ThreadPoolUtils {

    private static Map<ThreadPoolTypeEnums, ExecutorService> executorServiceMap = Maps.newHashMap();

    static {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        log.info("ThreadPoolUtils availableProcessors：{}", availableProcessors);
        for (ThreadPoolTypeEnums threadPoolTypeEnums : ThreadPoolTypeEnums.values()) {
            if (threadPoolTypeEnums == ThreadPoolTypeEnums.MARKETING_ATTRIBUTE || threadPoolTypeEnums == ThreadPoolTypeEnums.WHATS_APP || threadPoolTypeEnums == ThreadPoolTypeEnums.ASYNC_ADD_TAGS_TO_OBJ) {
                // 营销归因最多四个线程，因为要查询逻辑很重，避免给pass造成压力  whatsapp用户少，也不需要这么多线程
                ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 4, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new NamedThreadFactory("pool_marketing_" + threadPoolTypeEnums.getDesc()));
                executorServiceMap.put(threadPoolTypeEnums, threadPoolExecutor);
            } else if (threadPoolTypeEnums == ThreadPoolTypeEnums.REFRESH_AD_DATA) {
                ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 1, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new NamedThreadFactory("pool_marketing_" + threadPoolTypeEnums.getDesc()));
                executorServiceMap.put(threadPoolTypeEnums, threadPoolExecutor);
            }
            else {
                executorServiceMap.put(threadPoolTypeEnums, NamedThreadPool.newFixedThreadPool(availableProcessors * 4, "marketing_" + threadPoolTypeEnums.getDesc()));
            }
        }
    }

    //private static ExecutorService service = NamedThreadPool.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 4, "marketing");

    public static void execute(Runnable command, ThreadPoolTypeEnums threadPoolTypeEnums) {
        //executorServiceMap.get(threadPoolTypeEnums).execute(command);
        // 全部换成带有trace上下文的
        executeWithTraceContext(command, threadPoolTypeEnums);
    }

    public static Future<?> submit(Runnable command, ThreadPoolTypeEnums threadPoolTypeEnums) {
        //return executorServiceMap.get(threadPoolTypeEnums).submit(command);
        // 全部换成带有trace上下文的
        return submitWithTraceContext(command, threadPoolTypeEnums);
    }

   /* public static <T> Future<T> submit(Callable command) {
        return service.submit(command);
    }*/

    public static ExecutorService getService(ThreadPoolTypeEnums threadPoolTypeEnum) {
        return executorServiceMap.get(threadPoolTypeEnum);
    }

    public static void executeWithNewThread(Runnable command) {
        TraceContext context = TraceContext.get();
        String sourceProfile = RpcContext.getContext().getAttachment("sourceProfile");
        new Thread(() -> {
            if (context != null ) {
                TraceContext._set(context);
            }
            if (StringUtils.isNotEmpty(sourceProfile)) {
                RpcContext.getContext().set("sourceProfile", sourceProfile);
            }
            try {
                command.run();
            } finally {
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        }).start();
    }

    public static void executeWithNewThread(String threadName, Runnable command) {
        TraceContext context = TraceContext.get();
        String sourceProfile = RpcContext.getContext().getAttachment("sourceProfile");
        Thread thread = new Thread(() -> {
            if (context != null ) {
                TraceContext._set(context);
            }
            if (StringUtils.isNotEmpty(sourceProfile)) {
                RpcContext.getContext().set("sourceProfile", sourceProfile);
            }
            try {
                command.run();
            } catch (Exception e) {
                log.warn("executeWithNewThread error", e);
            } finally {
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });
        thread.setName(threadName);
        thread.start();
    }

    public static void main(String[] args) throws InterruptedException {
        ThreadPoolUtils.executeWithNewThread("test", () -> {
            System.out.println(Thread.currentThread().getName() + "hello");
        });
        Thread.sleep(1000L);
    }
    /**
     * 会把traceContext往下传
     */
    public static void executeWithTraceContext(Runnable command, ThreadPoolTypeEnums threadPoolTypeEnums) {
        TraceContext context = TraceContext.get();
        String sourceProfile = RpcContext.getContext().getAttachment("sourceProfile");
        executorServiceMap.get(threadPoolTypeEnums).execute(() -> {
            if (context != null ) {
                TraceContext._set(context);
            }
            if (StringUtils.isNotEmpty(sourceProfile)) {
                RpcContext.getContext().set("sourceProfile", sourceProfile);
            }
            long beginTime = System.currentTimeMillis();
            try {
                command.run();
            } catch (Exception e) {
                log.warn("executeWithTraceContext error happen, threadPoolTypeEnums: {}", threadPoolTypeEnums, e);
            } finally {
                if (context != null ) {
                    TraceContext.remove();
                }
            }
            long spendTime = System.currentTimeMillis() - beginTime;
            if (spendTime >= 1000) {
                log.info("异步线程池执行耗时: {} ms, ThreadPoolTypeEnums : {}", spendTime, threadPoolTypeEnums);
            }
        });
    }

    /**
     * 会把traceContext往下传
     */
    public static Future<?> submitWithTraceContext(Runnable command, ThreadPoolTypeEnums threadPoolTypeEnums) {
        TraceContext context = TraceContext.get();
        String sourceProfile = RpcContext.getContext().getAttachment("sourceProfile");
        return executorServiceMap.get(threadPoolTypeEnums).submit(() -> {
            if (context != null ) {
                TraceContext._set(context);
            }
            if (StringUtils.isNotEmpty(sourceProfile)) {
                RpcContext.getContext().set("sourceProfile", sourceProfile);
            }
            long beginTime = System.currentTimeMillis();
            try {
                command.run();
            } finally {
                if (context != null ) {
                    TraceContext.remove();
                }
            }
            long spendTime = System.currentTimeMillis() - beginTime;
            if (spendTime >= 1000) {
                log.info("异步线程池执行耗时: {} ms, ThreadPoolTypeEnums : {}", spendTime, threadPoolTypeEnums);
            }
        });
    }

    @Getter
    @AllArgsConstructor
    public enum ThreadPoolTypeEnums {

        /**
         * 适用于调用链路较长，耗时较长的业务, 离线统计业务一定要用该类型
         */
        HEAVY_BUSINESS(0, "heavy"),

        /**
         * 中型业务调用链路较长，但要求尽快处理且不允许丢弃的业务（消息发送，短信，邮件发送等）
         */
        MEDIUM_BUSINESS(1, "medium"),

        /**
         * 适用于调用链路较短，耗时较短业务（实时返回）
         */
        LIGHT_BUSINESS(2, "light"),

        /**
         * 通讯录专用（大批量短时线程）
         */
        QYWX_ADDRESS_BOOK(3, "qywxAddressBook"),
        /**
         * 通讯录专用（大批量短时线程）
         */
        SOP_QYWX_MSG(4, "sopQywxMsg"),

        /**
         * 企业微信回调事件处理
         */
        QYWX_CALLBACK_HANDLE(5, "qywxCallbackHandle"),

        /**
         * 全员推广
         */
        SEND_NOTCIE(6, "send_notice"),

        /**
         * 企业微信群发（消息群发+朋友圈）
         */
        QYWX_MSG_SEND(7, "qywx_msg_send"),

        /**
         * 欢迎语专用线程池
         */
        CUSTOMER_WELCOME_MESSAGE(8, "CustomerWelcomeMessage"),

        AD(9, "ad"),

        MARKETING_ATTRIBUTE(10, "MARKETING_ATTRIBUTE"),

        WHATS_APP(11, "WHATS_APP"),

        REFRESH_AD_DATA(12, "REFRESH_AD_DATA"),

        HANDLE_COUPON_RECEIVE(13, "HANDLE_COUPON_RECEIVE"),
        ASYNC_ADD_TAGS_TO_OBJ(14, "ASYNC_ADD_TAGS_TO_OBJ"),

        QYWX_DATA_INIT(16, "QYWX_DATA_INIT"),
        ;

        private Integer type;

        private String desc;
    }

}