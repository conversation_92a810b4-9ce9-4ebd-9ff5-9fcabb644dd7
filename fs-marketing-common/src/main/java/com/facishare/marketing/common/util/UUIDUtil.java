package com.facishare.marketing.common.util;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;

import java.net.SocketPermission;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: Smallfan
 * @Date: created in 2018/4/16 16:35
 * @Description:
 */
public class UUIDUtil {
    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String get24UUID() {
        String[] uid = UUID.randomUUID().toString().split("-");
        return uid[0] + uid[1] +uid[4];
    }

    public static Long getLongUUID() {
        Integer uuid = UUID.randomUUID().toString().replace("-", "").hashCode();
        return Long.valueOf(uuid = uuid < 0 ? -uuid : uuid);
    }

    public static void main(String[] args) {
        System.out.println(UUIDUtil.getUUID());
        System.out.println(UUIDUtil.get24UUID());
        System.out.println(getLongUUID());
        System.out.println("ms:"+System.currentTimeMillis());
    }

    public static String generateUID(int len){
        Random random = new Random();
        String value = "";
        for (int i =0; i < len; i++){
            value += (random.nextInt(9) + 1);
        }

        return value;
    }


    public static void getEa(){
        String ea = null;
        String url = "https://a2.ceshi112.com/image/40030079_sandbox/C_202311_22_ca299b1232cd4a8bb6c8faa20b58357c"; //从这个url中去除88146字符串

      //  String url = "http://a9.fspage.com/image/extract?ea=40030079_sandbox&path=C_202310_07_af454de70a234872abaffefa0aa36cda&left=0&top=311&width=750&height=416";
        if (url.contains("image/extract?")){
            ea =url.substring(url.indexOf("ea=") + 3, url.indexOf("&"));
        }else if (url.contains("image/")){
            String subUrl = url.substring(url.indexOf("image/") + 6);
            ea = subUrl.substring(0, subUrl.indexOf("/"));
            System.out.println("subUrl:"+subUrl);
    //        ea = url.substring(url.indexOf("image/") + 6, url.indexOf("/"));
        }
        System.out.println("this ea:"+ea);

    }
}
