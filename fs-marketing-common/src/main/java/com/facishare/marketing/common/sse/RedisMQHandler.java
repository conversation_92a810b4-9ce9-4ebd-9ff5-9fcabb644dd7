package com.facishare.marketing.common.sse;

import com.alibaba.fastjson.JSONObject;
import com.facishare.common.UidUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import redis.clients.jedis.params.SetParams;

import java.util.concurrent.TimeUnit;

@Slf4j
public class RedisMQHandler {

    private static final long POLL_INTERVAL = 200L;
    public static final long MAX_RETRY_TIME = 300_000L;

    public static SseEmitter onMessage(MergeJedisCmd jedisCmd, String channel) {
        log.info("processChannel start: channel={}", channel);
        SseEmitter sseEmitter = new SseEmitter(RedisMQHandler.MAX_RETRY_TIME);
        String key = RedisMQ.MSG_PRE + channel;
        if (!jedisCmd.exists(key)) {
            sseEmitter.complete();
        }
        if (processLock(jedisCmd, channel)) {
            ThreadPoolUtils.executeWithNewThread("onMessage", () -> {
                try {
                    process(jedisCmd, channel, sseEmitter);
                } finally {
                    try {
                        sseEmitter.complete();
                    } catch (Exception e) {
                        log.warn("关闭SSE连接失败: channel={}", channel, e);
                    }
                    processUnLock(jedisCmd, channel);
                }
            });
        } else {
            sseEmitter.complete();
        }
        return sseEmitter;
    }

    private static void process(MergeJedisCmd jedisCmd, String channel, SseEmitter sseEmitter) {
        long startTime = System.currentTimeMillis();
        String key = RedisMQ.MSG_PRE + channel;
        while (System.currentTimeMillis() - startTime < MAX_RETRY_TIME) {
            try {
                String message = jedisCmd.rpop(key);
                if (message != null) {
                    JSONObject msgObj = JSONObject.parseObject(message);
                    if (msgObj != null) {
                        log.info("processChannel msg: channel={}, message={}", channel, msgObj);
                        sseEmitter.send(msgObj, MediaType.APPLICATION_JSON);
                        if (msgObj.getBoolean("finish")) {
                            break;
                        }
                    }
                } else {
                    TimeUnit.MILLISECONDS.sleep(POLL_INTERVAL);
                }
            } catch (Exception e) {
                log.error("处理消息失败: channel={}, error={}", channel, e.getMessage(), e);
            }
        }
    }

    public static boolean processLock(MergeJedisCmd mergeJedisCmd, String channel) {
        String lockKey = RedisMQ.MSG_PRE + "lock:" + channel;
        // 尝试获取锁，设置过期时间为MAX_RETRY_TIME毫秒
        String result = mergeJedisCmd.set(lockKey, UidUtil.getUid(), new SetParams().nx().px(MAX_RETRY_TIME));
        return result != null && result.equals("OK");
    }

    public static void processUnLock(MergeJedisCmd mergeJedisCmd, String channel) {
        String lockKey = RedisMQ.MSG_PRE + "lock:" + channel;
        mergeJedisCmd.del(lockKey);
    }
}
