package com.facishare.marketing.api.service.qywx;

import com.facishare.marketing.api.arg.UpdateShareCardUrl;
import com.facishare.marketing.api.arg.qywx.card.AddCardInfoArg;
import com.facishare.marketing.api.arg.qywx.card.QueryCardArg;
import com.facishare.marketing.api.arg.qywx.card.QueryCardHolderInfoVO;
import com.facishare.marketing.api.result.qywx.card.*;
import com.facishare.marketing.api.vo.UpdateEnterpriseDefaultProductVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

import java.util.List;

public interface CardService {

    Result<CardInfoResult> queryMyBaseCardInfo(QueryCardArg arg);

    Result<CardInfoResult> queryMyBaseCardInfoWithFsInfo(String ea, Integer fsUserId);

    Result<CardInfoResult> queryBaseCardInfo(QueryCardArg arg);

    Result<QueryProductsAndFeedResult> queryProductsAndFeed(QueryCardArg arg);

    Result<QueryPhotoListResult> queryPhotoList(QueryCardArg arg);

    Result<QueryCardResult> queryCardInfo(QueryCardArg arg);

    Result<AddCardResult> addCardInfo(AddCardInfoArg arg);

    Result<TradeListResult> queryTradeList();

    Result<PageResult<QueryCardHolderInfoResult>> queryCardHolderInfo(QueryCardHolderInfoVO vo);

    Result updateEnterpriseDefaultProduct(UpdateEnterpriseDefaultProductVO vo);

    Result<List<String>> changePhotosType(List<String> photos,String ea);

    Result updateShareCardUrl(UpdateShareCardUrl arg);
}
