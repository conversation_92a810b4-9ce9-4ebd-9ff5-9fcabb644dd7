package com.facishare.marketing.api.arg.sms;

import com.facishare.marketing.common.enums.I18nKeyEnumV3;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.vo.BaseVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/8/6
 * @Desc
 * @IgnoreI18nFile
 **/
@Data
public class GetSmsParamListV2Arg extends BaseVO {

    @ApiModelProperty(value = "市场活动id")
    private String marketingEventId;

    @Override
    public Result checkParam() {
        if (StringUtils.isBlank(marketingEventId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnumV3.MARK_SMS_GETSMSPARAMLISTV2ARG_23));
        }
        return super.checkParam();
    }
}