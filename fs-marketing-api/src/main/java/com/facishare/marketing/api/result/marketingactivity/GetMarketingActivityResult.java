package com.facishare.marketing.api.result.marketingactivity;

import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.result.mail.MailGroupSendMessageDetailResult;
import com.facishare.marketing.api.result.qywx.QywxGroupSendMessageDetailResult;
import com.facishare.marketing.api.result.qywx.QywxMomentDetailResult;
import com.facishare.marketing.api.vo.*;
import com.facishare.marketing.api.vo.qywx.MomentMessageVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupSendMessageVO;
import com.facishare.marketing.api.vo.whatsapp.WhatsAppMarketingActivityDetailVO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/25.
 */
@Data
@ToString
public class GetMarketingActivityResult  implements Serializable {
    private static final long serialVersionUID = -3860581945032111219L;
    @ApiModelProperty("推广ID")
    private String id;
    @ApiModelProperty("市场活动ID")
    private String marketingEventId;
    @ApiModelProperty("市场活动类型")
    private String eventType;
    @ApiModelProperty("市场活动名称")
    private String marketingEventName;
    @ApiModelProperty("推广名称")
    private String name;
    @ApiModelProperty("推广类型")
    private Integer spreadType;
    @ApiModelProperty("推广状态")
    private Integer status;
    @ApiModelProperty("创建者id")
    private String createById;
    @ApiModelProperty("创建者名称")
    private String createByName;
    @ApiModelProperty("推广封面url")
    private String coverUrl;
    @ApiModelProperty("推广封面apath")
    private String coverApath;
    @ApiModelProperty(value = "微信消息类型")
    private Integer wechatMessageType;
    @ApiModelProperty("微信营销活动")
    private WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO;
    @ApiModelProperty("全员营销详情")
    private MarketingActivityNoticeSendVO marketingActivityNoticeSendVO;
    @ApiModelProperty("伙伴营销详情")
    private MarketingActivityPartnerNoticeSendVO marketingActivityPartnerNoticeSendVO;
    @ApiModelProperty("短信营销详情")
    private MarketingActivityGroupSenderDeatilVO marketingActivityGroupSenderDeatilVO;
    @ApiModelProperty(value = "微信模版消息Data")
    private WeChatTemplateMessageData weChatTemplateMessageVO;
    @ApiModelProperty("企业微信群发消息详情")
    private QywxGroupSendMessageDetailResult qywxGroupSendMessageDetailResult;
    @ApiModelProperty("邮件群发消息详情")
    private MailGroupSendMessageDetailResult mailGroupSendMessageDetailResult;
    @ApiModelProperty("企业微信朋友圈消息详情")
    private QywxMomentDetailResult qywxMomentDetailResult;
    @ApiModelProperty(value = "企业微信群发消息配置")
    private QywxGroupSendMessageVO qywxGroupSendMessageVO;
    @ApiModelProperty("企业微信朋友圈消息详情配置")
    private MomentMessageVO momentMessageVO;
    @ApiModelProperty("自定义推广详情")
    private CustomizedSpreadDetailResult customizedSpreadDetailResult;
    @ApiModelProperty("人数")
    private Integer uv;
    @ApiModelProperty("未推广员工人数")
    private Integer uuv;
    @ApiModelProperty("次数")
    private Integer pv;
    @ApiModelProperty("线索数")
    private Integer leadCount;
    @ApiModelProperty("是否可以取消发送")
    private Boolean sendCancelable;

    @ApiModelProperty("推广状态")
    private Integer spreadStatus;
    @ApiModelProperty("是否可以撤回推广")
    private Boolean sendRevocable;

    @ApiModelProperty(value = "发送错误原因，当status为失败时才有")
    private String reason;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty("whatsapp营销")
    WhatsAppMarketingActivityDetailVO whatsAppMarketingActivityDetailVO;
}