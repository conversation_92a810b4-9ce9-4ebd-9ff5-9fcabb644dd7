package com.facishare.marketing.api.arg.usermarketingaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OutQueryUserMarketingActionArg implements Serializable {

    @ApiModelProperty(value = "tenantId", hidden = true)
    private Integer tenantId;

    @ApiModelProperty(value = "fsUserId", hidden = true)
    private Integer fsUserId;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "对象apiName")
    private String objectApiName;

    @ApiModelProperty(value = "对象id")
    private String objectId;

    @ApiModelProperty(value = "营销用户id, 对象id传入二选一")
    private String userMarketingId;

}
