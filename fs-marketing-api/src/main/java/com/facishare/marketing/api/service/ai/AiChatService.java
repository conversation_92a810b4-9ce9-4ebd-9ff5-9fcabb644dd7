package com.facishare.marketing.api.service.ai;

import com.facishare.marketing.api.arg.AiChatRecordArg;
import com.facishare.marketing.api.result.ai.AiChatRecordResult;
import com.facishare.marketing.api.result.ai.AiChatSessionResult;
import com.facishare.marketing.api.result.ai.AiChatShareDetailResult;
import com.facishare.marketing.api.vo.ai.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

import java.util.List;

public interface AiChatService {

    Result<String> assembleRequestUrl(String ea, Integer fsUserId);
    Result<String> streamingRequestUrl(String ea, Integer fsUserId);
    Result<PageResult> pageQueryPrompt(String ea, Integer fsUserId, PageQueryPromptVO vo);
    Result createPrompt(String ea, Integer fsUserId, CreatePromptVO obj);
    Result editPrompt(String ea, Integer fsUserId, CreatePromptVO obj);
    Result editRecordV2(String ea, Integer fsUserId, AiChatRecordArg arg);
    Result markPrompt(String ea, Integer fsUserId, String id);
    Result matchScene(String ea, Integer fsUserId, MatchSceneVO vo);
    Result<String> createShare(String ea, Integer fsUserId, AiChatShareVO vo);
    Result<AiChatShareDetailResult> getShareDetail(String ea, Integer fsUserId, String id);
    Result<String> getCurrentUserAvatar(String ea, Integer fsUserId);
    Result<Boolean> deleteSession(String ea, Integer fsUserId, String id);
    Result createRecord(String ea, Integer fsUserId, AiChatRecordCreateVO vo);
    Result<Boolean> deleteRecord(String ea, Integer fsUserId, String id);
    Result editSession(String ea, Integer fsUserId, String id, String title);
    Result setSessionTop(String ea, Integer fsUserId, String id, boolean isTop);
    Result editRecord(String ea, Integer fsUserId, String id, String content, int actionStatus);
    Result getRecord(String ea, Integer fsUserId, String id);
    Result report(String ea, Integer fsUserId, AiChatRecordArg arg);
    Result getAgentWelcome(String ea, Integer fsUserId, AgentWelcomeVO arg);
    Result<PageResult<AiChatSessionResult>> pageQuerySessions(String ea, Integer fsUserId, AiChatSessionVO vo);
    Result<PageResult<AiChatRecordResult>> pageQueryRecords(String ea, Integer fsUserId, AiChatRecordVO vo);
    Result<List<AiChatRecordResult>> querySessionRecordsByButtonApiName(String ea, Integer fsUserId, AiChatRecordVO vo);
    Result chatCompleteWithSession(String ea, Integer fsUserId, ChatCompleteVO vo);
    Result chatCompleteWithoutSession(String ea, Integer fsUserId, ChatCompleteVO vo);
    Result getChatCompleteResult(String ea, Integer fsUserId, String id);
    Result isOpenShareGPT(String ea, Integer fsUserId);

    Result promptCompletions(String ea, Integer fsUserId, PromptCompletionsArg arg);
}
