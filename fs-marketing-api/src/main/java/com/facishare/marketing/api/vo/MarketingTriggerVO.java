/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.api.vo;

import com.facishare.marketing.common.enums.I18nKeyEnumV2;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.vo.qywx.QywxAttachmentsVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.typehandlers.value.FlexibleJson;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * Created on 2021-02-25.
 */
@Data
public class MarketingTriggerVO implements Serializable {
	@ApiModelProperty("触发器ID")
	private String id;
	@ApiModelProperty("使用范围 public private")
	private String usageRange;
	/** @see com.facishare.marketing.common.enums.TriggerSnapshotStatusEnum */
	@ApiModelProperty("触发器状态，enabled, disabled")
	private String snapshotStatus;
	@ApiModelProperty("触发器名称")
	private String name;
	@ApiModelProperty("触发器描述")
	private String description;
	private Integer creator;
	@ApiModelProperty("实例个数")
	private Integer instanceCount;
	/** @see com.facishare.marketing.common.enums.TriggerSceneEnum */
	@ApiModelProperty("使用场景:conference, live, wx_service_account, marketing_event, email, official_website,qywx")
	private String triggerScene;
	/** @see com.facishare.marketing.common.enums.TriggerTypeEnum */
	@ApiModelProperty("触发类型： single_timing， trigger_by_action,repeat_timing")
	private String triggerType;
	/** 触发时刻, 结合定时触发使用
	 * @see TriggerTimeTypeEnum
	 */
	@ApiModelProperty("触发时刻类型：at_conference_start, before_conference_start, at_conference_end, after_conference_end")
	private String triggerTimeType;
	/** 当天为0 前N天为-N 后N天为N */
	@ApiModelProperty("触发偏移天数， 当天为0 前N天为-N 后N天为N")
	private Integer triggerDayOffset;
	/** 当天的第几分钟触发 */
	@ApiModelProperty("当天的第几分钟触发")
	private Integer triggerAtMinutes;
	/** 定时触发的目标用户类型
	 * @see com.facishare.marketing.common.enums.TriggerTargetUserTypeEnum
	 */
	@ApiModelProperty("定时触发目标用户：all_campaign, pending_review_campaign, review_success_campaign, review_failure_campaign, checked_in_campaign, not_checked_in_campaign")
	private String triggerTargetUserType;
	/** @see com.facishare.marketing.common.enums.TriggerActionTypeEnum */
	@ApiModelProperty("触发动作类型： conference_enroll， conference_enroll_review, conference_enroll_review_pass, conference_enroll_review_fail, conference_check_in, live_enroll, live_view, live_replay, live_chat, " + "look_up_object_in_marketing_event, submit_form_in_marketing_event, wx_service_subscribe, wx_service_click_menu, mail_open, mail_click, mail_response, mail_unsubscribe, mail_report_spam look_up_website submit_website_form")
	private String triggerActionType;
	/** @see ObjectTypeEnum */
//	@ApiModelProperty("目标对象类型")
//	private Integer targetObjectType;
//	@ApiModelProperty("目标对象id")
//	private String targetObjectId;
	/**
	 * {
	 *   "bdba73be47614d5491f3a28262993242": 26,
	 *   "test":0
	 * }
	 */
	@ApiModelProperty("目标对象")
	private FlexibleJson targetObjects;
	@ApiModelProperty("额外信息，对于点击邮件链接为邮件链接")
	private String additionalMsg;
	/** @see com.facishare.marketing.common.enums.LimitTypeEnum */
	@ApiModelProperty("用户限制类型：none")
	private String limitType;
	@ApiModelProperty("用户动作次数")
	private Integer limitActionCount;
	@ApiModelProperty("动作持续时间限制")
	private Integer limitActionDurationMinutes;
	@ApiModelProperty("用户是否只执行一次")
	private Boolean userExecuteOnce;
	@ApiModelProperty("是否按照条件过滤")
	private Boolean filterByCondition;
	@ApiModelProperty("IN: 包含全部 HASANYOF:包含任意")
	private String tagOperator;
	@ApiModelProperty("标签名称列表")
	private TagNameList tagNames;
	@ApiModelProperty("是否排除标签")
	private Boolean excludeTags;
	@ApiModelProperty("要排除的标签名称列表")
	private TagNameList excludeTagNames;
	@ApiModelProperty("前端扩展字段")
	private FlexibleJson frontEndExtensions;
	@ApiModelProperty("触发器任务列表")
	private List<TriggerTaskVO> triggerTasks;
	@ApiModelProperty("触发器种类 0-默认触发器 1-模板触发器")
	private Integer type;
	private Long createTime;
	private Long updateTime;

	//定时触发的时间
	private Long executeTime;
	//重复周期类型 月 周 天
	private Integer repeatType;
	//重复日期
	private List<Integer> repeatValue;
	private Long repeatRangeStart;
	private Long repeatRangeEnd;

	//人群--发送范围
	private Integer sendRange;

	@ApiModelProperty("当sendRange=3时生效，为营销用户对象的列表")
	private List<String> marketingUserGroupIds;

	@ApiModelProperty("筛选条件  只有sendRange=1才有效")
	private List<Map<String, Object>> filters = Lists.newArrayList();

	@ApiModelProperty("标签列表")
	private List<TagName> tagIdList;

	private List<String> groupMsgSenderIds;

	@ApiModelProperty("客户群SOP 筛选群条件")
	private List<Map<String, Object>> chatGroupFilters = Lists.newArrayList();

	public void addTriggerTaskVo(TriggerTaskVO triggerTaskVO){
		if (triggerTasks == null){
			triggerTasks = new ArrayList<>(8);
		}
		triggerTasks.add(triggerTaskVO);
	}
	
	public void sortTriggerTasks(){
		if (triggerTasks != null && !triggerTasks.isEmpty()) {
			Collections.sort(triggerTasks);
		}
	}
	
	public String verifyErrorMsg(){
		if (!TriggerUsageRangeEnum.isValid(usageRange)){
			return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_144);
		}
		if (!TriggerSceneEnum.isValid(triggerScene)){
			return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_147);
		}
		if (!TriggerTypeEnum.isValid(triggerType)){
			return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_150);
		}
		if (TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(triggerType)){
			if (TriggerSceneEnum.CONFERENCE.getTriggerScene().equals(triggerScene)){
				if (!TriggerTargetUserTypeEnum.getConferenceTargetUserTypes().contains(triggerTargetUserType)){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_155);
				}
				if (triggerDayOffset == null){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_158);
				}
				if (triggerAtMinutes == null){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_161);
				}
			}
			if (TriggerSceneEnum.LIVE.getTriggerScene().equals(triggerScene)){
				if (!triggerActionType.equals(TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType())
						&& !TriggerTargetUserTypeEnum.getLiveTargetUserTypes().contains(triggerTargetUserType)){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_155);
				}
				if (triggerDayOffset == null){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_158);
				}
				if (triggerAtMinutes == null){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_161);
				}
			}
		}
		if (TriggerTypeEnum.TRIGGER_BY_ACTION.getTriggerType().equals(triggerType)){
			if (TriggerSceneEnum.CONFERENCE.getTriggerScene().equals(triggerScene)){
				if(!triggerActionType.equals(TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType()) && !TriggerActionTypeEnum.getConferenceTriggerActionTypes().contains(triggerActionType)){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_180);
				}
			}
			if (TriggerSceneEnum.LIVE.getTriggerScene().equals(triggerScene)){
				if(!triggerActionType.equals(TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType()) && !TriggerActionTypeEnum.getLiveTriggerActionTypes().contains(triggerActionType)){
					return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_180);
				}
			}
		}
		if (!LimitTypeEnum.isValid(limitType)){
			return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_190);
		}
		if (LimitTypeEnum.LIMIT_BY_ACTION_COUNT.getLimitType().equals(limitType) && (limitActionCount == null || limitActionCount < 0)){
			return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_193);
		}
		if (LimitTypeEnum.LIMIT_BY_ACTION_DURATION.getLimitType().equals(limitType) && (limitActionDurationMinutes == null || limitActionDurationMinutes < 0)){
			return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_196);
		}
		
		if (triggerTasks == null || triggerTasks.isEmpty()){
			return I18nUtil.get(I18nKeyEnumV2.MARK_VO_MARKETINGTRIGGERVO_200);
		}
		
		for (TriggerTaskVO triggerTask : triggerTasks) {
			String taskErrorMsg = triggerTask.verifyErrorMsg();
			if (!Strings.isNullOrEmpty(taskErrorMsg)){
				return taskErrorMsg;
			}
		}
		
		return null;
	}
	
	
}