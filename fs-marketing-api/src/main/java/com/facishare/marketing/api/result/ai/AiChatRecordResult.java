package com.facishare.marketing.api.result.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiChatRecordResult implements Serializable {
    private String id;
    private String sessionId;
    private String tips;
    private String content;
    private List<Object> contentList;
    private String referenceText;
    private String contentType;
    private String cardType;
    private AgentResponse.AgentAction action;
    private List<AgentResponse.AgentAction> actions;
    private Object data;
    private String previewContent;
    private Object displayData;
    private String actionType;
    private Object originalData;
    private int likeStatus;
    private int actionStatus;
    private Integer createBy;
    private Long createTime;
    private boolean hasFollowUpQuestion;
    private String followUpQuestion;
    private boolean finish;
    private AgentResponse.AgentProcess process;
    @Deprecated
    private int status = 2;
}
