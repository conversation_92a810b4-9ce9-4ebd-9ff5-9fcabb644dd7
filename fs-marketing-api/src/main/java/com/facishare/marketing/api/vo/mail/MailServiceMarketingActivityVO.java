package com.facishare.marketing.api.vo.mail;

import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2020/7/2.
 */
@Data
public class MailServiceMarketingActivityVO implements Serializable{
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "模板id", required = true)
    private String templateId;
    @ApiModelProperty(value = "邮件发送时机  1->立即发送  2 ->延迟发送 ", required = true)
    private Integer type;
    @ApiModelProperty(value = "Excel的taPath", required = true)
    private String taPath;
    @ApiModelProperty(value = " 1 导入excel表格，2 按选择人群 3 会议活动成员 6 直播活动成员 8 活动营销 9 按对象标签")
    private Integer sendRange;
    @ApiModelProperty("导入文件名")
    private String fileName;
    @ApiModelProperty("人群id  只有sendRange=2才有效")
    private List<String> marketingUserGroupIds;
    @ApiModelProperty("会议发送邮件映射 key 为报名id， value 为对应email地址(旧映射字段)")
    private Map<String, String> enrollMailMap;
    @ApiModelProperty("会议发送邮件参会人员列表")
    private List<String> campaignIds;
    @ApiModelProperty("邮件内容")
    private String content;
    @ApiModelProperty("定时发送时间，以ms为单位")
    private Long fixedTime;
    @ApiModelProperty("邮件类型 0：触发  1：批量")
    private Integer mailType;
    @ApiModelProperty("发件人地址ids")
    private List<String> sendMailIds;
    @ApiModelProperty("回复人地址ids")
    private List<String> replyToIds;
    @ApiModelProperty(value = "过滤掉N天内发送过的用户， 为空表示不过滤")
    private Integer filterNDaySentUser;
    @ApiModelProperty(value = "邮件附件地址")
    private List<MailAttachment> attachments;
    @ApiModelProperty(value = "直接发送邮箱地址")
    private TagNameList tagNames;
    @ApiModelProperty(value = "多标签查询的操作符号 HASANYOF(包含以下所有标签） 、 IN(包含以下任意标签）")
    private String tagOperator;
    @ApiModelProperty("需要排除的标签")
    private TagNameList excludeTagNames;

    @Data
    public static class MailAttachment implements Serializable{
        @ApiModelProperty(value = "附件的名称")
        private String attachmentName;

        @ApiModelProperty(value = "附件的路径")
        private String attachmentPath;

        @ApiModelProperty(value = "附件大小")
        private long size;

        @ApiModelProperty(value = "文件后缀名")
        private String ext;
    }
}
