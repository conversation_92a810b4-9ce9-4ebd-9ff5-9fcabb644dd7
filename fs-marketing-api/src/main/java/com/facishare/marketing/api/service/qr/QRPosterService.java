package com.facishare.marketing.api.service.qr;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.qr.*;
import com.facishare.marketing.api.vo.CreateQRPosterVO;
import com.facishare.marketing.api.vo.QueryCreateChuangKeTieJsSdkOptionVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

import java.util.List;

public interface QRPosterService {

    Result<QueryQRPosterByEaListUnitResult> createQRPoster(CreateQRPosterVO vo);

    Result deleteQRPoster(String ea, Integer userId, String qrPosterId);

    Result<SpreadQRPosterResult> spreadQRPoster(String qrPosterId, Integer qrCodeId, String ea, Integer userId, String finalTAPath, String marketingActivityId, String spreadTaskId, String inviteId, String posterEa);

    Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByEa(String ea, Integer userId, Integer pageSize,
                                                                      Integer pageNum, Long time, String marketingEventId,
                                                                      String title, boolean needDetail, Integer type, String groupId);

    Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByEa(String ea, Integer userId, Integer pageSize,
                                                                      Integer pageNum, Long time, String marketingEventId,
                                                                      String title, boolean needDetail, Integer type, String groupId, boolean needCheckMobileDisplay);


    Result<PageResult<QueryQRPosterByTypeAndTargetIdUnitResult>> queryListByTypeAndTargetId(String ea, Integer type, String targetId, Integer pageSize, Integer pageNum, Long time);

    Result<QueryQRPosterDetailResult> queryDetail(String ea, Integer userId, String qrPosterId, String inviteId, boolean needAvatar);

    Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByForwardTypeAndTargetId(String ea, Integer userId, Integer pageSize, Integer pageNum, Long time,String marketingEventId, List<Integer> forwardTypes, String targetId, Integer type, boolean needDetail);

    Result<QueryCreateChuangKeTieJsSdkOptionResult> queryChuangKeTieJsSdkOption(QueryCreateChuangKeTieJsSdkOptionVO vo);


    Result<SyncChuangKeTiePosterResult> syncChuangKeTiePoster(String ea, Integer userId, String designId, String url);

    Result<EditObjectGroupResult> editQRPosterGroup(String ea, Integer fsUserId, EditObjectGroupArg arg);

    Result<Void> deleteQRPosterGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg);

    Result<Void> setQRPosterGroup(String ea, Integer fsUserId, SetObjectGroupArg arg);

    Result<Void> deleteQRPosterBatch(String ea, Integer fsUserId, DeleteMaterialArg arg);

    Result<Void> topQRPoster(String ea, Integer fsUserId, TopMaterialArg arg);

    Result<Void> cancelTopQRPoster(String ea, Integer fsUserId, CancelMaterialTopArg arg);

    Result<Void> addQRPosterGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg);

    Result<ObjectGroupListResult> listQRPosterGroup(String ea, Integer fsUserId, ListGroupArg arg);

    Result<List<String>> getGroupRole(String groupId);

    Result<SpreadQRPosterResult> spreadQRPosterForPartner(String erUpstreamEa, Integer erOuterUid, String erOutTenantId, String qrPosterId, Integer qrCodeId, String finalTAPath, String marketingActivityId, String spreadTaskId, String inviteId);

    Result<PageResult<QueryQRPosterByEaListUnitResult>> queryEvenQrPosterByTargetId(EvenQrPosterByTargetIdArg arg);
}
