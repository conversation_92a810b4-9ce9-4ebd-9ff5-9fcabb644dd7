package com.facishare.marketing.api.result.sms;

import com.facishare.marketing.common.enums.sms.mw.MwSendTaskTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * @创建人 zhengliy
 * @创建时间 2018/12/25 20:13
 * @描述
 * @IgnoreI18nFile
 */
@Data
public class QuerySMSSendResult implements Serializable {
    @ApiModelProperty(name = "id", value = "群发id")
    private String id;
    //创建时间
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private long createTime;
    @ApiModelProperty(name = "sendTime", value = "发送时间")
    private long sendTime;

    // 发送短信人的姓名
    @ApiModelProperty(name = "creator", value = "创建人")
    private String creator;
    //实际发送人数
    @ApiModelProperty(name = "actualSenderCount", value = "送达人数")
    private Integer actualSenderCount;
    //需要发送人数
    @ApiModelProperty(name = "toSenderCount", value = "需要发送人数 ")
    private Integer toSenderCount;
    /**@see com.facishare.marketing.common.enums.sms.mw.MwSendStatusEnum  发送状态 */
    @ApiModelProperty(name = "status", value = "" + "0 新建\n" + "1 发送完成\n" + "2 发送中\n" + "3 定时发送中\n" + "6 发送完成 \n" + "7 发送失败 \n" + "10取消发送")
    private Integer status;
    // 消费短信数量
    @ApiModelProperty(name = "consumerCount", value = "消费短信数量")
    private Integer consumerCount;
    // 模板名称
    @ApiModelProperty(name = "templateName", value = "模板名称")
    private String templateName;
    @ApiModelProperty(name = "templateId", value = "模板名称")
    private String templateId;
    @ApiModelProperty(name = "templateStatus", value = "模板审核状态 状态：0确认有效，1审核中，2确认无效，3隐藏，4禁用，5模板不存在，6已过期，7删除，8审核失败")
    private Integer templateStatus;
    // 短信内容
    @ApiModelProperty(name = "content", value = "模板内容")
    private String content;

    // 发送时间枚举
    /** {@link MwSendTaskTypeEnum} */
    @ApiModelProperty(name = "type", value = "发送时机   1为立即发送， 2为定时发送")
    private Integer type;

    @ApiModelProperty(name = "signatrure", value = "短信签名")
    private String signatrure;

    @ApiModelProperty(name = "reply", value = "失败信息")
    private String reply;

    @ApiModelProperty(name = "scheduleTime", value = "定时发送时间")
    private long scheduleTime;

    @ApiModelProperty(name = "sceneType", value = "发送场景")
    private Integer sceneType;

    @ApiModelProperty(name = "marketingActivityId", value = "推广活动id")
    private String marketingActivityId;

    @ApiModelProperty(name = "marketingEventId", value = "市场活动id")
    private String marketingEventId;

    @ApiModelProperty(name = "marketingEventName", value = "市场活动名称")
    private String marketingEventName;

    @ApiModelProperty(name = "marketingEventType", value = "市场活动类型")
    private String marketingEventType;

    @ApiModelProperty(value = "市场活动如果是会议-保存会议id")
    private String marketingObjectId;

    @ApiModelProperty("访问次数")
    private Integer pv;

    @ApiModelProperty("访问次数")
    private Integer uv;

    @ApiModelProperty("线索数")
    private Integer leadCount;

    @ApiModelProperty("是否可以取消")
    private Boolean sendCancelable;

    @ApiModelProperty("用户群体")
    private List<String> userGroupIds; //用户群组ID

    @ApiModelProperty("taPath名称")
    private String taPathName;

    @ApiModelProperty("审核状态")
    private String auditStatus;
}
