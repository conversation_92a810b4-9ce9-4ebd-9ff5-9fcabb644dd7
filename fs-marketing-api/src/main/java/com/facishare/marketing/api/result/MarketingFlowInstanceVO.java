package com.facishare.marketing.api.result;

import com.facishare.marketing.api.MarketingActivityNodeAdditionalConfigWithNameVO;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.vo.MarketingActivityNodeAdditionalConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @IgnoreI18nFile
 */
@Data
public class MarketingFlowInstanceVO implements Serializable {
    @ApiModelProperty("流程实例id")
    private String id;
    @ApiModelProperty(value = "营销用户数据")
    private UserMarketingAccountData userMarketingAccount;
    @ApiModelProperty(value = "流程实例状态")
    private String state;
    @ApiModelProperty(value = "启动时间")
    private Long startTime;
    @ApiModelProperty(value = "结束时间")
    private Long endTime;
    @ApiModelProperty(value = "流程执行详情")
    private List<TaskVO> tasks;

    @Data
    public static class TaskVO implements Serializable{
        @ApiModelProperty(value = "节点名称")
        private String activityName;
        @ApiModelProperty(value = "节点开始时间")
        private Long startTime;
        @ApiModelProperty(value = "节点结束时间")
        private Long endTime;
        @ApiModelProperty(value = "节点持续时间")
        private Long duration;
        @ApiModelProperty(value = "当前节点是否结束")
        private Boolean ended;
        @ApiModelProperty(value = "执行结果")
        private Boolean executeSuccess;
        @ApiModelProperty(value = "执行失败原因")
        private String executeFailReason;
        @ApiModelProperty(value = "节点类型:1：CRM属性更新 2：点击链接 3：提交表单 4：活动签到 " + "5：发送服务号消息 6：发送短信消息 7：添加标签 8：删除标签 9：等待 10:属性更新 " + "11:创建定时等待节点的结果对象 12:回复微信消息 13 会议签到 14 会议报名 1000001 启动 1000002 结束 1000003 网关")
        private Integer nodeType;
        @ApiModelProperty(value = "节点定义")
        private MarketingActivityNodeAdditionalConfigWithNameVO activityDefinition;

        public void plusDuration(Long duration){
            if(duration != null){
                if(this.duration == null){
                    this.duration = duration;
                }else{
                    this.duration += duration;
                }
            }
        }
    }
}

