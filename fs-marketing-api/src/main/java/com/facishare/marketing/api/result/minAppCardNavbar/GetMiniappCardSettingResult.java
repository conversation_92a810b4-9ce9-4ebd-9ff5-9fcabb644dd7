package com.facishare.marketing.api.result.minAppCardNavbar;

import com.facishare.marketing.api.result.cta.CtaRelationInfo;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * Created  By zhoux 2021/05/07
 **/
@Data
public class GetMiniappCardSettingResult implements Serializable {

    @ApiModelProperty("导航栏颜色")
    private String navbarButtonColor;

    @ApiModelProperty("字段编辑设置")
    private String fieldEditSetting;

    @ApiModelProperty("字段名称设置")
    private String fieldNameSetting;

    @ApiModelProperty("背景色设置")
    private String backgroundColor;

    @ApiModelProperty("名片类型")
    private Integer cardType;

    @ApiModelProperty("是否开启名片logo")
    private Boolean openCardLogo;

    @ApiModelProperty("名片logo")
    private String cardLogo;

    @ApiModelProperty("名片背景设置类型 1: 设置背景图 2: 设置背景颜色")
    private Integer bgSettingType;

    @ApiModelProperty("名片背景图地址")
    private String cardImagePath;

    @ApiModelProperty("名片背景颜色")
    private String cardColor;

    @ApiModelProperty("名片头像地址")
    private String cardAvatarPath;

    @ApiModelProperty("文字和图标颜色")
    private String fontIconColor;

    @ApiModelProperty("名片logo是否默认")
    private Integer cardLogoDefault; // 名片logo是否默认 1: 默认 2:自定义

    @ApiModelProperty("名片背景图片是否默认")
    private Integer cardImageDefault; // 名片背景图片是否默认 1: 默认 2:自定义

    @ApiModelProperty("名片头像是否默认")
    private Integer cardAvatarDefault; // 名片头像是否默认 1: 默认 2:自定义

    @ApiModelProperty("名片背景图片默认索引(无边框模板才有)")
    private Integer cardImageDefaultIndex;

    @ApiModelProperty("名片分享风格")
    private Integer cardShareType; // 0: 默认风格 1:卡片风格  2:信封风格

    @ApiModelProperty("关联CTA")
    private List<CtaRelationInfo> ctaRelationInfos;
}
