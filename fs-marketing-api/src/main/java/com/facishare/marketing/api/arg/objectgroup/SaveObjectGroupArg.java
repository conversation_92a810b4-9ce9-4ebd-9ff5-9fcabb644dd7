package com.facishare.marketing.api.arg.objectgroup;

import com.facishare.marketing.common.enums.I18nKeyEnumV3;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.vo.BaseVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/6/25
 * @IgnoreI18nFile
 **/
@Data
@ToString(callSuper = true)
public class SaveObjectGroupArg extends BaseVO {

    @ApiModelProperty("分组 编辑时非空")
    private String id;

    @ApiModelProperty("分组名称，同一企业不能重复")
    private String name;

    /**
     * @see com.facishare.marketing.common.enums.ObjectTypeEnum
     */
    @ApiModelProperty("物料类型： 10000：目标人群")
    private Integer objectType;

    private Integer type = 2;//2:企微客户 4:企微客户群

    private String parentId; //父id

    private Integer seqNo; //序号

    private SaveObjectGroupVisibleArg visible;// 可见范围

    @Override
    public Result checkParam() {
        if (StringUtils.isBlank(this.name)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnumV3.MARK_OBJECTGROUP_SAVEOBJECTGROUPARG_42));
        }
        return super.checkParam();
    }

}