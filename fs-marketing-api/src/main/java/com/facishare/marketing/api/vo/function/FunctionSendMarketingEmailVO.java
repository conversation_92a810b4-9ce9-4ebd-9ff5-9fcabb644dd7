package com.facishare.marketing.api.vo.function;

import com.facishare.marketing.api.vo.mail.MailServiceMarketingActivityVO;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FunctionSendMarketingEmailVO implements Serializable {
    /**
     * 市场活动Id
     */
    private String marketingEventId;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * MailSendRangeEnum 函数支持支持 人群、标签
     */
    private Integer sendRange;

    /**
     * 邮件模板
     */
    private String templateId;


    /**
     * 邮件发送时机  1->立即发送  2 ->延迟发送
     */
    private Integer type;

    /**
     * 发送的目标人群的ID
     */
    private List<String> marketingUserGroupIds;

    /**
     * 根据营销对象标签发送
     */
    private TagNameList tagNames;

    /**
     * 多标签查询的操作符号，函数支持两个：HASANYOF(包含以下所有标签） 、 IN(包含以下任意标签）
     */
    private String tagOperator;

    /**
     * 需要排除的标签
     */
    private TagNameList excludeTagNames;
    /**
     * 邮件内容
     */
    private String content;

    /**
     * 定时发送时间，以ms为单位
     */
    private Long fixedTime;

    /**
     * 发件人地址
     */
    private String sendTo;

    /**
     * 回复人地址
     */
    private String replyFrom;

    /**
     * 过滤掉N天内发送过的用户， 为空表示不过滤
     */
    private Integer filterNDaySentUser;
}
