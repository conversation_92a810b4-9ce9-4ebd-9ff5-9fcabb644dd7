/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.api.arg;

import com.google.common.collect.ImmutableList;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @IgnoreI18nFile
 */
@Data
public class AddProductInitArg implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer isOperation;
    private String id;
    private String name;
    private String price;
    private String discountPrice;
    private String video;
    private String summary;
    private List<String> headPics;
    private List<String> detailPics;

    // 1：个人产品  2：公司产品
    private Integer type;
    private Boolean tryOutEnable;
    /** 按钮名称 **/
    private String tryOutButtonValue;
    /** 对象描述ID **/
    private String tryOutFormObjectApiName;

    //封面缩略图
    private List<String> headPicsThumbs;

    //背景缩略图
    private List<String> detailPicsThumbs;

    //url原图path
    private List<String>  path;

    //url缩略图path
    private List<String> thumbsPath;

    //旧url原图path(为做兼容)
    private List<String> oldPath;


    public void buildProductDefaultArg() {
        this.name = "客脉企业全员营销获客版（示例）";
        this.isOperation = 1;
        this.price = "免费使用";
        this.discountPrice = "";
        this.summary = "企业智能微信营销方案 每个企业成员都是获客流量入口，低成本高效率获客 全员共享企业品宣资源，用户雷达捕捉每一个用户互动，激活社群运营与客户持续连接 线索跟进转换存入企业CRM客户库，企业客户资产累积扩增 自动采集推广行为过程数据，互动效果全面掌握，与纷享销客CRM无缝集成。";
        this.type = 2;
        this.video = "";
        this.tryOutEnable = false;
        this.tryOutButtonValue = null;
        this.tryOutFormObjectApiName = "";
    }


}
