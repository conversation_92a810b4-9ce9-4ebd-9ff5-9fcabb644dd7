package com.facishare.marketing.api.vo;

import com.facishare.marketing.api.arg.sms.SmsVarArg;
import com.facishare.marketing.api.result.MarketingUserGroupDetailResult;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * @author: dongzhb
 * @date: 2019/2/26
 * @Description:
 * @IgnoreI18nFile
 */
@Data
@ToString(callSuper = true)
public class MarketingActivityGroupSenderDeatilVO  implements Serializable {
    private static final long serialVersionUID = 5061290646425604560L;
    @ApiModelProperty("短信活动ID")
    private String id;
    @ApiModelProperty("需要发送人数")
    private Integer toSenderCount;
    @ApiModelProperty("发送成功人数")
    private Integer actualSenderCount;
    @ApiModelProperty("发送失败")
    private Integer  faillSenderCount;
    @ApiModelProperty("模板名称")
    private String templateName;
    @ApiModelProperty("模板内容")
    private String content;
    @ApiModelProperty("模板id")
    private String templateId;
    @ApiModelProperty(value = "变量列表")
    private List<SmsVarArg> smsVarArgs;
    @ApiModelProperty(name = "templateStatus", value = "模板审核状态 状态：0确认有效，1审核中，2确认无效，3隐藏，4禁用，5模板不存在，6已过期，7删除，8审核失败")
    private Integer templateStatus;
    @ApiModelProperty(name = "type", value = "发送时机   1为立即发送， 2为定时发送")
    private Integer type;
    @ApiModelProperty(name = "sceneType", value = "发送场景")
    private Integer sceneType;
    @ApiModelProperty("短信签名")
    private String signatrure;
    @ApiModelProperty(name = "status", value = ""
        + "0 新建\n"
        + "1 发送成功\n"
        + "2 发送中\n"
        + "3 定时发送中\n"
        + "6 发送成功\n"
        + "7 发送失败 \n"
        + "10 取消发送"
    )
    private Integer status;
    @ApiModelProperty(name = "reply", value = "失败信息")
    private String reply;
    @ApiModelProperty("发送范围  1导入excel表格，2按选择人群")
    private Integer sendRange;
    @ApiModelProperty("人群id  只有sendRange=2才有效")
    private List<String> marketingUserGroupIds;
    @ApiModelProperty(name = "scheduleTime", value = "定时发送时间")
    private long scheduleTime;
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private long createTime;
    @ApiModelProperty(name = "sendTime", value = "发送时间")
    private long sendTime;
}


