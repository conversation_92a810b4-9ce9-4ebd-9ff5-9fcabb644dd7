package com.facishare.marketing.api.service.qywx;

import com.facishare.marketing.api.arg.qywx.miniappProduct.ProductNaviStyleQueryArg;
import com.facishare.marketing.api.arg.qywx.miniappProduct.QueryProductListArg;
import com.facishare.marketing.api.result.qywx.miniappProduct.ProductNaviStyleSettingResult;
import com.facishare.marketing.api.result.qywx.miniappProduct.QueryProductDetailResult;
import com.facishare.marketing.api.vo.product.QueryProductListVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import java.util.List;

public interface MiniAppProductService {


    Result<PageResult<QueryProductDetailResult>> queryProductList(QueryProductListArg arg);


    Result<ProductNaviStyleSettingResult> queryProductNaviStyle(ProductNaviStyleQueryArg arg);


    Result<PageResult<QueryProductDetailResult>> queryEnterpriseDefaultProduct(QueryProductListVO arg);
}
