package com.facishare.marketing.api.data.usermarketingaccount;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetInfoByIdentifyVO implements Serializable {

    private String userMarketingId;
    private String userMarketingName;
    private String phone;
    private String email;
    private List<RelateObj> relateObjList;

    @Data
    public static class RelateObj implements Serializable {

        private String apiName;

        private String objectId;
    }

}
