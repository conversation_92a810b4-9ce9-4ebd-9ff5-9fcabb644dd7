package com.facishare.marketing.qywx.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.FsViewProfileDTO;
import com.facishare.marketing.api.arg.QueryI18nArg;
import com.facishare.marketing.api.arg.TenantBrandColorArg;
import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.result.EnterpriseSettingsResult;
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult;
import com.facishare.marketing.api.result.account.GetFsUserInfoResult;
import com.facishare.marketing.api.result.account.GetQywxBaseInfoFromWxResult;
import com.facishare.marketing.api.result.account.QywxEmployeeBindWxUserResult;
import com.facishare.marketing.api.result.qywx.QywxBaseInfoResult;
import com.facishare.marketing.api.service.AccountService;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.common.annoation.Authentication;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.PhoneNumberCheck;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.facishare.marketing.qywx.arg.*;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.reader.UnicodeReader;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;

/**
 * Created by zhengh on 2020/1/13.
 */
@RestController
@RequestMapping("/account")
@Slf4j
public class AccountController {
    @Autowired
    private AccountService accountService;
    @Autowired
    private SettingService settingService;

    @Autowired
    private MergeJedisCmd jedisCmd;

    private Gson gs = new Gson();
    @ReloadableProperty("open_marketing_tag_objects")
    private String openMarketingTagObjects;
    /**
     * 灰度企业获取api
     * **/

    private static final String MARKETING_GRAY_EA_LISt = "MARKETING_GRAY_EA_LISt_";
    private static final int MARKETING_GRAY_EA_LISt_EXPIRED_TIME = 3600 * 8;

    private FsViewProfileDTO getConfigProfile() {
        FsViewProfileDTO fsViewProfileDTO = getGrayEaList();
        if(fsViewProfileDTO != null){
            log.info("AccountController -> getApiNameList from redis cache, FsViewProfileDTO:{}", fsViewProfileDTO);
            return fsViewProfileDTO;
        }else {
            //拉取配置文件并格式转换
            IConfig config = ConfigFactory.getInstance().getConfig("fs-web-view-profile");
            String yText = config.getString();
            Map<String, Object> map = yamlHandler(yText);
            fsViewProfileDTO = BeanUtil.copyByFastJson(map.get("openMarketingTagObjects"), FsViewProfileDTO.class);
            setGrayEaList(fsViewProfileDTO);
        }
        return fsViewProfileDTO;
    }

    private Map<String, Object> yamlHandler(String text){
        //返回的结果
        Map<String, Object> result = new LinkedHashMap<>();
        try {
            //读取方式
            UnicodeReader reader = new UnicodeReader(new ByteArrayInputStream(text.getBytes()) {
            });
            //单文件处理
            Yaml yaml = new Yaml();
            Object object = yaml.load(reader);
            if (object instanceof Map) {
                result = (Map) object;
            }

            reader.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    public boolean setGrayEaList(FsViewProfileDTO fsViewProfileDTO) {
        String key = MARKETING_GRAY_EA_LISt;
        String value = gs.toJson(fsViewProfileDTO);;
        String result = jedisCmd.setex(key, MARKETING_GRAY_EA_LISt_EXPIRED_TIME, value);
        return !Strings.isNullOrEmpty(result);
    }
    public FsViewProfileDTO getGrayEaList() {
        String key = MARKETING_GRAY_EA_LISt;
        String value = jedisCmd.get(key);
        if (value == null) {
            return null;
        }
        return gs.fromJson(value, FsViewProfileDTO.class);
    }


    @ApiOperation(value = "判断开通状况")
    @TokenCheckTrigger
    @RequestMapping(value = "/isApply", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<AccountIsApplyForKISResult> isApply(@RequestBody QYWXBaseArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("AccountController.isApply failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null){
            log.warn("ArticleController.isApply failed arg param error token:{}", arg.getToken());
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.isApplyForQyWxKIS(arg.getFsEa(), arg.getFsUserId(), arg.getUid(), arg.getAppId());
    }

    @ApiOperation(value = "查询企业微信员工H5和个人微信绑定情况")
    @TokenCheckTrigger
    @RequestMapping(value = "/queryQywxUserBindWxUserInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<QywxEmployeeBindWxUserResult> queryQywxUserBindWxUserInfo(@RequestBody QYWXBaseArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("AccountController.isApply failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null){
            log.warn("ArticleController.isApply failed arg param error token:{}", arg.getToken());
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.queryQywxH5UserBindWxUserInfo(arg.getFsEa(), arg.getFsUserId(), arg.getCorpId(), arg.getQyUserId(), arg.getAppId());
    }

    @ApiOperation(value = "获取当前人的纷享身份信息")
    @TokenCheckTrigger
    @RequestMapping(value = "/getFsUserInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetFsUserInfoResult> getFsUserInfo(@RequestBody QYWXBaseArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("AccountController.getFsUserInfo failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null){
            log.warn("ArticleController.getFsUserInfo failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.getFsUserInfo(arg.getFsEa(), arg.getFsUserId(), arg.getUid());
    }


    @ApiOperation(value = "发送手机验证码")
    @TokenCheckTrigger
    @RequestMapping(value = "/sendSMCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @Authentication
    public Result sendSMCode(@RequestBody SendSMCodeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("AccountController.sendSMCode param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (!PhoneNumberCheck.isPhoneLegal(arg.getPhone())) {
            log.info("AccountController.sendSMCode failed phone number is illegal phone:{}", arg.getPhone());
            return new Result(SHErrorCode.PHONE_NUMBER_ILLEGAL);
        }
        return accountService.sendSMCode(arg.getPhone(), arg.getObjectType(), arg.getObjectId());
    }


    @ApiOperation(value = "校验手机验证码")
    @TokenCheckTrigger
    @RequestMapping(value = "/checkSMCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result checkSMCode(@RequestBody CheckSMCodeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("AccountController.checkSMCode param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.checkSMCode(arg.getPhone(), arg.getVerifyCode());
    }

    @ApiOperation(value = "绑定到企业微信")
    @TokenCheckTrigger
    @RequestMapping(value = "/bindToWxWorkExternalUser", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result bindToWxWorkExternalUser(@RequestBody BindToWxWorkExternalUserArg arg) {
        if (arg.isWrongParam()) {
            log.warn("AccountController.bindToWxWorkExternalUser param error args:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.bindToWxWorkExternalUser(arg.getFsEa(), arg.getFriendUid(), arg.getWxWorkExternalUserId().get(0));
    }

    @ApiOperation(value = "企业微信小程序基础信息")
    @TokenCheckTrigger
    @RequestMapping(value = "/qywxBaseInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QywxBaseInfoResult> qywxBaseInfo(@RequestBody QywxBaseInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("AccountController.qywxBaseInfo param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.qywxBaseInfo(arg.getFsEa(), arg.getCurrentAppId());
    }

    @ApiOperation(value = "获取企业微信基本信息（微信端）")
    @RequestMapping(value = "/getQywxBaseInfoFromWx", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetQywxBaseInfoFromWxResult> getQywxBaseInfoFromWx(@RequestBody GetQywxBaseInfoFromWxArg arg) {
        if (arg.isWrongParam()) {
            log.warn("AccountController.getQywxBaseInfoFromWx param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.getQywxBaseInfoFromWx(arg.getFsEa(), arg.getTargetUid());
    }

    /**
     * 主要针对这个全员推广链接,跳转进行绑定逻辑(旷世企业)
     * */
    @ApiOperation(value = "判断用户是否已绑定员工信息,获取applyInfoKey")
    @RequestMapping(value = "/getApplyInfoKeyForWx", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> getApplyInfoKeyForWx(@RequestBody GetApplyInfoKeyArg arg) {
        if (arg.isWrongParam()) {
            log.warn("AccountController.getApplyInfoKeyForWx param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.getApplyInfoKeyForWx(arg.getEa(),arg.getFsUserId());
    }

    @ApiOperation(value = "获取用户所在企业权限信息")
    @TokenCheckTrigger
    @RequestMapping(value = "/getEnterpriseSettings", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EnterpriseSettingsResult> getEnterpriseSettings(@RequestBody EmployeeMsgArg arg) {
        //没有表示非员工身份,这里直接返回,
        if(StringUtils.isBlank(arg.getFsEa())|| arg.getFsUserId()==null){
            return Result.newSuccess();
        }
        return settingService.getEnterpriseSettings(arg.getFsEa(), arg.getFsUserId());
    }

    @ApiOperation(value = "根据企业是否灰度返回对应的apiName")
    @TokenCheckTrigger
    @RequestMapping(value = "/getApiNameList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> getApiNameList(@RequestBody EmployeeMsgArg arg) {
        String ea = arg.getFsEa();
//        FsViewProfileDTO fsViewProfileDTO = getConfigProfile();
        FsViewProfileDTO fsViewProfileDTO = JSON.parseObject(openMarketingTagObjects, FsViewProfileDTO.class);
        if (fsViewProfileDTO != null && StringUtils.isNotEmpty(fsViewProfileDTO.getRule())) {
            String[] split = fsViewProfileDTO.getRule().split(";");
            if(Arrays.asList(split).contains(ea)){
                return Result.newSuccess(fsViewProfileDTO.getV());
            }
        }
        return Result.newSuccess(new ArrayList<>());
    }

    @ApiOperation(value = "查询企业品牌色设置")
    @TokenCheckTrigger
    @RequestMapping(value = "/queryTenantBrandColor", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> queryTenantBrandColor(@RequestBody TenantBrandColorArg arg) {
        //先取参数了的ea信息,没有再看身份上是否携带
        if(!arg.validParam()){
            if(StringUtils.isBlank(arg.getFsEa())){
                return new Result<>(SHErrorCode.PARAMS_ERROR);
            }
            arg.setEa(arg.getFsEa());
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(arg.getEa()) && arg.getEa().contains(";")){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.queryTenantBrandColor(arg);
    }

    @ApiOperation(value = "查询企业多语词条")
    @TokenCheckTrigger
    @FilterLog
    @RequestMapping(value = "/queryI18n", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<LinkedTreeMap<String,Object>> queryI18n(@RequestBody QueryI18nArg arg) {
        if(StringUtils.isBlank(arg.getEa()) && StringUtils.isBlank(arg.getFsEa())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if(!arg.validParam()){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if(StringUtils.isBlank(arg.getEa())){
            arg.setEa(arg.getFsEa());
        }
        return settingService.queryI18n(arg);
    }

    @ApiOperation(value = "检查互联企业是否有crm账号")
    @TokenCheckTrigger
    @FilterLog
    @RequestMapping(value = "/checkEnterpriseRelationHasCrmAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Integer> publicEmployeeHasCrmAccount(@RequestBody QYWXBaseArg arg) {
        // 空实现，谨防前端调进来报错
        return Result.newSuccess(1);
    }
}
