/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.qywx.arg.privateMessage;

import com.facishare.mankeep.common.enums.PrivateMessageTypeEnum;
import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Arrays;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/09/30
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PrivateMessageSendArg extends QYWXBaseArg {

    @ApiModelProperty(value = "接收方用户id")
    private String targetUid;

    @ApiModelProperty(value = "会话id")
    private String sessionId;

    /**
     * 发送类型  {@link com.facishare.mankeep.common.enums.PrivateMessageTypeEnum}
     */
    @ApiModelProperty(value = "发送类型")
    private Integer type;

    @ApiModelProperty(value = "图片TAPat(图片消息)")
    private String picPath;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "产品id(产品信息)")
    private String  productId;

    @ApiModelProperty(value = "文件信息")
    private FileInfo fileInfo;

    @Data
    private class FileInfo implements Serializable {
        @ApiModelProperty(value = "文件id")
        private String fileId;

        @ApiModelProperty(value = "企业账号")
        private String ea;

        /**
         * 文件使用类型{@link com.facishare.mankeep.common.enums.FileUseTypeEnum}
         */
        @ApiModelProperty(value = "文章使用类型")
        private Integer fileUseType;

        /**
         * 文件来源类型{@link com.facishare.mankeep.common.enums.FileBelongEnum}
         */
        @ApiModelProperty(value = "文件使用类型")
        private Integer fileSourceType;

        @ApiModelProperty(value = "文件名")
        private String fileName;

        @ApiModelProperty(value = "文件扩展名")
        private String fileExtension;

        @ApiModelProperty(value = "文件大小")
        private Long fileSize;

        @ApiModelProperty(value = "根目录id")
        private String rootFolderId;

        @ApiModelProperty(value = "父级目录id")
        private String parentFolderId;

/*        @ApiModelProperty(value = "父级目录名")
        private String parentFolderName;*/

     /*   @ApiModelProperty(value = "阅读总数")
        private Integer readCount;*/

        /*@ApiModelProperty(value = "创建者id")
        private Integer creatorId;

        @ApiModelProperty(value = "创建者名")
        private String creatorName;*/

        /*@ApiModelProperty(value = "创建时间")
        private Long createTime;*/

        @ApiModelProperty(value = "是否可浏览")
        private Boolean isCanPreview;

        @ApiModelProperty(value = "图片url")
        private String url;

        @ApiModelProperty(value = "缩略图url")
        private String thumbnailUrl;

       /* @ApiModelProperty(value = "缩略图path")
        private String thumbnailPath;

        @ApiModelProperty(value = "缩略图token")
        private String thumbnailToken;*/

        @ApiModelProperty(value = "文件npath")
        private String nPath;
    }

    public boolean isWrongType() {
        return type == null || (Arrays.stream(PrivateMessageTypeEnum.values()).noneMatch(data -> data.getType() == type));
    }

    public String reSetContent() {
        if (type == PrivateMessageTypeEnum.PICTURE_MESSAGE.getType() && StringUtils.isBlank(content)) {
            return "[图片]";
        } else if (type == PrivateMessageTypeEnum.FILE_MESSAGE.getType() && StringUtils.isBlank(content)) {
            return "[文件]";
        } else if (type == PrivateMessageTypeEnum.CONSULTING_PRODUCT_MESSAGE.getType() && StringUtils.isBlank(content)) {
            return "[产品咨询]";
        } else if (type == PrivateMessageTypeEnum.PRODUCTS_RECOMMENDED.getType() && StringUtils.isBlank(content)) {
            return "[产品推荐]";
        } else if(type == PrivateMessageTypeEnum.WX_PERSONAL_OFFICIAL_ACCOUNTS.getType() && StringUtils.isBlank(content)) {
            return "[发送公众号]";
        } else {
            return content;
        }
    }

    public boolean isWrongTypeParams() {
        return type == PrivateMessageTypeEnum.FILE_MESSAGE.getType() && fileInfo == null || type == PrivateMessageTypeEnum.PICTURE_MESSAGE.getType() && picPath == null
            || (type == PrivateMessageTypeEnum.CONSULTING_PRODUCT_MESSAGE.getType() || type == PrivateMessageTypeEnum.PRODUCTS_RECOMMENDED.getType()) && productId == null;
    }

    public boolean isWrongParams() {
        return StringUtils.isBlank(targetUid) ||  "undefined".equals(targetUid) || StringUtils.isBlank(getAppId()) || getAppId().equals(WxAppInfoEnum.Mankeep.getAppId());
    }

}
