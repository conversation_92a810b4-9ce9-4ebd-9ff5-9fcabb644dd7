package com.facishare.marketing.qywx.controller;

import com.facishare.marketing.qywx.arg.GetTaskStatusArg;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.result.SpreadTaskNormalResult;
import com.facishare.marketing.api.result.kis.KisNoticeDetailResult;
import com.facishare.marketing.api.result.kis.QuerySpreadTaskListResult;
import com.facishare.marketing.api.service.kis.SpreadTaskService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.facishare.marketing.qywx.arg.GetNoticeDetailArg;
import com.facishare.marketing.qywx.arg.QuerySpreadTaskListArg;
import com.facishare.marketing.qywx.arg.QywxzTaskSendRecordlArg;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by zhengh on 2020/1/10.
 */
@RestController
@RequestMapping("/spreadTask")
@Slf4j
public class SpreadTaskController {
    @Autowired
    private SpreadTaskService spreadTaskService;

    /**
     * 查询员工推广列表
     * @param token
     * @param arg
     * @return
     */
    @ApiOperation(value = "查询员工推广列表")
    @TokenCheckTrigger
    @RequestMapping(value = "/querySpreadTaskList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<QuerySpreadTaskListResult> querSpreadTaskList(@RequestBody QuerySpreadTaskListArg arg){
        if (StringUtils.isBlank(arg.getToken())){
            log.warn("SpreadTaskController.querySpreadTaskList failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null){
            log.warn("SpreadTaskController.querySpreadTaskList failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return spreadTaskService.querySpreadTaskList(arg.getFsEa(), arg.getFsUserId(), true,arg.getUpstreamEa());
    }

    @ApiOperation(value = "KIS个人推广跳转到KIS端物料详情页")
    @TokenCheckTrigger
    @RequestMapping(value = "/getNoticeDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<KisNoticeDetailResult> getNoticeDetail(@RequestBody GetNoticeDetailArg arg) {
        if(arg.isWrongParam()) {
            log.warn("SpreadTaskController.getNoticeDetail param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return spreadTaskService.getNoticeDetail(arg.getNoticeId());
    }


    @ApiOperation(value = "企业微信推广或sop员工发送记录统计")
    @TokenCheckTrigger
    @RequestMapping(value = "/qywxTaskSendRecord", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<Void> qywxTaskSendRecord(@RequestBody QywxzTaskSendRecordlArg arg) {
        if(arg.isWrongParam()) {
            log.warn("SpreadTaskController.qywxTaskSendRecord param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return spreadTaskService.qywxTaskSendRecord(arg.getFsEa(), arg.getFsUserId(), arg.getExternalUserId(), arg.getQyUserId(), arg.getMarketingActivityId(), arg.getTriggerTaskInstanceId(), arg.getType());
    }

    @ResponseBody
    @TokenCheckTrigger
    @RequestMapping(value = "/spreadTaskIsRevocation", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Boolean> spreadTaskIsRevocation(@RequestBody IdArg arg) {
        Result<Boolean> noticeDetail = spreadTaskService.spreadTaskIsRevocation(arg.getId());
        if (null == noticeDetail) {
            return Result.newError(SHErrorCode.KIS_QUERY_SPREAD_STATUS_FAILED);
        }
        return noticeDetail;
    }

    /**
     * @description: 判断推广物料是否能正常访问 (1.可能已撤回 2.推广活动关联的市场活动已作废)
     * @author: mingqiao
     * @date: 2022/8/18 14:53
     * @param arg:
     * @return: com.facishare.marketing.common.result.Result<SpreadTaskNormalResult>
     */
    @ResponseBody
    @TokenCheckTrigger
    @RequestMapping(value = "/spreadTaskIsNormal", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<SpreadTaskNormalResult> spreadTaskIsNormal(@RequestBody GetTaskStatusArg arg) {
        if(StringUtils.isBlank(arg.getObjectId()) && arg.getObjectType() == null && StringUtils.isBlank(arg.getMarketingActivityId()) && StringUtils.isBlank(arg.getMarketingEventId())) {
            log.warn("SpreadTaskController.arg.getId() param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return spreadTaskService.spreadTaskIsNormal(arg.getObjectId(),arg.getObjectType(),arg.getMarketingActivityId(),arg.getMarketingEventId(), null);
    }
}
