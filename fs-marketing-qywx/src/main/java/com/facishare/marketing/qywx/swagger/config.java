/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.qywx.swagger;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.async.DeferredResult;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class config {

    public static final String SWAGGER_SCAN_BASE_PACKAGE = "com.facishare.marketing.qywx.controller";
    public static final String VERSION = "1.0.0";

    @Bean
    public Docket buildDocket(){
        boolean enable = false;
        String SpringEnv = System.getProperty("spring.profiles.active");
        if (SpringEnv != null && (SpringEnv.equals("fstest"))) {
            enable = true;
        }
        String env = System.getProperty("process.profile");
        if (env != null && (env.equals("fstest"))) {
            enable = true;
        }

        return new Docket(DocumentationType.SWAGGER_2)
            .genericModelSubstitutes(DeferredResult.class)
            .useDefaultResponseMessages(false)
            .forCodeGeneration(false)
            .apiInfo(buildApiInf())
            .select().apis(RequestHandlerSelectors.basePackage(SWAGGER_SCAN_BASE_PACKAGE))
            .paths(PathSelectors.any())
            .build().enable(enable);
    }

    private ApiInfo buildApiInf(){
        return new ApiInfoBuilder()
            .title("企业微信接口文档")
            .build();
    }
}