package com.facishare.marketing.qywx.controller;

import com.facishare.mankeep.api.result.BaseCardInfoResult;
import com.facishare.mankeep.api.vo.QueryCardVO;
import com.facishare.marketing.api.arg.UpdateShareCardUrl;
import com.facishare.marketing.api.arg.qywx.card.AddCardFileVo;
import com.facishare.marketing.api.arg.qywx.card.QueryCardArg;
import com.facishare.marketing.api.arg.qywx.card.QueryCardHolderInfoVO;
import com.facishare.marketing.api.result.qywx.card.*;
import com.facishare.marketing.api.service.qywx.CardService;
import com.facishare.marketing.common.enums.PicOperationFlagEnum;
import com.facishare.marketing.common.enums.ReqFromEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.facishare.marketing.qywx.arg.card.AddCardInfoArg;
import com.facishare.marketing.qywx.arg.card.QueryCardHolderInfoArg;
import com.facishare.marketing.qywx.arg.card.QueryTradeListArg;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created  By zhoux 2020/01/14
 **/
@RestController
@RequestMapping("/card")
@Slf4j
public class CardController {

    @Autowired
    private CardService cardService;

    @Deprecated
    @ApiOperation(value = "查看名片全量信息, 客脉已废弃")
    @TokenCheckTrigger
    @RequestMapping(value = "queryCardInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryCardResult> queryCardInfo(@RequestBody QueryCardArg arg) {
        return cardService.queryCardInfo(arg);
    }

    @ApiOperation(value = "查看自己名片基础信息")
    @TokenCheckTrigger
    @RequestMapping(value = "queryMyBaseCardInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CardInfoResult> queryMyBaseCardInfo(@RequestBody QueryCardArg arg) {
        if (arg.isWrongParam()) {
            log.warn("CardController.queryMyBaseCardInfo param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        arg.setCardUid(arg.getUid());
        return cardService.queryMyBaseCardInfo(arg);
    }

    @ApiOperation(value = "使用cradUid查看自己名片基础信息")
    @RequestMapping(value = "queryMyBaseCardInfoWithCardUid", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CardInfoResult> queryMyBaseCardInfoWithCardUid(@RequestBody QueryCardArg arg) {
        if (StringUtils.isBlank(arg.getCardUid())) {
            log.warn("CardController.queryMyBaseCardInfo param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        arg.setUid(arg.getCardUid());
        return cardService.queryMyBaseCardInfo(arg);
    }


    @ApiOperation(value = "查看名片基础信息")
    @TokenCheckTrigger
    @RequestMapping(value = "queryBaseCardInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CardInfoResult> queryBaseCardInfo(@RequestBody QueryCardArg arg) {
        return cardService.queryBaseCardInfo(arg);
    }

    @ApiOperation(value = "查看名片详情中产品及最新动态")
    @TokenCheckTrigger
    @RequestMapping(value = "queryProductsAndFeed", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryProductsAndFeedResult> queryProductsAndFeed(@RequestBody QueryCardArg arg) {
        return cardService.queryProductsAndFeed(arg);
    }

    @ApiOperation(value = "查看名片详情中图片列表")
    @TokenCheckTrigger
    @RequestMapping(value = "queryPhotoList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryPhotoListResult> queryPhotoList(@RequestBody QueryCardArg arg) {
        return cardService.queryPhotoList(arg);
    }

    @ApiOperation(value = "更新/添加卡片")
    @TokenCheckTrigger
    @RequestMapping(value = "addCardInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<AddCardResult> addCardInfo(@RequestBody AddCardInfoArg addCardInfoArg) {
        Result checkResult = addCardInfoArg.isWrongParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        if (addCardInfoArg.getIsOperation() == null) {
            addCardInfoArg.setIsOperation(PicOperationFlagEnum.OPERATION_PIC.getType());
        }
        com.facishare.marketing.api.arg.qywx.card.AddCardInfoArg arg = BeanUtil.copy(addCardInfoArg, com.facishare.marketing.api.arg.qywx.card.AddCardInfoArg.class);
        arg.setFrom(ReqFromEnum.MINIAPP.getFrom());
        arg.setUid(addCardInfoArg.getUid());

        if (CollectionUtils.isNotEmpty(addCardInfoArg.getAddCardFileArgList())) {
            arg.setAddCardFileList(BeanUtil.copy(addCardInfoArg.getAddCardFileArgList(), AddCardFileVo.class));
        }
        return cardService.addCardInfo(arg);
    }

    @ApiOperation(value = "使用cradUid更新/添加卡片")
    @RequestMapping(value = "addCardInfoWithCardUid", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<AddCardResult> addCardInfoWithCardUid(@RequestBody AddCardInfoArg addCardInfoArg) {
        Result checkResult = addCardInfoArg.isWrongParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        if (StringUtils.isBlank(addCardInfoArg.getCardUid())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (addCardInfoArg.getIsOperation() == null) {
            addCardInfoArg.setIsOperation(PicOperationFlagEnum.OPERATION_PIC.getType());
        }
        com.facishare.marketing.api.arg.qywx.card.AddCardInfoArg arg = BeanUtil.copy(addCardInfoArg, com.facishare.marketing.api.arg.qywx.card.AddCardInfoArg.class);
        arg.setFrom(ReqFromEnum.MINIAPP.getFrom());
        arg.setUid(addCardInfoArg.getCardUid());

        if (CollectionUtils.isNotEmpty(addCardInfoArg.getAddCardFileArgList())) {
            arg.setAddCardFileList(BeanUtil.copy(addCardInfoArg.getAddCardFileArgList(), AddCardFileVo.class));
        }

        return cardService.addCardInfo(arg);
    }

    @ApiOperation(value = "查询行业列表")
    @TokenCheckTrigger
    @RequestMapping(value = "queryTradeList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<TradeListResult> queryTradeList(@RequestBody QueryTradeListArg arg) {
        return cardService.queryTradeList();
    }


    @ApiOperation(value = "查询名片夹信息")
    @TokenCheckTrigger
    @RequestMapping(value = "queryCardHolderInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<QueryCardHolderInfoResult>> queryCardHolderInfo(@RequestBody QueryCardHolderInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("CardController.queryCardHolderInfo error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryCardHolderInfoVO vo = BeanUtil.copy(arg, QueryCardHolderInfoVO.class);
        vo.setEa(arg.getFsEa());
        return cardService.queryCardHolderInfo(vo);
    }

    @ApiOperation(value = "更新名片纷享卡片")
    @TokenCheckTrigger
    @RequestMapping(value = "updateShareCardUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result updateShareCardUrl(@RequestBody UpdateShareCardUrl arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return cardService.updateShareCardUrl(arg);
    }
}
