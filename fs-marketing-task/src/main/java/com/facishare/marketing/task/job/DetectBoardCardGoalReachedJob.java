/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.BoardService;
import com.facishare.marketing.common.util.TimeMeasureUtil;
import com.facishare.marketing.task.dao.BoardCardDao;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@JobHander(value = "DetectBoardCardGoalReachedJob")
@Service
@Slf4j
public class DetectBoardCardGoalReachedJob extends IJobHandler {
    @Autowired
    private BoardCardDao boardCardDao;
    @Autowired
    private BoardService boardService;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("DetectBoardCardGoalReachedJob start");
            notifyBoardCardTaskLifecycleChanged();
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

   // @Scheduled(cron = "${cron.DetectBoardCardGoalReachedJob}")
    private void notifyBoardCardTaskLifecycleChanged(){
        TimeMeasureUtil.runAndMeasureRunTime("DetectBoardCardGoalReachedJob", () -> {
            List<String> allEas = boardCardDao.listAllNeedCheckGoalReachedEas();
            for (String ea : allEas) {
                try {
                    UserInfoKeeper.setEa(ea);
                    boardService.detectGoalReachedBoardCardTaskAndSendNotification(ea);
                }catch (Exception e){
                    log.warn("Exception at DetectBoardCardGoalReachedJob", e);
                }
            }
        });
    }
}
