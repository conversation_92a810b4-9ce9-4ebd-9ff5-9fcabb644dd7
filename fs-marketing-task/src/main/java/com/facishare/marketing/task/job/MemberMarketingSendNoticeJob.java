/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.api.vo.MarketingActivityExternalConfigVO;
import com.facishare.marketing.common.enums.NoticeStatusEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.task.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.task.dao.NoticeDao;
import com.facishare.marketing.task.entity.NoticeEntity;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * Created By tianh, 2018/5/23.
 **/
@JobHander(value = "memberMarketingSendNoticeJob")
@Service
@Slf4j
public class MemberMarketingSendNoticeJob extends IJobHandler {
    @Autowired
    private NoticeDao noticeDao;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("MemberMarketingSendNoticeJob start");
            handleNotice();
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

    private void handleNotice() {
        try {
            Date endTime = new Date();
            Date startTime = DateUtil.plusMinutes(endTime, -60);
            List<NoticeEntity> noticeEntityList = noticeDao.listMemberMarketingByStatusAndTime(NoticeStatusEnum.UN_SEND.getStatus(), startTime, endTime);
            log.info("member marketing Notices Result:{}", noticeEntityList);
            if (CollectionUtils.isEmpty(noticeEntityList)) {
                return;
            }
            for (NoticeEntity noticeEntity : noticeEntityList) {
                try {
                    MarketingActivityExternalConfigVO activity = marketingActivityExternalConfigDao.getMarketingActivityByAssociateId(noticeEntity.getId());
                    UserInfoKeeper.setEa(noticeEntity.getFsEa());
                    noticeService.sendNoticeById(noticeEntity.getId(), activity.getMarketingActivityId(),activity.getMarketingEventId());
                } catch (Exception e) {
                    log.error("member marketing handleNotice fail noticeEntity: {}", noticeEntity, e);
                }
            }
        } catch (Exception e) {
            log.error("member marketing handleNotice error, ", e);
        }
    }
}
