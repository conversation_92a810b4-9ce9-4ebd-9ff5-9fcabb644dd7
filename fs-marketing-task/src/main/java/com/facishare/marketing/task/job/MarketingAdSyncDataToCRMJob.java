/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.task.dao.AdAccountDAO;
import com.facishare.marketing.task.entity.AdAccountEntity;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by zhengh on 2021/3/8.
 */
@JobHander(value = "marketingAdSyncDataToCRMJob")
@Service
@Slf4j
public class MarketingAdSyncDataToCRMJob extends IJobHandler {
    @Autowired
    private BaiduCampaignService baiduCampaignService;
    @Autowired
    private AdAccountDAO adAccountDAO;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("sync ad data start");
            syncAdDataByTask();
            log.info("sync ad data end");
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

    public void syncAdDataByTask() {
        List<AdAccountEntity> adAccountEntityList = adAccountDAO.getAllAdAccount();
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            ThreadPoolUtils.execute(() -> {
                UserInfoKeeper.setEa(adAccountEntity.getEa());
                baiduCampaignService.refreshAllDataByAccountId(adAccountEntity.getId());
            }, ThreadPoolUtils.ThreadPoolTypeEnums.AD);
        }
    }

}
