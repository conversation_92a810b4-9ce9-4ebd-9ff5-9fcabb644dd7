/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.QywxMomentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@JobHander(value = "MomentTaskScheduleJob")
@Service
@Slf4j
public class MomentTaskScheduleJob extends IJobHandler {
    @Autowired
    private QywxMomentService qywxMomentService;

    /**
     * 持续更新朋友圈任务状态 更新momentid
     * @param params
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            qywxMomentService.qywxMomentTaskSchedule();
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
        return new ReturnT(200, "定时任务调用成功");
    }
}
