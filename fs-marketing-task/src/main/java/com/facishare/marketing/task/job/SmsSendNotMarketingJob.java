/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.sms.SmsJobHandlerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2021/3/18 14:13
 * @Version 1.0
 */
@JobHander(value = "SmsSendNotMarketingJob")
@Service
@Slf4j
public class SmsSendNotMarketingJob extends IJobHandler {
    @Autowired
    private SmsJobHandlerService smsJobHandlerService;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("SmsSendNotMarketingJob start");
            mwSmsSendNotMarketing();
            log.info("SmsSendNotMarketingJob end");
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

    public void mwSmsSendNotMarketing() {
        smsJobHandlerService.mwSmsSendNotMarketingTask();
    }
}
