/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.arg.SendSaveClueFailMessageArg;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.task.dao.CustomizeFormDataDao;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@JobHander(value = "SendSaveClueFailNoticeWebsiteJob")
@Service
@Slf4j
public class SendSaveClueFailNoticeWebsiteJob extends IJobHandler {

    @Autowired
    private CustomizeFormDataDao customizeFormDataDao;
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Value("${notice.time.span}")
    private Integer noticeTimeSpan;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            log.info("SendSaveClueFailNoticeWebsiteJob start");
            handleSaveClueFailNotice();
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

    private void handleSaveClueFailNotice() {
        try {
            Date endTime = new Date();
            String startTimeString = DateUtil.format(endTime.getTime() - noticeTimeSpan * 60 * 1000);
            Date startTime = DateUtil.parse(startTimeString);
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            List<SendSaveClueFailMessageArg> args = customizeFormDataDao.listByTimeForSaveClueFailWebSite(sdf.format(startTime), sdf.format(endTime));
            log.info("SendSaveClueFailMessages Result:{}", args);
            if (CollectionUtils.isEmpty(args)) {
                return;
            }

            for (SendSaveClueFailMessageArg arg : args) {
                try {
                    UserInfoKeeper.setEa(arg.getEa());
                    customizeFormDataService.sendSaveClueFailMessage(arg);
                } catch (Exception e) {
                    log.error("===========FAIL send message exception! args:{}", GsonUtil.toJson(arg));
                }
            }
        } catch (Exception e) {
            log.error("Error: handleNotice!", e);
        }
    }

}
