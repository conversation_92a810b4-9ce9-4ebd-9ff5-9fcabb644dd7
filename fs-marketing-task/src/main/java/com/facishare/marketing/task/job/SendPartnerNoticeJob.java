/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.common.enums.NoticeStatusEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.task.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.task.dao.NoticeDao;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * Created By tianh, 2018/5/23.
 **/
@JobHander(value = "SendPartnerNoticeJob")
@Service
@Slf4j
public class SendPartnerNoticeJob extends IJobHandler {
    @Autowired
    private NoticeDao noticeDao;
    @Autowired
    private NoticeService noticeService;
    @Value("${notice.time.span}")
    private Integer noticeTimeSpan;
    @Value("${host}")
    private String host;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("SendNoticeJob start");
            handleNotice();
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

    // @Scheduled(cron = "${notice.cron.expression}")
    private void handleNotice() {
        try {
            Date endTime = new Date();
            String startTimeString = DateUtil.format(endTime.getTime() - noticeTimeSpan * 60 * 1000);
            Date startTime = DateUtil.parse(startTimeString);

            List<String> noticeIds = noticeDao.listByStatusAndTimeForPartner(NoticeStatusEnum.UN_SEND.getStatus(), startTime, endTime);
            log.info("partner Notices Result:{}", noticeIds);
            if (CollectionUtils.isEmpty(noticeIds)) {
                return;
            }

            for (String noticeId : noticeIds) {
                try {
                    String marketingActivityId = marketingActivityExternalConfigDao.getMarketingActivityIdByAssociateId(noticeId);
                    noticeService.sendPartnerNoticeById(noticeId, marketingActivityId);
                } catch (Exception e) {
                    log.error("===========FAIL send notice exception! notice:{}", noticeId, e);
                }
            }
        } catch (Exception e) {
            log.error("Error: handleNotice!", e);
        }
    }
}
