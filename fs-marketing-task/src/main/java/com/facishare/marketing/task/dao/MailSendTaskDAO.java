package com.facishare.marketing.task.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.task.data.MailSendTaskEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2020/6/4.
 */
public interface MailSendTaskDAO {

    @FilterLog
    @Select("SELECT id, ea FROM mail_send_task WHERE send_status=0 AND fix_time<=(extract(epoch FROM now()) * 1000)")
    List<MailSendTaskEntity> getNeedSendTask();

    @FilterLog
    @Select("SELECT ea, id FROM mail_send_task where send_status = 3 and create_time >= (SELECT now( ) - INTERVAL '1 day') and create_time > '2023-05-12 23:00:00'")
    List<MailSendTaskEntity> getSuccessTask();

}

