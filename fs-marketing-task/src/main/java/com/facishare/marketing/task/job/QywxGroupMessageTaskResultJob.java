/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.QywxGroupMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@JobHander(value = "QywxGroupMessageTaskResultJob")
@Service
@Slf4j
public class QywxGroupMessageTaskResultJob extends IJobHandler {
    @Autowired
    private QywxGroupMessageService qywxGroupMessageService;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("QywxGroupMessageTaskResultJob task start");
            qywxGroupMessageService.qywxMessageResultSchedule();
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
        return new ReturnT(200, "定时任务调用成功");
    }
}
