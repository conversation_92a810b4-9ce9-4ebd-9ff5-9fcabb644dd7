/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.qywx.QywxStaffService;
import com.facishare.marketing.common.util.MDCUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.github.trace.TraceContext;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@JobHander(value = "QywxActivatedAccountJob")
@Service
@Slf4j
public class QywxActivatedAccountJob extends IJobHandler {

    @Autowired
    private QywxStaffService qywxStaffService;


    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        TraceContext context = TraceContext.get();
        context.setTraceId(UUIDUtil.getUUID());
        qywxStaffService.initQywxActivatedAccount(null);
        return new ReturnT(200, "QywxAddressBookJob定时任务调用成功");
    }
}
