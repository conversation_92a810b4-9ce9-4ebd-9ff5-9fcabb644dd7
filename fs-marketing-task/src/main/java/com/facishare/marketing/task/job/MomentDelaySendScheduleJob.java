/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.QywxMomentService;
import com.facishare.marketing.task.dao.QYWXMomentDelaySendDAO;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@JobHander(value = "MomentDelaySendScheduleJob")
@Service
@Slf4j
public class MomentDelaySendScheduleJob extends IJobHandler {
    @Autowired
    private QywxMomentService qywxMomentService;
    @Autowired
    private QYWXMomentDelaySendDAO qywxMomentDelaySendDAO;

    /**
     * 朋友圈延迟发送处理
     * @param params
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            handleExecute();
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
        return new ReturnT(200, "定时任务调用成功");
    }

    //灰度改造
    private void handleExecute(){
        List<String> eaList = qywxMomentDelaySendDAO.getMomentDelayTaskEa();
        for (String ea : eaList) {
            try {
                UserInfoKeeper.setEa(ea);
                qywxMomentService.qywxMomentDelayTaskScheduleByEa(ea);
            } catch (Exception e) {
                log.error("FAIL handleExecute MomentDelaySendScheduleJob message exception! args:{}", ea,e);
            }
        }
    }
}
