package com.facishare.marketing.task.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.task.entity.AdAccountEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AdAccountDAO {

    @Select("SELECT * FROM ad_account WHERE status != 99 and status != -1")
    @FilterLog
    List<AdAccountEntity> getAllAdAccount();

    @FilterLog
    @Select("SELECT ea, id FROM ad_account WHERE source = '巨量引擎' and type = 3 and status != 99 and status != -1 and status != 1")
    List<AdAccountEntity> queryAllLocalAdAccount();

}
