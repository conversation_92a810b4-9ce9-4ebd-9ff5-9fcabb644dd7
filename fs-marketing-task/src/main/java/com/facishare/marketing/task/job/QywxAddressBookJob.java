/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.service.qywx.QywxStaffService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created  By zhoux 2021/07/16
 **/
@JobHander(value = "QywxAddressBookJob")
@Service
@Slf4j
public class QywxAddressBookJob extends IJobHandler {

    @Autowired
    private QywxStaffService qywxStaffService;
    @Autowired
    private QYWXContactService qywxContactService;
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(calendar.DATE,-1);
            qywxContactService.deleteTemplateWebFanQrCodeByDays(calendar.getTime());
            qywxContactService.deleteHexagonWebFanQrCodeByDays(calendar.getTime());
            qywxContactService.deleteConfig();
            //qywxStaffService.initQywxAddressBook();
        } catch (Exception e) {
            log.error("QywxAddressBookJob定时任务调用异常调用异常，error:{}", e);
            throw e;
        }
        return new ReturnT(200, "QywxAddressBookJob定时任务调用成功");
    }
}
