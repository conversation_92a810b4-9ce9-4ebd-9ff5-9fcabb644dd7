/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.MarketingSceneService;
import com.facishare.marketing.task.dao.SceneTriggerTimedTaskDao;
import com.facishare.marketing.task.data.SceneTriggerTimedTaskEntity;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2021-03-02.
 */
@JobHander(value = "SceneTriggerTimedTaskCheckJob")
@Service
@Slf4j
public class SceneTriggerTimedTaskCheckJob extends IJobHandler {
	@Autowired
	private SceneTriggerTimedTaskDao sceneTriggerTimedTaskDao;
	@Autowired
	private MarketingSceneService marketingSceneService;
	
	@Override
	public ReturnT execute(TriggerParam params) throws Exception {
		try {
			List<SceneTriggerTimedTaskEntity> todoTasks = new LinkedList<>();
			Long currentTime = System.currentTimeMillis();
			int limit = 1000;
			for (int offset = 0; offset < 100000; offset+=limit) {
				List<SceneTriggerTimedTaskEntity> tasks = sceneTriggerTimedTaskDao.pageListTaskIds(currentTime, offset, limit);
				todoTasks.addAll(tasks);
				if (tasks.size() < limit){
					break;
				}
			}
			for (SceneTriggerTimedTaskEntity task : todoTasks) {
				try {
					UserInfoKeeper.setEa(task.getEa());
					marketingSceneService.startSceneTrigger(task.getId());
				}catch (Exception e){
					log.warn("Exception at execute task:", task.getId());
				}
			}
		} catch (Exception e) {
			log.error("定时任务调用异常调用异常，error:", e);
			throw e;
		}
		return new ReturnT(200, "定时任务调用成功");
	}
}
