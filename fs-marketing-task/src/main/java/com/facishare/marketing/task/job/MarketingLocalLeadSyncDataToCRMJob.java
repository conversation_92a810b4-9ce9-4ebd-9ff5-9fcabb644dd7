/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import com.facishare.marketing.api.vo.advertiser.headlines.SyncHeadlinesLocalLeadsVO;
import com.facishare.marketing.common.enums.advertiser.headlines.HeadlinesAdAccountTypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.task.dao.AdAccountDAO;
import com.facishare.marketing.task.entity.AdAccountEntity;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@JobHander(value = "marketingLocalLeadSyncDataToCRMJob")
@Service
@Slf4j
public class MarketingLocalLeadSyncDataToCRMJob extends IJobHandler {

    @Autowired
    private BaiduCampaignService baiduCampaignService;

    @Autowired
    private AdAccountDAO adAccountDAO;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            log.info("sync local lead job start");

            List<AdAccountEntity> adAccountEntityList = adAccountDAO.queryAllLocalAdAccount();
            if (CollectionUtils.isEmpty(adAccountEntityList)) {
                return new ReturnT(200, "成功");
            }
            for (AdAccountEntity adAccountEntity : adAccountEntityList) {
                UserInfoKeeper.setEa(adAccountEntity.getEa());
                SyncHeadlinesLocalLeadsVO syncHeadlinesLocalLeadsVO = new SyncHeadlinesLocalLeadsVO();
                Date endTime = new Date();
                syncHeadlinesLocalLeadsVO.setEndTime(DateUtil.getTimeStamp(endTime));
                syncHeadlinesLocalLeadsVO.setAdAccountId(adAccountEntity.getId());
                syncHeadlinesLocalLeadsVO.setEa(adAccountEntity.getEa());
                baiduCampaignService.refreshLocalLeadByAccountId(syncHeadlinesLocalLeadsVO);
            }
            log.info("sync local lead job end");
            return new ReturnT(200, "成功");
        } catch (Exception e) {
            log.error("本地推线索定时任务调用失败，发生异常：e:", e);
            throw e;
        }
    }

}
