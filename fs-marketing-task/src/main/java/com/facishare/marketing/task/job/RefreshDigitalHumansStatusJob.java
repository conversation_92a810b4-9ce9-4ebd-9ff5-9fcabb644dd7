/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.digitalHumans.DigitalHumansService;
import com.facishare.marketing.common.enums.digitalHumans.DigitalHumansStatusEnum;
import com.facishare.marketing.task.dao.DigitalHumansDAO;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@JobHander(value = "RefreshDigitalHumansStatusJob")
@Service
@Slf4j
public class RefreshDigitalHumansStatusJob extends IJobHandler {
    @Autowired
    private DigitalHumansService digitalHumansService;

    @Autowired
    private DigitalHumansDAO digitalHumansDAO;
    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            handleExecute();
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
        return new ReturnT(200, "定时任务调用成功");
    }
    private void handleExecute(){
        List<String> eaList = digitalHumansDAO.getNeedRefreshTaskEa(DigitalHumansStatusEnum.listNeedRefreshStatus());
        for (String ea : eaList) {
            try {
                UserInfoKeeper.setEa(ea);
                digitalHumansService.RefreshDigitalHumans(ea);
            } catch (Exception e) {
                log.error("FAIL handleExecute RefreshDigitalHumansStatusJob message exception! args:{}", ea,e);
            }
        }
    }
}
