package com.facishare.marketing.web.controller;

import com.facishare.marketing.api.result.GetTemplateByIdResult;
import com.facishare.marketing.api.result.ListActivityTemplateResult;
import com.facishare.marketing.api.result.ListAllContentActivityTemplateResult;
import com.facishare.marketing.api.service.ActivityTemplateService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.GetTemplateByIdArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 01/09/2018
 */
@RestController
@RequestMapping("template")
@Slf4j
@Api(description = "活动模版", tags = "TemplateController")
public class ActivityTemplateController {
    @Autowired
    private ActivityTemplateService activityTemplateService;

    @CheckIdentityTrigger
    @RequestMapping(value = "listActivityTemplates", method = RequestMethod.POST)
    @ApiOperation(value = "拉取活动模版", notes = "拉取活动模版")
    public Result<List<ListActivityTemplateResult>> listTemplates() {
        return activityTemplateService.listActivityTemplates();
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listAllContentActivityTemplates", method = RequestMethod.POST)
    @ApiOperation(value = "拉取活动模版所有内容", notes = "拉取活动模版所有内容")
    public Result<List<ListAllContentActivityTemplateResult>> listAllContentActivityTemplates() {
        return activityTemplateService.listAllContentActivityTemplates();
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getTemplateById", method = RequestMethod.POST)
    @ApiOperation(value = "获取活动模版", notes = "获取活动模版")
    public Result<GetTemplateByIdResult> getTemplateById(@RequestBody GetTemplateByIdArg arg) {
        return activityTemplateService.getTemplateById(arg.getId(), UserInfoKeeper.getEa());
    }
}
