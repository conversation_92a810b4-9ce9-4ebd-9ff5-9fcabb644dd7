package com.facishare.marketing.web.controller;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.distribution.OpenDistributeStatusArg;
import com.facishare.marketing.api.arg.miniAppSetting.ConfigMiniAppAutoUpgradeArg;
import com.facishare.marketing.api.arg.miniAppSetting.GetMiniAppAutoUpgradeStatusArg;
import com.facishare.marketing.api.arg.usermarketingaccount.DeleteMarketingUserGroupCustomizeObjectMappingArg;
import com.facishare.marketing.api.arg.usermarketingaccount.MarketingUserGroupCustomizeObjectMappingArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.marketinguser.MarketingUserExcludeApinameResult;
import com.facishare.marketing.api.result.distribution.CommitClueRewardStatusResult;
import com.facishare.marketing.api.result.distribution.QueryClueAuditStatusResult;
import com.facishare.marketing.api.service.CardTemplateService;
import com.facishare.marketing.api.service.EnterpriseMetaConfigService;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.vo.GetCardCommonSettingVo;
import com.facishare.marketing.api.vo.SystemOperationRecordVO;
import com.facishare.marketing.api.vo.UpdateCardCommonSettingVo;
import com.facishare.marketing.common.enums.MarketingSceneType;
import com.facishare.marketing.common.enums.SyncObjectTagStatusEnum;
import com.facishare.marketing.common.enums.distribution.DistributeStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CardCommonSetting;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.*;
import com.facishare.marketing.web.arg.distribution.QueryClueAuditStatusArg;
import com.facishare.marketing.web.arg.distribution.SetClueAuditStatusArg;
import com.facishare.marketing.web.arg.distribution.SetCommitClueRewardStatusArg;
import com.facishare.marketing.web.arg.distribution.SocialDistributionDetailArg;
import com.facishare.marketing.web.arg.marketinguser.MarketingUserExcludeApinameArg;
import com.facishare.marketing.web.enums.CheckIndentityAuthScopeEnum;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.fxiaoke.common.Pair;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.*;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("setting")
@Slf4j
@Api(description = "基础设置", tags = "SettingController")
public class SettingController {
    @Autowired
    private SettingService settingService;
    @Autowired
    private EnterpriseMetaConfigService enterpriseMetaConfigService;

    @Autowired
    private CardTemplateService cardTemplateService;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;


    @CheckIdentityTrigger
    @RequestMapping(value = "listAllConfig", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<AllConfigResult> listAllConfig() {
        return settingService.listAllConfig(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listMappingSettings", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取客脉CRM映射信息", notes = "获取客脉CRM映射信息")
    public Result<ListMappingSettingsResult> listMappingSettings(@RequestBody ListMappingSettingsArg arg) {
        return settingService.listMappingSettings(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getMankeepObjectApiName(), arg.getCrmObjectApiName());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateSaveConfig", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> updateSaveConfig(@RequestBody UpdateEnterpriseMetaConfigArg arg) {
        return settingService.updateSaveConfig(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getIsSaveCustomerToCrmEnabled());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateIsSaveMankeepToCrmEnabled", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "人脉存入crm线索开关", notes = "人脉存入crm线索开关")
    public Result<Void> updateIsSaveMankeepToCrmEnabled(@RequestBody UpdateIsSaveMankeepToCrmEnabledArg arg) {
        return settingService.updateIsSaveMankeepToCrmEnabled(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getIsSaveMankeepToCrmEnabled());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "mergeFieldMappings", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "保存或更新映射关系", notes = "保存或更新映射关系")
    public Result<Void> mergeFieldMappings(@RequestBody MergeFieldMappingsArg arg) {
        Preconditions.checkArgument(arg.getFieldMappingConfigs() != null);
        Preconditions.checkArgument(!arg.getFieldMappingConfigs().isEmpty());
        return settingService.mergeFieldMappings(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getFieldMappingConfigs());
    }

    @ApiOperation(value = "旧企业初始化执行脚本", notes = "旧企业初始化执行脚本")
    @RequestMapping(value = "brushLibrary", method = RequestMethod.POST)
    public Result<Void> brushLibrary(@RequestBody JSONObject arg) {
        String randomNum = arg.getString("randomNum");
        return settingService.brushLibrary(randomNum);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @ApiOperation(value = "开启社会化分销", notes = "开启社会化分销", tags = "1.5.5")
    @RequestMapping(value = "openDistributeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> openDistributeStatus(@RequestBody com.facishare.marketing.web.arg.distribution.OpenDistributeStatusArg arg) {
        if (arg.isWrongParam()) {
            log.warn("SettingController.openDistributeStatus arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        OpenDistributeStatusArg openDistributeStatusArg = BeanUtil.copy(arg, OpenDistributeStatusArg.class);
        openDistributeStatusArg.setEa(UserInfoKeeper.getEa());
        openDistributeStatusArg.setFsUid(UserInfoKeeper.getFsUserId());
        return settingService.openDistributeStatus(openDistributeStatusArg);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @ApiOperation(value = "修改社会化分销计划", notes = "修改社会化分销计划", tags = "1.5.5")
    @RequestMapping(value = "updateDistributePlan", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> updateDistributePlan(@RequestBody com.facishare.marketing.web.arg.distribution.OpenDistributeStatusArg arg) {
        if (arg.isWrongParam()) {
            log.warn("SettingController.updateDistributePlan arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        OpenDistributeStatusArg openDistributeStatusArg = BeanUtil.copy(arg, OpenDistributeStatusArg.class);
        openDistributeStatusArg.setEa(UserInfoKeeper.getEa());
        openDistributeStatusArg.setFsUid(UserInfoKeeper.getFsUserId());
        log.warn("SettingController.updateDistributePlan arg error arg:{}", openDistributeStatusArg);
        return settingService.updateDistributePlan(openDistributeStatusArg);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @ApiOperation(value = "判断是否开启社会化分销", notes = "判断是否开启社会化分销", tags = "1.5.5")
    @RequestMapping(value = "judgeDistributeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<DistributeStatusResult> judgeDistributeStatus() {
        if (StringUtils.isBlank(UserInfoKeeper.getEa())) {
            log.warn("SettingController.judgeDistributeStatus ea error");
            return new Result<DistributeStatusResult>(SHErrorCode.PARAMS_ERROR, new DistributeStatusResult(DistributeStatusEnum.CLOSE.getType()));
        }
        return settingService.judgeDistributeStatus(UserInfoKeeper.getEa());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @ApiOperation(value = "获得社会化分销计划的数据", notes = "获得社会化分销的数据", tags = "1.5.5")
    @RequestMapping(value = "getSocialDistributeTableDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<SocialDistributionDetailResult> getSocialDistributeTableDetail(@RequestBody SocialDistributionDetailArg arg) {
        if (StringUtils.isBlank(UserInfoKeeper.getEa()) || StringUtils.isBlank(arg.getPlanId())) {
            log.warn("SettingController.getSocialDistributeTableDetail ea or planId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.getSocialDistributeTableDetail(UserInfoKeeper.getEa(), arg.getPlanId());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.ONLY_APP_ADMIN)
    @ApiOperation(value = "设置企业信息", notes = "设置企业信息", tags = "1.8.2")
    @RequestMapping(value = "setEnterpriseInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> setEnterpriseInfo(@RequestBody com.facishare.marketing.web.arg.SetEnterpriseInfoArg arg) {
        Preconditions.checkArgument(arg != null);
        //初始创建要设置公司icon
        com.facishare.marketing.api.arg.SetEnterpriseInfoArg vo = BeanUtil.copy(arg, com.facishare.marketing.api.arg.SetEnterpriseInfoArg.class);
        vo.setUserId(UserInfoKeeper.getFsUserId());
        vo.setEa(UserInfoKeeper.getEa());
        return settingService.setEnterpriseInfo(vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.ONLY_APP_ADMIN)
    @ApiOperation(value = "查询企业信息", notes = "查询企业信息", tags = "1.8.2")
    @RequestMapping(value = "queryEnterpriseInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EnterpriseInfoResult> queryEnterpriseInfo() {
        return settingService.queryEnterpriseInfo(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "setClueAuditStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置分销线索审核开关状态", notes = "分销线索审核开关状态")
    public Result<Void> setClueAuditStatus(@RequestBody SetClueAuditStatusArg arg) {
        if (arg == null || arg.getClueAuditStatus() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.setClueAuditStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getClueAuditStatus());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "queryClueAuditStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询分销线索审核开关状态", notes = "分销线索审核开关状态")
    public Result<QueryClueAuditStatusResult> queryClueAuditStatus(@RequestBody QueryClueAuditStatusArg arg) {
        return settingService.queryClueAuditStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "setCommitClueRewardStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置提交线索激励开关状态", notes = "提交线索激励开关状态")
    public Result<Void> setCommitClueRewardStatus(@RequestBody SetCommitClueRewardStatusArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getPlanId()) || arg.getClueRewardStatus() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.setCommitClueRewardStatus(arg.getPlanId(), arg.getClueRewardStatus(), arg.getValidClueReward());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "getCommitClueRewardStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询提交线索激励开关状态", notes = "查询提交线索激励开关状态")
    public Result<CommitClueRewardStatusResult> getCommitClueRewardStatus(@RequestBody SetCommitClueRewardStatusArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getPlanId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.getCommitClueRewardStatus(arg.getPlanId());
    }

    @RequestMapping(value = "getAppId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取营销通appId", notes = "获取营销通appId")
    public Result<GetAppIdResult> getAppId() {
        return settingService.getAppId();
    }

    @RequestMapping(value = "getBoundMiniappInfo", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取该企业绑定的小程序信息", tags = {"440", "520"})
    public Result<GetBoundMiniappInfoResult> getBoundMiniappInfo(@RequestParam("platformId") String platformId){
        return settingService.getBoundMiniappInfo(UserInfoKeeper.getEa(), platformId);
    }

    @RequestMapping(value = "getBoundMiniappInfoV2", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取该企业绑定的小程序信息", tags = {"440", "10.6"})
    public Result<GetBoundMiniappInfoResult> getBoundMiniappInfoV2(@RequestBody MiniappPlatformArg arg){
        return settingService.getBoundMiniappInfo(UserInfoKeeper.getEa(), arg.getPlatformId());
    }


    @RequestMapping(value = "getSimpleBoundMiniappInfo", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取该企业绑定的小程序信息", tags = "440")
    public Result<GetSimpleBoundMiniappInfoResult> getSimpleBoundMiniappInfo(@RequestParam("platformId") String platformId){
        return settingService.getSimpleBoundMiniappInfo(UserInfoKeeper.getEa(), platformId);
    }

    @RequestMapping(value = "commitCodeAndSubmitAudit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "提交小程序代码并提交审核", tags = "440")
    public Result<String> commitCodeAndSubmitAudit(@RequestBody CommitCodeAndSubmitAuditArg arg){
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getWxAppId()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getCodeTemplateId()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getCodeVersion()));
        return settingService.commitCodeAndSubmitAudit(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "releaseCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "发布小程序代码", tags = "440")
    public Result<String> releaseCode(@RequestBody ReleaseCodeArg arg){
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getWxAppId()));
        return settingService.releaseCode(UserInfoKeeper.getEa(), arg);
    }

    @RequestMapping(value = "getVersionAuditStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询小程序审核状态", tags = "960")
    Result<Integer> getVersionAuditStatus(@RequestBody MiniappVersionAuditStatusArg arg){
        return settingService.getVersionAuditStatus(arg.getWxAppId(), arg.getPlatform());
    }

    @RequestMapping(value = "unAuthWxApp", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "取消小程序授权", tags = "440")
    public Result<String> unAuthWxApp(@RequestBody UnAuthWxAppArg unAuthWxAppArg){
        return settingService.unAuthWxAppId(UserInfoKeeper.getEa(), unAuthWxAppArg);
    }

    @GetMapping(value = "getImageByMaterialId")
    @ApiOperation(value = "通过素材id获取图片", tags = "440")
    public ResponseEntity<byte[]> getImageByMaterialId(@RequestParam("wxAppId") String wxAppId, @RequestParam("materialId") String materialId){
        Result<byte[]> result = settingService.getMaterialBytes(UserInfoKeeper.getEa(), wxAppId, materialId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);
        headers.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS).getHeaderValue());
        if(result.getData() == null){
            return new ResponseEntity<>(new byte[0], headers, HttpStatus.SERVICE_UNAVAILABLE);
        }else{
            return new ResponseEntity<>(result.getData(), headers, HttpStatus.OK);
        }
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @PostMapping(value = "listRole")
    @ApiOperation(value = "获取角色列表", tags = "功能权限")
    public Result<List<RoleResult>> listRole(){
        return settingService.listRole(UserInfoKeeper.getEa());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @PostMapping(value = "listUserRoles")
    @ApiOperation(value = "获取角色列表", tags = "功能权限")
    public Result<List<UserRoleResult>> listUserRoles(){
        return settingService.listUserRoles(UserInfoKeeper.getEa());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @PostMapping(value = "addUserRoles")
    @ApiOperation(value = "添加员工角色", tags = "功能权限")
    public Result<Void> addUserRoles(@RequestBody AddUserRolesArg arg){
        return settingService.addUserRoles(UserInfoKeeper.getEa(), new HashSet<>(arg.getEmployeeIds()), new HashSet<>(arg.getRoleIds()));
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @PostMapping(value = "editUserRoles")
    @ApiOperation(value = "编辑员工角色", tags = "功能权限")
    public Result<Void> editUserRoles(@RequestBody EditUserRolesArg arg){
        return settingService.editUserRoles(UserInfoKeeper.getEa(), arg.getEmployeeId(), new HashSet<>(arg.getRoleIds()));
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @PostMapping(value = "deleteUserRoleByUser")
    @ApiOperation(value = "删除员工角色", tags = "功能权限")
    public Result<Void> deleteUserRoleByUser(@RequestBody DeleteUserRoleByUser arg){
        return settingService.deleteUserRoleByUser(UserInfoKeeper.getEa(), arg.getEmployeeId());
    }


    @PostMapping(value = "setMiniappIntroductionSite")
    @ApiOperation(value = "设置小程序介绍页", tags = {"520"})
    public Result<Void> setMiniappIntroductionSite(@RequestBody SetMiniappIntroductionSiteConfigArg arg){
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getMiniappIntroductionSiteId()), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_SETTINGCONTROLLER_318));
        return settingService.setMiniappIntroductionSite(UserInfoKeeper.getEa(), arg);
    }


    /**
     * 请求托管小程序的介绍页频道。其中，托管小程序默认频道全部打开
     * @return
     */
    @PostMapping(value = "getWechatAccountChannelList")
    @ApiOperation(value = "获取托管小程序介绍页的频道信息", tags = "520")
    public Result<List<WechatAccountChannelResult>> getWechatAccountChannelList() {
        String ea = UserInfoKeeper.getEa();
        return enterpriseMetaConfigService.getWechatAccountChannelList(ea);
    }

    @PostMapping("updateWechatAccountChannelList")
    @ApiOperation(value="修改托管小程序介绍页的频道信息，名称，位置等",tags = "520")
    public Result<Void> updateWechatAccountChannelList(@RequestBody EditWechatAccountChannelDataArg arg) {
        String ea = UserInfoKeeper.getEa();
        return enterpriseMetaConfigService.updateWechatAccountChannelList(ea,arg.getData());
    }

    @PostMapping(value = "getCardCommonSetting")
    @ApiOperation(value = "获取小程序名片通用设置")
    public Result<List<GetCardCommonSettingResult>> getCardCommonSetting(@RequestBody GetCardCommonSettingArg arg) {
        GetCardCommonSettingVo vo = BeanUtil.copy(arg, GetCardCommonSettingVo.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return cardTemplateService.getCardCommonSetting(vo);
    }

    @PostMapping(value = "updateCardCommonSetting")
    @ApiOperation(value = "更新小程序名片通用设置")
    public Result updateCardCommonSetting(@RequestBody UpdateCardCommonSettingArg arg) {
        if (arg.isWrongParam()) {
            log.warn("SettingController.updateCardCommonSetting param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        //return enterpriseMetaConfigService.updateCardCommonSetting(UserInfoKeeper.getEa(), arg.getSetting());
        UpdateCardCommonSettingVo vo = BeanUtil.copy(arg, UpdateCardCommonSettingVo.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        if (CollectionUtils.isNotEmpty(arg.getSetting())) {
            List<CardCommonSetting> setting = arg.getSetting();
            vo.setSetting(setting);
        }
        return cardTemplateService.updateCardCommonSetting(vo);
    }
    
    @PostMapping(value = "pageListOperationRecord")
    @ApiOperation(value = "分页拉取系统操作记录（含人群）", tags = "600")
    public Result<PageResult<SystemOperationRecordVO>> pageListOperationRecord(@RequestBody PageListOperationRecordArg arg) {
        return settingService.pageListOperationRecord(UserInfoKeeper.getEa(), arg);
    }
    
    @PostMapping(value = "listReplaceParamsByScene")
    @ApiOperation(value = "根据场景拉取替换参数", tags = "640")
    public Result<List<Pair<String, String>>> listReplaceParamsByScene(@RequestBody JSONObject arg) {
        String marketingSceneType = arg.getString("marketingSceneType");
        List<Pair<String, String>> pairList = new LinkedList<>();
        if (MarketingSceneType.LIVE.getType().equals(marketingSceneType)){
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_122), "Live.viewUrl"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_119), "Live.startTime"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_121), "Live.lecture"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_120), "Live.endTime"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_118), "Live.title"));
        }
        if(MarketingSceneType.CONFERENCE.getType().equals(marketingSceneType)){
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_ACTIVITYENTITY_118), "Conference.startTime"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_ACTIVITYENTITY_120), "Conference.location"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_ACTIVITYENTITY_119), "Conference.endTime"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_ACTIVITYENTITY_117), "Conference.title"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_SETTINGCONTROLLER_391), "Conference.activityUrl"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_SETTINGCONTROLLER_392), "Campaign.ticketUrl"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1079), "Campaign.ticketCode"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_SETTINGCONTROLLER_394), "Campaign.enrollName"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_72), "Campaign.enrollPhone"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_797), "Campaign.enrollEmail"));
        }
        return Result.newSuccess(pairList);
    }
    
    @PostMapping(value = "listReplaceParamsByPrefix")
    @ApiOperation(value = "根据对象", tags = "640")
    public Result<List<Pair<String, String>>> listReplaceParamsByObject(@RequestBody JSONObject arg) {
        String prefix = arg.getString("prefix");
        List<Pair<String, String>> pairList = new LinkedList<>();
        if("MarketingUser".equals(prefix)){
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1442), "MarketingUser.name"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_2180_3), "MarketingUser.phone"));
            pairList.add(new Pair<>(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_797), "MarketingUser.email"));
        }
        return Result.newSuccess(pairList);
    }

    @PostMapping(value = "listParamsBySceneAndSpreadType")
    @ApiOperation(value = "根据场景与渠道拉取参数")
    public Result<ListParamsBySceneAndSpreadTypeResult> listParamsBySceneAndSpreadType(@RequestBody ListParamsBySceneAndSpreadTypeArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.listParamsBySceneAndSpreadType(UserInfoKeeper.getEa(), arg);
    }

    @RequestMapping(value = "setMarketingUserGroupCustomizeObjectMapping", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置目标人群自定义对象字段映射", notes = "设置目标人群自定义对象字段映射")
    public Result setMarketingUserGroupCustomizeObjectMapping(@RequestBody MarketingUserGroupCustomizeObjectMappingArg arg){
        if (StringUtils.isEmpty(arg.getObjectApiName())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.setMarketingUserGroupCustomizeObjectMapping(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "getMarketingUserGroupCustomizeObjectMapping", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置目标人群自定义对象字段映射", notes = "设置目标人群自定义对象字段映射")
    public Result<List<MarketingUserGroupCustomizeObjectMappingResult>> getMarketingUserGroupCustomizeObjectMapping(){
        return settingService.getMarketingUserGroupCustomizeObjectMapping(UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "deleteMarketingUserGroupCustomizeObjectMapping", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置目标人群自定义对象字段映射", notes = "设置目标人群自定义对象字段映射")
    public Result deleteMarketingUserGroupCustomizeObjectMapping(@RequestBody DeleteMarketingUserGroupCustomizeObjectMappingArg arg){
        return settingService.deleteMarketingUserGroupCustomizeObjectMapping(arg.getId());
    }

    @RequestMapping(value = "getMarketingUserExcludeApiname", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询营销用户关联排除的对象", notes = "查询营销用户关联排除的对象")
    public Result<List<MarketingUserExcludeApinameResult>> getMarketingUserExcludeApiname(){
         return userMarketingAccountService.getExcludeApiNameList(UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "setMarketingUserExcludeApiname", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置营销用户关联排除的对象", notes = "营销用户关联排除的对象")
    public Result setMarketingUserExcludeApiname(@RequestBody MarketingUserExcludeApinameArg arg){
        if (StringUtils.isEmpty(arg.getObjectApiName()) || StringUtils.isEmpty(arg.getObjectName())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.setExcludeApiName(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getObjectApiName(), arg.getObjectName(), arg.getStatus());
    }

    @RequestMapping(value = "getH5AccessPermissionsSeeting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询H5内容访问授权设置", notes = "查询H5内容访问授权设置")
    public Result getH5AccessPermissionsSeeting(){
        return settingService.getH5AccessPermissionsSeeting(null, null, UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "saveH5AccessPermissionsSeeting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "保存H5内容访问授权设置", notes = "保存H5内容访问授权设置")
    public Result saveH5AccessPermissionsSeeting(@RequestBody H5AccessPermissionsSeetingArg arg) {
        if (!arg.check()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.saveH5AccessPermissionsSeeting(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "getMiniappAccessPermissionsSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询小程序内容访问授权设置", notes = "查询H5内容访问授权设置")
    public Result getMiniappAccessPermissionsSetting() {
        return settingService.getMiniappAccessPermissionsSetting(UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "saveMiniappAccessPermissionsSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "保存小程序内容访问授权设置", notes = "保存H5内容访问授权设置")
    public Result saveMiniappAccessPermissionsSetting(@RequestBody MiniappAccessPermissionsSettingArg arg) {
        if (!arg.check()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return settingService.saveMiniappAccessPermissionsSetting(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "getSpreadContentDomain", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询用户自己配置的域名", notes = "查询用户自己配置的域名")
    public Result<String> getSpreadContentDomain(){
        return settingService.getSpreadContentDomain(UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "setMarketingUserSyncObjectTagsStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置营销用户同步关联对象标签", notes = "设置营销用户同步关联对象标签")
    public Result setMarketingUserSyncObjectTagsStatus(@RequestBody SetMarketingUserSyncObjectTagsArg arg){
        if (arg.getSyncObjectTagStatus() != SyncObjectTagStatusEnum.DISABLE.getValue() && arg.getSyncObjectTagStatus() != SyncObjectTagStatusEnum.ENABLE.getValue()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return settingService.setMarketingUserSyncObjectTagsStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getSyncObjectTagStatus());
    }

    @RequestMapping(value = "queryMarketingUserSyncObjectTagsStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询营销用户同步关联对象标签状态", notes = "设置营销用户同步关联对象标签状态")
   public Result<QueryMarketingUserSyncObjectTagsStatus> queryMarketingUserSyncObjectTagsStatus(){
        return settingService.queryMarketingUserSyncObjectTagsStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @PostMapping(value = "addUserAccessible")
    @ApiOperation(value = "添加员工权限设置", tags = "数据权限")
    public Result<Void> addUserAccessible(@RequestBody AddUserAccessibleArg arg){
        if (!arg.checkParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setUserId(UserInfoKeeper.getFsUserId());
        return settingService.addUserAccessible(arg);
    }

    @PostMapping(value = "editUserAccessible")
    @ApiOperation(value = "编辑员工权限设置", tags = "数据权限")
    public Result<Void> editUserAccessible(@RequestBody AddUserAccessibleArg arg){
        if (!arg.checkParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setUserId(UserInfoKeeper.getFsUserId());
        return settingService.editUserAccessible(arg);
    }

    @PostMapping(value = "queryEnterpriseChannelsAndRolesInfo")
    @ApiOperation(value = "获取渠道和角色列表", tags = "功能权限")
    public Result<EnterpriseChannelsAndRolesInfoResult> queryEnterpriseChannelsAndRolesInfo(){
        return settingService.queryEnterpriseChannelsAndRolesInfo(UserInfoKeeper.getEa());
    }

    @PostMapping(value = "queryUserAccessible")
    @ApiOperation(value = "获取用户权限", tags = "功能权限")
    public Result<UserAccessibleResult> queryUserAccessible(){
        UserAccessibleArg arg = new UserAccessibleArg();
        arg.setEa(UserInfoKeeper.getEa());
        arg.setUserId(UserInfoKeeper.getFsUserId());
        return settingService.queryUserAccessible(arg);
    }

    @RequestMapping(value = "configMiniAppAutoUpgrade", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置自动升级小程序开关状态", notes = "10.6")
    public Result configMiniAppAutoUpgrade(@RequestBody ConfigMiniAppAutoUpgradeArg arg){
        if (StringUtils.isEmpty(arg.getPlatformId()) || StringUtils.isEmpty(arg.getWxAppId()) || StringUtils.isEmpty(arg.getStatus())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return settingService.configMiniAppAutoUpgrade(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "getMiniAppAutoUpgradeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取自动升级小程序配置开关", notes = "10.6")
    public Result<String> getMiniAppAutoUpgradeStatus(@RequestBody GetMiniAppAutoUpgradeStatusArg arg){
        return settingService.getMiniAppAutoUpgradeStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "isPluginEnabled", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "是否开通营销AI插件")
    public Result<Boolean> isPluginEnabled(@RequestBody PluginEnabledArg arg){
        return settingService.isPluginEnabled(arg);
    }

    //查询所属企业的环境信息
    @RequestMapping(value = "getEnvironment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询所属企业的环境信息")
    public Result<GetEnvironmentResult> getEnvironment(){
        return settingService.getEnvironment(UserInfoKeeper.getEa());
    }
}