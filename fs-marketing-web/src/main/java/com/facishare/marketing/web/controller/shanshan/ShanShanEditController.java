package com.facishare.marketing.web.controller.shanshan;


import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.arg.shanshan.ShanShanCreateEmailArg;
import com.facishare.marketing.api.arg.shanshan.ShanShanPushEmailArg;
import com.facishare.marketing.api.arg.shanshan.queryShanShanEditEmailListArg;
import com.facishare.marketing.api.result.shanshan.ShanShanCreateEmailResult;
import com.facishare.marketing.api.result.shanshan.ShanShanEmailDetailResult;
import com.facishare.marketing.api.service.shanshan.ShanShanEditService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 闪闪编辑器相关的接口
 */

@Slf4j
@RestController
@RequestMapping("/shanShanEdit")
public class ShanShanEditController {

    @Autowired
    private ShanShanEditService shanShanEditService;


    @ResponseBody
    @RequestMapping(value = "/shanShanEditCreateEmail", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    public Result<ShanShanCreateEmailResult> shanShanEditCreateEmail(@RequestBody ShanShanCreateEmailArg arg) {
        if(arg==null || StringUtils.isBlank(arg.getName())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return shanShanEditService.createEmail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),arg.getName());
    }

    @ResponseBody
    @RequestMapping(value = "/shanShanEditGetCode", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    public Result<String> shanShanEditGetCode() {
        return shanShanEditService.shanShanEditGetCode(UserInfoKeeper.getEa());
    }

    @ResponseBody
    @RequestMapping(value = "/queryShanShanEditEmailList", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    public Result<PageResult<ShanShanEmailDetailResult>> queryShanShanEditEmailList(@RequestBody queryShanShanEditEmailListArg arg) {
        if(arg==null||!arg.isPageArgValid()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return shanShanEditService.queryShanShanEditEmailList(UserInfoKeeper.getEa(),arg);
    }

    @ResponseBody
    @RequestMapping(value = "/queryShanShanEditEmailDetail", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    public Result<ShanShanEmailDetailResult> queryShanShanEditEmailDetail(@RequestBody IdArg arg) {
        if(arg==null || StringUtils.isBlank(arg.getId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return shanShanEditService.queryShanShanEditEmailDetail(UserInfoKeeper.getEa(), arg.getId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "deleteShanShanTemplate", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除模板")
    public Result<Void> deleteShanShanTemplate(@RequestBody IdArg arg) {
        if(arg==null || StringUtils.isBlank(arg.getId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return shanShanEditService.deleteEmailTemplate(UserInfoKeeper.getEa(),arg.getId());
    }

    //闪闪编辑器推送接口
    @ResponseBody
    @RequestMapping(value = "/shanShanEditCallBack", method = {RequestMethod.POST})
    public String shanShanEditCallback(HttpServletRequest request, @RequestBody String data) {
        String key = request.getHeader("Authorization");
        log.info("ShanShanEditController shanShanEditCallback , key={}, data={}", key,data);
        return shanShanEditService.shanShanEditCallback(key,data).getData();
    }

    //闪闪编辑器推送实际处理
    @ResponseBody
    @RequestMapping(value = "/handleShanShanCallback", method = {RequestMethod.POST, RequestMethod.GET})
    public Result<Void> handleShanShanCallback(@RequestParam("pushEmailArg") String arg) {
        ShanShanPushEmailArg shanShanPushEmailArg = JSON.parseObject(arg, ShanShanPushEmailArg.class);
        return shanShanEditService.handleShanShanCallback(shanShanPushEmailArg);
    }

}
