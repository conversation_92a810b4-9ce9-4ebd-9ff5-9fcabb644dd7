/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.web.arg.marketingactivity;

import com.facishare.marketing.common.enums.MarketingActivitySpreadTypeEnum;
import com.facishare.marketing.web.vo.ExternalConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddArg implements Serializable {
    @ApiModelProperty("营销活动额外配置")
    private MarketingActivityExternalConfigVO marketingActivityExternalConfigVO;
    @ApiModelProperty("营销活动配置")
    private MarketingActivityVO marketingActivityVO;

    @Data
    public static class MarketingActivityExternalConfigVO implements Serializable {
        @ApiModelProperty("营销额外配置")
        private ExternalConfigVO externalConfig;
        @ApiModelProperty("MANKEEP_PRODUCT(4,\"客脉产品\"),\n" + "    MANKEEP_ARTICLE(6,\"客脉文章\"),\n" + "    MANKEEP_ACTIVITY(13,\"客脉活动\"),\n" + "    MANKEEP_SPREAD_NOTICE(15,\"客脉推广\"),\n" + "    MANKEEP_SEND_MESSAGE(17,\"客脉发送短信\"),\n" + "    WECHAT_SERVICE_SEND_MESSAGE(1001,\"服务号发送消息任务\")")
        private int associateIdType;
    }

    @Data
    public static class MarketingActivityVO implements Serializable {
        @ApiModelProperty("市场活动id")
        private String marketingEventId;
        /**
         * {@link MarketingActivitySpreadTypeEnum}
         */
        @ApiModelProperty("推广类型    \nALL_SPREAD(1,\"全员推广\"),\n" +
                "    WECHAT_SERVICE(2,\"服务号营销\"),\n" +
                "    SEND_NOTE(3,\"短信推广\");")
        private Integer spreadType;

        /**
         * {@link com.facishare.marketing.common.enums.SendStatusEnum}
         */
        @ApiModelProperty("状态 DRAFT(1,\"草稿\"), PROCESSING(2,\"进行中\"), FINISHED(3,\"已完成\"), FAIL(4,\"失败\"),NOT_BEGIN(5,\"未开始\")")
        private Integer status;
    }

}