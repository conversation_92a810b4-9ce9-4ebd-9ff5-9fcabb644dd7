package com.facishare.marketing.web.controller.hexagon;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.hexagon.*;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.FileToHexagonResult;
import com.facishare.marketing.api.result.hexagon.*;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.common.enums.UserMarketingActionClientEnum;
import com.facishare.marketing.api.vo.QueryHexagonBackgroudColorVO;
import com.facishare.marketing.common.enums.hexagon.HexagonCategoryEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonPreviewEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.enums.hexagon.MiniAppSiteSpreadInfoEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.web.arg.*;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("hexagon")
@Slf4j
@Api(value = "hexagon", tags = "hexagon_3.2.3")
public class HexagonController {

    @Autowired
    private HexagonService hexagonService;

    @ApiOperation(value = "创建、编辑站点")
    @RequestMapping(value = "/editSite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreateSiteResult> editSite(@RequestBody CreateSiteArg arg) {
        if (EmptyUtil.isNullForList(arg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isNotBlank(arg.getId())) {
            if (EmptyUtil.isNullForList(arg.getStatus())) {
                HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatus());
                if (null == hexagonStatusEnum) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }

                if (!HexagonStatusEnum.siteVaildStatus(hexagonStatusEnum)) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
            }
        } else {
            if (EmptyUtil.isNullForList(arg.getStatus())) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatus());
            if (null == hexagonStatusEnum) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            if ((hexagonStatusEnum != HexagonStatusEnum.NORMAL && hexagonStatusEnum != HexagonStatusEnum.NOTVISIBLE)) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }
        if (arg.getNeedBindWebSite() == null) {
            arg.setNeedBindWebSite(false);
        }

        return hexagonService.editSite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "更新微页面外部展示名称")
    @RequestMapping(value = "/updateSiteOutDisplayName", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result updateSiteOutDisplayName(@RequestBody UpdateSiteOutDisplayNameArg arg) {
        if (StringUtils.isEmpty(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.updateSiteOutDisplayName(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "微页面下复制站点")
    @RequestMapping(value = "/hexagonCopySite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreateSiteResult> hexagonCopySite(@RequestBody HexagonCopyArg hexagonCopyArg) {
        if (EmptyUtil.isNullForList(hexagonCopyArg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (EmptyUtil.isNullForList(hexagonCopyArg.getId(), hexagonCopyArg.getName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.hexagonCopySite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), hexagonCopyArg);
    }

    @ApiOperation(value = "内容营销下复制站点")
    @RequestMapping(value = "/marketingCopySite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreateSiteResult> marketingCopySite(@RequestBody MarketingCopyArg marketingCopyArg) {
        if (EmptyUtil.isNullForList(marketingCopyArg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (EmptyUtil.isNullForList(marketingCopyArg.getId(), marketingCopyArg.getName(),marketingCopyArg.getMarketingEventId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.marketingCopySite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), marketingCopyArg, false);
    }

    @ApiOperation(value = "删除站点")
    @RequestMapping(value = "/deleteSite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result deleteSite(@RequestBody DeleteSiteArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.deleteSite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @ApiOperation(value = "修改站点状态")
    @RequestMapping(value = "/changeSiteStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result changeSiteStatus(@RequestBody ChangeSiteStatusArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getId(), arg.getStatus())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatus());
        if (hexagonStatusEnum == null || hexagonStatusEnum == HexagonStatusEnum.DELETED) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.changeSiteStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId(), arg.getStatus());
    }

    @ApiOperation(value = "查询当前企业下所有站点", tags = "460")
    @RequestMapping(value = "/getSiteByEa", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<GetSiteByEaUnitResult>> getSiteByEa(@RequestBody GetSiteByEaArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getPageNum(), arg.getPageSize())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (null != arg.getStatusFitter()) {
            HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatusFitter());
            if (null == hexagonStatusEnum || hexagonStatusEnum == HexagonStatusEnum.DELETED || hexagonStatusEnum == HexagonStatusEnum.NOTVISIBLE) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }

        return hexagonService.getSiteByEa(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getPageSize(), arg.getPageNum(), arg.getTime(), arg.getSearchFitter(), arg.getStatusFitter(), arg.getExcludeSystemSite());
    }

    @ApiOperation(value = "查询单个站点")
    @RequestMapping(value = "/getSiteById", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetSiteByEaUnitResult> getSiteById(@RequestBody GetSiteByIdArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.getSiteById(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @ApiOperation(value = "预览站点" , tags = "322")
    @RequestMapping(value = "/sitePreview", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<SitePreviewResult> sitePreview(@RequestBody SitePreviewArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getType(), arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(arg.getType());
        if (null == hexagonPreviewEnum) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.sitePreview(UserInfoKeeper.getEa(), arg);
    }

    @ApiOperation(value = "创建、编辑页面")
    @RequestMapping(value = "/editPage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreatePageResult> editPage(@RequestBody CreatePageArg arg) {
        log.info("this  createPage: " + JSONObject.toJSONString(arg));
        if (EmptyUtil.isNullForList(arg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isBlank(arg.getId())) {
            if (EmptyUtil.isNullForList(arg.getHexagonSiteId())) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        } else {
            if (null != arg.getStatus()) {
                HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatus());
                if (!HexagonStatusEnum.pageVaildStatus(hexagonStatusEnum)) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
            }
        }

        return hexagonService.editPage(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "删除页面")
    @RequestMapping(value = "/deletePage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result deletePage(@RequestBody DeletePageArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.deletePage(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),  arg.getId());
    }

    @ApiOperation(value = "获取当前站点下所有页面")
    @RequestMapping(value = "/getPagesBySiteId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<GetPagesBySiteIdUnitResult>> getPagesBySiteId(@RequestBody GetPagesBySiteIdArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getSiteId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.getPagesBySiteId(UserInfoKeeper.getEa(), arg.getSiteId());
    }

    @ApiOperation(value = "企业自定义创建，编辑站点模板")
    @RequestMapping(value = "/editTemplateEaSite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreateTemplateSiteResult> editTemplateEaSite(@RequestBody CreateTemplateSiteEaArg arg) {
//        if (EmptyUtil.isNullForList(arg)) {
//            log.warn("HexagonService.editTemplateEaSite params eror");
//            return Result.newError(SHErrorCode.PARAMS_ERROR);
//        }
//        if(StringUtils.isBlank(arg.getId())){
////未写完
//        }

        return hexagonService.editTemplateEaSite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "删除模板站点")
    @RequestMapping(value = "/deleteTemplateSite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result deleteTemplateSite(@RequestBody DeleteTemplateSiteArg arg) {
        return hexagonService.deleteTemplateSite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }


    @ApiOperation(value = "修改站点模板状态1：正常 4：已删除")
    @RequestMapping(value = "/changeTemplateSiteStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result changeTemplateSiteStatus(@RequestBody ChangeTemplateSiteArg arg) {
        return hexagonService.changeTemplateSiteStatus(arg.getStatus(), arg.getId(), arg.getEa());
    }


    @ApiOperation(value = "获取所有系统站点模板")
    @RequestMapping(value = "/getTemplateSite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<GetTemplateSiteResult>> getTemplateSite(@RequestBody GetTemplateSiteArg arg) {
        //       获取系统模板
        if (EmptyUtil.isNullForList(arg.getPageSize(), arg.getPageNum(), arg.getType())) {
            log.warn("HexagonService.getSystemTemplate params error");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        //       判断category
        HexagonCategoryEnum hexagonCategoryEnum;
        if (arg.getCategory() != null) {
            hexagonCategoryEnum = HexagonCategoryEnum.getByType(arg.getCategory());
            if (hexagonCategoryEnum == null) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }
//        如果type==1,说明查询的是系统模板，type==2,则查询的是企业自有的模板
        String ea = null;
        if (arg.getType() == 2) {
            ea = UserInfoKeeper.getEa();
        }

        return hexagonService.getTemplateSite(ea, arg.getCategory(), arg.getPageSize(), arg.getPageNum(), arg.getTime(), arg.getType());
    }


    @ApiOperation(value = "创建，编辑模板页面")
    @RequestMapping(value = "/editTemplatePage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreateTemplatePageResult> editTemplatePage(@RequestBody CreateTemplatePageArg arg) {
        if (EmptyUtil.isNullForList(arg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.editTemplatePage(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }


    @ApiOperation(value = "获取当前模板站点下的所有页面")
    @RequestMapping(value = "/getPagesByTemplateSiteId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<GetPagesByTemplateSiteIdResult>> getPagesByTemplateSiteId(@RequestBody GetPagesByTemplateSiteIdArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getTemplateSiteId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.getPagesByTemplateSiteId(UserInfoKeeper.getEa(), arg.getTemplateSiteId());
    }

    @ApiOperation(value = "创建&编辑微页面站点分组")
    @RequestMapping(value = "/editHexagonGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EditObjectGroupResult> editHexagonGroup(@RequestBody EditHexagonGroupArg arg) {
        if (!arg.checkParamValid()){
            log.info("HexagonGroupController.editTemplateGroup param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.editHexagonGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "删除微页面站点分组")
    @RequestMapping(value = "/deleteHexagonGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result deleteHexagonGroup(@RequestBody DeleteHexagonGroupArg arg) {
        if (!arg.checkParamValid()){
            log.info("HexagonGroupController.editTemplateGroup param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.deleteHexagonGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "获取微页面分组")
    @RequestMapping(value = "/listHexagonGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<ListHexagonTemplateGroupResult> listHexagonGroup(@RequestBody ListHexagonGroupArg arg){
        return hexagonService.listHexagonGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "按分组展示微页面")
    @RequestMapping(value = "/listHexagonByGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<GetSiteByEaUnitResult>> listHexagonByGroup(@RequestBody ListHexagonByGroupArg arg){
        return hexagonService.listHexagonByGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "微页面设置分组")
    @RequestMapping(value = "/setHexagonGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result setHexagonGroup(@RequestBody SetHexagonGroupArg arg){
        if (StringUtils.isEmpty(arg.getGroupId()) || StringUtils.isEmpty(arg.getHexagonId())){
            log.info("HexagonGroupController.setHexagonGroup param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.setHexagonGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "微页面设置专属状态")
    @RequestMapping(value = "/setHexagonSystemStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result setHexagonSystemStatus(@RequestBody SetHexagonSystemStatusArg arg){
        if (StringUtils.isEmpty(arg.getHexagonSiteId())){
            //目前只支持专属转公开
            log.info("HexagonGroupController.setHexagonSystemStatus param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.setHexagonSystemStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }


    @ApiOperation(value = "重置会议表单数据")
    @RequestMapping(value = "/resetConferenceFormBySite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result resetConferenceFormBySite(@RequestBody ResetConferenceFormBySiteArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        return hexagonService.resetConferenceFormBySite(arg);
    }

    @ApiOperation(value = "获取活动中心配置信息")
    @RequestMapping(value = "getActivityCenterInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetActivityCenterInfoResult> getActivityCenterInfo(@RequestBody GetActivityCenterInfoArg arg) {
        return hexagonService.getActivityCenterInfo(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), true);
    }

    @ApiOperation(value = "获取内容中心配置信息")
    @RequestMapping(value = "getContentCenterInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetContentCenterInfoResult> getContentCenterInfo(@RequestBody GetContentCenterInfoArg arg) {
        return hexagonService.getContentCenterInfo(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), true);
    }

    @ApiOperation(value = "获取产品推广配置信息")
    @RequestMapping(value = "getProductSpreadInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetProductSpreadInfoResult> getProductSpreadInfo(@RequestBody GetProductSpreadInfoArg arg) {
        return hexagonService.getProductSpreadInfo(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), true);
    }

    @ApiOperation(value = "获取公司动态配置信息")
    @RequestMapping(value = "getEnterpriseDynamicInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetMiniAppSiteSpreadInfoResult> getEnterpriseDynamicInfo(@RequestBody GetMiniAppSiteSpreadInfoArg arg) {
        return hexagonService.getMiniAppSiteSpreadInfo(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), MiniAppSiteSpreadInfoEnum.ENTERPRISE_DYNAMICS.getType(), true);
    }

    @ApiOperation(value = "设置微页面访问标签")
    @RequestMapping(value = "addHexagonVisitTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result addHexagonVisitTag(@RequestBody AddHexagonVisitTagArg arg) {
        return hexagonService.addHexagonVisitTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiModelProperty(value = "查询微页面设置的访问标签")
    @RequestMapping(value = "queryHexagonVisitTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<TagNameList> queryHexagonVisitTag(@RequestBody QueryHexagonVisitTagArg arg) {
        return hexagonService.queryHexagonVisitTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getHexagonSiteId());
    }

    /*
    需求先不实现
    @ApiOperation(value = "微页面设置访问权限")
    @RequestMapping(value = "/setHexagonAuthorize", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result setHexagonAuthorize(@RequestBody SetHexagonAuthorizeArg arg){
        if (StringUtils.isEmpty(arg.getHexagonSiteId())){
            log.info("HexagonGroupController.setHexagonGroup param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.setHexagonAuthorize(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }
    */

    @ApiOperation(value = "微页面批量设置分组")
    @RequestMapping(value = "/setHexagonGroupBatch", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> setHexagonGroupBatch(@RequestBody SetObjectGroupArg arg){
        if (StringUtils.isEmpty(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getObjectIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.setHexagonGroupBatch(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "批量删除微页面")
    @RequestMapping(value = "/deleteSiteBatch", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> deleteSiteBatch(@RequestBody DeleteMaterialArg arg){
        if (CollectionUtils.isEmpty(arg.getIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.deleteSiteBatch(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "置顶微页面")
    @RequestMapping(value = "/topHexagonSite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> topHexagonSite(@RequestBody TopMaterialArg arg){
        if (StringUtils.isBlank(arg.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.topHexagonSite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "取消置顶微页面")
    @RequestMapping(value = "/cancelTopHexagonSite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> cancelTopHexagonSite(@RequestBody CancelMaterialTopArg arg){
        if (StringUtils.isBlank(arg.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.cancelTopHexagonSite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "微页面分组增加适用角色")
    @RequestMapping(value = "/addHexagonGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> addHexagonGroupRole(@RequestBody SaveObjectGroupVisibleArg arg){
        if (StringUtils.isBlank(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getRoleIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.addHexagonGroupRole(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "获取微页面分组适用角色")
    @RequestMapping(value = "/getGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> getGroupRole(@RequestBody JSONObject arg){
//        @ApiParam("分组ID") @RequestParam("groupId")
        String groupId = arg.getString("groupId");
        return hexagonService.getGroupRole(UserInfoKeeper.getEa(), groupId);
    }

    @ApiOperation(value = "文件转码微页面")
    @RequestMapping(value = "/fileToHexagon", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<FileToHexagonResult> fileToHexagon(@RequestBody FileToHexagonArg arg){
        if (!arg.checkParam()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setUserId(UserInfoKeeper.getFsUserId());
        return hexagonService.fileToHexagon(arg);
    }

    @ApiOperation(value = "生成外部链接参数")
    @RequestMapping(value = "/genMktParam", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> genMktParam(@RequestBody MktParamArg arg){
        if (null == arg){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.genMktParam(GsonUtil.toJson(arg));
    }

    @ApiOperation(value = "查询微页面背景色")
    @RequestMapping(value = "/queryHexagonBackgroundColorSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> queryHexagonBackgroundColorSetting(){
        QueryHexagonBackgroudColorVO vo = new QueryHexagonBackgroudColorVO();
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return hexagonService.queryHexagonBackgroundColorSetting(vo);
    }

    @ApiOperation(value = "更新微页面背景色设置")
    @RequestMapping(value = "/updateHexagonBackgroundColorSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> updateHexagonBackgroundColorSetting(@RequestBody UpdateHexagonBackgroudColorArg arg){
        if (null == arg || CollectionUtils.isEmpty(arg.getColorConfigs())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.updateHexagonBackgroundColorSetting(UserInfoKeeper.getEa(),arg);
    }
}
