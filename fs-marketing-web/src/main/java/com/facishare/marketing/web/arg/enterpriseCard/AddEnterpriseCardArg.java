/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.web.arg.enterpriseCard;

import com.facishare.mankeep.common.enums.PicOperationFlagEnum;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @创建人 zhengliy
 * @创建时间 2019/1/15 19:24
 * @描述
 */
@Data
public class AddEnterpriseCardArg implements Serializable {

    @ApiModelProperty(name = "companyName", value = "公司名")
    String companyName;
    @ApiModelProperty(name = "companyAddress", value = "公司地址")
    String companyAddress;
    @ApiModelProperty(name = "video", value = "视频链接地址")
    String video;
    @ApiModelProperty(name = "tradeCode", value = "行业编码")
    String tradeCode;

    @ApiModelProperty(name = "photos", value = "图片的tapath数组")
    List<String> photos;



    @ApiModelProperty(name = "addressBookDefaultRange", value = "员工信息自动从通讯录获取的信息范围\n "
        + "avatar:"
        + "name:"
        + "phone:"
        + "vocation:"
        + "email:"
        + "1 为选择")
    String addressBookDefaultRange;

    /**
     * 操作图片类型 {@link com.facishare.mankeep.common.enums.PicOperationFlagEnum}
     */
    @ApiModelProperty(name = "isOperation", value = "是否操作了图片  0：无操作\n 1：为操作")
    Integer isOperation;
    @ApiModelProperty(name = "productIds", value = "产品的id数组")
    List<String> productIds;
    public boolean isWrongParam(){
        if(StringUtils.isBlank(companyName) || StringUtils.isBlank(tradeCode) || StringUtils.isBlank(addressBookDefaultRange)){
            return true;
        }
        if(isOperation == null || Arrays.stream(PicOperationFlagEnum.values()).noneMatch(data -> data.getType() == isOperation)){
            return true;
        }
        return false;
    }

    @ApiModelProperty(name = "isProductExcluded", value = "是否不存在产品信息（true的情况下，表示没有productIds参数信息；false或者null表示有productIds参数信息）")
    Boolean isProductExcluded;

    private String cardTemplateId; //模板id

    @ApiModelProperty(value = "视频封面")
    private String videoCover;

    @ApiModelProperty(value = "名片微页面id")
    private String cardHexagonId;

    @ApiModelProperty(value = "内容类型 0：视频或图片 1：微页面")
    private Integer contentType;
}
