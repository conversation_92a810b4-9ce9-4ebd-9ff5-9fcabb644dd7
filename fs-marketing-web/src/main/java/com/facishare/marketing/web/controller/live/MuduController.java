package com.facishare.marketing.web.controller.live;

import com.facishare.marketing.api.arg.live.CheckMuduRelateArg;
import com.facishare.marketing.api.arg.live.GetMuduLiveArg;
import com.facishare.marketing.api.arg.live.ListMuduEventArg;
import com.facishare.marketing.api.result.live.GetMuduAccountResult;
import com.facishare.marketing.api.result.live.GetMuduEventListResult;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.api.vo.live.BindMuduAccountVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.arg.live.BindMuduAccountArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/6
 **/
@RestController
@RequestMapping("mudu")
@Slf4j
@Api(description = "目睹", tags = "MuduController")
public class MuduController {

    @Autowired
    private LiveService liveService;

    @RequestMapping(value = "bindMuduAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "绑定目睹账号")
    public Result<Void> bindMuduAccount(@RequestBody BindMuduAccountArg arg){
        BindMuduAccountVO vo = BeanUtil.copy(arg, BindMuduAccountVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return liveService.bindMuduAccount(vo);
    }

    @RequestMapping(value = "getMuduAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取目睹账号")
    public Result<GetMuduAccountResult> getMuduAccount(){
        return liveService.getMuduAccount(UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "queryLive", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取目睹直播列表")
    public Result<PageResult<GetMuduEventListResult>> queryLive(@RequestBody ListMuduEventArg arg){
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return liveService.getMuduEventList(arg);
    }

    @RequestMapping(value = "getLive", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取目睹直播详情")
    public Result<GetMuduEventListResult> getLive(@RequestBody GetMuduLiveArg arg){
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return liveService.getMuduLive(arg);
    }

    @RequestMapping(value = "checkRelate", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "检查当前目睹直播是否已经关联市场活动")
    public Result<Map<String,String>> checkRelate(@RequestBody CheckMuduRelateArg arg){
        if (StringUtils.isEmpty(arg.getId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return liveService.getMarketingLiveByPolyvId(UserInfoKeeper.getEa(), arg.getId());
    }
}
