package com.facishare.marketing.web.controller.advertiser.headlines;

import com.facishare.marketing.api.result.advertiser.headlines.*;
import com.facishare.marketing.api.result.baidu.TrendGraphDataResult;
import com.facishare.marketing.api.service.advertiser.headlines.HeadlinesService;
import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import com.facishare.marketing.api.vo.advertiser.headlines.*;
import com.facishare.marketing.api.vo.baidu.QueryCampaignDetailVO;
import com.facishare.marketing.api.vo.baidu.RelateMarketingEventVO;
import com.facishare.marketing.api.vo.baidu.TrendGraphDataVO;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.web.arg.advertiser.baidu.QueryCampaignDetailArg;
import com.facishare.marketing.web.arg.advertiser.baidu.RelateMarketingEventArg;
import com.facishare.marketing.web.arg.advertiser.baidu.TrendGraphDataArg;
import com.facishare.marketing.web.arg.advertiser.headlines.*;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("headlines")
@Api(description = "头条广告", tags = "HeadlinesController")
public class HeadlinesController {

    @Autowired
    private HeadlinesService headlinesService;

    @Autowired
    private BaiduCampaignService campaignService;

    @RequestMapping(value = "/queryHeadlinesCampaignList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取头条广告组列表", notes = "获取头条广告组列表")
    public Result<PageResult<HeadlinesCampaignInfoResult>> queryHeadlinesCampaignList(@RequestBody QueryHeadlinesCampaignListArg arg) {
        Preconditions.checkArgument(arg != null);
        QueryHeadlinesCampaignListVO vo = BeanUtil.copy(arg, QueryHeadlinesCampaignListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setNameKey(StringUtils.isNotBlank(arg.getNameKey()) ? arg.getNameKey() : null);
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        return headlinesService.queryCampaignList(vo);
    }

    /**
     * 新版头条首页
     * @param arg
     * @return
     */
    @RequestMapping(value = "/queryNewHeadlinesCampaignList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取头条广告组列表", notes = "获取头条广告组列表")
    public Result<PageResult<HeadlinesAdPlanResult>> queryNewHeadlinesCampaignList(@RequestBody QueryHeadlinesAdPlanListArg arg) {
        Preconditions.checkArgument(arg != null);
        QueryHeadlinesAdPlanListVO vo = BeanUtil.copy(arg, QueryHeadlinesAdPlanListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        vo.setPageSize(arg.getPageSize());
        vo.setPageNum(arg.getPageNum());
        vo.setStatus(arg.getStatus() != null ? arg.getStatus() : null);
        vo.setNameKey(StringUtils.isNotBlank(arg.getNameKey()) ? arg.getNameKey() : null);
        vo.setStartTime(arg.getStartTime() != null ? (new Date(arg.getStartTime())) : null);
        vo.setEndTime(arg.getEndTime() != null ? (new Date(arg.getEndTime())) : null);
        vo.setGroupNameKey(arg.getGroupNameKey());
        return headlinesService.queryNewHeadlinesCampaignList(vo);
    }

    @RequestMapping(value = "/queryHeadlinesAdPlanList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取头条广告计划列表", notes = "获取头条广告计划列表")
    public Result<PageResult<HeadlinesAdPlanResult>> queryHeadlinesAdPlanList(@RequestBody QueryHeadlinesAdPlanListArg arg) {
        Preconditions.checkArgument(arg != null);
        QueryHeadlinesAdPlanListVO vo = BeanUtil.copy(arg, QueryHeadlinesAdPlanListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        vo.setPageSize(arg.getPageSize());
        vo.setPageNum(arg.getPageNum());
        vo.setStatus(arg.getStatus() != null ? arg.getStatus() : null);
        vo.setNameKey(StringUtils.isNotBlank(arg.getNameKey()) ? arg.getNameKey() : null);
        vo.setStartTime(arg.getStartTime() != null ? (new Date(arg.getStartTime())) : null);
        vo.setCampaignId(arg.getCampaignId());
        vo.setEndTime(arg.getEndTime() != null ? (new Date(arg.getEndTime())) : null);
        vo.setGroupNameKey(arg.getGroupNameKey());
        return headlinesService.queryAdPlanList(vo);
    }

    @RequestMapping(value = "/queryHeadlinesCampaignDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询头条广告组详情", notes = "查询头条广告组详情")
    public Result<HeadlinesCampaignDetailResult> queryHeadlinesCampaignDetail(@RequestBody QueryCampaignDetailArg arg) {
        Preconditions.checkArgument(arg != null);
        QueryCampaignDetailVO vo = BeanUtil.copy(arg, QueryCampaignDetailVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        vo.setStartTime(arg.getStartTime() != null ? (new Date(arg.getStartTime())) : null);
        vo.setEndTime(arg.getEndTime() != null ? new Date(arg.getEndTime()) : null);
        return headlinesService.queryHeadlinesCampaignDetail(vo);
    }

    @RequestMapping(value = "/queryHeadlinesAdData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询头条广告计划数据", notes = "查询头条广告计划数据")
    public Result<HeadlinesAdPlanDataResult> queryHeadlinesAdData(@RequestBody QueryHeadlinesAdPlanDataListArg arg) {
        Preconditions.checkArgument(arg != null);
        QueryHeadlinesAdPlanListVO vo = BeanUtil.copy(arg, QueryHeadlinesAdPlanListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        vo.setAdId(arg.getAdId());
        vo.setStartTime(arg.getStartTime() != null ? (new Date(arg.getStartTime())) : DateUtil.getSomeDay(new Date(), -7));
        vo.setEndTime(arg.getEndTime() != null ? new Date(arg.getEndTime()) : new Date());
        return headlinesService.queryAdData(vo);
    }


    @RequestMapping(value = "/queryHeadlinesAdDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询头条广告计划详情", notes = "查询头条广告计划详情")
    public Result<HeadlinesAdDetailResult> queryHeadlinesAdDetail(@RequestBody QueryHeadlinesAdDetailArg arg) {
        Preconditions.checkArgument(arg != null);
        QueryHeadlinesAdDetailVO vo = BeanUtil.copy(arg, QueryHeadlinesAdDetailVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        vo.setId(arg.getId());
        vo.setStartTime(arg.getStartTime() != null ? (new Date(arg.getStartTime())) : DateUtil.getSomeDay(new Date(),-30));
        vo.setEndTime(arg.getEndTime() != null ? new Date(arg.getEndTime()) : DateUtil.getSomeDay(new Date(), -1));
        return headlinesService.queryAdDetail(vo);
    }




    @RequestMapping(value = "/getTrendGraphDataList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动广告趋势图数据", notes = "获取市场活动广告趋势图数据")
    public Result<TrendGraphDataResult> getTrendGraphDataList(@RequestBody TrendGraphDataArg arg) {
        Preconditions.checkArgument(arg != null);
        if (arg.getStartTime() == null || Strings.isNullOrEmpty(arg.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        TrendGraphDataVO vo = BeanUtil.copy(arg, TrendGraphDataVO.class);
        vo.setStartTime(arg.getStartTime() != null ? (new Date(arg.getStartTime())) : null);
        vo.setEndTime(arg.getEndTime() != null ? (new Date(arg.getEndTime())) : DateUtil.getSomeDay(new Date(), -1));
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        return campaignService.getTrendGraphDataList(vo);
    }

    @RequestMapping(value = "/getCluesTrendGraphList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取表单线索新增趋势图", notes = "获取表单线索新增趋势图")
    public Result<List<CluesTrendGraphDataResult>> getCluesTrendGraphList(@RequestBody QueryCluesTrendGraphDataListArg arg) {
        Preconditions.checkArgument(arg != null);
        ClueTrendGraphDataVo vo = new ClueTrendGraphDataVo();
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setMarketingEventId(arg.getMarketingEventId());
        vo.setStartTime(new Date(arg.getStartTime()));
        vo.setEndTime(new Date(arg.getEndTime()));
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        return headlinesService.queryCluesTrendGraphList(vo);
    }



    @RequestMapping(value = "/relateCampaignMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "广告组关联市场活动", notes = "广告组关联市场活动")
    public Result<Void> relateCampaignMarketingEvent(@RequestBody RelateMarketingEventArg arg) {
        Preconditions.checkArgument(arg != null);
        if (Strings.isNullOrEmpty(arg.getId()) || Strings.isNullOrEmpty(arg.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        RelateMarketingEventVO vo = BeanUtil.copy(arg, RelateMarketingEventVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return campaignService.relateMarketingEvent(vo);
    }


    @RequestMapping(value = "/relateSubMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "广告计划关联子级市场活动", notes = "广告计划关联子级市场活动")
    public Result<Void> relateSubMarketingEvent(@RequestBody RelateSubMarketingEventArg arg) {
        Preconditions.checkArgument(arg != null);
        if (Strings.isNullOrEmpty(arg.getId()) || Strings.isNullOrEmpty(arg.getSubMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        RelateSubMarketingEventVO vo = BeanUtil.copy(arg, RelateSubMarketingEventVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return headlinesService.relateSubMarketingEvent(vo);
    }

    @RequestMapping(value = "/updateAdLeadsMappingData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新或保存广告线索映射", notes = "更新或保存广告线索映射")
    public Result updateAdLeadsMappingData(@RequestBody AdLeadsMappingArg arg) {
        Preconditions.checkArgument( arg != null);
        if (Strings.isNullOrEmpty(arg.getCrmRecordType())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AdLeadsMappingVO vo = BeanUtil.copy(arg, AdLeadsMappingVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        return headlinesService.updateAdLeadsMappingData(vo);
    }

    @RequestMapping(value = "/queryAdLeadsMappingData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询广告线索映射", notes = "查询广告线索映射")
    public Result<QueryAdLeadsMappingDataResult> queryAdLeadsMappingData(@RequestBody QueryAdLeadsMappingArg arg) {
        Preconditions.checkArgument( arg != null);
        QueryAdLeadsMappingVO vo = BeanUtil.copy(arg, QueryAdLeadsMappingVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        return headlinesService.queryAdLeadsMappingData(vo);
    }


    @RequestMapping(value = "/queryHeadlinesAppId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询广告线索映射", notes = "查询广告线索映射")
    public Result<QueryHeadlinesAppIdResult> queryHeadlinesAppId(@RequestBody QueryHeadlinesAppIdArg arg) {
        Preconditions.checkArgument( arg != null);
        QueryHeadlinesAppIdVO vo = BeanUtil.copy(arg, QueryHeadlinesAppIdVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        return headlinesService.queryHeadlinesAppId(vo);
    }

    @RequestMapping(value = "/syncAllLocalClue", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "手动全量同步历史本地推线索", notes = "手动全量同步历史本地推线索")
    public Result<Void> syncAllLocalClue(@RequestBody SyncHeadlinesLocalLeadsArg arg) {
        try {
            Preconditions.checkArgument(arg != null);
            Long startTime = arg.getStartTime();
            Long endTime = arg.getEndTime();
            // 非空校验
            Preconditions.checkArgument(startTime != null, "startTime can not null");
            Preconditions.checkArgument(endTime != null, "endTime can not null");
            // 时间范围限制
            Duration duration = Duration.between(Instant.ofEpochMilli(startTime), Instant.ofEpochMilli(endTime));
            long monthsBetween = duration.toDays() / 30; // 简单地将一个月估计为30天
            Preconditions.checkArgument(monthsBetween <= 3, "The time before and after cannot exceed 3 months");
            SyncHeadlinesLocalLeadsVO vo = BeanUtil.copy(arg, SyncHeadlinesLocalLeadsVO.class);
            vo.setEa(UserInfoKeeper.getEa());
            return campaignService.refreshLocalLeadByAccountId(vo);
        } catch (IllegalArgumentException e) {
            log.error("HeadlinesController syncAllLocalClue error ea:{}", UserInfoKeeper.getEa(), e);
            return new Result<>(SHErrorCode.PARAMS_ERROR.getErrorCode(), e.getMessage());
        }
    }

}
