package com.facishare.marketing.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.CompanyWxOfficialAccountsListArg;
import com.facishare.marketing.api.arg.QueryWxOfficialAccountsQrCodeChannelArg;
import com.facishare.marketing.api.arg.UpsertWxOfficialAccountsQrCodeChannelArg;
import com.facishare.marketing.api.arg.wx.*;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.WxOfficialAccountsService;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Created by ranluch on 2019/10/30.
 */
@RestController
@RequestMapping("wxOfficialAccounts")
@Slf4j
@Api(description = "微信公众号", tags = "WxOfficialAccountsController")
public class WxOfficialAccountsController {

    @Autowired
    private WxOfficialAccountsService wxOfficialAccountsService;

    /**
     * 获取企业下公众号列表
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "getCompanyWxOfficialAccountsList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取企业绑定的微信公众号信息列表", notes = "获取公众号信息")
    public Result<List<AccountDataListResult>> getCompanyWxOfficialAccountsList(@RequestBody CompanyWxOfficialAccountsListArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());

        return wxOfficialAccountsService.getCompanyWxOfficialAccountsList(arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "upsertWxOfficialAccountsQrCodeChannel", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "添加/更新渠道二维码渠道映射")
    public Result upsertWxOfficialAccountsQrCodeChannel(@RequestBody UpsertWxOfficialAccountsQrCodeChannelArg arg) {
        if (arg.isWrongParam()) {
            log.warn("WxOfficialAccountsController.upsertWxOfficialAccountsQrCodeChannel param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        return wxOfficialAccountsService.upsertWxOfficialAccountsQrCodeChannel(arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "queryWxOfficialAccountsQrCodeChannel", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取渠道二维码渠道映射")
    public Result<QueryWxOfficialAccountsQrCodeChannelResult> queryWxOfficialAccountsQrCodeChannel(@RequestBody QueryWxOfficialAccountsQrCodeChannelArg arg) {
        if (arg.isWrongParam()) {
            log.warn("WxOfficialAccountsController.queryWxOfficialAccountsQrCodeChannel param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        return wxOfficialAccountsService.queryWxOfficialAccountsQrCodeChannel(arg);
    }


    @RequestMapping(value = "batchGetWxMaterial", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "批量获取素材内容")
    public Result<PageResult<BatchGetWxMaterialResult>> batchGetWxMaterial(@RequestBody BatchGetWxMaterialArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return wxOfficialAccountsService.batchGetDraft(arg);
    }

    @RequestMapping(value = "updateWxMaterial", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新微信素材")
    public Result updateWxMaterial(@RequestBody UpdateWxMaterialArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return wxOfficialAccountsService.updateWxDraft(arg);
    }

    @RequestMapping(value = "queryWxMaterialByMediaId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据微信公众号素材id获取信息")
    public Result<BatchGetWxMaterialResult> queryWxMaterialByMediaId(@RequestBody QueryWxMaterialByMediaIdArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return wxOfficialAccountsService.queryWxDraftlByDraftId(arg);
    }

    @RequestMapping(value = "bindOfficialWebsite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "微信公众号渠道二维码绑定官网")
    public Result<String> bindOfficialWebsite(@RequestBody BindOfficialWebsiteArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return wxOfficialAccountsService.bindOfficialWebsite(arg);
    }

    @RequestMapping(value = "unbindOfficialWebsite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "公众号渠道二维码解绑官网")
    public Result<String> unbindOfficialWebsite(@RequestBody UnBindOfficialWebsiteArg arg) {
        if(StringUtils.isBlank(arg.getEa())) {
            arg.setEa(UserInfoKeeper.getEa());
        }
        return wxOfficialAccountsService.unbindOfficialWebsite(arg);
    }

    @RequestMapping(value = "queryOfficialWebsiteWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询绑定了官网的公众号渠道二维码")
    public Result<List<OfficialWebsiteWxQrCodeResult>> queryOfficialWebsiteWxQrCode(@RequestBody QueryOfficialWebsiteWxQrCodeArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return wxOfficialAccountsService.queryOfficialWebsiteWxQrCode(arg);
    }

    @RequestMapping(value = "createOfficialWebsiteWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建官网引流公众号渠道二维码")
    public Result<OfficialWebsiteWxQrCodeResult> createOfficialWebsiteWxQrCode(@RequestBody CreateOfficialWebsiteWxQrCodeArg arg, HttpServletRequest request) {
        if (StringUtils.isEmpty(arg.getSceneId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ipAddr = request.getRemoteAddr();
        arg.setIpAddr(ipAddr);
        return wxOfficialAccountsService.createOfficialWebsiteWxQrCode(arg);
    }

    @RequestMapping(value = "queryTempOfficialWebsiteWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询临时公众号渠道二维码")
    public Result<List<OfficialWebsiteWxQrCodeResult>> queryTempOfficialWebsiteWxQrCode(@RequestBody QueryOfficialWebsiteWxQrCodeArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return wxOfficialAccountsService.queryTempOfficialWebsiteWxQrCode(arg);
    }

    @RequestMapping(value = "getMemberInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取会员信息")
    public Result<CustomizeFormDataEnrollResult> getMemberInfo(@RequestBody JSONObject arg) {
        String browserUserId = arg.getString("browserUserId");
        if (StringUtils.isEmpty(browserUserId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.getMemberInfo(browserUserId);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "bindOfficialWebsiteLogin", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "微信公众号登录二维码绑定官网")
    public Result<String> bindOfficialWebsiteLogin(@RequestBody BindOfficialWebsiteArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return wxOfficialAccountsService.bindOfficialWebsiteLogin(arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "unBindOfficialWebsiteLogin", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "微信公众号登录二维码绑定官网")
    public Result unBindOfficialWebsiteLogin(@RequestBody UnBindOfficialWebsiteArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.unBindOfficialWebsiteLogin(UserInfoKeeper.getEa(), arg.getId());
    }

    @RequestMapping(value = "queryOfficialWebsiteLoginWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询官网的公众号登录二维码")
    public Result<OfficialWebsiteWxQrCodeResult> queryOfficialWebsiteLoginWxQrCode(@RequestBody QueryOfficialWebsiteWxQrCodeArg arg) {
        if (StringUtils.isEmpty(arg.getEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.queryOfficialWebsiteLoginWxQrCode(arg);
    }

    @RequestMapping(value = "createOfficialWebsiteLoginWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建官网登录公众号渠道二维码")
    public Result<OfficialWebsiteWxQrCodeResult> createOfficialWebsiteLoginWxQrCode(@RequestBody CreateOfficialWebsiteWxQrCodeArg arg) {
        if (StringUtils.isEmpty(arg.getSceneId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.createOfficialWebsiteLoginWxQrCode(arg);
    }

    @RequestMapping(value = "getNearlyExternalConfig", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询最近的公众号消息模板配置")
    public Result<WeChatTemplateMessageData.TemplateMessageDatas> getNearlyExternalConfig(@RequestBody GetWeChatOfficialExternalConfigArg arg) {
        if (StringUtils.isEmpty(arg.getWeChatOfficialTemplateId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return wxOfficialAccountsService.getNearlyExternalConfig(UserInfoKeeper.getEa(),arg.getWeChatOfficialTemplateId());
    }

}
